{"name": "@supabase/pg-meta", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@supabase/pg-meta", "version": "0.0.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/pg": "^8.11.11", "@vitest/coverage-v8": "^3.0.9", "npm-run-all": "^4.1.5", "pg": "^8.13.1", "postgres-array": "^3.0.2", "typescript": "~5.5.0", "vite": "^6.2.4", "vitest": "^3.0.5"}}, "../../node_modules/.pnpm/@types+pg@8.11.11/node_modules/@types/pg": {"version": "8.11.11", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^4.0.1"}}, "../../node_modules/.pnpm/@vitest+coverage-v8@3.0.9_supports-color@8.1.1_vitest@3.0.9_@types+node@22.13.14_jiti@2.4.2_j_qve2guu6le5ehdzprie3vw47pi/node_modules/@vitest/coverage-v8": {"version": "3.0.9", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.3.0", "@bcoe/v8-coverage": "^1.0.2", "debug": "^4.4.0", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^5.0.6", "istanbul-reports": "^3.1.7", "magic-string": "^0.30.17", "magicast": "^0.3.5", "std-env": "^3.8.0", "test-exclude": "^7.0.1", "tinyrainbow": "^2.0.0"}, "devDependencies": {"@types/debug": "^4.1.12", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-lib-report": "^3.0.3", "@types/istanbul-lib-source-maps": "^4.0.4", "@types/istanbul-reports": "^3.0.4", "@types/test-exclude": "^6.0.2", "@vitest/browser": "3.0.9", "pathe": "^2.0.3", "v8-to-istanbul": "^9.3.0", "vite-node": "3.0.9", "vitest": "3.0.9"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@vitest/browser": "3.0.9", "vitest": "3.0.9"}, "peerDependenciesMeta": {"@vitest/browser": {"optional": true}}}, "../../node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all": {"version": "4.1.5", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "chalk": "^2.4.1", "cross-spawn": "^6.0.5", "memorystream": "^0.3.1", "minimatch": "^3.0.4", "pidtree": "^0.3.0", "read-pkg": "^3.0.0", "shell-quote": "^1.6.1", "string.prototype.padend": "^3.0.0"}, "bin": {"npm-run-all": "bin/npm-run-all/index.js", "run-p": "bin/run-p/index.js", "run-s": "bin/run-s/index.js"}, "devDependencies": {"@types/node": "^4.9.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-preset-power-assert": "^2.0.0", "babel-register": "^6.26.0", "codecov": "^3.1.0", "eslint": "^4.19.1", "eslint-config-mysticatea": "^12.0.0", "fs-extra": "^7.0.1", "mocha": "^5.2.0", "nyc": "^11.9.0", "p-queue": "^2.4.2", "power-assert": "^1.6.1", "rimraf": "^2.6.2", "yarn": "^1.12.3"}, "engines": {"node": ">= 4"}}, "../../node_modules/.pnpm/pg@8.13.1/node_modules/pg": {"version": "8.13.1", "dev": true, "license": "MIT", "dependencies": {"pg-connection-string": "^2.7.0", "pg-pool": "^3.7.0", "pg-protocol": "^1.7.0", "pg-types": "^2.1.0", "pgpass": "1.x"}, "devDependencies": {"@cloudflare/workers-types": "^4.20230404.0", "async": "2.6.4", "bluebird": "3.7.2", "co": "4.6.0", "pg-copy-streams": "0.3.0", "typescript": "^4.0.3", "workerd": "^1.20230419.0", "wrangler": "3.58.0"}, "engines": {"node": ">= 8.0.0"}, "optionalDependencies": {"pg-cloudflare": "^1.1.1"}, "peerDependencies": {"pg-native": ">=3.0.1"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}}, "../../node_modules/.pnpm/postgres-array@3.0.2/node_modules/postgres-array": {"version": "3.0.2", "dev": true, "license": "MIT", "devDependencies": {"standard": "^17.0.0", "tape": "^5.0.0"}, "engines": {"node": ">=12"}}, "../../node_modules/.pnpm/typescript@5.5.2/node_modules/typescript": {"version": "5.5.2", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.3.0", "@dprint/typescript": "0.91.0", "@esfx/canceltoken": "^1.0.0", "@octokit/rest": "^20.1.1", "@types/chai": "^4.3.16", "@types/microsoft__typescript-etw": "^0.1.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.6", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.3", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "@typescript-eslint/utils": "^7.11.0", "azure-devops-node-api": "^13.0.0", "c8": "^9.1.0", "chai": "^4.4.1", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.46.1", "esbuild": "^0.21.4", "eslint": "^8.57.0", "eslint-formatter-autolinkable-stylish": "^1.3.0", "eslint-plugin-local": "^4.2.2", "fast-xml-parser": "^4.4.0", "glob": "^10.4.1", "hereby": "^1.8.9", "jsonc-parser": "^3.2.1", "minimist": "^1.2.8", "mocha": "^10.4.0", "mocha-fivemat-progress-reporter": "^0.1.0", "ms": "^2.1.3", "node-fetch": "^3.3.2", "playwright": "^1.44.1", "source-map-support": "^0.5.21", "tslib": "^2.6.2", "typescript": "^5.4.5", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "../../node_modules/.pnpm/vite@6.2.4_@types+node@22.13.14_jiti@2.4.2_sass@1.72.0_terser@5.39.0_tsx@4.19.3_yaml@2.4.5/node_modules/vite": {"version": "6.2.4", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "postcss": "^8.5.3", "rollup": "^4.30.1"}, "bin": {"vite": "bin/vite.js"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@babel/parser": "^7.26.9", "@jridgewell/trace-mapping": "^0.3.25", "@polka/compression": "^1.0.0-next.25", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-dynamic-import-vars": "2.1.4", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "16.0.0", "@rollup/pluginutils": "^5.1.4", "@types/escape-html": "^1.0.4", "@types/pnpapi": "^0.0.5", "artichokie": "^0.3.1", "cac": "^6.7.14", "chokidar": "^3.6.0", "connect": "^3.7.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.6", "debug": "^4.4.0", "dep-types": "link:./src/types", "dotenv": "^16.4.7", "dotenv-expand": "^12.0.1", "es-module-lexer": "^1.6.0", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "http-proxy": "^1.18.1", "launch-editor-middleware": "^2.10.0", "lightningcss": "^1.29.2", "magic-string": "^0.30.17", "mlly": "^1.7.4", "mrmime": "^2.0.1", "nanoid": "^5.1.3", "open": "^10.1.0", "parse5": "^7.2.1", "pathe": "^2.0.3", "periscopic": "^4.0.2", "picocolors": "^1.1.1", "picomatch": "^4.0.2", "postcss-import": "^16.1.0", "postcss-load-config": "^6.0.1", "postcss-modules": "^6.0.1", "resolve.exports": "^2.0.3", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-esbuild": "^6.2.1", "rollup-plugin-license": "^3.6.0", "sass": "^1.85.1", "sass-embedded": "^1.85.1", "sirv": "^3.0.1", "source-map-support": "^0.5.21", "strip-literal": "^3.0.0", "terser": "^5.39.0", "tinyglobby": "^0.2.12", "tsconfck": "^3.1.5", "tslib": "^2.8.1", "types": "link:./types", "ufo": "^1.5.4", "ws": "^8.18.1"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "../../node_modules/.pnpm/vitest@3.0.9_@types+node@22.13.14_jiti@2.4.2_jsdom@20.0.3_supports-color@8.1.1__msw@2.7.3_@ty_wh7wi5sq2skfll4c6qhvzorm5y/node_modules/vitest": {"version": "3.0.9", "dev": true, "license": "MIT", "dependencies": {"@vitest/expect": "3.0.9", "@vitest/mocker": "3.0.9", "@vitest/pretty-format": "^3.0.9", "@vitest/runner": "3.0.9", "@vitest/snapshot": "3.0.9", "@vitest/spy": "3.0.9", "@vitest/utils": "3.0.9", "chai": "^5.2.0", "debug": "^4.4.0", "expect-type": "^1.1.0", "magic-string": "^0.30.17", "pathe": "^2.0.3", "std-env": "^3.8.0", "tinybench": "^2.9.0", "tinyexec": "^0.3.2", "tinypool": "^1.0.2", "tinyrainbow": "^2.0.0", "vite": "^5.0.0 || ^6.0.0", "vite-node": "3.0.9", "why-is-node-running": "^2.3.0"}, "bin": {"vitest": "vitest.mjs"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@antfu/install-pkg": "^1.0.0", "@edge-runtime/vm": "^5.0.0", "@sinonjs/fake-timers": "14.0.0", "@types/debug": "^4.1.12", "@types/estree": "^1.0.6", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-reports": "^3.0.4", "@types/jsdom": "^21.1.7", "@types/micromatch": "^4.0.9", "@types/node": "^22.13.5", "@types/prompts": "^2.4.9", "@types/sinonjs__fake-timers": "^8.1.5", "acorn-walk": "^8.3.4", "birpc": "0.2.19", "cac": "^6.7.14", "chai-subset": "^1.6.0", "find-up": "^6.3.0", "flatted": "^3.3.3", "get-tsconfig": "^4.10.0", "happy-dom": "^17.1.4", "jsdom": "^26.0.0", "local-pkg": "^1.0.0", "micromatch": "^4.0.8", "pretty-format": "^29.7.0", "prompts": "^2.4.2", "strip-literal": "^3.0.0", "tinyglobby": "^0.2.12", "ws": "^8.18.1"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/debug": "^4.1.12", "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "@vitest/browser": "3.0.9", "@vitest/ui": "3.0.9", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/debug": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "../../node_modules/.pnpm/zod@3.23.8/node_modules/zod": {"version": "3.23.8", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@jest/globals": "^29.4.3", "@rollup/plugin-typescript": "^8.2.0", "@swc/core": "^1.3.66", "@swc/jest": "^0.2.26", "@types/benchmark": "^2.1.0", "@types/jest": "^29.2.2", "@types/node": "14", "@typescript-eslint/eslint-plugin": "^5.15.0", "@typescript-eslint/parser": "^5.15.0", "babel-jest": "^29.5.0", "benchmark": "^2.1.4", "dependency-cruiser": "^9.19.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-ban": "^1.6.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^7.0.4", "jest": "^29.3.1", "lint-staged": "^12.3.7", "nodemon": "^2.0.15", "prettier": "^2.6.0", "pretty-quick": "^3.1.3", "rollup": "^2.70.1", "ts-jest": "^29.1.0", "ts-morph": "^14.0.0", "ts-node": "^10.9.1", "tslib": "^2.3.1", "tsx": "^3.8.0", "typescript": "~4.5.5", "vitest": "^0.32.2"}, "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "node_modules/@types/pg": {"resolved": "../../node_modules/.pnpm/@types+pg@8.11.11/node_modules/@types/pg", "link": true}, "node_modules/@vitest/coverage-v8": {"resolved": "../../node_modules/.pnpm/@vitest+coverage-v8@3.0.9_supports-color@8.1.1_vitest@3.0.9_@types+node@22.13.14_jiti@2.4.2_j_qve2guu6le5ehdzprie3vw47pi/node_modules/@vitest/coverage-v8", "link": true}, "node_modules/npm-run-all": {"resolved": "../../node_modules/.pnpm/npm-run-all@4.1.5/node_modules/npm-run-all", "link": true}, "node_modules/pg": {"resolved": "../../node_modules/.pnpm/pg@8.13.1/node_modules/pg", "link": true}, "node_modules/postgres-array": {"resolved": "../../node_modules/.pnpm/postgres-array@3.0.2/node_modules/postgres-array", "link": true}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.5.2/node_modules/typescript", "link": true}, "node_modules/vite": {"resolved": "../../node_modules/.pnpm/vite@6.2.4_@types+node@22.13.14_jiti@2.4.2_sass@1.72.0_terser@5.39.0_tsx@4.19.3_yaml@2.4.5/node_modules/vite", "link": true}, "node_modules/vitest": {"resolved": "../../node_modules/.pnpm/vitest@3.0.9_@types+node@22.13.14_jiti@2.4.2_jsdom@20.0.3_supports-color@8.1.1__msw@2.7.3_@ty_wh7wi5sq2skfll4c6qhvzorm5y/node_modules/vitest", "link": true}, "node_modules/zod": {"resolved": "../../node_modules/.pnpm/zod@3.23.8/node_modules/zod", "link": true}}}