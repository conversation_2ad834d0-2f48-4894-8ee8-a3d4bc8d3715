{"name": "eslint-config-supabase", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "eslint-config-supabase", "version": "1.0.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"eslint-config-next": "^14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-config-turbo": "^2.0.4"}}, "../../node_modules/.pnpm/eslint-config-next@14.2.4_eslint@8.57.0_supports-color@8.1.1__supports-color@8.1.1_typescript@5.5.2/node_modules/eslint-config-next": {"version": "14.2.4", "license": "MIT", "dependencies": {"@next/eslint-plugin-next": "14.2.4", "@rushstack/eslint-patch": "^1.3.3", "@typescript-eslint/parser": "^5.4.2 || ^6.0.0 || 7.0.0 - 7.2.0", "eslint-import-resolver-node": "^0.3.6", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.5.0 || 5.0.0-canary-7118f5dd7-20230705"}, "peerDependencies": {"eslint": "^7.23.0 || ^8.0.0", "typescript": ">=3.3.1"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "../../node_modules/.pnpm/eslint-config-prettier@9.1.0_eslint@8.57.0_supports-color@8.1.1_/node_modules/eslint-config-prettier": {"version": "9.1.0", "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "../../node_modules/.pnpm/eslint-config-turbo@2.0.4_eslint@8.57.0_supports-color@8.1.1_/node_modules/eslint-config-turbo": {"version": "2.0.4", "license": "MIT", "dependencies": {"eslint-plugin-turbo": "2.0.4"}, "devDependencies": {"@turbo/eslint-config": "0.0.0", "@types/eslint": "^8.44.2"}, "peerDependencies": {"eslint": ">6.6.0"}}, "node_modules/eslint-config-next": {"resolved": "../../node_modules/.pnpm/eslint-config-next@14.2.4_eslint@8.57.0_supports-color@8.1.1__supports-color@8.1.1_typescript@5.5.2/node_modules/eslint-config-next", "link": true}, "node_modules/eslint-config-prettier": {"resolved": "../../node_modules/.pnpm/eslint-config-prettier@9.1.0_eslint@8.57.0_supports-color@8.1.1_/node_modules/eslint-config-prettier", "link": true}, "node_modules/eslint-config-turbo": {"resolved": "../../node_modules/.pnpm/eslint-config-turbo@2.0.4_eslint@8.57.0_supports-color@8.1.1_/node_modules/eslint-config-turbo", "link": true}}}