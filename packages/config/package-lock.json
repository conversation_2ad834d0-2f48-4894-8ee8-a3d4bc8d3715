{"name": "config", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "config", "version": "0.0.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"@mertasan/tailwindcss-variables": "^2.2.3", "@radix-ui/colors": "^0.1.8", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.9", "deepmerge": "^4.2.2", "mini-svg-data-uri": "^1.4.3", "tailwindcss-radix": "^2.0.0"}, "devDependencies": {"tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.6"}}, "../../node_modules/.pnpm/@mertasan+tailwindcss-variables@2.7.0_autoprefixer@10.4.16_postcss@8.5.3__postcss@8.5.3/node_modules/@mertasan/tailwindcss-variables": {"version": "2.7.0", "license": "MIT", "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "fs-extra": "^10.0.0", "jest": "^29.4.3", "postcss": "^8.4.21", "postcss-import": "^14.1.0", "prettier": "^2.5.0", "snapshot-diff": "^0.10.0", "tailwindcss": "^3.2.7"}, "engines": {"node": ">=12.13.0"}, "peerDependencies": {"autoprefixer": "^10.0.2", "postcss": "^8.0.9"}}, "../../node_modules/.pnpm/@radix-ui+colors@0.1.9/node_modules/@radix-ui/colors": {"version": "0.1.9", "license": "MIT", "devDependencies": {"@rollup/plugin-typescript": "^8.2.1", "@types/node": "^15.0.3", "rollup": "^2.48.0", "tslib": "^2.2.0", "typescript": "^4.2.4"}}, "../../node_modules/.pnpm/@tailwindcss+forms@0.5.6_tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2__/node_modules/@tailwindcss/forms": {"version": "0.5.6", "license": "MIT", "dependencies": {"mini-svg-data-uri": "^1.2.3"}, "devDependencies": {"autoprefixer": "^10.4.6", "concurrently": "^5.3.0", "live-server": "^1.2.2", "postcss": "^8.4.13", "tailwindcss": "^3.0.24"}, "peerDependencies": {"tailwindcss": ">=3.0.0 || >= 3.0.0-alpha.1"}}, "../../node_modules/.pnpm/@tailwindcss+typography@0.5.10_tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2__/node_modules/@tailwindcss/typography": {"version": "0.5.10", "license": "MIT", "dependencies": {"lodash.castarray": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.merge": "^4.6.2", "postcss-selector-parser": "6.0.10"}, "devDependencies": {"@mdx-js/loader": "^1.0.19", "@mdx-js/mdx": "^1.6.6", "@next/mdx": "^8.1.0", "autoprefixer": "^10.2.1", "highlight.js": "^10.4.1", "jest": "^26.6.1", "jest-diff": "^27.3.1", "next": "^12.0.1", "postcss": "^8.2.3", "prettier": "^2.1.2", "react": "^17.0.2", "react-dom": "^17.0.2", "tailwindcss": "^3.2.2"}, "peerDependencies": {"tailwindcss": ">=3.0.0 || insiders"}}, "../../node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge": {"version": "4.3.1", "license": "MIT", "devDependencies": {"@types/node": "^8.10.54", "is-mergeable-object": "1.1.0", "is-plain-object": "^5.0.0", "jsmd": "^1.0.2", "rollup": "^1.23.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tape": "^4.11.0", "ts-node": "7.0.1", "typescript": "=2.2.2", "uglify-js": "^3.6.1"}, "engines": {"node": ">=0.10.0"}}, "../../node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules/mini-svg-data-uri": {"version": "1.4.4", "license": "MIT", "bin": {"mini-svg-data-uri": "cli.js"}}, "../../node_modules/.pnpm/tailwindcss-animate@1.0.7_tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2__/node_modules/tailwindcss-animate": {"version": "1.0.7", "dev": true, "license": "MIT", "devDependencies": {"husky": "^7.0.4", "lint-staged": "^12.3.4", "prettier": "^2.5.1", "tailwindcss": "^3.0.22"}, "peerDependencies": {"tailwindcss": ">=3.0.0 || insiders"}}, "../../node_modules/.pnpm/tailwindcss-radix@2.8.0/node_modules/tailwindcss-radix": {"version": "2.8.0", "license": "MIT", "devDependencies": {"@types/node": "^18.0.6", "husky": "^8.0.1", "prettier": "2.7.1", "release-it": "^15.1.3", "rimraf": "^3.0.2", "tailwindcss": "^3.1.6", "typescript": "^4.7.4"}}, "../../node_modules/.pnpm/tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2_/node_modules/tailwindcss": {"version": "3.4.1", "dev": true, "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.5.3", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.0", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.19.1", "lilconfig": "^2.1.0", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.23", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.1", "postcss-nested": "^6.0.1", "postcss-selector-parser": "^6.0.11", "resolve": "^1.22.2", "sucrase": "^3.32.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "devDependencies": {"@swc/cli": "^0.1.62", "@swc/core": "^1.3.55", "@swc/jest": "^0.2.26", "@swc/register": "^0.1.10", "autoprefixer": "^10.4.14", "browserslist": "^4.21.5", "concurrently": "^8.0.1", "cssnano": "^6.0.0", "esbuild": "^0.17.18", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.6.0", "jest-diff": "^29.6.0", "lightningcss": "1.18.0", "prettier": "^2.8.8", "rimraf": "^5.0.0", "source-map-js": "^1.0.2", "turbo": "^1.9.3"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@mertasan/tailwindcss-variables": {"resolved": "../../node_modules/.pnpm/@mertasan+tailwindcss-variables@2.7.0_autoprefixer@10.4.16_postcss@8.5.3__postcss@8.5.3/node_modules/@mertasan/tailwindcss-variables", "link": true}, "node_modules/@radix-ui/colors": {"resolved": "../../node_modules/.pnpm/@radix-ui+colors@0.1.9/node_modules/@radix-ui/colors", "link": true}, "node_modules/@tailwindcss/forms": {"resolved": "../../node_modules/.pnpm/@tailwindcss+forms@0.5.6_tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2__/node_modules/@tailwindcss/forms", "link": true}, "node_modules/@tailwindcss/typography": {"resolved": "../../node_modules/.pnpm/@tailwindcss+typography@0.5.10_tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2__/node_modules/@tailwindcss/typography", "link": true}, "node_modules/deepmerge": {"resolved": "../../node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge", "link": true}, "node_modules/mini-svg-data-uri": {"resolved": "../../node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules/mini-svg-data-uri", "link": true}, "node_modules/tailwindcss": {"resolved": "../../node_modules/.pnpm/tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2_/node_modules/tailwindcss", "link": true}, "node_modules/tailwindcss-animate": {"resolved": "../../node_modules/.pnpm/tailwindcss-animate@1.0.7_tailwindcss@3.4.1_ts-node@10.9.2_@types+node@22.13.14_typescript@5.5.2__/node_modules/tailwindcss-animate", "link": true}, "node_modules/tailwindcss-radix": {"resolved": "../../node_modules/.pnpm/tailwindcss-radix@2.8.0/node_modules/tailwindcss-radix", "link": true}}}