name: Automatically fix typos
on:
  push:
    branches:
      - master

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  pull-requests: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: master
      - uses: sobolevn/misspell-fixer-action@master
      - uses: peter-evans/create-pull-request@v4
        env:
          ACTIONS_ALLOW_UNSECURE_COMMANDS: 'true'
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
