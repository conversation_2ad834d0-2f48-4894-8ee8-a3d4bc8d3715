name: '[Docs] Lint v2'
on:
  pull_request:

env:
  CARGO_NET_GIT_FETCH_WITH_CLI: true

permissions:
  pull-requests: write

jobs:
  supa-mdx-lint:
    name: supa-mdx-lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            supa-mdx-lint.config.toml
            supa-mdx-lint
            apps/docs/content
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 #v3.0.2
        id: filter
        with:
          filters: |
            docs:
              - 'apps/docs/content/**'
              - 'supa-mdx-lint/**'
              - 'supa-mdx-lint.config.toml'
      - name: cache cargo
        id: cache-cargo
        if: steps.filter.outputs.docs == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
          key: e5609278486869b545fda23b5732727b6e70a1c1
      - name: install linter
        if: steps.filter.outputs.docs == 'true' && steps.cache-cargo.outputs.cache-hit != 'true'
        run: cargo install --locked --git https://github.com/supabase-community/supa-mdx-lint --rev e5609278486869b545fda23b5732727b6e70a1c1
      - name: install reviewdog
        if: steps.filter.outputs.docs == 'true'
        uses: reviewdog/action-setup@3f401fe1d58fe77e10d665ab713057375e39b887 # v1.3.0
        with:
          reviewdog_version: v0.20.2
      - name: run linter (internal)
        if: steps.filter.outputs.docs == 'true' && github.event.pull_request.head.repo.full_name == github.repository
        env:
          BASE_REF: ${{ github.base_ref }}
          REVIEWDOG_GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          set -o pipefail
          git diff --name-only "origin/$BASE_REF" HEAD \
            | { grep -E "^apps/docs/content/" || test $? = 1; } \
            | xargs -r supa-mdx-lint --format rdf \
            | reviewdog -f=rdjsonl -reporter=github-pr-review -tee
      - name: run linter (external)
        if: steps.filter.outputs.docs == 'true' && github.event.pull_request.head.repo.full_name != github.repository
        env:
          BASE_REF: ${{ github.base_ref }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          BRANCH_NAME: ${{ github.head_ref }}
        run: |
          set -o pipefail
          run_lints() {
            git diff --name-only "origin/$BASE_REF" HEAD \
              | { grep -E "^apps/docs/content/" || test $? = 1; } \
              | xargs -rx -n 1000000000 supa-mdx-lint --format markdown
          }
          set +e
          LINT_RESULTS=$(run_lints)
          LINT_EXIT_CODE=$?
          set -e
          if [[ $LINT_EXIT_CODE -ne 0 ]]; then
            gh pr comment "$BRANCH_NAME" --body "$LINT_RESULTS"
            exit 1
          fi
