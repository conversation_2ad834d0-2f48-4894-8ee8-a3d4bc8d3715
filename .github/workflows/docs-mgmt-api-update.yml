name: Update Mgmt Api Docs

on:
  schedule:
    # Run at 00:00 UTC every Monday
    - cron: '0 0 * * 1'
  workflow_dispatch:

permissions:
  pull-requests: write
  contents: read

jobs:
  update-docs:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: master
          sparse-checkout: |
            apps/docs

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install deps
        run: pnpm i

      - name: Change to apps/docs/spec directory and run make command
        working-directory: apps/docs/spec
        run: make download.api.v1 dereference.api.v1 generate.sections.api.v1 format

      - name: Create pull request
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'feat: update mgmt api docs'
          title: 'feat: update mgmt api docs'
          body: 'This PR updates mgmt api docs automatically.'
          branch: 'gha/auto-update-mgmt-api-docs'
          base: 'master'
