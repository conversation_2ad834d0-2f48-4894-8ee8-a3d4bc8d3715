name: Autofix Linting Checks

on:
  pull_request:
    types:
      - labeled

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  autofix:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    if: ${{ github.event_name == 'pull_request' && (github.event.label.name == 'autofix') }}
    steps:
      - name: Calculate number of commits
        run: echo "PR_FETCH_DEPTH=$(( ${{ github.event.pull_request.commits }} + 1 ))" >> "${GITHUB_ENV}"

      - uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          token: ${{ secrets.PAT_AUTOFIX }}
          fetch-depth: ${{ env.PR_FETCH_DEPTH }}
          sparse-checkout: |
            packages
            apps

      - uses: pnpm/action-setup@v4
        name: Install pnpm

      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: pnpm

      - name: Install required dependencies
        run: pnpm i

      - name: Run Prettier in fix mode
        run: pnpm run format

      - name: Commit changes and push to existing branch
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "ci: Autofix updates from GitHub workflow"
          commit_user_name: "kevcodez"
          commit_user_email: "<EMAIL>"
