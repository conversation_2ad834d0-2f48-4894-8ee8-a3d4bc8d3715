name: PG Meta Tests

on:
  push:
    branches: ['master']
    paths:
      - 'packages/pg-meta/**/*'
  pull_request:
    branches: ['master']
    paths:
      - 'packages/pg-meta/**/*'

# Cancel old builds on new commit for same workflow + branch/PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  id-token: write

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          sparse-checkout: |
            packages/pg-meta
            packages/tsconfig

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'

      - name: Install deps
        run: pnpm i

      - name: Run tests
        run: pnpm --filter=@supabase/pg-meta run test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          directory: packages/pg-meta/coverage
          flags: pg-meta
