{"name": "with-nextjs-13", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.0.4", "next": "13.0.0", "react": "18.2.0", "react-dom": "18.2.0", "server-only": "^0.0.1"}, "devDependencies": {"@types/node": "18.11.7", "@types/react": "18.0.24", "@types/react-dom": "18.0.8", "eslint": "8.26.0", "eslint-config-next": "13.0.0", "typescript": "4.8.4"}}