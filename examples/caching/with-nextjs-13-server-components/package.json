{"name": "next13", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.1.0", "@types/node": "18.11.9", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "eslint": "8.27.0", "eslint-config-next": "13.0.3", "next": "13.0.3", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "4.8.4"}}