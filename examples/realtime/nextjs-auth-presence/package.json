{"name": "nextjs-auth-presence", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"pages/**/*.{js,jsx,ts,tsx,css,md,json}\""}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.6.0", "@supabase/auth-helpers-react": "^0.3.1", "@supabase/auth-ui-react": "^0.2.7", "@supabase/supabase-js": "^2.7.1", "next": "^13.1.6", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "18.11.0", "@types/react": "18.0.28", "@types/react-dom": "18.0.10", "eslint": "8.25.0", "eslint-config-next": "12.3.1", "typescript": "4.8.4"}}