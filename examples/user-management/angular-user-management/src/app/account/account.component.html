<form [formGroup]="updateProfileForm" (ngSubmit)="updateProfile()" class="form-widget">
  <app-avatar
    [avatarUrl]="this.avatarUrl"
    (upload)="updateAvatar($event)">
  </app-avatar>
  <div>
    <label for="email">Email</label>
    <input id="email" type="text" [value]="session.user.email" disabled />
  </div>
  <div>
    <label for="username">Name</label>
    <input
      formControlName="username"
      id="username"
      type="text"
    />
  </div>
  <div>
    <label for="website">Website</label>
    <input
      formControlName="website"
      id="website"
      type="url"
    />
  </div>

  <div>
    <button
      type="submit"
      class="button primary block"
      [disabled]="loading"
    >
      {{ loading ? 'Loading ...' : 'Update' }}
    </button>
  </div>

  <div>
    <button class="button block" (click)="signOut()">Sign Out</button>
  </div>
</form>
