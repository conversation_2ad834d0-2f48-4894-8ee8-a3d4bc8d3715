{"name": "expo-user-management", "version": "2.0.0", "scripts": {"start": "expo start --dev-client", "prebuild": "expo prebuild", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "format": "prettier --write \"**/*.{js,json,md,ts,tsx,jsx,css}\""}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@rneui/themed": "^4.0.0-rc.8", "@supabase/supabase-js": "^2.0.4", "expo": "~51.0.8", "expo-splash-screen": "~0.27.4", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.1", "react-native-document-picker": "^8.1.1", "react-native-url-polyfill": "^1.3.0", "react-native-web": "~0.19.10"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/react": "~18.2.79", "@types/react-native": "^0.69.5", "prettier": "^2.7.1", "typescript": "~5.3.3"}, "private": true}