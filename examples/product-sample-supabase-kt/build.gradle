buildscript {
    ext {
        compose_version = '1.3.0'
        hilt_version = '2.48'
        ktor_version = '2.3.0'
    }
}// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.application' version '7.4.2' apply false
    id 'com.android.library' version '7.4.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.0' apply false
    id("com.google.dagger.hilt.android") version "2.44" apply false
    id 'org.jetbrains.kotlin.plugin.serialization' version '1.7.0'
}