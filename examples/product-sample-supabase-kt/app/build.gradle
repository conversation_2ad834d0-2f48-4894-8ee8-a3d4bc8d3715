plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.dagger.hilt.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'org.jetbrains.kotlin.plugin.serialization' version '1.7.0'
}

android {
    namespace 'com.example.manageproducts'
    compileSdk 33

    defaultConfig {
        applicationId "com.example.manageproducts"
        minSdk 28
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
        Properties properties = new Properties()
        properties.load(project.rootProject.file("local.properties").newDataInputStream())
        buildConfigField("String", "API_KEY", "\"${properties.getProperty("API_KEY")}\"")
        buildConfigField("String", "SECRET", "\"${properties.getProperty("SECRET")}\"")
        buildConfigField("String", "SUPABASE_URL", "\"${properties.getProperty("SUPABASE_URL")}\"")
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.2.0'
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.3.1'
    implementation 'androidx.activity:activity-compose:1.3.1'
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation 'androidx.compose.material3:material3:1.0.0-alpha11'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    debugImplementation "androidx.compose.ui:ui-test-manifest:$compose_version"

    implementation 'androidx.compose.material3:material3:1.0.0-alpha02'
    implementation "androidx.compose.material:material:1.1.1"
    
    // Supabase setup
    implementation "io.github.jan-tennert.supabase:gotrue-kt:$supabase_version"
    implementation "io.github.jan-tennert.supabase:postgrest-kt:$supabase_version"
    implementation "io.github.jan-tennert.supabase:storage-kt:$supabase_version"
    implementation "io.ktor:ktor-client-android:$ktor_version"
    implementation "io.ktor:ktor-utils:$ktor_version"
    implementation "io.ktor:ktor-client-core:$ktor_version"
    implementation "io.coil-kt:coil-compose:1.3.2"


    implementation "com.google.dagger:hilt-android:$hilt_version"
    annotationProcessor "com.google.dagger:hilt-compiler:$hilt_version"
    implementation "androidx.hilt:hilt-navigation-compose:1.0.0"

    // For instrumentation tests
    androidTestImplementation  "com.google.dagger:hilt-android-testing:$hilt_version"
    androidTestAnnotationProcessor "com.google.dagger:hilt-compiler:$hilt_version"
    kapt "com.google.dagger:hilt-android-compiler:$hilt_version"

    implementation 'androidx.navigation:navigation-compose:2.5.3'
    implementation 'com.google.accompanist:accompanist-swiperefresh:0.24.13-rc'
}