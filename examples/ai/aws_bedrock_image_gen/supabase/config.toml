# A string used to distinguish different Supabase projects on the same host. Defaults to the
# working directory name when running `supabase init`.
project_id = "aws_bedrock_image_gen"

[api]
# Disable data API since we are not using the PostgREST client in this example.
enabled = false

[storage]
enabled = true
# The maximum file size allowed for all buckets in the project.
file_size_limit = "50MiB"

[functions.image_gen]
enabled = true
verify_jwt = true
# import_map = "./functions/image_gen/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
# entrypoint = "./functions/image_gen/index.ts"
