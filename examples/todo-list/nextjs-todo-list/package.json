{"name": "nextjs-todo-list", "version": "2.0.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:css\" \"next dev\"", "dev:css": "tailwindcss -w -i ./styles/tailwind.css -o styles/app.css", "build": "next build", "build:css": "tailwindcss -m -i ./styles/tailwind.css -o styles/app.css", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/font": "13.1.6", "@supabase/auth-helpers-react": "^0.3.1", "@supabase/auth-ui-react": "^0.2.8", "@supabase/supabase-js": "^2.8.0", "@types/node": "18.14.0", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "eslint": "8.34.0", "eslint-config-next": "13.1.6", "next": "13.1.6", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "4.9.5"}, "devDependencies": {"concurrently": "^7.6.0", "tailwindcss": "^3.2.7"}}