{"name": "sveltejs-todo-list", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:css\" \"vite\"", "dev:css": "tailwindcss -w -i ./src/tailwind.css -o src/assets/app.css", "build": "npm run build:css && vite build", "build:css": "tailwindcss -m -i ./src/tailwind.css -o src/app.css", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^1.0.1", "@tsconfig/svelte": "^3.0.0", "concurrently": "^7.3.0", "svelte": "^3.49.0", "svelte-check": "^2.8.0", "svelte-hcaptcha": "^0.1.1", "svelte-preprocess": "^4.10.7", "tailwindcss": "^3.1.8", "tslib": "^2.4.0", "typescript": "^4.7.4", "vite": "^4.3.9"}, "dependencies": {"@supabase/supabase-js": "^2.0.4"}}