create table discord_promise_challenge (
  id bigint generated by default as identity primary key,
  inserted_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null,
  user_id text not null,
  username text not null,
  promise text not null,
  email text,
  submission text,
  resolved boolean default false
);
alter table discord_promise_challenge enable row level security;