# A string used to distinguish different Supabase projects on the same host. Defaults to the working
# directory name when running `supabase init`.
project_id = "edge-functions"

[api]
enabled = true
# Port to use for the API URL.
port = 54321
# Schemas to expose in your API. Tables, views and stored procedures in this schema will get API
# endpoints. `public` is always included.
schemas = ["public", "graphql_public"]
# Extra schemas to add to the search_path of every request. `public` is always included.
extra_search_path = ["public", "extensions"]
# The maximum number of rows returns from a view, table, or stored procedure. Limits payload size
# for accidental or malicious requests.
max_rows = 1000

[db]
# Port to use for the local database URL.
port = 54322
# Port used by db diff command to initialize the shadow database.
shadow_port = 54320
# The database major version to use. This has to be the same as your remote database's. Run `SHOW
# server_version;` on the remote database to check.
major_version = 15

[db.seed]
# If enabled, seeds the database after migrations during a db reset.
enabled = false
# Specifies an ordered list of seed files to load during db reset.
# Supports glob patterns relative to supabase directory. For example:
# sql_paths = ['./seeds/*.sql', '../project-src/seeds/*-load-testing.sql']

[storage.buckets.my-bucket]
[storage.buckets.videos]

[storage.buckets.images]
public = true
objects_path = "./buckets/images"

[functions.auth-hook-react-email-resend]
[functions.background-upload-storage]
[functions.browser-with-cors]
[functions.cloudflare-turnstile]
[functions.connect-supabase]
[functions.discord-bot]
[functions.file-upload-storage]
[functions.get-tshirt-competition]
[functions.github-action-deploy]
[functions.huggingface-image-captioning]
[functions.image-manipulation]
[functions.location]
[functions.oak-server]
[functions.og-image-with-storage-cdn]
[functions.openai]
[functions.opengraph]
[functions.postgres-on-the-edge]
[functions.puppeteer]
[functions.read-storage]
[functions.restful-tasks]
[functions.select-from-table-with-auth-rls]
[functions.send-email-resend]
[functions.send-email-smtp]
[functions.sentry]
[functions.sentryfied]
[functions.slack-bot-mention]
[functions.streams]
[functions.stripe-webhooks]
[functions.telegram-bot]
[functions.tweet-to-image]
[functions.upstash-redis-counter]
[functions.upstash-redis-ratelimit]

[functions.kysely-postgres]
import_map = "./functions/import_map.json"

[functions.wasm-modules]
static_files = [ "./functions/wasm-modules/add-wasm/pkg/*.wasm"]
