# auth hook react email resend
RESEND_API_KEY=your_resend_api_key
SEND_EMAIL_HOOK_SECRET=base64_secret

# cloudflare-turnstile
CLOUDFLARE_TURNSTILE_SECRET_KEY=your_secret_key

# discord-bot 
DISCORD_PUBLIC_KEY=

# location
IPINFO_TOKEN="your https://ipinfo.io token"

# openai
OPENAI_API_KEY="<YOUR API KEY HERE>"

# postgres-on-the-edge & kysely-postgres
# get your DB params and cert from the project dashboard
# https://supabase.com/dashboard/project/_/settings/database
# enable `Use connection pooling` and use `Transaction` mode
DB_HOSTNAME=
DB_PASSWORD=
DB_USER=
DB_SSL_CERT="-----BEGIN CERTIFICATE-----
-----END CERTIFICATE-----"

# puppeteer 
PUPPETEER_BROWSERLESS_IO_TOKEN=

# send-email-resend
RESEND_API_KEY=

# send-email-smtp
SMTP_HOSTNAME="your.hostname.com"
SMTP_PORT="2587"
SMTP_USERNAME="your_username"
SMTP_PASSWORD="your_password"
SMTP_FROM="<EMAIL>"

# stripe-webhooks
STRIPE_API_KEY="<YOUR API KEY HERE>"
STRIPE_WEBHOOK_SIGNING_SECRET="<YOUR WEBHOOK SIGNING HERE>"

# telegram-bot
TELEGRAM_BOT_TOKEN="get it from https://t.me/BotFather"
FUNCTION_SECRET="random secret"

# upstash-redis-counter 
UPSTASH_REDIS_REST_URL=
UPSTASH_REDIS_REST_TOKEN=

# connect-supabase - https://supabase.com/docs/guides/platform/oauth-apps/publish-an-oauth-app
SUPA_CONNECT_CLIENT_ID=
SUPA_CONNECT_CLIENT_SECRET=

# elevenlabs-text-to-speech
ELEVENLABS_API_KEY=
