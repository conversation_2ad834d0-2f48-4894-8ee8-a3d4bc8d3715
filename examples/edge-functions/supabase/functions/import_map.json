{"imports": {"denomailer": "https://deno.land/x/denomailer@0.12.0/mod.ts", "nacl": "https://cdn.skypack.dev/tweetnacl@v1.0.3?dts", "oak": "https://deno.land/x/oak@v11.1.0/mod.ts", "og_edge": "https://deno.land/x/og_edge@0.0.4/mod.ts", "openai": "https://esm.sh/openai@3.1.0", "grammy": "https://deno.land/x/grammy@v1.8.3/mod.ts", "react": "https://esm.sh/react@18.2.0", "std/server": "https://deno.land/std@0.177.0/http/server.ts", "stripe": "https://esm.sh/stripe@11.1.0?target=deno", "sift": "https://deno.land/x/sift@0.6.0/mod.ts", "@supabase/supabase-js": "jsr:@supabase/supabase-js@2", "postgres": "https://deno.land/x/postgres@v0.17.0/mod.ts", "puppeteer": "https://deno.land/x/puppeteer@16.2.0/mod.ts", "React": "https://esm.sh/react@18.2.0?deno-std=0.177.0", "upstash_redis": "https://deno.land/x/upstash_redis@v1.19.3/mod.ts", "xhr_polyfill": "https://deno.land/x/xhr@0.3.0/mod.ts", "kysely": "https://esm.sh/kysely@0.23.4"}}