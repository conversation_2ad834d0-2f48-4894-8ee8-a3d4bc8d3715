{"name": "app", "version": "0.1.0", "private": true, "dependencies": {"@supabase/auth-ui-react": "^0.2.1", "@supabase/supabase-js": "^2.0.0", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-json-editor-ajrm": "^2.5.13", "react-scripts": "5.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.4", "postcss": "^8.4.12", "tailwindcss": "^3.0.23"}}