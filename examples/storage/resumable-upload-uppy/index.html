<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Resumable Upload Supabase + UppyJS</title>
    <link href="https://releases.transloadit.com/uppy/v3.6.1/uppy.min.css" rel="stylesheet" />

    <style>
      html {
        background: #9e44ef;
      }
      body {
        height: 100vh;
        background: radial-gradient(72.03% 66.03% at 50% 69.72%, #dbb8bf 0, transparent 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      a {
        display: block;
        margin: 10px;
        text-decoration: none;
      }
      #logo {
        max-width: 150px;
      }
      #drag-drop-area {
        margin-top: 40px;
      }
    </style>
  </head>
  <body>
    <img id="logo" src="supabase-logo-wordmark--dark.png" />
    <div id="drag-drop-area"></div>
    <a href="https://supabase.com/docs/guides/storage/uploads/resumable-uploads" target="_blank"
      >Read the docs.</a
    >

    <script type="module">
      import {
        Uppy,
        Dashboard,
        Tus,
      } from 'https://releases.transloadit.com/uppy/v3.6.1/uppy.min.mjs'

      const SUPABASE_ANON_KEY = 'replace-with-your-anon-key'
      const SUPABASE_PROJECT_ID = 'replace-with-your-project-id'
      const STORAGE_BUCKET = 'replace-with-your-bucket-id'
      const BEARER_TOKEN='replace-with-your-bearer-token'

      const folder = ''
      const supabaseStorageURL = `https://${SUPABASE_PROJECT_ID}.supabase.co/storage/v1/upload/resumable`

      var uppy = new Uppy()
        .use(Dashboard, {
          inline: true,
          limit: 10,
          target: '#drag-drop-area',
          showProgressDetails: true,
        })
        .use(Tus, {
          endpoint: supabaseStorageURL,
          headers: {
            authorization: `Bearer ${BEARER_TOKEN}`,
            apikey: SUPABASE_ANON_KEY,
          },
          uploadDataDuringCreation: true,
          chunkSize: 6 * 1024 * 1024,
          allowedMetaFields: ['bucketName', 'objectName', 'contentType', 'cacheControl'],
          onError: function (error) {
            console.log('Failed because: ' + error)
          },
        })

      uppy.on('file-added', (file) => {
        const supabaseMetadata = {
          bucketName: STORAGE_BUCKET,
          objectName: folder ? `${folder}/${file.name}` : file.name,
          contentType: file.type,
        }

        file.meta = {
          ...file.meta,
          ...supabaseMetadata,
        }

        console.log('file added', file)
      })

      uppy.on('complete', (result) => {
        console.log('Upload complete! We’ve uploaded these files:', result.successful)
      })
    </script>
  </body>
</html>
