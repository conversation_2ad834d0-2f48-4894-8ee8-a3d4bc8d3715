# Example .env for preview environments
# Get these from your API settings: https://supabase.com/dashboard/project/_/settings/api
# URL of supabase api will be passed to next frontend build for app deployment
NEXT_PUBLIC_SUPABASE_URL=https://<preview-branch-ref>.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=<preview-branch-apikey>
# The frontend site url, will be used by Supabase Auth services to properly configure auth redirects
SUPABASE_AUTH_SITE_URL=https://<preview-app-url>.vercel.app/
SUPABASE_AUTH_ADDITIONAL_REDIRECT_URLS=https://<preview-app-url>.vercel.app/**
# Credentials for github connection
SUPABASE_AUTH_EXTERNAL_GITHUB_CLIENT_ID=Ov23liopb2cDTY8Nohec
SUPABASE_AUTH_EXTERNAL_GITHUB_SECRET="encrypted:BCsRXT2v9U/5qsCIfJJeZ14FgdoyTIgwy+RNCezWrnjj4C/RecQJFZIP8Ec4yw8bLosHh8bShLXlXfl0iNxWkqqZp+FVeMvt4WMiUfsFJge0TWmAds35dSg/VLCUrEZOYUGCpT8PR6tb7ScA2r8="
