# Example .env for production environment
# Get these from your API settings: https://supabase.com/dashboard/project/_/settings/api
# URL of supabase api will be passed to next frontend build for app deployment
NEXT_PUBLIC_SUPABASE_URL=https://<your-project-ref>.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=<your-project-apikey>
# The frontend site url, will be used by Supabase Auth services to properly configure auth redirects
SUPABASE_AUTH_SITE_URL=https://<your-app-url>.vercel.app/
SUPABASE_AUTH_ADDITIONAL_REDIRECT_URLS=https://<your-app-url>.vercel.app/**
# Credentials for github connection
SUPABASE_AUTH_EXTERNAL_GITHUB_CLIENT_ID=Ov23liopb2cDTY8Nohec
SUPABASE_AUTH_EXTERNAL_GITHUB_SECRET=encrypted:BKPNXvhfgruoT3DWEfDE5Ec3E3G3bpGkcwmQmUw/yQEK/gZQOaFbaYfvIm67RWCeHukSwJC2ylm3HuOOp1FG+B35pHMQFC9rhItizJaEt5ph0IJxARUgysoXAEzk53v1S1mSXY/lyvQAh7gSQBU=
