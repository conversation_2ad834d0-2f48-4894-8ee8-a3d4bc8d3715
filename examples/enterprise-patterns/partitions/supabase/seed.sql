insert into sales (order_date, amount, customer_id)
values
  ('2000-01-01', 100, 1),
  ('2000-01-02', 200, 2),
  ('2000-01-03', 300, 1),
  ('2000-01-04', 400, 2),
  ('2000-01-05', 500, 1),
  ('2000-01-06', 600, 2),
  ('2000-01-07', 700, 1),
  ('2000-01-08', 800, 2),
  ('2000-01-09', 900, 1),
  ('2000-01-10', 1000, 2),
  ('2000-01-11', 1100, 1),
  ('2000-01-12', 1200, 2),
  ('2000-01-13', 1300, 1),
  ('2000-01-14', 1400, 2),
  ('2000-01-15', 1500, 1),
  ('2000-01-16', 1600, 2),
  ('2000-01-17', 1700, 1),
  ('2000-01-18', 1800, 2),
  ('2000-01-19', 1900, 1),
  ('2000-01-20', 2000, 2),
  ('2000-02-01', 100, 1),
  ('2000-02-02', 200, 2),
  ('2000-02-03', 300, 1),
  ('2000-02-04', 400, 2),
  ('2000-02-05', 500, 1),
  ('2000-02-06', 600, 2),
  ('2000-02-07', 700, 1),
  ('2000-02-08', 800, 2),
  ('2000-02-09', 900, 1),
  ('2000-02-10', 1000, 2),
  ('2000-02-11', 1100, 1),
  ('2000-02-12', 1200, 2),
  ('2000-02-13', 1300, 1),
  ('2000-02-14', 1400, 2),
  ('2000-02-15', 1500, 1),
  ('2000-02-16', 1600, 2),
  ('2000-02-17', 1700, 1),
  ('2000-02-18', 1800, 2),
  ('2000-02-19', 1900, 1),
  ('2000-02-20', 2000, 2);

insert into customers (name, country)
values
    ('John Smith', 'US'),
    ('Alice Johnson', 'US'),
    ('Michael Brown', 'US'),
    ('Emily Davis', 'US'),
    ('Robert Johnson', 'US'),
    ('Sarah Wilson', 'US'),
    ('David Thompson', 'CANADA'),
    ('Jennifer Lee', 'CANADA'),
    ('Christopher Martin', 'CANADA'),
    ('Emma Rodriguez', 'CANADA'),
    ('Li Wei', 'CHINA'),
    ('Aarav Patel', 'INDIA'),
    ('Yuki Tanaka', 'JAPAN'),
    ('Ravi Kumar', 'INDIA'),
    ('Zhang Wei', 'CHINA'),
    ('Hiroshi Yamamoto', 'JAPAN'),
    ('Mei Chen', 'CHINA'),
    ('Ananya Gupta', 'INDIA'),
    ('Shinji Kimura', 'JAPAN'),
    ('Sanjay Singh', 'INDIA');

insert into products (name, category, price)
values
    ('Product 1', 'Category A', 100),
    ('Product 2', 'Category B', 200),
    ('Product 3', 'Category A', 150),
    ('Product 4', 'Category C', 300),
    ('Product 5', 'Category B', 250),
    ('Product 6', 'Category C', 350),
    ('Product 7', 'Category A', 120),
    ('Product 8', 'Category B', 180),
    ('Product 9', 'Category C', 270),
    ('Product 10', 'Category A', 220);
