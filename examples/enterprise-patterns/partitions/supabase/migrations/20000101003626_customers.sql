-- Create the partitioned table
create table customers (
    id bigint generated by default as identity,
    name text,
    country text,

    -- We need to include all the 
    -- partitioning columns in constraints:
    primary key (country, id) 
)
partition by list(country);


create table customers_americas 
	partition of customers
	for values in ('US', 'CANADA');

create table customers_asia 
	partition of customers
  for values in ('INDIA', 'CHINA', 'JAPAN');
