{"name": "studio-tests", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "studio-tests", "version": "1.0.0", "hasInstallScript": true, "license": "ISC", "devDependencies": {"@playwright/test": "^1.49.0", "dotenv": "^16.4.7", "lodash": "^4.17.21"}}, "../../node_modules/.pnpm/@playwright+test@1.49.1/node_modules/@playwright/test": {"version": "1.49.1", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright": "1.49.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}}, "../../node_modules/.pnpm/dotenv@16.4.7/node_modules/dotenv": {"version": "16.4.7", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@types/node": "^18.11.3", "decache": "^4.6.2", "sinon": "^14.0.1", "standard": "^17.0.0", "standard-version": "^9.5.0", "tap": "^19.2.0", "typescript": "^4.8.4"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/@playwright/test": {"resolved": "../../node_modules/.pnpm/@playwright+test@1.49.1/node_modules/@playwright/test", "link": true}, "node_modules/dotenv": {"resolved": "../../node_modules/.pnpm/dotenv@16.4.7/node_modules/dotenv", "link": true}, "node_modules/lodash": {"resolved": "../../node_modules/.pnpm/lodash@4.17.21/node_modules/lodash", "link": true}}}