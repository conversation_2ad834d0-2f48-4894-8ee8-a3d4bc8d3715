{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "current-user-avatar-nextjs", "type": "registry:component", "title": "Current User Avatar", "description": "Component which renders the current user's avatar.", "dependencies": ["@supabase/ssr@latest", "@supabase/supabase-js@latest"], "registryDependencies": ["avatar"], "files": [{"path": "registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx", "content": "'use client'\n\nimport { useCurrentUserImage } from '@/registry/default/blocks/current-user-avatar/hooks/use-current-user-image'\nimport { useCurrentUserName } from '@/registry/default/blocks/current-user-avatar/hooks/use-current-user-name'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/registry/default/components/ui/avatar'\n\nexport const CurrentUserAvatar = () => {\n  const profileImage = useCurrentUserImage()\n  const name = useCurrentUserName()\n  const initials = name\n    ?.split(' ')\n    ?.map((word) => word[0])\n    ?.join('')\n    ?.toUpperCase()\n\n  return (\n    <Avatar>\n      {profileImage && <AvatarImage src={profileImage} alt={initials} />}\n      <AvatarFallback>{initials}</AvatarFallback>\n    </Avatar>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts", "content": "import { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { useEffect, useState } from 'react'\n\nexport const useCurrentUserName = () => {\n  const [name, setName] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchProfileName = async () => {\n      const { data, error } = await createClient().auth.getSession()\n      if (error) {\n        console.error(error)\n      }\n\n      setName(data.session?.user.user_metadata.full_name ?? '?')\n    }\n\n    fetchProfileName()\n  }, [])\n\n  return name || '?'\n}\n", "type": "registry:hook"}, {"path": "registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts", "content": "import { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { useEffect, useState } from 'react'\n\nexport const useCurrentUserImage = () => {\n  const [image, setImage] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchUserImage = async () => {\n      const { data, error } = await createClient().auth.getSession()\n      if (error) {\n        console.error(error)\n      }\n\n      setImage(data.session?.user.user_metadata.avatar_url ?? null)\n    }\n    fetchUserImage()\n  }, [])\n\n  return image\n}\n", "type": "registry:hook"}, {"path": "registry/default/clients/nextjs/lib/supabase/client.ts", "content": "import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n", "type": "registry:lib"}, {"path": "registry/default/clients/nextjs/lib/supabase/middleware.ts", "content": "import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Do not run code between createServerClient and\n  // supabase.auth.getUser(). A simple mistake could make it very hard to debug\n  // issues with users being randomly logged out.\n\n  // IMPORTANT: DO NOT REMOVE auth.getUser()\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  if (\n    !user &&\n    !request.nextUrl.pathname.startsWith('/login') &&\n    !request.nextUrl.pathname.startsWith('/auth')\n  ) {\n    // no user, potentially respond by redirecting the user to the login page\n    const url = request.nextUrl.clone()\n    url.pathname = '/auth/login'\n    return NextResponse.redirect(url)\n  }\n\n  // IMPORTANT: You *must* return the supabaseResponse object as it is.\n  // If you're creating a new response object with NextResponse.next() make sure to:\n  // 1. Pass the request in it, like so:\n  //    const myNewResponse = NextResponse.next({ request })\n  // 2. Copy over the cookies, like so:\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\n  //    the cookies!\n  // 4. Finally:\n  //    return myNewResponse\n  // If this is not done, you may be causing the browser and server to go out\n  // of sync and terminate the user's session prematurely!\n\n  return supabaseResponse\n}\n", "type": "registry:lib"}, {"path": "registry/default/clients/nextjs/lib/supabase/server.ts", "content": "import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n", "type": "registry:lib"}]}