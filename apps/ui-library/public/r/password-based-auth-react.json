{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "password-based-auth-react", "type": "registry:block", "title": "Password Based Auth flow for React and Supabase", "description": "Password Based Auth flow for React and Supabase", "dependencies": ["@supabase/supabase-js@latest"], "registryDependencies": ["button", "card", "input", "label"], "files": [{"path": "registry/default/blocks/password-based-auth-react/components/login-form.tsx", "content": "import { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/react/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport { useState } from 'react'\n\nexport function LoginForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const supabase = createClient()\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n      if (error) throw error\n      // Update this route to redirect to an authenticated route. The user already has an active session.\n      location.href = '/info'\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl\">Login</CardTitle>\n          <CardDescription>Enter your email below to login to your account</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleLogin}>\n            <div className=\"flex flex-col gap-6\">\n              <div className=\"grid gap-2\">\n                <Label htmlFor=\"email\">Email</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                />\n              </div>\n              <div className=\"grid gap-2\">\n                <div className=\"flex items-center\">\n                  <Label htmlFor=\"password\">Password</Label>\n                  <a\n                    href=\"/forgot-password\"\n                    className=\"ml-auto inline-block text-sm underline-offset-4 hover:underline\"\n                  >\n                    Forgot your password?\n                  </a>\n                </div>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n              </div>\n              {error && <p className=\"text-sm text-red-500\">{error}</p>}\n              <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                {isLoading ? 'Logging in...' : 'Login'}\n              </Button>\n            </div>\n            <div className=\"mt-4 text-center text-sm\">\n              Don&apos;t have an account?{' '}\n              <a href=\"/sign-up\" className=\"underline underline-offset-4\">\n                Sign up\n              </a>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-react/components/sign-up-form.tsx", "content": "import { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/react/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport { useState } from 'react'\n\nexport function SignUpForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [repeatPassword, setRepeatPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [success, setSuccess] = useState(false)\n\n  const handleSignUp = async (e: React.FormEvent) => {\n    const supabase = createClient()\n    e.preventDefault()\n    setError(null)\n\n    if (password !== repeatPassword) {\n      setError('Passwords do not match')\n      return\n    }\n    setIsLoading(true)\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n      })\n      if (error) throw error\n      setSuccess(true)\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      {success ? (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Thank you for signing up!</CardTitle>\n            <CardDescription>Check your email to confirm</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-muted-foreground\">\n              You've successfully signed up. Please check your email to confirm your account before\n              signing in.\n            </p>\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Sign up</CardTitle>\n            <CardDescription>Create a new account</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSignUp}>\n              <div className=\"flex flex-col gap-6\">\n                <div className=\"grid gap-2\">\n                  <Label htmlFor=\"email\">Email</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                  />\n                </div>\n                <div className=\"grid gap-2\">\n                  <div className=\"flex items-center\">\n                    <Label htmlFor=\"password\">Password</Label>\n                  </div>\n                  <Input\n                    id=\"password\"\n                    type=\"password\"\n                    required\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                  />\n                </div>\n                <div className=\"grid gap-2\">\n                  <div className=\"flex items-center\">\n                    <Label htmlFor=\"repeat-password\">Repeat Password</Label>\n                  </div>\n                  <Input\n                    id=\"repeat-password\"\n                    type=\"password\"\n                    required\n                    value={repeatPassword}\n                    onChange={(e) => setRepeatPassword(e.target.value)}\n                  />\n                </div>\n                {error && <p className=\"text-sm text-red-500\">{error}</p>}\n                <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                  {isLoading ? 'Creating an account...' : 'Sign up'}\n                </Button>\n              </div>\n              <div className=\"mt-4 text-center text-sm\">\n                Already have an account?{' '}\n                <a href=\"/login\" className=\"underline underline-offset-4\">\n                  Login\n                </a>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-react/components/forgot-password-form.tsx", "content": "import { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/react/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport { useState } from 'react'\n\nexport function ForgotPasswordForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [success, setSuccess] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleForgotPassword = async (e: React.FormEvent) => {\n    const supabase = createClient()\n    e.preventDefault()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      // The url which will be included in the email. This URL needs to be configured in your redirect URLs in the Supabase dashboard at https://supabase.com/dashboard/project/_/auth/url-configuration\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: 'http://localhost:3000/update-password',\n      })\n      if (error) throw error\n      setSuccess(true)\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      {success ? (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Check Your Email</CardTitle>\n            <CardDescription>Password reset instructions sent</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-muted-foreground\">\n              If you registered using your email and password, you will receive a password reset\n              email.\n            </p>\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Reset Your Password</CardTitle>\n            <CardDescription>\n              Type in your email and we&apos;ll send you a link to reset your password\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleForgotPassword}>\n              <div className=\"flex flex-col gap-6\">\n                <div className=\"grid gap-2\">\n                  <Label htmlFor=\"email\">Email</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                  />\n                </div>\n                {error && <p className=\"text-sm text-red-500\">{error}</p>}\n                <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                  {isLoading ? 'Sending...' : 'Send reset email'}\n                </Button>\n              </div>\n              <div className=\"mt-4 text-center text-sm\">\n                Already have an account?{' '}\n                <a href=\"/login\" className=\"underline underline-offset-4\">\n                  Login\n                </a>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-react/components/update-password-form.tsx", "content": "import { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/react/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport { useState } from 'react'\n\nexport function UpdatePasswordForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleForgotPassword = async (e: React.FormEvent) => {\n    const supabase = createClient()\n    e.preventDefault()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.updateUser({ password })\n      if (error) throw error\n      // Update this route to redirect to an authenticated route. The user already has an active session.\n      location.href = '/info'\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl\">Reset Your Password</CardTitle>\n          <CardDescription>Please enter your new password below.</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleForgotPassword}>\n            <div className=\"flex flex-col gap-6\">\n              <div className=\"grid gap-2\">\n                <Label htmlFor=\"password\">New password</Label>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  placeholder=\"New password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n              </div>\n              {error && <p className=\"text-sm text-red-500\">{error}</p>}\n              <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                {isLoading ? 'Saving...' : 'Save new password'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/clients/react/lib/supabase/client.ts", "content": "import { createClient as createSupabaseClient } from '@supabase/supabase-js'\n\nexport function createClient() {\n  return createSupabaseClient(\n    import.meta.env.VITE_SUPABASE_URL!,\n    import.meta.env.VITE_SUPABASE_ANON_KEY!\n  )\n}\n", "type": "registry:lib"}]}