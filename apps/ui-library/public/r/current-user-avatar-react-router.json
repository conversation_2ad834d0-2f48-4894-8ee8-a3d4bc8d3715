{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "current-user-avatar-react-router", "type": "registry:component", "title": "Current User Avatar", "description": "Component which renders the current user's avatar.", "dependencies": ["@supabase/ssr@latest", "@supabase/supabase-js@latest"], "registryDependencies": ["avatar"], "files": [{"path": "registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx", "content": "'use client'\n\nimport { useCurrentUserImage } from '@/registry/default/blocks/current-user-avatar/hooks/use-current-user-image'\nimport { useCurrentUserName } from '@/registry/default/blocks/current-user-avatar/hooks/use-current-user-name'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/registry/default/components/ui/avatar'\n\nexport const CurrentUserAvatar = () => {\n  const profileImage = useCurrentUserImage()\n  const name = useCurrentUserName()\n  const initials = name\n    ?.split(' ')\n    ?.map((word) => word[0])\n    ?.join('')\n    ?.toUpperCase()\n\n  return (\n    <Avatar>\n      {profileImage && <AvatarImage src={profileImage} alt={initials} />}\n      <AvatarFallback>{initials}</AvatarFallback>\n    </Avatar>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts", "content": "import { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { useEffect, useState } from 'react'\n\nexport const useCurrentUserName = () => {\n  const [name, setName] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchProfileName = async () => {\n      const { data, error } = await createClient().auth.getSession()\n      if (error) {\n        console.error(error)\n      }\n\n      setName(data.session?.user.user_metadata.full_name ?? '?')\n    }\n\n    fetchProfileName()\n  }, [])\n\n  return name || '?'\n}\n", "type": "registry:hook"}, {"path": "registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts", "content": "import { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { useEffect, useState } from 'react'\n\nexport const useCurrentUserImage = () => {\n  const [image, setImage] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchUserImage = async () => {\n      const { data, error } = await createClient().auth.getSession()\n      if (error) {\n        console.error(error)\n      }\n\n      setImage(data.session?.user.user_metadata.avatar_url ?? null)\n    }\n    fetchUserImage()\n  }, [])\n\n  return image\n}\n", "type": "registry:hook"}, {"path": "registry/default/clients/react-router/lib/supabase/client.ts", "content": "/// <reference types=\"vite/types/importMeta.d.ts\" />\nimport { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    import.meta.env.VITE_SUPABASE_URL!,\n    import.meta.env.VITE_SUPABASE_ANON_KEY!\n  )\n}\n", "type": "registry:lib"}, {"path": "registry/default/clients/react-router/lib/supabase/server.ts", "content": "import { createServer<PERSON><PERSON>, parseCookie<PERSON>eader, serialize<PERSON>ook<PERSON><PERSON>eader } from '@supabase/ssr'\n\nexport function createClient(request: Request) {\n  const headers = new Headers()\n\n  const supabase = createServerClient(\n    process.env.VITE_SUPABASE_URL!,\n    process.env.VITE_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return parseCookieHeader(request.headers.get('Cookie') ?? '') as {\n            name: string\n            value: string\n          }[]\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            headers.append('Set-Cookie', serializeCookieHeader(name, value, options))\n          )\n        },\n      },\n    }\n  )\n\n  return { supabase, headers }\n}\n", "type": "registry:lib"}]}