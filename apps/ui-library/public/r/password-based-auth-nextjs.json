{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "password-based-auth-nextjs", "type": "registry:block", "title": "Password Based Auth flow for Nextjs and Supabase", "description": "Password Based Auth flow for Nextjs and Supabase", "dependencies": ["@supabase/ssr@latest", "@supabase/supabase-js@latest"], "registryDependencies": ["button", "card", "input", "label"], "files": [{"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/login/page.tsx", "content": "import { LoginForm } from '@/registry/default/blocks/password-based-auth-nextjs/components/login-form'\n\nexport default function Page() {\n  return (\n    <div className=\"flex min-h-svh w-full items-center justify-center p-6 md:p-10\">\n      <div className=\"w-full max-w-sm\">\n        <LoginForm />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/auth/login/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/error/page.tsx", "content": "import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/registry/default/components/ui/card'\n\nexport default async function Page({ searchParams }: { searchParams: Promise<{ error: string }> }) {\n  const params = await searchParams\n\n  return (\n    <div className=\"flex min-h-svh w-full items-center justify-center p-6 md:p-10\">\n      <div className=\"w-full max-w-sm\">\n        <div className=\"flex flex-col gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-2xl\">Sorry, something went wrong.</CardTitle>\n            </CardHeader>\n            <CardContent>\n              {params?.error ? (\n                <p className=\"text-sm text-muted-foreground\">Code error: {params.error}</p>\n              ) : (\n                <p className=\"text-sm text-muted-foreground\">An unspecified error occurred.</p>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/auth/error/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/protected/page.tsx", "content": "import { redirect } from 'next/navigation'\n\nimport { LogoutButton } from '@/registry/default/blocks/password-based-auth-nextjs/components/logout-button'\nimport { createClient } from '@/registry/default/clients/nextjs/lib/supabase/server'\n\nexport default async function ProtectedPage() {\n  const supabase = await createClient()\n\n  const { data, error } = await supabase.auth.getUser()\n  if (error || !data?.user) {\n    redirect('/login')\n  }\n\n  return (\n    <div className=\"flex h-svh w-full items-center justify-center gap-2\">\n      <p>\n        Hello <span>{data.user.email}</span>\n      </p>\n      <LogoutButton />\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/protected/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/confirm/route.ts", "content": "import { createClient } from '@/registry/default/clients/nextjs/lib/supabase/server'\nimport { type EmailOtpType } from '@supabase/supabase-js'\nimport { redirect } from 'next/navigation'\nimport { type NextRequest } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url)\n  const token_hash = searchParams.get('token_hash')\n  const type = searchParams.get('type') as EmailOtpType | null\n  const next = searchParams.get('next') ?? '/'\n\n  if (token_hash && type) {\n    const supabase = await createClient()\n\n    const { error } = await supabase.auth.verifyOtp({\n      type,\n      token_hash,\n    })\n    if (!error) {\n      // redirect user to specified redirect URL or root of app\n      redirect(next)\n    } else {\n      // redirect the user to an error page with some instructions\n      redirect(`/auth/error?error=${error?.message}`)\n    }\n  }\n\n  // redirect the user to an error page with some instructions\n  redirect(`/auth/error?error=No token hash or type`)\n}\n", "type": "registry:page", "target": "app/auth/confirm/route.ts"}, {"path": "registry/default/blocks/password-based-auth-nextjs/components/login-form.tsx", "content": "'use client'\n\nimport { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState } from 'react'\n\nexport function LoginForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const router = useRouter()\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    const supabase = createClient()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n      if (error) throw error\n      // Update this route to redirect to an authenticated route. The user already has an active session.\n      router.push('/protected')\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl\">Login</CardTitle>\n          <CardDescription>Enter your email below to login to your account</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleLogin}>\n            <div className=\"flex flex-col gap-6\">\n              <div className=\"grid gap-2\">\n                <Label htmlFor=\"email\">Email</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                />\n              </div>\n              <div className=\"grid gap-2\">\n                <div className=\"flex items-center\">\n                  <Label htmlFor=\"password\">Password</Label>\n                  <Link\n                    href=\"/auth/forgot-password\"\n                    className=\"ml-auto inline-block text-sm underline-offset-4 hover:underline\"\n                  >\n                    Forgot your password?\n                  </Link>\n                </div>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n              </div>\n              {error && <p className=\"text-sm text-red-500\">{error}</p>}\n              <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                {isLoading ? 'Logging in...' : 'Login'}\n              </Button>\n            </div>\n            <div className=\"mt-4 text-center text-sm\">\n              Don&apos;t have an account?{' '}\n              <Link href=\"/auth/sign-up\" className=\"underline underline-offset-4\">\n                Sign up\n              </Link>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-nextjs/middleware.ts", "content": "import { updateSession } from '@/registry/default/clients/nextjs/lib/supabase/middleware'\nimport { type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  return await updateSession(request)\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n", "type": "registry:file", "target": "middleware.ts"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/sign-up/page.tsx", "content": "import { SignUpForm } from '@/registry/default/blocks/password-based-auth-nextjs/components/sign-up-form'\n\nexport default function Page() {\n  return (\n    <div className=\"flex min-h-svh w-full items-center justify-center p-6 md:p-10\">\n      <div className=\"w-full max-w-sm\">\n        <SignUpForm />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/auth/sign-up/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/sign-up-success/page.tsx", "content": "import {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\n\nexport default function Page() {\n  return (\n    <div className=\"flex min-h-svh w-full items-center justify-center p-6 md:p-10\">\n      <div className=\"w-full max-w-sm\">\n        <div className=\"flex flex-col gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-2xl\">Thank you for signing up!</CardTitle>\n              <CardDescription>Check your email to confirm</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <p className=\"text-sm text-muted-foreground\">\n                You&apos;ve successfully signed up. Please check your email to confirm your account\n                before signing in.\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/auth/sign-up-success/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/components/sign-up-form.tsx", "content": "'use client'\n\nimport { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState } from 'react'\n\nexport function SignUpForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [repeatPassword, setRepeatPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const router = useRouter()\n\n  const handleSignUp = async (e: React.FormEvent) => {\n    e.preventDefault()\n    const supabase = createClient()\n    setIsLoading(true)\n    setError(null)\n\n    if (password !== repeatPassword) {\n      setError('Passwords do not match')\n      setIsLoading(false)\n      return\n    }\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          emailRedirectTo: `${window.location.origin}/protected`,\n        },\n      })\n      if (error) throw error\n      router.push('/auth/sign-up-success')\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl\">Sign up</CardTitle>\n          <CardDescription>Create a new account</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSignUp}>\n            <div className=\"flex flex-col gap-6\">\n              <div className=\"grid gap-2\">\n                <Label htmlFor=\"email\">Email</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                />\n              </div>\n              <div className=\"grid gap-2\">\n                <div className=\"flex items-center\">\n                  <Label htmlFor=\"password\">Password</Label>\n                </div>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n              </div>\n              <div className=\"grid gap-2\">\n                <div className=\"flex items-center\">\n                  <Label htmlFor=\"repeat-password\">Repeat Password</Label>\n                </div>\n                <Input\n                  id=\"repeat-password\"\n                  type=\"password\"\n                  required\n                  value={repeatPassword}\n                  onChange={(e) => setRepeatPassword(e.target.value)}\n                />\n              </div>\n              {error && <p className=\"text-sm text-red-500\">{error}</p>}\n              <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                {isLoading ? 'Creating an account...' : 'Sign up'}\n              </Button>\n            </div>\n            <div className=\"mt-4 text-center text-sm\">\n              Already have an account?{' '}\n              <Link href=\"/auth/login\" className=\"underline underline-offset-4\">\n                Login\n              </Link>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/forgot-password/page.tsx", "content": "import { ForgotPasswordForm } from '@/registry/default/blocks/password-based-auth-nextjs/components/forgot-password-form'\n\nexport default function Page() {\n  return (\n    <div className=\"flex min-h-svh w-full items-center justify-center p-6 md:p-10\">\n      <div className=\"w-full max-w-sm\">\n        <ForgotPasswordForm />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/auth/forgot-password/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/app/auth/update-password/page.tsx", "content": "import { UpdatePasswordForm } from '@/registry/default/blocks/password-based-auth-nextjs/components/update-password-form'\n\nexport default function Page() {\n  return (\n    <div className=\"flex min-h-svh w-full items-center justify-center p-6 md:p-10\">\n      <div className=\"w-full max-w-sm\">\n        <UpdatePasswordForm />\n      </div>\n    </div>\n  )\n}\n", "type": "registry:page", "target": "app/auth/update-password/page.tsx"}, {"path": "registry/default/blocks/password-based-auth-nextjs/components/forgot-password-form.tsx", "content": "'use client'\n\nimport { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport Link from 'next/link'\nimport { useState } from 'react'\n\nexport function ForgotPasswordForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [success, setSuccess] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleForgotPassword = async (e: React.FormEvent) => {\n    e.preventDefault()\n    const supabase = createClient()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      // The url which will be included in the email. This URL needs to be configured in your redirect URLs in the Supabase dashboard at https://supabase.com/dashboard/project/_/auth/url-configuration\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/update-password`,\n      })\n      if (error) throw error\n      setSuccess(true)\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      {success ? (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Check Your Email</CardTitle>\n            <CardDescription>Password reset instructions sent</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-muted-foreground\">\n              If you registered using your email and password, you will receive a password reset\n              email.\n            </p>\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Reset Your Password</CardTitle>\n            <CardDescription>\n              Type in your email and we&apos;ll send you a link to reset your password\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleForgotPassword}>\n              <div className=\"flex flex-col gap-6\">\n                <div className=\"grid gap-2\">\n                  <Label htmlFor=\"email\">Email</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                  />\n                </div>\n                {error && <p className=\"text-sm text-red-500\">{error}</p>}\n                <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                  {isLoading ? 'Sending...' : 'Send reset email'}\n                </Button>\n              </div>\n              <div className=\"mt-4 text-center text-sm\">\n                Already have an account?{' '}\n                <Link href=\"/auth/login\" className=\"underline underline-offset-4\">\n                  Login\n                </Link>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-nextjs/components/update-password-form.tsx", "content": "'use client'\n\nimport { cn } from '@/lib/utils'\nimport { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/registry/default/components/ui/card'\nimport { Input } from '@/registry/default/components/ui/input'\nimport { Label } from '@/registry/default/components/ui/label'\nimport { useRouter } from 'next/navigation'\nimport { useState } from 'react'\n\nexport function UpdatePasswordForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const router = useRouter()\n\n  const handleForgotPassword = async (e: React.FormEvent) => {\n    e.preventDefault()\n    const supabase = createClient()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.updateUser({ password })\n      if (error) throw error\n      // Update this route to redirect to an authenticated route. The user already has an active session.\n      router.push('/protected')\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl\">Reset Your Password</CardTitle>\n          <CardDescription>Please enter your new password below.</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleForgotPassword}>\n            <div className=\"flex flex-col gap-6\">\n              <div className=\"grid gap-2\">\n                <Label htmlFor=\"password\">New password</Label>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  placeholder=\"New password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n              </div>\n              {error && <p className=\"text-sm text-red-500\">{error}</p>}\n              <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                {isLoading ? 'Saving...' : 'Save new password'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n", "type": "registry:component"}, {"path": "registry/default/blocks/password-based-auth-nextjs/components/logout-button.tsx", "content": "'use client'\n\nimport { createClient } from '@/registry/default/clients/nextjs/lib/supabase/client'\nimport { Button } from '@/registry/default/components/ui/button'\nimport { useRouter } from 'next/navigation'\n\nexport function LogoutButton() {\n  const router = useRouter()\n\n  const logout = async () => {\n    const supabase = createClient()\n    await supabase.auth.signOut()\n    router.push('/auth/login')\n  }\n\n  return <Button onClick={logout}>Logout</Button>\n}\n", "type": "registry:component"}, {"path": "registry/default/clients/nextjs/lib/supabase/client.ts", "content": "import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n", "type": "registry:lib"}, {"path": "registry/default/clients/nextjs/lib/supabase/middleware.ts", "content": "import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Do not run code between createServerClient and\n  // supabase.auth.getUser(). A simple mistake could make it very hard to debug\n  // issues with users being randomly logged out.\n\n  // IMPORTANT: DO NOT REMOVE auth.getUser()\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  if (\n    !user &&\n    !request.nextUrl.pathname.startsWith('/login') &&\n    !request.nextUrl.pathname.startsWith('/auth')\n  ) {\n    // no user, potentially respond by redirecting the user to the login page\n    const url = request.nextUrl.clone()\n    url.pathname = '/auth/login'\n    return NextResponse.redirect(url)\n  }\n\n  // IMPORTANT: You *must* return the supabaseResponse object as it is.\n  // If you're creating a new response object with NextResponse.next() make sure to:\n  // 1. Pass the request in it, like so:\n  //    const myNewResponse = NextResponse.next({ request })\n  // 2. Copy over the cookies, like so:\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\n  //    the cookies!\n  // 4. Finally:\n  //    return myNewResponse\n  // If this is not done, you may be causing the browser and server to go out\n  // of sync and terminate the user's session prematurely!\n\n  return supabaseResponse\n}\n", "type": "registry:lib"}, {"path": "registry/default/clients/nextjs/lib/supabase/server.ts", "content": "import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n", "type": "registry:lib"}]}