{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "supabase-client-react", "type": "registry:lib", "title": "Supabase Client for React", "description": "", "dependencies": ["@supabase/supabase-js@latest"], "registryDependencies": [], "files": [{"path": "registry/default/clients/react/lib/supabase/client.ts", "content": "import { createClient as createSupabaseClient } from '@supabase/supabase-js'\n\nexport function createClient() {\n  return createSupabaseClient(\n    import.meta.env.VITE_SUPABASE_URL!,\n    import.meta.env.VITE_SUPABASE_ANON_KEY!\n  )\n}\n", "type": "registry:lib"}], "docs": "You'll need to add a `.env` file with the following environment variables to your project: `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`."}