// typecheck

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { registry } from '../registry/index'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const registryPath = path.join(__dirname, '..', 'registry.json')

const cleanedRegistry = {
  $schema: 'https://ui.shadcn.com/schema/registry.json',
  ...registry,
  items: registry.items.filter((item) => item.type !== 'registry:example'),
}

fs.writeFileSync(registryPath, JSON.stringify(cleanedRegistry, null, 2))

// Create a registry index file for use by ComponentPreview in the app.
const registryIndex = `
// @ts-nocheck
// This file is autogenerated by scripts/build-registry.ts
// Do not edit this file directly.
import * as React from "react"

export const Index: Record<string, any> = {
  "default": {
    ${registry.items.map((item) => {
      const componentFile = item.files.find((file) => file.path.endsWith('.tsx'))

      return `
    "${item.name}": {
      name: "${item.name}",
      type: "${item.type}",
      registryDependencies: [${item.registryDependencies.map((dep) => `"${dep}"`).join(',')}],
      ${componentFile ? `component: React.lazy(() => import("@/${componentFile.path}")),` : ''}
      source: "",
      files: [${item.files?.map((file) => `"${file.path}"`).join(',')}],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    }
    `
    })}
  },
}
`
fs.writeFileSync(`__registry__/index.tsx`, registryIndex)
