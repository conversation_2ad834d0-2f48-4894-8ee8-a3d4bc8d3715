{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "supabase-client-tanstack", "type": "registry:lib", "title": "Supabase Client for TanStack Start", "description": "", "registryDependencies": [], "dependencies": ["@supabase/ssr@latest", "@supabase/supabase-js@latest"], "docs": "You'll need to add a `.env` file with the following environment variables to your project: `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`.", "files": [{"path": "registry/default/clients/tanstack/lib/supabase/client.ts", "type": "registry:lib"}, {"path": "registry/default/clients/tanstack/lib/supabase/server.ts", "type": "registry:lib"}]}