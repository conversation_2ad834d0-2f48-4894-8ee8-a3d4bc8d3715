{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "supabase-client-react", "type": "registry:lib", "title": "Supabase Client for React", "description": "", "registryDependencies": [], "dependencies": ["@supabase/supabase-js@latest"], "docs": "You'll need to add a `.env` file with the following environment variables to your project: `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`.", "files": [{"path": "registry/default/clients/react/lib/supabase/client.ts", "type": "registry:lib"}]}