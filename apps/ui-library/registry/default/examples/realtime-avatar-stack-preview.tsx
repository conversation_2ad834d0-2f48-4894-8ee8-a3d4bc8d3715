// 'use client'

import { AvatarStack } from '@/registry/default/blocks/realtime-avatar-stack/components/avatar-stack'
import { users } from './utils'

const avatars = users.filter(
  (u) =>
    u.name === '<PERSON>' || u.name === '<PERSON>y R.' || u.name === '<PERSON>' || u.name === '<PERSON>'
)

const CurrentUserAvatarPreview = () => {
  return <AvatarStack avatars={avatars} />
}

export default CurrentUserAvatarPreview
