// SUPABASE NOTE: THIS FILE WAS ADDED TO SATISFY TANSTACK BUILD CONFIGURATION. IT IS NOT INCLUDED IN THE BLOCK.

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as ProtectedImport } from './routes/_protected'
import { Route as ProtectedInfoImport } from './routes/_protected/info'
import { Route as AuthConfirmImport } from './routes/auth/confirm'
import { Route as AuthErrorImport } from './routes/auth/error'
import { Route as ForgotPasswordImport } from './routes/forgot-password'
import { Route as IndexImport } from './routes/index'
import { Route as LoginImport } from './routes/login'
import { Route as SignUpImport } from './routes/sign-up'
import { Route as SignUpSuccessImport } from './routes/sign-up-success'
import { Route as UpdatePasswordImport } from './routes/update-password'

// Create/Update Routes

const UpdatePasswordRoute = UpdatePasswordImport.update({
  id: '/update-password',
  path: '/update-password',
  getParentRoute: () => rootRoute,
} as any)

const SignUpSuccessRoute = SignUpSuccessImport.update({
  id: '/sign-up-success',
  path: '/sign-up-success',
  getParentRoute: () => rootRoute,
} as any)

const SignUpRoute = SignUpImport.update({
  id: '/sign-up',
  path: '/sign-up',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const ForgotPasswordRoute = ForgotPasswordImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const ProtectedRoute = ProtectedImport.update({
  id: '/_protected',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AuthErrorRoute = AuthErrorImport.update({
  id: '/auth/error',
  path: '/auth/error',
  getParentRoute: () => rootRoute,
} as any)

const AuthConfirmRoute = AuthConfirmImport.update({
  id: '/auth/confirm',
  path: '/auth/confirm',
  getParentRoute: () => rootRoute,
} as any)

const ProtectedInfoRoute = ProtectedInfoImport.update({
  id: '/info',
  path: '/info',
  getParentRoute: () => ProtectedRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_protected': {
      id: '/_protected'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof ProtectedImport
      parentRoute: typeof rootRoute
    }
    '/forgot-password': {
      id: '/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof ForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/sign-up': {
      id: '/sign-up'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof SignUpImport
      parentRoute: typeof rootRoute
    }
    '/sign-up-success': {
      id: '/sign-up-success'
      path: '/sign-up-success'
      fullPath: '/sign-up-success'
      preLoaderRoute: typeof SignUpSuccessImport
      parentRoute: typeof rootRoute
    }
    '/update-password': {
      id: '/update-password'
      path: '/update-password'
      fullPath: '/update-password'
      preLoaderRoute: typeof UpdatePasswordImport
      parentRoute: typeof rootRoute
    }
    '/_protected/info': {
      id: '/_protected/info'
      path: '/info'
      fullPath: '/info'
      preLoaderRoute: typeof ProtectedInfoImport
      parentRoute: typeof ProtectedImport
    }
    '/auth/confirm': {
      id: '/auth/confirm'
      path: '/auth/confirm'
      fullPath: '/auth/confirm'
      preLoaderRoute: typeof AuthConfirmImport
      parentRoute: typeof rootRoute
    }
    '/auth/error': {
      id: '/auth/error'
      path: '/auth/error'
      fullPath: '/auth/error'
      preLoaderRoute: typeof AuthErrorImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

interface ProtectedRouteChildren {
  ProtectedInfoRoute: typeof ProtectedInfoRoute
}

const ProtectedRouteChildren: ProtectedRouteChildren = {
  ProtectedInfoRoute: ProtectedInfoRoute,
}

const ProtectedRouteWithChildren = ProtectedRoute._addFileChildren(ProtectedRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof ProtectedRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/login': typeof LoginRoute
  '/sign-up': typeof SignUpRoute
  '/sign-up-success': typeof SignUpSuccessRoute
  '/update-password': typeof UpdatePasswordRoute
  '/info': typeof ProtectedInfoRoute
  '/auth/confirm': typeof AuthConfirmRoute
  '/auth/error': typeof AuthErrorRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof ProtectedRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/login': typeof LoginRoute
  '/sign-up': typeof SignUpRoute
  '/sign-up-success': typeof SignUpSuccessRoute
  '/update-password': typeof UpdatePasswordRoute
  '/info': typeof ProtectedInfoRoute
  '/auth/confirm': typeof AuthConfirmRoute
  '/auth/error': typeof AuthErrorRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_protected': typeof ProtectedRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/login': typeof LoginRoute
  '/sign-up': typeof SignUpRoute
  '/sign-up-success': typeof SignUpSuccessRoute
  '/update-password': typeof UpdatePasswordRoute
  '/_protected/info': typeof ProtectedInfoRoute
  '/auth/confirm': typeof AuthConfirmRoute
  '/auth/error': typeof AuthErrorRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/forgot-password'
    | '/login'
    | '/sign-up'
    | '/sign-up-success'
    | '/update-password'
    | '/info'
    | '/auth/confirm'
    | '/auth/error'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/forgot-password'
    | '/login'
    | '/sign-up'
    | '/sign-up-success'
    | '/update-password'
    | '/info'
    | '/auth/confirm'
    | '/auth/error'
  id:
    | '__root__'
    | '/'
    | '/_protected'
    | '/forgot-password'
    | '/login'
    | '/sign-up'
    | '/sign-up-success'
    | '/update-password'
    | '/_protected/info'
    | '/auth/confirm'
    | '/auth/error'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ProtectedRoute: typeof ProtectedRouteWithChildren
  ForgotPasswordRoute: typeof ForgotPasswordRoute
  LoginRoute: typeof LoginRoute
  SignUpRoute: typeof SignUpRoute
  SignUpSuccessRoute: typeof SignUpSuccessRoute
  UpdatePasswordRoute: typeof UpdatePasswordRoute
  AuthConfirmRoute: typeof AuthConfirmRoute
  AuthErrorRoute: typeof AuthErrorRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ProtectedRoute: ProtectedRouteWithChildren,
  ForgotPasswordRoute: ForgotPasswordRoute,
  LoginRoute: LoginRoute,
  SignUpRoute: SignUpRoute,
  SignUpSuccessRoute: SignUpSuccessRoute,
  UpdatePasswordRoute: UpdatePasswordRoute,
  AuthConfirmRoute: AuthConfirmRoute,
  AuthErrorRoute: AuthErrorRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_protected",
        "/forgot-password",
        "/login",
        "/sign-up",
        "/sign-up-success",
        "/update-password",
        "/auth/confirm",
        "/auth/error"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_protected": {
      "filePath": "_protected.tsx",
      "children": [
        "/_protected/info"
      ]
    },
    "/forgot-password": {
      "filePath": "forgot-password.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/sign-up": {
      "filePath": "sign-up.tsx"
    },
    "/sign-up-success": {
      "filePath": "sign-up-success.tsx"
    },
    "/update-password": {
      "filePath": "update-password.tsx"
    },
    "/_protected/info": {
      "filePath": "_protected/info.tsx",
      "parent": "/_protected"
    },
    "/auth/confirm": {
      "filePath": "auth/confirm.ts"
    },
    "/auth/error": {
      "filePath": "auth/error.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
