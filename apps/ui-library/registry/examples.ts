import { type Registry } from 'shadcn/registry'

export const examples: Registry['items'] = [
  {
    name: 'dropzone-demo',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/dropzone-demo.tsx',
        type: 'registry:example',
      },
    ],
  },
  {
    name: 'realtime-cursor-demo',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/realtime-cursor-demo.tsx',
        type: 'registry:example',
      },
    ],
  },
  {
    name: 'password-based-auth-demo',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/password-based-auth.tsx',
        type: 'registry:example',
      },
    ],
  },
  {
    name: 'current-user-avatar-demo',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/current-user-avatar-demo.tsx',
        type: 'registry:example',
      },
    ],
  },
  {
    name: 'current-user-avatar-preview',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/current-user-avatar-preview.tsx',
        type: 'registry:example',
      },
    ],
  },
  {
    name: 'realtime-avatar-stack-demo',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/realtime-avatar-stack-demo.tsx',
        type: 'registry:example',
      },
    ],
  },
  {
    name: 'realtime-avatar-stack-preview',
    type: 'registry:example',
    registryDependencies: [],
    files: [
      {
        path: 'registry/default/examples/realtime-avatar-stack-preview.tsx',
        type: 'registry:example',
      },
    ],
  },
]
