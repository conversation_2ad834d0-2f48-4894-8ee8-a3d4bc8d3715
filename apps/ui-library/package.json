{"name": "ui-library", "version": "0.1.0", "private": true, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --port 3004", "build": "pnpm run content:build && pnpm run build:registry && pnpm run build:llms && next build", "build:registry": "tsx --tsconfig ./tsconfig.scripts.json ./scripts/build-registry.mts && prettier --write registry.json && rimraf -G public/r && shadcn build && tsx scripts/clean-registry.ts", "build:llms": "tsx --tsconfig ./tsconfig.scripts.json ./scripts/build-llms-txt.ts", "start": "next start", "lint": "next lint", "lint:mdx": "supa-mdx-lint content --config ../../supa-mdx-lint.config.toml", "content:build": "contentlayer2 build", "clean": "rimraf node_modules .next .turbo", "typecheck": "contentlayer2 build && tsc --noEmit -p tsconfig.json"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.0.7", "@react-router/fs-routes": "^7.4.0", "@supabase/supa-mdx-lint": "0.2.6-alpha", "class-variance-authority": "^0.6.0", "common": "workspace:*", "contentlayer2": "0.4.6", "eslint-config-supabase": "workspace:*", "framer-motion": "^11.0.3", "icons": "workspace:*", "jotai": "^2.8.0", "lucide-react": "*", "next": "catalog:", "next-contentlayer2": "0.4.6", "next-themes": "^0.3.0", "react": "^18.2.0", "react-docgen": "^7.0.3", "react-dom": "^18.2.0", "react-inlinesvg": "^4.0.4", "react-wrap-balancer": "^1.1.0", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.9.0", "rehype-slug": "^6.0.0", "remark": "^14.0.3", "remark-code-import": "^1.2.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "ui": "workspace:*", "ui-patterns": "workspace:*", "unist-util-visit": "^5.0.0", "zod": "^3.22.4"}, "devDependencies": {"@react-router/dev": "^7.1.5", "@shikijs/compat": "^1.1.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-router": "^1.114.27", "@tanstack/react-start": "^1.114.25", "@types/lodash": "^4.17.16", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.0.1", "config": "workspace:^", "lodash": "^4.17.21", "mdast-util-toc": "^6.1.1", "postcss": "^8", "react-dropzone": "^14.3.8", "react-router": "^7.4.0", "rimraf": "^4.1.3", "shadcn": "2.4.0-canary.16", "shiki": "^1.1.7", "tailwindcss": "^3.3.0", "tsconfig": "workspace:*", "tsx": "^4.19.3", "typescript": "~5.5.0", "unist-builder": "3.0.0", "vite": "^6.2.4"}}