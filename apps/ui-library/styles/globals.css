@tailwind base;
@tailwind components;
@tailwind utilities;

@import './../../../packages/ui/build/css/source/global.css';
@import './../../../packages/ui/build/css/themes/dark.css';
@import './../../../packages/ui/build/css/themes/classic-dark.css';
@import './../../../packages/ui/build/css/themes/light.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 84.2% 60.2%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 13% 70.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.625rem;
    --sidebar: 210 20% 98.5%;
    --sidebar-foreground: 224 71.4% 4.1%;
    --sidebar-primary: 220.9 39.3% 11%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 220 14.3% 95.9%;
    --sidebar-accent-foreground: 220.9 39.3% 11%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 220 13% 70.8%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 84.2% 60.2%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 217.9 10.6% 55.6%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar: 220.9 39.3% 11%;
    --sidebar-foreground: 210 20% 98%;
    --sidebar-primary: 220 70% 50%;
    --sidebar-primary-foreground: 210 20% 98%;
    --sidebar-accent: 215 27.9% 16.9%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 215 27.9% 16.9%;
    --sidebar-ring: 220 13% 43.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg text-foreground;
    /* font-feature-settings: "rlig" 1, "calt" 1; */
    font-synthesis-weight: none;
    text-rendering: optimizeLegibility;
  }
}

@layer utilities {
  .step {
    counter-increment: step;
  }

  .step:before {
    @apply absolute w-9 h-9 bg-muted rounded-full font-mono font-medium text-center text-base inline-flex items-center justify-center -indent-px border-4 border-background;
    @apply ml-[-50px] mt-[-4px];
    content: counter(step);
  }

  .chunk-container {
    @apply shadow-none;
  }

  .chunk-container::after {
    content: '';
    @apply absolute -inset-4 shadow-xl rounded-xl border;
  }
}

@media (max-width: 640px) {
  .container {
    @apply px-4;
  }
}

.preview {
  --background: 0 0% 100%;
  --foreground: 0 0% 14.5%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 14.5%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 14.5%;
  --primary: 0 0% 20.5%;
  --primary-foreground: 0 0% 98.5%;
  --secondary: 0 0% 97%;
  --secondary-foreground: 0 0% 20.5%;
  --muted: 0 0% 97%;
  --muted-foreground: 0 0% 55.6%;
  --accent: 0 0% 97%;
  --accent-foreground: 0 0% 20.5%;
  --destructive: 27.3 24.5% 57.7%;
  --destructive-foreground: 27.3 24.5% 57.7%;
  --border: 0 0% 92.2%;
  --input: 0 0% 92.2%;
  --ring: 0 0% 70.8%;
  --chart-1: 41.1 22.2% 64.6%;
  --chart-2: 184.7 11.8% 60%;
  --chart-3: 227.4 7% 39.8%;
  --chart-4: 84.4 18.9% 82.8%;
  --chart-5: 70.1 18.8% 76.9%;
  --radius: 0.625rem;
  --sidebar: 0 0% 98.5%;
  --sidebar-foreground: 0 0% 14.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 97%;
  --sidebar-accent-foreground: 0 0% 20.5%;
  --sidebar-border: 0 0% 92.2%;
  --sidebar-ring: 0 0% 70.8%;
}

[data-theme='dark'] .preview,
[data-theme='classic-dark'] .preview {
  --background: 0 0% 14.5%;
  --foreground: 0 0% 98.5%;
  --card: 0 0% 0%;
  --card-foreground: 0 0% 98.5%;
  --popover: 0 0% 14.5%;
  --popover-foreground: 0 0% 98.5%;
  --primary: 0 0% 98.5%;
  --primary-foreground: 0 0% 20.5%;
  --secondary: 0 0% 26.9%;
  --secondary-foreground: 0 0% 98.5%;
  --muted: 0 0% 26.9%;
  --muted-foreground: 0 0% 70.8%;
  --accent: 0 0% 26.9%;
  --accent-foreground: 0 0% 98.5%;
  --destructive: 25.7 14.1% 39.6%;
  --destructive-foreground: 25.3 23.7% 63.7%;
  --border: 0 0% 26.9%;
  --input: 0 0% 26.9%;
  --ring: 0 0% 55.6%;
  --chart-1: 264.4 24.3% 48.8%;
  --chart-2: 162.5 17% 69.6%;
  --chart-3: 70.1 18.8% 76.9%;
  --chart-4: 303.9 26.5% 62.7%;
  --chart-5: 16.4 24.6% 64.5%;
  --sidebar: 0 0% 20.5%;
  --sidebar-foreground: 0 0% 98.5%;
  --sidebar-primary: 264.4 24.3% 48.8%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 26.9%;
  --sidebar-accent-foreground: 0 0% 98.5%;
  --sidebar-border: 0 0% 26.9%;
  --sidebar-ring: 0 0% 43.9%;
}

.preview * {
  @apply border-border outline-ring/50;
}
