---
title: Supabase Client Libraries
description: Supabase client for React Single Page Applications
---

## Installation

<BlockItem name="supabase-client-react" description="Supabase Client for React SPA" />

## Folder structure

<RegistryBlock itemName="supabase-client-react" />

## Usage

This block installs a Supabase client for connecting your React project to Supabase. It's designed for use in client-side components.

If you've already set up a Supabase client in your project, you can just continue using that existing setup.

### Getting started

First, add a `.env` file to your project with the following environment variables:

```env
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
```

- If you're using supabase.com, you can find these values in the [Connect modal](https://supabase.com/dashboard/project/_?showConnect=true) under App Frameworks or in your project's [API settings](https://supabase.com/dashboard/project/_/settings/api).

- If you're using a local instance of Supabase, you can find these values by running `supabase start` or `supabase status` (if you already have it running).

## Further reading

- [Generating TypeScript types for your client](https://supabase.com/docs/guides/api/rest/generating-types)
