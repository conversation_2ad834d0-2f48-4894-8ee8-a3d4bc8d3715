---
title: Password-based Authentication
description: Password-based authentication block for React Single Page Applications
---

<BlockPreview name="password-based-auth/auth/sign-up" />

## Installation

<BlockItem
  name="password-based-auth-react"
  description="All needed components for the password based auth flow"
/>

## Folder structure

<RegistryBlock itemName="password-based-auth-react" />

## Usage

Once you install the block in your React project, you'll get all the necessary pages and components to set up a password-based authentication flow.

### Getting started

First, add a `.env` file to your project with the following environment variables:

```env
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=
```

- If you're using supabase.com, you can find these values in the [Connect modal](https://supabase.com/dashboard/project/_?showConnect=true) under App Frameworks or in your project's [API settings](https://supabase.com/dashboard/project/_/settings/api).

- If you're using a local instance of Supabase, you can find these values by running `supabase start` or `supabase status` (if you already have it running).

### Setting up routes and redirect URLs

1. Set the site URL in the [URL Configuration](https://supabase.com/dashboard/project/_/auth/url-configuration) settings in the Supabase Dashboard.
1. Set up the route users will visit to reset or update their password. Go to the [URL Configuration](https://supabase.com/dashboard/project/_/auth/url-configuration) settings and add the `forgot-password` route to the list of Redirect URLs. It should look something like: `http://example.com/auth/forgot-password`.
1. Update the redirect paths in the `login.tsx` and `update-password.tsx` components to point to the logged-in routes in your app.

1. Add the following code in the authenticated route to redirect to login if the user is unauthenticated.

```ts
import { useEffect } from "react"
import { createClient } from "@supabase/supabase-js"

export default function AuthenticatedRoute() {
  useEffect(() => {
    const checkAuth = async () => {
      const client = createClient()
      const { error } = await client.auth.getUser()

      if (error) {
        location.href = "/login"
      }
    }
    checkAuth()
  }, [])

  return <div>Authenticated page</div>;
}
```

## Further reading

- [Password-based authentication (implicit flow)](https://supabase.com/docs/guides/auth/passwords?queryGroups=flow&flow=implicit)
- [Authentication error codes](https://supabase.com/docs/guides/auth/debugging/error-codes)
- [Email templates](https://supabase.com/docs/guides/auth/auth-email-templates)
- [Email templates for local development](https://supabase.com/docs/guides/local-development/customizing-email-templates)
- [Custom SMTP](https://supabase.com/docs/guides/auth/auth-smtp)
