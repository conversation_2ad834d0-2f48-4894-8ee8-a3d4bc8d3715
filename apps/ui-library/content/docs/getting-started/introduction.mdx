---
title: Introduction
description: A flexible, open-source UI component library built on shadcn/ui, designed to simplify Supabase-powered projects with pre-built Auth, Storage, and Realtime features.
---

Our Supabase UI component library is a set of components built on top of the shadcn/ui registry system. It's designed to make building new or existing Supabase-powered projects faster and easier by providing pre-built solutions for common but challenging features—such as authentication, file uploads, and real-time updates.

Our Supabase UI library aims to solve these issues with:

- **Extensible components**: Modify and extend simple primitives as needed.

- **Composable Components**: Modular structure that makes components easy to integrate and combine.

- **Supabase Clients**: Interact with Supabase however you choose.

- **Scaffolding for Hard Problems**: Pre-built solutions for challenging features like sign-in/sign-up flows, file uploads, and real-time data sync.

- **New or existing projects**: Designed to work with brand new projects or to fit easily into existing ones.

### Extensible components

Our components are designed to be easily extensible. You can modify the code to fit your needs and use it as a starting point for your own components. You can change any of the code to fit your needs and use it as a starting point for your own components. The important part is that the code is designed to make the hard parts easy, and leave the easy parts for you.

### Composable components

Our components are designed to be easily composable. You can combine them to create new components that are tailored to your needs. If you want to change the design or behavior of a component, you can easily do so in your own codebase.

### Supabase clients

The Supabase clients are the backbone of your apps. They are designed to be easily integrated with your existing Supabase project. We provide both client-side and server-side clients for several major frameworks, and the code is designed to be easily integrated with your existing codebase.

### Scaffolding for hard problems

Setting up something like Auth should be easy, so we've done the hard work for you. Our components are designed to be easily integrated with your new or existing Supabase projects. Installing them drops the files into place so you can get the hard parts out of the way quickly.
