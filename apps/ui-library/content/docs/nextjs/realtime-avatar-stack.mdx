---
title: Realtime Avatar Stack
description: Avatar stack in realtime
---

<BlockPreview name="realtime-avatar-stack-demo" />

## Installation

<BlockItem
  name="realtime-avatar-stack-nextjs"
  description="Renders a stack of avatars which are connected via Supabase Realtime"
/>

## Folder structure

<RegistryBlock itemName="realtime-avatar-stack-nextjs" />

## Usage

The `RealtimeAvatarStack` component renders stacked avatars which are connected to Supabase Realtime. It uses the Presence feature of Supabase Realtime. You can use this to show currently online users in a chatroom, game session or collaborative app.

```tsx
import { RealtimeAvatarStack } from '@/components/realtime-avatar-stack'

export default function Page() {
  return (
    <Header className="flex items-center justify-between">
      <h1>Lumon Industries</h1>
      <RealtimeAvatarStack roomName="break_room" />
    </Header>
  )
}
```

## Props

| Prop       | Type     | Default | Description                                          |
| ---------- | -------- | ------- | ---------------------------------------------------- |
| `roomName` | `string` | `null`  | The name of the Supabase Realtime room to connect to |

## Further reading

- [Realtime Presence](https://supabase.com/docs/guides/realtime/presence)
- [Realtime authorization](https://supabase.com/docs/guides/realtime/authorization)
