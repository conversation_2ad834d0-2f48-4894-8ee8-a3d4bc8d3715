---
title: Supabase Client Libraries
description: Supabase client for Next.js
---

## Installation

<BlockItem name="supabase-client-nextjs" description="Supabase Client for Next.js" />

## Folder structure

<RegistryBlock itemName="supabase-client-nextjs" />

## Usage

This block installs a Supabase client for connecting your Next.js project to Supabase. It's designed for use with the App Router and fully supports server-side rendering (SSR).

If you've already set up your Supabase client—either using the `npx create-next-app -e with-supabase` template or another method—you can continue using your existing setup.

### Getting started

First, add a `.env` file to your project with the following environment variables:

```env
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
```

- If you're using supabase.com, you can find these values in the [Connect modal](https://supabase.com/dashboard/project/_?showConnect=true) under App Frameworks or in your project's [API settings](https://supabase.com/dashboard/project/_/settings/api).

- If you're using a local instance of Supabase, you can find these values by running `supabase start` or `supabase status` (if you already have it running).

<Callout type="warning" className="mt-4">
  {' '}
  This Supabase client is built for SSR with the Next.js App Router. If you're building a React SPA,
  use the [React SPA client](/ui/docs/react-router/client) instead.{' '}
</Callout>

## Further reading

- [Generating TypeScript types for your client](https://supabase.com/docs/guides/api/rest/generating-types)
