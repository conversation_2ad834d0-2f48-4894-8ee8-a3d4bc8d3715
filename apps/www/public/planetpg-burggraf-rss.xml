
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Thu, 24 Nov 2022 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-burggraf-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/sql-or-nosql-both-with-postgresql</guid>
  <title>SQL or NoSQL? Why not use both (with PostgreSQL)?</title>
  <link>https://supabase.com/blog/sql-or-nosql-both-with-postgresql</link>
  <description>How to turn Postgres into an easy-to-use NoSQL database that retains all the power of SQL</description>
  <pubDate>Thu, 24 Nov 2022 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/postgres-wasm</guid>
  <title>Postgres WASM by Snaplet and Supabase</title>
  <link>https://supabase.com/blog/postgres-wasm</link>
  <description>We&apos;re open sourcing postgres-wasm, a PostgresQL server that runs inside a browser, with our friends at Snaplet.</description>
  <pubDate>Mon, 03 Oct 2022 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
