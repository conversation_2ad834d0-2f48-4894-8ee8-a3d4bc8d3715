
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Wed, 06 Sep 2023 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-gregnr-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/increase-performance-pgvector-hnsw</guid>
  <title>pgvector v0.5.0: Faster semantic search with HNSW indexes</title>
  <link>https://supabase.com/blog/increase-performance-pgvector-hnsw</link>
  <description>Increase performance in pgvector using HNSW indexes</description>
  <pubDate>Wed, 06 Sep 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/fewer-dimensions-are-better-pgvector</guid>
  <title>pgvector: Fewer dimensions are better</title>
  <link>https://supabase.com/blog/fewer-dimensions-are-better-pgvector</link>
  <description>Increase performance in pgvector by using embedding vectors with fewer dimensions</description>
  <pubDate>Thu, 03 Aug 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/openai-embeddings-postgres-vector</guid>
  <title>Storing OpenAI embeddings in Postgres with pgvector</title>
  <link>https://supabase.com/blog/openai-embeddings-postgres-vector</link>
  <description>An example of how to build an AI-powered search engine using OpenAI&apos;s embeddings and PostgreSQL.</description>
  <pubDate>Mon, 06 Feb 2023 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
