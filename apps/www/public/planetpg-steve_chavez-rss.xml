
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Fri, 16 Dec 2022 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-steve_chavez-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/postgrest-11-prerelease</guid>
  <title>PostgREST 11 pre-release</title>
  <link>https://supabase.com/blog/postgrest-11-prerelease</link>
  <description>Describes new features of PostgREST 11 pre-release</description>
  <pubDate>Fri, 16 Dec 2022 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/roles-postgres-hooks</guid>
  <title>Protecting reserved roles with PostgreSQL Hooks</title>
  <link>https://supabase.com/blog/roles-postgres-hooks</link>
  <description>Using Postgres Hooks to protect functionality in your Postgres database.</description>
  <pubDate>Fri, 02 Jul 2021 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
