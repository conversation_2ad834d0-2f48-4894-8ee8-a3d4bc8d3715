
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Mon, 01 May 2023 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-paul_copplestone-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/postgres-pluggable-strorage</guid>
  <title>Next steps for Postgres pluggable storage</title>
  <link>https://supabase.com/blog/postgres-pluggable-strorage</link>
  <description>Exploring history of Postgres pluggable storage and the possibility of landing it in the Postgres core.</description>
  <pubDate>Mon, 01 May 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/postgres-crdt</guid>
  <title>pg_crdt - an experimental CRDT extension for Postgres</title>
  <link>https://supabase.com/blog/postgres-crdt</link>
  <description>Embedding Yjs and Automerge into Postgres for collaborative applications.</description>
  <pubDate>Sat, 10 Dec 2022 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/partial-postgresql-data-dumps-with-rls</guid>
  <title>Partial data dumps using Postgres Row Level Security</title>
  <link>https://supabase.com/blog/partial-postgresql-data-dumps-with-rls</link>
  <description>Using RLS to create seed files for local PostgreSQL testing.</description>
  <pubDate>Tue, 28 Jun 2022 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/postgres-as-a-cron-server</guid>
  <title>Postgres as a CRON Server</title>
  <link>https://supabase.com/blog/postgres-as-a-cron-server</link>
  <description>Running repetitive tasks with your Postgres database.</description>
  <pubDate>Fri, 05 Mar 2021 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/postgresql-views</guid>
  <title>Postgres Views</title>
  <link>https://supabase.com/blog/postgresql-views</link>
  <description>Creating and using a view in PostgreSQL.</description>
  <pubDate>Wed, 18 Nov 2020 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
