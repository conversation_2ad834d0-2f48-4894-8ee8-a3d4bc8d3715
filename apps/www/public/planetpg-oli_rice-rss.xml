
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Wed, 13 Dec 2023 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-oli_rice-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/postgrest-12</guid>
  <title>PostgREST 12</title>
  <link>https://supabase.com/blog/postgrest-12</link>
  <description>PostgREST 12 is out and we take a look at some of the major new features like JWT Caching and Aggregate Functions</description>
  <pubDate>Wed, 13 Dec 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/pg-graphql-postgres-functions</guid>
  <title>pg_graphql: Postgres functions now supported</title>
  <link>https://supabase.com/blog/pg-graphql-postgres-functions</link>
  <description>pg_graphql now supports the most requested feature: Postgres functions a.k.a. User Defined Functions (UDFs)</description>
  <pubDate>Tue, 12 Dec 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/fewer-dimensions-are-better-pgvector</guid>
  <title>pgvector: Fewer dimensions are better</title>
  <link>https://supabase.com/blog/fewer-dimensions-are-better-pgvector</link>
  <description>Increase performance in pgvector by using embedding vectors with fewer dimensions</description>
  <pubDate>Thu, 03 Aug 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/whats-new-in-pg-graphql-v1-2</guid>
  <title>What&apos;s New in pg_graphql v1.2</title>
  <link>https://supabase.com/blog/whats-new-in-pg-graphql-v1-2</link>
  <description>New Features in the v1.2 release of pg_graphql</description>
  <pubDate>Fri, 21 Apr 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/type-constraints-in-65-lines-of-sql</guid>
  <title>Type Constraints in 65 lines of SQL</title>
  <link>https://supabase.com/blog/type-constraints-in-65-lines-of-sql</link>
  <description>Creating validated data types in Postgres</description>
  <pubDate>Fri, 17 Feb 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/pg-graphql-v1</guid>
  <title>pg_graphql v1.0</title>
  <link>https://supabase.com/blog/pg-graphql-v1</link>
  <description>Announcing the v1.0 release of pg_graphql</description>
  <pubDate>Fri, 16 Dec 2022 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation</guid>
  <title>pg_jsonschema: JSON Schema support for Postgres</title>
  <link>https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation</link>
  <description>Today we&apos;re releasing pg_jsonschema, a Postgres extension for JSON validation.</description>
  <pubDate>Fri, 19 Aug 2022 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/postgres-audit</guid>
  <title>Postgres Auditing in 150 lines of SQL</title>
  <link>https://supabase.com/blog/postgres-audit</link>
  <description>PostgreSQL has a robust set of features which we can leverage to create a generic auditing solution in 150 lines of SQL.</description>
  <pubDate>Tue, 08 Mar 2022 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
