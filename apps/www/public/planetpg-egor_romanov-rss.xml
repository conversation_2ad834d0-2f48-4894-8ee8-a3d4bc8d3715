
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON><PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Thu, 03 Aug 2023 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-egor_romanov-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/fewer-dimensions-are-better-pgvector</guid>
  <title>pgvector: Fewer dimensions are better</title>
  <link>https://supabase.com/blog/fewer-dimensions-are-better-pgvector</link>
  <description>Increase performance in pgvector by using embedding vectors with fewer dimensions</description>
  <pubDate>Thu, 03 Aug 2023 00:00:00 -0700</pubDate>
</item>
<item>
  <guid>https://supabase.com/blog/pgvector-performance</guid>
  <title>pgvector 0.4.0 performance</title>
  <link>https://supabase.com/blog/pgvector-performance</link>
  <description>There&apos;s been a lot of talk about pgvector performance lately, so we took some datasets and pushed pgvector to the limits to find out its strengths and limitations.</description>
  <pubDate>Thu, 13 Jul 2023 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
