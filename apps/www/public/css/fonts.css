/* NOTE: this file is a duplicate of /studio/public/css/fonts.css and should be updated there and then copied here - once we migrate fully to supabase.com/dashboard, this can be deleted */

/* header and body font */

@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-Book.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-Book.woff) format('woff');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-BookItalic.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-BookItalic.woff) format('woff');
  font-weight: 400;
  font-style: italic;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-Medium.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-Medium.woff) format('woff');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-MediumItalic.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-MediumItalic.woff) format('woff');
  font-weight: 500;
  font-style: italic;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-Bold.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-Bold.woff) format('woff');
  font-weight: 700;
  font-style: 600;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-BoldItalic.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-BoldItalic.woff) format('woff');
  font-style: 600;
  font-style: italic;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-Black.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-Black.woff) format('woff');
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: 'circular';
  src:
    url(/fonts/custom/CustomFont-BlackItalic.woff2) format('woff2'),
    url(/fonts/custom/CustomFont-BlackItalic.woff) format('woff');
  font-weight: 800;
  font-style: italic;
}

/* mono font */

@font-face {
  font-family: 'source code pro';
  src: url('/fonts/source-code-pro/SourceCodePro-Regular.eot');
  src:
    url('/fonts/source-code-pro/SourceCodePro-Regular.woff2') format('woff2'),
    url('/fonts/source-code-pro/SourceCodePro-Regular.woff') format('woff'),
    url('/fonts/source-code-pro/SourceCodePro-Regular.ttf') format('truetype'),
    url('/fonts/source-code-pro/SourceCodePro-Regular.svg#SourceCodePro-Regular') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
