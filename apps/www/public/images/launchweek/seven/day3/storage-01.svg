<svg width="1385" height="401" viewBox="0 0 1385 401" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_29_6516)">
<rect width="1385" height="400" transform="translate(0 0.335938)" fill="#1B1B1D"/>
<g filter="url(#filter0_f_29_6516)">
<ellipse cx="390.351" cy="375.287" rx="390.351" ry="375.287" transform="matrix(0.978344 0.206986 0.206986 -0.978344 626.148 753.062)" fill="#1C0C60"/>
</g>
<g filter="url(#filter1_f_29_6516)">
<ellipse cx="188.31" cy="181.043" rx="188.31" ry="181.043" transform="matrix(0.978344 0.206986 0.206986 -0.978344 610.039 471.285)" fill="#310C60"/>
</g>
<g filter="url(#filter2_f_29_6516)">
<ellipse cx="1286.78" cy="-143.193" rx="639.69" ry="338.19" fill="#0A0A0A"/>
</g>
<g filter="url(#filter3_b_29_6516)">
<path d="M840.428 259.367C841.24 258.84 842.187 258.559 843.155 258.559H1645.54C1650.51 258.559 1652.43 265.037 1648.26 267.75L1469.05 384.318C1468.24 384.845 1467.29 385.126 1466.32 385.126H663.943C658.967 385.126 657.045 378.648 661.217 375.935L840.428 259.367Z" fill="url(#paint0_linear_29_6516)" fill-opacity="0.5"/>
<path d="M843.155 259.367H1645.54C1649.71 259.367 1651.32 264.798 1647.82 267.072L1468.61 383.64C1467.93 384.082 1467.14 384.318 1466.32 384.318H663.943C659.771 384.318 658.161 378.887 661.658 376.613L840.869 260.045C841.549 259.602 842.343 259.367 843.155 259.367Z" stroke="url(#paint1_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter4_b_29_6516)">
<path d="M877.428 211.938C878.24 211.41 879.187 211.129 880.155 211.129H1578.54C1583.51 211.129 1585.43 217.607 1581.26 220.32L1402.05 336.888C1401.24 337.416 1400.29 337.697 1399.32 337.697H700.943C695.967 337.697 694.045 331.219 698.217 328.505L877.428 211.938Z" fill="url(#paint2_linear_29_6516)" fill-opacity="0.5"/>
<path d="M880.155 211.937H1578.54C1582.71 211.937 1584.32 217.368 1580.82 219.643L1401.61 336.21C1400.93 336.653 1400.14 336.888 1399.32 336.888H700.943C696.771 336.888 695.161 331.457 698.658 329.183L877.869 212.615C878.549 212.173 879.343 211.937 880.155 211.937Z" stroke="url(#paint3_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter5_b_29_6516)">
<path d="M923.428 160.715C924.24 160.187 925.187 159.906 926.155 159.906H1608.54C1613.51 159.906 1615.43 166.384 1611.26 169.098L1432.05 285.665C1431.24 286.193 1430.29 286.474 1429.32 286.474H746.943C741.967 286.474 740.045 279.996 744.217 277.282L923.428 160.715Z" fill="url(#paint4_linear_29_6516)" fill-opacity="0.5"/>
<path d="M926.155 160.715H1608.54C1612.71 160.715 1614.32 166.145 1610.82 168.42L1431.61 284.988C1430.93 285.43 1430.14 285.665 1429.32 285.665H746.943C742.771 285.665 741.161 280.235 744.658 277.96L923.869 161.393C924.549 160.95 925.343 160.715 926.155 160.715Z" stroke="url(#paint5_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter6_b_29_6516)">
<path d="M944.428 109.492C945.24 108.965 946.187 108.684 947.155 108.684H1597.54C1602.51 108.684 1604.43 115.162 1600.26 117.875L1421.05 234.443C1420.24 234.97 1419.29 235.251 1418.32 235.251H767.943C762.967 235.251 761.045 228.773 765.217 226.06L944.428 109.492Z" fill="url(#paint6_linear_29_6516)" fill-opacity="0.5"/>
<path d="M947.155 109.492H1597.54C1601.71 109.492 1603.32 114.923 1599.82 117.197L1420.61 233.765C1419.93 234.207 1419.14 234.443 1418.32 234.443H767.943C763.771 234.443 762.161 229.012 765.658 226.738L944.869 110.17C945.549 109.727 946.343 109.492 947.155 109.492Z" stroke="url(#paint7_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter7_b_29_6516)">
<path d="M977.428 58.2735C978.24 57.7458 979.187 57.4648 980.155 57.4648H1614.54C1619.51 57.4648 1621.43 63.9428 1617.26 66.6562L1438.05 183.224C1437.24 183.752 1436.29 184.032 1435.32 184.032H800.943C795.967 184.032 794.045 177.554 798.217 174.841L977.428 58.2735Z" fill="url(#paint8_linear_29_6516)" fill-opacity="0.5"/>
<path d="M980.155 58.2733H1614.54C1618.71 58.2733 1620.32 63.7039 1616.82 65.9785L1437.61 182.546C1436.93 182.989 1436.14 183.224 1435.32 183.224H800.943C796.771 183.224 795.161 177.793 798.658 175.519L977.869 58.9512C978.549 58.5087 979.343 58.2733 980.155 58.2733Z" stroke="url(#paint9_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter8_b_29_6516)">
<path d="M1062.12 -17.4609C1062.93 -17.9886 1063.88 -18.2695 1064.84 -18.2695H1739.71C1744.69 -18.2695 1746.61 -11.7915 1742.44 -9.07817L1525.53 132.005C1524.72 132.533 1523.78 132.814 1522.81 132.814H847.943C842.967 132.814 841.045 126.336 845.217 123.622L1062.12 -17.4609Z" fill="url(#paint10_linear_29_6516)" fill-opacity="0.5"/>
<path d="M1064.84 -17.4611H1739.71C1743.88 -17.4611 1745.49 -12.0305 1742 -9.75584L1525.09 131.327C1524.41 131.77 1523.62 132.005 1522.81 132.005H847.943C843.771 132.005 842.16 126.575 845.658 124.3L1062.56 -16.7832C1063.24 -17.2256 1064.03 -17.4611 1064.84 -17.4611Z" stroke="url(#paint11_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter9_b_29_6516)">
<path d="M1064.43 -44.1679C1065.24 -44.6956 1066.19 -44.9766 1067.15 -44.9766H1629.54C1634.51 -44.9766 1636.43 -38.4986 1632.26 -35.7852L1453.05 80.7824C1452.24 81.3101 1451.29 81.591 1450.32 81.591H887.943C882.967 81.591 881.045 75.113 885.217 72.3997L1064.43 -44.1679Z" fill="url(#paint12_linear_29_6516)" fill-opacity="0.5"/>
<path d="M1067.15 -44.1682H1629.54C1633.71 -44.1682 1635.32 -38.7375 1631.82 -36.4629L1452.61 80.1047C1451.93 80.5471 1451.14 80.7826 1450.32 80.7826H887.943C883.771 80.7826 882.161 75.352 885.658 73.0773L1064.87 -43.4903C1065.55 -43.9327 1066.34 -44.1682 1067.15 -44.1682Z" stroke="url(#paint13_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter10_b_29_6516)">
<path d="M1097.43 -86.9843C1098.24 -87.5121 1099.19 -87.793 1100.15 -87.793H1662.54C1667.51 -87.793 1669.43 -81.315 1665.26 -78.6016L1486.05 37.966C1485.24 38.4937 1484.29 38.7746 1483.32 38.7746H920.943C915.967 38.7746 914.045 32.2966 918.217 29.5833L1097.43 -86.9843Z" fill="url(#paint14_linear_29_6516)" fill-opacity="0.5"/>
<path d="M1100.15 -86.9846H1662.54C1666.71 -86.9846 1668.32 -81.5539 1664.82 -79.2793L1485.61 37.2883C1484.93 37.7307 1484.14 37.9662 1483.32 37.9662H920.943C916.771 37.9662 915.161 32.5356 918.658 30.2609L1097.87 -86.3067C1098.55 -86.7491 1099.34 -86.9846 1100.15 -86.9846Z" stroke="url(#paint15_linear_29_6516)" stroke-width="1.61682"/>
</g>
<g filter="url(#filter11_f_29_6516)">
<circle cx="1989.71" cy="96.2227" r="328.129" fill="#1C0C60"/>
</g>
<g filter="url(#filter12_f_29_6516)">
<ellipse cx="1427.95" cy="392.387" rx="377.102" ry="178.148" fill="#090320" fill-opacity="0.9"/>
</g>
</g>
<defs>
<filter id="filter0_f_29_6516" x="396.556" y="-208.7" width="1378.34" height="1350.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="149.688" result="effect1_foregroundBlur_29_6516"/>
</filter>
<filter id="filter1_f_29_6516" x="344.328" y="-147.634" width="974.833" height="961.548" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="149.688" result="effect1_foregroundBlur_29_6516"/>
</filter>
<filter id="filter2_f_29_6516" x="565.546" y="-562.923" width="1442.46" height="839.459" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="40.77" result="effect1_foregroundBlur_29_6516"/>
</filter>
<filter id="filter3_b_29_6516" x="645.999" y="245.624" width="1017.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter4_b_29_6516" x="682.999" y="198.194" width="913.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter5_b_29_6516" x="728.999" y="146.972" width="897.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter6_b_29_6516" x="749.999" y="95.749" width="865.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter7_b_29_6516" x="782.999" y="44.5303" width="849.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter8_b_29_6516" x="829.999" y="-31.2041" width="927.654" height="176.952" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter9_b_29_6516" x="869.999" y="-57.9111" width="777.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter10_b_29_6516" x="902.999" y="-100.728" width="777.48" height="152.437" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.46728"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_29_6516"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_29_6516" result="shape"/>
</filter>
<filter id="filter11_f_29_6516" x="1289.59" y="-603.906" width="1400.26" height="1400.26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="186" result="effect1_foregroundBlur_29_6516"/>
</filter>
<filter id="filter12_f_29_6516" x="910.852" y="74.2383" width="1034.2" height="636.297" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="70" result="effect1_foregroundBlur_29_6516"/>
</filter>
<linearGradient id="paint0_linear_29_6516" x1="954.745" y1="193.631" x2="1101.67" y2="375.118" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint1_linear_29_6516" x1="1091.49" y1="385.127" x2="1153.85" y2="329.567" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint2_linear_29_6516" x1="831.745" y1="146.201" x2="978.672" y2="327.688" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint3_linear_29_6516" x1="968.486" y1="337.697" x2="1030.85" y2="282.137" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint4_linear_29_6516" x1="877.745" y1="94.9783" x2="1024.67" y2="276.465" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint5_linear_29_6516" x1="1014.49" y1="286.474" x2="1076.85" y2="230.914" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint6_linear_29_6516" x1="898.745" y1="43.7556" x2="1045.67" y2="225.242" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint7_linear_29_6516" x1="1035.49" y1="235.252" x2="1097.85" y2="179.692" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint8_linear_29_6516" x1="931.745" y1="-7.46315" x2="1078.67" y2="174.024" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint9_linear_29_6516" x1="876.893" y1="296.765" x2="1092.4" y2="280.302" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint10_linear_29_6516" x1="1007.35" y1="-95.7738" x2="1182.73" y2="120.866" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint11_linear_29_6516" x1="1170.57" y1="132.814" x2="1245.02" y2="66.4928" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint12_linear_29_6516" x1="1018.74" y1="-109.905" x2="1165.67" y2="71.5822" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint13_linear_29_6516" x1="1155.49" y1="81.5915" x2="1217.85" y2="26.0317" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint14_linear_29_6516" x1="1051.74" y1="-152.721" x2="1198.67" y2="28.7658" gradientUnits="userSpaceOnUse">
<stop stop-color="#BB93C5" stop-opacity="0"/>
<stop offset="0.197872" stop-color="#BB93C5" stop-opacity="0.19"/>
<stop offset="1" stop-color="#302642"/>
</linearGradient>
<linearGradient id="paint15_linear_29_6516" x1="1188.49" y1="38.7751" x2="1250.85" y2="-16.7847" gradientUnits="userSpaceOnUse">
<stop stop-color="#D478FF" stop-opacity="0.05"/>
<stop offset="1" stop-color="#D782FF" stop-opacity="0.74"/>
</linearGradient>
<clipPath id="clip0_29_6516">
<rect width="1385" height="400" fill="white" transform="translate(0 0.335938)"/>
</clipPath>
</defs>
</svg>
