<svg width="467" height="398" viewBox="0 0 467 398" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3052_32878)">
<rect width="467" height="398" fill="#030A0C"/>
<g opacity="0.71" filter="url(#filter0_f_3052_32878)">
<ellipse cx="400.367" cy="347.919" rx="337.098" ry="176.771" fill="url(#paint0_linear_3052_32878)"/>
</g>
<g style="mix-blend-mode:overlay" filter="url(#filter1_f_3052_32878)">
<path d="M568.756 357.478C568.756 432.556 418.019 493.418 256.649 493.418C95.2797 493.418 -87.8118 357.478 -35.5363 357.478C144.994 357.478 335.253 198.418 496.623 198.418C657.992 198.418 568.756 282.4 568.756 357.478Z" fill="#C57AFF" fill-opacity="0.9"/>
</g>
<circle cx="318.51" cy="225.717" r="1.63552" transform="rotate(-5.00418 318.51 225.717)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="285.131" cy="104.869" r="1.63552" transform="rotate(-5.00418 285.131 104.869)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="290.163" cy="132.358" r="1.63552" transform="rotate(-5.00418 290.163 132.358)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="331.155" cy="325.748" r="1.63552" transform="rotate(-5.00418 331.155 325.748)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="367.084" cy="309.303" r="1.63552" transform="rotate(-5.00418 367.084 309.303)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="331.819" cy="192.002" r="1.63552" transform="rotate(-5.00418 331.819 192.002)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="334.819" cy="217.13" r="0.838979" transform="rotate(-5.00418 334.819 217.13)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter2_d_3052_32878)">
<circle cx="252.757" cy="158.907" r="0.838979" transform="rotate(41.6234 252.757 158.907)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="332.034" cy="204.524" r="0.838979" transform="rotate(-5.00418 332.034 204.524)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="302.946" cy="207.912" r="0.838979" transform="rotate(72.8592 302.946 207.912)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="326.624" cy="188.583" r="0.838979" transform="rotate(-5.00418 326.624 188.583)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="317.926" cy="207.608" r="0.838979" transform="rotate(72.8592 317.926 207.608)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="353.047" cy="307.647" r="0.838979" transform="rotate(72.8592 353.047 307.647)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="388.024" cy="273.776" r="0.838979" transform="rotate(72.8592 388.024 273.776)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="426.465" cy="337.74" r="0.838979" transform="rotate(72.8592 426.465 337.74)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="352.489" cy="364.76" r="0.838979" transform="rotate(72.8592 352.489 364.76)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter3_d_3052_32878)">
<circle cx="332.653" cy="163.314" r="0.838979" transform="rotate(-36.24 332.653 163.314)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter4_d_3052_32878)">
<circle cx="305.38" cy="188.993" r="0.838979" transform="rotate(-36.24 305.38 188.993)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter5_d_3052_32878)">
<circle cx="385.669" cy="330.314" r="0.838979" transform="rotate(-36.24 385.669 330.314)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter6_f_3052_32878)">
<path d="M232.868 143.855L284.822 235.418H180.914L232.868 143.855Z" fill="url(#paint1_linear_3052_32878)"/>
</g>
<g filter="url(#filter7_d_3052_32878)">
<path d="M232.868 148.844L280.615 232.992H185.121L232.868 148.844Z" fill="#E2BBE6"/>
</g>
<g opacity="0.5">
<path d="M233.869 121.297L305.39 247.344H162.348L233.869 121.297Z" stroke="url(#paint2_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M242.337 74.1255L388.028 279.951L150.768 309.083L242.337 74.1255Z" stroke="url(#paint3_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M239.396 30.02L478.884 298.092L153.794 379.146L239.396 30.02Z" stroke="url(#paint4_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M225.678 -8.95444L575.266 300.356L172.854 454.827L225.678 -8.95444Z" stroke="url(#paint5_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M202.144 -40.8521L674.272 285.777L208.921 533.208L202.144 -40.8521Z" stroke="url(#paint6_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M170.062 -63.9035L772.859 253.86L262.493 611.222L170.062 -63.9035Z" stroke="url(#paint7_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M130.988 -76.5388L867.926 204.617L333.574 685.75L130.988 -76.5388Z" stroke="url(#paint8_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M86.7206 -77.4618L956.373 138.543L421.657 753.663L86.7206 -77.4618Z" stroke="url(#paint9_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M39.2706 -65.6512L1035.19 56.6488L525.745 811.937L39.2706 -65.6512Z" stroke="url(#paint10_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-9.19961 -40.4132L1101.53 -39.5826L644.345 857.698L-9.19961 -40.4132Z" stroke="url(#paint11_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-56.4081 -1.40385L1152.77 -148.215L775.511 888.305L-56.4081 -1.40385Z" stroke="url(#paint12_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-100.008 51.3691L1186.61 -266.884L916.88 901.426L-100.008 51.3691Z" stroke="url(#paint13_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-137.656 117.523L1201.07 -392.859L1065.71 895.088L-137.656 117.523Z" stroke="url(#paint14_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-167.049 196.313L1194.65 -523.084L1218.93 867.746L-167.049 196.313Z" stroke="url(#paint15_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-186.001 286.618L1166.26 -654.259L1373.22 818.311L-186.001 286.618Z" stroke="url(#paint16_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-192.474 386.982L1115.35 -782.893L1525.07 746.208L-192.474 386.982Z" stroke="url(#paint17_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-184.642 495.627L1041.89 -905.379L1670.87 651.402L-184.642 495.627Z" stroke="url(#paint18_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-160.942 610.481L946.401 -1018.08L1806.96 534.405L-160.942 610.481Z" stroke="url(#paint19_linear_3052_32878)" stroke-linejoin="bevel"/>
<path d="M-120.107 729.211L829.962 -1117.42L1929.73 396.281L-120.107 729.211Z" stroke="url(#paint20_linear_3052_32878)" stroke-linejoin="bevel"/>
</g>
<path d="M238.978 30.7407L257.017 -2.67337V-7.29688H7.69141V401.845H321.457L285.364 363.377L253.232 318.799L230.83 278.973L216.235 243.107L205.148 190.27V171.714L238.978 30.7407Z" fill="url(#paint21_radial_3052_32878)"/>
<g filter="url(#filter8_f_3052_32878)">
<ellipse cx="233.501" cy="28.084" rx="364.321" ry="139.396" fill="#0C0B18" fill-opacity="0.65"/>
</g>
</g>
<defs>
<filter id="filter0_f_3052_32878" x="-262.95" y="-155.072" width="1326.64" height="1005.98" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="163.11" result="effect1_foregroundBlur_3052_32878"/>
</filter>
<filter id="filter1_f_3052_32878" x="-208.633" y="34.418" width="966" height="623" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="82" result="effect1_foregroundBlur_3052_32878"/>
</filter>
<filter id="filter2_d_3052_32878" x="250.247" y="156.395" width="5.02255" height="5.02255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.835715"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3052_32878"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3052_32878" result="shape"/>
</filter>
<filter id="filter3_d_3052_32878" x="330.141" y="160.801" width="5.02255" height="5.02255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.835715"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3052_32878"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3052_32878" result="shape"/>
</filter>
<filter id="filter4_d_3052_32878" x="302.868" y="186.481" width="5.02255" height="5.02255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.835715"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3052_32878"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3052_32878" result="shape"/>
</filter>
<filter id="filter5_d_3052_32878" x="383.157" y="327.801" width="5.02255" height="5.02255" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.835715"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3052_32878"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3052_32878" result="shape"/>
</filter>
<filter id="filter6_f_3052_32878" x="175.914" y="138.855" width="113.906" height="101.562" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.5" result="effect1_foregroundBlur_3052_32878"/>
</filter>
<filter id="filter7_d_3052_32878" x="162.081" y="125.804" width="141.576" height="130.228" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="11.52"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.184314 0 0 0 0 0.0509804 0 0 0 0 0.356863 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3052_32878"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3052_32878" result="shape"/>
</filter>
<filter id="filter8_f_3052_32878" x="-222.82" y="-203.312" width="912.641" height="462.793" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="46" result="effect1_foregroundBlur_3052_32878"/>
</filter>
<linearGradient id="paint0_linear_3052_32878" x1="400.367" y1="171.148" x2="400.367" y2="524.69" gradientUnits="userSpaceOnUse">
<stop stop-color="#6F13A4"/>
<stop offset="1" stop-color="#6F13A4" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_3052_32878" x1="232.868" y1="143.855" x2="232.868" y2="235.418" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAE0FF"/>
<stop offset="1" stop-color="#FFC1FD" stop-opacity="0.74"/>
</linearGradient>
<linearGradient id="paint2_linear_3052_32878" x1="204.66" y1="198.724" x2="236.424" y2="205.946" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint3_linear_3052_32878" x1="210.513" y1="215.455" x2="264.861" y2="220.373" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint4_linear_3052_32878" x1="220.279" y1="236.135" x2="296.787" y2="233.353" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint5_linear_3052_32878" x1="234.656" y1="260.033" x2="331.926" y2="244.246" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint6_linear_3052_32878" x1="254.212" y1="286.307" x2="369.893" y2="252.471" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint7_linear_3052_32878" x1="279.379" y1="314.02" x2="410.207" y2="257.509" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint8_linear_3052_32878" x1="310.44" y1="342.186" x2="452.313" y2="258.935" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint9_linear_3052_32878" x1="347.509" y1="369.754" x2="495.579" y2="256.398" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint10_linear_3052_32878" x1="390.544" y1="395.676" x2="539.328" y2="249.664" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint11_linear_3052_32878" x1="439.321" y1="418.904" x2="582.834" y2="238.602" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint12_linear_3052_32878" x1="493.456" y1="438.418" x2="625.354" y2="223.192" gradientUnits="userSpaceOnUse">
<stop stop-color="#989BC7" stop-opacity="0.69"/>
<stop offset="1" stop-color="white" stop-opacity="0.25"/>
</linearGradient>
<linearGradient id="paint13_linear_3052_32878" x1="204.251" y1="10.918" x2="324.994" y2="90.004" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#A095CF"/>
</linearGradient>
<linearGradient id="paint14_linear_3052_32878" x1="183.519" y1="34.1451" x2="323.475" y2="103.146" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#A095CF"/>
</linearGradient>
<linearGradient id="paint15_linear_3052_32878" x1="164.755" y1="65.373" x2="323.108" y2="120.708" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#A095CF"/>
</linearGradient>
<linearGradient id="paint16_linear_3052_32878" x1="149.28" y1="104.419" x2="324.623" y2="142.573" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#D4C9C9"/>
</linearGradient>
<linearGradient id="paint17_linear_3052_32878" x1="138.412" y1="150.898" x2="328.746" y2="168.519" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#D4C9C9"/>
</linearGradient>
<linearGradient id="paint18_linear_3052_32878" x1="133.41" y1="204.228" x2="336.171" y2="198.214" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#D4C9C9"/>
</linearGradient>
<linearGradient id="paint19_linear_3052_32878" x1="135.452" y1="263.626" x2="347.543" y2="231.214" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#D4C9C9"/>
</linearGradient>
<linearGradient id="paint20_linear_3052_32878" x1="145.612" y1="328.117" x2="363.448" y2="266.97" gradientUnits="userSpaceOnUse">
<stop stop-color="#A095CF" stop-opacity="0.6"/>
<stop offset="1" stop-color="#D4C9C9"/>
</linearGradient>
<radialGradient id="paint21_radial_3052_32878" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(282.611 189.919) rotate(166.549) scale(121.366 286.191)">
<stop stop-color="#CF6AFF"/>
<stop offset="1" stop-color="#734D92" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_3052_32878">
<rect width="467" height="398" fill="white"/>
</clipPath>
</defs>
</svg>
