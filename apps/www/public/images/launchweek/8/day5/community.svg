<svg width="1368" height="398" viewBox="0 0 1368 398" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3079_33407)">
<rect width="1368" height="398" fill="#030A0C"/>
<g filter="url(#filter0_f_3079_33407)">
<path d="M868.826 -432.357C821.225 -426.004 790.974 -353.942 724.612 -316.299C665.602 -282.826 570.139 -287.253 529.192 -226.093C462.909 -127.092 726.929 -6.7045 832.156 122.357C1016.76 366.313 849.09 503.152 1127.57 266.901C1157.16 240.274 1312.9 104.447 1040.28 -78.0758C895.113 -175.266 971.759 -439.089 868.826 -432.357Z" fill="#7032BF" fill-opacity="0.1"/>
</g>
<g filter="url(#filter1_f_3079_33407)">
<path d="M792.778 -58.9497C735.616 -17.6117 717.23 82.6025 873.391 114.881C981.179 137.161 929.843 340.967 1008.34 352.707C1119.82 369.381 1258.46 223.708 1339.72 75.5503C1415.02 -61.7303 1330.93 -198.49 1249.42 -104.63C1124.94 38.7159 1082.77 1.45381 1030.97 -25.7894C974.006 -55.751 947.965 17.3282 923.033 -5.07346C875.315 -47.949 832.624 -87.7648 792.778 -58.9497Z" fill="#080316"/>
</g>
<g filter="url(#filter2_f_3079_33407)">
<path d="M877.62 48.702C913.504 -10.3281 1036.44 15.6821 1109.38 51.5514C1182.33 87.4207 1178.3 138.785 1142.42 197.815C1106.53 256.845 1070.78 302.787 1008.03 161.529C971.597 85.9684 841.735 107.732 877.62 48.702Z" fill="#A91BEC" fill-opacity="0.17"/>
</g>
<circle cx="1106.7" cy="105.248" r="1.85544" transform="rotate(-66.8891 1106.7 105.248)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="967.943" cy="74.0452" r="1.85544" transform="rotate(-66.8891 967.943 74.0452)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="998.134" cy="83.7014" r="1.85544" transform="rotate(-66.8891 998.134 83.7014)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1213.56" cy="146.073" r="1.85544" transform="rotate(-66.8891 1213.56 146.073)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1216.31" cy="101.338" r="1.85544" transform="rotate(-66.8891 1216.31 101.338)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1080.09" cy="73.9085" r="1.85544" transform="rotate(-66.8891 1080.09 73.9085)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1106.84" cy="84.3341" r="0.951793" transform="rotate(-66.8891 1106.84 84.3341)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter3_d_3079_33407)">
<circle cx="1000.94" cy="126.634" r="0.951793" transform="rotate(-20.2616 1000.94 126.634)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="1092.73" cy="80.3927" r="0.951793" transform="rotate(-66.8891 1092.73 80.3927)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1080.57" cy="111.307" r="0.951793" transform="rotate(10.9742 1080.57 111.307)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1073.89" cy="77.2833" r="0.951793" transform="rotate(-66.8891 1073.89 77.2833)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1088.28" cy="96.1507" r="0.951793" transform="rotate(10.9742 1088.28 96.1507)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1207.16" cy="114.494" r="0.951793" transform="rotate(10.9742 1207.16 114.494)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1191.96" cy="61.3851" r="0.951793" transform="rotate(10.9742 1191.96 61.3851)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1276.51" cy="57.1195" r="0.951793" transform="rotate(10.9742 1276.51 57.1195)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1264" cy="145.588" r="0.951793" transform="rotate(10.9742 1264 145.588)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter4_d_3079_33407)">
<circle cx="1051.83" cy="57.7396" r="0.951793" transform="rotate(-98.1249 1051.83 57.7396)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter5_d_3079_33407)">
<circle cx="1062.95" cy="98.7514" r="0.951793" transform="rotate(-98.1249 1062.95 98.7514)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter6_d_3079_33407)">
<circle cx="1247.27" cy="93.9662" r="0.951793" transform="rotate(-98.1249 1247.27 93.9662)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="784.504" cy="101.89" r="1.85544" transform="rotate(75 784.504 101.89)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="912.942" cy="40.7959" r="1.85544" transform="rotate(75 912.942 40.7959)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="883.231" cy="51.831" r="1.85544" transform="rotate(75 883.231 51.831)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1072.46" cy="81.456" r="1.85544" transform="rotate(75 1072.46 81.456)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="675.231" cy="135.718" r="1.85544" transform="rotate(75 675.231 135.718)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="700.676" cy="172.612" r="1.85544" transform="rotate(75 700.676 172.612)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="824.79" cy="110.12" r="1.85544" transform="rotate(75 824.79 110.12)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="797.307" cy="118.42" r="0.951793" transform="rotate(75 797.307 118.42)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="595.319" cy="299.681" r="0.951793" transform="rotate(75 595.319 299.681)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="562.487" cy="337.224" r="0.951793" transform="rotate(75 562.487 337.224)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="571.089" cy="391.201" r="0.951793" transform="rotate(75 571.089 391.201)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter7_d_3079_33407)">
<circle cx="854.526" cy="19.7801" r="0.951793" transform="rotate(121.628 854.526 19.7801)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="810.839" cy="112.822" r="0.951793" transform="rotate(75 810.839 112.822)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="801.324" cy="80.9934" r="0.951793" transform="rotate(152.863 801.324 80.9934)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="827.581" cy="103.642" r="0.951793" transform="rotate(75 827.581 103.642)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="804.621" cy="97.6691" r="0.951793" transform="rotate(152.863 804.621 97.6691)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="699.766" cy="156.607" r="0.951793" transform="rotate(152.863 699.766 156.607)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="744.496" cy="189.021" r="0.951793" transform="rotate(152.863 744.496 189.021)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="680.602" cy="244.56" r="0.951793" transform="rotate(152.863 680.602 244.56)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="635.844" cy="167.228" r="0.951793" transform="rotate(152.863 635.844 167.228)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter8_d_3079_33407)">
<circle cx="857.006" cy="105.4" r="0.951793" transform="rotate(43.7642 857.006 105.4)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter9_d_3079_33407)">
<circle cx="822.947" cy="79.9942" r="0.951793" transform="rotate(43.7642 822.947 79.9942)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter10_d_3079_33407)">
<circle cx="1060.66" cy="75.0996" r="0.951793" transform="rotate(43.7642 1060.66 75.0996)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter11_d_3079_33407)">
<circle cx="680.865" cy="197.518" r="0.951793" transform="rotate(43.7642 680.865 197.518)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter12_d_3079_33407)">
<circle cx="555.162" cy="330.795" r="0.951793" transform="rotate(43.7642 555.162 330.795)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 844.227 226.305)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 715.785 165.211)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 745.5 176.25)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 556.273 205.871)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 953.496 260.137)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 928.055 297.031)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 803.941 234.539)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.258819 0.965926 0.965926 0.258819 832.059 243.945)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter13_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.524396 0.851475 0.851475 -0.524396 774.203 146.156)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.258819 0.965926 0.965926 0.258819 818.531 238.344)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 827.434 208.094)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.258819 0.965926 0.965926 0.258819 801.789 229.164)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 824.141 224.77)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 928.996 283.707)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 884.266 316.121)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 948.16 371.664)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 992.918 294.328)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter14_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.722192 0.691692 0.691692 0.722192 773.062 230.742)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter15_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.722192 0.691692 0.691692 0.722192 807.125 205.336)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter16_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.722192 0.691692 0.691692 0.722192 569.902 209.098)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter17_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.722192 0.691692 0.691692 0.722192 949.207 322.863)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1154.17 318.09)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1021.73 321.738)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1043.51 315.445)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1267.36 301.555)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1260.29 345.812)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1121.36 342.871)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1150.79 339.254)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter18_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.84002 0.542556 0.542556 -0.84002 1056.45 275.922)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1136.17 340.027)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 1131.17 308.887)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.182487 0.983208 0.983208 -0.182487 1117.1 338.953)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 1135.38 325.363)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 1255.4 333.383)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 1228.99 381.902)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 1317.66 315.438)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter19_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.35382 0.935314 0.935314 0.35382 1091.86 352.75)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter20_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.35382 0.935314 0.935314 0.35382 1111.66 315.148)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter21_d_3079_33407)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.35382 0.935314 0.935314 0.35382 1290.5 360.016)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g opacity="0.6" filter="url(#filter22_f_3079_33407)">
<ellipse cx="993.846" cy="231.382" rx="430.791" ry="255.351" fill="url(#paint0_linear_3079_33407)"/>
</g>
<g filter="url(#filter23_f_3079_33407)">
<path d="M1043.47 67.0455C1015.31 45.9268 1027.76 -22.3941 1056.84 -38.0374C1098.49 -69.9539 1313.98 -18.3967 1390.49 -9.49823V206.823C1390.49 374.013 1248.97 280.885 1172.71 278.685C1096.44 276.485 1204.97 181.891 1196.17 106.362C1189.75 51.235 1078.67 93.4438 1043.47 67.0455Z" fill="#03090E" fill-opacity="0.5"/>
</g>
<g filter="url(#filter24_f_3079_33407)">
<path d="M830.726 533.034C800.644 533.034 668.602 477.295 668.602 414.747C668.602 352.2 800.644 306.53 830.726 306.53C935.818 291.882 856.75 360.412 885.195 419.782C905.812 469.489 860.809 533.034 830.726 533.034Z" fill="#030A0C"/>
</g>
<g filter="url(#filter25_f_3079_33407)">
<path d="M1379.24 51.6669C1349.16 51.6669 1217.11 27.4972 1217.11 0.375461C1217.11 -26.7462 1349.16 -46.5495 1379.24 -46.5495C1484.33 -52.9012 1405.26 -23.1853 1433.71 2.55872C1454.32 24.1127 1409.32 51.6669 1379.24 51.6669Z" fill="#030A0C"/>
</g>
<g filter="url(#filter26_f_3079_33407)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M912.071 -120.15L911.543 -120.555L910.984 -120.193L875.863 -97.432L837.423 -72.5208L836.835 -72.1396L837.03 -71.4666L858.216 1.51273L858.596 2.82011L859.686 2.0052L912.062 -37.1329L912.743 -37.642L912.307 -38.3721L877.652 -96.3439L911.452 -118.248L953.522 -85.9795V-25.8223H955.408V-86.4447V-86.9098L955.038 -87.1929L912.071 -120.15ZM839.037 -71.3193L876.068 -95.3175L910.252 -38.1345L859.648 -0.320435L839.037 -71.3193ZM783.935 -32.0837L784.754 -31.1825L860.335 51.9591L860.992 52.6813L860.248 53.3126L716.061 175.599L716.025 175.629L715.987 175.655L589.39 263.231L615.408 304.121L650.849 289.042L652.161 288.483V289.909V323.001H650.275V291.335L615.411 306.169L614.676 306.482L614.247 305.808L587.311 263.478L586.824 262.712L587.57 262.196L714.387 174.469L697.247 100.798L697.156 100.405L697.377 100.067L783.267 -31.0648L783.935 -32.0837ZM716.019 173.161L699.176 100.763L784.178 -29.0127L858.283 52.5055L716.019 173.161ZM1261.14 97.8749L1260.07 99.582L1161.21 257.33L1160.81 257.954L1160.11 257.726L1078.05 231.017L954.099 384.638L935.09 461.868L950.384 549.877L948.526 550.2L933.198 461.996L933.164 461.801L933.211 461.609L952.317 383.983L952.368 383.78L952.499 383.617L1076.77 229.611L1069.31 101.669L1069.24 100.509L1070.39 100.682L1151.31 112.841L1259.14 98.1469L1261.14 97.8749ZM1078.63 229.222L1160 255.705L1257.4 100.288L1151.43 114.728L1151.3 114.746L1151.17 114.726L1071.26 102.72L1078.63 229.222ZM995.264 209.507L994.941 208.052L993.764 208.967L942.189 249.108L942.072 249.199L941.989 249.322L928.028 269.876L929.588 270.936L943.465 250.505L993.746 211.371L999.039 235.191L980.35 237.729L979.73 237.813L979.567 238.417L955.726 326.481L957.547 326.974L981.224 239.514L1000.31 236.922L1001.32 236.784L1001.1 235.783L995.264 209.507ZM1076.07 383.405L1076.52 383.522L1157.71 404.777L1160.07 405.395L1157.91 406.525L1019.41 478.962L984.483 564.174L982.738 563.459L1017.75 478.038L1025.61 422.133L1025.67 421.749L1025.97 421.514L1075.71 383.684L1076.07 383.405ZM1019.86 476.6L1154.87 405.984L1076.49 385.463L1027.42 422.78L1019.86 476.6ZM1303.32 185.085L1294.24 130.147L1296.1 129.839L1305.25 185.231L1305.33 185.685L1305.02 186.023L1257.54 237.683L1251.9 301.344L1251.87 301.652L1251.67 301.884L1185.9 376.47L1185.54 376.886L1184.99 376.767L1078.48 353.534L1078.01 353.431L1077.82 352.989L1053.93 298.079L1053.69 297.544L1054.07 297.096L1083.54 262.03L1083.95 261.55L1084.54 261.736L1151.63 282.573L1152.07 282.711L1152.23 283.149L1185.54 374.034L1250.05 300.869L1255.69 237.197L1255.71 236.878L1255.93 236.642L1303.32 185.085ZM1183.72 374.559L1150.62 284.235L1084.58 263.723L1055.89 297.863L1079.35 351.794L1183.72 374.559ZM990.901 101.038L990.06 100.957L989.887 101.783L982.29 138.105L982.172 138.669L982.62 139.031L1044.82 189.35L1046 187.884L984.254 137.927L991.56 102.996L1032.5 106.957L1032.68 105.08L990.901 101.038ZM735.024 227.015L815.318 165.854L814.175 164.354L733.68 225.668L733.479 225.821L733.381 226.055L691.634 326.107L693.375 326.833L735.024 227.015ZM913.468 18.6348L912.61 18.4412L912.357 19.2837L879.968 127.247L879.963 127.261L879.96 127.276L825.655 331.68L739.523 328.114L739.445 329.998L826.333 333.596L827.089 333.627L827.283 332.896L881.655 128.238L954.582 102.122L955.207 101.898V101.234V28.8067V28.0528L954.472 27.8868L913.468 18.6348ZM953.321 100.57L882.311 126L913.911 20.6681L953.321 29.5606V100.57Z" fill="url(#paint1_radial_3079_33407)" fill-opacity="0.15"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M911.653 -119.623L911.513 -119.73L911.365 -119.634L876.243 -96.8738L837.804 -71.9626L837.648 -71.8615L837.7 -71.6831L858.886 1.2962L858.986 1.64281L859.275 1.42677L911.651 -37.7113L911.831 -37.8463L911.715 -38.0399L876.718 -96.5853L911.489 -119.119L954.218 -86.3447V-25.8456H954.718V-86.468V-86.5913L954.62 -86.6664L911.653 -119.623ZM838.232 -71.6441L876.298 -96.3132L911.171 -37.9769L859.265 0.810204L838.232 -71.6441ZM784.042 -30.9639L784.26 -30.7249L859.841 52.4166L860.015 52.6081L859.818 52.7755L715.631 175.062L715.621 175.07L715.611 175.077L588.465 263.032L615.158 304.98L651.138 289.671L651.486 289.523V289.901V322.993H650.986V290.279L615.158 305.523L614.963 305.606L614.849 305.427L587.914 263.097L587.785 262.894L587.983 262.757L715.187 174.762L697.941 100.632L697.916 100.528L697.975 100.438L783.865 -30.6938L784.042 -30.9639ZM715.62 174.416L698.452 100.623L784.107 -30.1497L859.297 52.5615L715.62 174.416ZM1259.77 98.7455L1259.48 99.1981L1160.62 256.946L1160.52 257.111L1160.33 257.051L1077.82 230.195L953.466 384.307L934.386 461.827L949.705 549.979L949.212 550.065L933.884 461.861L933.875 461.809L933.888 461.758L952.994 384.133L953.007 384.079L953.042 384.036L1077.48 229.822L1070.01 101.613L1069.99 101.305L1070.29 101.351L1151.31 113.525L1259.24 98.8176L1259.77 98.7455ZM1077.97 229.719L1160.3 256.515L1258.78 99.3851L1151.34 114.025L1151.31 114.03L1151.27 114.025L1070.52 101.891L1077.97 229.719ZM994.591 209.648L994.505 209.263L994.193 209.505L942.619 249.646L942.588 249.67L942.565 249.703L928.605 270.257L929.018 270.538L942.957 250.017L994.189 210.143L999.883 235.768L980.447 238.407L980.283 238.43L980.239 238.59L956.399 326.653L956.881 326.784L980.679 238.88L1000.22 236.227L1000.49 236.19L1000.43 235.925L994.591 209.648ZM1076.24 384.145L1076.35 384.176L1157.55 405.432L1158.17 405.596L1157.6 405.895L1018.88 478.449L983.853 563.895L983.39 563.705L1018.44 478.204L1026.31 422.214L1026.32 422.112L1026.4 422.049L1076.14 384.219L1076.24 384.145ZM1018.99 477.823L1156.79 405.752L1076.35 384.691L1026.79 422.385L1018.99 477.823ZM1304.06 185.297L1294.93 130.025L1295.42 129.944L1304.57 185.336L1304.59 185.456L1304.51 185.546L1256.87 237.379L1251.22 301.274L1251.21 301.356L1251.15 301.417L1185.39 376.003L1185.29 376.113L1185.15 376.082L1078.63 352.849L1078.51 352.821L1078.45 352.704L1054.56 297.794L1054.5 297.652L1054.6 297.534L1084.08 262.467L1084.18 262.34L1084.34 262.389L1151.43 283.226L1151.55 283.262L1151.59 283.378L1185.29 375.357L1250.72 301.148L1256.38 237.25L1256.39 237.165L1256.45 237.103L1304.06 185.297ZM1184.81 375.496L1151.16 283.667L1084.35 262.916L1055.09 297.737L1078.86 352.387L1184.81 375.496ZM990.845 101.719L990.623 101.697L990.577 101.917L982.979 138.238L982.948 138.388L983.067 138.484L1045.26 188.803L1045.58 188.414L983.5 138.191L991.02 102.238L1032.58 106.259L1032.62 105.761L990.845 101.719ZM734.46 226.56L814.902 165.287L814.599 164.889L734.104 226.203L734.05 226.244L734.024 226.306L692.277 326.357L692.739 326.55L734.46 226.56ZM913.319 19.3022L913.092 19.2509L913.025 19.4742L880.635 127.437L880.633 127.445L826.186 332.387L739.498 328.798L739.477 329.297L826.365 332.895L826.566 332.903L826.617 332.709L881.082 127.7L954.352 101.461L954.518 101.401V101.225V28.7981V28.5982L954.323 28.5542L913.319 19.3022ZM881.256 127.107L913.437 19.8413L954.018 28.998V101.049L881.256 127.107Z" fill="url(#paint2_radial_3079_33407)"/>
<g filter="url(#filter27_d_3079_33407)">
<circle cx="1000.47" cy="236.006" r="1.5" transform="rotate(-15 1000.47 236.006)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter28_d_3079_33407)">
<circle cx="928.511" cy="270.058" r="1.5" transform="rotate(89.5676 928.511 270.058)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter29_d_3079_33407)">
<circle cx="953.297" cy="383.398" r="1.5" transform="rotate(89.5676 953.297 383.398)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter30_d_3079_33407)">
<circle cx="1078.93" cy="351.211" r="1.5" transform="rotate(89.5676 1078.93 351.211)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter31_d_3079_33407)">
<circle cx="1055.09" cy="297.25" r="1.5" transform="rotate(89.5676 1055.09 297.25)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter32_d_3079_33407)">
<circle cx="1084.26" cy="262.64" r="1.5" transform="rotate(89.5676 1084.26 262.64)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter33_d_3079_33407)">
<circle cx="1151.52" cy="282.847" r="1.5" transform="rotate(89.5676 1151.52 282.847)" fill="#FFFFF9"/>
</g>
<circle cx="1205.73" cy="292.855" r="1" transform="rotate(89.5676 1205.73 292.855)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter34_d_3079_33407)">
<circle cx="1075.93" cy="384.089" r="1.5" transform="rotate(89.5676 1075.93 384.089)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter35_d_3079_33407)">
<circle cx="733.996" cy="225.98" r="1.5" transform="rotate(89.5676 733.996 225.98)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter36_d_3079_33407)">
<circle cx="713.984" cy="175.261" r="1.5" transform="rotate(89.5676 713.984 175.261)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter37_d_3079_33407)">
<circle cx="1160.57" cy="255.484" r="1.5" transform="rotate(89.5676 1160.57 255.484)" fill="#FFFFF9"/>
</g>
<circle cx="1133.47" cy="61.1212" r="1" transform="rotate(-15 1133.47 61.1212)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter38_d_3079_33407)">
<circle cx="1258.07" cy="99.0616" r="0.75" transform="rotate(-15 1258.07 99.0616)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter39_d_3079_33407)">
<circle cx="954.376" cy="100.87" r="0.75" transform="rotate(-15 954.376 100.87)" fill="#FFFFF9"/>
</g>
<circle cx="1227.85" cy="86.8165" r="1" transform="rotate(-15 1227.85 86.8165)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter40_d_3079_33407)">
<circle cx="1156.33" cy="405.057" r="1.5" transform="rotate(-15 1156.33 405.057)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter41_d_3079_33407)">
<circle cx="980.829" cy="238.904" r="1.5" transform="rotate(-15 980.829 238.904)" fill="#FFFFF9"/>
</g>
<circle cx="861.416" cy="286.902" r="1" transform="rotate(-15 861.416 286.902)" fill="#FFFFF9" fill-opacity="0.4"/>
<circle cx="760.799" cy="270.07" r="1" transform="rotate(-15 760.799 270.07)" fill="#FFFFF9" fill-opacity="0.4"/>
<path opacity="0.2" d="M1792.3 676.262L1629.18 663.166L1466.06 723.886M1466.06 723.886L1415.26 507.984L1315.25 496.872L1298.58 547.672L1312.07 631.017L1466.06 723.886Z" stroke="white" stroke-opacity="0.5" stroke-width="0.942974"/>
<g filter="url(#filter42_d_3079_33407)">
<circle cx="913.286" cy="19.4717" r="0.75" transform="rotate(-15 913.286 19.4717)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter43_d_3079_33407)">
<circle cx="942.859" cy="248.937" r="1.5" transform="rotate(89.5676 942.859 248.937)" fill="#FFFFF9"/>
</g>
<circle cx="1155.47" cy="189.333" r="1" transform="rotate(150 1155.47 189.333)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter44_d_3079_33407)">
<circle cx="1149.18" cy="114.242" r="0.75" transform="rotate(-105.432 1149.18 114.242)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter45_d_3079_33407)">
<circle cx="1071.07" cy="101.569" r="1.5" transform="rotate(-105.432 1071.07 101.569)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter46_d_3079_33407)">
<path d="M1077.6 228.092C1078.4 227.871 1079.23 228.34 1079.45 229.139C1079.67 229.937 1079.2 230.763 1078.4 230.984C1077.6 231.204 1076.78 230.735 1076.56 229.937C1076.34 229.138 1076.81 228.312 1077.6 228.092Z" fill="#FFFFF9"/>
</g>
<g filter="url(#filter47_d_3079_33407)">
<circle cx="1293.48" cy="129.549" r="1.5" transform="rotate(-105.432 1293.48 129.549)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter48_d_3079_33407)">
<circle cx="697.578" cy="101.299" r="1.5" transform="rotate(-105.432 697.578 101.299)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter49_d_3079_33407)">
<circle cx="953.984" cy="28.7682" r="1.5" transform="rotate(-105.432 953.984 28.7682)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter50_d_3079_33407)">
<circle cx="881.246" cy="128.034" r="1.5" transform="rotate(-105.432 881.246 128.034)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter51_d_3079_33407)">
<circle cx="651.484" cy="323.534" r="0.75" transform="rotate(-105.432 651.484 323.534)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter52_d_3079_33407)">
<circle cx="586.613" cy="264.202" r="1.5" transform="rotate(-105.432 586.613 264.202)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter53_d_3079_33407)">
<circle cx="649.013" cy="290.995" r="3.24339" transform="rotate(-105.432 649.013 290.995)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter54_d_3079_33407)">
<circle cx="614.441" cy="304.609" r="0.75" transform="rotate(-105.432 614.441 304.609)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter55_d_3079_33407)">
<circle cx="997.568" cy="266.733" r="1.5" transform="rotate(-15 997.568 266.733)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter56_d_3079_33407)">
<circle cx="858.164" cy="1.06118" r="1.5" transform="rotate(-105.432 858.164 1.06118)" fill="#FFFFF9"/>
</g>
<circle cx="776.713" cy="43.1879" r="1" transform="rotate(-105.432 776.713 43.1879)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter57_d_3079_33407)">
<circle cx="859.086" cy="52.3548" r="0.75" transform="rotate(-105.432 859.086 52.3548)" fill="#FFFFF9"/>
</g>
<circle cx="677.155" cy="65.1293" r="1" transform="rotate(-105.432 677.155 65.1293)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter58_d_3079_33407)">
<circle cx="983.262" cy="138.238" r="0.75" transform="rotate(-105.432 983.262 138.238)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter59_d_3079_33407)">
<circle cx="1031.84" cy="105.761" r="2.18831" transform="rotate(-105.432 1031.84 105.761)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter60_d_3079_33407)">
<circle cx="991.004" cy="101.917" r="1.5" transform="rotate(-105.432 991.004 101.917)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter61_d_3079_33407)">
<circle cx="1045.16" cy="188.046" r="1.5" transform="rotate(-105.432 1045.16 188.046)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter62_d_3079_33407)">
<circle cx="737.695" cy="329.268" r="1.5" transform="rotate(-105.432 737.695 329.268)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter63_d_3079_33407)">
<circle cx="1302.75" cy="185.003" r="1.5" transform="rotate(-105.432 1302.75 185.003)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter64_d_3079_33407)">
<circle cx="993.887" cy="209.647" r="1.5" transform="rotate(-105.432 993.887 209.647)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter65_d_3079_33407)">
<circle cx="1256.43" cy="236.795" r="1.5" transform="rotate(150 1256.43 236.795)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter66_d_3079_33407)">
<circle cx="956.529" cy="325.549" r="1.5" transform="rotate(150 956.529 325.549)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter67_d_3079_33407)">
<circle cx="826.545" cy="332.467" r="1.5" transform="rotate(150 826.545 332.467)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter68_d_3079_33407)">
<circle cx="692.033" cy="325.127" r="1.5" transform="rotate(150 692.033 325.127)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter69_d_3079_33407)">
<circle cx="814.338" cy="164.931" r="1.5" transform="rotate(150 814.338 164.931)" fill="#FFFFF9"/>
</g>
<circle cx="1173.53" cy="181.993" r="1" transform="rotate(150 1173.53 181.993)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter70_d_3079_33407)">
<circle cx="1184.81" cy="375.447" r="1.5" transform="rotate(150 1184.81 375.447)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter71_d_3079_33407)">
<circle cx="1250.49" cy="299.455" r="1.5" transform="rotate(150 1250.49 299.455)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter72_d_3079_33407)">
<circle cx="1204.57" cy="161.28" r="1.5" transform="rotate(-105.432 1204.57 161.28)" fill="#FFFFF9"/>
</g>
<g style="mix-blend-mode:overlay" filter="url(#filter73_f_3079_33407)">
<ellipse cx="894.879" cy="111.249" rx="354.359" ry="145.963" transform="rotate(43.6665 894.879 111.249)" fill="#C448F0" fill-opacity="0.9"/>
</g>
<g filter="url(#filter74_d_3079_33407)">
<circle cx="1076.54" cy="50.6338" r="1.5" transform="rotate(-74.419 1076.54 50.6338)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter75_d_3079_33407)">
<circle cx="1161.2" cy="36.0713" r="1.5" transform="rotate(-74.419 1161.2 36.0713)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter76_d_3079_33407)">
<circle cx="1292.45" cy="88.2002" r="1.5" transform="rotate(-74.419 1292.45 88.2002)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter77_d_3079_33407)">
<circle cx="1344.02" cy="79.6494" r="1.5" transform="rotate(-74.419 1344.02 79.6494)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter78_d_3079_33407)">
<circle cx="1346.63" cy="51.7393" r="1.5" transform="rotate(-74.419 1346.63 51.7393)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter79_d_3079_33407)">
<circle cx="1327.98" cy="10.5401" r="1.5" transform="rotate(-74.419 1327.98 10.5401)" fill="#8A8A8A"/>
</g>
<path opacity="0.2" d="M1075.46 51.1758L1160.96 36.3632L1236.7 -16.3559M1236.7 -16.3559L1291.3 87.81L1344.13 80.3114L1345.98 52.0227L1328.04 11.004L1236.7 -16.3559Z" stroke="white" stroke-opacity="0.5" stroke-width="0.5"/>
<g filter="url(#filter80_d_3079_33407)">
<circle cx="556.595" cy="315.049" r="1.5" transform="rotate(-15 556.595 315.049)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter81_d_3079_33407)">
<circle cx="539.575" cy="276.877" r="1.5" transform="rotate(-15 539.575 276.877)" fill="#FFFFF9"/>
</g>
<path opacity="0.2" d="M538.726 276.355L556.262 314.977L533.988 326.113L538.726 413.5" stroke="white" stroke-opacity="0.5" stroke-width="0.5"/>
<g filter="url(#filter82_d_3079_33407)">
<circle cx="534.488" cy="326.022" r="1.5" transform="rotate(-105.432 534.488 326.022)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter83_d_3079_33407)">
<circle cx="598.321" cy="89.0216" r="1.5" transform="rotate(-15 598.321 89.0216)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter84_d_3079_33407)">
<circle cx="624.825" cy="107.213" r="1.5" transform="rotate(-15 624.825 107.213)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter85_d_3079_33407)">
<circle cx="548.853" cy="130.975" r="1.5" transform="rotate(-15 548.853 130.975)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter86_d_3079_33407)">
<circle cx="548.853" cy="207.26" r="1.5" transform="rotate(-15 548.853 207.26)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter87_d_3079_33407)">
<circle cx="624.794" cy="182.389" r="1.5" transform="rotate(-15 624.794 182.389)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter88_d_3079_33407)">
<circle cx="625.642" cy="24.4943" r="1.5" transform="rotate(-15 625.642 24.4943)" fill="#FFFFF9"/>
</g>
<path opacity="0.2" d="M625.371 24.2148L597.652 89.1367L625.371 106.906" stroke="white" stroke-opacity="0.5" stroke-width="0.5"/>
<g filter="url(#filter89_f_3079_33407)">
<path d="M841.559 317.224C869.718 338.343 857.27 406.664 828.183 422.307C786.533 454.223 472.838 437.508 396.33 428.61L396.33 212.289C396.33 45.0989 688.857 50.333 712.322 105.584C742.146 175.808 680.057 202.379 688.857 277.908C695.279 333.034 806.361 290.826 841.559 317.224Z" fill="#03090E" fill-opacity="0.5"/>
</g>
<path opacity="0.2" d="M548.852 130.906L624.793 106.906V182.848L548.852 206.848V130.906Z" stroke="white" stroke-opacity="0.5" stroke-width="0.5"/>
</g>
<defs>
<filter id="filter0_f_3079_33407" x="435.656" y="-515.484" width="845.961" height="987.898" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="41.5" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter1_f_3079_33407" x="737.998" y="-148.924" width="644.383" height="516.269" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.665" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter2_f_3079_33407" x="819.084" y="-38.5839" width="400.695" height="345.738" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="26.165" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter3_d_3079_33407" x="998.088" y="123.784" width="5.69861" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter4_d_3079_33407" x="1048.98" y="54.8929" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter5_d_3079_33407" x="1060.1" y="95.9046" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter6_d_3079_33407" x="1244.42" y="91.1194" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter7_d_3079_33407" x="851.678" y="16.9319" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter8_d_3079_33407" x="854.159" y="102.553" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter9_d_3079_33407" x="820.1" y="77.1468" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter10_d_3079_33407" x="1057.81" y="72.2523" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter11_d_3079_33407" x="678.018" y="194.67" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter12_d_3079_33407" x="552.315" y="327.948" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter13_d_3079_33407" x="772.666" y="143.619" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter14_d_3079_33407" x="770.186" y="229.241" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter15_d_3079_33407" x="804.248" y="203.834" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter16_d_3079_33407" x="567.026" y="207.596" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter17_d_3079_33407" x="946.33" y="321.362" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter18_d_3079_33407" x="1054.92" y="272.791" width="5.69861" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter19_d_3079_33407" x="1089.56" y="351.127" width="5.6947" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter20_d_3079_33407" x="1109.36" y="313.526" width="5.6947" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter21_d_3079_33407" x="1288.21" y="358.393" width="5.6947" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter22_f_3079_33407" x="236.835" y="-350.189" width="1514.02" height="1163.14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="163.11" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter23_f_3079_33407" x="855.629" y="-220.348" width="706.863" height="698.863" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="86" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter24_f_3079_33407" x="504.602" y="140.527" width="549.82" height="556.508" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="82" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter25_f_3079_33407" x="1073.11" y="-191.418" width="509.82" height="387.086" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="72" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter26_f_3079_33407" x="584.824" y="-122.555" width="722.504" height="688.73" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter27_d_3079_33407" x="991.813" y="227.352" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter28_d_3079_33407" x="919.86" y="261.406" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter29_d_3079_33407" x="944.645" y="374.746" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter30_d_3079_33407" x="1070.27" y="342.559" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter31_d_3079_33407" x="1046.44" y="288.598" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter32_d_3079_33407" x="1075.61" y="253.988" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter33_d_3079_33407" x="1142.87" y="274.195" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter34_d_3079_33407" x="1067.27" y="375.438" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter35_d_3079_33407" x="725.344" y="217.328" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter36_d_3079_33407" x="705.332" y="166.61" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter37_d_3079_33407" x="1151.91" y="246.832" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter38_d_3079_33407" x="1250.16" y="91.1603" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter39_d_3079_33407" x="946.473" y="92.9689" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter40_d_3079_33407" x="1147.67" y="396.402" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter41_d_3079_33407" x="972.176" y="230.25" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter42_d_3079_33407" x="905.383" y="11.5705" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter43_d_3079_33407" x="934.207" y="240.285" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter44_d_3079_33407" x="1141.28" y="106.34" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter45_d_3079_33407" x="1062.42" y="92.9181" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter46_d_3079_33407" x="1069.35" y="220.887" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter47_d_3079_33407" x="1284.83" y="120.899" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter48_d_3079_33407" x="688.926" y="92.6486" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter49_d_3079_33407" x="945.332" y="20.1173" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter50_d_3079_33407" x="872.594" y="119.383" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter51_d_3079_33407" x="643.582" y="315.633" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter52_d_3079_33407" x="577.961" y="255.551" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter53_d_3079_33407" x="638.617" y="280.598" width="20.7927" height="20.7927" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter54_d_3079_33407" x="606.539" y="296.707" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter55_d_3079_33407" x="988.914" y="258.078" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter56_d_3079_33407" x="849.512" y="-7.58969" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter57_d_3079_33407" x="851.184" y="44.4533" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter58_d_3079_33407" x="975.36" y="130.336" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter59_d_3079_33407" x="1022.5" y="96.422" width="18.6794" height="18.6794" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter60_d_3079_33407" x="982.352" y="93.2658" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter61_d_3079_33407" x="1036.5" y="179.395" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter62_d_3079_33407" x="729.043" y="320.617" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter63_d_3079_33407" x="1294.1" y="176.352" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter64_d_3079_33407" x="985.235" y="200.996" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter65_d_3079_33407" x="1247.78" y="228.141" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter66_d_3079_33407" x="947.875" y="316.895" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter67_d_3079_33407" x="817.891" y="323.813" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter68_d_3079_33407" x="683.379" y="316.473" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter69_d_3079_33407" x="805.684" y="156.277" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter70_d_3079_33407" x="1176.16" y="366.793" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter71_d_3079_33407" x="1241.83" y="290.801" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter72_d_3079_33407" x="1195.92" y="152.629" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter73_f_3079_33407" x="397.689" y="-376.983" width="994.379" height="976.462" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="110.841" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<filter id="filter74_d_3079_33407" x="1071.25" y="45.3405" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter75_d_3079_33407" x="1155.9" y="30.778" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter76_d_3079_33407" x="1287.16" y="82.9069" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter77_d_3079_33407" x="1338.72" y="74.3561" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter78_d_3079_33407" x="1341.34" y="46.4459" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter79_d_3079_33407" x="1322.69" y="5.2467" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter80_d_3079_33407" x="551.301" y="309.755" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter81_d_3079_33407" x="534.282" y="271.583" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter82_d_3079_33407" x="529.196" y="320.731" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter83_d_3079_33407" x="593.028" y="83.7272" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter84_d_3079_33407" x="619.532" y="101.919" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter85_d_3079_33407" x="543.559" y="125.68" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter86_d_3079_33407" x="543.559" y="201.965" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter87_d_3079_33407" x="619.501" y="177.094" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter88_d_3079_33407" x="620.348" y="19.1998" width="10.5847" height="10.5886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33407"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33407" result="shape"/>
</filter>
<filter id="filter89_f_3079_33407" x="280.332" y="-45.0195" width="693.066" height="602.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="58" result="effect1_foregroundBlur_3079_33407"/>
</filter>
<linearGradient id="paint0_linear_3079_33407" x1="993.846" y1="-23.9688" x2="993.846" y2="486.733" gradientUnits="userSpaceOnUse">
<stop stop-color="#6F13A4"/>
<stop offset="1" stop-color="#6F13A4" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_3079_33407" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(946.076 221.811) rotate(90) scale(288.716 377.401)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_3079_33407" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(946.189 222.082) rotate(90) scale(141.813 148.696)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_3079_33407">
<rect width="1368" height="398" fill="white"/>
</clipPath>
</defs>
</svg>
