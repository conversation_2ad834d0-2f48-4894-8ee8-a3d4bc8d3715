<svg width="467" height="398" viewBox="0 0 467 398" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3079_33677)">
<rect width="467" height="398" fill="#030A0C"/>
<g filter="url(#filter0_f_3079_33677)">
<path d="M-32.1736 -432.357C-79.7755 -426.004 -110.026 -353.942 -176.388 -316.299C-235.398 -282.826 -330.861 -287.253 -371.808 -226.093C-438.091 -127.092 -174.071 -6.7045 -68.8436 122.357C115.762 366.313 -51.9099 503.152 226.568 266.901C256.162 240.274 411.895 104.447 139.276 -78.0758C-5.88735 -175.266 70.7593 -439.089 -32.1736 -432.357Z" fill="#7032BF" fill-opacity="0.1"/>
</g>
<g filter="url(#filter1_f_3079_33677)">
<path d="M-108.222 -58.9497C-165.384 -17.6117 -183.77 82.6025 -27.6093 114.881C80.1788 137.161 28.8429 340.967 107.339 352.707C218.817 369.381 357.463 223.708 438.724 75.5503C514.019 -61.7303 429.935 -198.49 348.424 -104.63C223.939 38.7159 181.773 1.45381 129.974 -25.7894C73.006 -55.751 46.9654 17.3282 22.0334 -5.07346C-25.6851 -47.949 -68.3763 -87.7648 -108.222 -58.9497Z" fill="#080316"/>
</g>
<g filter="url(#filter2_f_3079_33677)">
<path d="M-23.3802 48.702C12.5042 -10.3281 135.436 15.6821 208.382 51.5514C281.328 87.4207 277.3 138.785 241.416 197.815C205.531 256.845 169.778 302.787 107.031 161.529C70.5968 85.9684 -59.2647 107.732 -23.3802 48.702Z" fill="#A91BEC" fill-opacity="0.17"/>
</g>
<circle cx="205.704" cy="105.248" r="1.85544" transform="rotate(-66.8891 205.704 105.248)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="66.9426" cy="74.0452" r="1.85544" transform="rotate(-66.8891 66.9426 74.0452)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="97.134" cy="83.7014" r="1.85544" transform="rotate(-66.8891 97.134 83.7014)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="312.56" cy="146.073" r="1.85544" transform="rotate(-66.8891 312.56 146.073)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="315.314" cy="101.338" r="1.85544" transform="rotate(-66.8891 315.314 101.338)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="179.091" cy="73.9085" r="1.85544" transform="rotate(-66.8891 179.091 73.9085)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="205.839" cy="84.3341" r="0.951793" transform="rotate(-66.8891 205.839 84.3341)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter3_d_3079_33677)">
<circle cx="99.9374" cy="126.634" r="0.951793" transform="rotate(-20.2616 99.9374 126.634)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="191.733" cy="80.3927" r="0.951793" transform="rotate(-66.8891 191.733 80.3927)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="179.574" cy="111.307" r="0.951793" transform="rotate(10.9742 179.574 111.307)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="172.89" cy="77.2833" r="0.951793" transform="rotate(-66.8891 172.89 77.2833)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="187.277" cy="96.1507" r="0.951793" transform="rotate(10.9742 187.277 96.1507)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="306.156" cy="114.494" r="0.951793" transform="rotate(10.9742 306.156 114.494)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="290.96" cy="61.3851" r="0.951793" transform="rotate(10.9742 290.96 61.3851)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="375.515" cy="57.1195" r="0.951793" transform="rotate(10.9742 375.515 57.1195)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="363.003" cy="145.588" r="0.951793" transform="rotate(10.9742 363.003 145.588)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter4_d_3079_33677)">
<circle cx="150.827" cy="57.7396" r="0.951793" transform="rotate(-98.1249 150.827 57.7396)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter5_d_3079_33677)">
<circle cx="161.948" cy="98.7514" r="0.951793" transform="rotate(-98.1249 161.948 98.7514)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter6_d_3079_33677)">
<circle cx="346.273" cy="93.9662" r="0.951793" transform="rotate(-98.1249 346.273 93.9662)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="11.9419" cy="40.7959" r="1.85544" transform="rotate(75 11.9419 40.7959)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="171.458" cy="81.456" r="1.85544" transform="rotate(75 171.458 81.456)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter7_d_3079_33677)">
<circle cx="159.658" cy="75.0996" r="0.951793" transform="rotate(43.7642 159.658 75.0996)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 52.4961 260.137)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(-0.258819 0.965926 0.965926 0.258819 27.0547 297.031)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 27.9961 283.707)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 47.1602 371.664)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.889921 0.456115 0.456115 -0.889921 91.918 294.328)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter8_d_3079_33677)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.722192 0.691692 0.691692 0.722192 48.207 322.863)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 253.168 318.09)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 120.73 321.738)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 142.512 315.445)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 366.355 301.555)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 359.285 345.812)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="1.85544" cy="1.85544" r="1.85544" transform="matrix(0.182487 0.983208 0.983208 -0.182487 220.359 342.871)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.182487 0.983208 0.983208 -0.182487 249.789 339.254)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter9_d_3079_33677)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.84002 0.542556 0.542556 -0.84002 155.453 275.922)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.182487 0.983208 0.983208 -0.182487 235.168 340.027)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 230.172 308.887)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.182487 0.983208 0.983208 -0.182487 216.098 338.953)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 234.383 325.363)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 354.398 333.383)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 327.992 381.902)" fill="#CACBDC" fill-opacity="0.15"/>
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(0.999599 0.0283061 0.0283061 -0.999599 416.66 315.438)" fill="#CACBDC" fill-opacity="0.15"/>
<g filter="url(#filter10_d_3079_33677)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.35382 0.935314 0.935314 0.35382 190.859 352.75)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter11_d_3079_33677)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.35382 0.935314 0.935314 0.35382 210.656 315.148)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter12_d_3079_33677)">
<circle cx="0.951793" cy="0.951793" r="0.951793" transform="matrix(-0.35382 0.935314 0.935314 0.35382 389.5 360.016)" fill="white" fill-opacity="0.41" shape-rendering="crispEdges"/>
</g>
<g opacity="0.6" filter="url(#filter13_f_3079_33677)">
<ellipse cx="92.8457" cy="231.382" rx="430.791" ry="255.351" fill="url(#paint0_linear_3079_33677)"/>
</g>
<g filter="url(#filter14_f_3079_33677)">
<path d="M-27.9996 125C-56.1585 103.881 -57.0878 -22.3941 -28.0008 -38.0374C13.6492 -69.9539 412.985 -18.3966 489.493 -9.49818V206.823C489.493 374.013 394.761 328.2 318.499 326C242.237 323.8 280.505 218.029 271.706 142.5C265.283 87.3733 7.19893 151.398 -27.9996 125Z" fill="#03090E" fill-opacity="0.5"/>
</g>
<g filter="url(#filter15_f_3079_33677)">
<path d="M-70.2737 533.034C-100.356 533.034 -232.398 477.295 -232.398 414.747C-232.398 352.2 -100.356 306.53 -70.2737 306.53C34.8177 291.882 -44.2496 360.412 -15.805 419.782C4.81185 469.489 -40.1914 533.034 -70.2737 533.034Z" fill="#030A0C"/>
</g>
<g filter="url(#filter16_f_3079_33677)">
<path d="M478.238 51.6669C448.156 51.6669 316.114 27.4972 316.114 0.375461C316.114 -26.7462 448.156 -46.5495 478.238 -46.5495C583.329 -52.9012 504.262 -23.1853 532.707 2.55872C553.324 24.1127 508.32 51.6669 478.238 51.6669Z" fill="#030A0C"/>
</g>
<g filter="url(#filter17_f_3079_33677)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.0712 -120.15L10.5431 -120.555L9.9845 -120.193L-25.137 -97.432L-63.5768 -72.5208L-64.1649 -72.1396L-63.9695 -71.4666L-42.7836 1.51273L-42.4041 2.82011L-41.3135 2.0052L11.0618 -37.1329L11.7431 -37.642L11.3067 -38.3721L-23.3482 -96.3439L10.4515 -118.248L52.5216 -85.9795V-25.8223H54.4075V-86.4447V-86.9098L54.0385 -87.1929L11.0712 -120.15ZM-61.963 -71.3193L-24.9319 -95.3175L9.25153 -38.1345L-41.3519 -0.320435L-61.963 -71.3193ZM-117.065 -32.0837L-116.246 -31.1825L-40.6647 51.9591L-40.0081 52.6813L-40.7524 53.3126L-184.939 175.599L-184.975 175.629L-185.013 175.655L-311.61 263.231L-285.592 304.121L-250.151 289.042L-248.839 288.483V289.909V323.001H-250.725V291.335L-285.589 306.169L-286.324 306.482L-286.753 305.808L-313.689 263.478L-314.176 262.712L-313.43 262.196L-186.613 174.469L-203.753 100.798L-203.844 100.405L-203.623 100.067L-117.733 -31.0648L-117.065 -32.0837ZM-184.981 173.161L-201.824 100.763L-116.822 -29.0127L-42.7167 52.5055L-184.981 173.161ZM360.138 97.8749L359.068 99.582L260.205 257.33L259.814 257.954L259.114 257.726L177.053 231.017L53.0986 384.638L34.0896 461.868L49.384 549.877L47.5259 550.2L32.1976 461.996L32.1638 461.801L32.2111 461.609L51.3174 383.983L51.3675 383.78L51.4991 383.617L175.765 229.611L168.31 101.669L168.242 100.509L169.391 100.682L250.312 112.841L358.142 98.1469L360.138 97.8749ZM177.632 229.222L258.998 255.705L356.4 100.288L250.433 114.728L250.299 114.746L250.165 114.726L170.26 102.72L177.632 229.222ZM94.264 209.507L93.9406 208.052L92.7643 208.967L41.1895 249.108L41.0721 249.199L40.9886 249.322L27.0278 269.876L28.5878 270.936L42.4651 250.505L92.7462 211.371L98.039 235.191L79.3502 237.729L78.7303 237.813L78.5668 238.417L54.7263 326.481L56.5468 326.974L80.2238 239.514L99.3088 236.922L100.325 236.784L100.102 235.783L94.264 209.507ZM175.074 383.405L175.519 383.522L256.71 404.777L259.069 405.395L256.908 406.525L118.411 478.962L83.4833 564.174L81.7383 563.459L116.751 478.038L124.611 422.133L124.665 421.749L124.974 421.514L174.709 383.684L175.074 383.405ZM118.858 476.6L253.873 405.984L175.485 385.463L126.425 422.78L118.858 476.6ZM402.318 185.085L393.241 130.147L395.102 129.839L404.253 185.231L404.328 185.685L404.017 186.023L356.537 237.683L350.902 301.344L350.875 301.652L350.67 301.884L284.903 376.47L284.536 376.886L283.995 376.767L177.479 353.534L177.008 353.431L176.816 352.989L152.926 298.079L152.693 297.544L153.069 297.096L182.543 262.03L182.946 261.55L183.545 261.736L250.629 282.573L251.074 282.711L251.235 283.149L284.536 374.034L349.051 300.869L354.686 237.197L354.715 236.878L354.931 236.642L402.318 185.085ZM282.72 374.559L249.625 284.235L183.584 263.723L154.888 297.863L178.352 351.794L282.72 374.559ZM89.9012 101.038L89.0604 100.957L88.8874 101.783L81.2899 138.105L81.172 138.669L81.6198 139.031L143.815 189.35L145.001 187.884L83.2539 137.927L90.5605 102.996L131.499 106.957L131.68 105.08L89.9012 101.038ZM-165.976 227.015L-85.6818 165.854L-86.8246 164.354L-167.32 225.668L-167.521 225.821L-167.619 226.055L-209.366 326.107L-207.625 326.833L-165.976 227.015ZM12.4681 18.6348L11.6102 18.4412L11.3574 19.2837L-21.0323 127.247L-21.0366 127.261L-21.0404 127.276L-75.345 331.68L-161.477 328.114L-161.555 329.998L-74.667 333.596L-73.9109 333.627L-73.7166 332.896L-19.3448 128.238L53.582 102.122L54.207 101.898V101.234V28.8067V28.0528L53.4716 27.8868L12.4681 18.6348ZM52.321 100.57L-18.6894 126L12.9111 20.6681L52.321 29.5606V100.57Z" fill="url(#paint1_radial_3079_33677)" fill-opacity="0.15"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.6531 -119.623L10.5131 -119.73L10.3649 -119.634L-24.7565 -96.8738L-63.1964 -71.9626L-63.3522 -71.8615L-63.3005 -71.6831L-42.1144 1.2962L-42.0139 1.64281L-41.7247 1.42677L10.6506 -37.7113L10.8312 -37.8463L10.7155 -38.0399L-24.2823 -96.5853L10.4888 -119.119L53.2181 -86.3447V-25.8456H53.7181V-86.468V-86.5913L53.6203 -86.6664L10.6531 -119.623ZM-62.7685 -71.6441L-24.7021 -96.3132L10.1706 -37.9769L-41.7349 0.810204L-62.7685 -71.6441ZM-116.958 -30.9639L-116.74 -30.7249L-41.1591 52.4166L-40.985 52.6081L-41.1823 52.7755L-185.369 175.062L-185.379 175.07L-185.389 175.077L-312.535 263.032L-285.842 304.98L-249.862 289.671L-249.514 289.523V289.901V322.993H-250.014V290.279L-285.842 305.523L-286.037 305.606L-286.151 305.427L-313.086 263.097L-313.215 262.894L-313.017 262.757L-185.813 174.762L-203.059 100.632L-203.084 100.528L-203.025 100.438L-117.134 -30.6938L-116.958 -30.9639ZM-185.38 174.416L-202.548 100.623L-116.893 -30.1497L-41.7031 52.5615L-185.38 174.416ZM358.768 98.7455L358.485 99.1981L259.622 256.946L259.518 257.111L259.332 257.051L176.818 230.195L52.4662 384.307L33.3857 461.827L48.7049 549.979L48.2123 550.065L32.884 461.861L32.8751 461.809L32.8876 461.758L51.994 384.133L52.0072 384.079L52.0421 384.036L176.476 229.822L169.005 101.613L168.987 101.305L169.292 101.351L250.311 113.525L358.239 98.8176L358.768 98.7455ZM176.971 229.719L259.302 256.515L357.777 99.3851L250.343 114.025L250.307 114.03L250.272 114.025L169.522 101.891L176.971 229.719ZM93.5911 209.648L93.5054 209.263L93.1935 209.505L41.6187 249.646L41.5876 249.67L41.5654 249.703L27.6046 270.257L28.0182 270.538L41.9569 250.017L93.1887 210.143L98.8825 235.768L79.447 238.407L79.2827 238.43L79.2393 238.59L55.3989 326.653L55.8815 326.784L79.6786 238.88L99.2192 236.227L99.4885 236.19L99.4296 235.925L93.5911 209.648ZM175.236 384.145L175.354 384.176L256.545 405.432L257.17 405.596L256.598 405.895L117.876 478.449L82.8531 563.895L82.3904 563.705L117.436 478.204L125.308 422.214L125.323 422.112L125.405 422.049L175.139 384.219L175.236 384.145ZM117.995 477.823L255.793 405.752L175.345 384.691L125.789 422.385L117.995 477.823ZM403.06 185.297L393.928 130.025L394.422 129.944L403.573 185.336L403.593 185.456L403.511 185.546L355.871 237.379L350.215 301.274L350.208 301.356L350.154 301.417L284.387 376.003L284.29 376.113L284.146 376.082L177.631 352.849L177.506 352.821L177.455 352.704L153.565 297.794L153.503 297.652L153.603 297.534L183.077 262.467L183.184 262.34L183.343 262.389L250.427 283.226L250.545 283.262L250.588 283.378L284.29 375.357L349.725 301.148L355.38 237.25L355.388 237.165L355.445 237.103L403.06 185.297ZM283.808 375.496L250.161 283.667L183.353 262.916L154.085 297.737L177.862 352.387L283.808 375.496ZM89.8455 101.719L89.6226 101.697L89.5767 101.917L81.9792 138.238L81.948 138.388L82.0667 138.484L144.262 188.803L144.577 188.414L82.4999 138.191L90.0202 102.238L131.576 106.259L131.625 105.761L89.8455 101.719ZM-166.54 226.56L-86.0981 165.287L-86.4011 164.889L-166.896 226.203L-166.95 226.244L-166.976 226.306L-208.723 326.357L-208.261 326.55L-166.54 226.56ZM12.3192 19.3022L12.0918 19.2509L12.0248 19.4742L-20.3651 127.437L-20.3671 127.445L-74.8145 332.387L-161.502 328.798L-161.523 329.297L-74.6347 332.895L-74.4343 332.903L-74.3828 332.709L-19.9175 127.7L53.3519 101.461L53.5176 101.401V101.225V28.7981V28.5982L53.3227 28.5542L12.3192 19.3022ZM-19.7438 127.107L12.4366 19.8413L53.0176 28.998V101.049L-19.7438 127.107Z" fill="url(#paint2_radial_3079_33677)"/>
<g filter="url(#filter18_d_3079_33677)">
<circle cx="99.466" cy="236.006" r="1.5" transform="rotate(-15 99.466 236.006)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter19_d_3079_33677)">
<circle cx="27.5114" cy="270.058" r="1.5" transform="rotate(89.5676 27.5114 270.058)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter20_d_3079_33677)">
<circle cx="52.2965" cy="383.398" r="1.5" transform="rotate(89.5676 52.2965 383.398)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter21_d_3079_33677)">
<circle cx="177.925" cy="351.211" r="1.5" transform="rotate(89.5676 177.925 351.211)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter22_d_3079_33677)">
<circle cx="154.089" cy="297.25" r="1.5" transform="rotate(89.5676 154.089 297.25)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter23_d_3079_33677)">
<circle cx="183.257" cy="262.64" r="1.5" transform="rotate(89.5676 183.257 262.64)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter24_d_3079_33677)">
<circle cx="250.519" cy="282.847" r="1.5" transform="rotate(89.5676 250.519 282.847)" fill="#FFFFF9"/>
</g>
<circle cx="304.734" cy="292.855" r="1" transform="rotate(89.5676 304.734 292.855)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter25_d_3079_33677)">
<circle cx="174.925" cy="384.089" r="1.5" transform="rotate(89.5676 174.925 384.089)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter26_d_3079_33677)">
<circle cx="259.566" cy="255.484" r="1.5" transform="rotate(89.5676 259.566 255.484)" fill="#FFFFF9"/>
</g>
<circle cx="232.471" cy="61.1212" r="1" transform="rotate(-15 232.471 61.1212)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter27_d_3079_33677)">
<circle cx="357.067" cy="99.0616" r="0.75" transform="rotate(-15 357.067 99.0616)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter28_d_3079_33677)">
<circle cx="53.3756" cy="100.87" r="0.75" transform="rotate(-15 53.3756 100.87)" fill="#FFFFF9"/>
</g>
<circle cx="326.85" cy="86.8165" r="1" transform="rotate(-15 326.85 86.8165)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter29_d_3079_33677)">
<circle cx="255.325" cy="405.057" r="1.5" transform="rotate(-15 255.325 405.057)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter30_d_3079_33677)">
<circle cx="79.8293" cy="238.904" r="1.5" transform="rotate(-15 79.8293 238.904)" fill="#FFFFF9"/>
</g>
<path opacity="0.2" d="M891.301 676.262L728.179 663.166L565.064 723.886M565.064 723.886L514.263 507.984L414.25 496.872L397.581 547.672L411.075 631.017L565.064 723.886Z" stroke="white" stroke-opacity="0.5" stroke-width="0.942974"/>
<g filter="url(#filter31_d_3079_33677)">
<circle cx="12.2857" cy="19.4717" r="0.75" transform="rotate(-15 12.2857 19.4717)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter32_d_3079_33677)">
<circle cx="41.859" cy="248.937" r="1.5" transform="rotate(89.5676 41.859 248.937)" fill="#FFFFF9"/>
</g>
<circle cx="254.47" cy="189.333" r="1" transform="rotate(150 254.47 189.333)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter33_d_3079_33677)">
<circle cx="248.184" cy="114.242" r="0.75" transform="rotate(-105.432 248.184 114.242)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter34_d_3079_33677)">
<circle cx="170.074" cy="101.569" r="1.5" transform="rotate(-105.432 170.074 101.569)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter35_d_3079_33677)">
<path d="M176.605 228.092C177.403 227.871 178.229 228.34 178.45 229.139C178.67 229.937 178.202 230.763 177.403 230.984C176.604 231.204 175.778 230.735 175.558 229.937C175.337 229.138 175.806 228.312 176.605 228.092Z" fill="#FFFFF9"/>
</g>
<g filter="url(#filter36_d_3079_33677)">
<circle cx="392.48" cy="129.549" r="1.5" transform="rotate(-105.432 392.48 129.549)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter37_d_3079_33677)">
<circle cx="52.9843" cy="28.7682" r="1.5" transform="rotate(-105.432 52.9843 28.7682)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter38_d_3079_33677)">
<circle cx="96.5676" cy="266.733" r="1.5" transform="rotate(-15 96.5676 266.733)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter39_d_3079_33677)">
<circle cx="82.2617" cy="138.238" r="0.75" transform="rotate(-105.432 82.2617 138.238)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter40_d_3079_33677)">
<circle cx="130.84" cy="105.761" r="2.18831" transform="rotate(-105.432 130.84 105.761)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter41_d_3079_33677)">
<circle cx="90.0038" cy="101.917" r="1.5" transform="rotate(-105.432 90.0038 101.917)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter42_d_3079_33677)">
<circle cx="144.156" cy="188.046" r="1.5" transform="rotate(-105.432 144.156 188.046)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter43_d_3079_33677)">
<circle cx="401.75" cy="185.003" r="1.5" transform="rotate(-105.432 401.75 185.003)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter44_d_3079_33677)">
<circle cx="92.8866" cy="209.647" r="1.5" transform="rotate(-105.432 92.8866 209.647)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter45_d_3079_33677)">
<circle cx="355.431" cy="236.795" r="1.5" transform="rotate(150 355.431 236.795)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter46_d_3079_33677)">
<circle cx="55.5291" cy="325.549" r="1.5" transform="rotate(150 55.5291 325.549)" fill="#FFFFF9"/>
</g>
<circle cx="272.529" cy="181.993" r="1" transform="rotate(150 272.529 181.993)" fill="#FFFFF9" fill-opacity="0.4"/>
<g filter="url(#filter47_d_3079_33677)">
<circle cx="283.81" cy="375.447" r="1.5" transform="rotate(150 283.81 375.447)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter48_d_3079_33677)">
<circle cx="349.486" cy="299.455" r="1.5" transform="rotate(150 349.486 299.455)" fill="#FFFFF9"/>
</g>
<g filter="url(#filter49_d_3079_33677)">
<circle cx="303.574" cy="161.28" r="1.5" transform="rotate(-105.432 303.574 161.28)" fill="#FFFFF9"/>
</g>
<g style="mix-blend-mode:overlay" filter="url(#filter50_f_3079_33677)">
<ellipse cx="-6.12062" cy="111.249" rx="354.359" ry="145.963" transform="rotate(43.6665 -6.12062 111.249)" fill="#C448F0" fill-opacity="0.9"/>
</g>
<g filter="url(#filter51_d_3079_33677)">
<circle cx="175.539" cy="50.6338" r="1.5" transform="rotate(-74.419 175.539 50.6338)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter52_d_3079_33677)">
<circle cx="260.195" cy="36.0713" r="1.5" transform="rotate(-74.419 260.195 36.0713)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter53_d_3079_33677)">
<circle cx="391.453" cy="88.2002" r="1.5" transform="rotate(-74.419 391.453 88.2002)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter54_d_3079_33677)">
<circle cx="443.016" cy="79.6494" r="1.5" transform="rotate(-74.419 443.016 79.6494)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter55_d_3079_33677)">
<circle cx="445.629" cy="51.7393" r="1.5" transform="rotate(-74.419 445.629 51.7393)" fill="#8A8A8A"/>
</g>
<g filter="url(#filter56_d_3079_33677)">
<circle cx="426.984" cy="10.5401" r="1.5" transform="rotate(-74.419 426.984 10.5401)" fill="#8A8A8A"/>
</g>
<path opacity="0.2" d="M174.457 51.1758L259.955 36.3632L335.703 -16.3559M335.703 -16.3559L390.298 87.81L443.125 80.3114L444.978 52.0227L427.044 11.004L335.703 -16.3559Z" stroke="white" stroke-opacity="0.5" stroke-width="0.5"/>
<g filter="url(#filter57_f_3079_33677)">
<path d="M-59.4407 317.224C-31.2818 338.343 -43.7298 406.664 -72.8168 422.307C-114.467 454.223 -428.162 437.508 -504.67 428.61L-504.67 212.289C-504.67 45.0989 -212.143 50.333 -188.678 105.584C-158.854 175.808 -220.943 202.379 -212.143 277.908C-205.721 333.034 -94.6392 290.826 -59.4407 317.224Z" fill="#03090E" fill-opacity="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_f_3079_33677" x="-465.344" y="-515.484" width="845.961" height="987.898" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="41.5" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter1_f_3079_33677" x="-163.002" y="-148.924" width="644.383" height="516.269" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.665" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter2_f_3079_33677" x="-81.9159" y="-38.5839" width="400.695" height="345.738" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="26.165" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter3_d_3079_33677" x="97.0882" y="123.784" width="5.69861" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter4_d_3079_33677" x="147.979" y="54.8929" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter5_d_3079_33677" x="159.1" y="95.9046" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter6_d_3079_33677" x="343.424" y="91.1194" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter7_d_3079_33677" x="156.811" y="72.2523" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter8_d_3079_33677" x="45.3304" y="321.362" width="5.6947" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter9_d_3079_33677" x="153.92" y="272.791" width="5.69861" height="5.6947" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter10_d_3079_33677" x="188.565" y="351.127" width="5.6947" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter11_d_3079_33677" x="208.362" y="313.526" width="5.6947" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter12_d_3079_33677" x="387.205" y="358.393" width="5.6947" height="5.69861" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.94809"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter13_f_3079_33677" x="-664.165" y="-350.189" width="1514.02" height="1163.14" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="163.11" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter14_f_3079_33677" x="-221.469" y="-220.348" width="882.961" height="725.711" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="86" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter15_f_3079_33677" x="-396.398" y="140.527" width="549.82" height="556.508" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="82" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter16_f_3079_33677" x="172.113" y="-191.418" width="509.82" height="387.086" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="72" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter17_f_3079_33677" x="-316.176" y="-122.555" width="722.504" height="688.73" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter18_d_3079_33677" x="90.8127" y="227.352" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter19_d_3079_33677" x="18.8595" y="261.406" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter20_d_3079_33677" x="43.6447" y="374.746" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter21_d_3079_33677" x="169.274" y="342.559" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter22_d_3079_33677" x="145.438" y="288.598" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter23_d_3079_33677" x="174.606" y="253.988" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter24_d_3079_33677" x="241.867" y="274.195" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter25_d_3079_33677" x="166.274" y="375.438" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter26_d_3079_33677" x="250.914" y="246.832" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter27_d_3079_33677" x="349.164" y="91.1603" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter28_d_3079_33677" x="45.4728" y="92.9689" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter29_d_3079_33677" x="246.672" y="396.402" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter30_d_3079_33677" x="71.1759" y="230.25" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter31_d_3079_33677" x="4.38296" y="11.5705" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter32_d_3079_33677" x="33.2072" y="240.285" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter33_d_3079_33677" x="240.281" y="106.34" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter34_d_3079_33677" x="161.422" y="92.9181" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter35_d_3079_33677" x="168.352" y="220.887" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter36_d_3079_33677" x="383.828" y="120.899" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter37_d_3079_33677" x="44.3322" y="20.1173" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter38_d_3079_33677" x="87.9142" y="258.078" width="17.3044" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter39_d_3079_33677" x="74.3595" y="130.336" width="15.8044" height="15.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter40_d_3079_33677" x="121.5" y="96.422" width="18.6794" height="18.6794" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter41_d_3079_33677" x="81.3517" y="93.2658" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter42_d_3079_33677" x="135.504" y="179.395" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter43_d_3079_33677" x="393.098" y="176.352" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter44_d_3079_33677" x="84.2345" y="200.996" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter45_d_3079_33677" x="346.777" y="228.141" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter46_d_3079_33677" x="46.8752" y="316.895" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter47_d_3079_33677" x="275.156" y="366.793" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter48_d_3079_33677" x="340.832" y="290.801" width="17.3083" height="17.3083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter49_d_3079_33677" x="294.922" y="152.629" width="17.3044" height="17.3044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5761"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter50_f_3079_33677" x="-503.311" y="-376.983" width="994.379" height="976.462" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="110.841" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<filter id="filter51_d_3079_33677" x="170.247" y="45.3405" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter52_d_3079_33677" x="254.903" y="30.778" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter53_d_3079_33677" x="386.161" y="82.9069" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter54_d_3079_33677" x="437.723" y="74.3561" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter55_d_3079_33677" x="440.337" y="46.4459" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter56_d_3079_33677" x="421.692" y="5.2467" width="10.5847" height="10.5847" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.89618"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.980333 0 0 0 0 0.508333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3079_33677"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3079_33677" result="shape"/>
</filter>
<filter id="filter57_f_3079_33677" x="-620.668" y="-45.0195" width="693.066" height="602.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="58" result="effect1_foregroundBlur_3079_33677"/>
</filter>
<linearGradient id="paint0_linear_3079_33677" x1="92.8457" y1="-23.9688" x2="92.8457" y2="486.733" gradientUnits="userSpaceOnUse">
<stop stop-color="#6F13A4"/>
<stop offset="1" stop-color="#6F13A4" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_3079_33677" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(45.0763 221.811) rotate(90) scale(288.716 377.401)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_3079_33677" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(45.1892 222.082) rotate(90) scale(141.813 148.696)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_3079_33677">
<rect width="467" height="398" fill="white"/>
</clipPath>
</defs>
</svg>
