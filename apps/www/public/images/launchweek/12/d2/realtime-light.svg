<svg width="764" height="220" viewBox="0 0 764 220" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_895_5178)">
<rect width="764" height="219" transform="translate(0 0.419922)" fill="white"/>
<path d="M865.508 47.5982H752.791C744.7 47.5982 736.909 50.6633 730.987 56.1765L692.38 92.1173C686.458 97.6305 678.667 100.696 670.576 100.696H591.411" stroke="#707070" stroke-width="0.5" stroke-linecap="round"/>
<path d="M636.927 100.696H554.89C546.401 100.696 538.259 104.069 532.257 110.074L487.35 155.003C481.348 161.007 473.206 164.381 464.717 164.381H416.564" stroke="#B2B2B2" stroke-width="0.5" stroke-linecap="square" stroke-dasharray="2 3"/>
<path d="M456.226 60.7529L495.954 60.7529C503.471 60.7529 510.748 63.3992 516.509 68.2278L546.329 93.2206C552.09 98.0492 559.367 100.696 566.884 100.696H650.36" stroke="#525252" stroke-width="0.5" stroke-linecap="round"/>
<circle cx="394.86" cy="163.421" r="19.9058" fill="white" stroke="#B2B2B2"/>
<path d="M402.641 166.916V158.386C402.641 157.883 402.43 157.401 402.053 157.046C401.677 156.69 401.167 156.49 400.634 156.49H388.591C388.059 156.49 387.549 156.69 387.172 157.046C386.796 157.401 386.584 157.883 386.584 158.386V166.916M402.641 166.916H386.584M402.641 166.916L403.926 169.333C404.003 169.478 404.04 169.64 404.032 169.802C404.025 169.964 403.973 170.122 403.882 170.26C403.792 170.398 403.665 170.511 403.514 170.59C403.364 170.668 403.195 170.709 403.023 170.708H386.203C386.031 170.709 385.862 170.668 385.711 170.59C385.561 170.511 385.434 170.398 385.343 170.26C385.253 170.122 385.201 169.964 385.193 169.802C385.186 169.64 385.222 169.478 385.3 169.333L386.584 166.916" stroke="#525252" stroke-width="0.669043" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter0_d_895_5178)">
<path d="M583.104 100.696C583.104 94.6204 588.028 89.6956 594.104 89.6956H650.104C656.179 89.6956 661.104 94.6204 661.104 100.696C661.104 106.771 656.179 111.696 650.104 111.696H594.104C588.028 111.696 583.104 106.771 583.104 100.696Z" fill="#FCFCFC" shape-rendering="crispEdges"/>
<path d="M583.604 100.696C583.604 94.8966 588.305 90.1956 594.104 90.1956H650.104C655.902 90.1956 660.604 94.8966 660.604 100.696C660.604 106.495 655.903 111.196 650.104 111.196H594.104C588.305 111.196 583.604 106.495 583.604 100.696Z" stroke="#DFDFDF" shape-rendering="crispEdges"/>
<path d="M598.119 101.196V98.9856H598.039L596.569 102.576L595.099 98.9856H595.019V101.196V104.696H594.249V97.7156H595.329L596.559 100.796H596.619L597.849 97.7156H598.889V104.696H598.119V101.196ZM601.765 104.696V97.7156H606.065V98.4456H602.605V100.796H605.945V101.526H602.605V103.966H606.065V104.696H601.765ZM611.141 104.816C610.515 104.816 610.001 104.702 609.601 104.476C609.201 104.242 608.871 103.946 608.611 103.586L609.221 103.086C609.495 103.426 609.781 103.679 610.081 103.846C610.388 104.012 610.751 104.096 611.171 104.096C611.678 104.096 612.065 103.979 612.331 103.746C612.598 103.512 612.731 103.186 612.731 102.766C612.731 102.426 612.635 102.162 612.441 101.976C612.248 101.782 611.911 101.646 611.431 101.566L610.651 101.436C610.311 101.376 610.025 101.286 609.791 101.166C609.565 101.046 609.381 100.902 609.241 100.736C609.101 100.562 609.001 100.376 608.941 100.176C608.881 99.9689 608.851 99.7556 608.851 99.5356C608.851 98.8956 609.061 98.4122 609.481 98.0856C609.901 97.7589 610.468 97.5956 611.181 97.5956C611.741 97.5956 612.211 97.6889 612.591 97.8756C612.978 98.0622 613.288 98.3256 613.521 98.6656L612.931 99.1756C612.738 98.9222 612.505 98.7156 612.231 98.5556C611.958 98.3956 611.605 98.3156 611.171 98.3156C610.698 98.3156 610.335 98.4156 610.081 98.6156C609.828 98.8156 609.701 99.1156 609.701 99.5156C609.701 99.8289 609.795 100.086 609.981 100.286C610.175 100.479 610.518 100.619 611.011 100.706L611.761 100.836C612.101 100.896 612.388 100.986 612.621 101.106C612.855 101.226 613.041 101.369 613.181 101.536C613.328 101.702 613.431 101.889 613.491 102.096C613.551 102.302 613.581 102.519 613.581 102.746C613.581 103.392 613.368 103.899 612.941 104.266C612.521 104.632 611.921 104.816 611.141 104.816ZM618.437 104.816C617.811 104.816 617.297 104.702 616.897 104.476C616.497 104.242 616.167 103.946 615.907 103.586L616.517 103.086C616.791 103.426 617.077 103.679 617.377 103.846C617.684 104.012 618.047 104.096 618.467 104.096C618.974 104.096 619.361 103.979 619.627 103.746C619.894 103.512 620.027 103.186 620.027 102.766C620.027 102.426 619.931 102.162 619.737 101.976C619.544 101.782 619.207 101.646 618.727 101.566L617.947 101.436C617.607 101.376 617.321 101.286 617.087 101.166C616.861 101.046 616.677 100.902 616.537 100.736C616.397 100.562 616.297 100.376 616.237 100.176C616.177 99.9689 616.147 99.7556 616.147 99.5356C616.147 98.8956 616.357 98.4122 616.777 98.0856C617.197 97.7589 617.764 97.5956 618.477 97.5956C619.037 97.5956 619.507 97.6889 619.887 97.8756C620.274 98.0622 620.584 98.3256 620.817 98.6656L620.227 99.1756C620.034 98.9222 619.801 98.7156 619.527 98.5556C619.254 98.3956 618.901 98.3156 618.467 98.3156C617.994 98.3156 617.631 98.4156 617.377 98.6156C617.124 98.8156 616.997 99.1156 616.997 99.5156C616.997 99.8289 617.091 100.086 617.277 100.286C617.471 100.479 617.814 100.619 618.307 100.706L619.057 100.836C619.397 100.896 619.684 100.986 619.917 101.106C620.151 101.226 620.337 101.369 620.477 101.536C620.624 101.702 620.727 101.889 620.787 102.096C620.847 102.302 620.877 102.519 620.877 102.746C620.877 103.392 620.664 103.899 620.237 104.266C619.817 104.632 619.217 104.816 618.437 104.816ZM627.564 104.696L626.974 102.706H624.524L623.934 104.696H623.064L625.204 97.7156H626.314L628.454 104.696H627.564ZM625.794 98.6056H625.704L624.724 101.976H626.774L625.794 98.6056ZM634.49 103.766H634.44C634.38 103.899 634.31 104.029 634.23 104.156C634.15 104.282 634.046 104.396 633.92 104.496C633.8 104.589 633.653 104.666 633.48 104.726C633.306 104.786 633.1 104.816 632.86 104.816C632.1 104.816 631.533 104.509 631.16 103.896C630.793 103.282 630.61 102.402 630.61 101.256C630.61 100.056 630.813 99.1456 631.22 98.5256C631.626 97.9056 632.253 97.5956 633.1 97.5956C633.426 97.5956 633.71 97.6389 633.95 97.7256C634.19 97.8122 634.393 97.9289 634.56 98.0756C634.733 98.2222 634.876 98.3889 634.99 98.5756C635.103 98.7556 635.196 98.9389 635.27 99.1256L634.55 99.4556C634.49 99.3022 634.42 99.1589 634.34 99.0256C634.266 98.8856 634.173 98.7656 634.06 98.6656C633.953 98.5589 633.823 98.4756 633.67 98.4156C633.516 98.3556 633.336 98.3256 633.13 98.3256C632.57 98.3256 632.156 98.5356 631.89 98.9556C631.63 99.3756 631.5 99.9356 631.5 100.636V101.716C631.5 102.069 631.526 102.392 631.58 102.686C631.633 102.972 631.72 103.222 631.84 103.436C631.966 103.649 632.126 103.816 632.32 103.936C632.52 104.049 632.763 104.106 633.05 104.106C633.503 104.106 633.856 103.969 634.11 103.696C634.363 103.422 634.49 103.069 634.49 102.636V101.916H633.02V101.246H635.27V104.696H634.49V103.766ZM638.246 104.696V97.7156H642.546V98.4456H639.086V100.796H642.426V101.526H639.086V103.966H642.546V104.696H638.246ZM647.622 104.816C646.995 104.816 646.482 104.702 646.082 104.476C645.682 104.242 645.352 103.946 645.092 103.586L645.702 103.086C645.975 103.426 646.262 103.679 646.562 103.846C646.868 104.012 647.232 104.096 647.652 104.096C648.158 104.096 648.545 103.979 648.812 103.746C649.078 103.512 649.212 103.186 649.212 102.766C649.212 102.426 649.115 102.162 648.922 101.976C648.728 101.782 648.392 101.646 647.912 101.566L647.132 101.436C646.792 101.376 646.505 101.286 646.272 101.166C646.045 101.046 645.862 100.902 645.722 100.736C645.582 100.562 645.482 100.376 645.422 100.176C645.362 99.9689 645.332 99.7556 645.332 99.5356C645.332 98.8956 645.542 98.4122 645.962 98.0856C646.382 97.7589 646.948 97.5956 647.662 97.5956C648.222 97.5956 648.692 97.6889 649.072 97.8756C649.458 98.0622 649.768 98.3256 650.002 98.6656L649.412 99.1756C649.218 98.9222 648.985 98.7156 648.712 98.5556C648.438 98.3956 648.085 98.3156 647.652 98.3156C647.178 98.3156 646.815 98.4156 646.562 98.6156C646.308 98.8156 646.182 99.1156 646.182 99.5156C646.182 99.8289 646.275 100.086 646.462 100.286C646.655 100.479 646.998 100.619 647.492 100.706L648.242 100.836C648.582 100.896 648.868 100.986 649.102 101.106C649.335 101.226 649.522 101.369 649.662 101.536C649.808 101.702 649.912 101.889 649.972 102.096C650.032 102.302 650.062 102.519 650.062 102.746C650.062 103.392 649.848 103.899 649.422 104.266C649.002 104.632 648.402 104.816 647.622 104.816Z" fill="#525252"/>
</g>
<rect x="511" y="60.7529" width="24" height="24" rx="6" fill="white"/>
<rect x="511" y="60.7529" width="24" height="24" rx="6" stroke="#525252" stroke-width="0.5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M523.335 78.7529C526.649 78.7529 529.335 76.0666 529.335 72.7529C529.335 69.4392 526.649 66.7529 523.335 66.7529C520.021 66.7529 517.335 69.4392 517.335 72.7529C517.335 76.0666 520.021 78.7529 523.335 78.7529ZM526.115 71.7833C526.408 71.4904 526.408 71.0155 526.115 70.7226C525.822 70.4297 525.348 70.4297 525.055 70.7226L522.585 73.1923L521.615 72.2226C521.322 71.9297 520.848 71.9297 520.555 72.2226C520.262 72.5155 520.262 72.9904 520.555 73.2833L522.055 74.7833C522.348 75.0762 522.822 75.0762 523.115 74.7833L526.115 71.7833Z" fill="#525252"/>
<rect x="494.896" y="119.016" width="24" height="24" rx="6" fill="white"/>
<rect x="494.896" y="119.016" width="24" height="24" rx="6" stroke="#DFDFDF" stroke-width="0.5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M507.23 137.016C510.544 137.016 513.23 134.329 513.23 131.016C513.23 127.702 510.544 125.016 507.23 125.016C503.917 125.016 501.23 127.702 501.23 131.016C501.23 134.329 503.917 137.016 507.23 137.016ZM506.261 128.985C505.968 128.692 505.493 128.692 505.2 128.985C504.907 129.278 504.907 129.753 505.2 130.046L506.17 131.016L505.2 131.985C504.907 132.278 504.907 132.753 505.2 133.046C505.493 133.339 505.968 133.339 506.261 133.046L507.23 132.076L508.2 133.046C508.493 133.339 508.968 133.339 509.261 133.046C509.554 132.753 509.554 132.278 509.261 131.985L508.291 131.016L509.261 130.046C509.554 129.753 509.554 129.278 509.261 128.985C508.968 128.692 508.493 128.692 508.2 128.985L507.23 129.955L506.261 128.985Z" fill="#525252"/>
<circle cx="443.146" cy="60.3184" r="19.9058" fill="white" stroke="#B2B2B2"/>
<path d="M450.927 63.8134V55.2831C450.927 54.7803 450.716 54.2982 450.34 53.9427C449.963 53.5872 449.453 53.3875 448.92 53.3875H436.878C436.345 53.3875 435.835 53.5872 435.458 53.9427C435.082 54.2982 434.87 54.7803 434.87 55.2831V63.8134M450.927 63.8134H434.87M450.927 63.8134L452.212 66.2303C452.289 66.3753 452.326 66.5366 452.318 66.6988C452.311 66.8611 452.259 67.0187 452.169 67.1567C452.078 67.2946 451.951 67.4083 451.801 67.4867C451.65 67.5651 451.481 67.6057 451.309 67.6046H434.489C434.317 67.6057 434.148 67.5651 433.997 67.4867C433.847 67.4083 433.72 67.2946 433.629 67.1567C433.539 67.0187 433.487 66.8611 433.48 66.6988C433.472 66.5366 433.509 66.3753 433.586 66.2303L434.87 63.8134" stroke="#525252" stroke-width="0.669043" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_d_895_5178" x="580.104" y="86.6956" width="86" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_895_5178"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_895_5178" result="shape"/>
</filter>
<clipPath id="clip0_895_5178">
<rect width="764" height="219" fill="white" transform="translate(0 0.419922)"/>
</clipPath>
</defs>
</svg>
