
  <rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
      <title>'Postgres | Supabase Blog</title>
      <link>https://supabase.com/blog</link>
      <description>Latest Postgres news from <PERSON><PERSON><PERSON> at Supabase</description>
      <language>en</language>
      <lastBuildDate>Tue, 09 Jan 2024 00:00:00 -0700</lastBuildDate>
      <atom:link href="https://supabase.com/planetpg-filipe-rss.xml" rel="self" type="application/rss+xml"/>
      <item>
  <guid>https://supabase.com/blog/elixir-clustering-using-postgres</guid>
  <title>Elixir clustering using Postgres</title>
  <link>https://supabase.com/blog/elixir-clustering-using-postgres</link>
  <description>Learn about our approach to connecting multiple nodes in Elixir using Postgres</description>
  <pubDate><PERSON><PERSON>, 09 Jan 2024 00:00:00 -0700</pubDate>
</item>

    </channel>
  </rss>
