{"name": "www", "version": "0.0.3", "description": "Supabase main website", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "pnpm run content:build && next --port 3000", "build": "pnpm run content:build && next build", "export": "next export", "start": "next start", "lint": "next lint", "clean": "rimraf node_modules", "typecheck": "pnpm run content:build && tsc --noEmit", "content:build": "contentlayer2 build && node scripts/generateStaticContent.mjs", "prettier": "prettier --write \"./{pages,components,lib,stores,styles,tests}/**/*.{ts,tsx,md,js,jsx,json}\"", "postbuild": "node ./internals/generate-sitemap.mjs && ./../../scripts/upload-static-assets.sh"}, "dependencies": {"@code-hike/mdx": "^0.9.0", "@codesandbox/sandpack-react": "^2.20.0", "@hcaptcha/react-hcaptcha": "^1.11.1", "@heroicons/react": "^1.0.6", "@mdx-js/react": "^2.3.0", "@next/bundle-analyzer": "^14.2.3", "@next/mdx": "^14.2.3", "@octokit/auth-app": "^7.0.0", "@octokit/core": "^6.0.0", "@octokit/graphql": "^8.0.0", "@octokit/plugin-paginate-graphql": "^4.0.0", "@octokit/rest": "^21.0.0", "@radix-ui/react-dialog": "^1.0.5", "@supabase/supabase-js": "catalog:", "@vercel/og": "^0.6.2", "ai-commands": "workspace:*", "animejs": "^3.2.2", "class-variance-authority": "^0.7.1", "classnames": "^2.3.1", "clsx": "^1.2.1", "cobe": "^0.6.2", "common": "workspace:*", "common-tags": "^1.8.2", "config": "workspace:*", "contentlayer2": "0.5.3", "dayjs": "^1.11.12", "eslint-config-supabase": "workspace:*", "framer-motion": "^11.0.3", "globby": "^13.2.2", "gray-matter": "^4.0.3", "icons": "workspace:*", "lodash": "^4.0.0", "lucide-react": "*", "markdown-toc": "^1.2.0", "next": "catalog:", "next-contentlayer2": "0.5.3", "next-mdx-remote": "^4.4.1", "next-seo": "^6.5.0", "next-themes": "^0.3.0", "openai": "^4.20.1", "parse-numeric-range": "^1.3.0", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-countdown": "^2.3.5", "react-dom": "^18.2.0", "react-markdown": "^8.0.3", "react-syntax-highlighter": "^15.5.0", "react-transition-group": "^4.4.1", "react-use": "^17.4.0", "recharts": "^2.8.0", "rehype-slug": "^5.1.0", "remark": "^15.0.1", "remark-gfm": "^3.0.1", "remark-html": "^16.0.1", "shared-data": "workspace:*", "swiper": "^11.0.7", "three": "^0.170.0", "typed.js": "^2.0.16", "typescript": "~5.5.0", "ui": "workspace:*", "ui-patterns": "workspace:*", "use-debounce": "^7.0.1", "vanilla-tilt": "1.7.0", "yup": "^1.4.0"}, "devDependencies": {"@hookform/resolvers": "^3.1.1", "@types/animejs": "^3.1.12", "@types/classnames": "^2.3.1", "@types/common-tags": "^1.8.4", "@types/lodash": "^4.0.0", "@types/mdx-js__react": "^1.5.6", "@types/parse-numeric-range": "^0.0.1", "@types/react": "^18.2.24", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^18.2.8", "@types/react-syntax-highlighter": "^15.5.6", "@types/three": "^0.169.0", "@types/uuid": "^9.0.8", "api-types": "workspace:*", "autoprefixer": "^10.4.14", "postcss": "^8.4.31", "react-hook-form": "^7.45.0", "tsconfig": "workspace:*", "uuid": "^9.0.1", "zod": "^3.22.4"}, "license": "MIT"}