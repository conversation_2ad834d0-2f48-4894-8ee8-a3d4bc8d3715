---
title: 'Scale to Millions: Generative AI / Humata'
meta_title: 'Scale to Millions: Generative AI / Humata'
subtitle: 'Quickly and cost effectively build Generative AI applications that you can use to chat with your company’s documents.'
meta_description: 'Quickly and cost effectively build Generative AI applications that you can use to chat with your company’s documents.'
type: 'webinar'
onDemand: true
date: '2024-08-21T10:00:00.000-07:00'
timezone: 'America/Los_Angeles'
duration: '45 mins'
company:
  {
    name: '<PERSON><PERSON>',
    website_url: 'https://www.humata.ai/',
    logo: '/images/events/webinars/scale-to-millions-generative-ai-humata/humata-dark.svg',
    logo_light: '/images/events/webinars/scale-to-millions-generative-ai-humata/humata-light.svg',
  }
categories:
  - webinar
main_cta:
  {
    url: 'https://zoom.us/webinar/register/WN_cTcp6uv4S2OI63jDrOb1Ng',
    target: '_blank',
    label: 'Watch now',
  }
speakers: 'gregnr,khajvandi'
---

Is your organization still using search to find information stored in your business documents? Learn about building modern Generative AI applications that allow you to chat with your PDFs to provide customer support, summarize complex text, answer contract questions, or deliver business intelligence.

Over the past year, Supabase has helped several companies like Humata go from Generative AI concept to launching and scaling to over one million users. We’ll hear from Supabase AI engineer, Greg Richardson, how Supabase features like Vector and Edge Functions, combined with a one-of-a-kind developer experience, makes it easy to build Generative AI applications, while also delivering a cost effective solution on par or better than purpose-built vector database solutions.

Cyrus Khajvandi, co-founder & CEO of Google-backed [Humata](https://www.humata.ai/), will share how his company harnesses the power of large language models (LLMs), combined with Supabase, to help millions of experts quickly find the information they need, simply by asking their document a question in plain English (or 80 other languages).

In this session, we'll explore:

- The advantages of using a vector database to drive conversational queries with your business documents.
- Why Supabase’s unique set of features and developer experience make it possible to build highly scalable applications in a fraction of the time and cost required by traditional cloud providers.

Join us live to participate in the Q&A afterwards. Can’t make it to the event? Don’t worry, we'll send you the recording.
