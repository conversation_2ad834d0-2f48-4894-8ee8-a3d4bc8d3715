---
title: 'Moving from Fauna to Supabase, a Live Tutorial'
meta_title: 'Moving from Fauna to Supabase, a Live Tutorial'
subtitle: 'A live tutorial that shows you how to move your data and applications to Supabase from Fauna'
meta_description: 'A live tutorial that shows you how to move your data and applications to Supabase from Fauna'
type: 'webinar'
onDemand: true
date: '2025-04-08T09:00:00.000-07:00'
timezone: 'America/Los_Angeles'
duration: '45 mins'
company:
  {
    name: '<PERSON>',
    website_url: 'https://www.trybree.com',
    logo: '/images/events/webinars/fauna-to-supabase-migration/bree.svg',
    logo_light: '/images/events/webinars/fauna-to-supabase-migration/bree-light.svg',
  }
categories:
  - webinar
main_cta:
  {
    url: 'https://zoom.us/webinar/register/WN_26XTfO35SAC81jjS1ulWJw?amp_device_id=1bd3c92f-c97a-45ad-8aab-a508f950b536',
    target: '_blank',
    label: 'Register now',
  }
speakers: 'caruso,ryanxjhan'
---

## Keep Your Fauna Applications by Migrating to Supabase

The deadline to move off Fauna is approaching fast. In this 45-minute session, you'll learn how to move your data to Supabase, modify your FQL queries for SQL, and optimize your deployment. Bree has navigated this transition from Fauna to Supabase already and will be on hand to provide advice and best practices. This is a hands-on demonstration and will include plenty of time for Q&A.

### Agenda

- Concepts in Supabase and how they map to concepts in Fauna
- Using Postgres as a document store with JSONB
- Migrating your data
- Building queries using PostgREST and GraphQL
- Q&A

Join us live to participate in the Q&A afterwards. Can’t make it to the event? Don’t worry, we'll send you a link to the recording.
