---
title: 'Supabase Launch Week 12 Hackathon'
meta_title: 'Supabase Launch Week 12 Hackathon'
subtitle: 'Build an Open Source Project over 10 days. 5 prize categories.'
meta_description: 'Build an Open Source Project over 10 days. 5 prize categories.'
type: 'hackathon'
onDemand: false
date: '2024-09-13T09:00:00.000-07:00'
timezone: 'America/Los_Angeles'
duration: '10 days'
categories:
  - hackathon
main_cta:
  {
    url: 'https://hackathon.dev/lw12',
    target: '_blank',
    label: 'Submit project',
    disabled_label: 'Submissions closed',
  }
---

The hackathon starts on Friday, September 13th at 09:00 am PT and ends on Sunday, September 22nd at 11:59 pm PT. You could win an extremely limited edition Supabase swag and add your name to the Supabase Hackathon Hall of Fame.

For some inspiration, check out all the [winners from previous hackathons](https://supabase.com/blog/tags/hackathon).

This is the perfect excuse to "Build in a weekend, scale to millions.” Since you retain all the rights to your submissions, you can use the hackathon as a launch pad for your new Startup ideas, side projects, or indie hacks.

## Key Facts

- You have 10 days to build a new **Open Source** project using Supabase in some capacity
  - Starting 09:00 am PT Friday, September 13th 2024
  - The submission deadline is 11:59 pm Sunday midnight PT, September 22nd 2024
- Enter as an individual or as a team of up to 4 people
- Build whatever you want - a project, app, tool, library. Anything.
- 1-minute video containing the following:
  - Name of the project
  - Demonstration of the project
  - How Supabase is used within the project
- [Here is an example video](https://youtu.be/KaWJQzTTx5k). We do not assess the quality of the video itself. Remember to keep it concise.

## Prizes

There are 5 categories there will be prizes for:

- Best overall project (Swag kit)
- Best use of AI (Swag kit)
- Most fun / best easter egg (Swag kit)
- Most technically impressive (Swag kit)
- Most visually pleasing (Swag kit)

There will be a winner and a runner-up prize for each category. Every team member on winning/runner-up teams gets a Supabase Launch Week swag kit.

## Submission

You should submit your project using [this form](https://hackathon.dev/lw12) before 11:59 pm Sunday midnight PT, September 22nd, 2024.

## Judges

The Supabase team will judge the winners for each category.
We will be looking for:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
- FUN! 😃

## Rules

- Team size 1-4 (all team members on winning teams will receive a prize)
- You cannot be on multiple teams
- One submission per team
- It's not a requirement to use AI
- All design elements, code, etc., for your project must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- Include a link to a 1-minute demo video

## Resources

### Supabase resources to get you started

- [Real world RAG, the missing pieces for your AI application](https://www.youtube.com/watch?v=ibzlEQmgPPY)
- [Simplify complex SQL queries with Views](https://www.youtube.com/watch?v=IOYFS-2lFjU&t)
- [Row Level Security in Supabase](https://supabase.com/docs/guides/database/postgres/row-level-security)

### Community help

The Supabase Team will be taking part in the Hackathon and you'll find us live to build in our discord all week. Please join us by building in public:

- Text channel: [#hackathon](https://discord.gg/UYyweApy)
- Audio channel: [#hackathon](https://discord.gg/Vj3mTPwH)

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com/)

- [Previous Hackathon Prize Winners](https://supabase.com/blog/tags/hackathon)

![Discord](/images/blog/lw12/hackathon/discord.png)

## Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required.
- By making a submission, you grant Supabase permission to use screenshots, code snippets, and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
