import Layout from '../layouts/Layout'
import SectionContainer from '../components/Layouts/SectionContainer'

export const meta = {
  title: 'Support Policy',
  description: 'Supabase Support Policy',
}

<SectionContainer>

# Support Policy

Supabase is dedicated to providing an exceptional customer experience. As a crucial aspect of
this commitment, we offer limited technical support for all paid projects running on our hosted Supabase platform. Technical support is confined to the scope, hours, contacts, and channels below.

## Scope

Supabase's support offering is available only for the technologies supported by the Supabase Platform and is restricted to the following:

**Configuration Issues**

- Installation of extensions
- Best practices for auth configuration
- General questions about the supabase stack

**Troubleshooting**

- Providing workarounds or resolutions for known problems
- Answering general how-to questions and providing pointers to documentation
- Troubleshooting supported software packages (Realtime, Edge Functions, Webhooks, PostgREST, Auth) exhibiting erratic or faulty behavior on Supabase, independent of the user's application code

**Not Covered**

Supabase technical support services do not extend to the following areas:

- General debugging of user applications
- Rewriting application code for compatibility with Supabase
- Modifying and/or patching third-party or Open Source software packages for compatibility with Supabase

## Who can contact support?

Billing and account management support is available to all customers.

Limited technical support is accessible for Supabase customers utilizing paid Supabase resources and/or paid Supabase Add-Ons. Support requests will only be processed if:

- The request is made through one of our official support channels (see below)
- The request originates from a registered Supabase account email address
- The requester has Developer or higher access to any specific Supabase projects pertaining to the request.

## Official Support Channels

- Web and Dashboard: Support is available exclusively through the Supabase help feature in the dashboard or this website -[https://supabase.com/dashboard/support/new](https://supabase.com/dashboard/support/new)
- Email: If for some reason you are unable to access the dashboard or ticket from you can contact support <NAME_EMAIL> from a registered Supabase account email address

## Community Support

Supabase support staff will only address support requests received through the official channels mentioned earlier. However, community channels may exist for peer-to-peer support and discussions. This is support provided by volunteers that contribute to the Supabase community.

For questions related to debugging code, we recommend reaching out on **[GitHub Discussions](https://github.com/supabase/supabase/discussions)** or on **[Discord](https://discord.gg/rxTfewPvys)**. The community is made up of experienced developers who may be able to provide guidance and support with code-related issues.

To get the most helpful response from the community, providing precise and detailed information about your problem and any error messages you may be encountering is important. Be sure to **include any relevant code snippets explaining how to reproduce** the issue in your message.

Please note that Supabase team members may participate in community channels at their discretion, but there is no guarantee of response for support issues unless they are
submitted through one of the official channels above.

## Service Level Agreement

Service Level Agreements are only available to Enterprise customers. You can details of our SLA including support response times on the dedicated page for this topic -
[https://supabase.com/sla](https://supabase.com/sla)

## Proactive Monitoring

In the event of a platform issue, a notice will be posted on our platform status site at [status.supabase.com](https://status.supabase.com/) to promptly communicate the impact and status of any such issue. You do not need to submit a support ticket if there's an ongoing platform issue. Instead, monitor the status page which will always be kept up-to-date with the latest progress and information.

## Premium Support

Please contact us to find out about our Premium Support options available for Team Plan and Enterprise customers using the form below:

[https://forms.supabase.com/enterprise](https://forms.supabase.com/enterprise)

</SectionContainer>

export default (context) => <Layout meta={meta} children={context.children} context={context} />
