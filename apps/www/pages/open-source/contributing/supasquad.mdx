import Layout from '~/layouts/Layout'
import SectionContainer from '~/components/Layouts/SectionContainer'

export const meta = {
  title: 'SupaSquad | Supabase',
  description: 'Supabase Advocate program',
}

<SectionContainer>

# SupaSquad

The SupaSquad is an official Supabase advocate program where community members help build and manage the Supabase community.

- Official recognition in the Supabase community.
- Direct connection to the Supabase team.
- Help steer the Supabase community.

![Supabase SupaSquad](/images/supabase-squad.png)

## Requirements

As a member of the Squad, you choose the approach where you'll provide the most value.
You can help in one of five ways:

### Maintainer

Help maintain Supabase repositories. This includes building client libraries, managing issues, and fixing bugs.

### Expert

Answer user questions on GitHub [Discussions](https://github.com/supabase/supabase/discussions), [Discord](https://discord.supabase.com), and various other social platforms.

### Advocate

Spread the word on social channels and help to answer Supabase-related questions in the broader community and social channels.

### Builder

Build Supabase examples, blog about them, and add them to the [Supabase repo](https://github.com/supabase/supabase/tree/master/examples).

### Author

Write guest blog posts, create documentation, and help Supabase global expansion through translation.

### Moderator

Help us maintain the community guidelines in our GitHub and Community-led communities such as [Discord](https://discord.supabase.com), [Reddit](https://reddit.com/r/Supabase/),
[StackOverflow](https://stackoverflow.com/questions/tagged/supabase), etc.

## Benefits for SupaSquad members

- Access to a Supabase Discord channel providing direct communication with the team, Discord badges, and elevated privileges.
- Special AMA sessions with members of the Supabase team.
- Monthly DevRel committee call with industry-leading Developer Advocates (many of whom are [angel investors](https://supabase.com/blog/angels-of-supabase)), where you can learn from the best.
- We'll help you build your audience by promoting content via the Supabase social channels.
- Featured profile on Supabase website.
- Early access to new features (and the opportunity to provide feedback to the team!).
- Free credits that you can use for Squad efforts.
- Direct access to members of the Supabase team for questions, suggestions, etc.
- Help shape the future of the program.
- Exclusive Supabase Team swag drops are usually exclusively reserved for the Supabase core team.

## How to join

Apply to join the program using [this form](https://airtable.com/shr0FtLqLfhpuEya8).

## FAQs

<details>
  <summary>
    <i>Why are you only admitting 20 new members?</i>
  </summary>
  The entire Supabase team is only 20 people, so as you can imagine adding another 20 people sounds like
  a lot to us! We wish we could admit everyone who wanted to join. But we also want to make sure everyone
  who joins the Squad has an awesome experience. In the future we will probably expand the intake to
  include a monthly quota.
</details>
<details>
  <summary>
    <i>What is expected?</i>
  </summary>
  Mostly just enthusiasm. If you are interested in Open Source and want to get involved, the SupaSquad
  program is a great channel. You'll be given opportunities to contribute to the community in whatever
  ways match your skillset.
</details>
<details>
  <summary>
    <i>What if I become too busy to contribute?</i>
  </summary>
  No worries! The program isn't a job. It's just an opportunity to build your skillset and audience within
  the Supabase ecosystem.
</details>

</SectionContainer>

export default (context) => <Layout meta={meta} children={context.children} context={context} />
