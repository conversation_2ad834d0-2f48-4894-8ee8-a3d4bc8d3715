import {
  KeyIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  RewindIcon,
  CreditCardIcon,
  ClipboardCheckIcon,
} from '@heroicons/react/outline'
import { Activity, Lock } from 'lucide-react'
import Layout from '../layouts/Layout'

export const meta = {
  type: 'lp',
  title: 'Security at Supabase',
  description:
    'Supabase is trusted by thousands of developers for building and deploying secure applications.',
}

export const Section = ({ children, icon, img }) => (
  <div>
    {icon && (
      <div className="border shadow-background-surface-300 not-prose bg-surface-100 -mb-4 flex h-10 w-10 items-center justify-center rounded-full">
        <figure className="w-5 h-5 flex items-center justify-center">{icon}</figure>
      </div>
    )}
    {img && <div className="-mb-4 flex h-12 w-12 items-center justify-center">{img}</div>}
    {children}
  </div>
)

<div className="container mx-auto px-8 sm:px-16 xl:px-20 grid grid-cols-12 items-center my-8">
  <div className="col-span-12 lg:col-span-6">
    <h1 className="xl:text-5xl mb-4 [&_p]:m-0">Security at Supabase</h1>
    <h2 className="text-xl text-foreground-light max-w-xl m-0 [&_p]:m-0">
      Supabase is trusted by thousands of developers for building and deploying secure applications.
    </h2>
  </div>
  <div className="col-span-12 lg:col-span-5 lg:col-start-8 m-0 [&_p]:m-0">

![Supabase security](/images/security/security-hero.png)

  </div>
</div>

<div className="container mx-auto px-8 sm:px-16 xl:px-20 mb-16">

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-16">

<Section icon={<Lock strokeWidth={1}/>}>

### Multi-factor Authentication

Supabase allows users to enable Multi-factor authentication (MFA) on their account.
MFA adds an additional layer of security to your user account, by requiring a second factor to verify your user identity.

</Section>

<Section icon={<ShieldCheckIcon strokeWidth={1}/>}>

### SOC 2

Supabase is SOC 2 Type 2 compliant. This is an important security policy when [handling sensitive customer data](/docs/guides/security/soc-2-compliance).

Enterprise and Team customers can access our SOC 2 Type 2 report [on the dashboard](/dashboard/org/_/documents).

<div className="w-32">
  <img src="/images/security/soc2-type2.svg" />
</div>

</Section>

<Section icon={<Activity strokeWidth={1}/>}>

### HIPAA

Supabase is HIPAA compliant. You can store Protected Health Information (PHI) on our hosted platform once you enter into a Business Associate Agreement (BAA) with us and fulfill your HIPAA obligations under our [shared responsibility model](/docs/guides/platform/shared-responsibility-model#managing-healthcare-data).

Enterprise and Team customers can request to sign our BAA [on the dashboard](/dashboard/org/_/documents).

<div className="w-32">
  <img src="/images/security/HIPAA.svg" />
</div>

</Section>
<Section icon={<KeyIcon strokeWidth={1}/>}>

### Data Encryption

All customer data is encrypted at rest with AES-256 and in transit via TLS.

Sensitive information like access tokens and keys are encrypted at the application level before they are stored in the database.

</Section>

<Section icon={<UserGroupIcon strokeWidth={1}/>}>

### Role-based access control

Members of organizations in Supabase can be granted access to specific resources.

Read more about [fine grained access controls](/docs/guides/platform/access-control) including Read-Only and Billing-Only access.

</Section>

<Section icon={<RewindIcon strokeWidth={1}/>}>

### Backups

All paid customer databases are backed up every day.

Point in Time Recovery allows restoring the database to any point in time. Customers from the Pro Plan have access to this feature as an add-on.

</Section>

<Section icon={<CreditCardIcon strokeWidth={1}/>}>

### Payment processing

Supabase uses [Stripe](https://stripe.com) to process payments and does not store personal credit card information for any of our customers.

Stripe is a certified PCI Service Provider Level 1, which is the highest level of certification in the payments industry.

</Section>

<Section icon={<ClipboardCheckIcon strokeWidth={1}/>}>

### Vulnerability Management

Supabase works with industry experts to conduct regular penetration tests.

In addition to internal security reviews, we use various tools to scan our code for vulnerabilities including [GitHub](https://github.com), [Vanta](https://www.vanta.com/), and [Snyk](https://snyk.io/).

</Section>

<Section icon={<ShieldCheckIcon strokeWidth={1}/>}>

### DDoS Protection

Supabase combats Distributed Denial of Service attacks in several ways to mitigate resource abuse and prevent runaway bills.

In addition to protection at the CDN level via Cloudflare, we employ [fail2ban](https://github.com/fail2ban/fail2ban) to prevent brute force logins. Users can [customize rate limits](/docs/guides/platform/going-into-prod#rate-limiting-resource-allocation--abuse-prevention) for critical API routes and set [spend caps](/docs/guides/platform/cost-control#spend-cap) to prevent surprise bills.

</Section>

</div>

</div>

export default (context) => <Layout meta={meta} children={context.children} context={context} />
