module.exports = [
  {
    protocol: 'https',
    hostname: 'api.producthunt.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'avatars.githubusercontent.com',
    port: '',
    pathname: '/u/*',
  },
  {
    protocol: 'https',
    hostname: 'ca.slack-edge.com',
    port: '',
    pathname: '/*',
  },
  {
    protocol: 'https',
    hostname: 'colab.research.google.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'github.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 's3-us-west-2.amazonaws.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'img.youtube.com',
    port: '',
    pathname: '/vi/*',
  },
  {
    protocol: 'https',
    hostname: 'images.unsplash.com',
    port: '',
    pathname: '/photo-*',
  },
  {
    protocol: 'https',
    hostname: 'vercel.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'obuldanrptloktxcffvn.supabase.co',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'xguihxuzqibwxjnimxev.supabase.co',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'pbs.twimg.com',
    port: '',
    pathname: '/profile_images/**',
  },
  {
    protocol: 'https',
    hostname: 'res.cloudinary.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'supabase.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'user-images.githubusercontent.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'media.licdn.com',
    port: '',
    pathname: '/dms/image/**',
  },
]
