---
name: Xendit
title: Xendit use Supabase and create a full solution shipped to production in less than one week.
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
# meta_title:
description: As a payment processor, Xendit are responsible for verifying that all transactions are legal.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: As a payment processor, Xendit are responsible for verifying that all transactions are legal.
author: rory_wilding
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/xendit.png
logo_inverse: /images/customers/logos/light/xendit.png
og_image: /images/customers/og/xendit.jpg
tags:
  - supabase
date: '2023-02-14'
company_url: https://xendit.co
stats:
  [
    { stat: '120', label: Staff count },
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
  ]
misc: [{ label: 'Founded', text: Jakarta Selatan }]
about: Xendit is a financial technology company that provides payment solutions and simplifies the payment process for businesses in Indonesia, the Philippines, and Southeast Asia, from SMEs and e-commerce startups to large enterprises.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['fintech']
# "startup" | "enterprise" | "indie_dev"
company_size: 'enterprise'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Asia'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database']
---

## Challenge

As a payment processor, Xendit are responsible for verifying that all transactions are legal. Any transactions which are suspicions must be verified against a strict set of criteria, and the parties involved need to be checked against international sanctions lists. This is a critical anti-money-laundering operation and needs to be performed in realtime to prevent any delays on legitimate payments.

## Why they chose Supabase

Xendit needed something fast. Something that was cheaper than using the global players like Worldcheck or Refinitiv. Xendit already uses Postgres for a lot of their critical infrastructure, and so Xendit team are familiar with the technology and comfortable in it's ability to scale.

## What they built

Xendit parses international sanctions lists from the UN and the Indonesian government and loads them into Supabase. Since Supabase provides a full Postgres server, they can then use the [Trigram](https://www.postgresql.org/docs/current/pgtrgm.html) extension to perform full-text search on the lists, with a relevance score on every search.

Supabase was perfect for their use case, as they needed something built fast. The full solution was built and in production in less than one week.

> The full solution was built and in production in less than one week.

Xendit created a database function for searching, which they are able to call directly using their Python clients. They have plans to iterate on the current implementation using more advanced techniques, like machine learning, but for now the Supabase system has been in Production for 9 months without a problem.
