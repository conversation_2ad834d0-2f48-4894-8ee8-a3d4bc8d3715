---
title: <PERSON>p<PERSON><PERSON> and Supabase - GDPR-compliant AI chatbots for docs and websites.
meta_title: GDPR-compliant AI chatbots for docs and websites.
name: Markprompt
description: AI-powered chatbot platform, Markprompt, empowers developers to deliver efficient and GDPR-compliant prompt experiences on top of their content, by leveraging Supabase's secure and privacy-focused vector database and authentication solutions.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/markprompt.png
logo_inverse: /images/customers/logos/light/markprompt.png
og_image: /images/customers/og/markprompt.png
tags:
  - supabase
date: '2023-05-17'
company_url: 'https://markprompt.com/'
stats:
  [
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
  ]
misc: [{ label: 'Built by', text: 'The Motif Team' }]
about: Enterprise-grade AI chatbots for your website and docs.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['ai', 'saas', 'developer-tools']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Europe'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'vector']
---

Markprompt is an AI-powered chatbot platform that simplifies the process of finding relevant information in large-scale knowledge bases. With its chat-based interface, users can ask questions in natural language and receive instant, accurate answers. Markprompt's advanced analytics help teams identify knowledge gaps and improve their documentation strategies.

<Quote img="michael-fester.jpeg" caption="Michael Fester - Co-Founder, Markprompt">
  We've found that having access to the full features of Postgres, colocated with the embeddings,
  makes it the perfect vector database.
</Quote>

## The Challenge

When developing the platform, Markprompt considered GDPR compliance crucial as it enabled them to handle user data responsibly, maintain data privacy, and foster trust and confidence among their customers. As a result, the choice of a robust vector database and authentication solution became vital in ensuring adherence to GDPR requirements.

Markprompt has successfully indexed over half a million sections of content, with a steady influx of 10,000 - 50,000 new sections daily. This emphasizes the significance of [Supabase Vector](https://supabase.com/vector) in terms of reliability and scalability.

<Quote img="michael-fester.jpeg" caption="Michael Fester - Co-Founder, Markprompt">
  We decided to use Supabase over other specialized vector databases because it enabled us to be
  GDPR compliant from day one with little effort.
</Quote>

## Why they chose Supabase

By utilizing a trusted solution like Supabase for their vector database and authentication needs, Markprompt can implement access controls, user consent management, and audit trails, which are essential elements for GDPR compliance. This ensures that personal data is handled with care, protected from unauthorized access, and enables Markprompt to meet the strict data protection standards set forth by the GDPR.

Markprompt were also impressed with the ease of use and scalability of Supabase. The platform allowed them to focus on building their product, knowing that they had a powerful and reliable database solution backing them up.

<Quote img="marie-schneegans.jpeg" caption="Marie Schneegans - Co-Founder, Markprompt">
  Supabase has been a joy to work with. Handling enterprise data at scale can be daunting, but we
  feel we are in good hands as we grow.
</Quote>

## What They Built

Markprompt is a set of API endpoints that allow you to train your content and create a prompt to ask questions to it, for instance for a docs site. Markprompt also designed a user-friendly web dashboard that simplifies content training, synchronization with GitHub repos or websites, access key management, and provides insights into user query statistics. Furthermore, Markprompt's UI components, available as React and Web Components, allow the integration of prompts at all touch points of a company, from public websites to internal knowledge bases with just a few lines of code.

Like Supabase, Markprompt is an open-source platform, allowing customers the freedom to host the dashboard and model backend on their own premises. Markprompt warmly welcomes contributions from the community, fostering collaboration and continuous improvement of the platform to meet the evolving needs of developers and technical users.

<Quote img="michael-fester.jpeg" caption="Michael Fester - Co-Founder, Markprompt">
  Building everything on Supabase from the Auth, to DB, to the vectors—this integrated experience
  really made it for us.
</Quote>

## The Results

Choosing Supabase as their vector database and authentication solution empowered Markprompt to focus on developing innovative AI-powered solutions without compromising data security or impeding scalability. The seamless integration between Supabase and pgvector enabled them to meet the increasing demands of their expanding user base while upholding stringent GDPR requirements. Consequently, companies gain trust and confidence in the platform, solidifying its reputation as a robust enterprise offering.

To learn more about how Supabase Vector can help you store vector embeddings at scale and build GDPR-compliant apps, [reach out to us](https://forms.supabase.com/enterprise).

## Tech stack

- Next.js
- Vercel
- Typescript
- Tailwind
- Upstash
- Supabase
- Stripe
- Plain
- Fathom
