---
name: Humata
title: 'Humata Scales with Supabase: Achieving 4X Cost Savings and Enhanced Performance'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: Humata Scales with Supabase - Achieving 4X Cost Savings and Enhanced Performance
description: By partnering with Supabase, Humata achieved a 4X reduction in vector database costs, streamlined their development, and consolidated their data so they could scale seamlessly.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: By partnering with Supabase, Humata achieved a 4X reduction in vector database costs, streamlined their development, and consolidated their data so they could scale seamlessly.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/humata.png
logo_inverse: /images/customers/logos/light/humata.png
og_image: /images/customers/og/humata.jpg
tags:
  - supabase
date: '2024-10-01'
company_url: 'https://www.humata.ai'
stats: []
misc:
  [
    { label: 'Use case', text: 'AI' },
    {
      label: 'Solutions',
      text: 'Supabase Database, Supabase Auth, Supabase Realtime, Supabase Vector',
    },
  ]
about: Use AI to ask questions across all of your files.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['ai', 'saas', 'analytics']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'realtime', 'vector']
---

## Introduction

Humata, a platform enabling millions of users to chat with and analyse their all documents, relies on cutting-edge technology to support rapid growth and high user demand. Humata started as PDF AI that allows anyone to chat with their files, initially geared for scientific researchers. Humata has now expanded to serve major enterprises and government institutions by connecting their knowledge base to AI and allowing them to get answers they can trust with best-in-class referencing. With a tech stack that includes Supabase, GCP, OpenAI, Vercel, and NEXT.js, Humata's platform provides real-time, mission-critical analysis for researchers, enterprises, and government institutions. By partnering with Supabase, Humata achieved a 4X reduction in vector database costs, streamlined their development, and consolidated their data so they could scale seamlessly.

## The Challenge

As Humata's user base expanded, the need for scalable, reliable, and cost-effective infrastructure became critical. Initially, Humata used Pinecone for its vector database workloads for chat with doc solution, but managing this infrastructure became complex and expensive, with 20 s2.x8 pods and additional replicas required for latency. Humata needed a solution that would simplify infrastructure management, reduce costs, and enhance the platform's feature velocity without sacrificing performance.

## The Supabase Solution

Humata chose Supabase for its Postgres database, authentication, and real-time functionality. Supabase's open-source community and world-class support made it the perfect choice to meet Humata's growing needs. The flexibility of Supabase allowed Humata to consolidate all their data—including semantic searches and business data—into a single database, significantly improving query complexity and feature velocity. Backed by Supabase's Enterprise plan, Humata gained access to deep technical specialists to optimise the back-end performance ongoing, and leverages this pool of experts to de-risk their critical data infrastructure operations.

## Migration from Pinecone to Supabase

The migration from Pinecone to Supabase was a game-changer for Humata's chat with PDF technology. By switching to a single 16XL instance of Supabase's pgVector, Humata achieved a staggering 4X cost reduction. This transition also simplified infrastructure management, eliminated the need for multiple replicas, and boosted feature velocity by enabling complex queries that combine semantic search with business data.

## Key Benefits of Using Supabase

- **Cost Efficiency**: The move from Pinecone to Supabase resulted in a 75% reduction in database costs, allowing Humata to reallocate resources to further innovation.
- **Scalability**: Supabase's infrastructure scaled effortlessly, supporting Humata's growth to millions of users worldwide.
- **Developer Speed**: Supabase's ease of use and built-in features, such as row-level security and the frontend client, enabled faster development and more efficient workflows.
- **Seamless Real-Time Collaboration**: Supabase's Real Time feature enhanced collaboration, allowing users to see document changes and updates instantly.
- **Security & Compliance**: With Supabase's SOC 2 Type II and HIPAA-compliant infrastructure, Humata ensures the highest levels of security, protecting sensitive data as they approach HIPAA certification.

## Conclusion

Supabase has played an essential role in helping Humata scale globally, save significant infrastructure costs, and accelerate the delivery of new features. By consolidating their core backend components into Supabase, Humata now operates a more streamlined, cost-efficient platform that continues to support millions of users around the world.

To learn more about how Supabas Vector can help you store vector embeddings at scale and build AI apps with ease, [reach out to us](https://forms.supabase.com/enterprise).
