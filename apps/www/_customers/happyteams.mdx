---
name: HappyTeams
title: HappyTeams unlocks better performance and reduces cost with Supabase.
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: HappyTeams unlocks better performance and reduces cost with Supabase.
description: How a bootstrapped startup migrated from Heroku to Supabase in 30 minutes and never looked back.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: How a bootstrapped startup migrated from Heroku to Supabase in 30 minutes and never looked back.
author: rory_wilding
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/happyteams.png
logo_inverse: /images/customers/logos/light/happyteams.png
og_image: /images/customers/og/happyteams.jpg
tags:
  - supabase
date: '2023-02-16'
company_url: 'https://happyteams.io/'
stats:
  [
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
  ]
misc: [{ label: 'Founded', text: 'Dublin, Ireland' }]
about: HappyTeams is a purpose-built solution that empowers management to unlock valuable team insights.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'analytics']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Europe'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth']
---

## The Challenge

> Finding a painless and hands-free database experience to support growth

HappyTeams is a growing startup with an ambitious vision. The company is bootstrapped, so technical resources are scarce. Devs need to do more with less. This means staying laser-focused on delivering features, rather than database timeouts or performance issues.

The company launched its app on Heroku Postgres. But one year in, they were experiencing scaling issues. They needed to handle thousands of users while also shipping new features and building out integrations. Issues with database slowness and concurrent connections were holding them back.

Michael Dever, founder and CTO of HappyTeams, realized it was time for a change. A large overhaul of the app in early 2022 presented the opportunity to do so: "When you're in that first sprint of building a product, you're kind of locked with that first database decision you make. When we did a rebuild, it was time to look at something like Supabase."

![HappyTeams Home Page](/images/blog/2023-02-16-case-study-happyteams/happyteams-home-page.png)

## Why Supabase?

There were four main factors that led HappyTeams to switch from Heroku Postgres to Supabase:

1.  **Better performance**. The team was experiencing uneven performance with Heroku, which was sluggish at times. Supabase was consistently fast.

2.  **Enabling a large number of direct connections and connection pooling with PgBouncer**. HappyTeams was constantly hitting limits on the number of database connections. They could solve this problem with Supabase's [flexible plans](https://supabase.com/pricing) and [support for PgBouncer](https://supabase.com/blog/supabase-pgbouncer).

3.  **Quality of support, documentation, and onboarding**: While Heroku provided no support at the tier they were on, the Supabase team was readily available: "Responses were powerful and they answered all my questions, even on a Sunday. That just further sold the product for me." He was also impressed with the [documentation](https://supabase.com/docs) on the Supabase website and the onboarding process.

4.  **Lower costs**. Moving to Supabase reduced HappyTeams's costs: The base plan comes with everything they need to scale the product.

<Quote img="michael-dever-happyteams.jpeg" caption="Michael Dever - CTO of HappyTeams">
  Great documentation, great product, great pricing. It sells itself, really.
</Quote>

## Supabase provides a fast and reliable production database

After a 30-minute migration, HappyTeams can iterate quickly, add new features, and improve their product based on customer feedback

Once they made the decision, migrating from Heroku to Supabase was simple. HappyTeams completed their database migration in less than an hour using pg_dump and pg_restore.

Performance is no longer an issue. "Supabase just worked", says Michael. Pooling connections using PgBouncer was effortless - solving a recurring pain point for HappyTeams.

"Supabase is so fast. Way, way, way faster than our Heroku Postgres database, which I think was located in the same region."

Today, Supabase is an integral part of HappyTeams's tech stack. It has allowed the company to move fast and rapidly release new features - with minimal tech resources devoted to their backend infrastructure.

Thanks to Supabase's ease of use and reliability, HappyTeams can iterate quickly based on feature requests and deliver more value to their user base. They are planning to expand their usage, exploring functionality such as [Edge Functions](https://supabase.com/edge-functions) and the [API client](https://supabase.com/docs/guides/api).

<Quote img="michael-dever-happyteams.jpeg" caption="Michael Dever - CTO of HappyTeams">
  When we needed to add a comments feature to our app, we could get it done in just two days.
  Supabase is really like a dream come true for a developer.
</Quote>

Supabase is one of the best free alternatives to Heroku Postgres. Migrate your Heroku Postgres database to Supabase with this [guide](https://supabase.com/docs/guides/resources/migrating-to-supabase/heroku).
