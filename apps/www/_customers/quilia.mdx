---
name: Quilia
title: '<PERSON>uilia Saves 75% in Development Time with Supabase’s Data API and RLS Features'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
# meta_title:
description: Discover how Quilia leveraged Supabase’s built-in Data API and Row Level Security to accelerate development, enhance data protection, and halve infrastructure costs.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Migrating to Supabase resulted in a 75% reduction in development time, 50% lower costs, and enhanced security for sensitive client data.
author: prashant
author_title: <PERSON><PERSON><PERSON>
author_url: https://github.com/CoolAssPuppy
author_image_url: https://avatars.githubusercontent.com/u/914007?v=4
logo: /images/customers/logos/quilia.png
logo_inverse: /images/customers/logos/light/quilia.png
og_image: /images/customers/og/quilia.jpg
tags:
  - supabase
date: '2025-03-08'
company_url: https://www.quilia.com/
stats: [{ stat: '00,000', label: Example stat }]
misc: [{ label: 'Founded', text: 'Las Vegas, NV, USA' }]
# misc: [{ label: 'Founded', text: 'San Francisco, CA, USA' }]
about: Quilia is the Client App for Attorneys that ensures clients never miss an appointment.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'legal']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'functions']
---

Quilia is a startup that empowers personal injury clients to take control of their case data, streamlining the process of aggregating and sharing critical information with their attorneys. Our primary product is an app that allows clients to securely store and manage various aspects of their case, including treatment details, accident information, vehicle damage, wage loss, and more.

By providing a centralized platform for clients to input and access their case data, Quilia simplifies the information gathering process for attorneys, enabling them to build stronger cases and achieve better outcomes for their clients.

**The Challenge**

Prior to discovering Supabase, Quilia's infrastructure was built on Render and multiple servers with complex, hard-to-manage scripting. We had also developed a custom API that required significant maintenance. This setup made it challenging for most of our team to make changes to the site, often having to wait for API updates before completing otherwise simple tasks.

While the monetary cost was around $100 per month, we were spending a considerable amount of time on tasks that are now straightforward with Supabase. Our primary goal was to increase our release speed by improving accessibility for our team and reducing the time spent on infrastructure management.

Additionally, as a startup handling sensitive client data, we needed a solution that could provide robust security features and easy scalability to accommodate our growing user base.

<Img src="/images/customers/quilia/quilia-app.jpg" alt="Quilia iPhone app" zoomable />

**Why Supabase?**

Our new CTO, Lucho Suárez, introduced us to Supabase, and we were immediately impressed by its native API. As we delved deeper into Supabase's features, we realized it could solve many of our challenges.

Supabase's user-friendly interface and comprehensive documentation made it easy for our team to get started. The built-in authentication system and row-level security features were critical for ensuring the security of our clients' data. Additionally, the potential for future AI integration aligned with our long-term vision for the product.

<Quote img="lucho-suarez.jpg" caption="Lucho Suárez, CTO & Co-Founder">

We chose Supabase because it offered a complete package - a powerful database, easy-to-use API, and robust security features. It was the perfect fit for our needs.

</Quote>

**The Migration**

The migration to Supabase was smooth, thanks to the support from the Supabase team. We were able to quickly transfer our data and set up our new infrastructure. The process was well-documented, and whenever we had questions, the Supabase team was responsive and helpful.

<Quote img="kenny-eliason.jpg" caption="Kenny Eliason, CEO & Tech Co-Founder">

The migration to Supabase was one of the smoothest I've experienced. The documentation was thorough, and the support from the Supabase team was top-notch.

</Quote>

**The Results**

Since switching to Supabase, we've seen significant improvements in our app's performance and our development process:

1. **Improved Performance**: We noticed instant speed improvements in our app after migrating to Supabase. Page load times decreased, and the app felt snappier overall.
2. **Reduced Costs**: Our infrastructure costs were cut in half, going from $100 per month to around $50 per month with Supabase.
3. **Faster Development**: Our development and deployment time has been reduced by an impressive 75%. Supabase's user-friendly dashboard and straightforward API have made it much easier for our team to make changes and ship new features.
4. **Enhanced Security**: With Supabase's built-in authentication and row-level security features, we have peace of mind knowing that our clients' sensitive data is secure.
5. **Easier Onboarding**: The intuitive Supabase dashboard has made it much simpler to onboard new developers. They can quickly understand our database structure and start contributing to the codebase.

<Quote img="dean-m-tingey.avif" caption="Dean Tingey, CFO & Co-Founder">

Supabase has been a game-changer for us. It's reduced our costs, improved our app's performance, and allowed us to ship features faster than ever before.

</Quote>

**Future Plans**

Moving forward, we're excited to continue leveraging Supabase's features to enhance our product. We're particularly interested in exploring the AI tools for writing SQL queries and row-level security policies. As our app grows, we also plan to implement column-level security to further enhance our data protection.

Furthermore, as we scale our user base, we're confident that Supabase will be able to handle our growing needs. The ease of scaling with Supabase ensures that we can focus on building our core product without worrying about infrastructure limitations.

**Recommendation**

We wholeheartedly recommend Supabase to any organization looking for a comprehensive, user-friendly database solution. It's been instrumental in helping us scale our app, improve our development process, and secure our clients' data.

Supabase's excellent performance, responsive support team, and intuitive dashboard make database management a breeze. The pricing is transparent and affordable, making it an ideal choice for startups and small businesses.

In summary, Supabase is cheaper, easier to use, and simply works. It's allowed us to focus on what matters most - building a great product for our users.

**Tech Stack**

- Native Apps: React Native, Expo, TypeScript, Tamagui
- Database: Supabase.com
- API: Built-in API via Supabase
- Web Portal: NextJS, Tailwind, Sass, Radix-UI, react-hook-form, Zod
- Marketing Website: Gatsby, React, Tailwind CSS, WordPress
