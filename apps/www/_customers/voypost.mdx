---
name: Voypost
title: 'Voypost uses Supabase’s strong relational model to overcome NoSQL challenges'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: 'Voypost uses Supabase’s strong relational model to overcome NoSQL challenges'
description: 'Learn how Voypost leveraged Supabase to avoid NoSQL workarounds, enhance scalability, and deliver a superior user experience for Inkly, an intelligent contract negotiation platform.'
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: 'Learn how Voypost leveraged Supabase to avoid NoSQL workarounds, enhance scalability, and deliver a superior user experience for Inkly, an intelligent contract negotiation platform.'
author: micha<PERSON>_ridley
author_title: Proudct Marketing
logo: /images/customers/logos/voypost.png
logo_inverse: /images/customers/logos/light/voypost.png
og_image: /images/customers/og/voypost_logo_dark.png
tags:
  - supabase
date: '2024-06-04'
company_url: 'https://www.voypost.com/'
stats: []
misc:
  [
    { label: 'Use case', text: 'Contract negotiation platform, SaaS' },
    { label: 'Solutions', text: 'Database' },
  ]
about: 'Voypost, a leading software development company, excels in helping startups and small to medium-sized businesses build scalable and high quality applications. One of their standout projects using Supabase is Inkly, an intelligent contract negotiation platform.'
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'developer-tools']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Europe'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database']
---

## The challenge

Initially the Voypost team used Firebase to develop Inkly, an intelligent contract
negotiation platform, for a client. As the product evolved, they encountered increasing
complexity due to the relational nature of their data. They had to develop numerous
NoSQL workarounds, making data management cumbersome and limiting their ability to efficiently
implement new features. The need for a more suitable database solution became evident as they
sought to maintain high performance and scalability without the limitations imposed by
Firebase’s NoSQL structure.

## Why Supabase?

Voypost determined Supabase was the ideal solution for several reasons. Supabase offered the
benefits of a relational database with the ease of use and scalability required by Voypost. The
open-source nature of Supabase provided Voypost greater customization and control, ensuring
they were not locked into a proprietary system. Additionally, the ability to run Supabase on
their own infrastructure, if needed, provided a significant advantage in flexibility. The
familiar SQL querying capabilities and the robust support for real-time updates and serverless
functions made Supabase a compelling choice over Firebase.

## The Solution

Transitioning from Firebase to Supabase involved some manual work due to legacy code, but the
process was relatively smooth and the Voypost finished the core migration in under six months.
Supabase’s relational PostgreSQL database enabled Voypost to manage data more effectively and
perform complex queries without the constraints of Firestore. Supabase’s serverless architecture
enabled rapid deployment and scalability, while its open-source framework provided the freedom
to customize and extend the platform as needed.

## Results

With Supabase, Voypost achieved significant improvements in data management and query flexibility.
The shift to a relational database enabled them to scale the project more efficiently and manage
data with greater flexibility.They were able to deliver enhanced user experiences and introduce
new features quickly. The responsive support from Supabase contributed to a smoother development
journey. Supabase’s open-source nature, coupled with its robust performance and ease of use,
provided Voypost with the tools they needed to build a sophisticated, intelligent contract
negotiation platform. This successful implementation highlights how Supabase can help businesses
overcome data management challenges and achieve their development goals.

**Reduced codebase**\
Voypost reduced their codebase by 25% after eliminating NoSQL workarounds.

**Less headcount**\
Supabase made it possible for Voypost to develop the app with a small team as they did not need
to think about the infrastructure.

**Accelerated development**\
Voypost used Supabase’s dashboard to accelerate development and testing.

**Faster iteration**\
Supabase provided the ability to quickly make changes and deploy new releases. Voypost responded
quickly to client feedback and iterated quickly on client requests.

**Faster time to value and lower cost**\
As a result of using the Supabase platform, Voypost enjoyed a 20% faster development process
compared to their traditional development approach.

> To learn more about how Supabase can help you manage and scale your database, **[reach out to us](https://forms.supabase.com/enterprise)**.
