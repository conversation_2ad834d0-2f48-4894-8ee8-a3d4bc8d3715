---
name: Maergo
title: "Maergo's Express Delivery: How Supabase Helped Achieve Scalability, Speed, and Cost Saving"
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: "Maergo's Express Delivery - How Supabase Helped Achieve Scalability, Speed, and Cost Saving"
description: Discover how Maergo, a nationwide expedited parcel delivery service, reduced its codebase by 90%, decreased deployment times to just seconds, and achieved unprecedented scalability with Supabase.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: 'Discover how Maergo, a nationwide expedited parcel delivery service, reduced its codebase by 90%, decreased deployment times to just seconds, and achieved unprecedented scalability with Supabase.'
author: micha<PERSON>_ridley
author_title: Product Marketing
logo: /images/customers/logos/maergo.png
logo_inverse: /images/customers/logos/light/maergo.png
og_image: /images/customers/og/maergo.jpg
tags:
  - supabase
date: '2024-06-12'
stats: []
misc:
  [{ label: 'Use case', text: 'Logistics, SaaS' }, { label: 'Solutions', text: 'Database, Auth' }]
about: 'Maergo, a nationwide expedited parcel delivery service, chose Supabase to overcome its technology challenges. With Supabase, Maergo was able to reduce its codebase by 90%, decrease deployment times from 12-15 minutes to just seconds, and achieve unprecedented scalability during peak season. Discover how Supabase helped Maergo achieve faster time-to-market, improved performance, and significant cost savings.'
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['ecommerce', 'saas']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth']
---

## Introduction

Maergo, a nationwide expedited parcel delivery service, specializes in small parcels and e-commerce solutions. The company aims to provide fast and reliable shipping experiences for its customers while reducing costs and increasing scalability.

## The Problem

Maergo faced several challenges with their initial technology stack. The complex schema evolution in MongoDB led to data integration issues. Maergo found it difficult to iterate and deploy as quickly as they wanted due to slow deployment times of 12-15 minutes and scaling problems with AWS Elastic Beanstalk. Maergo had to write all of the middleware code for their MongoDB solution which led to a very large codebase at over 730 thousand lines of code (LOC). This code complexity made it difficult for non-developers to contribute.

## Why Supabase?

Maergo chose Supabase due to its lightweight and scalable architecture, which addressed their specific needs:

- The PostgREST interface provided a great CRUD experience, reducing the need for custom middleware
- Edge Functions enabled the development of business logic without worrying about deployment or scaling
- Supabase Auth was integrated seamlessly with row-level security

## The Results

Maergo's successful migration to Supabase enabled them to reduce costs, increase scalability, and improve deployment times. The company can now focus on building innovative solutions for its customers while leveraging Supabase's robust features and cost-effective model.

**Reduced codebase**\
Maergo's codebase went from 730k lines of code to 95k lines of code, while preserving all API components of the previous system and even introducing new functionality with the help of Supabase.

**Much faster deployment speed**\
Deployment times decreased from 12-15 minutes to a few seconds.

**Improved scalability**\
Maergo successfully handled 100x their highest sustained traffic with no problems on the database during the last load test.

**Simplified pricing and cost savings**\
Supabase's cost model was more appealing, allowing Maergo to scrap their previous infrastructure of MongoDB and Elastic Beanstalk and their initially planned refactor target architecture, including RDS Postgres and AWS EKS.

**Increased availability**\
Maergo finished their platform migration before peak season, achieving the highest availability in company history.

> To learn more about how Supabase can help you manage and scale your database, **[reach out to us](https://forms.supabase.com/enterprise)**.
