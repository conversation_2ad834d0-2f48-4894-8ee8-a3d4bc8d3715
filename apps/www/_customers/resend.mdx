---
name: Resend
title: "<PERSON>sen<PERSON>'s Journey with Supabase: Scaling Email Infrastructure with Ease"
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
# meta_title:
description: Scaling seamlessly to 5,000+ paying customers & millions of emails sent daily with Supabase
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Scaling seamlessly to 5,000+ paying customers & millions of emails sent daily with Supabase
author: prashant
author_title: <PERSON><PERSON><PERSON>
author_url: https://github.com/CoolAssPuppy
author_image_url: https://avatars.githubusercontent.com/u/914007?v=4
logo: /images/customers/logos/resend.png
logo_inverse: /images/customers/logos/light/resend.png
og_image: /images/customers/og/resend.jpg
tags:
  - supabase
date: '2025-02-21'
company_url: https://resend.com/
stats:
  [
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
  ]
misc: [{ label: 'Founded', text: 'San Francisco, CA, USA' }]
about: Resend is the new email API for developers. It's designed to build, test, and deliver transactional and marketing emails at scale.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'developer-tools']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'functions']
---

<Quote img="zeno-rocha.png" caption="Zeno Rocha, CEO, Resend">

Supabase enabled us to focus on building the best email infrastructure for developers—without worrying about backend complexity. Their authentication, database, and support have been game-changers for our rapid growth.

</Quote>

<Img src="/images/customers/resend/resend-homepage.png" alt="Resend website" zoomable />

Discover how [Resend](https://www.resend.com/), a fast-growing email sending platform, leveraged Supabase's database and authentication solutions to scale from an early-stage Y Combinator startup to millions of daily emails and a [Series A investment](https://resend.com/blog/series-a). By choosing Supabase, Resend was able to:

- Launch their product quickly without having to worry about backend complexity
- Scale seamlessly from zero to 5,000+ paying customers and over 300,000 developer signups in two years
- Ensure security and stability with built-in authentication and role-based access
- Save development time with intuitive, developer-friendly integrations

Learn how Supabase's support for startups and YC companies, scalability, security, and ease of use helped Resend focus on building a world-class email API for developers—without backend bottlenecks.

## The Challenge

<Subtitle>Building a High-Scale Email Platform Without Backend Complexity</Subtitle>

Resend started with the vision of creating a modern, developer-first email API. Communication is a fundamental part of any software, and developers are uniquely positioned to integrate their favorite tools early on. Resend exists to make human communication easier. They do that through the internet and the protocols that surround it, like email. However, as a small, fast-moving startup with a big vision, they faced several challenges:

- **Rapid Development Needs**: They needed a database and authentication solution that could be implemented immediately to support a proof of concept (POC), which got them admitted into YC.
- **Scalability Uncertainty**: Scalability wasn't a priority initially, but as they gained traction, they quickly required a highly scalable infrastructure.
- **Security & Stability**: With a rapidly increasing user base, eliminating security vulnerabilities and stability issues became top-of-mind engineering priorities.
- **Legacy Email API Limitations**: Many existing email API providers had outdated architectures, poor developer experiences, and lacked modern features. Resend's goal was to integrate with these legacy providers and abstract that complexity from their users.

Resend needed a flexible, developer-friendly backend that would allow them to iterate quickly without having to build and maintain complex backend infrastructure.

## Choosing Supabase

<Subtitle>Why Resend Chose Supabase</Subtitle>

Resend selected Supabase because the top priorities for startups are to move fast and grow faster:

- **Speed to Market**: Supabase's "Build in a Weekend" tagline aligned perfectly with Resend's need for rapid deployment.
- **Full-Stack Backend**: It combined database and authentication management, removing the need for separate tools.
- **Scalability & Flexibility**: Supabase evolved with Resend's needs, offering features like partitioning, read replicas, and database backups as they scaled.
- **Developer-Focused Experience**: Supabase provided a smooth, intuitive interface that enabled Resend's two front-end engineers to work with databases without deep expertise.

## Benefiting from the Supabase YC Program

Supabase is a [YC-backed company](https://www.ycombinator.com/companies/supabase) (S20 batch) and has always looked to support founders in scaling their startups quickly and efficiently. Through the Supabase YC Program, startups that join Y Combinator receive Supabase credits, allowing them to:

- Get instant access to a scalable backend without worrying about infrastructure costs.
- Use Supabase's full suite of database, authentication, and real-time tools at no upfront expense.
- Focus on product development and user acquisition rather than backend setup and maintenance.

For Resend, this deal was instrumental. When they joined YC with an idea and two paying customers, Supabase gave them the cost-effective backend foundation they needed to find product-market fit and scale their business.

<Quote img="zeno-rocha.png" caption="Zeno Rocha, CEO, Resend">

    The Supabase YC Program made it a no-brainer for us. We didn't have to worry about backend costs
    in the early days, which gave us more runway to focus on product-market fit.

</Quote>

## The Approach

<Subtitle>Growing and Scaling with supabase</Subtitle>

Resend chose Supabase because they prioritized rapid iteration and deployment, ensuring they could launch a robust email infrastructure without getting bogged down by backend development.

### Seamless Onboarding & Fast Implementation

- With Supabase's plug-and-play database and authentication solutions, Resend could deploy its initial version in days, accelerating its time to market.
- Supabase's developer-friendly SDKs allowed Resend's frontend engineers to build delightful user experiences without having to learn new tools or frameworks.

### Building a Scalable Backend from Day One

- Supabase is just PostgreSQL, and its native scalability provided Resend with a solid foundation that adapted to its growing user base, eliminating the need for early migrations.
- Features like partitioning and read replicas allowed the database to grow organically alongside customer demand without performance slowdowns.

### Iterating Without Backend Constraints

- Their team relied on row-level security (RLS) and automated backups to implement security enhancements without disrupting operations.
- By choosing Supabase, Resend remained agile and was able to iterate quickly based on customer input. This allowed Resend to focus on building a best-in-class and rapidly growing email platform that has solved a very tangible customer problem.

## Ensuring Security & Stability at Scale With Supabase

Security is critical in email infrastructure, and because Resend is a Supabase Teams customer, they receive direct support from Supabase, enabling them to:

- Identify vulnerabilities and implement best practices for database security.
- Strengthen authentication flows using Supabase's role-based access controls.
- Leverage automated database backups to prevent data loss.

This collaborative response ensures minimal downtime and reinforces Resend's confidence in Supabase as a reliable partner, not just a service provider.

<Quote img="zeno-rocha.png" caption="Zeno Rocha, CEO, Resend">

Having the Supabase team on hand during security reviews is invaluable. They respond quickly, help us secure our platform, and ensure we can continue scaling safely.

</Quote>

## The Results

<Subtitle>Scaling Seamlessly to 5,000+ Paying Customers & Millions of Emails Sent Daily</Subtitle>

<Img src="/images/customers/resend/resend-dashboard.jpg" alt="Resend dashboard" zoomable />

By leveraging Supabase's robust database and authentication infrastructure, Resend could scale seamlessly while maintaining security, stability, and developer efficiency.

Their rapid growth from a YC-backed startup to millions of emails sent daily highlights the impact of a backend that evolves alongside them.

Resend's successes include:

- **Exponential Growth**: Resend scaled from 0 to 1,000 paying customers in one year, doubling that in just six months and reaching 5,000+ paying customers today.
- **Massive User Adoption**: Resend's platform now has 300,000+ registered users, all authenticated via Supabase Auth.
- **Millions of Emails Sent Daily**: Supabase enables the infrastructure that powers millions of emails per day for Resend's customers.
- **Scalability Without Migration**: Supabase continues to meet Resend's evolving needs, preventing unnecessary migrations and allowing them to focus on product innovation.
- **Security & Stability**: Supabase supported critical security enhancements and helped Resend navigate significant technical challenges, ensuring a reliable platform.

<div className="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/gyd4nTAvsLs"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  />
</div>

## Value for YC Founders and Startups

For YC founders and early-stage startups, selecting the right backend infrastructure is crucial for rapid product development and efficient scaling. 59% of YC's W25 batch use Supabase, and over 500 YC companies have used Supabase in the past. Today, companies that use Supabase are more likely to be accepted into YC than other applicants. Resend's experience with Supabase highlights why it's a compelling choice for YC companies:

- **Faster Time to Market**: Supabase's easy-to-implement database and authentication features allowed Resend to build and launch their platform quickly, a key advantage for early-stage startups under time pressure.
- **Scalability Without Migration**: Unlike other backend solutions that startups often outgrow, Supabase evolved with Resend, ensuring they didn't waste time and effort on costly migrations.
- **Developer-Friendly Experience**: Supabase's PostgreSQL database and built-in authentication allow founders to focus on product-market fit instead of backend management.
- **Reliable Support and Security**: Supabase provided hands-on support and helped strengthen Resend's security infrastructure, making it an invaluable resource for startups handling sensitive user data.
- **Cost Savings Through the Supabase YC Program**: The credit Supabase provided through YC's deal eliminated backend infrastructure costs in Resend's critical early phase, helping them extend their runway and allocate resources to growth.

<Quote img="zeno-rocha.png" caption="Zeno Rocha, CEO, Resend">
  YC founders move fast, and Supabase helps us move even faster. It gave us everything we
  needed—auth, database, scalability—without the usual backend headaches.”
</Quote>

## Why Supabase?

<Quote img="zeno-rocha.png" caption="Zeno Rocha, CEO, Resend">

    For Resend, choosing Supabase was more than just finding a backend solution—it was about finding
    a technology partner to support their rapid growth and evolving needs. "Supabase gave us the
    flexibility and scalability needed at every growth stage. It's rare to find a tool that works
    just as well for startups as it does for large-scale operations."

</Quote>

Looking for a developer-friendly, scalable backend? See how Supabase can power your next big idea.

[Learn More](/)
