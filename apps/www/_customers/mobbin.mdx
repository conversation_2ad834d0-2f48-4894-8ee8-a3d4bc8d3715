---
name: <PERSON><PERSON><PERSON>
title: How <PERSON><PERSON><PERSON> migrated 200,000 users from Firebase for a better authentication experience.
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
# meta_title:
description: <PERSON><PERSON><PERSON> helps over 200,000 creators globally search and view the latest design patterns from well-known apps.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: <PERSON><PERSON><PERSON> helps over 200,000 creators globally search and view the latest design patterns from well-known apps.
author: rory_wilding
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/mobbin.png
logo_inverse: /images/customers/logos/light/mobbin.png
og_image: /images/customers/og/mobbin.jpg
tags:
  - supabase
date: '2023-02-14'
company_url: 'https://mobbin.design'
stats:
  [
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
  ]
misc: [{ label: 'Founded', text: Singapore }]
about: Mobbin is a comprehensive, expert-curated library of interfaces from the best-in-class digital products that helps product designers find relevant design inspiration and references with significantly less time and effort.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'developer-tools']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Asia'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth']
---

Mobbin helps over 200,000 creators globally search and view the latest design patterns from well-known apps.
Their platform allows creators to search screenshots of mobile apps to inspire product development.
Creators use Mobbin to combine inspiration and develop new experiences.

## From Idea to 30,000 users in one Product Hunt launch

The initial concept for Mobbin was just a Dropbox full of screenshots. After sharing with their early users,
the team tried organizing them into folders but this quickly became unscalable. Clear interest from users encouraged them to build a full website. They used Firebase for the backend and launched on
[Product Hunt](https://www.producthunt.com/posts/mobbin-1).

Mobbin went viral, and within the first week, they had 30,000 users. They have now more than 400,000
registered users through word of mouth. Their curation continues to attract designers and creators globally.

![Mobbin cover](/images/blog/mobbin/mobbin-cover.png)

## Scaling issues put Mobbin growth at risk

Firebase helped the team launch quickly, but at scale, they found Firebase lacked the functionality required for a high-quality user experience.

The first problem was duplicate user logins. Firebase Authentication doesn't automatically merge users with the same email address.
For example, users cannot log in with Google and Facebook if they share the same email address.
Users struggled to upgrade to a paid account because they couldn't remember which authentication provider they signed up with.

Next, they had issues with data integrity. While Firebase has 99.95% uptime, application code can (and does) fail.
Mobbin didn't want to implement idempotence for every mutative Firebase Function. Between a failed Function execution and a retry,
a document state might change without strict validation. The team could never be confident their data had the level of integrity they required.

Finally, the team grew concerned with their increasing Firebase bill. Because of the document-based data structure,
their API requests required several round trips. At one point, they were making 5 million API requests per month.
These round trips made their website slow and led to a notable time investment hacking together a fix.

![Mobbing screenshots](/images/blog/mobbin/mobbin-screenshots.jpg)

## Migrating to Supabase

Mobbin's initial product gave them a comprehensive understanding of customer requirements. For their second version,
they needed a better authentication experience, fairer pricing, and a relational database to deliver a quality
application sustainable for their business model.

The team found Supabase and realized it covered all the bases. Not only that, their team had experience with the
tools open source tools that Supabase is built with.

[Authentication](https://supabase.com/auth), built using [GoTrue](https://github.com/supabase/gotrue), instantly solved their users' login issues.
Under the hood, Supabase is just Postgres, and Jian Jie (co-founder and CTO of Mobbin) knows this is one of the best battle-tested
open source relational databases.

They decided that moving to Supabase was a no-brainer. The process itself was smooth - the real challenge was validating their
data integrity due to historical failures on their serverless functions.

Mobbin is now successfully running on top of Supabase, with superior performance compared to their old Firebase setup.
Because Supabase does not charge based on API requests, Mobbin significantly reduced their monthly spending without maintaining a custom backend.

<Quote img="jian-mobbin.jpg" caption="Jian Jie Liau, Co-founder and CTO at Mobbin.">
  <p>Migrating to Supabase meant that we could instantly fix our Auth problems and save money.</p>
  <p>
    Just being on Supabase alone gives us confidence we can deliver on whatever users need in the
    future.
  </p>
  <p>
    The cool thing is now we know under the hood it's just Postgres, it really does feel like
    anything is possible for the future of our product
  </p>
</Quote>

## Supabase saves Mobbin time and money

Migrating to Supabase helped the team at Mobbin instantly improve the end-user experience and save costs.
They now add new features with confidence, and they continue to help creators all over the globe find inspiration for their next project.
You can [check out Mobbin on their website](https://mobbin.design/browse/ios/apps).

[Sign up](https://supabase.com/dashboard/) for Supabase's Free Plan and set up a scalable backend in less than 2 minutes.
