---
name: Next Door Lending
title: 'How Next Door Lending leveraged Supabase to become a top 10 mortgage broker'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: 'How Next Door Lending leveraged Supabase to become a top 10 mortgage broker'
description: Discover how Supabase empowered NextDoorLending to move quickly and scale as required, leading to their surpassing other mortgage brokers and expanding their business.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Discover how Supabase empowered NextDoorLending to move quickly and scale as required, leading to their surpassing other mortgage brokers and expanding their business.
author: dave_wilson
author_title: Customer Success Manager
author_url: https://www.linkedin.com/in/david-wilson-*********/
author_image_url: https://media.licdn.com/dms/image/D4E03AQFurngYIRSHWQ/profile-displayphoto-shrink_200_200/0/1698406772235?e=1704931200&v=beta&t=Va55bBVbke2LzcEs5ReBOslLN_l3E8aqEeaQZv36sCA
logo: /images/customers/logos/next-door-lending.png
logo_inverse: /images/customers/logos/light/next-door-lending.png
og_image: /images/customers/og/next-door-lending.jpg
tags:
  - supabase
date: '2023-11-08'
company_url: 'https://www.nextdoorlending.com'
stats: []
misc:
  [
    { label: 'Use case', text: 'Real Estate' },
    { label: 'Solutions', text: 'Supabase Database, Supabase Auth, Supabase Storage' },
  ]
about: "Next Door Lending, a National Mortgage Broker that's licensed in 25 states, needed a solution that could support secure data management, a frictionless user experience, and future AI products."
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['real-estate', 'fintech']
# "startup" | "enterprise" | "indie_dev"
company_size: 'enterprise'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'storage']
---

## Introduction

Next Door Lending (NDL) is a top 10 mortgage brokerage in the country. They've processed $2 billion dollars of loans in the past three years. They evaluate a borrower's creditworthiness and ability to repay the loan. Then, they match each borrower with lenders that have the best loan terms. Currently, NDL is licensed and operates in 25 different states.

Partnering with over 50 lending companies, NDL offers hundreds of products for borrowers. These range from standard 30 Year Fixed Rate loans to mortgage refinances, to even USDA or Veteran loans. As a result, technology plays a critical role in NDL's daily operations and has been a differentiation vector against other mortgage brokers.

![Next Door Lending](/images/customers/next-door-lending/next-door-lending-website.jpg)

## The Challenge

- **Data ownership and management**: Not only is there regulation around financial data, one of NDL's core growth thesis is to leverage their data for future products. To do so, their internal teams needed a manageable data infrastructure. For Howard Haynes, CPO at NDL, "if we didn't have a way to manage data and access it, it would get very overwhelming." Without the tools to support the growth in data, "this would become a major pain point."
- **UX**: Every month, NDL adds thousands of new borrowers. Given the high cost of customer acquisition, any friction within the user experience directly translated to lost revenue. NDL's onboarding process needed to be smooth, fast, and seamless for borrowers.
- **Scale**: NDL operates across 25 states with 12 in-person branches to process loans, stay compliant, engage with local markets, and create a boutique experience for borrowers. NDL needed the ability to build future products such as predictive matching and AI tools for their branch brokers and borrowers.

<Quote img="howard-haynes.webp" caption="Howard Haynes, CPO at Next Door Lending">
  If we didn't have a way to manage data and access it, it would get very overwhelming. This would
  become a major pain point.
</Quote>

## Why Supabase?

NDL selected Supabase after considering other providers such as Amazon Web Services.

### Data - A single system to rule them all

For NDL, their backend challenges included designing the right APIs, row-level security, authentication, and a serving layer for their brokers. Having a single system that had all the appropriate functionality was important in maintaining its technical momentum. "When you use a lot of different data services, it's much harder to keep everything as fast as we need it to be", said Howard.

### UX - Frictionless authentication

As part of NDL's business model, they work with entrepreneurs who lead local branches. As a result, they used Supabase's user management and authentication product with built-in support for magic link sign-in and integrations with different auth providers. Beyond that, for both borrowers and branch brokers, Supabase enabled NDL to offer a frictionless authentication process that maintained high conversion and retention rates.

### Scale - To AI and beyond

NDL also deploys a separate website for each individual branch, connected together by a homegrown CMS and a shared Supabase backend server. For Howard, "when you're white labeling, and you're maintaining five or fifteen different deployments, it's very important that you can manage most of these things from the same system or the same backend. For NDL's future AI products, they can use [Supabase Vector](https://supabase.com/vector) to store, query, and index vector embeddings at scale, without introducing a new platform to their stack.

## The Solution

NDL employs multiple instances of Supabase for each of their branch web properties. Each instance is tied to a dedicated Vercel environment in Dev, QA, and UAT. Developers at NDL can iterate and test different scenarios before pushing changes live to customers.

Across all of NDL's branches, they see thousands of new signups each month, and each branch's website sends their signup data to the central Supabase backend via API. All loan details are aggregated in this server, allowing internal teams to access the right data quickly.
In the future, NDL plans to add a layer of predictive analytics to match borrowers and lenders. This would require capturing thousands of additional data points per user and building a comprehensive model. With Supabase, they're perfectly positioned to add on the additional capability.
"Supabase allows us to move fast and scale up as needed. There are a lot of tools that can only do one or the other – but Supabase really excels at both." - Howard Haynes, CPO at NDL
The Results

After launching their web application in 2020, Supabase helped NDL release new features and scale to become one of the nation's largest mortgage brokers. "Things that would have typically taken longer to set up have been a lot easier." Howard pointed at how the engineering team quickly onboarded to Supabase, "send[ing] someone to read the docs, they come back, and they're teaching me about a new feature in Supabase I wasn't aware of."

The engineering team now had the bandwidth to focus on building a custom CMS better tailored to their business requirements. With Supabase's auth product, NDL has also built custom onboarding flows for new users, asking them for details about their loan before a direct sign-up page, and increasing user happiness.

A significant portion of their business relies on SEO, making a fast-loading website crucial. They accomplished an impressive 0.5-second loading time by transitioning to a dynamic system where each page is directly loaded from the database. This transformation underscores the pivotal role Supabase played, as it houses and serves all their pages and data, ensuring their website's exceptional performance.

The success of NDL prompted its owners to expand the business with Neighbourhood Tech, a company that offers "NDL as a service" to other branches. They manage the additional system using Supabase, establishing a backend schema for data management, and utilizing replication for scalable expansion. This approach allows them to handle separate data quickly and efficiently.

An example of this is [Lock It Lending](https://www.lockitlending.com/). This new site uses Supabase to display all dynamic content, store lead information, and display financial literacy terms to their clients.

As a whole, Supabase has been able to solve all of NDL's backend needs in a single platform. They've been able to both move fast and scale up as needed. As a result, they've outpaced other mortgage brokers and expanded their business.

<Quote img="howard-haynes.webp" caption="Howard Haynes, CPO at Next Door Lending">
  “You can have a really great product, but you need to want to work with the people behind it. With
  Supabase, we always felt very aligned.”
</Quote>

## Tech Stack

- Vercel
- Next.js
- Chakra UI
- Supabase for database, auth, and storage.

> To learn more about how Supabase can help you build and scale apps with ease, [reach out to us](https://forms.supabase.com/enterprise).
