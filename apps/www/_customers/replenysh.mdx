---
name: Replenysh
title: <PERSON><PERSON>ysh uses Supabase to implement OTP in less than 24 hours.
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
# meta_title:
description: With Supabase, <PERSON><PERSON><PERSON><PERSON> gets a slick auth experience, reduces DevOps overhead, and continues to scale with Postgres.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: With Supabase, <PERSON>lenysh gets a slick auth experience, reduces DevOps overhead, and continues to scale with Postgres.
author: rory_wilding
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/replenysh.png
logo_inverse: /images/customers/logos/light/replenysh.png
og_image: /images/customers/og/replenysh.jpg
tags:
  - supabase
date: '2023-02-14'
company_url: https://replenysh.com/
stats:
  [
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
    { stat: '00,000', label: Example stat },
  ]
misc: [{ label: 'Founded', text: Southern California }]
about: Replenysh is a digital platform that connects global brands with local communities to recover materials and eliminate the concept of waste.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'ecommerce']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth']
---

Replenysh connects brands who want to recover their products and packaging with communities looking to monetize used materials.

Learn how Replenysh uses Supabase to power the circular economy, redefining how brands interact with their customers & products.

## Material recovery offers brands a new way to connect to their customers

At the end of a linear economy, products usually end up in landfills or the ocean. Every year, 8 million metric tons of plastics enter our ocean. This is on top of the estimated 150 million metric tons that currently circulate our marine environments. In the USA alone, every year, 139 million tons of waste is sent to landfills.

The problem is our current waste and recycling infrastructure can't support that demand. With less than 10% of material actually getting recycled, the vast majority just ends up in a landfill or as pollution.

Replenysh is building a solution to this problem by creating entirely new infrastructure to help brands get their materials back. They're building an ecosystem of tools to enable any community in the world to do the right thing.

Whenever material is dropped off at a host, brands have the opportunity to engage with that person, resulting in a win-win situation for the planet, the brands, and the individual who all want to make a positive impact. Anyone in the community can take their used products and materials to the hosts — with that material being tracked all the way back to the brands and manufacturers who will reuse it. Brands have the opportunity to engage with that person, resulting in a win-win situation for the planet, the brands, and the individual who all want to make a positive impact. This turns a traditional consumption model into an opportunity for brands and customers to participate in the circular economy.

Brands are adopting Replenysh to demonstrate their commitment to making a positive environmental impact, while seeking to engage with consumers in a new, authentic way. Replenysh provides the technology to communities that are helping build the future infrastructure where all materials are traceable and reused.

![Linear vs Circular economy](/images/blog/replenysh/circular-economy.png)

## Refactoring code leads to a better way forward

Historically the team created their own backend infrastructure, setting up APIs and Authentication to run their own OTP system with Twilio and passwords. Postgres is essential to their requirements because Replenysh use Row Level Security for authorization. Before adopting Supabase, the team used a Haskell server with a different Postgres instance deployed on Heroku.

The team decided to do a large refactor of their codebase and began investigating alternative hosting options to help speed up development time and reduce maintenance.

Firebase seemed appealing due to its ability to get started quickly, but the team didn't want to migrate away from a relational database. Specifically, Postgres and RLS fit their requirements.

When the team discovered Supabase on Hacker News they felt excitement. Supabase has a reputation for a smooth developer experience, providing Postgres and Row Level Security as a core feature.

As a bonus, Supabase provided more than just a database, reducing their DevOps overhead. After reading the [Supabase Auth documentation](https://supabase.com/docs/guides/auth/overview) the team determined they can have the functionality they need, like SMS OTP. When they saw [Mobbin are using Supabase at scale](https://supabase.com/case-studies-mobbin), they decided to migrate to Supabase.

## Globally recognized brands need quality at scale

Less than a day after beginning development with Supabase, the team completed their SMS OTP implementation, and deployed their app to the app store a few weeks later.

![Replenysh-Mobile-App](/images/blog/replenysh/Replenysh-Mobile-App.png)

Replenysh clients include brands with global recognition and millions of customers. With their reputation at stake, major brands have a high-quality threshold. If a global brand asks its customers to adopt a technology, the user experience must be high quality and reliable. Their customers' primary interaction point is mobile-first. This means a robust and smooth mobile Auth experience is non-negotiable. After switching to Supabase, when they demo, clients are instantly impressed with just how easy it is for users to log in with an SMS OTP and start participating in the circular economy. As a result, some of the world's largest retailers and beverage companies have recently joined Replenysh and the circular economy.

![Replenysh-large-screen-view](/images/blog/replenysh/Replenysh-large-screen-view.png)

## Supabase means faster development and reduced maintenance

<Quote img="clark-dinnison.jpeg" caption="Clark Dinnison, Head of Product at Replenysh.">
  <p>
    We saw the Auth update for launch week we thought it was the perfect time to get going with
    Supabase.
  </p>
  <p>
    We implemented & pushed live phone (OTP) auth and had something ready to deploy to the app store
    in less than 24-hours!
  </p>
  <p>
    Supabase's developer experience lived up to its reputation. Our app is ready to launch with
    major brands to help them reach sustainability goals and connect with their customers in a
    meaningful way
  </p>
</Quote>

## Supabase help Replenysh continue to scale

Supabase turbo-charged Replenysh's development time with a seamless end-user mobile login experience. They are ready to help major brands engage with their users through participation in the circular economy. With Supabase, the team knows they have a slick auth experience, reduced DevOps overhead, and can continue to scale with Postgres. You can test out the Supabase developer experience today by [starting a new project on the Free Plan!.](https://supabase.com/dashboard)
