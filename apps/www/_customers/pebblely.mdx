---
name: Pebblely
title: 'Scaling securely: one million users in 7 months with Supabase Auth'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: 'Scaling securely: one million users in 7 months with Supabase Auth'
description: Learn how Pebblely, an AI image generation company, used Supabase Auth to handle Single Sign On for their rapidly growing user base.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Learn how Pebblely, an AI image generation company, used Supabase Auth to handle Single Sign On for their rapidly growing user base.
author: paul_copplestone
author_title: CEO and Cofounder
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/pebblely.png
logo_inverse: /images/customers/logos/light/pebblely.png
og_image: /images/customers/og/pebblely.jpg
tags:
  - supabase
date: '2023-09-29'
company_url: 'https://pebblely.com'
stats: []
misc:
  [
    { label: 'Use case', text: 'Generative AI, Image generation' },
    { label: 'Solutions', text: 'Supabase Database, Supabase Auth, Supabase Realtime' },
  ]
about: 'Pebblely is an AI tool that turns boring product images into beautiful marketing assets in just seconds. The AI creates backgrounds based on the uploaded images and adds shadows or reflections accordingly.'
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['ai', 'media', 'ecommerce']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Asia'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'realtime']
---

Alfred and SK wanted to apply the innovations of AI to the advertising industry. At the start of 2023, they began building Pebblely: an AI image generation tool that turns boring images into beautiful marketing assets in just seconds. 7 months later, they had over 1 million users.

## The challenge: finding the right product

Alfred and SK came together in the middle of 2022 with the idea of building a startup together. They spoke with lawyers, students, accountants, and many others to explore interesting problems they could solve. Once they identified a promising opportunity, they developed a prototype to validate the idea and launched it within specific communities. This rapid iteration process was crucial to their startup's success.

Without Supabase, they would have been forced to manually create and manage databases, handle security and authentication, and build APIs from scratch. Particularly in the areas of security and authentication, they would have had to address issues related to user data and privacy. Thanks to Supabase, Alfred and SK were able to transform their ideas into prototypes in just a week each time they came up with a new concept.

It was during their sixth iteration that they conceived the idea for Pebblely. They observed that most people were using AI image generation tools for artwork but not for any commercial applications. Alfred, having previously owned an e-commerce shop, would often explore his neighborhood searching for suitable locations for product images. Combining this experience with SK's AI expertise, they realized that AI technology could automate the process of creating beautiful backgrounds.

![Pebblely's founders, Alfred and SK](/images/customers/pebblely/pebblely-founders-alfred-sk.jpg)

## Why Supabase?

In the case of Pebblely, Alfred and SK built their prototype in just two days, utilizing Supabase for the [database](https://supabase.com/database), API, [realtime](https://supabase.com/realtime) functionality, and [authentication](https://supabase.com/auth). They successfully launched the prototype in January 2023.

They wanted a solution that was both easy to use and would also support Pebblely as the company started to scale. Supabase was one of the technologies that the team could rely on.
Finally, there was the non-technical aspect of Supabase where people like Alfred could go in and make small updates with the dashboard without needing an engineer. In the past, Alfred had to ask the engineer or write the right SQL which took a lot of back and forth. As a result, Supabase also resulted in long-term saved engineering time.

<Quote img="alfred-lua-pebblely.jpeg" caption="Alfred Lua - Cofounder of Pebblely">
  Supabase was really handy in this part for us. It streamlined the database, the API, and
  authentication. Everything was up and running in two days.
</Quote>

## What they built

Pebblely is a tool where users can upload their image, remove the original background, and use AI to generate their perfect background for Instagram, Facebook, Email, and more.

One of their customers had hired seven different photographers and had spent a couple of thousand pounds on product photo shoots. However, the images never matched her vision because each photographer had their own style. Eventually, she was able to put the previous photographs into Pebblely and create images with more control. In the end, she was really happy to finally get the perfect photos that she had imagined in her mind.

![Pebblely uses AI to create backgrounds based on your uploaded images and adds shadows and reflections accordingly](/images/customers/pebblely/pebblely-product-in-action.jpg)

## The results

Pebblely grew to more than a million users in seven months by leveraging Supabase's all-in-one platform, with a special emphasis on the authentication solution. Using Supabase auth along with the database worked so seamlessly that it was unnecessary for them to evaluate other alternatives.

Setting up the initial email and password login took no time at all, and with each new challenge and unforeseen scenario, Supabase auth consistently proved its versatility and readiness to adapt. For example, Pebblely started seeing a lot of abuse and spam where people created multiple accounts to bypass limitations. To combat this, the team switched from email and password login to Google and magic link authentication, making it more difficult for users to create multiple accounts and increasing security while simplifying the login process. The total migration time? A couple of hours, because Supabase had the functionality ready.

As the company began engaging with enterprise-level customers, questions regarding enhanced authentication security started to emerge. One of the most prominent requests was for [Single Sign On (SSO)](https://supabase.com/docs/guides/auth/enterprise-sso) functionality. With Supabase's existing support, Alfred and SK have the capability to seamlessly integrate SSO into their product, attracting enterprise customers to fuel their growth.

<Quote img="alfred-lua-pebblely.jpeg" caption="Alfred Lua - Cofounder of Pebblely">
  As we started talking to bigger clients who might have demands for SSO, we realized that we would
  be able to very easily add that to our product. We don't have to worry about finding a vendor, how
  much it would cost, and how much additional work it would take to implement… Supabase is not only
  super easy to get started, but also provides all the backend solutions we require as we continue
  to grow.
</Quote>

Given the success of Pebblely, Alfred and SK are also building more AI products, such as [Vispunk Motion](https://vispunk.com/video) for AI video generation. And Supabase is the obvious go-to solution for all of them.

> To learn more about how Supabase Auth can help you build and scale secure AI apps with ease, [reach out to us](https://forms.supabase.com/enterprise).
