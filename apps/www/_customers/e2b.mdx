---
name: E2B
title: 'E2B: Accelerating AI-Driven Development with Supabase'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: 'E2B: Accelerating AI-Driven Development with Supabase'
description: Discover how E2B leveraged Supabase to streamline its platform, allowing secure, scalable execution of AI-generated code in the cloud.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Discover how E2B leveraged Supabase to streamline its platform, allowing secure, scalable execution of AI-generated code in the cloud.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/e2b.png
logo_inverse: /images/customers/logos/light/e2b.png
og_image: /images/customers/og/e2b.jpg
tags:
  - supabase
date: '2024-12-09'
company_url: 'https://e2b.dev'
stats: []
misc:
  [
    { label: 'Use case', text: 'Secure Code Execution, Developer Productivity, Scalability' },
    { label: 'Solutions', text: 'Supabase Auth, Supabase Database' },
  ]
about: An open-source platform that enables developers to run untrusted AI-generated code in the cloud safely and efficiently.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['ai', 'developer-tools', 'saas']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'North America'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'realtime', 'functions']
---

<Quote img="vasek-mlejnsky.jpg" caption="Vasek Mlejnsky, CEO, E2B">
  Supabase empowers us to focus on innovation rather than infrastructure. It's the backbone of our
  platform, enabling scalability and seamless developer experiences.
</Quote>

Discover how E2B, an open-source platform that enables developers to run untrusted AI-generated code in the cloud safely and efficiently, leveraged Supabase to streamline its platform, allowing secure, scalable execution of AI-generated code in the cloud.

Learn how Supabase's integrated authentication and database tools helped E2B optimize its development process.

## The Challenge - Simplifying Cloud-Based Code Execution

E2B aims to simplify how developers interact with AI-generated code by providing secure and scalable cloud-based sandboxes.

The platform's idea originated from the two founders' experience repeatedly integrating backend tools like authentication, notifications, and payment systems. Inspired by GPT-3.5's capabilities, they envisioned automating this repetitive work, which led to the creation of E2B's sandbox architecture.

With E2B's sandboxes, developers can quickly spin up lightweight, Ubuntu-based virtual machines with minimal effort, enabling flexible and secure code execution for various use cases.

Key challenges include:

- Managing infrastructure complexity while ensuring scalability for growing user needs.
- Safely executing untrusted AI-generated code in the cloud.
- Maintaining a seamless developer experience while handling millions of sandbox requests daily.

Having raised over $11 million in seed funding and just released SDK version 1.0 and a new landing page, E2B focused on hiring engineers with expertise in AI product development. They wanted to enhance the team's technical capabilities while implementing robust incident reporting and monitoring systems to ensure high reliability and transparency in service performance.

E2B needed to scale their platform to handle increased user demand while maintaining service quality and performance.

**Key Requirements**
**Authentication and Database Integration:** E2B needed a platform that seamlessly combined user authentication and database management. Supabase allowed authenticated users to connect automatically to the database without requiring custom integration.

**Scalability:** The platform needed to support high-scale operations, including logging millions of sandbox instances per month. Supabase's PostgreSQL database effortlessly handled this volume.

**Developer Productivity:** Supabase's SDKs, with first-party support for frameworks like Next.js and the AI-powered SQL assistant, were essential for streamlining development tasks. This enabled E2B's team to focus on innovation rather than operational overhead.

**Ease of Use and Familiarity:** Supabase's use of PostgreSQL and high-quality documentation reduced the learning curve for new developers, ensuring rapid onboarding and minimal support requirements.

**Security and Role-Based Access:** E2B required robust security features, including row-level security, to manage sensitive data and ensure data isolation across users and operations.

## The Approach - Building on Supabase for Efficiency and Security

E2B chose Supabase to manage their authentication and database needs due to its:

**Ease of Use and Integration:** Supabase seamlessly combines authentication and database management, making it easy for E2B to automatically connect users to their database without extensive custom integration.

**Developer-Friendly Tools:** Supabase provides intuitive SDKs with first-party support for frameworks like Next.js and React, enabling faster development with features like hooks and type safety.

**Time-Saving Features:** The AI-powered SQL assistant and query-sharing capabilities significantly reduced the time spent on writing and managing SQL queries, boosting productivity and collaboration among team members.

**Open-Source Community:** E2B's founders were early adopters of Supabase, drawn to its positioning as an open-source alternative to Firebase. Its active community and high-quality documentation made it a trusted choice for their team.

**Interlinked Systems:** The tight integration between Supabase's authentication system and its PostgreSQL database offered a streamlined solution that was both flexible and easy to manage.

**Scalability and Reliability:** Supabase's infrastructure seamlessly handled E2B's growing demands, including processing over 4 million monthly sandbox operations, demonstrating its ability to scale reliably.

E2B leveraged Supabase to store and track logs for sandbox operations, with every session start and end saved as database entries. This enabled detailed analytics while maintaining performance at scale.

## The Results - Delivering Innovation at Scale

E2B's adoption of Supabase transformed its operations and enhanced its ability to deliver a reliable and efficient platform for running untrusted AI-generated code in the cloud.

**Seamless Scalability:** Supabase handled over 4 million monthly sandbox operations, logging each start and close event without disruptions. This seamless scalability ensured that E2B could support its growing user base and complex workloads, from quick code executions to multi-hour processes.

**Enhanced Developer Productivity:** By leveraging Supabase's SDKs and AI-powered SQL assistant, E2B significantly reduced the time spent on repetitive tasks like writing queries and debugging database issues. Developers could focus on building innovative features rather than managing backend infrastructure, increasing overall velocity.

**Robust Security Features:** Supabase's row-level security ensured that user data and sandbox operations remained isolated and protected, meeting E2B's stringent security requirements. This was particularly critical for handling untrusted AI-generated code, which demanded high data integrity and user privacy.

**Cost-Effectiveness:** Supabase's authentication and database tools eliminated the need for additional infrastructure or third-party services, reducing operational costs. The easy-to-use tools also minimized development overhead, saving valuable resources.

**Improved User Experience:** Supabase's ability to seamlessly connect authentication and database operations ensured a smoother experience for E2B users, who rely on the platform for real-time execution of AI-driven workflows.

**Streamlined Operations:** Supabase's tools eliminated the need for complex custom infrastructure, freeing up E2B's team to focus on innovation.

**Enhanced Logging and Analytics:** The comprehensive logging of sandbox operations provided E2B with detailed analytics, enabling better insights into user behavior and system performance. This transparency empowered the team to optimize their platform further and meet customer demands more effectively.

## Why Supabase?

<Quote img="vasek-mlejnsky.jpg" caption="Vasek Mlejnsky, CEO, E2B">
  Supabase combines ease of use with powerful features. Its native integration of authentication and
  database tools is a game-changer, enabling us to build faster while maintaining security and
  scalability.
</Quote>

Through its partnership with Supabase, E2B built a foundation for long-term growth. It delivers a platform that meets the increasing demands of AI-driven development while maintaining exceptional reliability and security.

**Discover how Supabase can simplify your development process and support your growth.**

[Learn More](https://forms.supabase.com/enterprise)
