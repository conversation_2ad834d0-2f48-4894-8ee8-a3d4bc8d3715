---
name: Shotgun
title: 'Supabase migration delivers an 83% reduction in data infrastructure costs for Shotgun'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: 'Supabase migration delivers an 83% reduction in data infrastructure costs for Shotgun'
description: Discover how Shotgun, an event ticketing platform with over 3 million users, reduced costs and consolidated systems through migration to Supabase.
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Discover how Shotgun, an event ticketing platform with over 3 million users, reduced costs and consolidated systems through migration to Supabase.
author: paul_copplestone
author_title: CEO and Cofounder
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
logo: /images/customers/logos/shotgun.png
logo_inverse: /images/customers/logos/light/shotgun.png
og_image: /images/customers/og/shotgun.jpg
tags:
  - supabase
date: '2023-11-30'
company_url: 'https://shotgun.live/'
stats: []
misc:
  [
    { label: 'Use case', text: 'Event ticketing platform, SaaS' },
    { label: 'Solutions', text: 'Database' },
  ]
about: 'Shotgun is a French ticketing platform with both a B2B and B2C side. It starts with a simple tool for creators to create events, distribute tickets, and build faithful fan bases. Once an event is live, users can purchase tickets on Shotgun and also discover new events.'
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'media']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Europe'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database']
---

## The challenge

Shotgun had a particular challenge. Because of how large events can drive a disproportionate amount of traffic to their site, they always had to contend with large database bills to accommodate spikes in traffic.

At the time, they were spending $7,000 per month for Digital Ocean and on top of that, another $5,000 per month on Fivetran for database replication. Combined, their database costs were over $12,000 per month and one of the biggest spends they had for their infrastructure.

More importantly, due to the nature of their business, their Digital Ocean instance CPU usage rarely crossed 20%. That meant 80% of their spending was being paid as insurance for spikes in traffic.

As a result, when they began to trim expenses and prepare for future growth, the database was one of the first places they looked.

![Shotgun](/images/customers/shotgun/shotgun.jpg)

## Why Supabase?

Tackling their database cost problem, the Shotgun team considered moving back to Amazon RDS which they had previously migrated from. However, one of the coworkers remembered tweets about databases mentioning Supabase.

That then turned into a test migration of the Shotgun staging server. It was a low-cost proof of concept, especially since their spending on the staging server was only $100 per month.

Supabase delivered not only a cheaper [database](https://supabase.com/database) solution but also on the performance front, outstripping their previous Digital Ocean instance. That gave the team confidence to migrate their entire databases from both Digital Ocean and Fivetran. Post-migration, when the team looked at the benchmarks, they found even more performance improvements.

With Supabase, they found instant savings on the instance pricing and then additional cost savings with a more performant, smaller instance. On top of that, the Shotgun team was assured of standby support from Supabase for the migration. The decision was easy at that point.

<Quote
  img="florent-cailhol-shotgun.jpg"
  caption="Florent Cailhol - Senior Software Architect at Shotgun"
>
  It was the cost. We thought it might be too good to be true, but there's no denying that Supabase
  was better.
</Quote>

## The Results

The most significant change for Shotgun was the cost savings they achieved with Supabase. Instead of spending $12k per month, the team now spends $2,155 per month. An 83% decrease.

Their database infrastructure bill went from $7k to a bit over $2k and Shotgun was able to turn off most of their Fivetran spend by taking advantage of the [publications features](https://supabase.com/docs/guides/database/replication) available in Supabase.

The migration itself also led to further performance gains. Helped by our Head of Customer Success, Kevin Brolly, they made a couple of Postgres configuration changes and smaller database optimizations. Now, the Shotgun team sees a 40% lower response time on calls to the database, which leads to less usage/consumption of the server, and even a lower bill on other providers such as Vercel as well.

After moving to Supabase, Shotgun found a dependable database that consistently met their needs, demonstrating reliability even when handling one of the biggest events on their platform ever. Their engineering team now has confidence in their database.

And finally, it's become much easier for Shotgun to onboard new developers with the Supabase dashboard. With SQL, developers can query specific pieces of data, build performance reports, and even do basic operations.

Shotgun's experience with Supabase can be best summed up by, it's cheaper, it's easier, and it works.

<Quote
  img="florent-cailhol-shotgun.jpg"
  caption="Florent Cailhol - Senior Software Architect at Shotgun"
>
  Do not hesitate in moving to Supabase. The performance is great, the support is good, the
  dashboard is great, and when we talked to the team, we felt that we were talking to people that
  understood our issues and could help us with our performance problems… One of the best solutions
  for hosting a database.
</Quote>

## Tech stack

- Next.js
- Node
- Vercel
- Supabase

> To learn more about how Supabase can help you manage and scale your database, **[reach out to us](https://forms.supabase.com/enterprise)**.
