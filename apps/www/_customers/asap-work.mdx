---
name: Quivr
title: 'Scaling Beyond No-Code: asap.work’s Journey to a Faster, Flexible Solution with Supabase'
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
meta_title: 'Scaling Beyond No-Code: asap.work’s Journey to a Faster, Flexible Solution with Supabase'
description: ''
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: ''
author: prashant
author_title: <PERSON><PERSON><PERSON>
author_url: https://github.com/CoolAssPuppy
author_image_url: https://avatars.githubusercontent.com/u/914007?v=4
logo: /images/customers/logos/asap-work.png
logo_inverse: /images/customers/logos/light/asap-work.png
og_image: /images/customers/og/asap-work.jpg
tags:
  - supabase
date: '2025-02-28'
company_url: https://asap.work
stats: []
misc:
  [
    { label: 'Use case', text: 'Database Searches, User Authentication, Storage, Scalability' },
    { label: 'Solutions', text: 'Supabase Database, Supabase Auth, Supabase Storage' },
  ]
about: 'asap.work is a construction recruitment startup founded by industry experts from Adecco and Manpower. Using their knowledge and experience of the construction recruitment industry, asap.work focuses on providing a fair marketplace for temporary construction workers and fair pricing for clients.'
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['recruitment']
# "startup" | "enterprise" | "indie_dev"
company_size: 'startup'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Europe'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'storage']
---

<Quote img="robin-baillargeaux.jpg" caption="Robin Baillargeaux, CTO at asap.work">
  With no-code tools, you win a lot of time at the beginning because there’s no deployment or heavy
  setup, but as you grow, they become less manageable. You don’t have versioning, proper
  documentation, or collaborative tools like Git, making it hard to build a strong foundation.
  Switching to Supabase gave us a classical environment with all these tools, making everything more
  seamless and sustainable.
</Quote>

Discover how asap.work, a startup transforming temp recruitment in the construction industry, migrated from a no-code environment to Supabase, resulting in increased productivity, reduced costs, increased search performance, and enhanced user experience.

## The Challenge \- Optimizing database performance for complex searches

asap.work was experiencing performance issues with database searches and needed a solution to handle complex searches on its recruitment platform.

They were using Xano, a no-code tool for their backend and database. However, as their database grew, the system could take between 1 and 60 seconds to process database queries on their main talent search table. The platform could have 30 to 60 users running queries concurrently who relied on efficient real-time data access to search candidate CVs. The slow database searches were causing a productivity bottleneck. As the performance issues persisted, migration to a new platform became inevitable.

asap.work needed to enhance the performance and scalability of their construction industry recruitment platform.

**Key requirements**

- **Fast, Reliable Database Searches:** Their core requirement was a backend capable of efficiently handling large, complex search queries. Their users, mainly recruiters, needed to quickly and reliably perform searches on a talent database with 100,000 rows and 40 columns. These searches are essential for matching workers to available construction jobs.
- **Scalable User Authentication and File Management:** They needed a robust authentication system to manage user identities securely and efficiently. The platform also needed file storage capabilities to handle documents associated with temporary work assignments, such as resumes and compliance paperwork.
- **Developer-Friendly Platform for Long-Term Growth:** While migrating from a no-code platform, they sought a flexible, developer-friendly solution that would scale, allowing current and future developers to adapt and build upon the platform.
- **Enhanced User and Developer Experience:** They wanted to restore user confidence and operational efficiency and improve the experience for recruiters and the internal development team.

## The Approach – Migrating to Supabase from a no-code environment

The team conducted benchmark testing on several database solutions, and Supabase emerged as the ideal choice due to its superior performance and developer-friendly experience.

Since they were moving from a no-code to code environment, they hired new developers for the migration process and rebuilt their platform from scratch, allowing for improvements and changes.

The migration from Xano involved rebuilding the database schema to optimize it for future scalability. The database was a custom system built on JSON objects, locking asap.work into a specific vendor, and limiting scalability.

The migration from Xano to Supabase, including schema changes and data transfer, took approximately three months. This included a week of dedicated work on data migration, mainly using CSV imports and API calls for the data transfers.

## The Solution – Accelerating performance and streamlining development with Supabase

The migration to Supabase offered a tailored, developer-friendly environment equipped to handle complex database demands. asap.work utilised several critical components of Supabase to achieve their goals and enhance their platform’s performance and functionality:

**Enhanced Search Performance with PostgreSQL**

Migrating to Supabase’s PostgreSQL database from Xano enabled asap.work to reduce query times from up to 60 seconds to around 100 milliseconds – a massive performance improvement.

<Quote img="robin-baillargeaux.jpg" caption="Robin Baillargeaux, CTO at asap.work">
  We were facing random search times, sometimes 10, 30, or even 60 seconds. With Supabase, it’s now
  around 100 milliseconds, even for complex requests.
</Quote>

**Seamless User Authentication and File Storage**

With Supabase’s integrated Authentication and Storage, asap.work established a secure, streamlined user experience. Supabase’s Auth enabled easy management of user identities, while the storage component simplified file handling \- essential for managing resumes and compliance documents.

Robin highlighted the advantage of the storage capabilities:

_“Storage is straightforward, and having everything in one place makes file management much easier.”_

**Developer-first platform with Open-Source Flexibility**

Moving to Supabase’s open-source platform allowed asap.work to maintain complete control over its infrastructure, eliminating vendor lock-in. The developer-friendly environment enabled the team to rebuild with SQL, facilitating long-term scalability and seamless customization as the company’s needs evolve.

As Robin described it, _“The developer experience with Supabase has been one of the main benefits. It’s like having the best of both worlds: a robust platform with the freedom to customize.”​_

## The Results – With Supabase, asap.work reduced search times, increased efficiency, and restored user confidence

- **Faster Search Performance**: Database query times improved from up to 60 seconds to approximately 100 milliseconds, significantly boosting productivity and allowing recruiters to perform searches without delays.
- **Increased Developer Efficiency**: Migrating to Supabase’s code-based environment improved maintainability, streamlined development workflows, and enabled quicker troubleshooting, enhancing the development team’s productivity.
- **Restored User Confidence**: With consistent and reliable performance, asap.work restored user trust and operational efficiency, which was essential for the success of its recruitment platform.
- **Long-Term Cost Savings**: By avoiding the escalating costs with Xano, asap.work achieved cost stability while benefiting from the scalability of Supabase’s open-source platform. Robin explained, _“The cost-efficiency we’ve achieved with Supabase is tremendous, and the reliability has restored our team’s confidence.”_

## Why Supabase?

<Quote img="robin-baillargeaux.jpg" caption="Robin Baillargeaux, CTO at asap.work">
  Supabase gave us the performance boost we needed with features tailored to our growing demands.
  The open-source platform also gives us peace of mind for the future. It’s been transformational,
  and we’re excited to continue investigating Supabase’s real-time and edge capabilities.
</Quote>

Unlike no-code solutions that often rely on pre-configured workflows, Supabase gave asap.work the flexibility to redesign their database and backend logic from the ground up. This allowed the team to implement best practices like version control and remove duplicative logic, resulting in cleaner, more maintainable code.

For companies scaling beyond the initial stages of product development, Supabase bridges the gap between ease-of-use and powerful customization. It’s the ideal step up from no-code for teams that need scalability without compromising on control.
