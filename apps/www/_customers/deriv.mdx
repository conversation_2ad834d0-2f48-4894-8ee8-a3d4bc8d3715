---
name: Deriv
title: "Deriv's Journey with Supabase"
# Use meta_title to add a custom meta title. Otherwise it defaults to '{name} | Supabase Customer Stories':
# meta_title:
description: Accelerating Online Trading with a Scalable Postgres Backend
# Use meta_description to add a custom meta description. Otherwise it defaults to {description}:
meta_description: Accelerating Online Trading with a Scalable Postgres Backend
author: prashant
author_title: <PERSON><PERSON><PERSON>
author_url: https://github.com/CoolAssPuppy
author_image_url: https://avatars.githubusercontent.com/u/914007?v=4
logo: /images/customers/logos/deriv.png
logo_inverse: /images/customers/logos/light/deriv.png
og_image: /images/customers/og/deriv.jpg
tags:
  - supabase
date: '2025-03-06'
company_url: https://deriv.com/
stats: [{ stat: '00,000', label: Example stat }]
misc: [{ label: 'Founded', text: 'Malta' }]
about: Award-winning online broker, rated excellent on Trustpilot. Sign up for a demo trading account to trade forex, stocks, and other financial markets.
# "healthcare" | "fintech" | "ecommerce" | "education" | "gaming" | "media" | "real-estate" | "saas" | "social" | "analytics" | "ai" | "developer-tools"
industry: ['saas', 'non-bank financial service']
# "startup" | "enterprise" | "indie_dev"
company_size: 'enterprise'
# "Asia" | "Europe" | "North America" | "South America" | "Africa" | "Oceania"
region: 'Europe'
# "database" | "auth" | "storage" | "realtime" | "functions" | "vector"
supabase_products: ['database', 'auth', 'functions']
---

<Quote img="raunak-kathuria.jpg" caption="Raunak Kathuria, VP of Engineering, Deriv">

Supabase enabled us to move fast and focus on solving customer problems without getting bogged down by complexity. Its automation and integrations have been game-changers for us.

</Quote>

## The Challenge

<Subtitle>Enabling Agility and Automation in a Growing Trading Platform</Subtitle>

[Deriv](https://deriv.com) is a globally recognized online trading company with over 25-years legacy of innovation and growth. Over the years, the company has expanded rapidly, establishing itself as a leading platform for online trading. With this exponential growth, Deriv required additional development speed, optimizing operational efficiency, and scaling its infrastructure to meet the evolving demands of traders worldwide.

Deriv’s key requirements included:

- **Faster time to market**. Reducing setup time for backend infrastructure to accelerate feature delivery.
- **Automation and efficiency**. Eliminating manual processes related to database management and scaling.
- **Scalability and security**. Ensuring the platform could scale seamlessly while maintaining high security and compliance standards.
- **Flexibility and integration**. Choosing a solution that could integrate seamlessly with existing tools and systems.

<Quote img="raunak-kathuria.jpg" caption="Raunak Kathuria, VP of Engineering, Deriv">

Supabase provides many features out of the box. Say, for example, Authentication. We would have had to build something custom on top of our database. It would have taken 2-3 weeks, minimum, to come up with the schema ourselves.

</Quote>

## Choosing Supabase

<Subtitle>A Developer-Friendly, Scalable Backend Solution</Subtitle>

After evaluating multiple backend solutions, Deriv selected Supabase due to its automation capabilities, ease of integration, and developer-friendly ecosystem. The decision was driven by several key factors:

- **Automation and ease of use**. Supabase allowed Deriv to automate database setup, scaling, and security configurations with just a few clicks. Tasks that previously took hours—such as deploying read replicas or setting up PITR (Point-in-Time Recovery)—were reduced to minutes.
- **Integrated suite of tools**. Supabase offered built-in authentication, real-time capabilities, and storage, eliminating the need for separate services. The team also appreciated the ability to seamlessly integrate with external tools, such as API gateways.
- **Familiarity with PostgreSQL**. Since Supabase is built on PostgreSQL, Deriv’s developers could leverage their existing knowledge without a steep learning curve.
- **Rich partner ecosystem**. Supabase has a palette of integration partners to choose from, ensuring that Deriv can build a complete system using best-of-breed products.

"We wanted a backend that could accelerate our development while maintaining security and scalability. Supabase stood out due to its automation, integrations, and ecosystem,” said Raunak.

“Supabase integrates with tools like Twilio. We didn’t have to build our SMS One-Time Password (OTP) system. All of it is supported out of the box,” said Raunak.

## The Approach

<Subtitle>Rapid Implementation and Adoption</Subtitle>

Deriv adopted a strategic, phased approach to implementing Supabase:

- **Proof of Concept (POC)**. The team initiated a POC to determine how easily Supabase could be configured and integrated. The key criterion was that if a tool could not be properly set up within a week, we wouldn’t use it.
- **Tool evaluation**. The team tested 30–40 backend tools and services, focusing on the best automation, integrations, and ecosystem support. Supabase stood out as a clear leader in these categories.
- **API-centric development**. Instead of tightly coupling code directly to the database schema, Deriv implemented Supabase via REST APIs to maintain flexibility and minimize future technical debt.
- **Developer-led testing**. Developers were given autonomy to experiment with Supabase and assess its suitability. Initially cautious, they quickly saw its benefits, leading to organic adoption within the engineering team.
- **Integration with existing workflows**. Supabase was integrated into existing development processes with minimal disruption.
- **Smart AI assistance**. The ability to quickly deploy databases, manage authentication, and optimize performance via the Security Advisor and Performance Advisor built-in to Supabase was an incredible time-saver.

“I’m not a database expert, but Supabase’s Advisors are like having a PostgreSQL expert living with you in the dashboard,” said Raunak.

By prioritizing automation and developer experience, Deriv ensured smooth adoption with minimal friction.

## The Results

<Subtitle>From Days to Minutes…Faster Developer Velocity</Subtitle>

Supabase has delivered significant benefits to Deriv, transforming how the company builds and scales its trading platform. Key results include:

- **Deployment time reduced from hours to minutes**. Setting up a new database instance now takes just a few minutes compared to hours previously.
- **Enhanced developer velocity**. Developers can now deploy and iterate on new features faster, reducing dependency on infrastructure teams.
- **Seamless authentication and security**. Supabase’s built-in authentication features eliminated the need for a custom solution, saving weeks of development effort.
- **Flexibility and scalability**. Supabase’s ability to support multi-region deployments and automated backups has enabled Deriv to scale its infrastructure effortlessly.

"Supabase has significantly improved our development speed and efficiency. We no longer spend time managing infrastructure—instead, we focus on building the best experience for our customers," said Raunak.

## Looking Ahead

<Subtitle>Scaling with AI and Vector Databases</Subtitle>

As Deriv continues to expand, the company is increasingly focused on AI-driven automation and agent-based
trading. Supabase’s **pgVector database support** is a key factor in enabling AI-powered features that
enhance user experience and decision-making.

Note: see [Supabase pgVector vs. Pinecone analysis](https://supabase.com/blog/pgvector-vs-pinecone).

"We’re bullish on AI, and Supabase provides the perfect foundation for us to integrate AI-driven capabilities in the future," said Raunak.

## Why Supabase?

Deriv’s experience highlights why Supabase is the ideal backend for modern, scalable applications:

- **Faster time to market**. Automate database setup, authentication, and scaling.
- **Scalability without migration**. PostgreSQL-backed infrastructure ensures long-term flexibility and cloud portability.
- **Developer-friendly experience**. Built-in tools and integrations streamline development. Deriv was able to set up a project in one week while estimating a traditional setup would have taken multiple weeks or more.
- **Security and compliance**. Advanced security configurations and role-based access controls ensure data protection.

## Conclusion

For Deriv, Supabase has been a transformative solution, enabling agility, automation, and scalability. By eliminating backend complexity, Supabase allows Deriv to focus on innovation and delivering the best trading experience to its users.

<Quote img="raunak-kathuria.jpg" caption="Raunak Kathuria, VP of Engineering, Deriv">

What set Supabase apart for us was its feature set and how these features worked together cohesively to create a comprehensive environment. The platform's focus on security, performance, and developer experience aligned perfectly with our needs.

</Quote>

## Ready to scale your backend with Supabase?

Learn More at [www.supabase.com](/)
