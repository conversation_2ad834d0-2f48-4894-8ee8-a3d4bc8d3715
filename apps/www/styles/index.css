@tailwind base;
@tailwind components;
@tailwind utilities;

@import url(../pages/launch-week/launchWeek.css);

@import './../../../packages/ui/build/css/source/global.css';
@import './../../../packages/ui/build/css/themes/dark-combined.css';
@import './../../../packages/ui/build/css/themes/light.css';

@layer utilities {
  .run {
    animation-play-state: running;
  }
  .pause {
    animation-play-state: paused;
  }
}

@font-face {
  font-family: 'custom-font';
  src:
    url(../../../packages/common/assets/fonts/CustomFont-Book.woff2) format('woff2'),
    url(../../../packages/common/assets/fonts/CustomFont-Book.woff) format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'custom-font';
  src:
    url(../../../packages/common/assets/fonts/CustomFont-Medium.woff2) format('woff2'),
    url(../../../packages/common/assets/fonts/CustomFont-Medium.woff) format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

html {
  scroll-behavior: smooth !important;
}

body {
  @apply bg text-foreground;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* h1,
h2,
h3,
h4,
h5,
h6 { */
/*
  Typography
*/

.h1:not(.prose *):not(.overwrite),
.h2:not(.prose *):not(.overwrite),
.h3:not(.prose *):not(.overwrite),
.h4:not(.prose *):not(.overwrite),
.h5:not(.prose *):not(.overwrite),
.h6:not(.prose *):not(.overwrite) {
  /* @apply text-foreground; */
  margin-bottom: 16px;
}

.h1:not(.prose *):not(.overwrite) {
  @apply text-4xl;
}

.h2:not(.prose *):not(.overwrite) {
  @apply text-4xl;
}

.h3:not(.prose *):not(.overwrite) {
  @apply text-2xl;
}

.h4:not(.prose *):not(.overwrite) {
  @apply text-xl;
}

.h5:not(.prose *):not(.overwrite) {
  @apply text-xl;
}

h1:not(.overwrite),
h2:not(.overwrite),
h3:not(.overwrite),
h4:not(.overwrite),
h5:not(.overwrite),
h6:not(.overwrite) {
  scroll-margin-top: 90px;
}

.h1:not(.prose *):not(.overwrite),
.h2:not(.prose *):not(.overwrite) {
  line-height: 1.2 !important;
}

.label:not(.prose *):not(.overwrite) {
  @apply text-xs font-mono uppercase text-foreground-lighter tracking-wider;
}

.heading-gradient {
  @apply text-[#F4FFFA00] bg-clip-text bg-gradient-to-b from-foreground to-foreground/70;
}

.heading-gradient-brand {
  @apply text-[#3ECF8E00] bg-clip-text bg-gradient-to-b from-brand to-brand/80;
}

.toc-animate {
  @apply !translate-x-1 !text-brand;
}

.prose-toc a {
  transition: all 0.07s ease-in-out;
}

@media only screen and (max-width: 960px) {
  .h1:not(.prose *):not(.overwrite) {
    font-size: 2.2rem !important;
  }
  .h2:not(.prose *):not(.overwrite) {
    font-size: 2rem !important;
  }
}

@media only screen and (max-width: 720px) {
  .h1:not(.prose *):not(.overwrite) {
    font-size: 2rem !important;
  }
  .h2:not(.prose *):not(.overwrite) {
    font-size: 1.8rem !important;
  }
}

@media only screen and (max-width: 520px) {
  .h1:not(.prose *):not(.overwrite) {
    font-size: 1.8rem !important;
  }
  .h2:not(.prose *):not(.overwrite) {
    font-size: 1.6rem !important;
  }
}

.p:not(.prose *):not(.overwrite) {
  @apply text-foreground-light;
  margin-bottom: 16px;
}

.small:not(.prose *):not(.overwrite) {
  @apply text-foreground-lighter text-xs;
}

.sbui-border-fix select {
  @apply border-solid;
  border-width: 1px;
}

.section--masked {
  /* overflow: hidden; */
}

::selection {
  background-color: #6ee7b7 !important;
  color: #333 !important;
}

.section--bg-masked {
  position: absolute;
  width: 100%;
  height: 100%;
  /* overflow: visible; */
}

.section--bg {
  @apply bg-background;
  position: relative;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  transform-origin: 100% 0;
  overflow: hidden;
}

.section-container {
  position: relative;
}

.stroke-text {
  -webkit-text-fill-color: transparent; /* Will override color (regardless of order) */
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: hsl(var(--background-selection));
}

.stroke-text.fill {
  -webkit-text-fill-color: #1ae88f;
}

/* Database page */

.dashboard-tabs div[role='separator'] {
  @apply border border-strong;
}

.sbui-tabs--alt div[role='tablist'] .sbui-btn-primary {
  @apply bg-border-stronger text-white;
  text-shadow: none;
  font-weight: 400;
}
.sbui-tabs--alt div[role='tablist'] .sbui-btn-text {
  @apply text-border-muted;
  font-weight: 400;
  shadow: none;
}

.sbui-tabs--underline-alt div[role='tablist'] .sbui-tab-button-underline--active {
  @apply border-foreground-muted;
}
/* override position of tabs  */
.dashboard-tabs > div > div {
  justify-content: center;
}

/* Auth page */

.dark .header--light {
  display: none;
}

.header--dark {
  display: none;
}

.dark .header--dark {
  display: block;
}

table {
  margin-top: 1em;
  border-collapse: collapse;
}

.sbui-typography-container thead {
  @apply text-border;
}

.sbui-typography a {
  @apply break-words;
}

/*
* sets the image in @Next/Image components
* to respect the height of the content
*
*/
.next-image--dynamic-fill {
  display: block;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  grid-column: 1 / -1;
}
.next-image--dynamic-fill.wide,
video.wide {
  @apply sm:my-0 sm:max-w-none sm:-mx-8 sm:w-[calc(100%_+_4rem)];
}
@media (min-width: 560px) {
  .next-image--dynamic-fill.wide,
  video.wide {
    width: calc(100% + 6rem) !important;
    margin: 0 -3rem;
  }
}
@media (min-width: 1280px) {
  .next-image--dynamic-fill.wide,
  video.wide {
    width: calc(100% + 10rem) !important;
    margin: 0 -5rem;
  }
}
@media (min-width: 1535px) {
  .next-image--dynamic-fill.wide,
  video.wide {
    width: calc(100% + 14rem) !important;
    margin: 0 -7rem;
  }
}

.next-image--dynamic-fill > span {
  position: relative !important;
}
.next-image--dynamic-fill img {
  object-fit: contain;
  width: 100% !important;
  position: relative !important;
  height: unset !important;
}

.line-clamp {
  display: -webkit-box;
  margin: 0 auto;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/**
animations
*/

.fade-in-up-2ms {
  opacity: 0;
  transform: translate3d(0, 10px, 0);
  animation: fadeInUp 0.2s cubic-bezier(0.25, 0.25, 0, 1) both;
}

.fade-in-up-4ms {
  opacity: 0;
  transform: translate3d(0, 10px, 0);
  animation: fadeInUp 0.4s cubic-bezier(0.25, 0.25, 0, 1) both;
}

.fade-in-2ms {
  opacity: 0;
  transform: translate3d(0, 10px, 0);
  animation: fadeIn 0.2s cubic-bezier(0.25, 0.25, 0, 1) both;
}

.fade-in-4ms {
  opacity: 0;
  transform: translate3d(0, 10px, 0);
  animation: fadeIn 0.4s cubic-bezier(0.25, 0.25, 0, 1) both;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/**
launch week
*/

.launch-week-timeline-border--approaching {
  border-image: linear-gradient(to bottom, hsl(var(--brand-default)), var(--colors-purple7)) 1 100%;
}

.video-container {
  @apply overflow-hidden rounded-xl border;
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
}

.video-container.no-border {
  @apply !border-none;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.all-unset {
  all: unset;
}

.auth-container {
  background: linear-gradient(270deg, #71fb8e, #acecbc);
  background-size: 400% 400%;

  -webkit-animation: AnimationName 14s ease infinite;
  -moz-animation: AnimationName 14s ease infinite;
  animation: AnimationName 14s ease infinite;
}

@-webkit-keyframes AnimationName {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
@-moz-keyframes AnimationName {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
@keyframes AnimationName {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@property --rotate {
  syntax: '<angle>';
  initial-value: 132deg;
  inherits: false;
}

:root {
  --auth-ui-card-height: 50vh;
  --lw-secondary-color: #8a8f98;
}

.glow-area {
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: transparent;
  z-index: -1;
  cursor: pointer;
}

.glow-area::after {
  position: absolute;
  content: '';
  top: calc(var(--auth-ui-card-height) / 8);
  left: 0;
  right: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
  margin: 0 auto;
  transform: scale(0.75);
  filter: blur(calc(var(--auth-ui-card-height) / 6));
  background-image: linear-gradient(
    var(--rotate),
    hsl(var(--brand-default)),
    #3c67e3 43%,
    hsl(var(--brand-default))
  );
  opacity: 1;
  transition: opacity 0.5s;
  animation: spin 8s linear infinite;
}

@keyframes spin {
  0% {
    --rotate: 0deg;
  }
  100% {
    --rotate: 360deg;
  }
}

@keyframes transformSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* recharts */

.recharts-surface {
  overflow: visible;
}

/* Fix inline code block wrapping */
@media screen and (min-width: 769px) {
  .short-inline-codeblock {
    white-space: pre !important;
  }
}

.gradient-container {
  min-height: 360px;
  position: relative;
  max-width: 1600px;
  margin: 0 auto;
  left: 0;
  right: 0;
  overflow: hidden;
}
@media screen and (max-width: 769px) {
  .gradient-container {
    left: 50%;
    transform: translateX(-50%);
  }
}

.gradient-mask {
  z-index: -10;
  position: absolute;
  min-height: 100%;
  width: 75%;
  margin: 0 auto;
  left: 0;
  right: 0;
  background: radial-gradient(
    circle at 50% -35%,
    rgba(57, 97, 125, 0.32) 0%,
    rgba(57, 97, 125, 0.32) 5%,
    rgba(0, 222, 209, 0.75) 8%,
    hsl(var(--background-default)) 65%,
    hsl(var(--background-default)) 100%
  );
}

@media screen and (max-width: 769px) {
  .gradient-mask {
    width: 100%;
  }
}

/* The bottom part below the stroke */
.gradient-mask--masked {
  z-index: -10;
  position: absolute;
  min-height: 100%;
  width: 100%;
  margin: 0 auto;
  left: 0;
  right: 0;
  background: radial-gradient(
    circle at 50% -60%,
    rgba(255, 255, 255, 0) 0%,
    hsl(var(--background-default)) 65%
  );
}

@media screen and (max-width: 769px) {
  .gradient-mask--masked {
    background: radial-gradient(
      circle at 50% -40%,
      rgba(255, 255, 255, 0) 0%,
      hsl(var(--background-default)) 65%
    );
  }
}

/* The stroke on the circle */
.flair-mask-a {
  position: absolute;
  background-color: #375965;
  top: 1px;
  width: 180%;
  left: -40%;
  height: 500px;
  -webkit-clip-path: ellipse(52% 135px at 50% 3px);
  clip-path: ellipse(52% 213px at 50% -75px);
}

/* The inside of the circle */
.flair-mask-b {
  position: absolute;
  background-color: hsl(var(--background-default));
  top: 0px;
  width: 182%;
  left: -41%;

  height: 500px;
  -webkit-clip-path: ellipse(52% 135px at 50% 3px);
  clip-path: ellipse(52% 213px at 50% -75px);
}

@media only screen and (min-width: 600px) {
  .flair-mask-a {
    top: 1px;
    margin: 0 auto;
    width: 140%;
    left: -20%;
    right: 0;
  }
  .flair-mask-b {
    top: 0px;
    margin: 0 auto;
    width: 142%;
    left: -21%;
    right: 0;
  }
}

@media only screen and (min-width: 960px) {
  .flair-mask-a {
    top: 1px;
    margin: 0 auto;
    width: 120%;
    left: -10%;
    right: 0;
  }
  .flair-mask-b {
    top: 0px;
    margin: 0 auto;
    width: 121%;
    left: -10.5%;
    right: 0;
  }
}

.radial-mask {
  -webkit-mask-image: linear-gradient(rgba(0, 0, 0, 1), transparent);
}

/* Text with a gradient background */
[class*='gradient-text-'] {
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

[class*='gradient-text-']::selection {
  background-color: hsl(var(--brand-default));
  background-clip: unset;
  -webkit-text-fill-color: white;
}

@layer utilities {
  .gradient-text-brand-100 {
    background-image: linear-gradient(
      45deg,
      hsl(var(--brand-500)) 0%,
      hsl(var(--brand-default)) 100%
    );
  }
  .gradient-text-brand-300 {
    background-image: linear-gradient(
      45deg,
      hsl(var(--brand-500)) 0%,
      hsl(var(--brand-default)) 100%
    );
  }
  .gradient-text-100 {
    background-image: -webkit-linear-gradient(45deg, #fff 0%, var(--colors-green12) 100%);
  }

  .gradient-text-500 {
    background-image: -webkit-linear-gradient(
      45deg,
      hsl(var(--foreground-lighter)) 0%,
      hsl(var(--foreground-default)) 100%
    );
  }

  .gradient-text-pink-500 {
    @apply text-transparent bg-clip-text bg-gradient-to-br from-[#c867ee] to-[#f6a4f3];
  }

  .gradient-text-purple-500 {
    @apply text-transparent bg-clip-text bg-gradient-to-br from-[#902DFB] to-[#D87BF5];
  }

  .gradient-text-purple-800 {
    @apply text-transparent bg-clip-text bg-gradient-to-br from-[#5900EA] to-[#A242E5];
  }

  .gradient-text-gold-500 {
    background-image: -webkit-linear-gradient(45deg, #e4b641 100%, #f5bf0069 41%);
  }

  .radial-gradient-text-500 {
    background: radial-gradient(45% 100% at 50% 50%, #fff 30%, hsla(0, 0%, 100%, 0.35) 100%);
  }
  .radial-gradient-text-600 {
    background: radial-gradient(45% 100% at 50% 50%, #fff 30%, hsla(0, 0%, 100%, 0.6) 100%);
  }
}

/* LW7 Tickets Slider */
.ticket-brick-swiper .swiper-slide {
  width: 230px;
}

@media (min-width: 768px) {
  .ticket-brick-swiper .swiper-slide {
    width: 450px;
  }
}

.ticket-brick-swiper .swiper-wrapper,
.swiper-transition-linear .swiper-wrapper {
  transition-timing-function: linear;
}

/* dat.GUI */

.dg.ac {
  z-index: 9999 !important;
  margin-top: 65px !important;
}

/* Zoomable image */

[data-rmiz-modal]:focus,
[data-rmiz-modal-overlay]:focus {
  outline: none !important;
}

[data-rmiz-modal-overlay],
[data-rmiz-modal-img] {
  transition-timing-function: cubic-bezier(0.24, 0.25, 0.05, 1) !important;
}
[data-rmiz-modal-overlay='visible'] {
  @apply !bg-background;
  opacity: 0.8;
}
[data-rmiz-modal-img] {
  image-rendering: high-quality;
}

/* Loader dots */

.loader-dots div {
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loader-dots div:nth-child(1) {
  left: 4px;
  animation: loader-dots1 0.6s infinite;
}

.loader-dots div:nth-child(2) {
  left: 4px;
  animation: loader-dots2 0.6s infinite;
}

.loader-dots div:nth-child(3) {
  left: 16px;
  animation: loader-dots2 0.6s infinite;
}

.loader-dots div:nth-child(4) {
  left: 28px;
  animation: loader-dots3 0.6s infinite;
}

@keyframes loader-dots1 {
  0% {
    transform: scale(0);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes loader-dots3 {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0);
  }
}

@keyframes loader-dots2 {
  0% {
    transform: translate(0, 0);
  }

  100% {
    transform: translate(12px, 0);
  }
}

.lw13-game-input {
  visibility: none;
  opacity: 0;
}

@media (hover: none) {
  .lw13-game-input {
    visibility: visible !important;
    opacity: 1 !important;
  }
}
