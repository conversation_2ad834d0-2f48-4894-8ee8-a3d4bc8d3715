create type partner_type as enum ('technology', 'expert');

create table partner_contacts (
  id bigint generated by default as identity primary key,
  type partner_type not null,
  company text not null,
  country text not null,
  details text,
  email text not null unique,
  first text not null,
  last text not null,
  phone text,
  size int,
  title text,
  website text not null,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);
alter table partner_contacts enable row level security;

create policy "Enable public insert" on partner_contacts for insert with check (true);


create table partners (
  id bigint generated by default as identity primary key,
  slug text not null unique,
  type partner_type not null,
  category text not null,
  developer text not null,
  title text not null,
  description text not null,
  logo text not null,
  images text[],
  overview text not null,
  website text not null,
  docs text not null,
  contact bigint references partner_contacts not null,
  approved boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  tsv tsvector generated always as (
    setweight(to_tsvector('english', title), 'A')
      || setweight(to_tsvector('english', description), 'B')
      || setweight(to_tsvector('english', overview), 'C')
      || setweight(to_tsvector('english', category), 'D')
      || setweight(to_tsvector('english', slug), 'D')
  ) stored;
);
alter table partners enable row level security;

create policy "Enable public read access" on partners for select using (true);


create table enterprise_contacts (
  id bigint generated by default as identity primary key,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  company_name text not null,
  contact_name text not null,
  contact_email text not null,
  contact_phone text,
  company_size text,
  details text,
  country text
);
alter table enterprise_contacts enable row level security;

create policy "Enable public insert" on enterprise_contacts for insert with check (true);

