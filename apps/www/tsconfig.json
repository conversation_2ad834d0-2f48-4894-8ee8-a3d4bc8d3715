{"compilerOptions": {"baseUrl": ".", "paths": {"contentlayer/generated": ["./.contentlayer/generated"], "~/*": ["./*"], "@ui/*": ["./../../packages/ui/src/*"]}, "downlevelIteration": true, "target": "ES2021", "module": "esnext", "jsx": "preserve", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}]}, "exclude": ["node_modules", "supabase"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", ".contentlayer/generated", "./../../packages/ui/src/**/*.d.ts"]}