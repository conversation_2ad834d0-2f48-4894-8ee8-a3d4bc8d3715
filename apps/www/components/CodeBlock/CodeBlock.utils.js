const monokaiCustomTheme = {
  dark: {
    hljs: {
      display: 'block',
      overflowX: 'auto',
      padding: '0.5em',
      background: '#161616',
      color: '#ddd',
    },
    'hljs-tag': {
      color: '#569cd6',
    },
    'hljs-keyword': {
      color: '#569cd6',
      fontWeight: 'normal',
    },
    'hljs-selector-tag': {
      color: '#569cd6',
      fontWeight: 'normal',
    },
    'hljs-literal': {
      color: '#569cd6',
      fontWeight: 'normal',
    },
    'hljs-strong': {
      color: '#569cd6',
    },
    'hljs-name': {
      color: '#569cd6',
    },
    'hljs-code': {
      color: '#66d9ef',
    },
    'hljs-class .hljs-title': {
      color: 'gray',
    },
    'hljs-attribute': {
      color: '#bf79db',
    },
    'hljs-symbol': {
      color: '#bf79db',
    },
    'hljs-regexp': {
      color: '#bf79db',
    },
    'hljs-link': {
      color: '#bf79db',
    },
    'hljs-string': {
      color: '#3ECF8E',
    },
    'hljs-bullet': {
      color: '#3ECF8E',
    },
    'hljs-subst': {
      color: '#3ECF8E',
    },
    'hljs-title': {
      color: '#3ECF8E',
      fontWeight: 'normal',
    },
    'hljs-section': {
      color: '#3ECF8E',
      fontWeight: 'normal',
    },
    'hljs-emphasis': {
      color: '#3ECF8E',
    },
    'hljs-type': {
      color: '#3ECF8E',
      fontWeight: 'normal',
    },
    'hljs-built_in': {
      color: '#3ECF8E',
    },
    'hljs-builtin-name': {
      color: '#3ECF8E',
    },
    'hljs-selector-attr': {
      color: '#3ECF8E',
    },
    'hljs-selector-pseudo': {
      color: '#3ECF8E',
    },
    'hljs-addition': {
      color: '#3ECF8E',
    },
    'hljs-variable': {
      color: '#3ECF8E',
    },
    'hljs-template-tag': {
      color: '#3ECF8E',
    },
    'hljs-template-variable': {
      color: '#3ECF8E',
    },
    'hljs-comment': {
      color: '#75715e',
    },
    'hljs-quote': {
      color: '#75715e',
    },
    'hljs-deletion': {
      color: '#75715e',
    },
    'hljs-meta': {
      color: '#75715e',
    },
    'hljs-doctag': {
      fontWeight: 'normal',
    },
    'hljs-selector-id': {
      fontWeight: 'normal',
    },
  },
  light: {
    hljs: {
      display: 'block',
      overflowX: 'auto',
      padding: '0.5em',
      background: '#f9f9f9',
      color: '#24292f',
    },
    'hljs-tag': {
      color: '#0550ae',
    },
    'hljs-keyword': {
      color: '#0550ae',
      fontWeight: 'normal',
    },
    'hljs-selector-tag': {
      color: '#0550ae',
      fontWeight: 'normal',
    },
    'hljs-literal': {
      color: '#0550ae',
      fontWeight: 'normal',
    },
    'hljs-strong': {
      color: '#0550ae',
    },
    'hljs-name': {
      color: '#0550ae',
    },
    'hljs-code': {
      color: '#66d9ef',
    },
    'hljs-class .hljs-title': {
      color: 'gray',
    },
    'hljs-attribute': {
      color: '#bf79db',
    },
    'hljs-symbol': {
      color: '#bf79db',
    },
    'hljs-regexp': {
      color: '#bf79db',
    },
    'hljs-link': {
      color: '#bf79db',
    },
    'hljs-string': {
      color: '#1B9657',
    },
    'hljs-bullet': {
      color: '#1B9657',
    },
    'hljs-subst': {
      color: '#1B9657',
    },
    'hljs-title': {
      color: '#1B9657',
      fontWeight: 'normal',
    },
    'hljs-section': {
      color: '#1B9657',
      fontWeight: 'normal',
    },
    'hljs-emphasis': {
      color: '#1B9657',
    },
    'hljs-type': {
      color: '#1B9657',
      fontWeight: 'normal',
    },
    'hljs-built_in': {
      color: '#1B9657',
    },
    'hljs-builtin-name': {
      color: '#1B9657',
    },
    'hljs-selector-attr': {
      color: '#1B9657',
    },
    'hljs-selector-pseudo': {
      color: '#1B9657',
    },
    'hljs-addition': {
      color: '#1B9657',
    },
    'hljs-variable': {
      color: '#1B9657',
    },
    'hljs-template-tag': {
      color: '#1B9657',
    },
    'hljs-template-variable': {
      color: '#1B9657',
    },
    'hljs-comment': {
      color: '#75715e',
    },
    'hljs-quote': {
      color: '#75715e',
    },
    'hljs-deletion': {
      color: '#75715e',
    },
    'hljs-meta': {
      color: '#75715e',
    },
    'hljs-doctag': {
      fontWeight: 'normal',
    },
    'hljs-selector-id': {
      fontWeight: 'normal',
    },
  },
}

export default monokaiCustomTheme
