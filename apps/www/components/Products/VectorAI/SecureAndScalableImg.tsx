import React, { useEffect, useRef } from 'react'
import { useTheme } from 'next-themes'
import { DEFAULT_EASE } from '~/lib/animations'

const SecureAndScalableImg = ({ isHovered }: { isHovered: boolean }) => {
  const { resolvedTheme } = useTheme()
  const shieldsRef = useRef<any>()
  const mainRef = useRef<any>()

  const colors = {
    shieldsStroke: resolvedTheme?.includes('dark') ? '#494949' : '#D5D5D5',
    cardBg: resolvedTheme?.includes('dark') ? '#161616' : '#F8F9FA',
    bgGlow: resolvedTheme?.includes('dark') ? '#009C77' : '#00FFD9',
    iconBg: resolvedTheme?.includes('dark') ? '#112B25' : '#DCDCDC',
    smallIcon: resolvedTheme?.includes('dark') ? '#1DF7C3' : '#00826A',
    cardLine: resolvedTheme?.includes('dark') ? '#1DF7C3' : '#00826A',
  }

  useEffect(() => {
    mainRef.current.style.transform = isHovered ? 'translateY(-5px)' : 'translateY(0px)'

    if (!isHovered) return

    const shields = [...shieldsRef.current?.querySelectorAll('path')]

    shields.map((shield, i) => {
      shield.style.transition = 'opacity 0.2s ease'

      setTimeout(() => {
        shield.style.opacity = '0.1'

        setTimeout(() => {
          shield.style.opacity = '1'
        }, 400)
      }, i * 35)
    })
  }, [isHovered])

  const RenderedSVG = () => (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 284 211"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* concentric shields */}
      <g ref={shieldsRef}>
        <g filter="url(#filter0_b_4346_100010)">
          <path
            d="M211.626 48.3233V47.9921L211.305 47.9128L142.456 30.9229L142.355 30.8979L142.253 30.9229L73.4046 47.9128L73.0831 47.9921V48.3233V70.0275V70.1201C73.0831 99.1234 73.0831 113.726 77.0635 126.956C80.8942 139.688 87.3861 151.461 96.1092 161.495C105.173 171.921 117.523 179.714 142.051 195.192L142.109 195.229L142.326 195.403L142.58 195.242L142.658 195.193C167.186 179.715 179.536 171.921 188.6 161.495C197.323 151.461 203.815 139.688 207.646 126.956C211.626 113.726 211.626 99.1234 211.626 70.12V70.0275V48.3233Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter1_b_4346_100010)">
          <path
            d="M218.767 41.6039V41.2727L218.446 41.1934L142.456 22.4413L142.355 22.4163L142.254 22.4413L66.2639 41.1934L65.9424 41.2727V41.6039V65.5592V65.6519C65.9424 97.6724 65.9424 113.784 70.3338 128.379C74.5601 142.426 81.7223 155.414 91.346 166.485C101.346 177.988 114.971 186.586 142.051 203.674L142.129 203.724L142.355 203.866L142.58 203.724L142.659 203.674C169.738 186.586 183.364 177.988 193.364 166.485C202.987 155.414 210.15 142.426 214.376 128.379C218.767 113.784 218.767 97.6723 218.767 65.6517V65.5592V41.6039Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter2_b_4346_100010)">
          <path
            d="M225.908 34.8843V34.5532L225.587 34.4739L142.456 13.9596L142.355 13.9346L142.254 13.9596L59.1233 34.4739L58.8018 34.5532V34.8843V61.0908V61.1836C58.8018 96.2213 58.8018 113.841 63.6042 129.803C68.226 145.164 76.0585 159.368 86.5829 171.475C97.5185 184.054 112.42 193.457 142.051 212.156L142.129 212.205L142.355 212.348L142.581 212.205L142.659 212.156C172.29 193.457 187.192 184.054 198.127 171.475C208.652 159.368 216.484 145.164 221.106 129.803C225.908 113.842 225.908 96.2214 225.908 61.1839V61.0912V61.0908V34.8843Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter3_b_4346_100010)">
          <path
            d="M233.049 28.1649V27.8338L232.728 27.7545L142.457 5.47808L142.355 5.45308L142.254 5.47808L51.9827 27.7545L51.6612 27.8338V28.1649V56.6225V56.7153C51.6612 94.7703 51.6612 113.899 56.8746 131.226C61.8919 147.902 70.3946 163.322 81.8197 176.464C93.6912 190.12 109.868 200.329 142.051 220.637L142.13 220.687L142.355 220.829L142.581 220.687L142.659 220.637C174.842 200.329 191.019 190.12 202.891 176.464C214.316 163.322 222.819 147.902 227.836 131.226C233.049 113.899 233.049 94.7704 233.049 56.7157V56.6229V56.6225V28.1649Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter4_b_4346_100010)">
          <path
            d="M240.949 20.7319V20.4008L240.628 20.3214L142.457 -3.90425L142.356 -3.92924L142.255 -3.90425L44.0843 20.3214L43.7628 20.4008V20.7319V51.6797V51.7725C43.7628 93.1651 43.7628 113.963 49.4308 132.801C54.8856 150.931 64.1298 167.695 76.5512 181.984C89.4579 196.831 107.046 207.93 142.052 230.02L142.13 230.069L142.377 230.225L142.596 230.06L142.66 230.02C177.666 207.93 195.254 196.831 208.161 181.984C220.582 167.695 229.827 150.931 235.281 132.801C240.949 113.963 240.949 93.1652 240.949 51.7729V51.68V51.6797V20.7319Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter5_b_4346_100010)">
          <path
            d="M249.06 13.1004V12.7693L248.739 12.69L142.458 -13.5371L142.357 -13.5621L142.256 -13.5371L35.9749 12.69L35.6534 12.7693V13.1004V46.6049V46.6978C35.6534 91.5171 35.6534 114.028 41.7881 134.418C47.6922 154.041 57.6977 172.186 71.142 187.651C85.1115 203.72 104.149 215.734 142.052 239.652L142.131 239.702L142.139 239.707L142.147 239.711L142.147 239.711L142.388 239.849L142.597 239.693L142.661 239.653C180.564 215.734 199.602 203.72 213.572 187.651C227.016 172.186 237.021 154.041 242.926 134.418C249.06 114.028 249.06 91.5171 249.06 46.6976V46.6049V13.1004Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter6_b_4346_100010)">
          <path
            d="M257.171 5.46885V5.13772L256.85 5.05838L142.459 -23.17L142.358 -23.195L142.256 -23.17L27.8655 5.05838L27.544 5.13772V5.46885V41.53V41.6229C27.544 89.8691 27.544 114.094 34.1455 136.035C40.4988 157.151 51.2656 176.676 65.7328 193.318C80.7653 210.61 101.252 223.538 142.053 249.285L142.132 249.335L142.358 249.477L142.583 249.335L142.662 249.285C183.463 223.538 203.95 210.61 218.982 193.318C233.45 176.676 244.216 157.151 250.57 136.035C257.171 114.094 257.171 89.8692 257.171 41.6233V41.5302V41.53V5.46885Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter7_b_4346_100010)">
          <path
            d="M265.875 -2.72111V-3.05225L265.554 -3.13159L142.459 -33.5078L142.358 -33.5328L142.257 -33.5078L19.1624 -3.13159L18.8409 -3.05225V-2.72111V36.0838V36.1768C18.8409 88.1005 18.8409 114.164 25.9433 137.77C32.7787 160.488 44.3625 181.495 59.9275 199.4C76.1006 218.004 98.1422 231.913 142.054 259.623L142.132 259.673L142.358 259.815L142.584 259.673L142.661 259.624C186.574 231.913 208.615 218.004 224.789 199.4C240.354 181.495 251.938 160.488 258.773 137.77C265.875 114.164 265.875 88.1006 265.875 36.1771V36.0842V36.0838V-2.72111Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter8_b_4346_100010)">
          <path
            d="M274.974 -11.2833V-11.6144L274.653 -11.6938L142.459 -44.3154L142.358 -44.3404L142.256 -44.3154L10.0628 -11.6938L9.74127 -11.6144V-11.2833V30.3901V30.4831C9.74127 86.2515 9.74127 114.237 17.3674 139.584C24.7068 163.977 37.1447 186.533 53.8573 205.758C71.2229 225.734 94.8902 240.669 142.053 270.43L142.132 270.48L142.14 270.485L142.148 270.49L142.148 270.49L142.368 270.616L142.583 270.48L142.661 270.431C189.825 240.669 213.492 225.734 230.858 205.758C247.571 186.533 260.009 163.977 267.348 139.584C274.974 114.237 274.974 86.2514 274.974 30.4828V30.3901V-11.2833Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter9_b_4346_100010)">
          <path
            d="M284.646 -20.3844V-20.7155L284.324 -20.7949L142.459 -55.8032L142.358 -55.8282L142.256 -55.8032L0.390894 -20.7949L0.0693999 -20.7155V-20.3844V24.338V24.431C0.0693977 84.2862 0.0693966 114.315 8.25219 141.512C16.1273 167.686 29.473 191.888 47.4056 212.516C66.0388 233.95 91.4342 249.976 142.054 281.919L142.132 281.968L142.358 282.11L142.583 281.968L142.661 281.919C193.281 249.976 218.676 233.95 237.31 212.516C255.242 191.888 268.588 167.686 276.463 141.512C284.646 114.315 284.646 84.2864 284.646 24.4317V24.3387V24.338V-20.3844Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
        <g filter="url(#filter10_b_4346_100010)">
          <path
            d="M294.941 -30.0717V-30.4029L294.619 -30.4822L142.459 -68.031L142.358 -68.056L142.256 -68.031L-9.90403 -30.4822L-10.2255 -30.4029V-30.0717V17.8961V17.9892C-10.2255 82.1942 -10.2255 114.398 -1.45022 143.564C6.99513 171.633 21.3072 197.588 40.5382 219.71C60.5207 242.696 87.7556 259.882 142.054 294.147L142.132 294.196L142.387 294.357L142.604 294.183L142.662 294.147C196.96 259.882 224.195 242.696 244.177 219.71C263.408 197.588 277.72 171.633 286.166 143.564C294.941 114.398 294.941 82.1942 294.941 17.9892V17.8961V-30.0717Z"
            stroke={colors.shieldsStroke}
            strokeWidth="0.845575"
          />
        </g>
      </g>
      {/* bg glow */}
      <g filter="url(#filter11_f_4346_100010)">
        <ellipse cx="143.196" cy="124.219" rx="84.9456" ry="84.4399" fill={colors.bgGlow} />
      </g>
      {/* Main content */}
      <g
        ref={mainRef}
        style={{ transition: `all 0.18s cubic-bezier(${DEFAULT_EASE})` }}
        filter="url(#filter12_b_4346_100010)"
      >
        <rect x="72.8496" y="79.5647" width="139" height="65.3972" rx="8" fill={colors.cardBg} />
        <path
          d="M99.1605 95.8374C99.0961 95.1897 98.8101 94.6745 98.3026 94.2919C97.795 93.9093 97.1567 93.7181 96.3878 93.7181C95.8461 93.7181 95.3688 93.8109 94.956 93.9965C94.5469 94.1821 94.2249 94.4397 93.9901 94.7692C93.759 95.095 93.6435 95.4662 93.6435 95.8828C93.6435 96.1897 93.7098 96.4586 93.8423 96.6897C93.9749 96.9207 94.1529 97.1196 94.3764 97.2862C94.6037 97.4491 94.8537 97.5893 95.1264 97.7067C95.4029 97.8241 95.6813 97.9226 95.9616 98.0022L97.1889 98.3544C97.5601 98.4567 97.9313 98.5874 98.3026 98.7465C98.6738 98.9056 99.0128 99.1063 99.3196 99.3487C99.6302 99.5874 99.8783 99.8809 100.064 100.229C100.253 100.574 100.348 100.989 100.348 101.474C100.348 102.095 100.187 102.654 99.8651 103.15C99.5431 103.646 99.081 104.04 98.4787 104.332C97.8764 104.62 97.1548 104.764 96.3139 104.764C95.5223 104.764 94.8348 104.633 94.2514 104.371C93.6681 104.106 93.2098 103.741 92.8764 103.275C92.5431 102.809 92.3556 102.269 92.3139 101.656H93.4048C93.4427 102.114 93.5942 102.502 93.8594 102.82C94.1245 103.139 94.4692 103.381 94.8935 103.548C95.3177 103.71 95.7912 103.792 96.3139 103.792C96.8935 103.792 97.4086 103.695 97.8594 103.502C98.3139 103.305 98.67 103.032 98.9276 102.684C99.1889 102.332 99.3196 101.923 99.3196 101.457C99.3196 101.063 99.2173 100.733 99.0128 100.468C98.8082 100.199 98.5223 99.9737 98.1548 99.7919C97.7912 99.6101 97.3688 99.4491 96.8878 99.309L95.4957 98.8999C94.5829 98.6272 93.8745 98.2503 93.3707 97.7692C92.867 97.2881 92.6151 96.6745 92.6151 95.9283C92.6151 95.3033 92.7817 94.754 93.1151 94.2806C93.4522 93.8033 93.9067 93.4321 94.4787 93.1669C95.0545 92.898 95.7003 92.7635 96.4162 92.7635C97.1397 92.7635 97.7798 92.8961 98.3366 93.1612C98.8935 93.4264 99.3348 93.7919 99.6605 94.2578C99.9901 94.72 100.166 95.2465 100.189 95.8374H99.1605ZM112.445 98.7465C112.445 99.9586 112.229 101.014 111.797 101.911C111.365 102.805 110.77 103.498 110.013 103.991C109.259 104.479 108.392 104.724 107.411 104.724C106.429 104.724 105.56 104.479 104.803 103.991C104.049 103.498 103.456 102.805 103.024 101.911C102.596 101.014 102.382 99.9586 102.382 98.7465C102.382 97.5343 102.596 96.4813 103.024 95.5874C103.456 94.6897 104.051 93.9965 104.808 93.5078C105.566 93.0154 106.433 92.7692 107.411 92.7692C108.392 92.7692 109.259 93.0154 110.013 93.5078C110.77 93.9965 111.365 94.6897 111.797 95.5874C112.229 96.4813 112.445 97.5343 112.445 98.7465ZM111.411 98.7465C111.411 97.7048 111.236 96.8128 110.888 96.0703C110.539 95.3241 110.064 94.754 109.462 94.3601C108.859 93.9662 108.176 93.7692 107.411 93.7692C106.649 93.7692 105.967 93.9662 105.365 94.3601C104.763 94.754 104.286 95.3222 103.933 96.0647C103.585 96.8071 103.411 97.701 103.411 98.7465C103.411 99.7881 103.585 100.68 103.933 101.423C104.282 102.165 104.757 102.735 105.359 103.133C105.962 103.527 106.645 103.724 107.411 103.724C108.176 103.724 108.859 103.527 109.462 103.133C110.068 102.739 110.545 102.171 110.893 101.428C111.242 100.682 111.414 99.7881 111.411 98.7465ZM124.128 96.5647H123.06C122.98 96.1669 122.836 95.7995 122.628 95.4624C122.423 95.1215 122.166 94.8241 121.855 94.5703C121.545 94.3165 121.192 94.1196 120.798 93.9794C120.404 93.8393 119.978 93.7692 119.52 93.7692C118.785 93.7692 118.115 93.9605 117.509 94.3431C116.906 94.7256 116.423 95.2881 116.06 96.0306C115.7 96.7692 115.52 97.6745 115.52 98.7465C115.52 99.826 115.7 100.735 116.06 101.474C116.423 102.212 116.906 102.773 117.509 103.156C118.115 103.534 118.785 103.724 119.52 103.724C119.978 103.724 120.404 103.654 120.798 103.514C121.192 103.373 121.545 103.178 121.855 102.928C122.166 102.674 122.423 102.377 122.628 102.036C122.836 101.695 122.98 101.326 123.06 100.928H124.128C124.033 101.462 123.855 101.96 123.594 102.423C123.336 102.881 123.007 103.282 122.605 103.627C122.207 103.972 121.749 104.241 121.23 104.434C120.711 104.627 120.141 104.724 119.52 104.724C118.543 104.724 117.675 104.479 116.918 103.991C116.16 103.498 115.565 102.805 115.134 101.911C114.705 101.017 114.491 99.9624 114.491 98.7465C114.491 97.5306 114.705 96.4756 115.134 95.5817C115.565 94.6878 116.16 93.9965 116.918 93.5078C117.675 93.0154 118.543 92.7692 119.52 92.7692C120.141 92.7692 120.711 92.8658 121.23 93.059C121.749 93.2484 122.207 93.5173 122.605 93.8658C123.007 94.2105 123.336 94.612 123.594 95.0703C123.855 95.5287 124.033 96.0268 124.128 96.5647ZM130.874 104.565V103.786L134.675 99.5135C135.171 98.9529 135.571 98.4737 135.874 98.076C136.177 97.6783 136.396 97.3109 136.533 96.9737C136.673 96.6366 136.743 96.2862 136.743 95.9226C136.743 95.4794 136.639 95.0931 136.43 94.7635C136.226 94.4302 135.944 94.1707 135.584 93.9851C135.224 93.7995 134.819 93.7067 134.368 93.7067C133.891 93.7067 133.472 93.809 133.112 94.0135C132.756 94.2181 132.48 94.4984 132.283 94.8544C132.086 95.2105 131.987 95.6196 131.987 96.0817H130.97C130.97 95.4378 131.118 94.8677 131.413 94.3715C131.713 93.8715 132.12 93.4794 132.635 93.1953C133.154 92.9112 133.741 92.7692 134.396 92.7692C135.044 92.7692 135.62 92.9093 136.124 93.1897C136.627 93.4662 137.023 93.8431 137.311 94.3203C137.599 94.7976 137.743 95.3317 137.743 95.9226C137.743 96.3506 137.667 96.7654 137.516 97.1669C137.368 97.5684 137.112 98.0154 136.749 98.5078C136.385 99.0003 135.881 99.6025 135.237 100.315L132.351 103.536V103.61H138.107V104.565H130.874ZM144.685 93.8828V92.9283H153.122V93.8828H149.435V104.565H148.372V93.8828H144.685ZM155.009 107.837C154.808 107.837 154.62 107.818 154.446 107.781C154.272 107.743 154.134 107.701 154.031 107.656L154.304 106.764C154.649 106.87 154.955 106.907 155.224 106.877C155.493 106.851 155.732 106.729 155.94 106.514C156.149 106.301 156.334 105.976 156.497 105.536L156.815 104.65L153.605 95.8374H154.702L157.293 103.229H157.372L159.963 95.8374H161.06L157.366 105.945C157.211 106.366 157.02 106.716 156.793 106.996C156.565 107.281 156.302 107.491 156.003 107.627C155.707 107.767 155.376 107.837 155.009 107.837ZM163.038 107.837V95.8374H164.021V97.5022H164.141C164.262 97.2408 164.429 96.9737 164.641 96.701C164.853 96.4245 165.133 96.1915 165.482 96.0022C165.834 95.8128 166.277 95.7181 166.811 95.7181C167.531 95.7181 168.16 95.9075 168.697 96.2862C169.239 96.6612 169.66 97.1878 169.959 97.8658C170.262 98.54 170.413 99.326 170.413 100.224C170.413 101.125 170.262 101.915 169.959 102.593C169.66 103.271 169.239 103.799 168.697 104.178C168.16 104.557 167.535 104.746 166.822 104.746C166.296 104.746 165.855 104.652 165.499 104.462C165.146 104.273 164.86 104.04 164.641 103.764C164.425 103.483 164.258 103.21 164.141 102.945H164.05V107.837H163.038ZM164.038 100.207C164.038 100.911 164.143 101.536 164.351 102.082C164.563 102.623 164.866 103.049 165.26 103.36C165.658 103.667 166.139 103.82 166.703 103.82C167.283 103.82 167.771 103.661 168.169 103.343C168.571 103.021 168.875 102.587 169.084 102.042C169.296 101.496 169.402 100.885 169.402 100.207C169.402 99.5362 169.298 98.9321 169.089 98.3942C168.885 97.8563 168.582 97.4302 168.18 97.1158C167.779 96.7976 167.286 96.6385 166.703 96.6385C166.135 96.6385 165.652 96.7919 165.254 97.0987C164.857 97.4018 164.554 97.8222 164.345 98.3601C164.141 98.8942 164.038 99.5097 164.038 100.207ZM176.161 104.746C175.346 104.746 174.642 104.557 174.047 104.178C173.452 103.796 172.992 103.267 172.666 102.593C172.344 101.915 172.183 101.135 172.183 100.252C172.183 99.3734 172.344 98.5931 172.666 97.9112C172.992 97.2256 173.441 96.6897 174.013 96.3033C174.589 95.9131 175.253 95.7181 176.007 95.7181C176.481 95.7181 176.937 95.8052 177.376 95.9794C177.816 96.1499 178.21 96.4131 178.558 96.7692C178.911 97.1215 179.189 97.5665 179.393 98.1044C179.598 98.6385 179.7 99.2711 179.7 100.002V100.502H172.882V99.6101H178.666C178.666 99.0495 178.553 98.5457 178.325 98.0987C178.102 97.648 177.789 97.2919 177.388 97.0306C176.99 96.7692 176.53 96.6385 176.007 96.6385C175.454 96.6385 174.967 96.7862 174.547 97.0817C174.126 97.3772 173.797 97.7673 173.558 98.2522C173.323 98.737 173.204 99.2673 173.2 99.8431V100.377C173.2 101.07 173.32 101.676 173.558 102.195C173.801 102.71 174.143 103.11 174.587 103.394C175.03 103.678 175.554 103.82 176.161 103.82C176.573 103.82 176.935 103.756 177.246 103.627C177.56 103.498 177.823 103.326 178.036 103.11C178.251 102.89 178.414 102.65 178.524 102.389L179.484 102.701C179.352 103.068 179.134 103.407 178.831 103.718C178.532 104.029 178.157 104.279 177.706 104.468C177.259 104.654 176.744 104.746 176.161 104.746ZM187.702 92.9283V104.565H186.639V92.9283H187.702ZM191.858 92.9283V104.565H190.795V92.9283H191.858Z"
          fill="url(#paint0_linear_4346_100010)"
        />
        <path
          d="M90.8516 115.565H193.852"
          stroke={resolvedTheme?.includes('dark') ? '#F3F3F3' : 'black'}
          strokeOpacity="0.1"
          strokeWidth="0.845575"
        />
        <path
          d="M97.6961 128.263H97.0285C96.9788 128.015 96.8888 127.785 96.7586 127.574C96.6308 127.361 96.4698 127.175 96.2757 127.017C96.0815 126.858 95.8614 126.735 95.6151 126.647C95.3689 126.56 95.1026 126.516 94.8161 126.516C94.3569 126.516 93.9378 126.636 93.559 126.875C93.1826 127.114 92.8808 127.465 92.6535 127.929C92.4286 128.391 92.3161 128.957 92.3161 129.627C92.3161 130.302 92.4286 130.87 92.6535 131.331C92.8808 131.793 93.1826 132.143 93.559 132.383C93.9378 132.619 94.3569 132.738 94.8161 132.738C95.1026 132.738 95.3689 132.694 95.6151 132.606C95.8614 132.519 96.0815 132.397 96.2757 132.241C96.4698 132.082 96.6308 131.896 96.7586 131.683C96.8888 131.47 96.9788 131.239 97.0285 130.991H97.6961C97.6369 131.324 97.5257 131.636 97.3623 131.924C97.2013 132.211 96.9954 132.462 96.7444 132.677C96.4958 132.893 96.2094 133.061 95.885 133.182C95.5607 133.302 95.2044 133.363 94.8161 133.363C94.2053 133.363 93.6632 133.21 93.1897 132.905C92.7162 132.597 92.3445 132.164 92.0747 131.605C91.8071 131.046 91.6734 130.387 91.6734 129.627C91.6734 128.867 91.8071 128.208 92.0747 127.649C92.3445 127.09 92.7162 126.658 93.1897 126.353C93.6632 126.045 94.2053 125.891 94.8161 125.891C95.2044 125.891 95.5607 125.951 95.885 126.072C96.2094 126.191 96.4958 126.359 96.7444 126.576C96.9954 126.792 97.2013 127.043 97.3623 127.329C97.5257 127.616 97.6369 127.927 97.6961 128.263ZM101.33 133.377C100.821 133.377 100.38 133.258 100.009 133.022C99.6371 132.783 99.3495 132.452 99.1459 132.031C98.9446 131.607 98.844 131.12 98.844 130.568C98.844 130.019 98.9446 129.531 99.1459 129.105C99.3495 128.676 99.63 128.341 99.9875 128.1C100.347 127.856 100.763 127.734 101.234 127.734C101.53 127.734 101.815 127.789 102.09 127.897C102.364 128.004 102.611 128.169 102.828 128.391C103.049 128.611 103.223 128.889 103.35 129.226C103.478 129.559 103.542 129.955 103.542 130.412V130.724H99.2808V130.167H102.896C102.896 129.816 102.825 129.501 102.683 129.222C102.543 128.94 102.348 128.718 102.097 128.554C101.848 128.391 101.561 128.309 101.234 128.309C100.888 128.309 100.584 128.402 100.321 128.586C100.059 128.771 99.8525 129.015 99.7034 129.318C99.5566 129.621 99.482 129.952 99.4797 130.312V130.646C99.4797 131.079 99.5542 131.458 99.7034 131.782C99.8549 132.104 100.069 132.354 100.346 132.532C100.623 132.709 100.951 132.798 101.33 132.798C101.588 132.798 101.814 132.758 102.008 132.677C102.205 132.597 102.369 132.489 102.502 132.354C102.637 132.217 102.738 132.066 102.807 131.903L103.407 132.098C103.324 132.328 103.188 132.54 102.999 132.734C102.812 132.928 102.577 133.084 102.296 133.203C102.016 133.319 101.694 133.377 101.33 133.377ZM104.935 133.263V127.809H105.549V128.647H105.596C105.705 128.372 105.894 128.151 106.164 127.983C106.436 127.812 106.744 127.727 107.087 127.727C107.139 127.727 107.197 127.728 107.261 127.731C107.325 127.733 107.378 127.735 107.421 127.738V128.38C107.392 128.376 107.343 128.369 107.272 128.359C107.201 128.35 107.124 128.345 107.041 128.345C106.757 128.345 106.504 128.405 106.281 128.526C106.061 128.644 105.887 128.809 105.759 129.02C105.631 129.23 105.567 129.471 105.567 129.741V133.263H104.935ZM110.99 127.809V128.359H108.387V127.809H110.99ZM109.2 126.502H109.836V131.839C109.836 132.066 109.875 132.245 109.953 132.375C110.031 132.503 110.133 132.594 110.258 132.649C110.384 132.701 110.517 132.727 110.66 132.727C110.742 132.727 110.813 132.722 110.873 132.713C110.932 132.701 110.984 132.689 111.029 132.677L111.164 133.249C111.102 133.273 111.026 133.294 110.937 133.313C110.847 133.334 110.735 133.345 110.603 133.345C110.371 133.345 110.147 133.294 109.932 133.192C109.718 133.09 109.543 132.939 109.406 132.738C109.269 132.536 109.2 132.287 109.2 131.988V126.502ZM112.432 133.263V127.809H113.068V133.263H112.432ZM112.756 126.871C112.625 126.871 112.514 126.827 112.422 126.74C112.329 126.65 112.283 126.542 112.283 126.417C112.283 126.291 112.329 126.185 112.422 126.097C112.514 126.007 112.625 125.962 112.756 125.962C112.886 125.962 112.997 126.007 113.089 126.097C113.182 126.185 113.228 126.291 113.228 126.417C113.228 126.542 113.182 126.65 113.089 126.74C112.997 126.827 112.886 126.871 112.756 126.871ZM116.984 127.809V128.359H114.303V127.809H116.984ZM115.141 133.263V127.01C115.141 126.716 115.209 126.469 115.343 126.267C115.481 126.066 115.66 125.914 115.88 125.809C116.1 125.705 116.333 125.653 116.579 125.653C116.745 125.653 116.882 125.667 116.991 125.696C117.102 125.722 117.19 125.748 117.254 125.774L117.069 126.328C117.022 126.314 116.964 126.297 116.895 126.278C116.827 126.259 116.74 126.25 116.636 126.25C116.361 126.25 116.15 126.327 116 126.481C115.851 126.634 115.777 126.855 115.777 127.141L115.773 133.263H115.141ZM118.253 133.263V127.809H118.888V133.263H118.253ZM118.576 126.871C118.446 126.871 118.334 126.827 118.242 126.74C118.15 126.65 118.104 126.542 118.104 126.417C118.104 126.291 118.15 126.185 118.242 126.097C118.334 126.007 118.446 125.962 118.576 125.962C118.706 125.962 118.817 126.007 118.91 126.097C119.002 126.185 119.048 126.291 119.048 126.417C119.048 126.542 119.002 126.65 118.91 126.74C118.817 126.827 118.706 126.871 118.576 126.871ZM122.765 133.377C122.256 133.377 121.816 133.258 121.444 133.022C121.073 132.783 120.785 132.452 120.581 132.031C120.38 131.607 120.28 131.12 120.28 130.568C120.28 130.019 120.38 129.531 120.581 129.105C120.785 128.676 121.066 128.341 121.423 128.1C121.783 127.856 122.198 127.734 122.669 127.734C122.965 127.734 123.251 127.789 123.525 127.897C123.8 128.004 124.046 128.169 124.264 128.391C124.484 128.611 124.658 128.889 124.786 129.226C124.914 129.559 124.978 129.955 124.978 130.412V130.724H120.716V130.167H124.331C124.331 129.816 124.26 129.501 124.118 129.222C123.979 128.94 123.783 128.718 123.532 128.554C123.284 128.391 122.996 128.309 122.669 128.309C122.324 128.309 122.02 128.402 121.757 128.586C121.494 128.771 121.288 129.015 121.139 129.318C120.992 129.621 120.918 129.952 120.915 130.312V130.646C120.915 131.079 120.99 131.458 121.139 131.782C121.29 132.104 121.505 132.354 121.782 132.532C122.059 132.709 122.387 132.798 122.765 132.798C123.023 132.798 123.249 132.758 123.444 132.677C123.64 132.597 123.805 132.489 123.937 132.354C124.072 132.217 124.174 132.066 124.243 131.903L124.843 132.098C124.76 132.328 124.624 132.54 124.434 132.734C124.247 132.928 124.013 133.084 123.731 133.203C123.452 133.319 123.13 133.377 122.765 133.377ZM128.334 133.377C127.887 133.377 127.495 133.258 127.159 133.022C126.823 132.785 126.56 132.455 126.371 132.031C126.184 131.607 126.09 131.114 126.09 130.55C126.09 129.989 126.184 129.498 126.371 129.076C126.56 128.653 126.824 128.324 127.163 128.089C127.501 127.852 127.895 127.734 128.345 127.734C128.677 127.734 128.952 127.793 129.172 127.912C129.393 128.028 129.569 128.172 129.702 128.345C129.834 128.518 129.937 128.686 130.011 128.849H130.067V125.991H130.703V133.263H130.085V132.251H130.011C129.937 132.417 129.833 132.587 129.698 132.763C129.563 132.935 129.384 133.081 129.162 133.199C128.942 133.318 128.666 133.377 128.334 133.377ZM128.409 132.798C128.762 132.798 129.062 132.702 129.311 132.51C129.56 132.316 129.749 132.05 129.879 131.711C130.012 131.37 130.078 130.98 130.078 130.54C130.078 130.104 130.013 129.719 129.883 129.385C129.753 129.049 129.563 128.786 129.315 128.597C129.066 128.405 128.764 128.309 128.409 128.309C128.044 128.309 127.737 128.409 127.486 128.608C127.237 128.804 127.048 129.071 126.918 129.407C126.79 129.743 126.726 130.12 126.726 130.54C126.726 130.963 126.791 131.346 126.921 131.687C127.051 132.027 127.241 132.299 127.489 132.5C127.74 132.699 128.047 132.798 128.409 132.798Z"
          fill={resolvedTheme?.includes('dark') ? 'white' : 'black'}
        />
        <rect
          x="177.543"
          y="121.565"
          width="16.3076"
          height="15.3972"
          rx="7.69858"
          fill={colors.iconBg}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M185.585 125.591C185.655 125.556 185.738 125.556 185.808 125.591L185.958 125.665C186.779 126.07 187.683 126.281 188.598 126.281C188.738 126.281 188.851 126.394 188.851 126.533L188.851 129.2C188.851 130.331 188.27 131.384 187.313 131.988L185.831 132.923C185.749 132.975 185.644 132.975 185.562 132.923L184.08 131.988C183.123 131.384 182.543 130.331 182.543 129.2V126.533C182.543 126.466 182.57 126.402 182.617 126.355C182.664 126.307 182.728 126.281 182.795 126.281C183.711 126.281 184.614 126.07 185.435 125.665L185.585 125.591ZM185.697 126.098L185.659 126.117C184.844 126.519 183.954 126.745 183.048 126.78V129.2C183.048 130.158 183.539 131.05 184.35 131.561L185.697 132.411L187.044 131.561C187.854 131.05 188.346 130.158 188.346 129.2L188.346 126.78C187.44 126.745 186.55 126.519 185.735 126.117L185.697 126.098ZM187.522 127.72C187.621 127.819 187.621 127.978 187.522 128.077L185.527 130.072C185.429 130.17 185.269 130.17 185.171 130.072L184.088 128.989C183.989 128.89 183.989 128.73 184.088 128.632C184.186 128.533 184.346 128.533 184.445 128.632L185.349 129.536L187.165 127.72C187.264 127.622 187.423 127.622 187.522 127.72Z"
          fill={colors.smallIcon}
        />
        <rect
          x="73.3496"
          y="80.0647"
          width="138"
          height="64.3972"
          rx="7.5"
          stroke="url(#paint1_linear_4346_100010)"
        />
      </g>
      {/* main shield behind */}
      <g filter="url(#filter14_d_4346_100010)">
        <path
          d="M196.447 62.6772V62.2856L196.067 62.1918L142.472 48.9662L142.352 48.9366L142.232 48.9662L88.638 62.1918L88.2578 62.2856V62.6772V79.5726V79.6817C88.2578 102.224 88.2578 113.615 91.3636 123.937C94.3525 133.871 99.4179 143.057 106.224 150.887C113.296 159.022 122.929 165.101 141.993 177.131L142.085 177.189L142.334 177.346L142.592 177.205L142.592 177.205L142.606 177.197L142.619 177.189L142.712 177.13C161.775 165.101 171.408 159.022 178.48 150.887C185.287 143.057 190.352 133.871 193.341 123.937C196.447 113.615 196.447 102.224 196.447 79.6818V79.5728V79.5726V62.6772Z"
          stroke="url(#paint3_linear_4346_100010)"
          shapeRendering="crispEdges"
        />
      </g>
      {/* main shield behind glow */}
      <g filter="url(#filter13_f_4346_100010)">
        <path
          d="M195.947 79.5726V62.6772L142.352 49.4516L88.7578 62.6772V79.5726C88.7578 102.218 88.7578 113.541 91.8424 123.793C94.8109 133.659 99.8417 142.783 106.601 150.559C113.625 158.638 123.201 164.681 142.352 176.766L142.352 176.766C161.504 164.681 171.079 158.638 178.103 150.559C184.863 142.783 189.894 133.659 192.862 123.793C195.947 113.541 195.947 102.218 195.947 79.5728V79.5726Z"
          stroke="url(#paint2_radial_4346_100010)"
        />
      </g>
      <defs>
        <filter
          id="filter0_b_4346_100010"
          x="61.637"
          y="19.4393"
          width="161.437"
          height="187.505"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_b_4346_100010"
          x="54.4964"
          y="10.9577"
          width="175.718"
          height="204.431"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_b_4346_100010"
          x="47.3558"
          y="2.47605"
          width="189.999"
          height="221.395"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter3_b_4346_100010"
          x="40.2152"
          y="-6.00551"
          width="204.281"
          height="238.358"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter4_b_4346_100010"
          x="32.3167"
          y="-15.3878"
          width="220.077"
          height="257.148"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter5_b_4346_100010"
          x="24.2073"
          y="-25.0207"
          width="236.3"
          height="276.398"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter6_b_4346_100010"
          x="16.098"
          y="-34.6536"
          width="252.519"
          height="295.654"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter7_b_4346_100010"
          x="7.39485"
          y="-44.9914"
          width="269.925"
          height="316.329"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter8_b_4346_100010"
          x="-1.70476"
          y="-55.799"
          width="288.126"
          height="337.931"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter9_b_4346_100010"
          x="-11.3766"
          y="-67.2868"
          width="307.47"
          height="360.92"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter10_b_4346_100010"
          x="-21.6716"
          y="-79.5146"
          width="328.058"
          height="385.413"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="5.51156" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter11_f_4346_100010"
          x="-149.761"
          y="-168.233"
          width="585.914"
          height="584.903"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="104.006" result="effect1_foregroundBlur_4346_100010" />
        </filter>
        <filter
          id="filter12_b_4346_100010"
          x="69.4673"
          y="76.1824"
          width="145.767"
          height="72.1618"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="1.69115" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_4346_100010"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_4346_100010"
            result="shape"
          />
        </filter>
        <filter
          id="filter13_f_4346_100010"
          x="85.2578"
          y="45.9366"
          width="114.188"
          height="134.421"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="1.5" result="effect1_foregroundBlur_4346_100010" />
        </filter>
        <filter
          id="filter14_d_4346_100010"
          x="80.7578"
          y="41.4216"
          width="123.188"
          height="143.504"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="3.5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.95 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4346_100010" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4346_100010"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_4346_100010"
          x1="142.352"
          y1="87.5647"
          x2="142.352"
          y2="131.024"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={resolvedTheme?.includes('dark') ? 'white' : '#1CB199'} />
          <stop
            offset="1"
            stopColor={resolvedTheme?.includes('dark') ? 'white' : '#494949'}
            stopOpacity="0"
          />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4346_100010"
          x1="142.35"
          y1="74.3898"
          x2="142.35"
          y2="155.033"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            stopColor={resolvedTheme?.includes('dark') ? '#1DF7C3' : '#3AFFE5'}
            stopOpacity="0.86"
          />
          <stop
            offset="1"
            stopColor={resolvedTheme?.includes('dark') ? '#161616' : '#38BBAA'}
            stopOpacity="0"
          />
        </linearGradient>
        <radialGradient
          id="paint2_radial_4346_100010"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(144.569 41.4294) rotate(90) scale(84.0463 63.1218)"
        >
          <stop stopColor={resolvedTheme?.includes('dark') ? '#3AFFE5' : '#109079'} />
          <stop
            offset="1"
            stopColor={resolvedTheme?.includes('dark') ? '#38BBAA' : '#3DCB8C'}
            stopOpacity="0"
          />
        </radialGradient>
        <linearGradient
          id="paint3_linear_4346_100010"
          x1="142.352"
          y1="49.4516"
          x2="142.352"
          y2="79.1828"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={resolvedTheme?.includes('dark') ? '#1CF7C3' : '#109179'} />
          <stop
            offset="1"
            stopColor={resolvedTheme?.includes('dark') ? '#3DCB8C' : '#0B0B0B'}
            stopOpacity="0"
          />
        </linearGradient>
      </defs>
    </svg>
  )

  return <RenderedSVG />
}

export default SecureAndScalableImg
