import React from 'react'
import { m } from 'framer-motion'
import { DEFAULT_EASE } from '~/lib/animations'

const CustomersVisual = () => {
  const RenderedVisual = () => (
    <>
      <div
        className="absolute inset-0 mx-auto aspect-[5.13/1] h-full z-10"
        style={{
          background: `radial-gradient(50% 50% at 50% 50%, transparent, hsl(var(--background-alternative-default))`,
        }}
      />
      <m.svg
        width="100%"
        height="100%"
        viewBox="0 0 1755 342"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="relative z-0"
      >
        <path
          d="M20.3457 257.968L867.188 112.08L1718.69 250.968"
          stroke="url(#paint0_radial_4353_100693)"
          strokeWidth="1.1397"
        />
        <path
          d="M20.3457 115.831L867.188 141.264L1718.69 115.831"
          stroke="url(#paint1_radial_4353_100693)"
          strokeWidth="1.1397"
        />
        <path
          d="M345.188 60.1399L868.079 141.264L1393.85 60.1399"
          stroke="url(#paint2_radial_4353_100693)"
          strokeWidth="1.1397"
        />
        <path
          d="M232.902 86.2314L867.771 142.755L1506.13 86.2314"
          stroke="url(#paint3_radial_4353_100693)"
          strokeWidth="1.1397"
        />
        <path
          d="M295.096 150.689L867.941 136.029L1443.94 150.689"
          stroke="url(#paint4_radial_4353_100693)"
          strokeWidth="1.1397"
        />
        <g filter="url(#filter0_f_4353_100693)">
          <ellipse
            cx="864.594"
            cy="151.574"
            rx="271.124"
            ry="82.5656"
            fill="hsl(var(--background-alternative-default))"
          />
        </g>
        <path
          d="M321.689 212.498H1423.53L1024.59 146.214C989.707 140.418 958.375 121.477 937.034 93.2866L934.698 90.2009C917.88 67.9838 891.625 54.9292 863.76 54.9292C836.691 54.9292 811.094 67.2518 794.212 88.4107L790.055 93.6202C767.659 121.689 735.757 140.582 700.377 146.727L321.689 212.498Z"
          fill="url(#paint5_radial_4353_100693)"
        />
        {/* center green main line */}
        <path
          d="M1754 215.262L1019.22 134.195C986.915 130.631 957.348 114.393 937.009 89.0478V89.0478C919.7 67.4787 893.538 54.9292 865.882 54.9292H864.945C838.067 54.9292 812.559 66.7854 795.231 87.3316V87.3316C773.48 113.122 742.819 129.783 709.345 134L0.744141 223.277"
          stroke="url(#paint6_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <path
          d="M1754 215.262L1019.22 134.195C986.915 130.631 957.348 114.393 937.009 89.0478V89.0478C919.7 67.4787 893.538 54.9292 865.882 54.9292H864.945C838.067 54.9292 812.559 66.7854 795.231 87.3316V87.3316C773.48 113.122 742.819 129.783 709.345 134L0.744141 223.277"
          stroke="url(#paint6_linear_4353_100693_anim)"
          strokeWidth="1.1397"
        />
        <path
          d="M1727.67 263.927L1014.73 144.526C981.347 138.936 951.627 120.131 932.281 92.3602V92.3602C915.796 68.697 890.879 54.9292 864.541 54.9292H863.648C838.05 54.9292 813.756 67.9365 797.253 90.4774V90.4774C776.583 118.709 745.778 137.834 711.308 143.834L234.791 226.787"
          stroke="url(#paint7_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <path
          d="M1026.15 165.96L1730.54 308.606M702.311 164.135L34.3711 308.606M993.53 165.96L1526.68 308.606M745.614 164.135L238.233 308.606"
          stroke="url(#paint8_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <g filter="url(#filter1_f_4353_100693)">
          <path
            d="M761.402 127.441L795.229 87.3316C812.557 66.7854 838.066 54.9292 864.944 54.9292H865.881C893.537 54.9292 919.698 67.4787 937.008 89.0478L968.771 128.629"
            stroke="url(#paint9_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        {/* top green */}
        <g filter="url(#filter2_f_4353_100693)">
          <path
            d="M930.203 82.036C914.556 60.4668 890.905 48.1616 865.905 48.1616H865.057C840.76 48.1616 817.7 60.0178 802.035 80.564C818.898 64.5075 841.409 55.49 864.944 55.49H865.882C890.122 55.49 913.214 65.1309 930.203 82.036Z"
            fill="hsl(var(--brand-default))"
          />
        </g>
        <g filter="url(#filter3_f_4353_100693)">
          <path
            d="M944.969 69.0081C925.715 47.439 896.616 35.1338 865.854 35.1338H864.811C834.915 35.1338 806.542 46.99 787.268 67.5362C808.016 51.4797 835.714 39.2051 864.673 39.2051H865.826C895.651 39.2051 924.064 52.1031 944.969 69.0081Z"
            fill="hsl(var(--background-default))"
          />
        </g>
        {/* logo bg rays */}
        <path
          d="M864.407 72.3096V204.318M930.411 138.314L798.402 138.314M921.568 105.312L807.245 171.316M921.567 171.316L807.245 105.312"
          stroke="url(#paint10_radial_4353_100693)"
          strokeWidth="1.1397"
        />
        {/* Vector logo */}
        <path
          d="M849.206 126.548C849.11 126.603 849.052 126.705 849.052 126.815C849.052 126.925 849.11 127.027 849.206 127.082L864.249 135.768C864.345 135.823 864.463 135.823 864.558 135.768L879.602 127.082C879.698 127.027 879.757 126.925 879.757 126.815C879.757 126.704 879.698 126.602 879.602 126.547L871.273 121.738C870.277 121.163 869.936 119.889 870.511 118.893C871.086 117.897 872.36 117.556 873.356 118.131L885.172 124.953C885.816 125.325 886.213 126.013 886.213 126.757V140.621C886.213 141.771 885.281 142.704 884.131 142.704C882.98 142.704 882.048 141.771 882.048 140.621V131.015C882.048 130.904 881.989 130.802 881.894 130.747C881.798 130.692 881.68 130.692 881.585 130.747L866.641 139.375C866.545 139.43 866.486 139.532 866.486 139.642V156.782C866.486 156.892 866.545 156.994 866.641 157.049C866.736 157.104 866.854 157.104 866.949 157.049L875.575 152.069C876.571 151.494 877.845 151.835 878.42 152.831C878.995 153.828 878.654 155.101 877.658 155.676L865.446 162.727C864.802 163.099 864.008 163.099 863.363 162.727L850.917 155.541C849.92 154.966 849.579 153.692 850.154 152.696C850.729 151.7 852.003 151.358 852.999 151.933L861.858 157.048C861.953 157.103 862.071 157.103 862.167 157.048C862.262 156.993 862.321 156.891 862.321 156.781V139.642C862.321 139.532 862.262 139.43 862.167 139.375L847.224 130.748C847.129 130.693 847.011 130.693 846.916 130.748C846.82 130.803 846.761 130.905 846.761 131.016V140.502C846.761 141.652 845.829 142.584 844.679 142.584C843.528 142.584 842.596 141.652 842.596 140.502V126.949H842.596L842.596 126.939C842.594 126.894 842.594 126.848 842.596 126.802L842.596 126.802V126.792V126.757C842.596 126.013 842.993 125.325 843.637 124.953L855.453 118.131C856.449 117.556 857.723 117.897 858.298 118.893C858.873 119.889 858.532 121.163 857.536 121.738L849.206 126.548ZM864.405 108.276C865.555 108.276 866.487 109.208 866.487 110.359V124.758C866.487 125.909 865.555 126.841 864.405 126.841C863.255 126.841 862.322 125.909 862.322 124.758V110.359C862.322 109.208 863.255 108.276 864.405 108.276ZM874.406 144.134C874.981 143.138 876.255 142.797 877.251 143.372L890.883 151.242C891.879 151.817 892.22 153.091 891.645 154.087C891.07 155.083 889.796 155.424 888.8 154.849L875.168 146.979C874.172 146.404 873.831 145.13 874.406 144.134ZM853.031 144.52C853.606 145.516 853.264 146.789 852.268 147.365L840.011 154.441C839.015 155.016 837.741 154.675 837.166 153.679C836.591 152.683 836.932 151.409 837.929 150.834L850.186 143.757C851.182 143.182 852.455 143.523 853.031 144.52Z"
          fill="url(#paint11_linear_4353_100693)"
          stroke="url(#paint12_linear_4353_100693)"
          strokeWidth="0.617486"
          strokeMiterlimit="10"
          strokeLinejoin="round"
        />
        {/* logo side archs */}
        <circle
          cx="864.593"
          cy="138.671"
          r="65.9995"
          stroke="url(#paint13_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <circle
          cx="864.595"
          cy="138.672"
          r="65.9995"
          transform="rotate(-180 864.595 138.672)"
          stroke="url(#paint14_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <circle
          cx="864.595"
          cy="138.671"
          r="65.9995"
          transform="rotate(90 864.595 138.671)"
          stroke="url(#paint15_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <circle
          cx="864.593"
          cy="138.672"
          r="65.9995"
          transform="rotate(-90 864.593 138.672)"
          stroke="url(#paint16_linear_4353_100693)"
          strokeWidth="1.1397"
        />
        <g filter="url(#filter4_f_4353_100693)">
          <circle
            cx="864.593"
            cy="138.671"
            r="65.9995"
            stroke="url(#paint17_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter5_f_4353_100693)">
          <circle
            cx="864.595"
            cy="138.672"
            r="65.9995"
            transform="rotate(-180 864.595 138.672)"
            stroke="url(#paint18_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter6_f_4353_100693)">
          <circle
            cx="864.595"
            cy="138.671"
            r="65.9995"
            transform="rotate(90 864.595 138.671)"
            stroke="url(#paint19_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter7_f_4353_100693)">
          <circle
            cx="864.593"
            cy="138.672"
            r="65.9995"
            transform="rotate(-90 864.593 138.672)"
            stroke="url(#paint20_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter8_f_4353_100693)">
          <circle
            cx="864.593"
            cy="138.671"
            r="65.9995"
            stroke="url(#paint21_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter9_f_4353_100693)">
          <circle
            cx="864.595"
            cy="138.672"
            r="65.9995"
            transform="rotate(-180 864.595 138.672)"
            stroke="url(#paint22_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter10_f_4353_100693)">
          <circle
            cx="864.595"
            cy="138.671"
            r="65.9995"
            transform="rotate(90 864.595 138.671)"
            stroke="url(#paint23_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        {/* bg overlays */}
        <g filter="url(#filter11_f_4353_100693)">
          <circle
            cx="864.593"
            cy="138.672"
            r="65.9995"
            transform="rotate(-90 864.593 138.672)"
            stroke="url(#paint24_linear_4353_100693)"
            strokeWidth="1.1397"
          />
        </g>
        <g filter="url(#filter12_f_4353_100693)">
          <ellipse
            cx="303.093"
            cy="68.9323"
            rx="303.093"
            ry="68.9323"
            transform="matrix(1 -0.000779452 -0.000779452 -1 562 296.168)"
            fill="hsl(var(--background-alternative-default))"
          />
        </g>
        {/* coordinates */}
        <path
          opacity="0.5"
          d="M1390.2 92.5413L1388.66 92.7L1387.9 85.2755L1389.43 85.1169L1389.49 85.7216L1388.61 85.8129L1389.25 92.0358L1390.14 91.9445L1390.2 92.5413ZM1390.62 90.7926C1390.59 90.4822 1390.8 90.2109 1391.11 90.1788C1391.42 90.1468 1391.7 90.368 1391.73 90.6783C1391.76 90.9886 1391.54 91.2529 1391.23 91.2849C1390.92 91.317 1390.65 91.1029 1390.62 90.7926ZM1393.09 88.4467C1393.17 89.2345 1393.37 89.7768 1393.71 90.071C1393.94 90.2573 1394.2 90.3583 1394.57 90.3205C1394.94 90.2827 1395.18 90.129 1395.35 89.9016C1395.63 89.5433 1395.72 88.9714 1395.64 88.1836C1395.55 87.3958 1395.35 86.8536 1395.01 86.5594C1394.79 86.373 1394.52 86.272 1394.15 86.3098C1393.79 86.3477 1393.55 86.5013 1393.37 86.7287C1393.09 87.0871 1393.01 87.6589 1393.09 88.4467ZM1392.32 88.5265C1392.24 87.8182 1392.3 87.1534 1392.59 86.6087C1392.87 86.0729 1393.35 85.6932 1394.08 85.6175C1394.82 85.5419 1395.37 85.8147 1395.75 86.2815C1396.15 86.7554 1396.33 87.3956 1396.41 88.1039C1396.48 88.8121 1396.43 89.477 1396.14 90.0216C1395.86 90.5575 1395.37 90.9372 1394.64 91.0128C1393.91 91.0884 1393.36 90.8156 1392.97 90.3488C1392.58 89.8749 1392.39 89.2347 1392.32 88.5265ZM1397.17 89.2155L1397.89 88.9154C1398.01 89.5471 1398.52 89.9283 1399.17 89.8617C1399.78 89.7984 1400.23 89.3416 1400.16 88.705C1400.09 87.9967 1399.54 87.6921 1398.94 87.7537C1398.57 87.7916 1398.24 87.971 1398.04 88.2248C1397.76 88.1493 1397.52 88.0697 1397.24 88.0013L1397.65 85.3699L1400.37 85.0887L1400.44 85.8049L1398.24 86.0326L1398.01 87.5522C1398.21 87.2737 1398.61 87.1037 1399.02 87.0617C1400.03 86.9573 1400.84 87.5254 1400.95 88.5917C1401.05 89.5626 1400.38 90.4277 1399.24 90.5461C1398.14 90.6595 1397.32 90.0198 1397.17 89.2155ZM1401.82 89.6356C1401.79 89.3491 1402.01 89.0769 1402.35 89.0424C1402.69 89.0071 1403.02 89.2305 1403.07 89.7318C1403.18 90.7822 1402.54 91.2906 1402.05 91.4052L1402.01 90.9914C1402.39 90.8636 1402.61 90.4465 1402.58 90.0637C1402.56 90.0821 1402.5 90.1126 1402.41 90.1216C1402.12 90.152 1401.86 89.9778 1401.82 89.6356ZM1406.15 89.1884C1406.12 88.878 1406.33 88.6067 1406.64 88.5746C1406.95 88.5425 1407.22 88.7637 1407.26 89.0741C1407.29 89.3844 1407.07 89.6486 1406.76 89.6807C1406.44 89.7128 1406.18 89.4987 1406.15 89.1884ZM1407.63 88.3759L1407.54 87.4767L1409.43 84.1526L1410.5 84.0416L1410.84 87.3122L1411.72 87.221L1411.8 87.9451L1410.92 88.0364L1411.04 89.1982L1410.28 89.2763L1410.16 88.1145L1407.63 88.3759ZM1410.09 87.3903L1409.82 84.8678L1408.27 87.5778L1410.09 87.3903ZM1412.54 88.5276C1412.52 88.2411 1412.74 87.9689 1413.07 87.9344C1413.41 87.899 1413.74 88.1224 1413.79 88.6238C1413.9 89.6742 1413.26 90.1825 1412.78 90.2972L1412.73 89.8834C1413.11 89.7556 1413.34 89.3385 1413.31 88.9557C1413.28 88.9741 1413.22 89.0046 1413.13 89.0136C1412.84 89.044 1412.58 88.8697 1412.54 88.5276ZM1416.87 88.0803C1416.84 87.77 1417.05 87.4986 1417.37 87.4666C1417.68 87.4345 1417.95 87.6557 1417.98 87.9661C1418.01 88.2764 1417.79 88.5406 1417.48 88.5727C1417.17 88.6048 1416.91 88.3907 1416.87 88.0803ZM1419.34 85.7345C1419.42 86.5223 1419.62 87.0645 1419.97 87.3587C1420.19 87.5451 1420.45 87.6461 1420.82 87.6082C1421.19 87.5704 1421.43 87.4168 1421.61 87.1893C1421.88 86.831 1421.97 86.2592 1421.89 85.4714C1421.8 84.6836 1421.6 84.1413 1421.26 83.8471C1421.04 83.6608 1420.77 83.5598 1420.41 83.5976C1420.04 83.6354 1419.8 83.7891 1419.62 84.0165C1419.34 84.3748 1419.26 84.9467 1419.34 85.7345ZM1418.57 85.8142C1418.49 85.106 1418.55 84.4411 1418.84 83.8965C1419.12 83.3606 1419.6 82.9809 1420.33 82.9053C1421.07 82.8296 1421.62 83.1025 1422 83.5693C1422.4 84.0432 1422.58 84.6834 1422.66 85.3916C1422.73 86.0999 1422.68 86.7647 1422.39 87.3094C1422.11 87.8452 1421.62 88.2249 1420.89 88.3006C1420.16 88.3762 1419.61 88.1034 1419.22 87.6366C1418.83 87.1626 1418.64 86.5225 1418.57 85.8142ZM1424.11 84.5102L1423.32 84.4953C1423.3 84.4245 1423.29 84.3529 1423.29 84.2892C1423.19 83.4059 1423.77 82.5507 1424.9 82.434C1426 82.3197 1426.71 82.978 1426.8 83.8375C1426.87 84.5139 1426.55 85.0945 1426 85.5293L1425.04 86.2871C1424.77 86.5085 1424.53 86.7505 1424.49 87.0762L1427.14 86.8024L1427.22 87.5265L1423.6 87.8998C1423.53 87.0546 1423.8 86.3755 1424.62 85.7201L1425.42 85.0743C1425.87 84.7138 1426.06 84.3247 1426.02 83.9268C1425.97 83.4573 1425.6 83.0525 1424.95 83.12C1424.27 83.1898 1424 83.6922 1424.07 84.2811C1424.07 84.3527 1424.09 84.4315 1424.11 84.5102ZM1429.23 88.5083L1427.7 88.667L1427.64 88.0702L1428.52 87.9789L1427.88 81.7561L1426.99 81.8473L1426.93 81.2425L1428.47 81.0839L1429.23 88.5083Z"
          fill="url(#paint25_linear_4353_100693)"
          fillOpacity="0.37"
        />
        <path
          opacity="0.5"
          d="M187.134 202.473L185.597 202.625L184.861 195.198L186.397 195.046L186.457 195.651L185.574 195.738L186.191 201.964L187.075 201.876L187.134 202.473ZM187.564 200.726C187.534 200.416 187.748 200.145 188.058 200.114C188.369 200.084 188.64 200.306 188.671 200.616C188.702 200.927 188.479 201.19 188.168 201.221C187.858 201.252 187.595 201.037 187.564 200.726ZM191.603 198.863L189.445 199.076L189.38 198.424L191.538 198.21L191.603 198.863ZM193.072 198.09C193.15 198.878 193.349 199.421 193.692 199.717C193.911 199.904 194.179 200.006 194.545 199.97C194.911 199.933 195.153 199.781 195.332 199.554C195.61 199.197 195.698 198.625 195.62 197.837C195.541 197.049 195.343 196.506 195 196.21C194.78 196.023 194.513 195.921 194.147 195.957C193.781 195.994 193.539 196.146 193.36 196.373C193.082 196.73 192.994 197.302 193.072 198.09ZM192.3 198.166C192.23 197.458 192.284 196.793 192.576 196.25C192.861 195.715 193.346 195.337 194.078 195.265C194.811 195.192 195.36 195.467 195.745 195.935C196.137 196.411 196.322 197.052 196.392 197.76C196.462 198.469 196.407 199.134 196.116 199.677C195.831 200.212 195.346 200.59 194.613 200.662C193.881 200.735 193.331 200.46 192.947 199.991C192.554 199.516 192.37 198.875 192.3 198.166ZM197.148 198.875L197.875 198.578C197.986 199.21 198.498 199.594 199.143 199.53C199.756 199.469 200.209 199.014 200.146 198.377C200.076 197.669 199.523 197.362 198.926 197.421C198.559 197.457 198.223 197.635 198.023 197.888C197.742 197.811 197.501 197.731 197.229 197.661L197.644 195.032L200.366 194.762L200.437 195.478L198.232 195.697L197.997 197.215C198.202 196.938 198.604 196.769 199.01 196.729C200.021 196.629 200.825 197.2 200.931 198.267C201.027 199.238 200.357 200.101 199.211 200.214C198.112 200.323 197.301 199.68 197.148 198.875ZM201.799 199.314C201.771 199.028 201.993 198.757 202.327 198.723C202.67 198.689 202.998 198.914 203.047 199.416C203.152 200.467 202.51 200.972 202.023 201.085L201.982 200.671C202.363 200.545 202.587 200.129 202.557 199.746C202.535 199.764 202.474 199.794 202.386 199.803C202.092 199.832 201.833 199.657 201.799 199.314ZM206.13 198.885C206.099 198.575 206.313 198.304 206.624 198.273C206.934 198.242 207.205 198.465 207.236 198.775C207.267 199.086 207.044 199.349 206.733 199.38C206.423 199.411 206.16 199.195 206.13 198.885ZM210.168 197.021L208.01 197.235L207.946 196.583L210.103 196.369L210.168 197.021ZM212.121 196.386L211.675 195.779L213.056 194.227L210.588 194.471L210.517 193.755L214.02 193.408L214.09 194.108L212.752 195.616C213.508 195.541 214.395 195.967 214.5 197.026C214.586 197.894 213.995 198.748 212.793 198.867C211.599 198.986 210.845 198.273 210.713 197.434L211.46 197.183C211.56 197.864 212.096 198.245 212.717 198.183C213.426 198.113 213.764 197.637 213.713 197.12C213.646 196.443 213.076 196.21 212.567 196.261C212.408 196.277 212.251 196.324 212.121 196.386ZM215.397 197.966C215.368 197.679 215.59 197.408 215.925 197.375C216.267 197.341 216.595 197.566 216.645 198.067C216.749 199.118 216.108 199.624 215.62 199.737L215.579 199.323C215.961 199.196 216.185 198.78 216.155 198.397C216.133 198.416 216.071 198.446 215.984 198.454C215.689 198.484 215.431 198.308 215.397 197.966ZM219.727 197.537C219.696 197.226 219.91 196.956 220.221 196.925C220.531 196.894 220.803 197.116 220.833 197.427C220.864 197.737 220.641 198.001 220.331 198.031C220.02 198.062 219.758 197.847 219.727 197.537ZM223.765 195.673L221.608 195.887L221.543 195.234L223.7 195.02L223.765 195.673ZM225.235 194.9C225.313 195.688 225.511 196.231 225.854 196.527C226.074 196.714 226.341 196.816 226.707 196.78C227.074 196.744 227.316 196.591 227.494 196.364C227.772 196.007 227.86 195.436 227.782 194.648C227.704 193.859 227.505 193.316 227.163 193.021C226.943 192.834 226.676 192.731 226.309 192.768C225.943 192.804 225.701 192.957 225.523 193.183C225.244 193.541 225.156 194.112 225.235 194.9ZM224.462 194.977C224.392 194.268 224.447 193.604 224.739 193.06C225.023 192.525 225.508 192.148 226.241 192.075C226.973 192.002 227.523 192.278 227.907 192.746C228.3 193.221 228.484 193.862 228.554 194.571C228.625 195.279 228.57 195.944 228.278 196.488C227.993 197.022 227.508 197.4 226.776 197.473C226.044 197.545 225.494 197.27 225.11 196.802C224.717 196.326 224.533 195.685 224.462 194.977ZM232.856 191.709L232.68 192.369C232.478 192.277 232.125 192.199 231.735 192.238C230.66 192.344 230.061 193.2 230.103 194.265C230.276 193.902 230.713 193.529 231.398 193.461C232.417 193.36 233.228 193.923 233.339 195.045C233.449 196.152 232.614 196.894 231.603 196.994C230.616 197.092 229.521 196.509 229.352 194.805C229.162 192.887 230.208 191.682 231.713 191.532C232.183 191.486 232.6 191.565 232.856 191.709ZM230.322 195.344C230.389 196.021 230.979 196.372 231.544 196.316C232.149 196.256 232.619 195.808 232.551 195.123C232.483 194.431 231.926 194.084 231.329 194.143C230.732 194.202 230.254 194.652 230.322 195.344ZM235.482 197.678L233.946 197.831L233.887 197.234L234.77 197.146L234.153 190.921L233.269 191.008L233.209 190.403L234.746 190.251L235.482 197.678Z"
          fill="url(#paint26_linear_4353_100693)"
          fillOpacity="0.37"
        />
        <path
          opacity="0.5"
          d="M610.997 185.336L609.478 185.613L608.141 178.269L609.66 177.993L609.769 178.591L608.895 178.75L610.016 184.905L610.89 184.746L610.997 185.336ZM611.284 183.56C611.228 183.253 611.42 182.966 611.727 182.91C612.034 182.854 612.322 183.054 612.378 183.361C612.434 183.668 612.233 183.948 611.926 184.004C611.619 184.06 611.34 183.867 611.284 183.56ZM615.158 181.375L613.025 181.763L612.907 181.118L615.04 180.729L615.158 181.375ZM616.56 180.485C616.702 181.264 616.944 181.79 617.309 182.056C617.543 182.225 617.818 182.305 618.18 182.239C618.542 182.173 618.771 182.002 618.931 181.761C619.179 181.383 619.22 180.806 619.078 180.027C618.937 179.248 618.695 178.722 618.329 178.456C618.095 178.287 617.82 178.207 617.458 178.273C617.096 178.338 616.867 178.51 616.708 178.751C616.459 179.129 616.418 179.706 616.56 180.485ZM615.796 180.624C615.669 179.924 615.669 179.257 615.916 178.692C616.156 178.135 616.609 177.72 617.333 177.588C618.057 177.456 618.628 177.686 619.049 178.121C619.479 178.563 619.714 179.187 619.842 179.888C619.969 180.588 619.969 181.255 619.722 181.82C619.482 182.376 619.029 182.792 618.305 182.924C617.581 183.056 617.01 182.826 616.59 182.391C616.159 181.949 615.924 181.325 615.796 180.624ZM622.851 181.974L622.096 182.112L621.441 178.515L620.205 178.74L620.106 178.197C620.812 178.052 621.228 177.57 621.246 176.997L621.923 176.874L622.851 181.974ZM623.82 181.278C623.769 180.994 623.968 180.706 624.299 180.646C624.637 180.584 624.982 180.781 625.073 181.277C625.262 182.316 624.664 182.872 624.187 183.024L624.113 182.615C624.483 182.458 624.672 182.025 624.611 181.646C624.59 181.666 624.532 181.701 624.445 181.717C624.154 181.77 623.882 181.616 623.82 181.278ZM628.102 180.498C628.046 180.191 628.237 179.904 628.544 179.848C628.851 179.792 629.14 179.992 629.196 180.299C629.251 180.606 629.05 180.886 628.743 180.942C628.437 180.998 628.157 180.805 628.102 180.498ZM631.975 178.313L629.842 178.701L629.725 178.056L631.858 177.668L631.975 178.313ZM633.2 176.716L632.411 176.762C632.39 176.692 632.377 176.622 632.366 176.559C632.207 175.685 632.71 174.788 633.828 174.585C634.922 174.386 635.682 174.987 635.837 175.837C635.959 176.506 635.678 177.11 635.163 177.586L634.273 178.415C634.016 178.657 633.795 178.917 633.782 179.244L636.403 178.767L636.533 179.484L632.96 180.134C632.824 179.297 633.038 178.599 633.802 177.883L634.552 177.177C634.976 176.783 635.131 176.381 635.059 175.987C634.975 175.523 634.581 175.147 633.935 175.265C633.266 175.386 633.036 175.908 633.142 176.49C633.155 176.561 633.177 176.639 633.2 176.716ZM637.31 178.822C637.258 178.538 637.458 178.25 637.788 178.19C638.127 178.128 638.471 178.326 638.562 178.821C638.751 179.86 638.153 180.417 637.676 180.568L637.602 180.159C637.972 180.002 638.161 179.569 638.1 179.19C638.08 179.21 638.021 179.245 637.934 179.261C637.643 179.314 637.371 179.16 637.31 178.822ZM641.591 178.042C641.535 177.735 641.727 177.448 642.034 177.393C642.34 177.337 642.629 177.536 642.685 177.843C642.741 178.15 642.54 178.431 642.233 178.487C641.926 178.542 641.647 178.349 641.591 178.042ZM645.464 175.857L643.331 176.245L643.214 175.6L645.347 175.212L645.464 175.857ZM646.866 174.968C647.008 175.747 647.25 176.272 647.616 176.539C647.85 176.708 648.125 176.788 648.487 176.722C648.849 176.656 649.078 176.484 649.237 176.244C649.485 175.865 649.527 175.288 649.385 174.509C649.243 173.73 649.001 173.205 648.635 172.938C648.401 172.769 648.127 172.689 647.765 172.755C647.403 172.821 647.174 172.993 647.014 173.233C646.766 173.612 646.724 174.188 646.866 174.968ZM646.103 175.107C645.975 174.406 645.976 173.739 646.223 173.174C646.463 172.618 646.916 172.202 647.64 172.07C648.364 171.938 648.934 172.168 649.355 172.604C649.785 173.046 650.021 173.67 650.148 174.37C650.276 175.071 650.275 175.738 650.029 176.303C649.788 176.859 649.336 177.275 648.611 177.407C647.887 177.538 647.317 177.309 646.896 176.873C646.466 176.431 646.23 175.807 646.103 175.107ZM651.523 173.38L650.735 173.426C650.714 173.356 650.701 173.286 650.69 173.223C650.531 172.349 651.034 171.452 652.152 171.249C653.246 171.05 654.006 171.651 654.161 172.501C654.282 173.17 654.002 173.774 653.487 174.25L652.597 175.079C652.34 175.321 652.119 175.581 652.106 175.908L654.727 175.431L654.857 176.148L651.284 176.798C651.148 175.961 651.362 175.263 652.126 174.547L652.876 173.841C653.3 173.447 653.454 173.045 653.383 172.651C653.298 172.187 652.905 171.811 652.259 171.929C651.59 172.05 651.36 172.572 651.466 173.154C651.479 173.225 651.501 173.303 651.523 173.38ZM656.945 176.971L655.426 177.247L655.318 176.657L656.192 176.498L655.071 170.343L654.198 170.502L654.089 169.904L655.608 169.628L656.945 176.971Z"
          fill="url(#paint27_linear_4353_100693)"
          fillOpacity="0.37"
        />
        <path
          opacity="0.5"
          d="M1318.45 230.369L1316.93 230.072L1318.37 222.748L1319.89 223.046L1319.77 223.642L1318.9 223.471L1317.69 229.609L1318.56 229.781L1318.45 230.369ZM1319.36 228.821C1319.42 228.515 1319.71 228.318 1320.01 228.378C1320.32 228.438 1320.51 228.729 1320.45 229.036C1320.39 229.342 1320.1 229.529 1319.8 229.469C1319.49 229.409 1319.3 229.127 1319.36 228.821ZM1322.41 227.3C1322.25 228.077 1322.29 228.654 1322.53 229.036C1322.69 229.279 1322.91 229.454 1323.27 229.525C1323.64 229.596 1323.91 229.519 1324.15 229.354C1324.52 229.092 1324.77 228.571 1324.92 227.793C1325.07 227.016 1325.04 226.439 1324.8 226.057C1324.64 225.814 1324.41 225.639 1324.05 225.568C1323.69 225.497 1323.41 225.574 1323.18 225.739C1322.81 226.001 1322.56 226.523 1322.41 227.3ZM1321.65 227.15C1321.78 226.451 1322.03 225.831 1322.46 225.395C1322.89 224.965 1323.46 224.744 1324.19 224.886C1324.91 225.027 1325.36 225.45 1325.59 226.009C1325.83 226.578 1325.82 227.244 1325.68 227.943C1325.54 228.642 1325.3 229.262 1324.86 229.698C1324.44 230.128 1323.86 230.35 1323.14 230.208C1322.42 230.066 1321.97 229.644 1321.74 229.084C1321.5 228.516 1321.51 227.849 1321.65 227.15ZM1327.77 228.541L1327.52 227.831L1329.29 226.744L1326.85 226.266L1326.99 225.559L1330.45 226.238L1330.31 226.929L1328.59 227.986C1329.34 228.132 1330.06 228.797 1329.86 229.841C1329.69 230.696 1328.88 231.344 1327.69 231.111C1326.52 230.88 1326 229.979 1326.12 229.138L1326.9 229.113C1326.8 229.794 1327.21 230.314 1327.82 230.434C1328.52 230.572 1328.98 230.214 1329.08 229.704C1329.21 229.037 1328.73 228.649 1328.23 228.55C1328.07 228.519 1327.91 228.52 1327.77 228.541ZM1330.45 231C1330.5 230.717 1330.79 230.522 1331.12 230.587C1331.46 230.653 1331.71 230.963 1331.61 231.457C1331.41 232.493 1330.65 232.792 1330.15 232.76L1330.23 232.351C1330.63 232.341 1330.97 232.007 1331.05 231.632C1331.02 231.643 1330.95 231.654 1330.87 231.637C1330.58 231.58 1330.38 231.337 1330.45 231ZM1334.72 231.839C1334.78 231.533 1335.06 231.336 1335.37 231.396C1335.67 231.456 1335.87 231.747 1335.81 232.054C1335.75 232.36 1335.46 232.547 1335.15 232.487C1334.85 232.427 1334.66 232.145 1334.72 231.839ZM1338.49 230.649L1338.24 229.939L1340.01 228.852L1337.58 228.374L1337.72 227.667L1341.17 228.346L1341.03 229.037L1339.32 230.094C1340.06 230.24 1340.79 230.905 1340.58 231.949C1340.42 232.804 1339.6 233.452 1338.42 233.219C1337.24 232.988 1336.73 232.087 1336.84 231.246L1337.63 231.221C1337.53 231.902 1337.93 232.422 1338.54 232.542C1339.24 232.68 1339.7 232.322 1339.8 231.812C1339.94 231.144 1339.46 230.757 1338.95 230.658C1338.8 230.627 1338.63 230.628 1338.49 230.649ZM1341.17 233.108C1341.23 232.825 1341.52 232.63 1341.85 232.694C1342.19 232.761 1342.43 233.071 1342.34 233.565C1342.13 234.601 1341.37 234.9 1340.87 234.868L1340.95 234.459C1341.36 234.449 1341.69 234.115 1341.77 233.74C1341.75 233.751 1341.68 233.762 1341.59 233.745C1341.3 233.688 1341.11 233.445 1341.17 233.108ZM1345.44 233.947C1345.5 233.641 1345.78 233.444 1346.09 233.504C1346.4 233.564 1346.59 233.855 1346.53 234.162C1346.47 234.468 1346.18 234.655 1345.88 234.595C1345.57 234.535 1345.38 234.253 1345.44 233.947ZM1348.49 232.426C1348.33 233.203 1348.37 233.78 1348.61 234.162C1348.77 234.405 1348.99 234.58 1349.35 234.651C1349.71 234.722 1349.99 234.645 1350.23 234.48C1350.6 234.218 1350.85 233.697 1351 232.919C1351.15 232.142 1351.12 231.565 1350.87 231.183C1350.72 230.94 1350.49 230.765 1350.13 230.694C1349.77 230.623 1349.49 230.7 1349.26 230.865C1348.89 231.127 1348.64 231.649 1348.49 232.426ZM1347.72 232.276C1347.86 231.577 1348.11 230.957 1348.54 230.521C1348.97 230.091 1349.54 229.87 1350.27 230.011C1350.99 230.153 1351.43 230.576 1351.67 231.135C1351.91 231.704 1351.9 232.37 1351.76 233.069C1351.62 233.768 1351.38 234.388 1350.94 234.824C1350.51 235.254 1349.94 235.476 1349.22 235.334C1348.5 235.192 1348.05 234.77 1347.82 234.21C1347.58 233.641 1347.59 232.975 1347.72 232.276ZM1353.85 233.667L1353.59 232.957L1355.37 231.87L1352.93 231.392L1353.07 230.685L1356.52 231.364L1356.39 232.055L1354.67 233.112C1355.42 233.258 1356.14 233.923 1355.94 234.967C1355.77 235.822 1354.96 236.47 1353.77 236.237C1352.6 236.006 1352.08 235.105 1352.2 234.264L1352.98 234.239C1352.88 234.92 1353.29 235.44 1353.9 235.56C1354.6 235.698 1355.06 235.34 1355.16 234.83C1355.29 234.163 1354.81 233.775 1354.31 233.676C1354.15 233.645 1353.99 233.646 1353.85 233.667ZM1357.28 238.003L1355.77 237.705L1355.88 237.116L1356.75 237.287L1357.96 231.149L1357.09 230.977L1357.2 230.381L1358.72 230.679L1357.28 238.003Z"
          fill="url(#paint28_linear_4353_100693)"
          fillOpacity="0.37"
        />
        <path
          opacity="0.5"
          d="M440.449 67.7204L438.926 67.4619L440.176 60.1032L441.698 60.3617L441.596 60.9611L440.721 60.8125L439.674 66.9802L440.549 67.1288L440.449 67.7204ZM441.325 66.1489C441.377 65.8413 441.655 65.6369 441.963 65.6892C442.27 65.7414 442.473 66.0274 442.421 66.335C442.369 66.6426 442.084 66.8377 441.777 66.7855C441.469 66.7333 441.272 66.4565 441.325 66.1489ZM445.712 65.4169L443.574 65.054L443.684 64.4072L445.821 64.7702L445.712 65.4169ZM447.333 65.0593C447.2 65.8401 447.249 66.4163 447.501 66.7919C447.664 67.0305 447.895 67.1995 448.257 67.2611C448.62 67.3227 448.894 67.2394 449.126 67.0678C449.488 66.7967 449.724 66.2687 449.857 65.4879C449.989 64.707 449.941 64.1308 449.688 63.7552C449.526 63.5167 449.295 63.3476 448.932 63.286C448.57 63.2244 448.296 63.3078 448.064 63.4793C447.701 63.7505 447.465 64.2785 447.333 65.0593ZM446.568 64.9294C446.687 64.2274 446.915 63.6008 447.34 63.1536C447.756 62.713 448.323 62.4766 449.049 62.5998C449.774 62.7231 450.232 63.1335 450.479 63.6866C450.733 64.249 450.741 64.9158 450.622 65.6178C450.503 66.3197 450.275 66.9464 449.85 67.3936C449.434 67.8342 448.866 68.0705 448.141 67.9473C447.415 67.8241 446.957 67.4137 446.711 66.8605C446.457 66.2982 446.449 65.6313 446.568 64.9294ZM452.724 66.1613L452.453 65.4581L454.196 64.3258L451.751 63.9106L451.871 63.2008L455.341 63.79L455.224 64.4841L453.535 65.585C454.285 65.7123 455.027 66.3577 454.849 67.4067C454.703 68.2663 453.908 68.9347 452.717 68.7325C451.534 68.5316 450.995 67.6448 451.089 66.8007L451.876 66.7558C451.792 67.4394 452.209 67.9484 452.824 68.0528C453.526 68.172 453.978 67.8025 454.066 67.2898C454.179 66.6194 453.691 66.2444 453.186 66.1587C453.029 66.1319 452.866 66.1367 452.724 66.1613ZM455.466 68.5501C455.514 68.2662 455.8 68.0632 456.132 68.1194C456.471 68.177 456.728 68.4803 456.643 68.9772C456.466 70.0183 455.714 70.3369 455.215 70.3169L455.284 69.9068C455.686 69.8857 456.011 69.5434 456.084 69.1662C456.057 69.1779 455.99 69.1909 455.903 69.1761C455.612 69.1266 455.408 68.8892 455.466 68.5501ZM459.756 69.2786C459.808 68.971 460.087 68.7666 460.394 68.8189C460.702 68.8711 460.905 69.1571 460.853 69.4647C460.8 69.7723 460.516 69.9674 460.208 69.9152C459.9 69.8629 459.704 69.5862 459.756 69.2786ZM464.143 68.5466L462.006 68.1837L462.115 67.5369L464.253 67.8999L464.143 68.5466ZM466.21 70.8937L465.452 70.7651L466.065 67.1607L464.826 66.9505L464.919 66.4062C465.631 66.511 466.187 66.1997 466.399 65.6677L467.077 65.7828L466.21 70.8937ZM467.358 70.5694C467.407 70.2855 467.693 70.0825 468.024 70.1387C468.363 70.1963 468.62 70.4996 468.535 70.9965C468.359 72.0376 467.607 72.3562 467.107 72.3363L467.177 71.9261C467.578 71.905 467.904 71.5627 467.976 71.1855C467.95 71.1972 467.882 71.2102 467.796 71.1955C467.504 71.1459 467.301 70.9086 467.358 70.5694ZM471.648 71.2979C471.701 70.9903 471.979 70.786 472.286 70.8382C472.594 70.8904 472.797 71.1764 472.745 71.484C472.693 71.7916 472.408 71.9867 472.1 71.9345C471.793 71.8823 471.596 71.6055 471.648 71.2979ZM474.653 69.6982C474.52 70.4791 474.568 71.0553 474.821 71.4309C474.983 71.6694 475.214 71.8385 475.577 71.9001C475.94 71.9617 476.214 71.8783 476.446 71.7068C476.808 71.4356 477.044 70.9076 477.177 70.1268C477.309 69.346 477.261 68.7698 477.008 68.3942C476.846 68.1556 476.615 67.9866 476.252 67.925C475.889 67.8634 475.616 67.9467 475.384 68.1183C475.021 68.3894 474.785 68.9174 474.653 69.6982ZM473.888 69.5683C474.007 68.8664 474.235 68.2397 474.66 67.7925C475.076 67.3519 475.643 67.1156 476.369 67.2388C477.094 67.362 477.552 67.7724 477.799 68.3256C478.052 68.8879 478.061 69.5548 477.942 70.2567C477.823 70.9587 477.594 71.5853 477.17 72.0325C476.754 72.4731 476.186 72.7095 475.461 72.5863C474.735 72.463 474.277 72.0526 474.03 71.4995C473.777 70.9371 473.769 70.2703 473.888 69.5683ZM480.058 73.2452L479.301 73.1166L479.913 69.5122L478.675 69.302L478.767 68.7578C479.48 68.8625 480.036 68.5512 480.248 68.0192L480.926 68.1344L480.058 73.2452ZM482.087 74.7906L480.564 74.5321L480.665 73.9406L481.54 74.0892L482.588 67.9215L481.712 67.7729L481.814 67.1734L483.336 67.4319L482.087 74.7906Z"
          fill="url(#paint29_linear_4353_100693)"
          fillOpacity="0.37"
        />
        <defs>
          <filter
            id="filter0_f_4353_100693"
            x="565.471"
            y="41.0081"
            width="598.248"
            height="221.131"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="14" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter1_f_4353_100693"
            x="756.408"
            y="49.8005"
            width="217.368"
            height="83.7442"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="2.2794" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter2_f_4353_100693"
            x="799.756"
            y="45.8822"
            width="132.727"
            height="38.4332"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="1.1397" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter3_f_4353_100693"
            x="784.988"
            y="32.8544"
            width="162.26"
            height="38.4332"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="1.1397" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter4_f_4353_100693"
            x="793.465"
            y="67.5432"
            width="142.256"
            height="142.256"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="2.2794" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter5_f_4353_100693"
            x="793.467"
            y="67.5436"
            width="142.256"
            height="142.256"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="2.2794" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter6_f_4353_100693"
            x="793.467"
            y="67.5432"
            width="142.256"
            height="142.256"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="2.2794" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter7_f_4353_100693"
            x="793.465"
            y="67.5434"
            width="142.256"
            height="142.256"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="2.2794" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter8_f_4353_100693"
            x="795.744"
            y="69.8226"
            width="137.697"
            height="137.698"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="1.1397" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter9_f_4353_100693"
            x="795.746"
            y="69.823"
            width="137.697"
            height="137.698"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="1.1397" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter10_f_4353_100693"
            x="795.746"
            y="69.8226"
            width="137.697"
            height="137.698"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="1.1397" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter11_f_4353_100693"
            x="795.744"
            y="69.8228"
            width="137.697"
            height="137.698"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="1.1397" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <filter
            id="filter12_f_4353_100693"
            x="516.357"
            y="112.479"
            width="697.362"
            height="229.042"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feGaussianBlur stdDeviation="22.794" result="effect1_foregroundBlur_4353_100693" />
          </filter>
          <radialGradient
            id="paint0_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(859.159 138.208) rotate(92.2122) scale(263.179 749.441)"
          >
            <stop stopColor="hsl(var(--foreground-default))" stopOpacity="0" />
            <stop offset="0.489999" stopColor="hsl(var(--foreground-muted))" stopOpacity="0.84" />
            <stop offset="1" stopColor="hsl(var(--background-default))" stopOpacity="0" />
          </radialGradient>
          <radialGradient
            id="paint1_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(859.159 136.974) rotate(-103.238) scale(44.3621 730.071)"
          >
            <stop stopColor="hsl(var(--foreground-default))" stopOpacity="0" />
            <stop offset="0.489999" stopColor="hsl(var(--foreground-muted))" stopOpacity="0.84" />
            <stop offset="1" stopColor="hsl(var(--background-default))" stopOpacity="0" />
          </radialGradient>
          <radialGradient
            id="paint2_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(863.122 127.579) rotate(-92.6074) scale(137.882 462.616)"
          >
            <stop stopColor="hsl(var(--foreground-default))" stopOpacity="0" />
            <stop offset="0.489999" stopColor="hsl(var(--foreground-muted))" stopOpacity="0.84" />
            <stop offset="1" stopColor="hsl(var(--background-default))" stopOpacity="0" />
          </radialGradient>
          <radialGradient
            id="paint3_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(861.752 133.22) rotate(-90) scale(115.815 675.283)"
          >
            <stop stopColor="hsl(var(--foreground-default))" stopOpacity="0" />
            <stop offset="0.592292" stopColor="hsl(var(--foreground-muted))" stopOpacity="0.84" />
            <stop offset="1" stopColor="hsl(var(--background-default))" stopOpacity="0" />
          </radialGradient>
          <radialGradient
            id="paint4_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(862.511 138.502) rotate(105.433) scale(25.8224 489.043)"
          >
            <stop stopColor="hsl(var(--foreground-default))" stopOpacity="0" />
            <stop offset="0.489999" stopColor="hsl(var(--foreground-light))" stopOpacity="0.84" />
            <stop offset="1" stopColor="hsl(var(--background-default))" stopOpacity="0" />
          </radialGradient>
          <radialGradient
            id="paint5_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(862.32 54.7715) rotate(90) scale(193.44 193.44)"
          >
            <stop stopColor="hsl(var(--background-default))" />
            <stop offset="1" stopColor="hsl(var(--background-alternative-default))" />
          </radialGradient>
          <m.linearGradient
            animate={{
              x2: [0 - 300, 1755 + 100],
              x1: [0, 1755 + 300],
              y1: [0, 342],
              y2: [0, 342],
            }}
            transition={{
              duration: 4,
              delay: 6,
              repeat: Infinity,
              ease: DEFAULT_EASE,
            }}
            id="paint6_linear_4353_100693_anim"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="transparent" stopOpacity="0" />
            <stop offset="0.5" stopColor="hsl(var(--brand-default))" stopOpacity="0.6" />
            <stop offset="1" stopColor="transparent" stopOpacity="0" />
          </m.linearGradient>
          <linearGradient
            id="paint6_linear_4353_100693"
            x1="865.086"
            y1="54.9292"
            x2="864.98"
            y2="193.79"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-default))" />
            <stop offset="1" stopColor="hsl(var(--background-alternative-default))" />
          </linearGradient>
          <linearGradient
            id="paint7_linear_4353_100693"
            x1="863.785"
            y1="54.9292"
            x2="863.651"
            y2="207.271"
            gradientUnits="userSpaceOnUse"
          >
            <stop
              offset="0.331593"
              stopColor="hsl(var(--background-alternative-default))"
              stopOpacity="0"
            />
            <stop offset="0.594681" stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="1" stopColor="hsl(var(--background-default))" />
          </linearGradient>
          <linearGradient
            id="paint8_linear_4353_100693"
            x1="860.732"
            y1="176.86"
            x2="862.471"
            y2="236.32"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.0114691" stopColor="hsl(var(--background-alternative-default))" />
            <stop offset="0.540433" stopColor="hsl(var(--brand-default))" />
            <stop offset="1" stopColor="hsl(var(--background-alternative-default))" />
          </linearGradient>
          <linearGradient
            id="paint9_linear_4353_100693"
            x1="865.087"
            y1="54.9292"
            x2="865.087"
            y2="105.386"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-default))" />
            <stop offset="1" stopColor="white" stopOpacity="0" />
          </linearGradient>
          <radialGradient
            id="paint10_radial_4353_100693"
            cx="0"
            cy="0"
            r="1"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(864.407 138.314) rotate(90) scale(55.0818)"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
            <stop offset="0.522658" stopColor="hsl(var(--border-default))" />
            <stop offset="1" stopColor="hsl(var(--background-overlay-default))" stopOpacity="0" />
          </radialGradient>
          <linearGradient
            id="paint11_linear_4353_100693"
            x1="864.406"
            y1="107.967"
            x2="864.406"
            y2="163.315"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-light))" />
            <stop
              offset="1"
              stopColor="hsl(var(--background-overlay-default))"
              stopOpacity="0.29"
            />
          </linearGradient>
          <linearGradient
            id="paint12_linear_4353_100693"
            x1="884.152"
            y1="122.715"
            x2="865.625"
            y2="163.788"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--background-default))" />
            <stop offset="1" stopColor="hsl(var(--foreground-light))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint13_linear_4353_100693"
            x1="864.593"
            y1="72.1021"
            x2="864.593"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-light))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint14_linear_4353_100693"
            x1="864.595"
            y1="72.1024"
            x2="864.595"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-light))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint15_linear_4353_100693"
            x1="864.595"
            y1="72.1021"
            x2="864.595"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-light))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint16_linear_4353_100693"
            x1="864.593"
            y1="72.1022"
            x2="864.593"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-light))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint17_linear_4353_100693"
            x1="864.593"
            y1="72.1021"
            x2="864.593"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint18_linear_4353_100693"
            x1="864.595"
            y1="72.1024"
            x2="864.595"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint19_linear_4353_100693"
            x1="864.595"
            y1="72.1021"
            x2="864.595"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint20_linear_4353_100693"
            x1="864.593"
            y1="72.1022"
            x2="864.593"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.3125" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint21_linear_4353_100693"
            x1="864.593"
            y1="72.1021"
            x2="864.593"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-default))" />
            <stop offset="0.15625" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint22_linear_4353_100693"
            x1="864.595"
            y1="72.1024"
            x2="864.595"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.15625" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint23_linear_4353_100693"
            x1="864.595"
            y1="72.1021"
            x2="864.595"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.15625" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint24_linear_4353_100693"
            x1="864.593"
            y1="72.1022"
            x2="864.593"
            y2="105.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--foreground-lighter))" />
            <stop offset="0.15625" stopColor="hsl(var(--foreground-lighter))" stopOpacity="0" />
          </linearGradient>
          <linearGradient
            id="paint25_linear_4353_100693"
            x1="1407.7"
            y1="78.4847"
            x2="1409.34"
            y2="94.3999"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-default))" />
            <stop offset="1" stopColor="hsl(var(--brand-default))" stopOpacity="1" />
          </linearGradient>
          <linearGradient
            id="paint26_linear_4353_100693"
            x1="209.338"
            y1="188.027"
            x2="210.917"
            y2="203.949"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-500))" />
            <stop offset="1" stopColor="hsl(var(--brand-default))" stopOpacity="1" />
          </linearGradient>
          <linearGradient
            id="paint27_linear_4353_100693"
            x1="631.029"
            y1="169.305"
            x2="633.895"
            y2="185.046"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-500))" />
            <stop offset="1" stopColor="hsl(var(--brand-default))" stopOpacity="1" />
          </linearGradient>
          <linearGradient
            id="paint28_linear_4353_100693"
            x1="1339.46"
            y1="222.082"
            x2="1336.37"
            y2="237.781"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-500))" />
            <stop offset="1" stopColor="hsl(var(--brand-default))" stopOpacity="1" />
          </linearGradient>
          <linearGradient
            id="paint29_linear_4353_100693"
            x1="462.547"
            y1="59.1142"
            x2="459.868"
            y2="74.8884"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="hsl(var(--brand-500))" />
            <stop offset="1" stopColor="hsl(var(--brand-default))" stopOpacity="1" />
          </linearGradient>
        </defs>
      </m.svg>
    </>
  )

  return <RenderedVisual />
}

export default CustomersVisual
