import React, { useEffect, useRef } from 'react'
import { useTheme } from 'next-themes'
import { DEFAULT_EASE } from '~/lib/animations'
import { LazyMotion, domAnimation, m } from 'framer-motion'

const PGvectorImg = ({ isHovered }: { isHovered: boolean }) => {
  const { resolvedTheme } = useTheme()
  const svgRef = useRef<any>()

  const colors = {
    circles1: resolvedTheme?.includes('dark') ? '#105C3B' : '#008B4F',
    circles2: resolvedTheme?.includes('dark') ? '#1A2520' : '#DDDFDE',
    pgLogoStart: resolvedTheme?.includes('dark') ? '#70F8BB' : '#7CDBC5',
    pgLogoEnd: resolvedTheme?.includes('dark') ? '#A3A3A3' : '#006136',
    pgLogoBg: resolvedTheme?.includes('dark') ? '#161616' : 'white',
    radialBgStart: resolvedTheme?.includes('dark') ? '#131313' : '#F1F3F5',
    radialBgEnd: resolvedTheme?.includes('dark') ? '#161616' : '#F8F9FA',
    coordinatesStart: resolvedTheme?.includes('dark') ? '#23FF98' : '#002313',
    coordinatesEnd: resolvedTheme?.includes('dark') ? '#49FFAA' : '#032C18',
    rectStart: resolvedTheme?.includes('dark') ? '#17FDDF' : '#49615E',
    rectEnd: resolvedTheme?.includes('dark') ? '#10FFE0' : '#202020',
    arrow: resolvedTheme?.includes('dark') ? '#02FF8F' : '#00542F',
    asdfStart: resolvedTheme?.includes('dark') ? '#23FF98' : '#002313',
    asdfEnd: resolvedTheme?.includes('dark') ? '#49FFAA' : '#032C18',
  }

  const transition = (type: string) => ({
    transition: `${type} 0.18s cubic-bezier(${DEFAULT_EASE})`,
  })

  const lineVariants = {
    animate: {
      strokeDashoffset: 38,
      transition: {
        ease: 'linear',
        duration: 3,
        yoyo: false,
        repeat: Infinity,
      },
    },

    initial: {
      strokeDashoffset: 0,
    },
  }

  useEffect(() => {
    const coordinates = svgRef.current.querySelector('#coordinates')
    const circles = svgRef.current.querySelector('#circles')
    const arrowtl = svgRef.current.querySelector('#arrow-tl')
    const arrowtr = svgRef.current.querySelector('#arrow-tr')
    const arrowbl = svgRef.current.querySelector('#arrow-bl')
    const arrowbr = svgRef.current.querySelector('#arrow-br')

    coordinates.style.opacity = isHovered ? '1' : '0.4'
    circles.style.opacity = isHovered ? '1' : '0.2'
    arrowtl.style.transform = isHovered ? 'translate3d(0,0,0)' : 'translate3d(10px,4px,0)'
    arrowtr.style.transform = isHovered ? 'translate3d(0,0,0)' : 'translate3d(-5px,2px,0)'
    arrowbl.style.transform = isHovered ? 'translate3d(0,0,0)' : 'translate3d(4px,-2px,0)'
    arrowbr.style.transform = isHovered ? 'translate3d(0,0,0)' : 'translate3d(-8px,-1px,0)'
  }, [isHovered])

  const RenderedSVG = () => (
    <svg
      ref={svgRef}
      width="100%"
      height="100%"
      viewBox="0 0 284 211"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="rectangles">
        <m.rect
          x="143.039"
          y="23.9514"
          width="235.466"
          height="117.627"
          rx="3"
          stroke="url(#paint0_radial_4346_99807)"
          strokeWidth="0.5"
          strokeDasharray="2.05 3.41"
          variants={lineVariants}
          initial={'initial'}
          animate={isHovered ? 'animate' : 'initial'}
        />
        <m.rect
          x="-39.0586"
          y="151.092"
          width="146.622"
          height="70.089"
          rx="3"
          stroke="url(#paint1_radial_4346_99807)"
          strokeWidth="0.5"
          strokeDasharray="2.05 3.41"
          variants={lineVariants}
          initial={'initial'}
          animate={isHovered ? 'animate' : 'initial'}
        />
        <m.rect
          x="-61.9512"
          y="-24.5244"
          width="144.514"
          height="110.236"
          rx="3"
          stroke="url(#paint2_radial_4346_99807)"
          strokeWidth="0.5"
          strokeDasharray="2.05 3.41"
          variants={lineVariants}
          initial={'initial'}
          animate={isHovered ? 'animate' : 'initial'}
        />
      </g>
      <path
        d="M102.12 21.5861L102.005 24.4706L104.56 23.1283L102.12 21.5861ZM122.178 59.2388L103.388 23.4619L102.945 23.6944L121.735 59.4712L122.178 59.2388Z"
        fill="url(#paint3_linear_4346_99807)"
      />
      <path
        d="M168.931 34.4283L166.381 35.7809L168.828 37.3132L168.931 34.4283ZM146.498 70.7159L167.949 36.4679L167.525 36.2025L146.074 70.4505L146.498 70.7159Z"
        fill="url(#paint4_linear_4346_99807)"
      />
      <path
        d="M-34.8744 131.287L-32.2163 132.413L-32.5702 129.548L-34.8744 131.287ZM53.3456 120.138L-32.672 130.763L-32.6107 131.259L53.4069 120.635L53.3456 120.138Z"
        fill="url(#paint5_linear_4346_99807)"
      />
      <path
        d="M25.7628 140.804L28.5573 141.529L27.7872 138.747L25.7628 140.804ZM81.9615 124.99L27.8646 139.963L27.998 140.445L82.0949 125.472L81.9615 124.99Z"
        fill="url(#paint6_linear_4346_99807)"
      />
      <path
        d="M0.902166 113.734L3.13731 115.561L3.60186 112.712L0.902166 113.734ZM34.0495 118.886L3.16307 113.85L3.08261 114.343L33.969 119.379L34.0495 118.886Z"
        fill="url(#paint7_linear_4346_99807)"
      />
      <path
        d="M214.22 162.817L212.597 160.43L211.341 163.03L214.22 162.817ZM183.908 148.45L212.086 162.064L212.303 161.614L184.125 148L183.908 148.45Z"
        fill="url(#paint8_linear_4346_99807)"
      />
      <path
        d="M291.251 175.737L289.496 173.445L288.389 176.111L291.251 175.737ZM224.422 148.251L289.077 175.105L289.269 174.643L224.613 147.789L224.422 148.251Z"
        fill="url(#paint9_linear_4346_99807)"
      />
      <path
        d="M280.556 66.6293L277.67 66.6719L279.15 69.1503L280.556 66.6293ZM246.828 87.062L278.752 67.9976L278.496 67.5683L246.572 86.6327L246.828 87.062Z"
        fill="url(#paint10_linear_4346_99807)"
      />
      <path
        d="M291.098 86.7516L288.408 85.7035L288.845 88.557L291.098 86.7516ZM166.636 106.074L288.912 87.3395L288.836 86.8453L166.561 105.58L166.636 106.074Z"
        fill="url(#paint11_linear_4346_99807)"
      />
      <g id="circles">
        <circle
          cx="52.3215"
          cy="64.3578"
          r="3.04773"
          transform="rotate(-9.50119 52.3215 64.3578)"
          stroke={colors.circles1}
        />
        <circle cx="262.54" cy="116.714" r="3.04773" stroke={colors.circles1} />
        <circle cx="227.565" cy="52.9802" r="3.04773" stroke={colors.circles1} />
        <circle cx="199.868" cy="113.08" r="3.04773" stroke={colors.circles2} />
        <circle
          cx="121.351"
          cy="137.441"
          r="3.04773"
          transform="rotate(-9.50118 121.351 137.441)"
          stroke={colors.circles2}
        />
        <circle
          cx="72.6067"
          cy="162.583"
          r="3.04773"
          transform="rotate(-9.5012 72.6067 162.583)"
          stroke={colors.circles1}
        />
      </g>
      <rect
        x="-57.6504"
        y="0.103638"
        width="400"
        height="210"
        fill="url(#paint12_radial_4346_99807)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M183.411 113.532C182.915 112.029 181.615 110.983 179.934 110.731C179.141 110.613 178.234 110.663 177.159 110.885C175.287 111.271 173.898 111.418 172.884 111.447C176.711 104.986 179.822 97.6186 181.613 90.6834C184.51 79.4696 182.962 74.361 181.153 72.0501C176.367 65.934 169.383 62.6482 160.958 62.5475C156.464 62.4926 152.518 63.38 150.46 64.018C148.543 63.6801 146.483 63.4913 144.32 63.4563C140.268 63.3917 136.687 64.2751 133.627 66.0908C131.933 65.5181 129.215 64.7106 126.075 64.1952C118.691 62.9829 112.739 63.9276 108.386 67.0028C103.115 70.7263 100.671 77.1956 101.123 86.2311C101.267 89.0999 102.871 97.8281 105.398 106.106C106.85 110.864 108.399 114.815 110.001 117.85C112.272 122.155 114.703 124.69 117.432 125.6C118.961 126.109 121.74 126.466 124.663 124.032C125.033 124.481 125.527 124.927 126.183 125.341C127.016 125.866 128.035 126.295 129.052 126.55C132.719 127.466 136.153 127.237 139.082 125.952C139.1 126.473 139.114 126.971 139.126 127.401C139.146 128.098 139.165 128.782 139.19 129.421C139.364 133.745 139.658 137.107 140.53 139.459C140.577 139.588 140.642 139.785 140.71 139.994C141.145 141.326 141.872 143.554 143.721 145.3C145.637 147.108 147.954 147.662 150.076 147.662C151.14 147.662 152.155 147.523 153.046 147.332C156.221 146.651 159.826 145.615 162.434 141.901C164.9 138.39 166.099 133.103 166.316 124.771C166.344 124.535 166.37 124.31 166.395 124.096C166.411 123.951 166.429 123.803 166.446 123.656L167.027 123.707L167.177 123.717C170.41 123.864 174.364 123.178 176.792 122.051C178.711 121.16 184.859 117.914 183.411 113.532Z"
        fill={colors.pgLogoBg}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M183.411 113.532C182.915 112.029 181.615 110.983 179.934 110.731C179.141 110.613 178.234 110.663 177.159 110.885C175.287 111.271 173.898 111.418 172.884 111.447C176.711 104.986 179.822 97.6186 181.613 90.6834C184.51 79.4696 182.962 74.361 181.153 72.0501C176.367 65.934 169.383 62.6482 160.958 62.5475C156.464 62.4926 152.518 63.38 150.46 64.018C148.543 63.6801 146.483 63.4913 144.32 63.4563C140.268 63.3917 136.687 64.2751 133.627 66.0908C131.933 65.5181 129.215 64.7106 126.075 64.1952C118.691 62.9829 112.739 63.9276 108.386 67.0028C103.115 70.7263 100.671 77.1956 101.123 86.2311C101.267 89.0999 102.871 97.8281 105.398 106.106C106.85 110.864 108.399 114.815 110.001 117.85C112.272 122.155 114.703 124.69 117.432 125.6C118.961 126.109 121.74 126.466 124.663 124.032C125.033 124.481 125.527 124.927 126.183 125.341C127.016 125.866 128.035 126.295 129.052 126.55C132.719 127.466 136.153 127.237 139.082 125.952C139.1 126.473 139.114 126.971 139.126 127.401C139.146 128.098 139.165 128.782 139.19 129.421C139.364 133.745 139.658 137.107 140.53 139.459C140.577 139.588 140.642 139.785 140.71 139.994C141.145 141.326 141.872 143.554 143.721 145.3C145.637 147.108 147.954 147.662 150.076 147.662C151.14 147.662 152.155 147.523 153.046 147.332C156.221 146.651 159.826 145.615 162.434 141.901C164.9 138.39 166.099 133.103 166.316 124.771C166.344 124.535 166.37 124.31 166.395 124.096C166.411 123.951 166.429 123.803 166.446 123.656L167.027 123.707L167.177 123.717C170.41 123.864 174.364 123.178 176.792 122.051C178.711 121.16 184.859 117.914 183.411 113.532ZM135.912 88.7858C135.046 88.6652 134.261 88.7769 133.864 89.0773C133.641 89.2465 133.572 89.4424 133.553 89.5773C133.504 89.9345 133.754 90.3294 133.908 90.5331C134.343 91.1098 134.978 91.5061 135.607 91.5935C135.699 91.6061 135.789 91.6122 135.879 91.6122C136.929 91.6122 137.882 90.7953 137.967 90.1923C138.072 89.4372 136.975 88.9338 135.912 88.7858ZM164.612 88.8077H164.612C164.529 88.2158 163.476 88.047 162.476 88.1859C161.478 88.3251 160.51 88.7756 160.591 89.3688C160.656 89.8302 161.488 90.6178 162.475 90.6176C162.558 90.6176 162.642 90.6121 162.727 90.6003C163.385 90.5091 163.868 90.0911 164.097 89.8501C164.447 89.4831 164.649 89.0737 164.612 88.8077ZM181.083 114.159C180.716 113.05 179.537 112.693 177.576 113.098C171.756 114.299 169.671 113.467 168.987 112.963C173.511 106.071 177.233 97.7399 179.241 89.9666C180.192 86.2845 180.717 82.8651 180.76 80.0781C180.807 77.0185 180.287 74.7707 179.212 73.3974C174.879 67.8613 168.521 64.8917 160.823 64.8101C155.532 64.7507 151.061 66.105 150.194 66.4857C148.369 66.0318 146.379 65.7532 144.213 65.7176C140.241 65.6534 136.807 66.6044 133.964 68.5428C132.729 68.0834 129.538 66.9878 125.635 66.3592C118.888 65.2727 113.526 66.0958 109.7 68.8066C105.134 72.0412 103.026 77.8232 103.435 85.9915C103.572 88.7399 105.138 97.194 107.609 105.291C110.862 115.947 114.398 121.979 118.118 123.22C118.553 123.366 119.056 123.467 119.609 123.467C120.966 123.467 122.63 122.855 124.361 120.774C127.237 117.315 129.922 114.416 130.911 113.363C132.374 114.148 133.98 114.586 135.624 114.63C135.627 114.673 135.631 114.716 135.635 114.759C135.305 115.15 135.035 115.493 134.804 115.785C133.666 117.231 133.429 117.531 129.764 118.286C128.721 118.501 125.952 119.072 125.912 121.013C125.867 123.133 129.184 124.024 129.562 124.118C130.88 124.448 132.148 124.611 133.358 124.611C136.301 124.611 138.891 123.643 140.96 121.772C140.897 129.332 141.212 136.782 142.12 139.051C142.863 140.909 144.679 145.451 150.416 145.45C151.258 145.45 152.185 145.352 153.204 145.134C159.191 143.85 161.791 141.204 162.797 135.37C163.335 132.252 164.259 124.807 164.693 120.813C165.61 121.099 166.79 121.23 168.066 121.23C170.727 121.23 173.798 120.664 175.724 119.77C177.887 118.766 181.791 116.3 181.083 114.159ZM166.823 87.1724C166.803 88.3514 166.641 89.4217 166.469 90.5391C166.284 91.7407 166.093 92.983 166.045 94.4912C165.997 95.9588 166.18 97.4848 166.358 98.9603C166.716 101.941 167.083 105.009 165.66 108.037C165.439 107.644 165.226 107.216 165.03 106.741C164.853 106.313 164.469 105.624 163.938 104.671C161.869 100.961 157.025 92.2735 159.505 88.7285C160.243 87.6731 162.118 86.5885 166.823 87.1724ZM161.119 67.1982C168.016 67.3506 173.471 69.9306 177.334 74.8662C180.297 78.6521 177.034 95.8783 167.591 110.739C167.497 110.621 167.402 110.501 167.304 110.378C167.265 110.329 167.225 110.279 167.185 110.229C169.625 106.199 169.148 102.211 168.723 98.6758C168.549 97.225 168.384 95.8546 168.426 94.5675C168.469 93.2029 168.649 92.033 168.824 90.9015C169.038 89.5071 169.256 88.0645 169.196 86.3636C169.241 86.1853 169.259 85.9745 169.236 85.7243C169.082 84.0932 167.22 79.2124 163.425 74.7942C161.349 72.378 158.321 69.6737 154.188 67.8498C155.966 67.4813 158.397 67.1377 161.119 67.1982ZM122.53 119.25C120.622 121.543 119.305 121.104 118.872 120.959C116.05 120.018 112.775 114.053 109.888 104.594C107.39 96.4093 105.93 88.1792 105.814 85.8713C105.45 78.5726 107.219 73.4858 111.073 70.7523C117.346 66.3038 127.659 68.9664 131.803 70.3169C131.744 70.3756 131.682 70.4306 131.623 70.4902C124.822 77.358 124.984 89.0916 125 89.8087C125 90.0855 125.023 90.4773 125.055 91.0163C125.172 92.9896 125.39 96.6627 124.808 100.822C124.267 104.688 125.459 108.471 128.076 111.202C128.347 111.485 128.63 111.75 128.923 112C127.757 113.248 125.225 116.007 122.53 119.25ZM129.794 109.558C127.684 107.356 126.726 104.294 127.165 101.156C127.779 96.7618 127.552 92.9349 127.43 90.8789C127.413 90.5913 127.398 90.3392 127.389 90.1402C128.383 89.2595 132.986 86.7926 136.269 87.5449C137.768 87.8882 138.68 88.9082 139.06 90.6631C141.024 99.748 139.32 103.535 137.951 106.577C137.668 107.204 137.402 107.797 137.174 108.41L136.998 108.884C136.551 110.082 136.135 111.196 135.877 112.254C133.635 112.247 131.453 111.289 129.794 109.558ZM130.14 121.806C129.485 121.643 128.896 121.359 128.551 121.123C128.839 120.987 129.353 120.802 130.244 120.619C134.556 119.731 135.221 119.105 136.675 117.259C137.009 116.835 137.387 116.355 137.91 115.771C137.91 115.771 137.91 115.77 137.911 115.77C138.69 114.898 139.047 115.045 139.693 115.314C140.217 115.531 140.728 116.187 140.935 116.91C141.032 117.251 141.143 117.9 140.783 118.404C137.745 122.656 133.319 122.602 130.14 121.806ZM152.7 142.809C147.426 143.94 145.558 141.248 144.328 138.172C143.533 136.185 143.143 127.229 143.42 117.338C143.424 117.206 143.405 117.079 143.369 116.959C143.337 116.729 143.289 116.495 143.221 116.26C142.809 114.821 141.806 113.617 140.602 113.118C140.123 112.92 139.245 112.556 138.19 112.826C138.415 111.899 138.805 110.852 139.228 109.719L139.406 109.242C139.606 108.704 139.856 108.147 140.122 107.558C141.555 104.374 143.517 100.013 141.387 90.162C140.589 86.4723 137.925 84.6706 133.886 85.0889C131.464 85.3395 129.249 86.3165 128.144 86.8767C127.907 86.9971 127.689 87.1133 127.486 87.2267C127.795 83.5089 128.96 76.5613 133.318 72.1655C136.062 69.398 139.717 68.0313 144.17 68.1049C152.944 68.2486 158.57 72.7512 161.745 76.5032C164.481 79.7364 165.963 82.9932 166.554 84.7497C162.107 84.2977 159.083 85.1755 157.55 87.3672C154.215 92.1346 159.375 101.388 161.854 105.835C162.309 106.65 162.702 107.355 162.825 107.654C163.633 109.611 164.678 110.918 165.442 111.872C165.675 112.164 165.903 112.447 166.075 112.695C164.728 113.083 162.309 113.98 162.53 118.464C162.352 120.714 161.087 131.248 160.445 134.97C159.597 139.887 157.788 141.719 152.7 142.809ZM174.718 117.608C173.341 118.248 171.036 118.727 168.847 118.83C166.429 118.943 165.198 118.559 164.909 118.323C164.772 115.529 165.813 115.237 166.913 114.928C167.086 114.879 167.255 114.832 167.418 114.775C167.519 114.857 167.63 114.939 167.752 115.019C169.695 116.302 173.162 116.44 178.056 115.43C178.073 115.426 178.091 115.423 178.109 115.419C177.449 116.036 176.32 116.865 174.718 117.608Z"
        fill="url(#pgLogo)"
        fillOpacity="1"
      />
      <path
        id="arrow-tl"
        style={transition('transform')}
        d="M58.7307 67.0865L62.1609 71.7306L64.4677 66.438L58.7307 67.0865ZM130.873 97.9845L63.0557 68.4261L62.6561 69.3429L130.474 98.9012L130.873 97.9845Z"
        fill="url(#paint14_linear_4346_99807)"
      />
      <path
        id="arrow-tr"
        style={transition('transform')}
        d="M217.486 59.3723L211.724 59.0012L214.284 64.1764L217.486 59.3723ZM188.36 74.3344L213.674 61.8153L213.231 60.919L187.917 73.438L188.36 74.3344Z"
        fill="url(#paint15_linear_4346_99807)"
      />
      <path
        id="arrow-br"
        style={transition('transform')}
        d="M255.662 116.714L250.847 113.528L250.496 119.291L255.662 116.714ZM204.542 114.096L251.14 116.939L251.201 115.94L204.603 113.098L204.542 114.096Z"
        fill="url(#paint16_linear_4346_99807)"
      />
      <path
        id="arrow-bl"
        style={transition('transform')}
        d="M79.7356 159.947L85.5045 160.177L82.8194 155.066L79.7356 159.947ZM132.609 131.605L83.4868 157.411L83.9518 158.296L133.074 132.491L132.609 131.605Z"
        fill="url(#paint17_linear_4346_99807)"
      />
      <g id="coordinates" style={transition('opacity')}>
        <path
          d="M16.6192 167.276H15.0752V159.812H16.6192V160.42H15.7312V166.676H16.6192V167.276ZM17.2198 165.58C17.2198 165.268 17.4598 165.02 17.7718 165.02C18.0838 165.02 18.3318 165.268 18.3318 165.58C18.3318 165.892 18.0838 166.132 17.7718 166.132C17.4598 166.132 17.2198 165.892 17.2198 165.58ZM21.4222 164.124H19.2542V163.468H21.4222V164.124ZM22.9608 163.5C22.9608 164.292 23.1048 164.852 23.4168 165.18C23.6168 165.388 23.8728 165.516 24.2408 165.516C24.6088 165.516 24.8648 165.388 25.0648 165.18C25.3768 164.852 25.5208 164.292 25.5208 163.5C25.5208 162.708 25.3768 162.148 25.0648 161.82C24.8648 161.612 24.6088 161.484 24.2408 161.484C23.8728 161.484 23.6168 161.612 23.4168 161.82C23.1048 162.148 22.9608 162.708 22.9608 163.5ZM22.1848 163.5C22.1848 162.788 22.3048 162.132 22.6488 161.62C22.9848 161.116 23.5048 160.788 24.2408 160.788C24.9768 160.788 25.4968 161.116 25.8328 161.62C26.1768 162.132 26.2968 162.788 26.2968 163.5C26.2968 164.212 26.1768 164.868 25.8328 165.38C25.4968 165.884 24.9768 166.212 24.2408 166.212C23.5048 166.212 22.9848 165.884 22.6488 165.38C22.3048 164.868 22.1848 164.212 22.1848 163.5ZM28.8838 166.092H28.1158V162.436H26.8598V161.884C27.5798 161.868 28.0758 161.468 28.1958 160.908H28.8838V166.092ZM29.962 165.58C29.962 165.292 30.21 165.044 30.546 165.044C30.89 165.044 31.194 165.3 31.194 165.804C31.194 166.86 30.506 167.3 30.01 167.364V166.948C30.402 166.86 30.666 166.468 30.674 166.084C30.65 166.1 30.586 166.124 30.498 166.124C30.202 166.124 29.962 165.924 29.962 165.58ZM34.3136 165.58C34.3136 165.268 34.5536 165.02 34.8656 165.02C35.1776 165.02 35.4256 165.268 35.4256 165.58C35.4256 165.892 35.1776 166.132 34.8656 166.132C34.5536 166.132 34.3136 165.892 34.3136 165.58ZM38.516 164.124H36.348V163.468H38.516V164.124ZM40.0066 162.772L39.2226 162.676C39.2146 162.604 39.2146 162.532 39.2146 162.468C39.2146 161.58 39.8706 160.788 41.0066 160.788C42.1186 160.788 42.7586 161.516 42.7586 162.38C42.7586 163.06 42.3746 163.604 41.7826 163.98L40.7586 164.636C40.4626 164.828 40.1986 165.044 40.1266 165.364H42.7906V166.092H39.1586C39.1746 165.244 39.5106 164.596 40.3906 164.028L41.2546 163.468C41.7426 163.156 41.9666 162.788 41.9666 162.388C41.9666 161.916 41.6466 161.476 40.9906 161.476C40.3106 161.476 39.9906 161.948 39.9906 162.54C39.9906 162.612 39.9986 162.692 40.0066 162.772ZM43.673 165.58C43.673 165.292 43.921 165.044 44.257 165.044C44.601 165.044 44.905 165.3 44.905 165.804C44.905 166.86 44.217 167.3 43.721 167.364V166.948C44.113 166.86 44.377 166.468 44.385 166.084C44.361 166.1 44.297 166.124 44.209 166.124C43.913 166.124 43.673 165.924 43.673 165.58ZM48.0245 165.58C48.0245 165.268 48.2645 165.02 48.5765 165.02C48.8885 165.02 49.1365 165.268 49.1365 165.58C49.1365 165.892 48.8885 166.132 48.5765 166.132C48.2645 166.132 48.0245 165.892 48.0245 165.58ZM52.2269 164.124H50.0589V163.468H52.2269V164.124ZM53.7655 163.5C53.7655 164.292 53.9095 164.852 54.2215 165.18C54.4215 165.388 54.6775 165.516 55.0455 165.516C55.4135 165.516 55.6695 165.388 55.8695 165.18C56.1815 164.852 56.3255 164.292 56.3255 163.5C56.3255 162.708 56.1815 162.148 55.8695 161.82C55.6695 161.612 55.4135 161.484 55.0455 161.484C54.6775 161.484 54.4215 161.612 54.2215 161.82C53.9095 162.148 53.7655 162.708 53.7655 163.5ZM52.9895 163.5C52.9895 162.788 53.1095 162.132 53.4535 161.62C53.7895 161.116 54.3095 160.788 55.0455 160.788C55.7815 160.788 56.3015 161.116 56.6375 161.62C56.9815 162.132 57.1015 162.788 57.1015 163.5C57.1015 164.212 56.9815 164.868 56.6375 165.38C56.3015 165.884 55.7815 166.212 55.0455 166.212C54.3095 166.212 53.7895 165.884 53.4535 165.38C53.1095 164.868 52.9895 164.212 52.9895 163.5ZM58.6316 162.772L57.8476 162.676C57.8396 162.604 57.8396 162.532 57.8396 162.468C57.8396 161.58 58.4956 160.788 59.6316 160.788C60.7436 160.788 61.3836 161.516 61.3836 162.38C61.3836 163.06 60.9996 163.604 60.4076 163.98L59.3836 164.636C59.0876 164.828 58.8236 165.044 58.7516 165.364H61.4156V166.092H57.7836C57.7996 165.244 58.1356 164.596 59.0156 164.028L59.8796 163.468C60.3676 163.156 60.5916 162.788 60.5916 162.388C60.5916 161.916 60.2716 161.476 59.6156 161.476C58.9356 161.476 58.6156 161.948 58.6156 162.54C58.6156 162.612 58.6236 162.692 58.6316 162.772ZM63.322 167.276H61.778V166.676H62.666V160.42H61.778V159.812H63.322V167.276Z"
          fill="url(#paint18_linear_4346_99807)"
        />
        <path
          d="M280.535 120.798H278.991V113.334H280.535V113.942H279.647V120.198H280.535V120.798ZM281.136 119.102C281.136 118.79 281.376 118.542 281.688 118.542C282 118.542 282.248 118.79 282.248 119.102C282.248 119.414 282 119.654 281.688 119.654C281.376 119.654 281.136 119.414 281.136 119.102ZM283.83 117.022C283.83 117.814 283.974 118.374 284.286 118.702C284.486 118.91 284.742 119.038 285.11 119.038C285.478 119.038 285.734 118.91 285.934 118.702C286.246 118.374 286.39 117.814 286.39 117.022C286.39 116.23 286.246 115.67 285.934 115.342C285.734 115.134 285.478 115.006 285.11 115.006C284.742 115.006 284.486 115.134 284.286 115.342C283.974 115.67 283.83 116.23 283.83 117.022ZM283.054 117.022C283.054 116.31 283.174 115.654 283.518 115.142C283.854 114.638 284.374 114.31 285.11 114.31C285.846 114.31 286.366 114.638 286.702 115.142C287.046 115.654 287.166 116.31 287.166 117.022C287.166 117.734 287.046 118.39 286.702 118.902C286.366 119.406 285.846 119.734 285.11 119.734C284.374 119.734 283.854 119.406 283.518 118.902C283.174 118.39 283.054 117.734 283.054 117.022ZM289.329 117.206L288.945 116.558L290.473 115.15H287.993V114.43H291.513V115.134L290.033 116.502C290.793 116.502 291.633 117.014 291.633 118.078C291.633 118.95 290.961 119.742 289.753 119.742C288.553 119.742 287.873 118.958 287.825 118.11L288.593 117.934C288.625 118.622 289.121 119.054 289.745 119.054C290.457 119.054 290.841 118.614 290.841 118.094C290.841 117.414 290.297 117.126 289.785 117.126C289.625 117.126 289.465 117.158 289.329 117.206ZM292.433 119.102C292.433 118.814 292.681 118.566 293.017 118.566C293.361 118.566 293.665 118.822 293.665 119.326C293.665 120.382 292.977 120.822 292.481 120.886V120.47C292.873 120.382 293.137 119.99 293.145 119.606C293.121 119.622 293.057 119.646 292.969 119.646C292.673 119.646 292.433 119.446 292.433 119.102ZM296.784 119.102C296.784 118.79 297.024 118.542 297.336 118.542C297.648 118.542 297.896 118.79 297.896 119.102C297.896 119.414 297.648 119.654 297.336 119.654C297.024 119.654 296.784 119.414 296.784 119.102ZM300.259 117.206L299.875 116.558L301.403 115.15H298.923V114.43H302.443V115.134L300.963 116.502C301.723 116.502 302.563 117.014 302.563 118.078C302.563 118.95 301.891 119.742 300.683 119.742C299.483 119.742 298.803 118.958 298.755 118.11L299.523 117.934C299.555 118.622 300.051 119.054 300.675 119.054C301.387 119.054 301.771 118.614 301.771 118.094C301.771 117.414 301.227 117.126 300.715 117.126C300.555 117.126 300.395 117.158 300.259 117.206ZM303.362 119.102C303.362 118.814 303.61 118.566 303.946 118.566C304.29 118.566 304.594 118.822 304.594 119.326C304.594 120.382 303.906 120.822 303.41 120.886V120.47C303.802 120.382 304.066 119.99 304.074 119.606C304.05 119.622 303.986 119.646 303.898 119.646C303.602 119.646 303.362 119.446 303.362 119.102ZM307.714 119.102C307.714 118.79 307.954 118.542 308.266 118.542C308.578 118.542 308.826 118.79 308.826 119.102C308.826 119.414 308.578 119.654 308.266 119.654C307.954 119.654 307.714 119.414 307.714 119.102ZM310.408 117.022C310.408 117.814 310.552 118.374 310.864 118.702C311.064 118.91 311.32 119.038 311.688 119.038C312.056 119.038 312.312 118.91 312.512 118.702C312.824 118.374 312.968 117.814 312.968 117.022C312.968 116.23 312.824 115.67 312.512 115.342C312.312 115.134 312.056 115.006 311.688 115.006C311.32 115.006 311.064 115.134 310.864 115.342C310.552 115.67 310.408 116.23 310.408 117.022ZM309.632 117.022C309.632 116.31 309.752 115.654 310.096 115.142C310.432 114.638 310.952 114.31 311.688 114.31C312.424 114.31 312.944 114.638 313.28 115.142C313.624 115.654 313.744 116.31 313.744 117.022C313.744 117.734 313.624 118.39 313.28 118.902C312.944 119.406 312.424 119.734 311.688 119.734C310.952 119.734 310.432 119.406 310.096 118.902C309.752 118.39 309.632 117.734 309.632 117.022ZM315.907 117.206L315.523 116.558L317.051 115.15H314.571V114.43H318.091V115.134L316.611 116.502C317.371 116.502 318.211 117.014 318.211 118.078C318.211 118.95 317.539 119.742 316.331 119.742C315.131 119.742 314.451 118.958 314.403 118.11L315.171 117.934C315.203 118.622 315.699 119.054 316.323 119.054C317.035 119.054 317.419 118.614 317.419 118.094C317.419 117.414 316.875 117.126 316.363 117.126C316.203 117.126 316.043 117.158 315.907 117.206ZM320.113 120.798H318.569V120.198H319.457V113.942H318.569V113.334H320.113V120.798Z"
          fill="url(#paint19_linear_4346_99807)"
        />
        <path
          d="M243.094 56.8276H241.55V49.3636H243.094V49.9716H242.206V56.2276H243.094V56.8276ZM243.694 55.1316C243.694 54.8196 243.934 54.5716 244.246 54.5716C244.558 54.5716 244.806 54.8196 244.806 55.1316C244.806 55.4436 244.558 55.6836 244.246 55.6836C243.934 55.6836 243.694 55.4436 243.694 55.1316ZM246.389 53.0516C246.389 53.8436 246.533 54.4036 246.845 54.7316C247.045 54.9396 247.301 55.0676 247.669 55.0676C248.037 55.0676 248.293 54.9396 248.493 54.7316C248.805 54.4036 248.949 53.8436 248.949 53.0516C248.949 52.2596 248.805 51.6996 248.493 51.3716C248.293 51.1636 248.037 51.0356 247.669 51.0356C247.301 51.0356 247.045 51.1636 246.845 51.3716C246.533 51.6996 246.389 52.2596 246.389 53.0516ZM245.613 53.0516C245.613 52.3396 245.733 51.6836 246.077 51.1716C246.413 50.6676 246.933 50.3396 247.669 50.3396C248.405 50.3396 248.925 50.6676 249.261 51.1716C249.605 51.6836 249.725 52.3396 249.725 53.0516C249.725 53.7636 249.605 54.4196 249.261 54.9316C248.925 55.4356 248.405 55.7636 247.669 55.7636C246.933 55.7636 246.413 55.4356 246.077 54.9316C245.733 54.4196 245.613 53.7636 245.613 53.0516ZM250.368 54.2356L251.12 54.0116C251.168 54.6516 251.64 55.0836 252.288 55.0836C252.904 55.0836 253.4 54.6756 253.4 54.0356C253.4 53.3236 252.88 52.9636 252.28 52.9636C251.912 52.9636 251.56 53.1076 251.336 53.3396C251.064 53.2356 250.832 53.1316 250.568 53.0356L251.24 50.4596H253.976V51.1796H251.76L251.376 52.6676C251.608 52.4116 252.024 52.2836 252.432 52.2836C253.448 52.2836 254.192 52.9316 254.192 54.0036C254.192 54.9796 253.44 55.7716 252.288 55.7716C251.184 55.7716 250.44 55.0516 250.368 54.2356ZM254.952 55.1316C254.952 54.8436 255.2 54.5956 255.536 54.5956C255.88 54.5956 256.184 54.8516 256.184 55.3556C256.184 56.4116 255.496 56.8516 255 56.9156V56.4996C255.392 56.4116 255.656 56.0196 255.664 55.6356C255.64 55.6516 255.576 55.6756 255.488 55.6756C255.192 55.6756 254.952 55.4756 254.952 55.1316ZM259.304 55.1316C259.304 54.8196 259.544 54.5716 259.856 54.5716C260.168 54.5716 260.416 54.8196 260.416 55.1316C260.416 55.4436 260.168 55.6836 259.856 55.6836C259.544 55.6836 259.304 55.4436 259.304 55.1316ZM260.86 54.4756V53.5716L263.084 50.4596H264.164V53.7476H265.052V54.4756H264.164V55.6436H263.404V54.4756H260.86ZM263.404 53.7476V51.2116L261.58 53.7476H263.404ZM265.734 55.1316C265.734 54.8436 265.982 54.5956 266.318 54.5956C266.662 54.5956 266.966 54.8516 266.966 55.3556C266.966 56.4116 266.278 56.8516 265.782 56.9156V56.4996C266.174 56.4116 266.438 56.0196 266.446 55.6356C266.422 55.6516 266.358 55.6756 266.27 55.6756C265.974 55.6756 265.734 55.4756 265.734 55.1316ZM270.085 55.1316C270.085 54.8196 270.325 54.5716 270.637 54.5716C270.949 54.5716 271.197 54.8196 271.197 55.1316C271.197 55.4436 270.949 55.6836 270.637 55.6836C270.325 55.6836 270.085 55.4436 270.085 55.1316ZM272.779 53.0516C272.779 53.8436 272.923 54.4036 273.235 54.7316C273.435 54.9396 273.691 55.0676 274.059 55.0676C274.427 55.0676 274.683 54.9396 274.883 54.7316C275.195 54.4036 275.339 53.8436 275.339 53.0516C275.339 52.2596 275.195 51.6996 274.883 51.3716C274.683 51.1636 274.427 51.0356 274.059 51.0356C273.691 51.0356 273.435 51.1636 273.235 51.3716C272.923 51.6996 272.779 52.2596 272.779 53.0516ZM272.003 53.0516C272.003 52.3396 272.123 51.6836 272.467 51.1716C272.803 50.6676 273.323 50.3396 274.059 50.3396C274.795 50.3396 275.315 50.6676 275.651 51.1716C275.995 51.6836 276.115 52.3396 276.115 53.0516C276.115 53.7636 275.995 54.4196 275.651 54.9316C275.315 55.4356 274.795 55.7636 274.059 55.7636C273.323 55.7636 272.803 55.4356 272.467 54.9316C272.123 54.4196 272.003 53.7636 272.003 53.0516ZM277.645 52.3236L276.861 52.2276C276.853 52.1556 276.853 52.0836 276.853 52.0196C276.853 51.1316 277.509 50.3396 278.645 50.3396C279.757 50.3396 280.397 51.0676 280.397 51.9316C280.397 52.6116 280.013 53.1556 279.421 53.5316L278.397 54.1876C278.101 54.3796 277.837 54.5956 277.765 54.9156H280.429V55.6436H276.797C276.813 54.7956 277.149 54.1476 278.029 53.5796L278.893 53.0196C279.381 52.7076 279.605 52.3396 279.605 51.9396C279.605 51.4676 279.285 51.0276 278.629 51.0276C277.949 51.0276 277.629 51.4996 277.629 52.0916C277.629 52.1636 277.637 52.2436 277.645 52.3236ZM282.336 56.8276H280.792V56.2276H281.68V49.9716H280.792V49.3636H282.336V56.8276Z"
          fill="url(#paint20_linear_4346_99807)"
        />
        <path
          d="M-1.99217 67.4827H-3.53617V60.0187H-1.99217V60.6267H-2.88017V66.8827H-1.99217V67.4827ZM-1.39148 65.7867C-1.39148 65.4747 -1.15148 65.2267 -0.839485 65.2267C-0.527485 65.2267 -0.279485 65.4747 -0.279485 65.7867C-0.279485 66.0987 -0.527485 66.3387 -0.839485 66.3387C-1.15148 66.3387 -1.39148 66.0987 -1.39148 65.7867ZM2.81089 64.3307H0.64289V63.6747H2.81089V64.3307ZM4.34952 63.7067C4.34952 64.4987 4.49352 65.0587 4.80552 65.3867C5.00552 65.5947 5.26152 65.7227 5.62952 65.7227C5.99752 65.7227 6.25352 65.5947 6.45352 65.3867C6.76552 65.0587 6.90952 64.4987 6.90952 63.7067C6.90952 62.9147 6.76552 62.3547 6.45352 62.0267C6.25352 61.8187 5.99752 61.6907 5.62952 61.6907C5.26152 61.6907 5.00552 61.8187 4.80552 62.0267C4.49352 62.3547 4.34952 62.9147 4.34952 63.7067ZM3.57352 63.7067C3.57352 62.9947 3.69352 62.3387 4.03752 61.8267C4.37352 61.3227 4.89352 60.9947 5.62952 60.9947C6.36552 60.9947 6.88552 61.3227 7.22152 61.8267C7.56552 62.3387 7.68552 62.9947 7.68552 63.7067C7.68552 64.4187 7.56552 65.0747 7.22152 65.5867C6.88552 66.0907 6.36552 66.4187 5.62952 66.4187C4.89352 66.4187 4.37352 66.0907 4.03752 65.5867C3.69352 65.0747 3.57352 64.4187 3.57352 63.7067ZM9.84852 63.8907L9.46452 63.2427L10.9925 61.8347H8.51252V61.1147H12.0325V61.8187L10.5525 63.1867C11.3125 63.1867 12.1525 63.6987 12.1525 64.7627C12.1525 65.6347 11.4805 66.4267 10.2725 66.4267C9.07252 66.4267 8.39252 65.6427 8.34452 64.7947L9.11252 64.6187C9.14452 65.3067 9.64052 65.7387 10.2645 65.7387C10.9765 65.7387 11.3605 65.2987 11.3605 64.7787C11.3605 64.0987 10.8165 63.8107 10.3045 63.8107C10.1445 63.8107 9.98452 63.8427 9.84852 63.8907ZM12.9523 65.7867C12.9523 65.4987 13.2003 65.2507 13.5363 65.2507C13.8803 65.2507 14.1843 65.5067 14.1843 66.0107C14.1843 67.0667 13.4963 67.5067 13.0003 67.5707V67.1547C13.3923 67.0667 13.6563 66.6747 13.6643 66.2907C13.6403 66.3067 13.5763 66.3307 13.4883 66.3307C13.1923 66.3307 12.9523 66.1307 12.9523 65.7867ZM17.3038 65.7867C17.3038 65.4747 17.5438 65.2267 17.8558 65.2267C18.1678 65.2267 18.4158 65.4747 18.4158 65.7867C18.4158 66.0987 18.1678 66.3387 17.8558 66.3387C17.5438 66.3387 17.3038 66.0987 17.3038 65.7867ZM21.5062 64.3307H19.3382V63.6747H21.5062V64.3307ZM23.9366 66.2987H23.1686V62.6427H21.9126V62.0907C22.6326 62.0747 23.1286 61.6747 23.2486 61.1147H23.9366V66.2987ZM25.0148 65.7867C25.0148 65.4987 25.2628 65.2507 25.5988 65.2507C25.9428 65.2507 26.2468 65.5067 26.2468 66.0107C26.2468 67.0667 25.5588 67.5067 25.0628 67.5707V67.1547C25.4548 67.0667 25.7188 66.6747 25.7268 66.2907C25.7028 66.3067 25.6388 66.3307 25.5508 66.3307C25.2548 66.3307 25.0148 66.1307 25.0148 65.7867ZM29.3663 65.7867C29.3663 65.4747 29.6063 65.2267 29.9183 65.2267C30.2303 65.2267 30.4783 65.4747 30.4783 65.7867C30.4783 66.0987 30.2303 66.3387 29.9183 66.3387C29.6063 66.3387 29.3663 66.0987 29.3663 65.7867ZM32.0605 63.7067C32.0605 64.4987 32.2045 65.0587 32.5165 65.3867C32.7165 65.5947 32.9725 65.7227 33.3405 65.7227C33.7085 65.7227 33.9645 65.5947 34.1645 65.3867C34.4765 65.0587 34.6205 64.4987 34.6205 63.7067C34.6205 62.9147 34.4765 62.3547 34.1645 62.0267C33.9645 61.8187 33.7085 61.6907 33.3405 61.6907C32.9725 61.6907 32.7165 61.8187 32.5165 62.0267C32.2045 62.3547 32.0605 62.9147 32.0605 63.7067ZM31.2845 63.7067C31.2845 62.9947 31.4045 62.3387 31.7485 61.8267C32.0845 61.3227 32.6045 60.9947 33.3405 60.9947C34.0765 60.9947 34.5965 61.3227 34.9325 61.8267C35.2765 62.3387 35.3965 62.9947 35.3965 63.7067C35.3965 64.4187 35.2765 65.0747 34.9325 65.5867C34.5965 66.0907 34.0765 66.4187 33.3405 66.4187C32.6045 66.4187 32.0845 66.0907 31.7485 65.5867C31.4045 65.0747 31.2845 64.4187 31.2845 63.7067ZM37.9835 66.2987H37.2155V62.6427H35.9595V62.0907C36.6795 62.0747 37.1755 61.6747 37.2955 61.1147H37.9835V66.2987ZM40.2419 67.4827H38.6979V66.8827H39.5859V60.6267H38.6979V60.0187H40.2419V67.4827Z"
          fill="url(#paint21_linear_4346_99807)"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_4346_99807"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(260.699 83.7471) rotate(90) scale(50.1628 71.9898)"
        >
          <stop stopColor={colors.rectStart} />
          <stop offset="1" stopColor={colors.rectEnd} />
        </radialGradient>
        <radialGradient
          id="paint1_radial_4346_99807"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(34.2065 186.722) rotate(90) scale(29.89 44.8271)"
        >
          <stop stopColor={colors.rectStart} />
          <stop offset="1" stopColor={colors.rectEnd} />
        </radialGradient>
        <radialGradient
          id="paint2_radial_4346_99807"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(10.2607 31.5141) rotate(90) scale(47.0109 44.1827)"
        >
          <stop stopColor={colors.rectStart} />
          <stop offset="1" stopColor={colors.rectEnd} />
        </radialGradient>
        <linearGradient
          id="paint3_linear_4346_99807"
          x1="103.644"
          y1="20.5905"
          x2="123.04"
          y2="40.7061"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4346_99807"
          x1="170.559"
          y1="35.2435"
          x2="162.945"
          y2="62.1299"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_4346_99807"
          x1="-28.276"
          y1="118.776"
          x2="18.0891"
          y2="140.665"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_4346_99807"
          x1="24.9597"
          y1="140.108"
          x2="35.2785"
          y2="116.967"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_4346_99807"
          x1="0.768258"
          y1="112.818"
          x2="18.5578"
          y2="105.239"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint8_linear_4346_99807"
          x1="214.088"
          y1="163.734"
          x2="194.878"
          y2="165.94"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint9_linear_4346_99807"
          x1="282.318"
          y1="182.011"
          x2="256.032"
          y2="148.158"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint10_linear_4346_99807"
          x1="281.53"
          y1="70.3694"
          x2="256.656"
          y2="79.1142"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C4C4C4" />
          <stop offset="1" stopColor="#3F3F3F" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint11_linear_4346_99807"
          x1="291.112"
          y1="98.3415"
          x2="190.943"
          y2="101.575"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" />
          <stop offset="1" stopColor="#696969" stopOpacity="0" />
        </linearGradient>
        <radialGradient
          id="paint12_radial_4346_99807"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(142.35 95.48) rotate(90) scale(103.327 196.814)"
        >
          <stop stopColor={colors.radialBgStart} stopOpacity="0" />
          <stop offset="1" stopColor={colors.radialBgEnd} />
        </radialGradient>
        <linearGradient
          id="pgLogo"
          x1="183.038"
          y1="80.9614"
          x2="217.562"
          y2="142.614"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.pgLogoStart} stopOpacity="0.5" />
          <stop offset="1" stopColor={colors.pgLogoEnd} stopOpacity="1" />
        </linearGradient>
        <linearGradient
          id="paint14_linear_4346_99807"
          x1="62.3337"
          y1="70.1224"
          x2="88.2532"
          y2="95.5895"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.arrow} />
          <stop offset="1" stopColor="#3DCB8C" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint15_linear_4346_99807"
          x1="216.624"
          y1="71.8806"
          x2="189.643"
          y2="75.3287"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.arrow} />
          <stop offset="1" stopColor="#3DCB8C" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint16_linear_4346_99807"
          x1="251.364"
          y1="125.828"
          x2="206.423"
          y2="124.536"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.arrow} />
          <stop offset="1" stopColor="#3DCB8C" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint17_linear_4346_99807"
          x1="78.8167"
          y1="158.5"
          x2="101.531"
          y2="135.096"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.arrow} />
          <stop offset="1" stopColor="#3DCB8C" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint18_linear_4346_99807"
          x1="39.1989"
          y1="150.092"
          x2="39.1989"
          y2="176.291"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.coordinatesStart} />
          <stop offset="1" stopColor={colors.coordinatesEnd} stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint19_linear_4346_99807"
          x1="299.552"
          y1="103.614"
          x2="299.552"
          y2="129.813"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.coordinatesStart} />
          <stop offset="1" stopColor={colors.coordinatesEnd} stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint20_linear_4346_99807"
          x1="261.943"
          y1="39.6436"
          x2="261.943"
          y2="65.8419"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.coordinatesStart} />
          <stop offset="1" stopColor={colors.coordinatesEnd} stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint21_linear_4346_99807"
          x1="18.3532"
          y1="50.2987"
          x2="18.3532"
          y2="76.497"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={colors.asdfStart} />
          <stop offset="1" stopColor={colors.asdfEnd} stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  )

  return (
    <LazyMotion features={domAnimation}>
      <RenderedSVG />
    </LazyMotion>
  )
}

export default PGvectorImg
