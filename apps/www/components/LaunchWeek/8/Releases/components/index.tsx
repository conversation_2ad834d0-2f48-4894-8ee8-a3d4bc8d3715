import { Badge } from 'ui'

import Link from 'next/link'
import Image from 'next/image'
import { ExpandableVideo } from 'ui-patterns/ExpandableVideo'

export const PencilSvg = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.21792 11.2469L11.8015 2.66333L14.0953 4.95709L5.51167 13.5407M3.21792 11.2469L2.34219 14.4164L5.51167 13.5407M3.21792 11.2469L5.51167 13.5407"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const DocsSvg = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.96289 9.48618H10.9629M4.96289 7.48618H10.9629M4.96289 11.4862H8.96289M3 2.00034V13.9998H12.9996V5.60113L9.38156 2.00034H3ZM12.9644 5.58432L9.38004 2L9.38004 5.58432L12.9644 5.58432Z"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const PlaySvg = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.4287 8L8.74762 10.7026L4.06653 13.4053L4.06653 8L4.06653 2.59474L8.74762 5.29737L13.4287 8Z"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const GithubSvg = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.2125 1.75781C4.6765 1.75781 1.8125 4.62181 1.8125 8.15781C1.8125 10.9898 3.6445 13.3818 6.1885 14.2298C6.5085 14.2858 6.6285 14.0938 6.6285 13.9258C6.6285 13.7738 6.6205 13.2698 6.6205 12.7338C5.0125 13.0298 4.5965 12.3418 4.4685 11.9818C4.3965 11.7978 4.0845 11.2298 3.8125 11.0778C3.5885 10.9578 3.2685 10.6618 3.8045 10.6538C4.3085 10.6458 4.6685 11.1178 4.7885 11.3098C5.3645 12.2778 6.2845 12.0058 6.6525 11.8378C6.7085 11.4218 6.8765 11.1418 7.0605 10.9818C5.6365 10.8218 4.1485 10.2698 4.1485 7.82181C4.1485 7.12581 4.3965 6.54981 4.8045 6.10181C4.7405 5.94181 4.5165 5.28581 4.8685 4.40581C4.8685 4.40581 5.4045 4.23781 6.6285 5.06181C7.1405 4.91781 7.6845 4.84581 8.2285 4.84581C8.7725 4.84581 9.3165 4.91781 9.8285 5.06181C11.0525 4.22981 11.5885 4.40581 11.5885 4.40581C11.9405 5.28581 11.7165 5.94181 11.6525 6.10181C12.0605 6.54981 12.3085 7.11781 12.3085 7.82181C12.3085 10.2778 10.8125 10.8218 9.3885 10.9818C9.6205 11.1818 9.8205 11.5658 9.8205 12.1658C9.8205 13.0218 9.8125 13.7098 9.8125 13.9258C9.8125 14.0938 9.9325 14.2938 10.2525 14.2298C11.523 13.8009 12.627 12.9844 13.4092 11.8951C14.1913 10.8059 14.6122 9.49877 14.6125 8.15781C14.6125 4.62181 11.7485 1.75781 8.2125 1.75781Z"
      fill="#8F8F8F"
    />
  </svg>
)
export const LinkSvg = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.875 4.0013H4.20833C3.47195 4.0013 2.875 4.59826 2.875 5.33464V12.0013C2.875 12.7377 3.47195 13.3346 4.20833 13.3346H10.875C11.6114 13.3346 12.2083 12.7377 12.2083 12.0013V9.33464M9.54167 2.66797H13.5417M13.5417 2.66797V6.66797M13.5417 2.66797L6.875 9.33464"
      stroke="#8F8F8F"
      strokeWidth="1"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
export const ArrowTopRightSvg = () => (
  <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.44531 13.846L13.1152 4.17578"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
    <path
      d="M3.44531 4.09375H13.1152V13.7637"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const HackernewsSvg = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.26821 7.87591L9.94194 4.47266H11.8008L8.82871 10.074V13.606H7.16205V10.0213L4.1875 4.47266H6.15549L7.86748 7.9537C7.87321 7.95954 7.87972 7.96606 7.88751 7.97386C7.95585 8.04219 7.98757 8.11202 8.00726 8.16124C8.01322 8.17614 8.01776 8.18804 8.02175 8.19849C8.02548 8.20824 8.02872 8.21672 8.03217 8.22518L8.04751 8.24052C8.11375 8.10585 8.19796 7.98119 8.26821 7.87591Z"
      fill="#8F8F8F"
    />
  </svg>
)

export const TwitterSpacesSvg = () => (
  <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.46228 5.78799C4.46228 3.4802 6.3331 1.60938 8.64089 1.60938C10.9487 1.60938 12.8195 3.4802 12.8195 5.78799V8.17578C12.8195 10.4836 10.9487 12.3544 8.64089 12.3544C6.3331 12.3544 4.46228 10.4836 4.46228 8.17578V5.78799ZM6.85006 8.17578C7.09719 8.17578 7.29777 7.9752 7.29777 7.72807V6.2357C7.29777 5.98857 7.09719 5.78799 6.85006 5.78799C6.60292 5.78799 6.40235 5.98857 6.40235 6.2357V7.72807C6.40235 7.9752 6.60292 8.17578 6.85006 8.17578ZM8.64089 8.77272C8.88803 8.77272 9.0886 8.57215 9.0886 8.32501V5.63876C9.0886 5.39162 8.88803 5.19105 8.64089 5.19105C8.39376 5.19105 8.19319 5.39162 8.19319 5.63876V8.32501C8.19319 8.57215 8.39376 8.77272 8.64089 8.77272ZM10.4317 8.17578C10.6789 8.17578 10.8794 7.9752 10.8794 7.72807V6.2357C10.8794 5.98857 10.6789 5.78799 10.4317 5.78799C10.1846 5.78799 9.98402 5.98857 9.98402 6.2357V7.72807C9.98402 7.9752 10.1846 8.17578 10.4317 8.17578Z"
      fill="#8F8F8F"
    />
    <path
      d="M2.85924 9.24628C3.17502 9.15077 3.50692 9.32985 3.60243 9.64624C4.24654 11.7934 6.23138 13.3992 8.64185 13.3992C11.0523 13.3992 13.0372 11.7934 13.6819 9.64564C13.7768 9.32926 14.1099 9.15137 14.4251 9.24569C14.7402 9.34 14.9205 9.6725 14.825 9.98888C14.0364 12.6172 11.6039 14.5931 8.64185 14.5931C5.67981 14.5931 3.24725 12.6172 2.45928 9.98948C2.36377 9.6737 2.54286 9.34179 2.85924 9.24628Z"
      fill="#8F8F8F"
    />
  </svg>
)

export const ProductHuntSvg = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_3061_22934)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.00078 15.5094C12.0787 15.5094 15.3844 12.2037 15.3844 8.12578C15.3844 4.04782 12.0787 0.742188 8.00078 0.742188C3.92282 0.742188 0.617188 4.04782 0.617188 8.12578C0.617188 12.2037 3.92282 15.5094 8.00078 15.5094ZM6.89312 8.12578H8.98527C9.279 8.12578 9.56071 8.00909 9.76841 7.80139C9.97612 7.59369 10.0928 7.31198 10.0928 7.01824C10.0928 6.7245 9.97612 6.4428 9.76841 6.23509C9.56071 6.02739 9.279 5.9107 8.98527 5.9107H6.89312V8.12578ZM5.41641 4.43398H8.98527C9.67065 4.43398 10.328 4.70625 10.8126 5.1909C11.2973 5.67554 11.5695 6.33285 11.5695 7.01824C11.5695 7.70363 11.2973 8.36095 10.8126 8.84559C10.328 9.33023 9.67065 9.6025 8.98527 9.6025H6.89312V11.8176H5.41641V4.43398Z"
        fill="#8F8F8F"
      />
    </g>
    <defs>
      <clipPath id="clip0_3061_22934">
        <rect width="16" height="16" fill="currentColor" transform="translate(0 0.378906)" />
      </clipPath>
    </defs>
  </svg>
)

export const CheckCircleSolidIcon = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.00156 14.7805C11.5362 14.7805 14.4016 11.9151 14.4016 8.38047C14.4016 4.84585 11.5362 1.98047 8.00156 1.98047C4.46694 1.98047 1.60156 4.84585 1.60156 8.38047C1.60156 11.9151 4.46694 14.7805 8.00156 14.7805ZM10.9672 7.34615C11.2797 7.03373 11.2797 6.5272 10.9672 6.21478C10.6548 5.90236 10.1483 5.90236 9.83588 6.21478L7.20156 8.8491L6.16725 7.81478C5.85483 7.50236 5.3483 7.50236 5.03588 7.81478C4.72346 8.1272 4.72346 8.63373 5.03588 8.94615L6.63588 10.5462C6.9483 10.8586 7.45483 10.8586 7.76725 10.5462L10.9672 7.34615Z"
      fill="currentColor"
    />
  </svg>
)

export const SmallCard = ({
  className,
  innerClassName,
  children,
}: {
  className?: string
  innerClassName?: string
  children: any
}) => (
  <div
    className={[
      'group relative p-[1px] bg-gradient-to-b from-[#11171890] to-[#1C1C1C60] rounded-2xl overflow-hidden shadow-lg',
      className,
    ].join(' ')}
  >
    <div
      className={[
        'rounded-2xl text-sm text-[#9296AA] p-2 flex flex-row justify-between items-center backdrop-blur-md h-full',
        'bg-[#030A0C]',
        innerClassName,
      ].join(' ')}
    >
      {children}
    </div>
  </div>
)

export const StyledArticleBadge = ({
  className,
  children,
}: {
  className?: string
  children: any
}) => (
  <div
    className={['relative bg-transparent border border-[#F4FFFA90] rounded-full', className].join(
      ' '
    )}
  >
    <div className="!bg-transparent rounded-full !py-1 !px-4 w-full inset-[1px] text-sm border-none from-foreground to-[#6453C5]">
      <span className="text-sm text-[#F4FFFA80] bg-clip-text bg-gradient-to-r from-[#F4FFFA] to-[#7E7AAD]">
        {children}
      </span>
      <div className="absolute inset-0 w-full h-full bg-[#1C1C1C] rounded-full blur-2xl" />
    </div>
  </div>
)

export const AccordionHeader = ({
  date,
  day,
  weekDay,
  title,
  shipped,
  publishedAt,
  shippable = true,
  youtube_id,
  videoThumbnail,
}: {
  date: string
  day: number
  weekDay: string
  title: string
  shipped?: boolean
  publishedAt: string
  shippable?: boolean
  youtube_id?: string
  videoThumbnail?: string
}) => (
  <div
    className={[
      'h-[79px] hover:cursor-default flex flex-1 items-center scroll-mt-20 text-muted',
    ].join(' ')}
  >
    <div
      className={[
        'flex flex-1 sm:flex-none',
        shippable && shipped ? 'items-stretch' : 'flex-row items-center',
      ].join(' ')}
    >
      <div className="flex gap-4 w-full sm:w-auto sm:min-w-[240px] md:min-w-[380px] items-center">
        <span className="text-sm">
          <span className="inline sm:hidden md:inline">{weekDay} </span>
          {date && (
            <span>
              <span className="inline sm:hidden md:inline">・</span> {date}
            </span>
          )}
        </span>
        {shippable && shipped && (
          <Badge
            className={`relative hidden sm:inline-flex inset-0 !bg-[#05090B] border !border-[#061517] !py-1 !px-4 h-fit`}
          >
            <span className="text-[#F4FFFA40] text-sm font-normal bg-clip-text bg-gradient-to-r from-[#F4FFFA] to-[#675FA7]">
              Shipped
            </span>
          </Badge>
        )}
        {shippable && shipped && youtube_id && (
          <div className={['hover:cursor-pointer hover:!opacity-100'].join(' ')}>
            <ExpandableVideo
              videoId={youtube_id}
              trigger={
                <VideoPreviewTrigger
                  title={`Watch: Day ${day}`}
                  thumbnail={videoThumbnail ?? '/images/launchweek/8/day5/yt_d5.jpg'}
                />
              }
            />
          </div>
        )}
      </div>
    </div>
  </div>
)

export const VideoPreviewTrigger = ({
  title,
  thumbnail,
}: {
  title?: string
  thumbnail: string
}) => (
  <div className="flex items-center h-full gap-3 text-xs group/vid text-foreground-light hover:text-foreground transition-colors">
    <div className="relative h-10 !aspect-video flex items-center justify-center rounded overflow-hidden border border-foreground-lighter opacity-80 group-hover/vid:opacity-100 transition-colors">
      <div className="absolute z-10 w-2.5 h-2.5 text-white opacity-100">
        <svg viewBox="0 0 81 91" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M76.5621 37.998C82.3369 41.3321 82.3369 49.6673 76.5621 53.0014L13.2198 89.5721C7.44504 92.9062 0.226562 88.7386 0.226562 82.0704L0.226566 8.92901C0.226566 2.26085 7.44506 -1.90673 13.2199 1.42735L76.5621 37.998Z"
            fill="currentColor"
          />
        </svg>
      </div>
      <Image src={thumbnail} alt="Video thumbnail" fill sizes="100%" className="object-cover" />
    </div>
    {title && <span>{title}</span>}
  </div>
)

export const MultistepSectionHeader = ({ title, blog }: any) => {
  return (
    <div className="flex flex-1 flex-col sm:flex-row py-8">
      <div className="flex gap-4 w-full items-center justify-between md:justify-start">
        {title && <span className="text-foreground text-lg mt-3 sm:mt-0">{title}</span>}
        {!!blog && (
          <ChipLink href={blog} className="!w-auto !text-left !justify-between !flex-none">
            Blog post
            <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
              <PencilSvg />
            </div>
          </ChipLink>
        )}
      </div>
    </div>
  )
}

export const ChipLink = ({
  href,
  uiOnly,
  className,
  target,
  children,
}: {
  href?: string
  className?: string
  uiOnly?: boolean
  target?: '_blank' | '_self' | '_parent' | '_top' | 'framename'
  children: any
}) =>
  uiOnly ? (
    <span
      className={[
        'flex flex-auto justify-center sm:justify-between w-full text-center sm:text-left min-h-[43px] sm:w-auto items-center border border-[#232323] bg-gradient-to-r text-white from-[#46444460] to-[#19191980] hover:from-[#4e4e4e90] hover:to-[#19191990] hover:border-stronger backdrop-blur-xl rounded-full text-sm py-2 px-3 sm:pr-2',
        className,
      ].join(' ')}
    >
      {children}
    </span>
  ) : !!href ? (
    <Link
      href={href}
      target={target ?? '_self'}
      rel="noopener"
      className={[
        'flex flex-auto justify-center sm:justify-between w-full text-center sm:text-left min-h-[43px] sm:w-auto items-center border border-[#232323] bg-gradient-to-r text-white from-[#46444460] to-[#19191980] hover:from-[#4e4e4e90] hover:to-[#19191990] hover:border-stronger backdrop-blur-xl rounded-full text-sm py-2 px-3 sm:pr-2',
        className,
      ].join(' ')}
    >
      {children}
    </Link>
  ) : null

export const SectionButtons = ({
  blog,
  docs,
  video,
  github,
  url,
  hackernews,
  twitter_spaces,
  product_hunt,
  mobileGrid,
}: {
  blog?: string
  docs?: string
  video?: string
  github?: string
  url?: string
  hackernews?: string
  twitter_spaces?: string
  product_hunt?: string
  mobileGrid?: boolean
}) => {
  return (
    <div
      className={[
        'flex w-full max-w-full md:w-auto justify-center gap-2 z-10',
        mobileGrid && 'grid grid-cols-2 gap-2 sm:flex',
      ].join(' ')}
    >
      {!!blog && (
        <ChipLink href={blog}>
          Blog post
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <PencilSvg />
          </div>
        </ChipLink>
      )}
      {!!docs && (
        <ChipLink href={docs}>
          Docs
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <DocsSvg />
          </div>
        </ChipLink>
      )}
      {!!video && (
        <ChipLink href={video} target="_blank">
          Video
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <PlaySvg />
          </div>
        </ChipLink>
      )}
      {!!github && (
        <ChipLink href={github} target="_blank">
          GitHub
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <GithubSvg />
          </div>
        </ChipLink>
      )}
      {!!url && (
        <ChipLink href={url}>
          Read
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <LinkSvg />
          </div>
        </ChipLink>
      )}
      {hackernews && (
        <ChipLink href={hackernews} target="_blank">
          Hacker News
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <HackernewsSvg />
          </div>
        </ChipLink>
      )}
      {product_hunt && (
        <ChipLink href={product_hunt} target="_blank" className="col-span-full">
          Product Hunt
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <ProductHuntSvg />
          </div>
        </ChipLink>
      )}
      {twitter_spaces && (
        <ChipLink href={twitter_spaces} target="_blank" className="col-span-full">
          Twitter Spaces
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <TwitterSpacesSvg />
          </div>
        </ChipLink>
      )}
    </div>
  )
}

export const CartTitle = ({ children, className }: { children: any; className?: string }) => (
  <span
    className={[
      'z-0 relative text-[#F4FFFA90] bg-clip-text bg-gradient-to-r from-[#F4FFFA] to-[#675FA7] tracking-[-.5px] text-xl',
      className,
    ].join(' ')}
  >
    {children}
    <div className="absolute -z-10 inset-0 w-full h-full bg-[#1C1C1C90] rounded-full blur-xl" />
  </span>
)

export default {
  AccordionHeader,
  DocsSvg,
  PencilSvg,
  PlaySvg,
  CartTitle,
  ChipLink,
  SectionButtons,
  SmallCard,
  StyledArticleBadge,
}
