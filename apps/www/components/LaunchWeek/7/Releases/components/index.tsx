import { Badge } from 'ui'

import styles from '../styles/launchWeek7.module.css'
import Link from 'next/link'

export const PencilSvg = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.21792 11.2469L11.8015 2.66333L14.0953 4.95709L5.51167 13.5407M3.21792 11.2469L2.34219 14.4164L5.51167 13.5407M3.21792 11.2469L5.51167 13.5407"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const DocsSvg = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.96289 9.48618H10.9629M4.96289 7.48618H10.9629M4.96289 11.4862H8.96289M3 2.00034V13.9998H12.9996V5.60113L9.38156 2.00034H3ZM12.9644 5.58432L9.38004 2L9.38004 5.58432L12.9644 5.58432Z"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const PlaySvg = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.4287 8L8.74762 10.7026L4.06653 13.4053L4.06653 8L4.06653 2.59474L8.74762 5.29737L13.4287 8Z"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const GithubSvg = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.2125 1.75781C4.6765 1.75781 1.8125 4.62181 1.8125 8.15781C1.8125 10.9898 3.6445 13.3818 6.1885 14.2298C6.5085 14.2858 6.6285 14.0938 6.6285 13.9258C6.6285 13.7738 6.6205 13.2698 6.6205 12.7338C5.0125 13.0298 4.5965 12.3418 4.4685 11.9818C4.3965 11.7978 4.0845 11.2298 3.8125 11.0778C3.5885 10.9578 3.2685 10.6618 3.8045 10.6538C4.3085 10.6458 4.6685 11.1178 4.7885 11.3098C5.3645 12.2778 6.2845 12.0058 6.6525 11.8378C6.7085 11.4218 6.8765 11.1418 7.0605 10.9818C5.6365 10.8218 4.1485 10.2698 4.1485 7.82181C4.1485 7.12581 4.3965 6.54981 4.8045 6.10181C4.7405 5.94181 4.5165 5.28581 4.8685 4.40581C4.8685 4.40581 5.4045 4.23781 6.6285 5.06181C7.1405 4.91781 7.6845 4.84581 8.2285 4.84581C8.7725 4.84581 9.3165 4.91781 9.8285 5.06181C11.0525 4.22981 11.5885 4.40581 11.5885 4.40581C11.9405 5.28581 11.7165 5.94181 11.6525 6.10181C12.0605 6.54981 12.3085 7.11781 12.3085 7.82181C12.3085 10.2778 10.8125 10.8218 9.3885 10.9818C9.6205 11.1818 9.8205 11.5658 9.8205 12.1658C9.8205 13.0218 9.8125 13.7098 9.8125 13.9258C9.8125 14.0938 9.9325 14.2938 10.2525 14.2298C11.523 13.8009 12.627 12.9844 13.4092 11.8951C14.1913 10.8059 14.6122 9.49877 14.6125 8.15781C14.6125 4.62181 11.7485 1.75781 8.2125 1.75781Z"
      fill="#8F8F8F"
    />
  </svg>
)
export const LinkSvg = () => (
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.875 4.0013H4.20833C3.47195 4.0013 2.875 4.59826 2.875 5.33464V12.0013C2.875 12.7377 3.47195 13.3346 4.20833 13.3346H10.875C11.6114 13.3346 12.2083 12.7377 12.2083 12.0013V9.33464M9.54167 2.66797H13.5417M13.5417 2.66797V6.66797M13.5417 2.66797L6.875 9.33464"
      stroke="#8F8F8F"
      strokeWidth="1"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
export const ArrowTopRightSvg = () => (
  <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M3.44531 13.846L13.1152 4.17578"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
    <path
      d="M3.44531 4.09375H13.1152V13.7637"
      stroke="#8F8F8F"
      strokeMiterlimit="10"
      strokeLinejoin="bevel"
    />
  </svg>
)
export const HackernewsSvg = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.26821 7.87591L9.94194 4.47266H11.8008L8.82871 10.074V13.606H7.16205V10.0213L4.1875 4.47266H6.15549L7.86748 7.9537C7.87321 7.95954 7.87972 7.96606 7.88751 7.97386C7.95585 8.04219 7.98757 8.11202 8.00726 8.16124C8.01322 8.17614 8.01776 8.18804 8.02175 8.19849C8.02548 8.20824 8.02872 8.21672 8.03217 8.22518L8.04751 8.24052C8.11375 8.10585 8.19796 7.98119 8.26821 7.87591Z"
      fill="#8F8F8F"
    />
  </svg>
)

export const SmallCard = ({
  className,
  bgGradient = false,
  children,
}: {
  className?: string
  bgGradient?: boolean
  children: any
}) => (
  <div
    className={[
      'relative p-[1px] bg-gradient-to-b from-[#48484880] to-[#1C1C1C60] rounded-2xl overflow-hidden shadow-lg',
      className,
    ].join(' ')}
  >
    <div
      className={[
        'rounded-2xl text-sm px-4 sm:px-8 lg:px-10 py-4 flex flex-col sm:flex-row justify-between items-center backdrop-blur-md',
        bgGradient ? styles['lw7-article-card-gradient'] : 'bg-[#1c1c1c99]',
      ].join(' ')}
    >
      {children}
    </div>
  </div>
)

export const StyledArticleBadge = ({
  className,
  children,
}: {
  className?: string
  children: any
}) => (
  <div
    className={['relative bg-transparent border border-[#F4FFFA90] rounded-full', className].join(
      ' '
    )}
  >
    <div className="!bg-transparent rounded-full !py-1 !px-4 w-full inset-[1px] text-sm border-none">
      <span className="text-sm text-[#F4FFFA80] bg-clip-text bg-gradient-to-r from-[#F4FFFA] to-[#7E7AAD]">
        {children}
      </span>
      <div className="absolute inset-0 w-full h-full bg-[#1C1C1C] rounded-full blur-2xl" />
    </div>
  </div>
)

export const AccordionHeader = ({ date, day, title, shipped }: any) => {
  return (
    <div className="flex flex-1 flex-col sm:flex-row">
      <div className="flex gap-4 w-full sm:w-auto sm:min-w-[240px] md:min-w-[380px] items-center">
        <Badge
          className={`relative inset-0 !bg-transparent !py-1 !px-4 h-fit backdrop-blur-md ${
            shipped
              ? 'bg-gradient-to-br from-[#2A1E6C] to-[#2A1E6C00] !border-[#6044FF40]'
              : '!border-[#FFFFFF20]'
          }`}
        >
          <span className="text-[#F4FFFA40] text-sm font-normal bg-clip-text bg-gradient-to-r from-[#F4FFFA] to-[#675FA7]">
            {shipped ? 'Shipped' : 'Coming Soon'}
          </span>
        </Badge>

        <span className="text-muted text-sm">
          <span className="inline sm:hidden md:inline">{day} </span>
          {date && (
            <span>
              <span className="inline sm:hidden md:inline">・</span> {date}
            </span>
          )}
        </span>
      </div>
      {shipped && <span className="text-foreground text-lg mt-3 sm:mt-0">{title}</span>}
    </div>
  )
}
export const MultistepSectionHeader = ({ title, blog }: any) => {
  return (
    <div className="flex flex-1 flex-col sm:flex-row py-8">
      <div className="flex gap-4 w-full items-center justify-between md:justify-start">
        {title && <span className="text-foreground text-lg mt-3 sm:mt-0">{title}</span>}
        {!!blog && (
          <ChipLink href={blog} className="!w-auto !text-left !justify-between !flex-none">
            Blog post
            <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
              <PencilSvg />
            </div>
          </ChipLink>
        )}
      </div>
    </div>
  )
}

export const ChipLink = ({
  href,
  uiOnly,
  className,
  target,
  children,
}: {
  href?: string
  className?: string
  uiOnly?: boolean
  target?: '_blank' | '_self' | '_parent' | '_top' | 'framename'
  children: any
}) =>
  uiOnly ? (
    <span
      className={[
        'flex flex-auto justify-center sm:justify-between w-full text-center sm:text-left min-h-[43px] sm:w-auto items-center border border-[#232323] bg-gradient-to-r text-white from-[#46444490] to-[#19191980] hover:from-[#4e4e4e90] hover:to-[#19191980] backdrop-blur-xl rounded-full text-sm py-2 px-3 sm:pr-2',
        className,
      ].join(' ')}
    >
      {children}
    </span>
  ) : !!href ? (
    <Link
      href={href}
      target={target ?? '_self'}
      rel="noopener"
      className={[
        'flex flex-auto justify-center sm:justify-between w-full text-center sm:text-left min-h-[43px] sm:w-auto items-center border border-[#232323] bg-gradient-to-r text-white from-[#46444490] to-[#19191980] hover:from-[#4e4e4e90] hover:to-[#19191980] backdrop-blur-xl rounded-full text-sm py-2 px-3 sm:pr-2',
        className,
      ].join(' ')}
    >
      {children}
    </Link>
  ) : null

export const SectionButtons = ({
  blog,
  docs,
  video,
  github,
  url,
  hackernews,
  mobileGrid,
}: {
  blog?: string
  docs?: string
  video?: string
  github?: string
  url?: string
  hackernews?: string
  mobileGrid?: boolean
}) => {
  return (
    <div
      className={[
        'flex w-full max-w-full md:w-auto justify-center gap-2 z-10',
        mobileGrid && 'grid grid-cols-2 gap-2 sm:flex',
      ].join(' ')}
    >
      {!!blog && (
        <ChipLink href={blog}>
          Blog post
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <PencilSvg />
          </div>
        </ChipLink>
      )}
      {!!docs && (
        <ChipLink href={docs}>
          Docs
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <DocsSvg />
          </div>
        </ChipLink>
      )}
      {!!video && (
        <ChipLink href={video} target="_blank">
          Video
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <PlaySvg />
          </div>
        </ChipLink>
      )}
      {!!github && (
        <ChipLink href={github} target="_blank">
          Github
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <GithubSvg />
          </div>
        </ChipLink>
      )}
      {!!url && (
        <ChipLink href={url}>
          Read
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <LinkSvg />
          </div>
        </ChipLink>
      )}
      {hackernews && (
        <ChipLink href={hackernews} target="_blank">
          Hackernews
          <div className="bg-[#313131] rounded-full hidden sm:inline-block p-1 ml-2">
            <HackernewsSvg />
          </div>
        </ChipLink>
      )}
    </div>
  )
}

export const CartTitle = ({ children }: { children: any }) => (
  <span className="z-0 relative text-[#F4FFFA90] bg-clip-text bg-gradient-to-r from-[#F4FFFA] to-[#675FA7]">
    {children}
    <div className="absolute -z-10 inset-0 w-full h-full bg-[#1C1C1C90] rounded-full blur-xl" />
  </span>
)

export default {
  AccordionHeader,
  DocsSvg,
  PencilSvg,
  PlaySvg,
  CartTitle,
  ChipLink,
  SectionButtons,
  SmallCard,
  StyledArticleBadge,
}
