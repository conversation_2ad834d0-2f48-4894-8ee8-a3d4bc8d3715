.mask {
  mask-image: radial-gradient(black, transparent);
}

.dark_community {
  background-image: radial-gradient(closest-side at 50% 50%, #132121, #132121db, #121212);
}

.community {
  background-image: radial-gradient(closest-side at 50% 50%, #d9eeef, #dbeef0, white);
}

.wrappers > span {
  left: -200px !important;
}

.community_wrappers > span {
  left: -400px !important;
}

@media (max-width: 768px) {
  .community_wrappers > span > img {
    object-fit: contain !important;
  }
}

.lw7-article-card-gradient {
  background: linear-gradient(90deg, #1c1c1c -20%, #20113d50 150%);
}
