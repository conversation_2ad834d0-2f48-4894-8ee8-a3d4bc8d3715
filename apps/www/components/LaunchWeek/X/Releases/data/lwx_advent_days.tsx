export interface AdventDay {
  icon?: any
  className?: string
  id: string
  title: string
  description?: string
  is_shipped: boolean
  links: AdventLink[]
  icons?: AdventLink[]
  type?: string
}

export interface AdventLink {
  url: string
  label?: string
  icon?: any
  target?: '_blank'
}

export const days: AdventDay[] = [
  {
    title: 'Supabase Album',
    description: 'The best soundtrack to build your app in a weekend',
    icon: (
      <svg
        width="28"
        height="27"
        viewBox="0 0 28 27"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M26.7005 0.762595C26.924 0.952594 27.0528 1.23111 27.0528 1.52442V18.7135C27.0528 19.2658 26.605 19.7135 26.0528 19.7135C25.5005 19.7135 25.0528 19.2658 25.0528 18.7135V2.70048L10.8169 5.0192V21.358C10.8169 21.9103 10.3692 22.358 9.81689 22.358C9.26461 22.358 8.81689 21.9103 8.81689 21.358V4.1689C8.81689 3.67866 9.17228 3.26072 9.65613 3.18191L25.892 0.537428C26.1815 0.490276 26.4771 0.572595 26.7005 0.762595Z"
          fill="#EDEDED"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.75794 18.3916C4.04672 18.3916 2.69897 19.7414 2.69897 21.3583C2.69897 22.9752 4.04672 24.325 5.75794 24.325C7.46916 24.325 8.8169 22.9752 8.8169 21.3583C8.8169 19.7414 7.46916 18.3916 5.75794 18.3916ZM0.698975 21.3583C0.698975 18.5937 2.98575 16.3916 5.75794 16.3916C8.53013 16.3916 10.8169 18.5937 10.8169 21.3583C10.8169 24.1229 8.53013 26.325 5.75794 26.325C2.98575 26.325 0.698975 24.1229 0.698975 21.3583Z"
          fill="#EDEDED"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21.9935 15.7466C20.2823 15.7466 18.9346 17.0964 18.9346 18.7133C18.9346 20.3302 20.2823 21.68 21.9935 21.68C23.7048 21.68 25.0525 20.3302 25.0525 18.7133C25.0525 17.0964 23.7048 15.7466 21.9935 15.7466ZM16.9346 18.7133C16.9346 15.9487 19.2213 13.7466 21.9935 13.7466C24.7657 13.7466 27.0525 15.9487 27.0525 18.7133C27.0525 21.4779 24.7657 23.68 21.9935 23.68C19.2213 23.68 16.9346 21.4779 16.9346 18.7133Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'supabase-album',
    is_shipped: true,
    links: [
      {
        url: 'https://supabase.productions/',
        label: 'Listen now',
        target: '_blank',
      },
    ],
  },
  {
    title: 'Postgres Language Server',
    description: 'Implementing the Parser',
    icon: (
      <svg
        width="34"
        height="32"
        viewBox="0 0 34 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.43881 3.75378C4.10721 1.93324 5.84055 0.723145 7.77992 0.723145H15.6033V0.734736H17.0394C23.8756 0.734736 29.4173 6.27652 29.4173 13.1127V20.1749C29.4173 20.7272 28.9696 21.1749 28.4173 21.1749C27.8651 21.1749 27.4173 20.7272 27.4173 20.1749V13.1127C27.4173 7.38109 22.771 2.73474 17.0394 2.73474H15.4396C15.3877 2.73474 15.3366 2.73078 15.2868 2.72314H7.77992C6.6793 2.72314 5.6956 3.40989 5.31627 4.44308L2.7812 11.3479C2.37375 12.4577 2.69516 13.7038 3.58855 14.4781L5.32807 15.9856C6.12772 16.6786 6.58711 17.6847 6.58709 18.7428L6.58706 21.5134C6.58702 23.8192 8.45627 25.6885 10.7621 25.6885C11.4007 25.6885 11.9184 25.1708 11.9184 24.5322L11.9185 12.1874C11.9185 9.59233 12.955 7.10481 14.7977 5.27761C15.1899 4.88873 15.823 4.8914 16.2119 5.28357C16.6008 5.67574 16.5981 6.3089 16.2059 6.69777C14.742 8.14943 13.9185 10.1257 13.9185 12.1874L13.9184 24.5323C13.9184 26.2754 12.5053 27.6885 10.7621 27.6885C7.35169 27.6885 4.58701 24.9238 4.58706 21.5134L4.58709 18.7428C4.5871 18.2647 4.37953 17.8101 4.01822 17.497L2.27871 15.9894C0.757203 14.6708 0.209829 12.5486 0.90374 10.6586L3.43881 3.75378ZM16.539 18.5225C17.0348 18.2791 17.634 18.4838 17.8773 18.9796C19.1969 21.6686 21.9313 23.3727 24.9267 23.3726L32.8043 23.3726C33.3566 23.3725 33.8043 23.8203 33.8043 24.3725C33.8044 24.9248 33.3566 25.3725 32.8044 25.3726L29.4081 25.3726C29.4142 25.4172 29.4173 25.4628 29.4173 25.5091C29.4173 29.0627 26.1868 31.4165 22.6091 31.4165C19.2966 31.4165 16.5385 29.0518 15.9271 25.9188C15.8213 25.3767 16.175 24.8516 16.717 24.7458C17.2591 24.64 17.7843 24.9936 17.89 25.5357C18.3217 27.7475 20.2716 29.4165 22.6091 29.4165C25.447 29.4165 27.4173 27.6256 27.4173 25.5091C27.4173 25.4628 27.4205 25.4172 27.4266 25.3726L24.9267 25.3726C21.1684 25.3727 17.7375 23.2346 16.0818 19.8607C15.8385 19.3649 16.0432 18.7658 16.539 18.5225Z"
          fill="#EDEDED"
        />
        <path
          d="M21.7224 13.0006C21.7224 13.6338 22.2358 14.1472 22.869 14.1472C23.5022 14.1472 24.0156 13.6338 24.0156 13.0006C24.0156 12.3674 23.5022 11.854 22.869 11.854C22.2358 11.854 21.7224 12.3674 21.7224 13.0006Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'postgres-language-server-implementing-parser',
    is_shipped: true,
    links: [
      {
        url: '/blog/postgres-language-server-implementing-parser',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'Design at Supabase',
    description: "The transformative journey of Supabase's Design team",
    icon: (
      <svg
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M20.2446 8.70155C20.2295 7.69665 18.9596 7.26538 18.3333 8.05247L8.88245 19.9299C7.76695 21.3318 8.76744 23.3993 10.5614 23.3993H20.3351L20.45 31.0533C20.4651 32.0582 21.7349 32.4894 22.3612 31.7024L31.8121 19.825C32.9275 18.4231 31.927 16.3556 30.1332 16.3556H20.2949L20.2446 8.70155Z"
          fill="#EDEDED"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.39209 1.42236C9.22689 1.42236 9.06302 1.4277 8.90061 1.43819L8.40165 1.47044L8.33716 0.472517L8.83612 0.440273C9.01998 0.428392 9.20537 0.422363 9.39209 0.422363H10.3485V1.42236H9.39209ZM13.9131 0.422363H15.826V1.42236H13.9131V0.422363ZM19.3905 0.422363H21.3034V1.42236H19.3905V0.422363ZM24.8679 0.422363H26.7808V1.42236H24.8679V0.422363ZM30.3453 0.422363H31.3018C31.4885 0.422363 31.6739 0.428392 31.8577 0.440273L32.3567 0.472517L32.2922 1.47044L31.7932 1.43819C31.6308 1.4277 31.467 1.42236 31.3018 1.42236H30.3453V0.422363ZM4.8229 2.95351L4.44711 3.28333C4.20097 3.49936 3.96908 3.73124 3.75306 3.97738L3.42324 4.35317L2.67165 3.69353L3.00147 3.31774C3.24612 3.03899 3.50872 2.7764 3.78747 2.53175L4.16326 2.20193L4.8229 2.95351ZM36.5306 2.20193L36.9064 2.53175C37.1851 2.7764 37.4477 3.03899 37.6924 3.31774L38.0222 3.69353L37.2706 4.35317L36.9408 3.97738C36.7248 3.73124 36.4929 3.49936 36.2467 3.28333L35.871 2.95351L36.5306 2.20193ZM1.94016 7.93192L1.90792 8.43088C1.89742 8.59329 1.89209 8.75717 1.89209 8.92236V9.87881H0.89209V8.92236C0.89209 8.73564 0.898118 8.55025 0.91 8.36639L0.942244 7.86743L1.94016 7.93192ZM39.7516 7.86744L39.7838 8.36639C39.7957 8.55025 39.8018 8.73564 39.8018 8.92236V9.87881H38.8018V8.92236C38.8018 8.75717 38.7964 8.59329 38.7859 8.43088L38.7537 7.93192L39.7516 7.86744ZM39.8018 13.4433V15.3562H38.8018V13.4433H39.8018ZM1.89209 13.4433V15.3562H0.89209V13.4433H1.89209ZM1.89209 18.9207V20.8336H0.89209V18.9207H1.89209ZM39.8018 18.9207V20.8336H38.8018V18.9207H39.8018ZM1.89209 24.3982V26.3111H0.89209V24.3982H1.89209ZM39.8018 24.3982V26.3111H38.8018V24.3982H39.8018ZM1.89209 29.8756V30.832C1.89209 30.9972 1.89742 31.1611 1.90792 31.3235L1.94016 31.8225L0.942244 31.887L0.91 31.388C0.898118 31.2041 0.89209 31.0188 0.89209 30.832V29.8756H1.89209ZM39.8018 29.8756V30.832C39.8018 31.0188 39.7957 31.2041 39.7838 31.388L39.7516 31.887L38.7537 31.8225L38.7859 31.3235C38.7964 31.1611 38.8018 30.9972 38.8018 30.832V29.8756H39.8018ZM3.42324 35.4012L3.75306 35.777C3.96908 36.0232 4.20097 36.255 4.4471 36.4711L4.8229 36.8009L4.16326 37.5525L3.78746 37.2226C3.50872 36.978 3.24612 36.7154 3.00147 36.4367L2.67165 36.0609L3.42324 35.4012ZM38.0222 36.0609L37.6924 36.4367C37.4477 36.7154 37.1851 36.978 36.9064 37.2226L36.5306 37.5525L35.871 36.8009L36.2467 36.4711C36.4929 36.255 36.7248 36.0232 36.9408 35.777L37.2706 35.4012L38.0222 36.0609ZM8.40165 38.284L8.90061 38.3162C9.06302 38.3267 9.2269 38.332 9.39209 38.332H10.3485V39.332H9.39209C9.20537 39.332 9.01998 39.326 8.83612 39.3141L8.33716 39.2819L8.40165 38.284ZM32.3567 39.2819L31.8577 39.3141C31.6739 39.326 31.4885 39.332 31.3018 39.332H30.3453V38.332H31.3018C31.467 38.332 31.6308 38.3267 31.7932 38.3162L32.2922 38.284L32.3567 39.2819ZM13.9131 38.332H15.826V39.332H13.9131V38.332ZM19.3905 38.332H21.3034V39.332H19.3905V38.332ZM24.8679 38.332H26.7808V39.332H24.8679V38.332Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'design-at-supabase',
    is_shipped: true,
    links: [
      {
        url: '/blog/how-design-works-at-supabase',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'Supabase Grafana',
    description: 'Observability for your Supabase project',
    is_shipped: true,
    icon: (
      <svg
        width="34"
        height="36"
        viewBox="0 0 34 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M33.4652 15.7973C33.4026 15.2339 33.34 14.5452 33.0896 13.794C32.9017 13.0427 32.5887 12.2288 32.1505 11.3523C31.7122 10.4759 31.1488 9.59936 30.4601 8.72288C30.2097 8.40986 29.8967 8.03422 29.5211 7.7212C30.0219 5.71781 28.895 4.02746 28.895 4.02746C27.0168 3.90224 25.7647 4.5909 25.3264 4.96654C25.2639 4.96654 25.2012 4.90394 25.076 4.84132C24.763 4.71612 24.45 4.5909 24.0743 4.4657C23.6987 4.34048 23.3857 4.27788 23.01 4.15266C22.6344 4.09006 22.2587 4.02746 21.9457 3.96484C21.8831 3.96484 21.8205 3.96484 21.7579 3.96484C20.944 1.3354 18.565 0.208496 18.565 0.208496C15.9356 1.89886 15.4347 4.21527 15.4347 4.21527C15.4347 4.21527 15.4347 4.27788 15.4347 4.34048C15.3095 4.40308 15.1217 4.40308 14.9965 4.4657C14.8086 4.5283 14.6208 4.5909 14.3704 4.65352C14.1826 4.71612 13.9948 4.77872 13.7443 4.90394C13.3687 5.09176 12.9305 5.27956 12.5548 5.46738C12.1792 5.6552 11.8036 5.90562 11.4279 6.15606C11.3653 6.15606 11.3653 6.09344 11.3653 6.09344C7.67159 4.71612 4.41608 6.40648 4.41608 6.40648C4.10306 10.288 5.85602 12.7923 6.23166 13.2305C6.16904 13.4809 6.04384 13.7314 5.98123 13.9818C5.73081 14.8583 5.48038 15.7973 5.35518 16.7365C5.35518 16.8617 5.29256 16.9869 5.29256 17.1121C1.91185 18.8024 0.910156 22.2457 0.910156 22.2457C3.72742 25.5013 7.04553 25.689 7.04553 25.689C7.48378 26.4404 7.92202 27.1289 8.48546 27.8176C8.73588 28.068 8.98631 28.3811 9.17413 28.6315C8.17244 31.574 9.29934 34.0157 9.29934 34.0157C12.4296 34.1409 14.4956 32.6383 14.9339 32.2627C15.2469 32.3879 15.5599 32.4504 15.8729 32.5758C16.812 32.8262 17.8138 32.9514 18.8154 33.0139C19.0658 33.0139 19.3163 33.0139 19.5667 33.0139H19.6919H19.7545H19.8797H20.0049C21.5075 35.1425 24.0743 35.4556 24.0743 35.4556C25.9525 33.5147 26.0151 31.574 26.0151 31.1357V31.0732V31.0105C26.3907 30.7601 26.7663 30.4472 27.142 30.1341C27.8933 29.4454 28.5193 28.6942 29.0828 27.8803C29.1454 27.8176 29.208 27.7551 29.208 27.6299C31.3366 27.7551 32.7765 26.3152 32.7765 26.3152C32.4009 24.1239 31.1487 23.0596 30.8983 22.8719H30.8358C30.8358 22.7465 30.8358 22.6213 30.8358 22.4961C30.8358 22.2457 30.8358 21.9953 30.8358 21.8076V21.4945V21.4318V21.3693V21.3066V21.1814V20.9937C30.8358 20.931 30.8358 20.8685 30.8358 20.8058C30.8358 20.7433 30.8358 20.6806 30.8358 20.6179V20.4302V20.2423C30.7731 19.9919 30.7731 19.7415 30.7106 19.5536C30.4601 18.6147 30.1471 17.7381 29.6463 16.9242C29.1454 16.1104 28.582 15.4217 27.8933 14.7957C27.2046 14.1696 26.4534 13.7314 25.6395 13.3557C24.8256 12.9801 24.0117 12.7923 23.1353 12.6671C22.697 12.6044 22.3214 12.6044 21.8831 12.6044H21.7579H21.6953H21.6327H21.5701H21.4449C21.3823 12.6044 21.3196 12.6044 21.2571 12.6044C21.0692 12.6044 20.8188 12.6671 20.631 12.6671C19.8171 12.7923 19.0033 13.1053 18.3146 13.5435C17.6259 13.9818 16.9999 14.4826 16.499 15.0461C15.9982 15.6095 15.6225 16.2982 15.3721 16.9869C15.1217 17.6756 14.9965 18.3641 14.9339 19.0528C14.9339 19.2407 14.9339 19.3659 14.9339 19.5536C14.9339 19.6163 14.9339 19.6163 14.9339 19.679V19.8042C14.9339 19.8667 14.9339 19.9919 14.9339 20.0546C14.9965 20.3675 15.0591 20.7433 15.1217 21.0562C15.3095 21.6822 15.6225 22.2457 15.9356 22.7465C16.3112 23.2475 16.7494 23.6231 17.1877 23.936C17.6259 24.2491 18.1268 24.4995 18.6276 24.6247C19.1285 24.7499 19.5667 24.8126 20.0676 24.8126C20.1301 24.8126 20.1928 24.8126 20.2553 24.8126H20.318H20.3806C20.4432 24.8126 20.5058 24.8126 20.5058 24.8126C20.5058 24.8126 20.5058 24.8126 20.5684 24.8126H20.631H20.6936C20.7562 24.8126 20.8188 24.8126 20.8814 24.8126C20.944 24.8126 21.0067 24.8126 21.0692 24.7499C21.1944 24.7499 21.2571 24.6874 21.3823 24.6874C21.5701 24.6247 21.7579 24.5622 21.9457 24.437C22.1335 24.3743 22.2587 24.2491 22.4466 24.1239C22.5091 24.1239 22.5092 24.0614 22.5718 23.9987C22.7596 23.8735 22.7596 23.6231 22.6344 23.4979C22.5092 23.3727 22.3214 23.31 22.1962 23.4352C22.1335 23.4352 22.1335 23.4979 22.071 23.4979C21.9457 23.5604 21.8205 23.6231 21.6327 23.6856C21.5075 23.7483 21.3196 23.7483 21.1944 23.8108C21.1319 23.8108 21.0067 23.8108 20.944 23.8108C20.8814 23.8108 20.8814 23.8108 20.8188 23.8108C20.7562 23.8108 20.7562 23.8108 20.6936 23.8108C20.631 23.8108 20.631 23.8108 20.5684 23.8108C20.5058 23.8108 20.4432 23.8108 20.4432 23.8108H20.3806H20.318C20.2553 23.8108 20.2554 23.8108 20.1928 23.8108C19.8171 23.7483 19.5041 23.6856 19.1285 23.4979C18.7528 23.3727 18.4398 23.1223 18.1267 22.8719C17.8138 22.6213 17.5633 22.2457 17.3755 21.9328C17.1877 21.6197 16.9999 21.1814 16.9372 20.7433C16.8746 20.5554 16.8746 20.305 16.8746 20.1171C16.8746 20.0546 16.8746 19.9919 16.8746 19.9294V19.8667V19.8042C16.8746 19.679 16.8746 19.5536 16.9372 19.4284C17.1251 18.4893 17.5633 17.6129 18.3146 16.8617C18.5024 16.6738 18.6902 16.5486 18.8781 16.3607C19.0658 16.2355 19.3163 16.1104 19.5041 15.9852C19.7545 15.86 20.0049 15.7973 20.1928 15.7347C20.4432 15.6721 20.6936 15.6095 20.944 15.6095C21.0692 15.6095 21.1944 15.6095 21.3196 15.6095H21.3823H21.5075H21.5701H21.6953C21.9457 15.6095 22.2587 15.6721 22.5091 15.7347C23.0726 15.8599 23.5734 16.0478 24.0743 16.3607C25.076 16.9242 25.8899 17.8008 26.4534 18.8024C26.7038 19.3032 26.8916 19.8667 27.0168 20.4302C27.0168 20.5554 27.0794 20.7433 27.0794 20.8685V20.9937V21.1189C27.0794 21.1814 27.0794 21.1814 27.0794 21.2441C27.0794 21.3066 27.0794 21.3066 27.0794 21.3693V21.4945V21.6197C27.0794 21.6822 27.0794 21.8074 27.0794 21.8701C27.0794 22.058 27.0794 22.1832 27.0168 22.3709C27.0168 22.4961 26.9542 22.684 26.9542 22.8092C26.9542 22.9344 26.8915 23.1223 26.829 23.2475C26.7663 23.5604 26.6411 23.8735 26.5159 24.1239C26.2655 24.6874 25.9525 25.2509 25.5768 25.8142C24.8256 26.816 23.8239 27.6924 22.6344 28.1932C22.071 28.4438 21.4449 28.6315 20.8188 28.7567C20.5058 28.8194 20.1928 28.8194 19.8797 28.8819H19.8171H19.7545H19.6293H19.5041H19.4415C19.2537 28.8819 19.1285 28.8819 18.9406 28.8819C18.252 28.8194 17.5633 28.6942 16.9372 28.5063C16.3112 28.3186 15.6225 28.068 15.0591 27.7551C13.8696 27.1289 12.8053 26.2525 11.9288 25.1882C11.4905 24.6874 11.1149 24.1239 10.8645 23.4979C10.5514 22.8719 10.301 22.2457 10.1132 21.6197C9.9254 20.9937 9.80019 20.305 9.80019 19.679V19.5536V19.4911V19.3659V18.9903V18.9276V18.8024V18.7399C9.80019 18.6772 9.80019 18.552 9.80019 18.4893C9.80019 18.1764 9.86279 17.8008 9.86279 17.4877C9.9254 17.1746 9.988 16.799 10.0506 16.4861C10.1132 16.173 10.1758 15.7973 10.301 15.4843C10.4888 14.8583 10.7393 14.2322 10.9897 13.6061C11.5531 12.4166 12.2418 11.3523 13.1183 10.5385C13.3061 10.3506 13.5565 10.1002 13.807 9.9124C14.0574 9.72458 14.3078 9.53676 14.5582 9.41156C14.8087 9.22374 15.0591 9.09852 15.3095 8.97332C15.4347 8.9107 15.5599 8.8481 15.6851 8.7855C15.7477 8.7855 15.8103 8.72288 15.8729 8.72288C15.9355 8.72288 15.9982 8.66028 16.0608 8.66028C16.3112 8.53508 16.6242 8.47246 16.8746 8.34726C16.9372 8.34726 16.9999 8.28464 17.0625 8.28464C17.1251 8.28464 17.1877 8.22204 17.2503 8.22204C17.3755 8.15944 17.5633 8.15944 17.6885 8.09684C17.7511 8.09684 17.8138 8.03422 17.939 8.03422C18.0015 8.03422 18.0642 8.03422 18.1894 7.97162C18.252 7.97162 18.3146 7.97162 18.4398 7.90902H18.565H18.6902C18.7528 7.90902 18.8154 7.90902 18.9406 7.8464C19.0033 7.8464 19.1285 7.8464 19.1911 7.7838C19.2537 7.7838 19.3789 7.7838 19.4415 7.7838C19.5041 7.7838 19.5667 7.7838 19.6293 7.7838H19.7545H19.8171H19.8797C19.9424 7.7838 20.0676 7.7838 20.1301 7.7838H20.2553H20.318C20.3806 7.7838 20.4432 7.7838 20.5058 7.7838C20.8188 7.7838 21.0692 7.7838 21.3823 7.7838C21.9457 7.7838 22.5091 7.8464 23.0726 7.97162C24.1369 8.15944 25.1386 8.53508 26.0777 8.97332C27.0168 9.41156 27.8306 9.975 28.5193 10.6011C28.582 10.6637 28.582 10.6637 28.6445 10.7263C28.7072 10.7889 28.7072 10.7889 28.7697 10.8515C28.8324 10.9141 28.9576 10.9767 29.0201 11.1019C29.0828 11.2271 29.208 11.2271 29.2706 11.3523C29.3332 11.4775 29.4584 11.5402 29.5211 11.6028C29.834 11.9158 30.0844 12.2288 30.3349 12.6044C30.8358 13.2305 31.274 13.9192 31.587 14.5452C31.587 14.6078 31.6497 14.6078 31.6497 14.6704C31.6497 14.733 31.7122 14.733 31.7122 14.7957C31.7749 14.8583 31.7749 14.9209 31.8374 15.0461C31.9001 15.1087 31.9001 15.1713 31.9626 15.2965C32.0253 15.3591 32.0253 15.4217 32.0878 15.5469C32.2131 15.86 32.3383 16.1104 32.4009 16.3607C32.5261 16.799 32.6513 17.1746 32.7139 17.4877C32.7765 17.6129 32.9017 17.7381 33.0269 17.6756C33.1521 17.6756 33.2774 17.5504 33.2774 17.425C33.5278 16.6738 33.5278 16.2355 33.4652 15.7973Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'supabase-grafana',
    links: [
      {
        url: 'https://github.com/supabase/supabase-grafana',
        label: 'View on GitHub',
        target: '_blank',
      },
    ],
  },
  {
    title: 'pg_graphql: Postgres functions',
    description: 'pg_graphql now supports User Defined Functions (UDFs)',
    is_shipped: true,
    icon: (
      <svg
        width="32"
        height="36"
        viewBox="0 0 32 36"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.662 31.8127L5.49355 27.6737C4.92892 28.2646 4.13313 28.6326 3.25138 28.6326C1.53875 28.6326 0.150391 27.2442 0.150391 25.5314C0.150391 24.0709 1.16004 22.8461 2.51943 22.5172V14.2427C1.16004 13.9137 0.150391 12.689 0.150391 11.2284C0.150391 9.51568 1.53875 8.12724 3.25138 8.12724C4.13313 8.12724 4.92893 8.49528 5.49355 9.08614L12.662 4.94716C12.5815 4.67102 12.5382 4.37893 12.5382 4.07676C12.5382 2.36403 13.9266 0.975586 15.6392 0.975586C17.3519 0.975586 18.7402 2.36403 18.7402 4.07676C18.7402 4.37806 18.6972 4.66932 18.6171 4.94475L25.7866 9.08429C26.3511 8.49446 27.1462 8.12715 28.0271 8.12715C29.7397 8.12715 31.1281 9.51559 31.1281 11.2283C31.1281 12.6871 30.1208 13.9107 28.7639 14.2414V22.5183C30.1208 22.8491 31.1281 24.0726 31.1281 25.5314C31.1281 27.2442 29.7397 28.6326 28.0271 28.6326C27.1462 28.6326 26.3511 28.2653 25.7866 27.6755L18.6171 31.8151C18.6973 32.0905 18.7402 32.3817 18.7402 32.683C18.7402 34.3957 17.3519 35.7842 15.6392 35.7842C13.9266 35.7842 12.5382 34.3957 12.5382 32.683C12.5382 32.3809 12.5814 32.0888 12.662 31.8127ZM15.6392 7.17793C15.9416 7.17793 16.2339 7.13464 16.5103 7.05391L25.8838 23.2902C25.4635 23.6923 25.1561 24.2114 25.014 24.7953H6.26446C6.12268 24.2129 5.81648 23.6949 5.39782 23.2932L14.7723 7.05513C15.0474 7.13507 15.3383 7.17793 15.6392 7.17793ZM6.26444 26.2677C6.25374 26.3117 6.24209 26.3553 6.22954 26.3985L13.3994 30.5382C13.9639 29.9488 14.7587 29.5818 15.6392 29.5818C16.5206 29.5818 17.3162 29.9496 17.8808 30.5401L25.0496 26.4009C25.0368 26.3569 25.0249 26.3125 25.014 26.2677H6.26444ZM3.99177 14.2406C5.34689 13.9086 6.35236 12.686 6.35236 11.2284C6.35236 10.9275 6.3095 10.6365 6.22954 10.3614L13.3995 6.22159C13.4313 6.25482 13.4639 6.28734 13.4971 6.31912L4.12403 22.5548C4.08034 22.542 4.03624 22.5301 3.99177 22.5192V14.2406ZM27.1586 22.5535C27.2025 22.5407 27.2468 22.5289 27.2916 22.518V14.2417C25.934 13.9115 24.9261 12.6876 24.9261 11.2283C24.9261 10.9265 24.9692 10.6348 25.0496 10.359L17.8807 6.21978C17.8493 6.25259 17.8172 6.28471 17.7845 6.31612L27.1586 22.5535Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'pg-graphql-postgres-functions',
    links: [
      {
        url: '/blog/pg-graphql-postgres-functions',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'PostgREST 12',
    description: 'PostgREST 12 is out: take a look at some of the major new features',
    is_shipped: true,
    className: 'xl:col-span-2 xl:h-auto xl:aspect-auto',
    icon: (
      <svg
        width="27"
        height="40"
        viewBox="0 0 27 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.0322 36.3902L14.3034 37.2516C11.7407 35.013 7.01284 29.8864 8.60341 27.2884C10.194 24.6905 13.3899 25.1014 14.789 25.6316L7.74209 18.2973L11.8373 13.1528C13.3448 11.2589 13.3997 8.5907 11.9712 6.63648L9.15036 2.77743C8.9936 2.56299 9.03699 2.26258 9.24801 2.10126C9.45574 1.94245 9.7517 1.97543 9.91937 2.17607L18.8316 12.8409C17.6386 13.8717 15.1335 16.8169 14.6563 20.3515C14.1791 23.8861 18.1983 27.8627 20.2675 29.4092L19.5164 30.3589C16.4015 28.7684 12.9333 29.5417 11.9833 31.552C11.2234 33.1603 13.6993 35.4476 15.0322 36.3902Z"
          fill="#EDEDED"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.19759 0.72708C9.14118 0.00571147 10.4855 0.155532 11.2472 1.06693L19.3767 10.7951L26.0245 10.7951L26.0244 11.6733L20.1105 11.6733L21.2505 13.0375L19.963 14.1501C19.4762 14.5707 18.6221 15.6091 17.8598 16.8512C17.6227 17.2375 17.3991 17.6362 17.2004 18.035H26.0239V18.9131H16.8061C16.5747 19.4971 16.4176 20.0613 16.3731 20.5649C16.2712 21.718 16.778 23.0811 17.8987 24.5981C18.0694 24.8291 18.2477 25.055 18.4306 25.2745H26.024V26.1526H19.2161C19.968 26.9389 20.7262 27.5922 21.3035 28.0236L22.7276 29.088L20.2085 32.2733H26.0243V33.1515H14.0831L14.0904 33.1609C14.6453 33.8766 15.456 34.571 16.0314 34.978L17.5719 36.0674L14.4036 39.5637L13.9864 39.2718L13.1656 38.5547C11.8166 37.3764 9.88352 35.4297 8.47296 33.3885C8.41881 33.3101 8.36506 33.2311 8.31182 33.1515H0.193359V32.2733H7.76359C7.35983 31.575 7.01869 30.8459 6.80346 30.1156C6.47287 28.9938 6.37285 27.6191 7.12826 26.3853C7.17696 26.3057 7.22678 26.2282 7.27763 26.1526H0.193359V25.2745H7.99604C8.77306 24.4921 9.68669 24.0215 10.597 23.7655L5.93479 18.9131H0.193359V18.035H5.7401L10.484 12.0755C10.5868 11.9464 10.6796 11.812 10.7625 11.6733H0.193359V10.7951H11.1403C11.4367 9.74213 11.2506 8.58204 10.5748 7.6576L7.75399 3.79855C7.04195 2.82446 7.23903 1.45989 8.19759 0.72708ZM18.7299 31.8999L19.4613 32.2733H13.5844C13.5737 32.2303 13.5685 32.1938 13.5665 32.1666L13.5658 32.1543C13.7319 31.8503 14.2141 31.4922 15.2427 31.3084C16.2341 31.1312 17.4976 31.2706 18.7299 31.8999ZM10.6345 1.57894C10.147 0.99558 9.2865 0.899685 8.68253 1.36141C8.06899 1.83046 7.94285 2.70388 8.3986 3.32736L11.2194 7.1864C12.4002 8.80178 12.3549 11.0073 11.1087 12.5728L6.50588 18.3551L12.1887 24.2697L11.442 24.3942C10.1196 24.6147 8.73644 25.2878 7.80923 26.8022C7.21941 27.7656 7.27116 28.878 7.56936 29.8899C7.86856 30.9052 8.45125 31.9526 9.12984 32.9346C10.491 34.9042 12.3731 36.8023 13.6908 37.9534L14.4036 38.5759L16.3999 36.2165L15.5703 35.6299C14.9529 35.1932 14.0769 34.4466 13.4594 33.6501C13.1504 33.2516 12.9395 32.8829 12.839 32.5695C12.7985 32.4432 12.7775 32.3253 12.7702 32.2245C12.7639 32.1379 12.7645 32.0243 12.7975 31.9239L12.7981 31.9221L12.8159 31.8696L12.8256 31.8489C13.1406 31.1825 13.9487 30.7285 15.1022 30.5224C16.2707 30.3135 17.7106 30.4829 19.093 31.1888L19.7734 31.5362L21.5923 29.2363L20.8255 28.6632C19.8489 27.9334 18.3962 26.6151 17.2565 25.0725C16.0848 23.4867 15.451 21.9285 15.5777 20.4946C15.6911 19.2121 16.3986 17.7055 17.1793 16.4335C17.9625 15.1575 18.8667 14.0421 19.4409 13.5459L20.1342 12.9469L10.6345 1.57894Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'postgrest-12',
    links: [
      {
        url: '/blog/postgrest-12',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'Supavisor 1.0',
    description: 'A scalable connection pooler for Postgres',
    is_shipped: true,
    icon: (
      <svg
        width="33"
        height="33"
        viewBox="0 0 33 33"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.26747 1.22998C6.26747 0.677696 6.72413 0.22998 7.28747 0.22998H26.4058C26.9691 0.22998 27.4258 0.677696 27.4258 1.22998V7.96132C27.4258 8.5136 26.9691 8.96132 26.4058 8.96132H22.6745L25.7709 12.2508H31.671C32.2343 12.2508 32.691 12.6986 32.691 13.2508V19.9822C32.691 20.5345 32.2343 20.9822 31.671 20.9822H24.805C24.2417 20.9822 23.785 20.5345 23.785 19.9822V13.2508C23.785 13.2003 23.7889 13.1506 23.7962 13.1021L19.8986 8.96132H17.8671L17.889 12.251H20.2796C20.8429 12.251 21.2996 12.6987 21.2996 13.251V19.5681L26.5941 24.7588C26.8782 24.6939 27.1743 24.6596 27.4786 24.6596C29.6219 24.6596 31.3594 26.363 31.3594 28.4643C31.3594 30.5656 29.6219 32.269 27.4786 32.269C25.3353 32.269 23.5978 30.5656 23.5978 28.4643C23.5978 27.4111 24.0342 26.4579 24.7395 25.769L19.8571 20.9823H17.8666V24.7924C19.5151 25.2315 20.7274 26.7091 20.7274 28.4643C20.7274 30.5656 18.9899 32.269 16.8466 32.269C14.7033 32.269 12.9658 30.5656 12.9658 28.4643C12.9658 26.7091 14.1781 25.2315 15.8266 24.7924V20.9823H13.8361L8.98258 25.7407C9.70503 26.4317 10.1536 27.3967 10.1536 28.4643C10.1536 30.5656 8.41609 32.269 6.27277 32.269C4.12945 32.269 2.39195 30.5656 2.39195 28.4643C2.39195 26.363 4.12945 24.6596 6.27277 24.6596C6.56016 24.6596 6.84025 24.6902 7.10983 24.7483L12.3936 19.5681V13.251C12.3936 12.6987 12.8503 12.251 13.4136 12.251H15.849L15.827 8.96132H13.9686L9.83174 13.3563V19.9822C9.83174 20.5345 9.37508 20.9822 8.81174 20.9822H1.94578C1.38245 20.9822 0.925781 20.5345 0.925781 19.9822V13.2508C0.925781 12.6986 1.38245 12.2508 1.94578 12.2508H8.09635L11.1927 8.96132H7.28747C6.72413 8.96132 6.26747 8.5136 6.26747 7.96132V1.22998ZM7.79174 14.2508H2.96578V18.9822H7.79174V14.2508ZM14.4336 18.9823H19.2596V14.251H14.4336V18.9823ZM6.991 26.8021C6.77031 26.7104 6.52759 26.6596 6.27277 26.6596C5.25611 26.6596 4.43195 27.4676 4.43195 28.4643C4.43195 29.461 5.25611 30.269 6.27277 30.269C7.28942 30.269 8.11359 29.461 8.11359 28.4643C8.11359 27.7198 7.65369 27.0805 6.99721 26.8047C6.99514 26.8039 6.99307 26.803 6.991 26.8021ZM16.7407 26.6625C15.7733 26.7163 15.0058 27.5025 15.0058 28.4643C15.0058 29.461 15.83 30.269 16.8466 30.269C17.8633 30.269 18.6874 29.461 18.6874 28.4643C18.6874 27.5025 17.9199 26.7163 16.9525 26.6625C16.9177 26.6661 16.8824 26.6679 16.8466 26.6679C16.8109 26.6679 16.7755 26.6661 16.7407 26.6625ZM26.7264 26.8167C26.0847 27.0986 25.6378 27.7303 25.6378 28.4643C25.6378 29.461 26.4619 30.269 27.4786 30.269C28.4952 30.269 29.3194 29.461 29.3194 28.4643C29.3194 27.4676 28.4952 26.6596 27.4786 26.6596C27.2144 26.6596 26.9631 26.7142 26.736 26.8125C26.7328 26.8139 26.7296 26.8153 26.7264 26.8167ZM25.825 14.2508V18.9822H30.651V14.2508H25.825ZM8.30747 2.22998V6.96132H25.3858V2.22998H8.30747Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'supavisor',
    links: [
      {
        url: '/blog/supavisor-postgres-connection-pooler',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'Supabase Wrappers v0.2',
    description: 'Query Pushdown & Remote Subqueries',
    is_shipped: true,
    icon: (
      <svg
        width="33"
        height="33"
        viewBox="0 0 33 33"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M26.4285 6.34281C26.3059 6.22029 26.1817 6.10048 26.0559 5.98337C27.9262 9.90843 27.2364 14.7489 23.9866 17.9987C22.4166 19.5687 20.4735 20.5418 18.4428 20.9167L18.4034 30.395C19.012 30.3209 19.6166 30.2074 20.2132 30.0542C22.4894 29.4202 24.6385 28.2082 26.4285 26.4182C31.9721 20.8745 31.9721 11.8865 26.4285 6.34281ZM28.0859 27.7778C25.9818 29.8819 23.4395 31.2829 20.753 31.9804C15.3067 33.498 9.22176 32.1153 4.93885 27.8324C-1.38587 21.5077 -1.38587 11.2533 4.93885 4.9286C9.21486 0.652586 15.2871 -0.732503 20.7267 0.773332C23.4231 1.46852 25.9752 2.87204 28.0859 4.98272C34.3806 11.2774 34.3806 21.4831 28.0859 27.7778ZM6.35306 26.4182C9.12791 29.1931 12.7657 30.579 16.4026 30.576L16.422 25.9151C14.1845 25.8349 11.9682 24.9406 10.2592 23.2316C6.67332 19.6457 6.67332 13.8319 10.2592 10.246C13.845 6.66018 19.6589 6.66018 23.2447 10.246C23.9077 10.909 24.4484 11.6489 24.8666 12.437C25.4979 9.66225 24.7332 6.63404 22.5724 4.47324C21.7373 3.63814 20.7746 3.01281 19.7482 2.59531C19.3128 2.49914 18.8738 2.42376 18.4326 2.36917C18.3357 2.35718 18.2438 2.33172 18.1585 2.2949C13.9616 1.77114 9.57543 3.12045 6.35306 6.34281C0.809395 11.8865 0.809396 20.8745 6.35306 26.4182ZM23.7498 15.1174C23.4579 13.851 22.819 12.6488 21.8305 11.6603C19.0257 8.85544 14.4782 8.85544 11.6734 11.6603C8.86859 14.4651 8.86859 19.0126 11.6734 21.8174C12.994 23.138 14.6997 23.8369 16.4303 23.9138L16.4465 20.0436C16.4486 19.5304 16.839 19.1021 17.3498 19.0525C19.2568 18.8673 21.1115 18.0454 22.5724 16.5845C23.0265 16.1303 23.419 15.6378 23.7498 15.1174Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'supabase-wrappers-v0.2',
    links: [
      {
        url: '/blog/supabase-wrappers-v02',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'Supabase Libraries V2',
    description: 'Swift, Kotlin, C#, and Python are now stable and moving to the v2 API',
    type: 'clientLibs',
    is_shipped: true,
    className: 'xl:col-span-2 xl:aspect-auto xl:col-span-3 xl:aspect-auto xl:h-full',
    id: 'supabase-libraries-v2',
    links: [
      {
        url: '/blog/client-libraries-v2',
        label: 'Read blog post',
      },
    ],
    icons: [
      {
        url: '/blog/client-libraries-v2#supabase-python-v2',
        label: 'Python',
        icon: (
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 45 45"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M16.9371 1.08262C18.4527 0.81443 20.1752 0.660598 21.9789 0.652217C23.7827 0.643836 25.6633 0.780906 27.4758 1.08262C30.3391 1.55984 32.7514 3.70781 32.7514 6.56721V16.6141C32.7514 19.5603 30.4094 21.9757 27.4758 21.9757H16.9371C13.3589 21.9757 10.3457 25.0476 10.3457 28.5302V33.3507H6.71801C3.65157 33.3507 1.86045 31.125 1.11045 28.0014C0.0987175 23.805 0.141687 21.2971 1.11045 17.2782C1.9503 13.7719 4.63536 11.9288 7.70179 11.9288H22.2126V10.5884H11.6615V6.56721C11.6615 3.52244 12.4722 1.87142 16.9371 1.08262ZM18.2529 5.90316C18.2529 4.79095 17.3629 3.8864 16.273 3.8864C15.1792 3.8864 14.2931 4.79095 14.2931 5.90316C14.2931 7.01142 15.1792 7.90762 16.273 7.90762C17.3629 7.90762 18.2529 7.01143 18.2529 5.90316Z"
              fill="currentColor"
            />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M34.0671 16.6143V11.929H38.0269C41.0972 11.929 42.5446 14.2257 43.3024 17.2783C44.3571 21.5181 44.404 24.6965 43.3024 28.0016C42.236 31.212 41.0933 33.3509 38.0269 33.3509H22.2125V34.6913H32.7513V38.7125C32.7513 41.7573 30.1321 43.3051 27.4758 44.0741C23.4797 45.2337 20.2769 45.0562 16.937 44.0741C14.1479 43.2538 11.6615 41.5719 11.6615 38.7125V28.6656C11.6615 25.7747 14.0503 23.304 16.937 23.304H27.4758C30.9875 23.304 34.0671 20.2467 34.0671 16.6143ZM30.1197 39.3766C30.1197 38.2683 29.2336 37.3721 28.1398 37.3721C27.05 37.3721 26.16 38.2683 26.16 39.3766C26.16 40.4888 27.05 41.3933 28.1398 41.3933C29.2336 41.3933 30.1197 40.4888 30.1197 39.3766Z"
              fill="currentColor"
            />
          </svg>
        ),
      },
      {
        url: '/blog/client-libraries-v2#supabase-swift-v2',
        label: 'Swift',
        icon: (
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 45 46"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M44.7763 11.4367V34.0513C44.7763 35.0639 44.6919 36.0342 44.5231 37.0046C44.3544 37.975 44.059 38.9032 43.5949 39.7893C43.1308 40.6331 42.5823 41.4347 41.8651 42.152C41.19 42.8692 40.3884 43.4177 39.5024 43.8818C38.6163 44.3459 37.6881 44.6413 36.7177 44.81C35.7611 44.9764 34.7634 45.0198 33.8055 45.0614L33.7643 45.0632H11.1076C10.095 45.0632 9.12464 44.9788 8.15424 44.81C7.18384 44.6413 6.25563 44.3459 5.36961 43.8818C4.52579 43.4177 3.72416 42.8692 3.00691 42.152C2.28965 41.4769 1.74116 40.6753 1.27706 39.7893C0.812956 38.9032 0.517618 37.975 0.348853 37.0046C0.182461 36.0479 0.139106 35.0501 0.0974827 34.0922L0.0957031 34.0513V11.3945C0.137894 10.3819 0.180088 9.41155 0.348853 8.44115C0.517618 7.47075 0.812956 6.54254 1.27706 5.65652C1.74116 4.8127 2.28965 4.01106 3.00691 3.29381C3.09129 3.20942 3.18622 3.12504 3.28115 3.04066C3.37607 2.95628 3.471 2.8719 3.55539 2.78752C4.14606 2.32341 4.73675 1.9015 5.41181 1.56397C5.51729 1.52178 5.63331 1.46904 5.74934 1.4163C5.86536 1.36356 5.98139 1.31082 6.08686 1.26863C6.76192 1.01548 7.47917 0.804529 8.19643 0.677955C8.89378 0.554892 9.63103 0.511595 10.3306 0.470511C10.3506 0.469339 10.3705 0.468169 10.3904 0.466999C10.6013 0.424807 10.8545 0.424805 11.1076 0.424805H33.7643C34.7769 0.424805 35.7473 0.50919 36.7177 0.677955C37.6881 0.84672 38.6163 1.14205 39.5024 1.60616C40.3462 2.07026 41.1478 2.61875 41.8651 3.336C42.5823 4.01106 43.1308 4.8127 43.5949 5.69871C44.059 6.58473 44.3544 7.51294 44.5231 8.48334C44.6895 9.44008 44.7329 10.4378 44.7745 11.3957L44.7763 11.4367ZM35.5786 27.5116L35.452 28.0179C39.3336 32.7433 38.2788 37.8063 37.6882 36.9203C35.663 33.0387 31.9501 34.0091 30.0515 34.9795C29.9672 35.0217 29.8828 35.0744 29.7984 35.1271C29.714 35.1799 29.6296 35.2326 29.5452 35.2748C29.5452 35.317 29.503 35.317 29.503 35.317C25.5793 37.4266 20.2632 37.5531 14.9471 35.2748C10.4748 33.334 6.80414 30.0431 4.44143 26.2459C5.66498 27.1319 6.97291 27.9335 8.36522 28.5664C13.9767 31.2244 19.6303 31.0135 23.6385 28.5664C17.9426 24.1785 13.175 18.4827 9.54658 13.8416C8.87152 13.04 8.23864 12.154 7.69016 11.268C12.0781 15.234 18.9552 20.2547 21.4445 21.647C16.2128 16.12 11.5718 9.28498 11.7827 9.49593C20.0522 17.8076 27.6888 22.533 27.6888 22.533C27.9842 22.6596 28.1951 22.7862 28.3639 22.9128C28.5326 22.4908 28.6592 22.0689 28.7858 21.647C30.0937 16.8372 28.617 11.3102 25.2417 6.7535C32.9205 11.3523 37.435 20.1281 35.5786 27.5116Z"
              fill="currentColor"
            />
          </svg>
        ),
      },
      {
        url: '/blog/client-libraries-v2#supabase-kotlin-v2',
        label: 'Kotlin',
        icon: (
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M39.5927 39.2524H0.618164V0.277832H39.5927L19.7021 19.4825L39.5927 39.2524Z"
              fill="currentColor"
            />
          </svg>
        ),
      },
      {
        url: '/blog/client-libraries-v2#typescript-v2-updates',
        label: 'Typescript',
        icon: (
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 44 43"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M2.1342 0.725586H41.4989C42.3803 0.725586 43.0948 1.43205 43.0948 2.30352V41.2259C43.0948 42.0974 42.3803 42.8038 41.4989 42.8038H2.1342C1.25282 42.8038 0.53833 42.0974 0.53833 41.2259V2.30352C0.53833 1.43205 1.25282 0.725586 2.1342 0.725586ZM24.1952 23.1119V19.6608H9.04961V23.1119H14.4568V38.4778H18.7614V23.1119H24.1952ZM25.9113 38.0721C26.6055 38.422 27.4265 38.6845 28.3742 38.8595C29.3221 39.0344 30.321 39.122 31.3712 39.122C32.3946 39.122 33.3668 39.0257 34.288 38.8332C35.209 38.6408 36.0166 38.3236 36.7107 37.8818C37.4048 37.44 37.9545 36.8627 38.3594 36.1496C38.7643 35.4366 38.9668 34.5552 38.9668 33.5053C38.9668 32.7442 38.851 32.0773 38.6196 31.5042C38.3883 30.9311 38.0546 30.4215 37.6185 29.9753C37.1825 29.5291 36.6596 29.1289 36.0499 28.7746C35.4403 28.4203 34.7529 28.0857 33.9876 27.7707C33.4268 27.5432 32.924 27.3223 32.4791 27.108C32.034 26.8937 31.6558 26.6749 31.3444 26.4519C31.0329 26.2287 30.7927 25.9926 30.6236 25.7432C30.4545 25.494 30.37 25.2117 30.37 24.8968C30.37 24.6081 30.4456 24.3478 30.5969 24.1161C30.7482 23.8841 30.9617 23.6851 31.2376 23.5189C31.5136 23.3527 31.8517 23.2237 32.2522 23.1318C32.6526 23.04 33.0977 22.994 33.5871 22.994C33.9431 22.994 34.3191 23.0202 34.7151 23.0727C35.1111 23.1253 35.5094 23.2062 35.9098 23.3154C36.3103 23.4248 36.6997 23.5626 37.0779 23.7289C37.4561 23.8952 37.8054 24.0876 38.1258 24.3062V20.3826C37.4761 20.1376 36.7664 19.956 35.9965 19.838C35.2268 19.7199 34.3435 19.6608 33.3467 19.6608C32.3323 19.6608 31.3712 19.7679 30.4634 19.9824C29.5556 20.1966 28.757 20.5313 28.0672 20.9863C27.3774 21.4412 26.8324 22.0207 26.432 22.7249C26.0314 23.4292 25.8312 24.2713 25.8312 25.251C25.8312 26.5022 26.1983 27.5696 26.9325 28.453C27.6668 29.3367 28.7814 30.0847 30.2765 30.6971C30.8639 30.9334 31.4111 31.1651 31.9185 31.3927C32.4257 31.6201 32.864 31.8564 33.2333 32.1012C33.6026 32.3462 33.8941 32.613 34.1077 32.9017C34.3213 33.1904 34.4281 33.5184 34.4281 33.886C34.4281 34.1572 34.3613 34.4087 34.2278 34.6405C34.0943 34.8724 33.8918 35.0736 33.6204 35.2441C33.349 35.4147 33.0109 35.5482 32.606 35.6444C32.2011 35.7407 31.727 35.7887 31.1842 35.7887C30.2587 35.7887 29.342 35.6292 28.4343 35.3098C27.5267 34.9905 26.6856 34.5115 25.9113 33.8729V38.0721Z"
              fill="currentColor"
            />
          </svg>
        ),
      },
      {
        url: '/blog/client-libraries-v2#flutter-v2-updates',
        label: 'Flutter',
        icon: (
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 35 42"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M34.8363 0.230957L21.8372 0.24498L0.876465 20.7638L7.34342 27.1022L13.0273 21.5694L34.8363 0.230957Z"
              fill="currentColor"
            />
            <path
              d="M22.2101 19.1603C22.0257 19.152 21.8355 19.1434 21.7162 19.2976L10.5389 30.2377L16.9592 36.4842L16.9518 36.4914L21.5464 40.9929C21.5766 41.0191 21.6065 41.0484 21.637 41.0782C21.7627 41.2012 21.8977 41.3332 22.0924 41.2905C24.2148 41.2859 26.3373 41.2866 28.46 41.2874C30.5827 41.2882 32.7056 41.289 34.8289 41.2843L23.5293 30.2129L34.8252 19.1621L22.4309 19.159C22.3619 19.1672 22.2865 19.1638 22.2101 19.1603Z"
              fill="currentColor"
            />
          </svg>
        ),
      },
    ],
  },
  {
    title: 'Supabase x Fly.io',
    description:
      "We're launching a managed Postgres offering jointly developed by Supabase and Fly.io",
    is_shipped: true,
    className: 'xl:col-span-2 xl:aspect-auto h-full',
    icon: (
      <svg
        width="43"
        height="37"
        viewBox="0 0 43 37"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M23.5592 31.9455L18.5586 36.1655C18.2529 36.4172 17.8695 36.5545 17.4734 36.5545H30.3779C29.9803 36.554 29.5944 36.4166 29.2861 36.1655L24.2131 31.9445C24.1212 31.8681 24.0054 31.8263 23.8859 31.8265C23.7664 31.8267 23.6508 31.8688 23.5592 31.9455Z"
          fill="#EDEDED"
        />
        <path
          d="M21.7205 21.1154L21.6715 21.1012H21.6199L21.6185 21.1016C21.5709 21.1155 21.57 21.1157 21.5275 21.1435L21.4657 21.1992C21.379 21.2793 21.2958 21.3625 21.2146 21.4483C21.0885 21.5805 20.9706 21.7204 20.8619 21.8674C20.7965 21.9582 20.7378 22.0537 20.6863 22.1527C20.6464 22.2318 20.6148 22.315 20.5928 22.4008C20.5842 22.4401 20.578 22.4794 20.5755 22.5192L20.5801 22.6662C20.5837 22.6988 20.5887 22.7311 20.5949 22.7632C20.632 22.9542 20.7328 23.127 20.8807 23.2533L21.0063 23.3452C21.0723 23.3854 21.1425 23.4183 21.2156 23.4432C21.2875 23.4679 21.3615 23.4858 21.4366 23.4968L21.5974 23.5111L21.7878 23.5054C21.9674 23.4889 22.1409 23.4319 22.2952 23.3385C22.4141 23.2654 22.5138 23.1652 22.5863 23.046C22.6589 22.9268 22.702 22.7921 22.7123 22.653L22.7138 22.4988C22.7067 22.4166 22.6837 22.336 22.6531 22.2594C22.6357 22.2155 22.6158 22.1721 22.5939 22.1303C22.5683 22.0818 22.5413 22.0353 22.5122 21.9894C22.4448 21.8852 22.3708 21.7852 22.2917 21.6897C22.2294 21.6142 22.1646 21.5412 22.0982 21.4702C22.0038 21.3702 21.9058 21.2727 21.8047 21.1798L21.7638 21.1435C21.7601 21.141 21.7564 21.1383 21.7527 21.1357C21.7424 21.1284 21.7321 21.1211 21.7205 21.1154Z"
          fill="#EDEDED"
        />
        <path
          d="M21.3754 0.605469L21.3555 0.607C20.9007 0.652801 20.4634 0.806831 20.0803 1.05621C19.4907 1.43446 19.0405 2.00057 18.7087 2.61517C18.6536 2.71777 18.601 2.82242 18.552 2.92757C18.4183 3.21956 18.3034 3.51869 18.2074 3.82497C18.1411 4.03682 18.0829 4.25223 18.0329 4.46918C17.9701 4.74228 17.919 5.01844 17.8777 5.29562C17.8256 5.64784 17.7894 6.00262 17.7669 6.3579C17.7526 6.58404 17.7434 6.81017 17.7393 7.03682C17.7325 7.29691 17.7375 7.55717 17.7541 7.81681C17.797 8.42529 17.9012 9.02763 18.0364 9.6213C18.2117 10.3685 18.4272 11.1057 18.6822 11.8296C19.0015 12.7384 19.359 13.6333 19.7536 14.5121C20.2401 15.5976 20.7668 16.6647 21.3325 17.7112L21.3754 17.7903V0.605469Z"
          fill="#EDEDED"
        />
        <path
          d="M24.7399 0.98015L23.5837 0.65039L23.7981 0.807102C24.4372 1.30991 24.9093 1.99853 25.2478 2.73564C25.3904 3.04921 25.5121 3.37184 25.6122 3.70144C25.8767 4.57076 26.0145 5.47429 26.0686 6.38036C26.0818 6.60561 26.0901 6.83112 26.0936 7.05673C26.1028 7.42528 26.089 7.79486 26.0512 8.1624C26.0063 8.60293 25.9338 9.04091 25.8414 9.47429C25.7337 9.97965 25.6031 10.4794 25.455 10.974C25.2559 11.6341 25.0313 12.2859 24.7827 12.9291C24.5566 13.5146 24.3162 14.0945 24.0609 14.6683C23.6022 15.6977 23.1073 16.7106 22.577 17.705C23.3585 16.8995 24.1106 16.0658 24.8317 15.2058C25.4959 14.4175 26.1233 13.5988 26.7118 12.7525C27.1865 12.0639 27.6301 11.3518 28.015 10.608C28.1891 10.2732 28.3478 9.93014 28.4908 9.58047C28.6056 9.29936 28.7056 9.01242 28.7904 8.72084C28.8961 8.36454 28.9655 7.99854 28.9982 7.62845C29.0114 7.46306 29.0125 7.29716 29.0084 7.13177C29.0048 6.95361 28.9961 6.77546 28.9808 6.59782C28.9282 5.91992 28.7792 5.25274 28.5387 4.61619C28.3728 4.18587 28.1605 3.77393 27.9042 3.3921C27.4668 2.7475 26.9103 2.19238 26.2646 1.75657C25.7909 1.43497 25.2753 1.1777 24.7399 0.98015Z"
          fill="#EDEDED"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M34.8247 4.03417H29.9987L30.0901 4.26184L30.16 4.46756L30.3632 5.20773C30.3673 5.22101 30.3698 5.23428 30.3724 5.24755L30.5306 6.24857L30.5623 6.59569L30.5878 7.04286L30.5904 7.13576L30.5807 7.635L30.5725 7.76721L30.5454 7.98313L30.4061 8.78048L30.3724 8.92647L30.0513 9.92801C30.0484 9.93772 30.045 9.94726 30.0411 9.95659L29.743 10.6784L29.7318 10.7024L29.3423 11.4803L28.6613 12.6661L28.2999 13.219L27.866 13.8678L27.5602 14.2869L26.9543 15.097L26.5419 15.6156L25.9324 16.3578L25.5051 16.8576L24.8481 17.5967L24.4255 18.0607L23.2034 19.3369L22.9191 19.6177L23.087 19.7606C23.3245 19.979 23.5475 20.2126 23.7547 20.4599C23.817 20.5354 23.8772 20.6125 23.9354 20.6912C24.0626 20.863 24.177 21.044 24.2774 21.2328C24.3316 21.3352 24.3802 21.4405 24.4229 21.5482C24.5216 21.7955 24.5849 22.0556 24.6108 22.3206L24.622 22.5212C24.6281 22.905 24.5582 23.2864 24.4153 23.6432C24.2325 24.1007 23.9312 24.5014 23.5424 24.804C23.4066 24.9102 23.2616 25.0036 23.1095 25.0842C22.7568 25.268 22.3721 25.3822 21.9763 25.4206H21.9737C21.5098 25.4674 21.0412 25.4141 20.5995 25.2644C20.3964 25.194 20.2009 25.1016 20.0176 24.9893C19.8022 24.8576 19.6057 24.6973 19.4331 24.5135C19.1957 24.2609 19.0077 23.966 18.8788 23.6442C18.7515 23.3267 18.6819 22.9892 18.673 22.6473L18.6766 22.4048C18.695 22.124 18.7558 21.8476 18.8568 21.585C18.896 21.4822 18.9405 21.3815 18.99 21.2833C19.0871 21.0942 19.1982 20.9126 19.3224 20.7402C19.4038 20.6273 19.4896 20.5176 19.5796 20.4114C19.767 20.1919 19.9671 19.9831 20.1779 19.7866L20.2555 19.7167L20.38 19.6125L19.7593 18.9959L19.3836 18.6069L18.4352 17.5865L18.1988 17.3277L17.0482 15.9826L16.7226 15.5789L15.7149 14.2675L15.6031 14.1164L14.8287 12.976L14.6164 12.6324L14.0615 11.6799L13.892 11.3685L13.4122 10.3548L13.3213 10.122L13.0457 9.34608L12.9395 8.97956L12.8578 8.64112L12.7889 8.27359L12.7348 7.86522L12.7129 7.60437L12.7037 7.15924L12.7057 7.06583L12.7185 6.79936L12.8144 5.82846L12.8446 5.65847L13.063 4.6891L13.1136 4.52677L13.2759 4.05357L13.2846 4.03417H8.46936C4.17635 4.03417 0.691406 7.51912 0.691406 11.8116V28.781C0.691406 31.2037 1.80167 33.3691 3.54082 34.7963C3.58742 34.8015 3.63578 34.8074 3.68593 34.8135C4.3604 34.8958 5.36106 35.0178 6.82209 33.8295L14.6215 26.9684C14.775 26.8438 14.9667 26.7758 15.1644 26.7758C15.3621 26.7758 15.5537 26.8438 15.7072 26.9684L21.3213 31.5845L23.3147 29.9454C23.4684 29.8211 23.6601 29.7532 23.8578 29.7532C24.0555 29.7532 24.2473 29.8211 24.401 29.9454L30.5368 34.9903C31.2769 35.5503 32.2351 36.2129 34.4444 36.5585H34.8247C39.1172 36.5585 42.6021 33.0735 42.6021 28.781V11.8116C42.6021 7.51912 39.1172 4.03417 34.8247 4.03417ZM30.9973 25.7428C31.3414 25.3987 31.8062 25.2024 32.2928 25.1955C32.7794 25.2024 33.2442 25.3987 33.5884 25.7428C33.9326 26.0869 34.1291 26.5516 34.1361 27.0383C34.1287 27.5248 33.9321 27.9892 33.5879 28.3332C33.2438 28.6771 32.7792 28.8734 32.2928 28.8806C31.8063 28.8736 31.3417 28.6772 30.9976 28.3333C30.6536 27.9893 30.4571 27.5248 30.45 27.0383C30.4569 26.5517 30.6532 26.0869 30.9973 25.7428Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'fly-postgres',
    links: [
      {
        url: '/blog/postgres-on-fly-by-supabase',
        label: 'Read blog post',
      },
    ],
  },
  {
    title: 'Top 10 Launches of LWX',
    description: 'Our CEO takes a look at his favorite ships from LWX',
    className: 'sm:aspect-[2/1] sm:col-span-1 sm:col-span-2 sm:aspect-[2/1] h-full',
    is_shipped: true,
    icon: (
      <svg
        width="43"
        height="43"
        viewBox="0 0 97 96"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M45.1845 28.8958C46.1844 27.6366 48.2117 28.3265 48.2358 29.9343L48.3161 42.1777H64.0052C66.8691 42.1777 68.4663 45.4854 66.6855 47.7284L51.5973 66.731C50.5975 67.9901 48.5702 67.3003 48.5461 65.6926L48.3627 53.4492H32.7766C29.9127 53.4492 28.3154 50.1414 30.0963 47.8985L45.1845 28.8958Z"
          fill="#EDEDED"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M55.6755 2.97563C51.6248 -0.991875 45.1451 -0.991877 41.0944 2.97563L34.7454 9.19414C33.669 10.2485 32.2267 10.8459 30.72 10.8615L21.8335 10.9537C16.1638 11.0126 11.5819 15.5944 11.5231 21.2642L11.4309 30.1507C11.4152 31.6574 10.8178 33.0997 9.76348 34.1761L3.54496 40.525C-0.422539 44.5758 -0.422541 51.0555 3.54496 55.1062L9.76348 61.4551C10.8178 62.5316 11.4152 63.9739 11.4309 65.4805L11.5231 74.3671C11.5819 80.0368 16.1638 84.6187 21.8335 84.6775L30.72 84.7697C32.2267 84.7854 33.669 85.3828 34.7454 86.4371L41.0944 92.6556C45.1451 96.6231 51.6248 96.6231 55.6755 92.6556L62.0245 86.4371C63.1009 85.3828 64.5432 84.7854 66.0499 84.7697L74.9364 84.6775C80.6061 84.6187 85.188 80.0368 85.2468 74.3671L85.3391 65.4805C85.3547 63.9739 85.9521 62.5316 87.0064 61.4551L93.2249 55.1062C97.1924 51.0555 97.1925 44.5758 93.2249 40.525L87.0064 34.1761C85.9521 33.0997 85.3547 31.6574 85.3391 30.1507L85.2468 21.2642C85.188 15.5944 80.6061 11.0126 74.9364 10.9537L66.0499 10.8615C64.5432 10.8459 63.1009 10.2485 62.0245 9.19414L55.6755 2.97563ZM44.299 6.24742C46.5692 4.02384 50.2007 4.02384 52.4709 6.24742L58.8199 12.4659C60.7405 14.3471 63.314 15.4131 66.0023 15.441L74.8889 15.5332C78.0665 15.5662 80.6344 18.1341 80.6673 21.3117L80.7596 30.1982C80.7875 32.8866 81.8534 35.46 83.7346 37.3807L89.9532 43.7296C92.1767 45.9998 92.1767 49.6314 89.9531 51.9016L83.7346 58.2506C81.8534 60.1712 80.7875 62.7447 80.7596 65.433L80.6673 74.3195C80.6344 77.4971 78.0665 80.065 74.8889 80.098L66.0023 80.1902C63.314 80.2181 60.7405 81.2841 58.8199 83.1653L52.4709 89.3838C50.2007 91.6074 46.5692 91.6074 44.299 89.3838L37.95 83.1653C36.0294 81.2841 33.4559 80.2181 30.7676 80.1902L21.881 80.098C18.7034 80.065 16.1355 77.4971 16.1026 74.3195L16.0103 65.433C15.9824 62.7447 14.9165 60.1712 13.0353 58.2505L6.81676 51.9016C4.59317 49.6314 4.59317 45.9998 6.81676 43.7296L13.0353 37.3807C14.9165 35.46 15.9824 32.8865 16.0103 30.1982L16.1026 21.3117C16.1355 18.1341 18.7034 15.5662 21.881 15.5332L30.7676 15.441C33.4559 15.4131 36.0294 14.3471 37.95 12.4659L44.299 6.24742Z"
          fill="#EDEDED"
        />
      </svg>
    ),
    id: 'launch-week-x-best-launches',
    links: [
      {
        url: '/blog/launch-week-x-best-launches',
        label: 'Read blog post',
      },
    ],
  },
]
