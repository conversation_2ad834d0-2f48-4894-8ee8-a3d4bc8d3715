import { useBreakpoint } from 'common'
import { AnimatePresence, motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import { cn } from 'ui'
import { EASE_IN, EASE_OUT } from '~/lib/animations'
import { useSendTelemetryEvent } from '~/lib/telemetry'
import SectionContainer from '../Layouts/SectionContainer'

type Framework = {
  name: string
  icon: string
  docs: string
}

const frameworks: Framework[] = [
  {
    name: 'React',
    icon: 'M45.74 23.6983C45.2739 23.5379 44.7909 23.3861 44.2937 23.2426C44.3754 22.909 44.4504 22.5798 44.5171 22.2561C45.6119 16.9418 44.8961 12.6605 42.4518 11.2509C40.1079 9.89927 36.2748 11.3085 32.4035 14.6776C32.0313 15.0016 31.6579 15.3446 31.2848 15.704C31.0362 15.4662 30.7879 15.2364 30.5403 15.0165C26.4831 11.4141 22.4164 9.89599 19.9744 11.3097C17.6329 12.6652 16.9394 16.69 17.9249 21.7265C18.0201 22.2129 18.1313 22.7097 18.2571 23.2148C17.6816 23.3782 17.1259 23.5524 16.5943 23.7377C11.8376 25.3961 8.7998 27.9952 8.7998 30.6911C8.7998 33.4755 12.0609 36.2683 17.0153 37.9617C17.4063 38.0953 17.812 38.2217 18.2301 38.3416C18.0944 38.8879 17.9763 39.4232 17.8773 39.9454C16.9376 44.8944 17.6714 48.8242 20.0068 50.1711C22.4189 51.5622 26.4673 50.1324 30.4093 46.6865C30.7209 46.4141 31.0336 46.1253 31.3469 45.8225C31.7529 46.2135 32.1582 46.5835 32.5615 46.9306C36.3798 50.2164 40.151 51.5432 42.4842 50.1925C44.894 48.7975 45.6772 44.576 44.6604 39.4399C44.5828 39.0476 44.4924 38.6469 44.3909 38.239C44.6752 38.155 44.9543 38.0682 45.2265 37.978C50.3771 36.2715 53.7282 33.5127 53.7282 30.6911C53.7282 27.9854 50.5924 25.3688 45.74 23.6983ZM44.6228 36.1561C44.3772 36.2375 44.1251 36.3161 43.8682 36.3923C43.2996 34.5922 42.5322 32.6781 41.5931 30.7005C42.4893 28.7699 43.227 26.8803 43.7797 25.0919C44.2393 25.2249 44.6854 25.3651 45.1152 25.5132C49.2728 26.9444 51.8089 29.0605 51.8089 30.6911C51.8089 32.4279 49.07 34.6826 44.6228 36.1561ZM42.7776 39.8126C43.2272 42.0837 43.2914 44.1371 42.9936 45.7423C42.726 47.1847 42.1878 48.1463 41.5225 48.5315C40.1066 49.351 37.0787 48.2857 33.8132 45.4757C33.4388 45.1535 33.0618 44.8096 32.6835 44.4455C33.9495 43.061 35.2147 41.4514 36.4495 39.6638C38.6215 39.4711 40.6735 39.156 42.5344 38.7258C42.626 39.0955 42.7074 39.4581 42.7776 39.8126ZM24.1169 48.3898C22.7336 48.8784 21.6318 48.8924 20.9658 48.5084C19.5486 47.691 18.9594 44.5358 19.7631 40.3033C19.8551 39.8186 19.9647 39.3207 20.091 38.8118C21.9314 39.2187 23.9684 39.5116 26.1456 39.6881C27.3887 41.4373 28.6905 43.0452 30.0024 44.453C29.7157 44.7297 29.4302 44.9931 29.1463 45.2413C27.4032 46.7651 25.6564 47.8461 24.1169 48.3898ZM17.6361 36.1455C15.4453 35.3967 13.6361 34.4235 12.396 33.3616C11.2817 32.4073 10.7191 31.4599 10.7191 30.6911C10.7191 29.0551 13.1581 26.9684 17.226 25.5501C17.7196 25.378 18.2363 25.2158 18.7725 25.0635C19.3347 26.8923 20.0722 28.8043 20.9623 30.7378C20.0607 32.7 19.3128 34.6425 18.745 36.4927C18.3628 36.3829 17.9924 36.2672 17.6361 36.1455ZM19.8085 21.3579C18.9642 17.0428 19.5249 13.7876 20.936 12.9708C22.4391 12.1006 25.7628 13.3413 29.2659 16.4518C29.4898 16.6506 29.7146 16.8587 29.9401 17.074C28.6347 18.4756 27.3448 20.0714 26.1127 21.8103C23.9997 22.0061 21.977 22.3208 20.1174 22.742C20.0004 22.2717 19.8969 21.8097 19.8085 21.3579ZM39.1886 26.1433C38.744 25.3754 38.2876 24.6257 37.8223 23.8964C39.2558 24.0777 40.6293 24.3182 41.9191 24.6126C41.5318 25.8536 41.0492 27.1511 40.4811 28.4813C40.0735 27.7076 39.6425 26.9275 39.1886 26.1433ZM31.2854 18.4456C32.1707 19.4047 33.0573 20.4756 33.9293 21.6374C33.0506 21.5959 32.161 21.5743 31.264 21.5743C30.3755 21.5743 29.4925 21.5954 28.6192 21.6362C29.4921 20.4852 30.3863 19.4158 31.2854 18.4456ZM23.3317 26.1566C22.8876 26.9267 22.4645 27.7025 22.0634 28.4799C21.5045 27.1543 21.0263 25.8509 20.6357 24.5923C21.9176 24.3054 23.2846 24.0709 24.7089 23.8931C24.2371 24.6291 23.7769 25.3843 23.3317 26.1564V26.1566ZM24.75 37.626C23.2783 37.4618 21.8908 37.2394 20.6093 36.9604C21.0061 35.6793 21.4948 34.3481 22.0655 32.994C22.4677 33.7707 22.8925 34.5469 23.3393 35.3187H23.3393C23.7945 36.1049 24.266 36.875 24.75 37.626ZM31.3385 43.0719C30.4289 42.0904 29.5215 41.0047 28.6353 39.8368C29.4956 39.8706 30.3726 39.8879 31.264 39.8879C32.1798 39.8879 33.085 39.8672 33.9761 39.8276C33.1012 41.0164 32.2178 42.1038 31.3385 43.0719ZM40.4994 32.9249C41.0999 34.2937 41.6061 35.618 42.0081 36.8772C40.7054 37.1744 39.2989 37.4138 37.8171 37.5916C38.2835 36.8525 38.7439 36.0899 39.1963 35.3055C39.6539 34.5118 40.0885 33.717 40.4994 32.9249ZM37.5337 34.3466C36.8314 35.5643 36.1104 36.7268 35.3784 37.8241C34.0452 37.9194 32.6678 37.9685 31.264 37.9685C29.8659 37.9685 28.5058 37.9251 27.1962 37.8401C26.4347 36.7284 25.698 35.5625 25.0002 34.3571H25.0004C24.3044 33.155 23.6638 31.9427 23.0834 30.7372C23.6636 29.5289 24.3025 28.3152 24.9945 27.1152L24.9944 27.1155C25.6882 25.9123 26.4184 24.7521 27.1729 23.6473C28.509 23.5463 29.8792 23.4936 31.2639 23.4936H31.264C32.655 23.4936 34.0269 23.5467 35.3626 23.6486C36.1056 24.7453 36.8308 25.9017 37.5274 27.1051C38.2319 28.3219 38.879 29.5275 39.4642 30.7099C38.8808 31.9126 38.2351 33.1303 37.5337 34.3466ZM41.4931 12.9137C42.9976 13.7813 43.5826 17.2804 42.6374 21.8688C42.5771 22.1615 42.5092 22.4597 42.4354 22.762C40.5715 22.3319 38.5474 22.0118 36.4282 21.813C35.1937 20.055 33.9143 18.4567 32.6302 17.0731C32.9755 16.741 33.3202 16.4243 33.6636 16.1254C36.9805 13.2388 40.0806 12.0991 41.4931 12.9137ZM31.264 26.6791C33.4797 26.6791 35.276 28.4753 35.276 30.6911C35.276 32.9068 33.4797 34.703 31.264 34.703C29.0483 34.703 27.252 32.9068 27.252 30.6911C27.252 28.4753 29.0483 26.6791 31.264 26.6791Z',
    docs: '/docs/guides/getting-started/quickstarts/reactjs',
  },
  {
    name: 'Next.js',
    icon: 'M42.3148 48.6796C38.9009 50.9525 34.8014 52.2771 30.3924 52.2771C18.4957 52.2771 8.85156 42.6329 8.85156 30.7362C8.85156 18.8395 18.4957 9.19531 30.3924 9.19531C42.2891 9.19531 51.9333 18.8395 51.9333 30.7362C51.9333 37.1564 49.1245 42.9207 44.6688 46.8671L39.5552 40.2803V21.8278H36.584V36.4531L25.2299 21.8278H21.4808V39.6473H24.4801V25.6368L42.3148 48.6796Z',
    docs: '/docs/guides/getting-started/quickstarts/nextjs',
  },
  {
    name: 'RedwoodJS',
    icon: 'M21.7716 14.6475L31.2847 21.1184C31.4993 21.2609 31.7501 21.3389 32.0076 21.3434C32.2654 21.3415 32.5167 21.2633 32.7304 21.1184L42.2508 14.6257C42.6207 14.3618 42.8247 13.9218 42.7876 13.4678C42.7505 13.0139 42.4779 12.6131 42.0701 12.4131L32.5569 7.71949C32.1961 7.545 31.7757 7.545 31.4147 7.71949L21.9306 12.4131C21.5154 12.6141 21.2392 13.0227 21.2063 13.4841C21.1735 13.9455 21.3891 14.3893 21.7716 14.6475ZM35.2389 23.1497C35.2396 23.5789 35.4504 23.9801 35.8027 24.2233L43.4291 29.4176C43.9173 29.7529 44.5705 29.7111 45.0123 29.316L51.4098 23.614C51.6948 23.3601 51.8531 22.9925 51.8419 22.6102C51.8309 22.2279 51.6513 21.8702 51.3519 21.6335L45.2436 16.7658C44.7995 16.4143 44.1802 16.3908 43.711 16.7078L35.8027 22.0978C35.4566 22.3366 35.2466 22.7283 35.2389 23.1497ZM16.2704 30.2155C16.5786 30.4914 16.7371 30.8984 16.6969 31.311C16.6569 31.7258 16.4164 32.0946 16.0536 32.2975L11.4994 35.0179C11.0377 35.2929 10.4548 35.2533 10.0344 34.9183C9.61395 34.5834 9.44327 34.0226 9.60544 33.509L11.2898 28.2278C11.4219 27.8093 11.7568 27.4869 12.1789 27.3718C12.6007 27.2494 13.0555 27.3567 13.3789 27.6547L16.2704 30.2155ZM40.9712 30.7668L32.7377 25.1519C32.3001 24.8585 31.7295 24.8585 31.2919 25.1519L23.0582 30.7668C22.7308 30.9927 22.5234 31.3557 22.4945 31.7534C22.4731 32.154 22.6323 32.5428 22.9282 32.8126L31.1545 40.1468C31.3918 40.3577 31.6979 40.4738 32.0148 40.4732C32.3315 40.4731 32.6375 40.3571 32.875 40.1468L41.1014 32.8126C41.3981 32.544 41.5553 32.1535 41.5279 31.7534C41.5049 31.3557 41.2993 30.9912 40.9712 30.7668ZM19.0101 29.316L12.6199 23.614C12.3323 23.3537 12.1736 22.9795 12.1861 22.5911C12.1931 22.2083 12.3708 21.8488 12.6705 21.6118L18.7788 16.715C19.2259 16.3645 19.8465 16.3411 20.3185 16.657L28.2197 22.047C28.5863 22.2867 28.8075 22.696 28.8075 23.1352C28.8075 23.5743 28.5863 23.9837 28.2197 24.2233L20.6005 29.4175C20.1093 29.7514 19.4552 29.7097 19.0101 29.316ZM51.8218 37.5062L45.3158 33.625C44.8326 33.3349 44.2176 33.3937 43.7977 33.7702L35.8461 40.8432C35.5106 41.1418 35.3531 41.5933 35.4296 42.0366C35.5063 42.4798 35.8059 42.8518 36.2219 43.0196L47.2531 47.4738C47.4044 47.5365 47.5666 47.5686 47.7302 47.5681C48.2422 47.5697 48.7069 47.2683 48.9158 46.7991L52.335 39.1675C52.6132 38.5636 52.3917 37.8462 51.8218 37.5062ZM52.7253 28.2278L54.4097 33.509H54.3952C54.5198 33.904 54.449 34.3351 54.2047 34.669C53.9604 35.0031 53.572 35.2 53.159 35.1993C52.9275 35.2 52.7 35.1374 52.5013 35.018L47.9399 32.2975C47.5844 32.0904 47.3524 31.722 47.3182 31.311C47.2755 30.8981 47.4344 30.49 47.7447 30.2155L50.6362 27.6474C50.9629 27.3558 51.4143 27.2493 51.8362 27.3645C52.258 27.4846 52.5918 27.8088 52.7253 28.2278ZM28.5955 42.033C28.6733 41.5915 28.5174 41.1412 28.1835 40.8432L20.2318 33.7702C19.8119 33.3937 19.1969 33.3349 18.7138 33.625L12.2078 37.5062C11.6445 37.8477 11.4216 38.556 11.6873 39.1603L15.1138 46.7919C15.3977 47.4283 16.1316 47.7261 16.7764 47.4666L27.8004 43.0124C28.2165 42.846 28.5173 42.4755 28.5955 42.033ZM32.4991 44.2093L41.3472 47.7785C41.7799 47.963 42.0787 48.3684 42.128 48.8376C42.184 49.3127 41.9722 49.7795 41.5785 50.0491L32.7232 56.1791C32.511 56.3277 32.2591 56.4086 32.0003 56.4113C31.7418 56.4073 31.4903 56.3265 31.2774 56.1791L22.4294 50.0491C22.0343 49.7802 21.82 49.3139 21.8728 48.8376C21.9318 48.3636 22.2414 47.9586 22.6824 47.7785L31.5305 44.2093C31.8416 44.0856 32.188 44.0856 32.4991 44.2093Z',
    docs: '/docs/guides/getting-started/quickstarts/redwoodjs',
  },
  {
    name: 'Flutter',
    icon: `M46.5067 10.3828L34.3509 10.3962L14.75 29.9971L20.7974 36.0519L26.1125 30.7666L46.5067 10.3828Z M34.6996 28.4653C34.5272 28.4573 34.3493 28.4491 34.2378 28.5965L23.7856 39.0471L29.7894 45.0142L29.7825 45.021L34.079 49.3212C34.1072 49.3462 34.1352 49.3741 34.1637 49.4026C34.2813 49.5201 34.4074 49.6462 34.5895 49.6055C36.5743 49.601 38.5591 49.6017 40.544 49.6025C42.529 49.6032 44.5142 49.604 46.4998 49.5995L35.9333 39.0234L46.4963 28.467L34.906 28.464C34.8415 28.4719 34.7711 28.4686 34.6996 28.4653Z`,
    docs: '/docs/guides/getting-started/quickstarts/flutter',
  },
  {
    name: 'Kotlin',
    icon: `M51.7395 51.7398H12.2598V12.2601H51.7395L31.591 31.7137L51.7395 51.7398Z`,
    docs: '/docs/guides/getting-started/quickstarts/kotlin',
  },
  {
    name: 'Svelte',
    icon: 'M29.9094 11.2292C35.956 7.37668 44.3187 9.17299 48.553 15.2334H48.5532C50.5831 18.0746 51.3826 21.614 50.771 25.0519C50.4778 26.677 49.8581 28.2259 48.9493 29.6047C50.2752 32.1335 50.7201 35.0322 50.2136 37.8422C49.6086 41.2154 47.6106 44.1777 44.7096 46.0024L34.0903 52.7707C28.0445 56.623 19.6818 54.8274 15.4466 48.7665C13.4171 45.9251 12.6176 42.3859 13.2288 38.948C13.5223 37.3227 14.1422 35.7738 15.0512 34.3949C13.7247 31.8665 13.2794 28.9677 13.786 26.1577C14.3913 22.7845 16.3893 19.8223 19.29 17.9974L29.9094 11.2292ZM19.8146 45.9861C21.8311 48.8931 25.4469 50.2333 28.8709 49.343H28.8708C29.6345 49.139 30.3624 48.8192 31.0293 48.3946L41.6512 41.6252C43.396 40.5274 44.5979 38.7455 44.9622 36.7164C45.33 34.6483 44.8489 32.5192 43.6278 30.8101C41.6113 27.9032 37.9955 26.5629 34.5715 27.4531C33.8084 27.6571 33.081 27.9768 32.4147 28.4012L28.3617 30.9842C28.1601 31.1125 27.9401 31.2092 27.7093 31.271C26.6776 31.5384 25.5887 31.1342 24.9815 30.2584C24.614 29.7429 24.4693 29.1012 24.5801 28.4779C24.6899 27.8669 25.0519 27.3302 25.5774 26.9996L36.2002 20.2298C36.4017 20.1015 36.6218 20.0048 36.8526 19.9431C37.8838 19.6754 38.9725 20.0795 39.5793 20.9551C39.9039 21.4146 40.0556 21.974 40.0078 22.5345L39.9714 22.9285L40.3662 23.0484C41.8596 23.4989 43.265 24.2014 44.5218 25.1254L45.0657 25.5245L45.2658 24.9145C45.3729 24.59 45.4577 24.2586 45.5196 23.9225C45.8873 21.8544 45.4063 19.7254 44.1852 18.0162C42.1687 15.1093 38.553 13.7691 35.129 14.6593C34.3653 14.8633 33.6374 15.1832 32.9705 15.6077L22.3487 22.3777C20.6036 23.475 19.4016 25.2568 19.0376 27.2858C18.6699 29.3539 19.1509 31.4829 20.372 33.192C22.3885 36.099 26.0043 37.4392 29.4283 36.549C30.1914 36.345 30.9188 36.0256 31.5853 35.6017L35.6389 33.0177C35.8402 32.8895 36.06 32.7929 36.2905 32.7311C37.3221 32.4637 38.4111 32.868 39.0183 33.7438C39.3857 34.2592 39.5306 34.9007 39.4205 35.524C39.3102 36.1352 38.948 36.6718 38.4224 37.0028L27.7996 43.7722C27.5981 43.9006 27.378 43.9973 27.1471 44.059C26.116 44.3266 25.0273 43.9225 24.4204 43.0469C24.0955 42.5876 23.9438 42.0281 23.992 41.4675L24.0284 41.0735L23.6336 40.9537C22.1404 40.5032 20.7351 39.8011 19.4783 38.8776L18.934 38.4778L18.734 39.0878C18.6266 39.4122 18.5418 39.7437 18.4801 40.0798C18.1125 42.1479 18.5935 44.277 19.8146 45.9861Z',
    docs: '/docs/guides/getting-started/quickstarts/sveltekit',
  },
  {
    name: 'SolidJS',
    icon: 'M26.7069 8.48157C39.8712 5.45973 56.8336 18.3919 56.8336 18.3919L51.0722 28.1073L50.9621 28.0856C51.0348 28.141 51.0722 28.1726 51.0722 28.1726C51.0681 28.1647 50.7083 27.8867 50.0561 27.4261C46.2099 24.7104 32.1917 15.6482 20.9454 18.2298C20.8906 18.2436 20.8351 18.2574 20.7793 18.2714C20.4417 18.3556 20.0905 18.4432 19.7867 18.5547C18.0673 19.116 16.7102 20.0163 15.7606 21.1271L15.7312 21.1213L20.5914 12.7706C20.6522 12.6724 20.7085 12.5694 20.7654 12.4654C20.8589 12.2942 20.9541 12.12 21.0742 11.9583C22.0076 10.5936 23.5204 9.45636 25.5481 8.8065C25.852 8.69497 26.2032 8.60737 26.5408 8.52316L26.5433 8.52253C26.5983 8.50882 26.6529 8.49519 26.7069 8.48157Z M19.46 32.0592L32.05 27.9448C35.8485 26.7049 40.3864 27.4446 43.8122 29.5562C43.7796 29.5338 44.8294 30.2289 44.9407 30.3542L49.7055 28.4801C49.3614 28.2446 48.0959 27.3256 47.6565 27.0394C45.8866 25.8866 43.3927 24.3844 40.4974 22.978C34.6564 20.1405 27.4046 17.8034 21.1853 19.2266L21.0193 19.2682C20.6776 19.3536 20.3877 19.4261 20.1399 19.5171L20.1225 19.5235L20.1048 19.5293C17.6631 20.3263 16.1341 21.8339 15.5155 23.5318C14.8997 25.2222 15.1285 27.2564 16.5089 29.2452C17.2625 30.3308 18.2761 31.2823 19.46 32.0592Z M41.5714 52.075L41.5772 52.087L41.5651 52.0849L41.5714 52.075Z M47.1224 40.0146L42.5493 48.2321C42.4667 46.8997 41.9832 45.5181 41.0622 44.1913C37.9723 39.7398 31.1166 37.5303 25.7414 39.2849L7.16602 45.3854L7.25332 45.1164L12.6666 35.3578L32.3681 28.9194C34.8201 28.119 37.6546 28.2128 40.2542 29.0469C42.8556 29.8816 45.136 31.4297 46.5284 33.4354C48.133 35.7496 48.1942 38.1212 47.1224 40.0146Z M9.07091 46.9944C8.63742 46.7131 8.24774 46.4532 7.90665 46.2212L26.0604 40.2592C28.5122 39.4591 31.3463 39.5529 33.9456 40.387C36.5471 41.2218 38.8277 42.77 40.2201 44.7759C41.6005 46.7647 41.8293 48.7989 41.2135 50.4893C40.5949 52.1872 39.0659 53.6948 36.6242 54.4918L36.6065 54.4976L36.5891 54.504C36.3413 54.595 36.0514 54.6675 35.7097 54.7529L35.5437 54.7946C29.3243 56.2177 22.0725 53.8806 16.231 51.047C13.3356 49.6424 10.8415 48.1431 9.07091 46.9944Z',
    docs: '/docs/guides/getting-started/quickstarts/solidjs',
  },
  {
    name: 'Vue',
    icon: 'M43.0532 13.4531H50.1147L30.2756 47.8158L10.4365 13.4531H17.4978L30.2755 35.5845L43.0532 13.4531ZM42.1764 13.4531L30.2755 34.0659L18.3746 13.4531L25.6939 13.4531L30.2756 21.3888L34.8572 13.4531L42.1764 13.4531Z',
    docs: '/docs/guides/getting-started/quickstarts/vue',
  },
  {
    name: 'Nuxt',
    icon: 'M32.5784 45.4741H50.2199C50.7802 45.4741 51.3307 45.3325 51.8159 45.0634C52.3012 44.7943 52.7041 44.4072 52.9842 43.9409C53.2642 43.4748 53.4115 42.946 53.4113 42.4078C53.4111 41.8696 53.2633 41.3409 52.9828 40.875L41.1352 21.164C40.8552 20.6979 40.4524 20.3109 39.9672 20.0418C39.4821 19.7727 38.9317 19.631 38.3715 19.631C37.8113 19.631 37.261 19.7727 36.7758 20.0418C36.2906 20.3109 35.8878 20.6979 35.6078 21.164L32.5784 26.2073L26.6555 16.3452C26.3753 15.8792 25.9723 15.4922 25.487 15.2232C25.0017 14.9541 24.4513 14.8125 23.8909 14.8125C23.3306 14.8125 22.7802 14.9541 22.2949 15.2232C21.8096 15.4922 21.4066 15.8792 21.1263 16.3452L6.38358 40.875C6.10311 41.3409 5.95532 41.8696 5.95508 42.4078C5.95483 42.946 6.10214 43.4748 6.38219 43.9409C6.66224 44.4072 7.06515 44.7943 7.5504 45.0634C8.03564 45.3325 8.58612 45.4741 9.14645 45.4741H20.2203C24.6079 45.4741 27.8436 43.6229 30.07 40.0113L38.3706 26.2073L47.0599 40.6619H35.4754L32.5784 45.4741ZM20.0398 40.657L12.3116 40.6553L23.8961 21.3836L29.6763 31.0195L25.8062 37.4599C24.3276 39.8032 22.6479 40.657 20.0398 40.657Z',
    docs: '/docs/guides/getting-started/quickstarts/nuxtjs',
  },
  {
    name: 'Refine',
    icon: 'M31.7374 20.9337C32.7027 20.9337 33.6284 21.3132 34.311 21.9887C34.9936 22.6642 35.377 23.5803 35.377 24.5356V39.4661C35.377 40.4214 34.9936 41.3375 34.311 42.013C33.6284 42.6885 32.7027 43.068 31.7374 43.068C30.7721 43.068 29.8463 42.6885 29.1638 42.013C28.4812 41.3375 28.0978 40.4214 28.0978 39.4661V24.5356C28.0978 24.0626 28.1919 23.5942 28.3748 23.1572C28.5577 22.7203 28.8258 22.3232 29.1638 21.9887C29.5018 21.6543 29.903 21.3889 30.3446 21.2079C30.7861 21.0269 31.2594 20.9337 31.7374 20.9337ZM31.7371 27.1915C33.2665 27.1915 34.5063 25.9646 34.5063 24.451C34.5063 22.9375 33.2665 21.7106 31.7371 21.7106C30.2077 21.7106 28.9679 22.9375 28.9679 24.451C28.9679 25.9646 30.2077 27.1915 31.7371 27.1915Z M54.0424 32C54.0424 44.3777 44.0083 54.4118 31.6306 54.4118C19.2529 54.4118 9.21875 44.3777 9.21875 32C9.21875 19.6223 19.2529 9.58813 31.6306 9.58813C44.0083 9.58813 54.0424 19.6223 54.0424 32ZM31.7374 19.3933C30.36 19.3952 29.0396 19.9376 28.0659 20.9016C27.0921 21.8657 26.5444 23.1726 26.543 24.5356V39.4661C26.543 40.8294 27.0902 42.137 28.0644 43.101C29.0385 44.065 30.3597 44.6066 31.7374 44.6066C33.115 44.6066 34.4362 44.065 35.4104 43.101C36.3845 42.137 36.9318 40.8294 36.9318 39.4661V24.5356C36.9304 23.1726 36.3827 21.8657 35.4089 20.9016C34.4352 19.9376 33.1148 19.3952 31.7374 19.3933Z',
    docs: '/docs/guides/getting-started/quickstarts/refine',
  },
]

const TRANSITION_IN = 0.25
const TRANSITION_OUT = 0.1

const textVariants = {
  initial: {
    y: 10,
    opacity: 0,
  },
  animate: {
    y: 0,
    opacity: 1,
    transition: {
      duration: TRANSITION_IN,
      ease: EASE_OUT,
    },
  },
  exit: {
    y: 0,
    opacity: 0,
    transition: {
      duration: TRANSITION_OUT,
      ease: EASE_IN,
    },
  },
}

const HeroFrameworks = ({ className }: { className?: string }) => {
  const [activeFramework, setActiveFramework] = useState<Framework>(null!)
  const isXs = useBreakpoint(640)

  const sendTelemetryEvent = useSendTelemetryEvent()

  return (
    <SectionContainer className={cn(className)} id="frameworks">
      <div className="relative z-20 w-full max-w-6xl mx-auto h-full flex flex-col xl:flex-row gap-4 items-center justify-between">
        <div className="!leading-tight text-center xl:text-left text-2xl md:text-4xl text-foreground-lighter whitespace-nowrap">
          Use Supabase with{' '}
          <div className="block">
            <AnimatePresence mode="wait">
              <motion.span
                initial="initial"
                animate="animate"
                exit="exit"
                key={activeFramework?.name}
                variants={textVariants}
                className="inline-block text-foreground"
              >
                {activeFramework?.name ?? 'any framework'}
              </motion.span>
            </AnimatePresence>
          </div>
        </div>
        <div className="grid grid-cols-5 md:grid-cols-10">
          {frameworks.map((framework) => (
            <Link
              key={framework.name}
              href={framework.docs}
              className="transition-opacity group"
              onClick={() =>
                sendTelemetryEvent({
                  action: 'homepage_framework_quickstart_clicked',
                  properties: { frameworkName: framework.name },
                })
              }
              onMouseOver={() => setActiveFramework(framework)}
              onMouseOut={() => setActiveFramework(null!)}
            >
              <div
                className={cn(
                  'm-1 bg-[var(--color-bg-darkest)] h-16 w-16 flex items-center justify-center rounded-md group-hover:border transition-all text-foreground-light border-foreground-light hover:shadow',
                  !!activeFramework &&
                    activeFramework.name !== framework.name &&
                    'text-foreground-muted',
                  activeFramework?.name === framework.name && 'text-foreground'
                )}
              >
                <svg
                  width={isXs ? 35 : 45}
                  height={isXs ? 35 : 45}
                  fillRule="evenodd"
                  clipRule="evenodd"
                  viewBox="0 0 61 61"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d={framework.icon} fill="currentColor" />
                </svg>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </SectionContainer>
  )
}

export default HeroFrameworks
