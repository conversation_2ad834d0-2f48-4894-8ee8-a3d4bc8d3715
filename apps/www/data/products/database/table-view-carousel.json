[{"title": "The simplicity of a spreadsheet", "label": "Spreadsheet like editing", "img_url": "/images/product/database/table-view/spreadsheet-interface.png", "text": "Add, edit, and update your data with the simplicity of a no-code tool.", "url": "/docs/guides/database/tables"}, {"title": "Create tables", "label": "Create tables", "img_url": "/images/product/database/table-view/create-table.png", "text": "Add tables, columns and rows right in the dashboard. Without a single line SQL.", "url": "/docs/guides/database/tables#creating-tables"}, {"title": "Set up foreign keys", "label": "Set up foreign keys", "img_url": "/images/product/database/table-view/foreign-keys.png", "text": "Build connections throughout your data with the full power of a Relational Database.", "url": "/docs/guides/database/tables#joining-tables-with-foreign-keys"}, {"title": "Select and Export", "label": "Select and Export", "img_url": "/images/product/database/table-view/export.png", "text": "Pick the rows you want and export them into a CSV. ", "url": ""}]