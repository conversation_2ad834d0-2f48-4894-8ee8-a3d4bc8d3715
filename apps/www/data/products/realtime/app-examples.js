export default [
  {
    img: 'toast-messages.svg',
    title: 'Toast messages',
    description: 'Listen to changes in the database and show users a toast message.',
  },
  {
    img: 'live-avatars.svg',
    title: 'Live avatars',
    description: 'Share the status of users across multiple clients.',
  },
  {
    img: 'live-cursors.svg',
    title: 'Live cursors',
    description: 'Share the position and status of multiple cursors across multiple clients.',
  },
  {
    img: 'leaderboard.svg',
    title: 'Leaderboards',
    description:
      'Listen to changes in your database regarding the scores of your users and display them live.',
  },
  {
    img: 'stock-market.svg',
    title: 'Live charts',
    description:
      'Keep charts updated in Realtime by listening to changes in the database rather than polling at intervals.',
  },
  {
    img: 'in-app-chat.svg',
    title: 'In app chat',
    description: 'Listen to changes in the database to display up-to-date text messages.',
  },
  {
    img: 'code-editor.svg',
    title: 'Shared text editor',
    description: 'Users can collaborate with text documents or even code editors.',
  },

  {
    img: 'whiteboard.svg',
    title: 'Shared whiteboard',
    description:
      'Allow users to collaborate on a shared whiteboard, sharing positions and states of any kind of object in Realtime.',
  },
  {
    img: 'location.svg',
    title: 'Location',
    description: 'Listen to changes in the database regarding the position of a moving coordinate.',
  },
  {
    img: 'multiplayer-game.svg',
    title: 'Multiplayer games',
    description:
      "Keep track of how long a player has been playing, who's turn it is and even share to other clients what the player is doing.",
  },
  {
    img: 'form-presence.svg',
    title: 'Form presence',
    description: 'Display the active selection of different users throughout an interactive form.',
  },
]
