{"database": {"title": "Optimized Compute", "features": ["For projects that need dedicated compute", "For scaling number of connections", "Additional CPU & RAM options", "Additional Disk Bandwidth"], "rows": [{"columns": [{"key": "plan", "title": "Compute Size", "value": "Micro"}, {"key": "pricing", "title": "Price USD", "value": "$10"}, {"key": "cpu", "title": "CPU", "value": "2-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": false}, {"key": "memory", "title": "Memory", "value": "1 GB"}, {"key": "directConnections", "title": "Connections: Direct", "value": "60"}, {"key": "poolerConnections", "title": "Connections: Pooler", "value": "200"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "Small"}, {"key": "pricing", "title": "Price USD", "value": "$15"}, {"key": "cpu", "title": "CPU", "value": "2-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": false}, {"key": "memory", "title": "Memory", "value": "2 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "90"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "400"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "Medium"}, {"key": "pricing", "title": "Price USD", "value": "$60"}, {"key": "cpu", "title": "CPU", "value": "2-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": false}, {"key": "memory", "title": "Memory", "value": "4 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "120"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "600"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "Large"}, {"key": "pricing", "title": "Price USD", "value": "$110"}, {"key": "cpu", "title": "CPU", "value": "2-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "8 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "160"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "800"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "XL"}, {"key": "pricing", "title": "Price USD", "value": "$210"}, {"key": "cpu", "title": "CPU", "value": "4-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "16 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "240"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "1,000"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "2XL"}, {"key": "pricing", "title": "Price USD", "value": "$410"}, {"key": "cpu", "title": "CPU", "value": "8-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "32 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "380"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "1,500"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "4XL"}, {"key": "pricing", "title": "Price USD", "value": "$960"}, {"key": "cpu", "title": "CPU", "value": "16-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "64 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "480"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "3,000"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "8XL"}, {"key": "pricing", "title": "Price USD", "value": "$1,870"}, {"key": "cpu", "title": "CPU", "value": "32-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "128 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "490"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "6,000"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "12XL"}, {"key": "pricing", "title": "Price USD", "value": "$2,800"}, {"key": "cpu", "title": "CPU", "value": "48-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "192 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "500"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "9,000"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "16XL"}, {"key": "pricing", "title": "Price USD", "value": "$3,730"}, {"key": "cpu", "title": "CPU", "value": "64-core ARM"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "256 GB"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "500"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "12,000"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": ">16XL"}, {"key": "pricing", "title": "Price USD", "value": "Contact Us", "url": "/support/new?category=sales&subject=Enquiry%20about%20larger%20instance%20sizes"}, {"key": "cpu", "title": "CPU", "value": "Custom"}, {"key": "dedicated", "title": "Dedicated", "value": true}, {"key": "memory", "title": "Memory", "value": "Custom"}, {"key": "directConnections", "title": "Connections: Direct (recommended)", "value": "Custom"}, {"key": "poolerConnections", "title": "Connections: <PERSON><PERSON> (recommended)", "value": "Custom"}]}]}, "support": {"title": "Support", "features": ["Guaranteed Response Times", "SLAs", "Designated Support", "Advanced Support Scope"], "rows": [{"columns": [{"key": "plan", "title": "Plan", "value": "Standard"}, {"key": "pricing", "title": "Pricing", "value": "Contact Us"}, {"key": "severityLevel<PERSON>rgent", "title": "1. Severity Level: Urgent", "value": "1 business hour (24/7x365)"}, {"key": "severityLevelHigh", "title": "2. Severity Level: High", "value": "4 business hours (Monday - Friday)"}, {"key": "severityLevelNormal", "title": "3. Severity Level: Normal", "value": "1 business day (Monday - Friday)"}, {"key": "severityLevelLow", "title": "4. Severity Level: Low", "value": "2 business days (Monday - Friday)"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "Priority"}, {"key": "pricing", "title": "Pricing", "value": "Contact Us"}, {"key": "severityLevel<PERSON>rgent", "title": "1. Severity Level: Urgent", "value": "1 business hour (24/7x365)"}, {"key": "severityLevelHigh", "title": "2. Severity Level: High", "value": "2 business hours (Monday - Friday)"}, {"key": "severityLevelNormal", "title": "3. Severity Level: Normal", "value": "1 business day (Monday - Friday)"}, {"key": "severityLevelLow", "title": "4. Severity Level: Low", "value": "2 business days (Monday - Friday)"}]}, {"columns": [{"key": "plan", "title": "Plan", "value": "Priority Plus"}, {"key": "pricing", "title": "Pricing", "value": "Contact Us"}, {"key": "severityLevel<PERSON>rgent", "title": "1. Severity Level: Urgent", "value": "1 business hour (24/7x365)"}, {"key": "severityLevelHigh", "title": "2. Severity Level: High", "value": "2 business hours (24/7x365)"}, {"key": "severityLevelNormal", "title": "3. Severity Level: Normal", "value": "12 business hours (Monday - Friday)"}, {"key": "severityLevelLow", "title": "4. Severity Level: Low", "value": "1 business days (Monday - Friday)"}]}]}}