[{"imgUrl": "images/stripe.jpg", "title": "<PERSON><PERSON> SaaS starter", "url": "https://github.com/thorwebdev/nextjs-saas-starter", "description": "Build a SaaS business with Stripe subscriptions and Supabase.", "icons": [{"imgUrl": "https://avatars3.githubusercontent.com/u/54469796?s=480&v=4", "imgAlt": "supabase icon"}, {"imgUrl": "https://avatars1.githubusercontent.com/u/14985020?s=480&v=4", "imgAlt": "vercel icon"}, {"imgUrl": "https://avatars3.githubusercontent.com/u/856813?s=480&v=6", "imgAlt": "stripe icon"}], "ctaText": "View project"}, {"imgUrl": "images/slack-clone.jpg", "title": "Chat app with Next.js", "url": "https://github.com/supabase/supabase/tree/master/examples/slack-clone/nextjs-slack-clone", "description": "Build a full-stack Slack clone using Next.js and Supabase.", "icons": [{"imgUrl": "https://avatars3.githubusercontent.com/u/54469796?s=480&v=4", "imgAlt": "supabase icon"}, {"imgUrl": "https://avatars1.githubusercontent.com/u/14985020?s=480&v=4", "imgAlt": "vercel icon"}], "ctaText": "View project"}, {"imgUrl": "images/to-do-app.jpg", "title": "Todo list with Vue.js", "url": "https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/vue3-ts-todo-list", "description": "Build a simple todo list with Vue and Supabase.", "icons": [{"imgUrl": "https://avatars3.githubusercontent.com/u/54469796?s=480&v=4", "imgAlt": "supabase icon"}, {"imgUrl": "https://avatars1.githubusercontent.com/u/14985020?s=480&v=4", "imgAlt": "vercel icon"}], "ctaText": "View project"}]