[{"type": "Case Study", "title": "A web crawler handling millions of API requests.", "description": "See how Monitoro built and automated a scraping platform using Supabase.", "imgUrl": "images/case-study-monitoro.jpg", "logoUrl": "images/logos/monitoro.png", "organization": "Monitoro", "url": "/blog/case-study-monitoro", "path": "/blog/case-study-monitoro", "postMeta": {"name": "<PERSON>", "avatarUrl": "https://avatars0.githubusercontent.com/u/10214025?v=4", "publishDate": "Mar 16, 2020", "readLength": 6}, "ctaText": "View case study"}, {"type": "Case Study", "title": "Counter-fraud watchlists for the fintech industry.", "description": "See how Xendit use Supabase to build a full-text search engine.", "imgUrl": "images/case-study-xendit.jpg", "logoUrl": "images/logos/xendit.png", "organization": "Llama Lab", "url": "/blog/case-study-xendit", "path": "/blog/case-study-xendit", "postMeta": {"name": "<PERSON>", "avatarUrl": "https://avatars0.githubusercontent.com/u/10214025?v=4", "publishDate": "Mar 20, 2020", "readLength": 10}, "ctaText": "View case study"}, {"type": "Case Study", "title": "A no-code website builder, deployed in seven days.", "description": "See how Tayfa went from idea to paying customer in less than 30 days.", "imgUrl": "images/case-study-tayfab.jpg", "logoUrl": "images/logos/monitoro.png", "organization": "Tayfab", "url": "/blog/case-study-tayfa", "path": "/blog/case-study-tayfa", "postMeta": {"name": "<PERSON>", "avatarUrl": "https://avatars0.githubusercontent.com/u/10214025?v=4", "publishDate": "Mar 27, 2020", "readLength": 5}, "ctaText": "View case study"}]