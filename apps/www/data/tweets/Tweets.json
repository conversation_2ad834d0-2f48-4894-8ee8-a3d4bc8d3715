[{"text": "Working with @supabase has been one of the best dev experiences I've had lately. Incredibly easy to set up, great documentation, and so many fewer hoops to jump through than the competition. I definitely plan to use it on any and all future projects.", "url": "https://twitter.com/thatguy_tex/status/1497602628410388480", "handle": "thatguy_tex", "img_url": "/images/twitter-profiles/09HouOSt_400x400.jpg"}, {"text": "@supabase is just 🤯 Now I see why a lot of people love using it as a backend for their applications. I am really impressed with how easy it is to set up an Auth and then just code it together for the frontend. @IngoKpp now I see your joy with Supabase #coding #fullstackwebdev", "url": "https://twitter.com/IxoyeDesign/status/1497473731777728512", "handle": "IxoyeDesign", "img_url": "/images/twitter-profiles/C8opIL-g_400x400.jpg"}, {"text": "I've been using @supabase for two personal projects and it has been amazing being able to use the power of Postgres and don't have to worry about the backend", "url": "https://twitter.com/varlenneto/status/1496595780475535366", "handle": "<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/wkXN0t_F_400x400.jpg"}, {"text": "Y'all @supabase + @nextjs is amazing! 🙌 Barely an hour into a proof-of-concept and already have most of the functionality in place. 🤯🤯🤯", "url": "https://twitter.com/justinjunodev/status/1500264302749622273", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/9k_ZB9OO_400x400.jpg"}, {"text": "And thanks to @supabase, I was able to go from idea to launched feature in a matter of hours. Absolutely amazing!", "url": "https://twitter.com/BraydonCoyer/status/1511071369731137537", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/8YxkpW8f_400x400.jpg"}, {"text": "Contributing to open-source projects and seeing merged PRs gives enormous happiness! Special thanks to @supabase, for giving this opportunity by staying open-source and being junior-friendly✌🏼", "url": "https://twitter.com/damlakoksal/status/1511436907984662539", "handle": "<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/N8EfTFs7_400x400.jpg"}, {"text": "Holy crap. @supabase is absolutely incredible. Most elegant backend as a service I've ever used. This is a dream.", "url": "https://twitter.com/kentherogers/status/1512609587110719488", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/9l9Td-Fz_400x400.jpg"}, {"text": "Using @supabase I'm really pleased on the power of postgres (and sql in general). Despite being a bit dubious about the whole backend as a service thing I have to say I really don't miss anything. The whole experience feel very robust and secure.", "url": "https://twitter.com/paoloricciuti/status/1497691838597066752", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/OCDKFUOp_400x400.jpg"}, {"text": "@supabase is lit. It took me less than 10 minutes to setup, the DX is just amazing.", "url": "https://twitter.com/saxxone/status/1500812171063828486", "handle": "saxxone", "img_url": "/images/twitter-profiles/BXi6z1M7_400x400.jpg"}, {"text": "I’m not sure what magic @supabase is using but we’ve migrated @happyteamsdotio database to @supabase from @heroku and it’s much much faster at half the cost.", "url": "https://twitter.com/michaelcdever/status/1524753565599690754", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/rWX8Jzp5_400x400.jpg"}, {"text": "There are a lot of indie hackers building in public, but it’s rare to see a startup shipping as consistently and transparently as Supabase. Their upcoming March releases look to be 🔥 Def worth a follow! also opened my eyes as to how to value add in open source.", "url": "https://twitter.com/swyx/status/1366685025047994373", "handle": "swyx", "img_url": "/images/twitter-profiles/qhvO9V6x_400x400.jpg"}, {"text": "This weekend I made a personal record 🥇 on the less time spent creating an application with social login / permissions, database, cdn, infinite scaling, git push to deploy and for free. Thanks to @supabase and @vercel", "url": "https://twitter.com/jperelli/status/1366195769657720834", "handle": "<PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/_ki30kYo_400x400.jpg"}, {"text": "Badass! Supabase is amazing. literally saves our small team a whole engineer’s worth of work constantly. The founders and everyone I’ve chatted with at supabase are just awesome people as well :)", "url": "https://twitter.com/KennethCassel/status/1524359528619384834", "handle": "<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/pmQj3TX-_400x400.jpg"}, {"text": "Working with Supabase is just fun. It makes working with a DB so much easier.", "url": "https://twitter.com/the_BrianB/status/1524716498442276864", "handle": "the_BrianB", "img_url": "/images/twitter-profiles/7NITI8Z3_400x400.jpg"}, {"text": "This community is STRONG and will continue to be the reason why developers flock to @supabase over an alternative. Keep up the good work! ⚡️", "url": "https://twitter.com/_wilhelm__/status/1524074865107488769", "handle": "_wilhelm__", "img_url": "/images/twitter-profiles/CvqDy6YF_400x400.jpg"}, {"text": "Working on my next SaaS app and I want this to be my whole job because I'm just straight out vibing putting it together. @supabase and chill, if you will", "url": "https://twitter.com/drewclemcr8/status/1523843155484942340", "handle": "drewclemcr8", "img_url": "/images/twitter-profiles/bJlKtSxz_400x400.jpg"}, {"text": "@supabase Putting a ton of well-explained example API queries in a self-building documentation is just a classy move all around. I also love having GraphQL-style nested queries with traditional SQL filtering. This is pure DX delight. A+++. #backend", "url": "https://twitter.com/CodiferousCoder/status/1522233113207836675", "handle": "CodiferousCoder", "img_url": "/images/twitter-profiles/t37cVLwy_400x400.jpg"}, {"text": "Me using @supabase for the first time right now 🤯", "url": "https://twitter.com/nasiscoe/status/1365140856035024902", "handle": "nasiscoe", "img_url": "/images/twitter-profiles/nc2Ms5hH_400x400.jpg"}, {"text": "I'm trying @supabase, Firebase alternative that uses PostgreSQL (and you can use GraphQL too) in the cloud. It's incredible 😍", "url": "https://twitter.com/<PERSON>__<PERSON>allegos/status/1365699468109242374", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/1PH2mt6v_400x400.jpg"}, {"text": "Check out this amazing product @supabase. A must give try #newidea #opportunity", "url": "https://twitter.com/digitaldaswani/status/1364447219642814464", "handle": "<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/w8HLdlC7_400x400.jpg"}, {"text": "I gave @supabase a try this weekend and I was able to create a quick dashboard to visualize the data from the PostgreSQL instance. It's super easy to use Supabase's API or the direct DB connection. Check out the tutorial 📖", "url": "https://twitter.com/razvanilin/status/1363770020581412867", "handle": "r<PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/AiaH9vJ2_400x400.jpg"}, {"text": "Tried @supabase for the first time yesterday. Amazing tool! I was able to get my Posgres DB up in no time and their documentation on operating on the DB is super easy! 👏 Can't wait for Cloud functions to arrive! It's gonna be a great Firebase alternative!", "url": "https://twitter.com/chinchang457/status/1363347740793524227", "handle": "chinchang457", "img_url": "/images/twitter-profiles/LTw5OCnv_400x400.jpg"}, {"text": "I gave @supabase a try today and I was positively impressed! Very quick setup to get a working remote database with API access and documentation generated automatically for you 👌 10/10 will play more", "url": "https://twitter.com/razvanilin/status/1363002398738800640", "handle": "r<PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/AiaH9vJ2_400x400.jpg"}, {"text": "Wait. Is it so easy to write queries for @supabase ? It's like simple SQL stuff!", "url": "https://twitter.com/T0ny_Boy/status/1362911838908911617", "handle": "T0ny_Boy", "img_url": "/images/twitter-profiles/UCBhUBZl_400x400.jpg"}, {"text": "<PERSON><PERSON>, and @supabase have native support for magic link login?! I was going to use http://magic.link for this But if I can get my whole DB + auth + magic link support in one... Awesome", "url": "https://twitter.com/louisbarclay/status/1362016666868154371", "handle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/6f1O8ZOW_400x400.jpg"}, {"text": "@MongoDB or @MySQL?!?! Please, let me introduce you to @supabase and the wonderful world of @PostgreSQL before it's too late!!", "url": "https://twitter.com/jim_bisenius/status/1361772978841788416", "handle": "jim_bisenius", "img_url": "/images/twitter-profiles/rLgwUZSB_400x400.jpg"}, {"text": "Where has @supabase been all my life? 😍", "url": "https://twitter.com/Elsolo244/status/1360257201911320579", "handle": "Elsolo244", "img_url": "/images/twitter-profiles/v6citnk33y2wpeyzrq05_400x400.jpeg"}, {"text": "Honestly Supabase is such a killer Firebase alternative.", "url": "https://twitter.com/XPCheese/status/1360229397735895043", "handle": "XPCheese", "img_url": "/images/twitter-profiles/eYP6YXr7_400x400.jpg"}, {"text": "I think you'll love @supabase :-) Open-source, PostgreSQL-based & zero magic.", "url": "https://twitter.com/zippoxer/status/1360021315852328961", "handle": "zippoxer", "img_url": "/images/twitter-profiles/6rd3xub9_400x400.png"}, {"text": "@supabase is the answer to all of firebase’s problems imo", "url": "https://twitter.com/jim_bisenius/status/1358590362953142278", "handle": "jim_bisenius", "img_url": "/images/twitter-profiles/rLgwUZSB_400x400.jpg"}, {"text": "@supabase is insane.", "url": "https://twitter.com/codewithbhargav/status/1357647840911126528", "handle": "code<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/LQYfHXBp_400x400.jpg"}, {"text": "It’s fun, feels lightweight, and really quick to spin up user auth and a few tables. Almost too easy! Highly recommend.", "url": "https://twitter.com/nerdburn/status/1356857261495214085", "handle": "nerdburn", "img_url": "/images/twitter-profiles/66VSV9Mm_400x400.png"}, {"text": "I’m probably the wrong person to ask because I pick tools based on UX. Supabase was immediately approachable: instant setup, fast web app, auth, and easy APIs. Same reason I liked Firebase when I first discovered.", "url": "https://twitter.com/jasonbarone/status/1357015483619422210", "handle": "j<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/6zCnwpvi_400x400.jpg"}, {"text": "Now things are starting to get interesting! Firebase has long been the obvious choice for many #flutter devs for the ease of use. But their databases are NoSQL, which has its downsides... Seems like @supabase is working on something interesting here!", "url": "https://twitter.com/RobertBrunhage/status/1356973695865085953", "handle": "<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/5LMWEACf_400x400.jpg"}, {"text": "Honestly, I really love what @supabase is doing, you don't need to own a complete backend, just write your logic within your app and you'll get a powerful Postgresql at your disposal.", "url": "https://twitter.com/NavicsteinR/status/1356927229217959941", "handle": "NavicsteinR", "img_url": "/images/twitter-profiles/w_zNZAs7_400x400.jpg"}, {"text": "Next.js, @supabase, @stripe, and @vercel. Supastack™", "url": "https://twitter.com/jasonbarone/status/1356765411832922115", "handle": "j<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/6zCnwpvi_400x400.jpg"}, {"text": "I've really enjoyed the DX! Extremely fun to use, which is odd to say about a database at least for me.", "url": "https://twitter.com/Soham_Asmi/status/1373086068132745217", "handle": "<PERSON><PERSON>_<PERSON><PERSON>", "img_url": "/images/twitter-profiles/Os4nhKIr_400x400.jpg"}, {"text": "Supabase team is doing some awesome stuff #supabase #facts @supabase", "url": "https://twitter.com/_strawbird/status/1372607500499841025", "handle": "_strawbird", "img_url": "/images/twitter-profiles/iMBvvQdn_400x400.jpg"}, {"text": "Did a website with @supabase last week with no prior experience with it. Up and running in 20 minutes. It's awesome to use. Thumbs up", "url": "https://twitter.com/michael_webdev/status/1352885366928404481?s=20", "handle": "mi<PERSON><PERSON>_webdev", "img_url": "/images/twitter-profiles/SvAyLaWV_400x400.jpg"}, {"text": "Next.js, @supabase, @stripe, and @vercel. Supastack™", "url": "https://twitter.com/jasonbarone/status/1356765411832922115?s=20", "handle": "j<PERSON><PERSON><PERSON><PERSON>", "img_url": "/images/twitter-profiles/6zCnwpvi_400x400.jpg"}, {"text": "I just learned about @supabase and im in love 😍 Supabase is an open source Firebase alternative! EarListen (& react) to database changes 💁 Manage users & permissions 🔧 Simple UI for database interaction", "url": "https://twitter.com/0xBanana/status/1373677301905362948", "handle": "0xBanana", "img_url": "/images/twitter-profiles/pgHIGqZ0_400x400.jpg"}]