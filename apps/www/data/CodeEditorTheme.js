const monokaiCustomTheme = {
  hljs: {
    display: 'block',
    overflowX: 'auto',
    padding: '0.5em',
    background: '#272822',
    color: '#ddd',
  },
  'hljs-tag': {
    color: '#569cd6',
  },
  'hljs-keyword': {
    color: '#569cd6',
    fontWeight: 'bold',
  },
  'hljs-selector-tag': {
    color: '#569cd6',
    fontWeight: 'bold',
  },
  'hljs-literal': {
    color: '#569cd6',
    fontWeight: 'bold',
  },
  'hljs-strong': {
    color: '#569cd6',
  },
  'hljs-name': {
    color: '#569cd6',
  },
  'hljs-code': {
    color: '#66d9ef',
  },
  'hljs-class .hljs-title': {
    color: 'white',
  },
  'hljs-attribute': {
    color: '#bf79db',
  },
  'hljs-symbol': {
    color: '#bf79db',
  },
  'hljs-regexp': {
    color: '#bf79db',
  },
  'hljs-link': {
    color: '#bf79db',
  },
  'hljs-string': {
    color: '#3ECF8E',
  },
  'hljs-bullet': {
    color: '#3ECF8E',
  },
  'hljs-subst': {
    color: '#3ECF8E',
  },
  'hljs-title': {
    color: '#3ECF8E',
    fontWeight: 'bold',
  },
  'hljs-section': {
    color: '#3ECF8E',
    fontWeight: 'bold',
  },
  'hljs-emphasis': {
    color: '#3ECF8E',
  },
  'hljs-type': {
    color: '#3ECF8E',
    fontWeight: 'bold',
  },
  'hljs-built_in': {
    color: '#3ECF8E',
  },
  'hljs-builtin-name': {
    color: '#3ECF8E',
  },
  'hljs-selector-attr': {
    color: '#3ECF8E',
  },
  'hljs-selector-pseudo': {
    color: '#3ECF8E',
  },
  'hljs-addition': {
    color: '#3ECF8E',
  },
  'hljs-variable': {
    color: '#3ECF8E',
  },
  'hljs-template-tag': {
    color: '#3ECF8E',
  },
  'hljs-template-variable': {
    color: '#3ECF8E',
  },
  'hljs-comment': {
    color: '#75715e',
  },
  'hljs-quote': {
    color: '#75715e',
  },
  'hljs-deletion': {
    color: '#75715e',
  },
  'hljs-meta': {
    color: '#75715e',
  },
  'hljs-doctag': {
    fontWeight: 'bold',
  },
  'hljs-selector-id': {
    fontWeight: 'bold',
  },
}

export default monokaiCustomTheme
