[{"name": "TypeScript support", "description": "Type definitions built directly from your database schema", "badge": "", "url": "/docs/client/generating-types"}, {"name": "Install from CDN", "description": "Use Supabase in the browser without a build process", "badge": "", "url": "/docs/client/initializing"}, {"name": "Local emulator", "description": "Develop locally and push to production when you're ready", "badge": "", "url": "/docs/guides/self-hosting"}, {"name": "Supabase CLI", "description": "Manage Supabase projects from your local machine", "badge": "", "url": "https://github.com/supabase/cli"}]