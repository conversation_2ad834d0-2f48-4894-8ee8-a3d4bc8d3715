{"tabTableEditor": {"header": "Manage your data with the familiarity of a spreadsheet", "description": "You don’t have to be a database expert to use Supabase. Our table editor makes Postgres easy to use, even for non-techies. You can do everything right in our dashboard.", "cta": "Explore Table View", "url": "/docs/guides/platform"}, "tabSqlEditor": {"header": "In-built SQL editor for when you need greater control", "description": "Write, save, and execute SQL queries directly on our dashboard, with templates to save you time. Run common queries and even build applications using our growing list of templates.", "cta": "Explore SQL Editor", "url": "/docs/guides/platform"}, "tabAuthRules": {"header": "User management as straight-forward as it can be", "description": "Easily manage your users with Supabase Auth, with email logins, magic links, and third-party logins. Create complex access policies with SQL rules to fit your unique business needs.", "cta": "Explore Auth Policies", "url": "/docs/guides/auth"}}