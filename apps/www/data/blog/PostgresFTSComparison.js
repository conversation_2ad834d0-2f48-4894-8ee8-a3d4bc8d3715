const latency = [
  {
    query: 'awakening',
    meilisearch: 3.63,
    opensearch: 9.84,
    pg: 1.93,
    'sqlite-disk': 0.3,
    'sqlite-mem': 0.24,
    typesense: 2.07,
  },

  {
    query: 'comedy',
    meilisearch: 3.46,
    opensearch: 40.1,
    pg: 3.55,
    'sqlite-disk': 4.25,
    'sqlite-mem': 2.91,
    typesense: 3.14,
  },

  {
    query: 'love',
    meilisearch: 12.4,
    opensearch: 139.0,
    pg: 15.3,
    'sqlite-disk': 27.7,
    'sqlite-mem': 13.4,
    typesense: 30.6,
  },

  {
    query: 'romance',
    meilisearch: 3.05,
    opensearch: 23.9,
    pg: 2.14,
    'sqlite-disk': 1.97,
    'sqlite-mem': 1.43,
    typesense: 2.68,
  },

  {
    query: 'spy',
    meilisearch: 3.57,
    opensearch: 14.1,
    pg: 1.78,
    'sqlite-disk': 0.92,
    'sqlite-mem': 0.64,
    typesense: 3.78,
  },

  {
    query: 'superhero',
    meilisearch: 4.82,
    opensearch: 112.0,
    pg: 3.68,
    'sqlite-disk': 0.68,
    'sqlite-mem': 0.39,
    typesense: 3.99,
  },

  {
    query: 'superhero',
    meilisearch: 4.39,
    opensearch: 12.6,
    pg: 1.59,
    'sqlite-disk': 0.52,
    'sqlite-mem': 0.24,
    typesense: 2.09,
  },

  {
    query: 'superman',
    meilisearch: 3.45,
    opensearch: 11.4,
    pg: 1.24,
    'sqlite-disk': 0.36,
    'sqlite-mem': 0.19,
    typesense: 2.71,
  },

  {
    query: 'suprman',
    meilisearch: 2.69,
    opensearch: 6.78,
    pg: 1.0,
    'sqlite-disk': 0.08,
    'sqlite-mem': 0.06,
    typesense: 2.83,
  },

  {
    query: 'worldwar',
    meilisearch: 8.53,
    opensearch: 149.0,
    pg: 2.94,
    'sqlite-disk': 5.02,
    'sqlite-mem': 2.93,
    typesense: 3.26,
  },
]

const results = [
  {
    query: 'awakening',
    meilisearch: 15,
    opensearch: 32,
    pg: 210,
    'sqlite-disk': 63,
    'sqlite-mem': 63,
    typesense: 14,
  },
  {
    query: 'comedy',
    meilisearch: 67,
    opensearch: 756,
    pg: 1213,
    'sqlite-disk': 1220,
    'sqlite-mem': 1220,
    typesense: 55,
  },
  {
    query: 'love',
    meilisearch: 812,
    opensearch: 3036,
    pg: 5417,
    'sqlite-disk': 4691,
    'sqlite-mem': 4691,
    typesense: 745,
  },
  {
    query: 'romance',
    meilisearch: 35,
    opensearch: 383,
    pg: 630,
    'sqlite-disk': 554,
    'sqlite-mem': 554,
    typesense: 34,
  },
  {
    query: 'spy',
    meilisearch: 47,
    opensearch: 168,
    pg: 349,
    'sqlite-disk': 241,
    'sqlite-mem': 241,
    typesense: 47,
  },
  {
    query: 'superhero',
    meilisearch: 56,
    opensearch: 431,
    pg: 34,
    'sqlite-disk': 20,
    'sqlite-mem': 20,
    typesense: 8,
  },
  {
    query: 'superhero',
    meilisearch: 14,
    opensearch: 45,
    pg: 86,
    'sqlite-disk': 67,
    'sqlite-mem': 67,
    typesense: 8,
  },
  {
    query: 'superman',
    meilisearch: 41,
    opensearch: 25,
    pg: 47,
    'sqlite-disk': 47,
    'sqlite-mem': 47,
    typesense: 28,
  },
  {
    query: 'suprman',
    meilisearch: 29,
    opensearch: 0,
    pg: 0,
    'sqlite-disk': 0,
    'sqlite-mem': 0,
    typesense: 28,
  },
  {
    query: 'worldwar',
    meilisearch: 316,
    opensearch: 3805,
    pg: 834,
    'sqlite-disk': 781,
    'sqlite-mem': 781,
    typesense: 11,
  },
]

const avg_latency = [
  {
    query: 'awakening',
    meilisearch: 0.24,
    opensearch: 0.31,
    pg: 0.01,
    'sqlite-disk': 0.0,
    'sqlite-mem': 0.0,
    typesense: 0.15,
  },
  {
    query: 'comedy',
    meilisearch: 0.05,
    opensearch: 0.05,
    pg: 0.0,
    'sqlite-disk': 0.0,
    'sqlite-mem': 0.0,
    typesense: 0.06,
  },
  {
    query: 'love',
    meilisearch: 0.02,
    opensearch: 0.05,
    pg: 0.0,
    'sqlite-disk': 0.01,
    'sqlite-mem': 0.0,
    typesense: 0.04,
  },
  {
    query: 'romance',
    meilisearch: 0.09,
    opensearch: 0.06,
    pg: 0.0,
    'sqlite-disk': 0.0,
    'sqlite-mem': 0.0,
    typesense: 0.08,
  },
  {
    query: 'spy',
    meilisearch: 0.08,
    opensearch: 0.08,
    pg: 0.01,
    'sqlite-disk': 0.0,
    'sqlite-mem': 0.0,
    typesense: 0.08,
  },
  {
    query: 'superhero',
    meilisearch: 0.09,
    opensearch: 0.26,
    pg: 0.11,
    'sqlite-disk': 0.03,
    'sqlite-mem': 0.02,
    typesense: 0.5,
  },
  {
    query: 'superhero',
    meilisearch: 0.31,
    opensearch: 0.28,
    pg: 0.02,
    'sqlite-disk': 0.01,
    'sqlite-mem': 0.0,
    typesense: 0.26,
  },
  {
    query: 'superman',
    meilisearch: 0.08,
    opensearch: 0.45,
    pg: 0.03,
    'sqlite-disk': 0.01,
    'sqlite-mem': 0.0,
    typesense: 0.1,
  },
  {
    query: 'suprman',
    meilisearch: 0.09,
    opensearch: undefined,
    pg: undefined,
    'sqlite-disk': undefined,
    'sqlite-mem': undefined,
    typesense: 0.1,
  },
  {
    query: 'worldwar',
    meilisearch: 0.03,
    opensearch: 0.04,
    pg: 0.0,
    'sqlite-disk': 0.01,
    'sqlite-mem': 0.0,
    typesense: 0.3,
  },
]

export { latency, results, avg_latency }
