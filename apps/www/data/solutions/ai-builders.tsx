import { Timer } from 'lucide-react'
import { CubeIcon } from '@heroicons/react/outline'
import { Image } from 'ui'
import { AIData } from './solutions.types'

const data: AIData = {
  metadata: {
    metaTitle: 'Supabase for AI Builders',
    metaDescription:
      'Leading enterprises use Supabase to build faster, better, and more scalable products.',
  },
  heroSection: {
    id: 'hero',
    title: 'AI Builders',
    h1: <>Supabase for AI Builders</>,
    subheader: [
      <>
        Supabase is the Postgres development platform that powers a new generation of developer
        tools. Give your users an integrated, scalable backend that lets them focus on building
        without worrying about infrastructure.
      </>,
    ],
    image: (
      <Image
        src={{
          dark: '/images/solutions/ai-builders/ai-builders-agent-dark.svg',
          light: '/images/solutions/ai-builders/ai-builders-agent-light.svg',
        }}
        alt="AI agent for ai builders"
        width={1000}
        height={1000}
      />
    ),
    ctas: [
      {
        label: 'Start your project',
        href: 'https://supabase.com/dashboard',
        type: 'primary' as any,
      },
      {
        label: 'Connect your app',
        href: 'https://supabase.com/docs/guides/integrations/build-a-supabase-integration',
        type: 'default' as any,
      },
    ],
    logos: [
      {
        name: 'GitHub',
        image: '/images/enterprise/github.svg',
      },
      {
        name: 'PwC',
        image: '/images/enterprise/pwc.svg',
      },
    ],
  },
  quotes: {
    id: 'quotes',
    items: [
      {
        icon: '/images/logos/publicity/lovable.svg',
        avatar: '/images/avatars/anton-osika.jpg',
        author: 'Anton Osika',
        authorTitle: 'Lovable - CEO',
        quote: (
          <>
            We chose Supabase because it's{' '}
            <span className="text-foreground">extremely user friendly</span> and{' '}
            <span className="text-foreground">
              covers all the needs to build full-stack applications
            </span>
            .
          </>
        ),
      },
      {
        icon: '/images/logos/publicity/bolt.svg',
        avatar: '/images/avatars/eric-simons.jpg',
        author: 'Eric Simmons',
        authorTitle: 'Bolt.new - CEO',
        quote: (
          <>
            Supabase is awesome. Supabase is the{' '}
            <span className="text-foreground">key database integration</span> that we have...because
            it’s the{' '}
            <span className="text-foreground">
              best product in the world for storing and retrieving data
            </span>
            .
          </>
        ),
      },
      {
        icon: '/images/logos/publicity/v0.svg',
        avatar: '/images/avatars/guillermo-rauch.jpg',
        author: 'Guillermo Rauch',
        authorTitle: 'Vercel (v0) - CEO',
        quote: (
          <>
            <span className="text-foreground">v0 integrates with Supabase seamlessly.</span> If you
            ask v0 to generate an application and it needs Supabase,{' '}
            <span className="text-foreground">
              you’ll be prompted to create a Supabase account right there in the application
            </span>
            .
          </>
        ),
      },
      {
        icon: '/images/logos/publicity/tempo.svg',
        avatar: '/images/avatars/kevin-michael.jpg',
        author: 'Kevin Michael',
        authorTitle: 'Tempo - CEO',
        quote: (
          <>
            <span className="text-foreground">
              Supabase is the missing piece for building full-stack React apps
            </span>{' '}
            and has been <span className="text-foreground">our go-to for a long time</span>. We love
            Supabase and so do our customers.
          </>
        ),
      },
    ],
  },
  why: {
    id: 'why-supabase',
    label: '',
    heading: (
      <>
        Why <span className="text-foreground">AI Builders</span> choose Supabase
      </>
    ),
    features: [
      {
        icon: Timer,
        heading: 'Get to market faster',
        subheading:
          'Supabase is easy to use and set up. Use your existing Postgres knowledge and skills. Build with your favorite frameworks and tools.',
      },
      {
        icon: CubeIcon,
        heading: 'The tools you need at a great price',
        subheading:
          'Supabase offers a fully integrated suite of tools including authentication, storage, edge functions, real-time subscriptions, and vector search. Use one or all.',
      },
      {
        icon: (props: any) => (
          <svg
            width="23"
            height="23"
            viewBox="0 0 40 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M3.43881 3.75378C4.10721 1.93324 5.84055 0.723145 7.77992 0.723145H15.6033V0.734736H17.0394C23.8756 0.734736 29.4173 6.27652 29.4173 13.1127V20.1749C29.4173 20.7272 28.9696 21.1749 28.4173 21.1749C27.8651 21.1749 27.4173 20.7272 27.4173 20.1749V13.1127C27.4173 7.38109 22.771 2.73474 17.0394 2.73474H15.4396C15.3877 2.73474 15.3366 2.73078 15.2868 2.72314H7.77992C6.6793 2.72314 5.6956 3.40989 5.31627 4.44308L2.7812 11.3479C2.37375 12.4577 2.69516 13.7038 3.58855 14.4781L5.32807 15.9856C6.12772 16.6786 6.58711 17.6847 6.58709 18.7428L6.58706 21.5134C6.58702 23.8192 8.45627 25.6885 10.7621 25.6885C11.4007 25.6885 11.9184 25.1708 11.9184 24.5322L11.9185 12.1874C11.9185 9.59233 12.955 7.10481 14.7977 5.27761C15.1899 4.88873 15.823 4.8914 16.2119 5.28357C16.6008 5.67574 16.5981 6.3089 16.2059 6.69777C14.742 8.14943 13.9185 10.1257 13.9185 12.1874L13.9184 24.5323C13.9184 26.2754 12.5053 27.6885 10.7621 27.6885C7.35169 27.6885 4.58701 24.9238 4.58706 21.5134L4.58709 18.7428C4.5871 18.2647 4.37953 17.8101 4.01822 17.497L2.27871 15.9894C0.757203 14.6708 0.209829 12.5486 0.90374 10.6586L3.43881 3.75378ZM16.539 18.5225C17.0348 18.2791 17.634 18.4838 17.8773 18.9796C19.1969 21.6686 21.9313 23.3727 24.9267 23.3726L32.8043 23.3726C33.3566 23.3725 33.8043 23.8203 33.8043 24.3725C33.8044 24.9248 33.3566 25.3725 32.8044 25.3726L29.4081 25.3726C29.4142 25.4172 29.4173 25.4628 29.4173 25.5091C29.4173 29.0627 26.1868 31.4165 22.6091 31.4165C19.2966 31.4165 16.5385 29.0518 15.9271 25.9188C15.8213 25.3767 16.175 24.8516 16.717 24.7458C17.2591 24.64 17.7843 24.9936 17.89 25.5357C18.3217 27.7475 20.2716 29.4165 22.6091 29.4165C25.447 29.4165 27.4173 27.6256 27.4173 25.5091C27.4173 25.4628 27.4205 25.4172 27.4266 25.3726L24.9267 25.3726C21.1684 25.3727 17.7375 23.2346 16.0818 19.8607C15.8385 19.3649 16.0432 18.7658 16.539 18.5225Z"
              fill="hsl(var(--foreground-light))"
            />
            <path
              d="M21.7224 13.0006C21.7224 13.6338 22.2358 14.1472 22.869 14.1472C23.5022 14.1472 24.0156 13.6338 24.0156 13.0006C24.0156 12.3674 23.5022 11.854 22.869 11.854C22.2358 11.854 21.7224 12.3674 21.7224 13.0006Z"
              fill="hsl(var(--foreground-light))"
            />
          </svg>
        ),
        heading: 'Scalable and dependable',
        subheading:
          'Supabase is Postgres, with all the performance, high availability, and flexibility your users need to grow.',
      },
    ],
  },
  features: {
    id: 'features',
    heading: (
      <>
        Supabase powers <span className="text-foreground block">next generation tools</span>
      </>
    ),
    subheading:
      'Build a delightful application building experience backed seamlessly by a powerful application backend.',
    features: {
      'mgmt-api': {
        id: 'mgmt-api',
        icon: Timer,
        heading: (
          <>
            <svg
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M11.7118 15.4553C10.8778 15.8061 9.96154 16 9 16C8.03846 16 7.1222 15.8061 6.28818 15.4553M2.42438 11.4058C2.14983 10.6556 2 9.84529 2 9C2 8.15471 2.14983 7.34441 2.42438 6.59421M6.28818 2.54465C7.1222 2.19387 8.03846 2 9 2C9.96154 2 10.8778 2.19387 11.7118 2.54466M15.4553 6.28818C15.8061 7.1222 16 8.03846 16 9C16 9.96154 15.8061 10.8778 15.4553 11.7118M11.0051 11.1136L13.0078 13.1163M4.99476 5.10328L6.88638 6.99491M6.88638 11.0051L4.88369 13.0078M12.8967 4.99476L11.0051 6.88639M6.08663 9C6.08663 7.39099 7.39099 6.08663 9 6.08663C10.609 6.08663 11.9134 7.39099 11.9134 9C11.9134 10.609 10.609 11.9134 9 11.9134C7.39099 11.9134 6.08663 10.609 6.08663 9ZM3.10575 3.10575C3.58524 2.62626 4.36265 2.62626 4.84214 3.10575C5.32164 3.58524 5.32164 4.36265 4.84214 4.84215C4.36265 5.32164 3.58524 5.32164 3.10575 4.84214C2.62625 4.36265 2.62625 3.58524 3.10575 3.10575ZM13.1579 13.1579C13.6373 12.6784 14.4148 12.6784 14.8943 13.1579C15.3737 13.6373 15.3737 14.4148 14.8943 14.8943C14.4148 15.3737 13.6373 15.3737 13.1579 14.8943C12.6784 14.4148 12.6784 13.6373 13.1579 13.1579ZM14.8948 3.10572C15.3743 3.58522 15.3743 4.36263 14.8948 4.84212C14.4153 5.32161 13.6379 5.32161 13.1584 4.84212C12.6789 4.36263 12.6789 3.58522 13.1584 3.10572C13.6379 2.62623 14.4153 2.62623 14.8948 3.10572ZM4.84214 13.1579C5.32164 13.6373 5.32164 14.4148 4.84214 14.8943C4.36265 15.3737 3.58524 15.3737 3.10575 14.8943C2.62625 14.4148 2.62625 13.6373 3.10575 13.1579C3.58524 12.6784 4.36265 12.6784 4.84214 13.1579Z"
                stroke="currentColor"
                strokeWidth="1.15"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="bevel"
              />
            </svg>
            Management API
          </>
        ),
        subheading: (
          <>
            <span className="text-foreground">Enable your customers to scale their projects.</span>{' '}
            Use the Management API to programmatically back every project with a powerful Supabase
            backend.
          </>
        ),
        img: (
          <Image
            src={{
              dark: '/images/solutions/ai-builders/mgmt-api-permissions-dark.png',
              light: '/images/solutions/ai-builders/mgmt-api-permissions-light.png',
            }}
            alt="Management Api Permissions panel"
            width={900}
            height={900}
          />
        ),
      },
      postgres: {
        id: 'postgres',
        icon: Timer,
        heading: (
          <>
            <svg
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M8.09431 1.43094C7.77675 1.43094 7.51931 1.68838 7.51931 2.00594C7.51931 2.32351 7.77675 2.58094 8.09431 2.58094V1.43094ZM15.3251 14.1989C15.3251 13.8813 15.0676 13.6239 14.7501 13.6239C14.4325 13.6239 14.1751 13.8813 14.1751 14.1989H15.3251ZM14.1751 11.4632C14.1751 11.7807 14.4325 12.0382 14.7501 12.0382C15.0676 12.0382 15.3251 11.7807 15.3251 11.4632H14.1751ZM9.41204 14.2006C9.35121 13.8889 9.04923 13.6856 8.73755 13.7464C8.42587 13.8072 8.22251 14.1092 8.28334 14.4209L9.41204 14.2006ZM6.80129 13.6979L6.22629 13.6979V13.6979H6.80129ZM2.42103 3.21819L1.88126 3.02002L1.88126 3.02002L2.42103 3.21819ZM1.1209 6.7594L0.581129 6.56122H0.581129L1.1209 6.7594ZM1.68051 8.92905L1.30393 9.36357H1.30393L1.68051 8.92905ZM2.57264 9.70221L2.19606 10.1367V10.1367L2.57264 9.70221ZM3.04137 10.7287L2.46637 10.7287L3.04137 10.7287ZM3.04135 12.1496L3.61635 12.1496L3.04135 12.1496ZM6.80133 7.36668L6.22633 7.36667V7.36667L6.80133 7.36668ZM8.53109 4.59541C8.75659 4.37181 8.75812 4.00774 8.53452 3.78225C8.31092 3.55675 7.94685 3.55521 7.72135 3.77882L8.53109 4.59541ZM12.9599 13.616L12.9599 13.041L12.9599 13.616ZM17 14.191C17.3176 14.191 17.575 13.9335 17.575 13.616C17.575 13.2984 17.3176 13.041 17 13.041L17 14.191ZM9.4003 10.8228C9.2604 10.5377 8.91588 10.42 8.63079 10.5599C8.3457 10.6998 8.22801 11.0443 8.36791 11.3294L9.4003 10.8228ZM8.09431 2.58094H8.91481V1.43094H8.09431V2.58094ZM14.1751 14.1989C14.1751 15.2397 13.2037 16.1407 11.7713 16.1407V17.2907C13.6291 17.2907 15.3251 16.066 15.3251 14.1989H14.1751ZM8.91481 2.58094C11.82 2.58094 14.1751 4.93605 14.1751 7.84122H15.3251C15.3251 4.30092 12.4551 1.43094 8.91481 1.43094V2.58094ZM14.1751 7.84122V11.4632H15.3251V7.84122H14.1751ZM11.7713 16.1407C10.6027 16.1407 9.62784 15.3063 9.41204 14.2006L8.28334 14.4209C8.60251 16.0563 10.0421 17.2907 11.7713 17.2907V16.1407ZM8.17827 1.425H4.16598V2.575H8.17827V1.425ZM1.88126 3.02002L0.581129 6.56122L1.66067 6.95757L2.9608 3.41636L1.88126 3.02002ZM1.30393 9.36357L2.19606 10.1367L2.94922 9.26768L2.05709 8.49452L1.30393 9.36357ZM2.46637 10.7287L2.46635 12.1496L3.61635 12.1496L3.61637 10.7287L2.46637 10.7287ZM7.37633 7.36668C7.37633 6.3259 7.79205 5.32824 8.53109 4.59541L7.72135 3.77882C6.76455 4.72758 6.22634 6.01922 6.22633 7.36667L7.37633 7.36668ZM0.581129 6.56122C0.216349 7.55478 0.504096 8.6704 1.30393 9.36357L2.05709 8.49452C1.61842 8.11435 1.46061 7.50249 1.66067 6.95757L0.581129 6.56122ZM5.69543 15.3787C6.62374 15.3787 7.37629 14.6262 7.37629 13.6979H6.22629C6.22629 13.9911 5.98862 14.2287 5.69543 14.2287V15.3787ZM5.69543 14.2287C4.54717 14.2287 3.61633 13.2979 3.61635 12.1496L2.46635 12.1496C2.46632 13.933 3.91204 15.3787 5.69543 15.3787V14.2287ZM2.19606 10.1367C2.36774 10.2855 2.46637 10.5015 2.46637 10.7287L3.61637 10.7287C3.61637 10.168 3.37295 9.63491 2.94922 9.26768L2.19606 10.1367ZM4.16598 1.425C3.1453 1.425 2.23304 2.06187 1.88126 3.02002L2.9608 3.41636C3.14637 2.91094 3.62757 2.575 4.16598 2.575V1.425ZM12.9599 14.191L17 14.191L17 13.041L12.9599 13.041L12.9599 14.191ZM8.36791 11.3294C9.2275 13.081 11.0087 14.191 12.9599 14.191L12.9599 13.041C11.4474 13.041 10.0666 12.1806 9.4003 10.8228L8.36791 11.3294ZM7.37629 13.6979L7.37633 7.36668L6.22633 7.36667L6.22629 13.6979L7.37629 13.6979Z"
                fill="currentColor"
              />
              <path
                d="M11.9044 7.79688C11.9016 7.79688 11.9003 7.79642 11.8995 7.79609C11.8984 7.79561 11.8968 7.79466 11.8952 7.79306C11.8936 7.79147 11.8927 7.78989 11.8922 7.78874C11.8919 7.78798 11.8914 7.7867 11.8914 7.78384C11.8914 7.78098 11.8919 7.7797 11.8922 7.77894C11.8927 7.7778 11.8936 7.77622 11.8952 7.77462C11.8968 7.77302 11.8984 7.77207 11.8995 7.77159C11.9003 7.77127 11.9016 7.7708 11.9044 7.7708C11.9073 7.7708 11.9086 7.77127 11.9093 7.77159C11.9105 7.77207 11.9121 7.77302 11.9137 7.77462C11.9153 7.77622 11.9162 7.7778 11.9167 7.77894C11.917 7.7797 11.9175 7.78098 11.9175 7.78384C11.9175 7.7867 11.917 7.78798 11.9167 7.78874C11.9162 7.78989 11.9153 7.79147 11.9137 7.79306C11.9121 7.79466 11.9105 7.79561 11.9093 7.79609C11.9086 7.79642 11.9073 7.79688 11.9044 7.79688Z"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth="1.15"
                strokeLinecap="round"
              />
            </svg>
            A complete platform, built on Postgres
          </>
        ),
        subheading: (
          <>
            <span className="text-foreground">
              Build on powerful platform that grows with your customers.
            </span>{' '}
            Supabase offers the tools developers need to build powerful applications. Your customers
            will appreciate knowing they can start quickly with a prototype and scale to millions
            with ease.
          </>
        ),
        img: (
          <svg
            width="390"
            height="160"
            viewBox="0 0 390 160"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full"
          >
            <rect
              x="0.5"
              y="0.5"
              width="39"
              height="39"
              rx="7.5"
              fill="hsl(var(--background-alternative))"
            />
            <rect
              x="0.5"
              y="0.5"
              width="39"
              height="39"
              rx="7.5"
              stroke="hsl(var(--border-default))"
            />
            <path
              d="M15.0719 18.1621H24.9448V22.7588H15.0719V18.1621Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M14.2539 23.9102C14.2539 23.2755 14.7684 22.761 15.4031 22.761H24.5964C25.2311 22.761 25.7456 23.2755 25.7456 23.9102V26.2085C25.7456 26.8432 25.2311 27.3577 24.5964 27.3577H15.4031C14.7684 27.3577 14.2539 26.8432 14.2539 26.2085V23.9102Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M14.2539 14.7148C14.2539 14.0802 14.7684 13.5657 15.4031 13.5657H24.5964C25.2311 13.5657 25.7456 14.0802 25.7456 14.7148V17.0132C25.7456 17.6479 25.2311 18.1624 24.5964 18.1624H15.4031C14.7684 18.1624 14.2539 17.6479 14.2539 17.0132V14.7148Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M53.008 24.608H55.328C57.424 24.608 59.184 23.2 59.184 20.352C59.184 17.488 57.456 16.048 55.36 16.048H53.008V24.608ZM55.376 26H51.44V14.656H55.408C58.288 14.656 60.8 16.624 60.8 20.352C60.8 24.064 58.256 26 55.376 26ZM62.3156 23.936C62.3156 22.56 63.3236 21.792 64.6516 21.6L66.7316 21.296C67.1956 21.232 67.3236 20.992 67.3236 20.72C67.3236 19.968 66.8276 19.344 65.6596 19.344C64.6036 19.344 64.0116 20 63.9156 20.896L62.4756 20.56C62.6356 19.088 63.9636 18.064 65.6276 18.064C67.9316 18.064 68.8276 19.376 68.8276 20.88V24.736C68.8276 25.408 68.8916 25.808 68.9236 26H67.4516C67.4196 25.808 67.3716 25.52 67.3716 24.96C67.0356 25.504 66.2676 26.24 64.8916 26.24C63.3236 26.24 62.3156 25.152 62.3156 23.936ZM65.0996 24.976C66.3316 24.976 67.3236 24.384 67.3236 22.704V22.352L64.9716 22.704C64.3316 22.8 63.8516 23.168 63.8516 23.856C63.8516 24.432 64.3316 24.976 65.0996 24.976ZM73.1866 15.872V18.304H74.8506V19.664H73.1866V23.664C73.1866 24.368 73.4746 24.72 74.2586 24.72C74.4506 24.72 74.7226 24.688 74.8506 24.656V25.936C74.7226 25.984 74.3386 26.08 73.8266 26.08C72.5146 26.08 71.6826 25.28 71.6826 23.888V19.664H70.2106V18.304H70.6266C71.4586 18.304 71.8106 17.792 71.8106 17.12V15.872H73.1866ZM76.3781 23.936C76.3781 22.56 77.3861 21.792 78.7141 21.6L80.7941 21.296C81.2581 21.232 81.3861 20.992 81.3861 20.72C81.3861 19.968 80.8901 19.344 79.7221 19.344C78.6661 19.344 78.0741 20 77.9781 20.896L76.5381 20.56C76.6981 19.088 78.0261 18.064 79.6901 18.064C81.9941 18.064 82.8901 19.376 82.8901 20.88V24.736C82.8901 25.408 82.9541 25.808 82.9861 26H81.5141C81.4821 25.808 81.4341 25.52 81.4341 24.96C81.0981 25.504 80.3301 26.24 78.9541 26.24C77.3861 26.24 76.3781 25.152 76.3781 23.936ZM79.1621 24.976C80.3941 24.976 81.3861 24.384 81.3861 22.704V22.352L79.0341 22.704C78.3941 22.8 77.9141 23.168 77.9141 23.856C77.9141 24.432 78.3941 24.976 79.1621 24.976ZM86.7211 26H85.2331V14.416H86.7211V19.392C87.0891 18.704 87.9851 18.08 89.2651 18.08C91.6011 18.08 92.8011 19.872 92.8011 22.112C92.8011 24.4 91.5051 26.208 89.2171 26.208C88.0811 26.208 87.2011 25.712 86.7211 24.88V26ZM91.2651 22.112C91.2651 20.448 90.3851 19.408 88.9931 19.408C87.6651 19.408 86.7051 20.448 86.7051 22.112C86.7051 23.776 87.6651 24.864 88.9931 24.864C90.3691 24.864 91.2651 23.776 91.2651 22.112ZM94.2375 23.936C94.2375 22.56 95.2455 21.792 96.5735 21.6L98.6535 21.296C99.1175 21.232 99.2455 20.992 99.2455 20.72C99.2455 19.968 98.7495 19.344 97.5815 19.344C96.5255 19.344 95.9335 20 95.8375 20.896L94.3975 20.56C94.5575 19.088 95.8855 18.064 97.5495 18.064C99.8535 18.064 100.75 19.376 100.75 20.88V24.736C100.75 25.408 100.814 25.808 100.846 26H99.3735C99.3415 25.808 99.2935 25.52 99.2935 24.96C98.9575 25.504 98.1895 26.24 96.8135 26.24C95.2455 26.24 94.2375 25.152 94.2375 23.936ZM97.0215 24.976C98.2535 24.976 99.2455 24.384 99.2455 22.704V22.352L96.8935 22.704C96.2535 22.8 95.7735 23.168 95.7735 23.856C95.7735 24.432 96.2535 24.976 97.0215 24.976ZM102.373 24L103.733 23.52C103.829 24.32 104.437 24.96 105.525 24.96C106.373 24.96 106.837 24.48 106.837 23.936C106.837 23.456 106.485 23.088 105.845 22.944L104.533 22.656C103.333 22.4 102.613 21.584 102.613 20.496C102.613 19.184 103.845 18.064 105.349 18.064C107.461 18.064 108.117 19.44 108.277 20.128L106.949 20.624C106.885 20.224 106.565 19.344 105.349 19.344C104.581 19.344 104.069 19.84 104.069 20.368C104.069 20.832 104.357 21.168 104.949 21.296L106.197 21.568C107.589 21.872 108.325 22.72 108.325 23.856C108.325 24.944 107.413 26.24 105.509 26.24C103.397 26.24 102.501 24.88 102.373 24ZM111.225 21.36H115.433C115.401 20.256 114.681 19.392 113.321 19.392C112.057 19.392 111.289 20.368 111.225 21.36ZM115.657 23.36L116.953 23.808C116.521 25.168 115.289 26.24 113.497 26.24C111.433 26.24 109.625 24.736 109.625 22.128C109.625 19.712 111.369 18.064 113.305 18.064C115.673 18.064 117.001 19.696 117.001 22.096C117.001 22.288 116.985 22.48 116.969 22.576H111.177C111.209 23.952 112.201 24.912 113.497 24.912C114.745 24.912 115.369 24.224 115.657 23.36Z"
              fill="hsl(var(--foreground-light))"
            />
            <rect
              x="0.5"
              y="60.5"
              width="39"
              height="39"
              rx="7.5"
              fill="hsl(var(--background-alternative))"
            />
            <rect
              x="0.5"
              y="60.5"
              width="39"
              height="39"
              rx="7.5"
              stroke="hsl(var(--border-default))"
            />
            <path
              d="M14.9304 82.2214H20.5554M14.9304 82.2214V84.4714H20.5554V82.2214M14.9304 82.2214V79.9714H20.5554V82.2214M22.2495 76.625V74.375C22.2495 73.1324 21.2422 72.125 19.9995 72.125C18.7569 72.125 17.7495 73.1324 17.7495 74.375V76.625M14.9063 78.9233L14.9063 84.4517C14.9063 85.721 15.9353 86.75 17.2046 86.75H22.7329C24.0022 86.75 25.0313 85.721 25.0313 84.4517V78.9233C25.0313 77.654 24.0022 76.625 22.7329 76.625L17.2046 76.625C15.9353 76.625 14.9063 77.654 14.9063 78.9233Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeWidth="1.14917"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M59.296 86L58.064 82.832H53.04L51.84 86H50.16L54.672 74.656H56.48L60.992 86H59.296ZM55.536 76.288L53.6 81.376H57.504L55.536 76.288ZM67.102 85.072C66.686 85.872 65.726 86.24 64.83 86.24C63.038 86.24 61.95 84.896 61.95 83.152V78.304H63.454V82.928C63.454 83.984 63.934 84.912 65.198 84.912C66.414 84.912 67.022 84.112 67.022 82.944V78.304H68.526V84.576C68.526 85.184 68.574 85.728 68.606 86H67.166C67.134 85.824 67.102 85.392 67.102 85.072ZM73.1085 75.872V78.304H74.7725V79.664H73.1085V83.664C73.1085 84.368 73.3965 84.72 74.1805 84.72C74.3725 84.72 74.6445 84.688 74.7725 84.656V85.936C74.6445 85.984 74.2605 86.08 73.7485 86.08C72.4365 86.08 71.6045 85.28 71.6045 83.888V79.664H70.1325V78.304H70.5485C71.3805 78.304 71.7325 77.792 71.7325 77.12V75.872H73.1085ZM78.284 81.472V86H76.78V74.416H78.284V79.2C78.844 78.384 79.74 78.08 80.62 78.08C82.46 78.08 83.372 79.408 83.372 81.12V86H81.868V81.376C81.868 80.304 81.42 79.44 80.076 79.44C78.924 79.44 78.316 80.336 78.284 81.472Z"
              fill="hsl(var(--foreground-light))"
            />
            <rect
              x="0.5"
              y="120.5"
              width="39"
              height="39"
              rx="7.5"
              fill="hsl(var(--background-alternative))"
            />
            <rect
              x="0.5"
              y="120.5"
              width="39"
              height="39"
              rx="7.5"
              stroke="hsl(var(--border-default))"
            />
            <path
              d="M16.8056 146.151C17.7616 146.649 18.8482 146.93 20.0005 146.93C23.8279 146.93 26.9307 143.827 26.9307 140C26.9307 138.845 26.6479 137.755 26.1478 136.797M23.2356 133.87C22.2699 133.359 21.169 133.07 20.0005 133.07C16.1731 133.07 13.0703 136.173 13.0703 140C13.0703 141.179 13.365 142.29 13.8847 143.262M13.8847 143.262C13.6053 143.604 13.4377 144.04 13.4377 144.516C13.4377 145.609 14.3242 146.496 15.4178 146.496C16.5113 146.496 17.3978 145.609 17.3978 144.516C17.3978 143.422 16.5113 142.535 15.4178 142.535C14.7997 142.535 14.2478 142.819 13.8847 143.262ZM26.6007 135.477C26.6007 136.571 25.7142 137.458 24.6206 137.458C23.5271 137.458 22.6406 136.571 22.6406 135.477C22.6406 134.384 23.5271 133.497 24.6206 133.497C25.7142 133.497 26.6007 134.384 26.6007 135.477ZM24.4556 140C24.4556 142.46 22.461 144.455 20.0005 144.455C17.54 144.455 15.5454 142.46 15.5454 140C15.5454 137.54 17.54 135.545 20.0005 135.545C22.461 135.545 24.4556 137.54 24.4556 140Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeWidth="1.14917"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M58.336 146H51.44V134.656H58.336V136.112H53.008V139.6H57.84V141.072H53.008V144.544H58.336V146ZM61.1604 142.128C61.1604 143.712 61.9764 144.88 63.4164 144.88C64.7924 144.88 65.6404 143.68 65.6404 142.096C65.6404 140.512 64.8084 139.424 63.4324 139.424C62.0564 139.424 61.1604 140.544 61.1604 142.128ZM65.6564 144.96V144.832C65.2884 145.568 64.4724 146.208 63.2724 146.208C61.0164 146.208 59.6244 144.416 59.6244 142.128C59.6244 139.952 61.0964 138.096 63.2724 138.096C64.6324 138.096 65.3684 138.768 65.6244 139.392V134.416H67.1124V144.576C67.1124 145.312 67.1764 145.872 67.1924 146H65.7364C65.7044 145.824 65.6564 145.424 65.6564 144.96ZM69.1106 146.416L70.5666 146.08C70.6786 147.152 71.4946 147.968 72.6626 147.968C74.2786 147.968 75.0146 147.136 75.0146 145.472V144.368C74.6466 145.072 73.8146 145.632 72.6626 145.632C70.6146 145.632 69.1266 144.08 69.1266 141.904C69.1266 139.824 70.5506 138.16 72.6626 138.16C73.8466 138.16 74.6466 138.608 75.0306 139.376V138.304H76.5186V145.424C76.5186 147.376 75.5586 149.28 72.6626 149.28C70.7426 149.28 69.3026 148.064 69.1106 146.416ZM72.8866 144.368C74.1826 144.368 75.0786 143.392 75.0786 141.904C75.0786 140.416 74.1826 139.44 72.8866 139.44C71.5586 139.44 70.6626 140.416 70.6626 141.904C70.6626 143.408 71.5266 144.368 72.8866 144.368ZM79.8966 141.36H84.1046C84.0726 140.256 83.3526 139.392 81.9926 139.392C80.7286 139.392 79.9606 140.368 79.8966 141.36ZM84.3286 143.36L85.6246 143.808C85.1926 145.168 83.9606 146.24 82.1686 146.24C80.1046 146.24 78.2966 144.736 78.2966 142.128C78.2966 139.712 80.0406 138.064 81.9766 138.064C84.3446 138.064 85.6726 139.696 85.6726 142.096C85.6726 142.288 85.6566 142.48 85.6406 142.576H79.8486C79.8806 143.952 80.8726 144.912 82.1686 144.912C83.4166 144.912 84.0406 144.224 84.3286 143.36ZM93.3205 146H91.7525V134.656H98.6485V136.112H93.3205V139.76H98.1525V141.232H93.3205V146ZM104.758 145.072C104.342 145.872 103.382 146.24 102.486 146.24C100.694 146.24 99.6063 144.896 99.6063 143.152V138.304H101.11V142.928C101.11 143.984 101.59 144.912 102.854 144.912C104.07 144.912 104.678 144.112 104.678 142.944V138.304H106.182V144.576C106.182 145.184 106.23 145.728 106.262 146H104.822C104.79 145.824 104.758 145.392 104.758 145.072ZM110.253 141.552V146H108.749V138.304H110.221V139.408C110.781 138.448 111.693 138.08 112.589 138.08C114.429 138.08 115.341 139.408 115.341 141.12V146H113.837V141.376C113.837 140.304 113.389 139.44 112.045 139.44C110.861 139.44 110.253 140.384 110.253 141.552ZM120.965 139.456C119.749 139.456 118.629 140.352 118.629 142.144C118.629 143.904 119.733 144.848 120.981 144.848C122.421 144.848 122.949 143.872 123.125 143.264L124.453 143.84C124.085 144.944 122.981 146.24 120.981 146.24C118.741 146.24 117.093 144.48 117.093 142.144C117.093 139.744 118.773 138.064 120.965 138.064C123.013 138.064 124.069 139.344 124.389 140.512L123.029 141.088C122.837 140.336 122.277 139.456 120.965 139.456ZM128.234 135.872V138.304H129.898V139.664H128.234V143.664C128.234 144.368 128.522 144.72 129.306 144.72C129.498 144.72 129.77 144.688 129.898 144.656V145.936C129.77 145.984 129.386 146.08 128.874 146.08C127.562 146.08 126.73 145.28 126.73 143.888V139.664H125.258V138.304H125.674C126.506 138.304 126.858 137.792 126.858 137.12V135.872H128.234ZM133.393 146H131.905V138.304H133.393V146ZM131.569 135.392C131.569 134.784 132.049 134.304 132.641 134.304C133.249 134.304 133.729 134.784 133.729 135.392C133.729 135.984 133.249 136.464 132.641 136.464C132.049 136.464 131.569 135.984 131.569 135.392ZM139.248 144.896C140.528 144.896 141.648 143.936 141.648 142.144C141.648 140.368 140.528 139.408 139.248 139.408C137.968 139.408 136.848 140.368 136.848 142.144C136.848 143.936 137.968 144.896 139.248 144.896ZM139.248 138.064C141.552 138.064 143.184 139.808 143.184 142.144C143.184 144.496 141.552 146.24 139.248 146.24C136.944 146.24 135.312 144.496 135.312 142.144C135.312 139.808 136.944 138.064 139.248 138.064ZM146.612 141.552V146H145.108V138.304H146.58V139.408C147.14 138.448 148.052 138.08 148.948 138.08C150.788 138.08 151.7 139.408 151.7 141.12V146H150.196V141.376C150.196 140.304 149.748 139.44 148.404 139.44C147.22 139.44 146.612 140.384 146.612 141.552ZM153.373 144L154.733 143.52C154.829 144.32 155.437 144.96 156.525 144.96C157.373 144.96 157.837 144.48 157.837 143.936C157.837 143.456 157.485 143.088 156.845 142.944L155.533 142.656C154.333 142.4 153.613 141.584 153.613 140.496C153.613 139.184 154.845 138.064 156.349 138.064C158.461 138.064 159.117 139.44 159.277 140.128L157.949 140.624C157.885 140.224 157.565 139.344 156.349 139.344C155.581 139.344 155.069 139.84 155.069 140.368C155.069 140.832 155.357 141.168 155.949 141.296L157.197 141.568C158.589 141.872 159.325 142.72 159.325 143.856C159.325 144.944 158.413 146.24 156.509 146.24C154.397 146.24 153.501 144.88 153.373 144Z"
              fill="hsl(var(--foreground-light))"
            />
            <rect
              x="214.5"
              y="0.5"
              width="39"
              height="39"
              rx="7.5"
              fill="hsl(var(--background-alternative))"
            />
            <rect
              x="214.5"
              y="0.5"
              width="39"
              height="39"
              rx="7.5"
              stroke="hsl(var(--border-default))"
            />
            <path
              d="M239.627 19.4473V17.3013L235.557 13.2503H229.526C228.891 13.2503 228.377 13.7648 228.377 14.3994V17.7497M239.587 17.2825L235.555 13.25L235.555 16.1334C235.555 16.768 236.069 17.2825 236.704 17.2825L239.587 17.2825ZM229.989 17.7497H228.372C227.737 17.7497 227.223 18.2642 227.223 18.8989V24.4514C227.223 25.7207 228.252 26.7497 229.521 26.7497H238.424C239.694 26.7497 240.723 25.7207 240.723 24.4514V20.5964C240.723 19.9618 240.208 19.4473 239.573 19.4473H232.655C232.354 19.4473 232.064 19.3288 231.849 19.1173L230.795 18.0797C230.58 17.8682 230.29 17.7497 229.989 17.7497Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeWidth="1.14917"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M272.672 17.232L271.264 17.728C271.136 16.912 270.448 15.792 268.864 15.792C267.584 15.792 266.672 16.624 266.672 17.68C266.672 18.496 267.168 19.12 268.128 19.328L269.824 19.696C271.712 20.112 272.768 21.296 272.768 22.88C272.768 24.64 271.264 26.24 268.848 26.24C266.112 26.24 264.816 24.48 264.608 22.864L266.112 22.384C266.224 23.648 267.12 24.816 268.832 24.816C270.4 24.816 271.168 24 271.168 23.008C271.168 22.192 270.608 21.488 269.472 21.248L267.856 20.896C266.24 20.544 265.088 19.488 265.088 17.792C265.088 16.016 266.736 14.416 268.832 14.416C271.392 14.416 272.416 16 272.672 17.232ZM276.624 15.872V18.304H278.288V19.664H276.624V23.664C276.624 24.368 276.912 24.72 277.696 24.72C277.888 24.72 278.16 24.688 278.288 24.656V25.936C278.16 25.984 277.776 26.08 277.264 26.08C275.952 26.08 275.12 25.28 275.12 23.888V19.664H273.648V18.304H274.064C274.896 18.304 275.248 17.792 275.248 17.12V15.872H276.624ZM283.514 24.896C284.794 24.896 285.914 23.936 285.914 22.144C285.914 20.368 284.794 19.408 283.514 19.408C282.234 19.408 281.114 20.368 281.114 22.144C281.114 23.936 282.234 24.896 283.514 24.896ZM283.514 18.064C285.818 18.064 287.45 19.808 287.45 22.144C287.45 24.496 285.818 26.24 283.514 26.24C281.21 26.24 279.578 24.496 279.578 22.144C279.578 19.808 281.21 18.064 283.514 18.064ZM293.646 18.224V19.824C293.422 19.792 293.198 19.776 292.99 19.776C291.726 19.776 290.878 20.448 290.878 22.144V26H289.374V18.304H290.846V19.648C291.406 18.464 292.366 18.176 293.15 18.176C293.358 18.176 293.566 18.208 293.646 18.224ZM294.769 23.936C294.769 22.56 295.777 21.792 297.105 21.6L299.185 21.296C299.649 21.232 299.777 20.992 299.777 20.72C299.777 19.968 299.281 19.344 298.113 19.344C297.057 19.344 296.465 20 296.369 20.896L294.929 20.56C295.089 19.088 296.417 18.064 298.081 18.064C300.385 18.064 301.281 19.376 301.281 20.88V24.736C301.281 25.408 301.345 25.808 301.377 26H299.905C299.873 25.808 299.825 25.52 299.825 24.96C299.489 25.504 298.721 26.24 297.345 26.24C295.777 26.24 294.769 25.152 294.769 23.936ZM297.553 24.976C298.785 24.976 299.777 24.384 299.777 22.704V22.352L297.425 22.704C296.785 22.8 296.305 23.168 296.305 23.856C296.305 24.432 296.785 24.976 297.553 24.976ZM303.064 26.416L304.52 26.08C304.632 27.152 305.448 27.968 306.616 27.968C308.232 27.968 308.968 27.136 308.968 25.472V24.368C308.6 25.072 307.768 25.632 306.616 25.632C304.568 25.632 303.08 24.08 303.08 21.904C303.08 19.824 304.504 18.16 306.616 18.16C307.8 18.16 308.6 18.608 308.984 19.376V18.304H310.472V25.424C310.472 27.376 309.512 29.28 306.616 29.28C304.696 29.28 303.256 28.064 303.064 26.416ZM306.84 24.368C308.136 24.368 309.032 23.392 309.032 21.904C309.032 20.416 308.136 19.44 306.84 19.44C305.512 19.44 304.616 20.416 304.616 21.904C304.616 23.408 305.48 24.368 306.84 24.368ZM313.85 21.36H318.058C318.026 20.256 317.306 19.392 315.946 19.392C314.682 19.392 313.914 20.368 313.85 21.36ZM318.282 23.36L319.578 23.808C319.146 25.168 317.914 26.24 316.122 26.24C314.058 26.24 312.25 24.736 312.25 22.128C312.25 19.712 313.994 18.064 315.93 18.064C318.298 18.064 319.626 19.696 319.626 22.096C319.626 22.288 319.61 22.48 319.594 22.576H313.802C313.834 23.952 314.826 24.912 316.122 24.912C317.37 24.912 317.994 24.224 318.282 23.36Z"
              fill="hsl(var(--foreground-light))"
            />
            <rect
              x="214.5"
              y="60.5"
              width="39"
              height="39"
              rx="7.5"
              fill="hsl(var(--background-alternative))"
            />
            <rect
              x="214.5"
              y="60.5"
              width="39"
              height="39"
              rx="7.5"
              stroke="hsl(var(--border-default))"
            />
            <path
              d="M231.032 72.1074V74.9124M228.933 74.9124L226.536 72.4418M228.933 76.8532H226.184M236.534 82.5839L240.243 81.6607C240.774 81.5284 240.837 80.7977 240.335 80.5772L231.475 76.6811C230.996 76.4704 230.507 76.9578 230.717 77.4373L234.543 86.1868C234.762 86.687 235.489 86.6289 235.626 86.1002L236.534 82.5839Z"
              stroke="hsl(var(--foreground-lighter))"
              strokeWidth="1.14917"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M271.312 86L268.72 81.36H267.024V86H265.44V74.656H269.712C271.856 74.656 273.184 76.16 273.184 78.032C273.184 79.664 272.144 80.928 270.432 81.248L273.136 86H271.312ZM267.024 79.952H269.424C270.704 79.952 271.568 79.2 271.568 78.032C271.568 76.832 270.704 76.064 269.424 76.064H267.024V79.952ZM275.85 81.36H280.058C280.026 80.256 279.306 79.392 277.946 79.392C276.682 79.392 275.914 80.368 275.85 81.36ZM280.282 83.36L281.578 83.808C281.146 85.168 279.914 86.24 278.122 86.24C276.058 86.24 274.25 84.736 274.25 82.128C274.25 79.712 275.994 78.064 277.93 78.064C280.298 78.064 281.626 79.696 281.626 82.096C281.626 82.288 281.61 82.48 281.594 82.576H275.802C275.834 83.952 276.826 84.912 278.122 84.912C279.37 84.912 279.994 84.224 280.282 83.36ZM283.066 83.936C283.066 82.56 284.074 81.792 285.402 81.6L287.482 81.296C287.946 81.232 288.074 80.992 288.074 80.72C288.074 79.968 287.578 79.344 286.41 79.344C285.354 79.344 284.762 80 284.666 80.896L283.226 80.56C283.386 79.088 284.714 78.064 286.378 78.064C288.682 78.064 289.578 79.376 289.578 80.88V84.736C289.578 85.408 289.642 85.808 289.674 86H288.202C288.17 85.808 288.122 85.52 288.122 84.96C287.786 85.504 287.018 86.24 285.642 86.24C284.074 86.24 283.066 85.152 283.066 83.936ZM285.85 84.976C287.082 84.976 288.074 84.384 288.074 82.704V82.352L285.722 82.704C285.082 82.8 284.602 83.168 284.602 83.856C284.602 84.432 285.082 84.976 285.85 84.976ZM293.425 86H291.921V74.416H293.425V86ZM297.999 75.872V78.304H299.663V79.664H297.999V83.664C297.999 84.368 298.287 84.72 299.071 84.72C299.263 84.72 299.535 84.688 299.663 84.656V85.936C299.535 85.984 299.151 86.08 298.639 86.08C297.327 86.08 296.495 85.28 296.495 83.888V79.664H295.023V78.304H295.439C296.271 78.304 296.623 77.792 296.623 77.12V75.872H297.999ZM303.159 86H301.671V78.304H303.159V86ZM301.335 75.392C301.335 74.784 301.815 74.304 302.407 74.304C303.015 74.304 303.495 74.784 303.495 75.392C303.495 75.984 303.015 76.464 302.407 76.464C301.815 76.464 301.335 75.984 301.335 75.392ZM307.206 86H305.718V78.304H307.158V79.328C307.638 78.48 308.598 78.08 309.494 78.08C310.454 78.08 311.382 78.544 311.798 79.536C312.406 78.432 313.398 78.08 314.374 78.08C315.718 78.08 317.03 78.992 317.03 81.008V86H315.542V81.168C315.542 80.16 315.046 79.408 313.91 79.408C312.854 79.408 312.134 80.24 312.134 81.344V86H310.63V81.168C310.63 80.176 310.15 79.408 308.998 79.408C307.926 79.408 307.206 80.208 307.206 81.36V86ZM320.412 81.36H324.62C324.588 80.256 323.868 79.392 322.508 79.392C321.244 79.392 320.476 80.368 320.412 81.36ZM324.844 83.36L326.14 83.808C325.708 85.168 324.476 86.24 322.684 86.24C320.62 86.24 318.812 84.736 318.812 82.128C318.812 79.712 320.556 78.064 322.492 78.064C324.86 78.064 326.188 79.696 326.188 82.096C326.188 82.288 326.172 82.48 326.156 82.576H320.364C320.396 83.952 321.388 84.912 322.684 84.912C323.932 84.912 324.556 84.224 324.844 83.36Z"
              fill="hsl(var(--foreground-light))"
            />
            <rect
              x="214.5"
              y="120.5"
              width="39"
              height="39"
              rx="7.5"
              fill="hsl(var(--background-alternative))"
            />
            <rect
              x="214.5"
              y="120.5"
              width="39"
              height="39"
              rx="7.5"
              stroke="hsl(var(--border-default))"
            />
            <path
              d="M234.001 139.586V147.3M234.001 139.586L240.807 135.633M234.001 139.586L227.195 135.633M227.195 135.633V140.364M227.195 135.633V135.592L231.272 133.224M240.808 140.405V135.592L236.731 133.224M238.296 144.958L234.001 147.452L229.707 144.958"
              stroke="hsl(var(--foreground-lighter))"
              strokeWidth="1.14917"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M269.248 143.984L272.704 134.656H274.352L269.984 146H268.432L264.16 134.656H265.824L269.248 143.984ZM275.897 141.36H280.105C280.073 140.256 279.353 139.392 277.993 139.392C276.729 139.392 275.961 140.368 275.897 141.36ZM280.329 143.36L281.625 143.808C281.193 145.168 279.961 146.24 278.169 146.24C276.105 146.24 274.297 144.736 274.297 142.128C274.297 139.712 276.041 138.064 277.977 138.064C280.345 138.064 281.673 139.696 281.673 142.096C281.673 142.288 281.657 142.48 281.641 142.576H275.849C275.881 143.952 276.873 144.912 278.169 144.912C279.417 144.912 280.041 144.224 280.329 143.36ZM286.825 139.456C285.609 139.456 284.489 140.352 284.489 142.144C284.489 143.904 285.593 144.848 286.841 144.848C288.281 144.848 288.809 143.872 288.985 143.264L290.312 143.84C289.945 144.944 288.841 146.24 286.841 146.24C284.601 146.24 282.953 144.48 282.953 142.144C282.953 139.744 284.633 138.064 286.825 138.064C288.873 138.064 289.929 139.344 290.249 140.512L288.889 141.088C288.697 140.336 288.137 139.456 286.825 139.456ZM294.093 135.872V138.304H295.757V139.664H294.093V143.664C294.093 144.368 294.381 144.72 295.165 144.72C295.357 144.72 295.629 144.688 295.757 144.656V145.936C295.629 145.984 295.245 146.08 294.733 146.08C293.421 146.08 292.589 145.28 292.589 143.888V139.664H291.117V138.304H291.533C292.365 138.304 292.717 137.792 292.717 137.12V135.872H294.093ZM300.982 144.896C302.262 144.896 303.382 143.936 303.382 142.144C303.382 140.368 302.262 139.408 300.982 139.408C299.702 139.408 298.582 140.368 298.582 142.144C298.582 143.936 299.702 144.896 300.982 144.896ZM300.982 138.064C303.286 138.064 304.918 139.808 304.918 142.144C304.918 144.496 303.286 146.24 300.982 146.24C298.678 146.24 297.046 144.496 297.046 142.144C297.046 139.808 298.678 138.064 300.982 138.064ZM311.115 138.224V139.824C310.891 139.792 310.667 139.776 310.459 139.776C309.195 139.776 308.347 140.448 308.347 142.144V146H306.843V138.304H308.315V139.648C308.875 138.464 309.835 138.176 310.619 138.176C310.827 138.176 311.035 138.208 311.115 138.224ZM311.998 144L313.358 143.52C313.454 144.32 314.062 144.96 315.15 144.96C315.998 144.96 316.462 144.48 316.462 143.936C316.462 143.456 316.11 143.088 315.47 142.944L314.158 142.656C312.958 142.4 312.238 141.584 312.238 140.496C312.238 139.184 313.47 138.064 314.974 138.064C317.086 138.064 317.742 139.44 317.902 140.128L316.574 140.624C316.51 140.224 316.19 139.344 314.974 139.344C314.206 139.344 313.694 139.84 313.694 140.368C313.694 140.832 313.982 141.168 314.574 141.296L315.822 141.568C317.214 141.872 317.95 142.72 317.95 143.856C317.95 144.944 317.038 146.24 315.134 146.24C313.022 146.24 312.126 144.88 311.998 144Z"
              fill="hsl(var(--foreground-light))"
            />
          </svg>
        ),
      },
      branching: {
        id: 'branching',
        icon: Timer,
        heading: (
          <>
            <svg
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.5 15.75C14.7426 15.75 15.75 14.7426 15.75 13.5C15.75 12.2574 14.7426 11.25 13.5 11.25C12.2574 11.25 11.25 12.2574 11.25 13.5C11.25 14.7426 12.2574 15.75 13.5 15.75Z"
                stroke="currentColor"
                strokeWidth="1.15"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M4.5 6.75C5.74264 6.75 6.75 5.74264 6.75 4.5C6.75 3.25736 5.74264 2.25 4.5 2.25C3.25736 2.25 2.25 3.25736 2.25 4.5C2.25 5.74264 3.25736 6.75 4.5 6.75Z"
                stroke="currentColor"
                strokeWidth="1.15"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M9.75 4.5H12C12.3978 4.5 12.7794 4.65804 13.0607 4.93934C13.342 5.22064 13.5 5.60218 13.5 6V11.25"
                stroke="currentColor"
                strokeWidth="1.15"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8.25 13.5H6C5.60218 13.5 5.22064 13.342 4.93934 13.0607C4.65804 12.7794 4.5 12.3978 4.5 12V6.75"
                stroke="currentColor"
                strokeWidth="1.15"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Branching
          </>
        ),
        subheading: (
          <>
            <span className="text-foreground">Offer production and development branches.</span>{' '}
            Enable your customers to deploy and test changes without affecting their main production
            applications.
          </>
        ),
      },
      pricing: {
        id: 'pricing',
        icon: Timer,
        heading: (
          <>
            <svg
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.00391 1.67476C4.95842 1.67476 1.67891 4.95427 1.67891 8.99975C1.67891 13.0452 4.95842 16.3248 9.00391 16.3248C13.0494 16.3248 16.3289 13.0452 16.3289 8.99975C16.3289 4.95427 13.0494 1.67476 9.00391 1.67476ZM1.32891 8.99975C1.32891 4.76097 4.76512 1.32476 9.00391 1.32476C13.2427 1.32476 16.6789 4.76097 16.6789 8.99975C16.6789 13.2385 13.2427 16.6748 9.00391 16.6748C4.76512 16.6748 1.32891 13.2385 1.32891 8.99975Z"
                fill="black"
                stroke="currentColor"
                strokeWidth="1"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M5.90901 5.90877C6.33097 5.48681 6.90326 5.24976 7.5 5.24976H12C12.4142 5.24976 12.75 5.58554 12.75 5.99976C12.75 6.41397 12.4142 6.74976 12 6.74976H7.5C7.30109 6.74976 7.11032 6.82877 6.96967 6.96943C6.82902 7.11008 6.75 7.30084 6.75 7.49976C6.75 7.69867 6.82902 7.88943 6.96967 8.03009C7.11032 8.17074 7.30109 8.24976 7.5 8.24976H10.5C11.0967 8.24976 11.669 8.48681 12.091 8.90877C12.5129 9.33072 12.75 9.90302 12.75 10.4998C12.75 11.0965 12.5129 11.6688 12.091 12.0907C11.669 12.5127 11.0967 12.7498 10.5 12.7498H6C5.58579 12.7498 5.25 12.414 5.25 11.9998C5.25 11.5855 5.58579 11.2498 6 11.2498H10.5C10.6989 11.2498 10.8897 11.1707 11.0303 11.0301C11.171 10.8894 11.25 10.6987 11.25 10.4998C11.25 10.3008 11.171 10.1101 11.0303 9.96943C10.8897 9.82877 10.6989 9.74976 10.5 9.74976H7.5C6.90326 9.74976 6.33097 9.5127 5.90901 9.09075C5.48705 8.66879 5.25 8.09649 5.25 7.49976C5.25 6.90302 5.48705 6.33072 5.90901 5.90877Z"
                fill="currentColor"
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.99609 3.75C9.41031 3.75 9.74609 4.08579 9.74609 4.5V13.5C9.74609 13.9142 9.41031 14.25 8.99609 14.25C8.58188 14.25 8.24609 13.9142 8.24609 13.5V4.5C8.24609 4.08579 8.58188 3.75 8.99609 3.75Z"
                fill="currentColor"
              />
            </svg>
            Pricing
          </>
        ),
        subheading: (
          <>
            <span className="text-foreground">Pricing that’s designed for builders.</span> Supabase
            offers pricing options for AI Builders that enable you to build substantial businesses
            that empower your users to go into production.
          </>
        ),
      },
    },
  },
  testimonials: {
    id: 'testimonials',
    label: '',
    heading: (
      <>
        Powerful tools, <span className="text-foreground block">powered by Supabase</span>
      </>
    ),
    videos: {
      lovable: {
        url: 'https://www.youtube.com/watch?v=9GQtXXERnqU',
      },
      bolt: {
        url: 'https://www.youtube.com/watch?v=LfAV5fmRybg',
      },
    },
  },
  'cta-section': {
    id: 'connect-to-supabase',
    label: '',
    heading: <>Connect your app to Supabase now</>,
    subheading:
      'Set up a Supabase OAuth app so your users can start interacting with their Supabase Project.',
    cta: {
      label: 'View docs',
      href: 'https://supabase.com/docs/guides/auth/auth-google',
      type: 'text',
    },
  },
}

export default data
