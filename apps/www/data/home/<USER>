[{"label": "Table editor", "title": "Manage your data with the familiarity of a spreadsheet", "text": "You don’t have to be a database expert to use Supabase. Our table editor makes Postgres easy to use, even for non-techies. You can do everything right in our dashboard.", "youtube_id": "xIHjwJgxOmk", "cta": "Explore Table View", "url": "/database"}, {"label": "SQL Editor", "title": "In-built SQL editor for when you need greater control", "text": "Write, save, and execute SQL queries directly on our dashboard, with templates to save you time. Run common queries and even build applications using our growing list of templates.", "video_url": "https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/tabSqlEditor.mp4", "youtube_id": "Hch1mZPZ53A", "cta": "Explore SQL Editor", "url": "/database"}, {"label": "Auth rules", "title": "User management as straight-forward as it can be", "text": "Easily manage your users with Supabase Auth, with email logins, magic links, and third-party logins. Create complex access policies with SQL rules to fit your unique business needs.", "video_url": "https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/tabAuthRules.mp4", "youtube_id": "vP319FCIZ6Y", "cta": "Explore Auth", "url": "/auth"}]