---
title: 'Supabase Swift'
description: 'Supabase Swift is now officially supported.'
author: grdsdev
image: launch-week-11/swift/og.png
thumb: launch-week-11/swift/thumb.png
categories:
  - product
tags:
  - launch-week
  - database
date: '2024-04-15'
toc_depth: 3
launchweek: '11'
---

We are excited to announce that Supabase Swift libraries are now officially supported by Supabase.

This makes it simple to interact with Supabase from applications on Apple's platforms, including iOS, macOS, watchOS, tvOS, and visionOS:

```swift
let url = URL(string: "...")!
let anonKey = "public-anon-key"
let client = SupabaseClient(supabaseURL: url, supabaseKey: anon<PERSON>ey)

struct Country: Decodable {
  let id: Int
  let name: String
}

let countries: [Country] = try await supabase.from("countries")
  .select()
  .execute()
  .value
```

## New features

This release includes the following new features:

- WhatsApp OTP: https://github.com/supabase/supabase-swift/pull/287
- Captcha support: https://github.com/supabase/supabase-swift/pull/276
- SSO: https://github.com/supabase/supabase-swift/pull/289
- Simplified Storage uploads: https://github.com/supabase/supabase-swift/pull/290
- Anonymous sign-ins: https://github.com/supabase-community/supabase-swift/releases/tag/v2.6.0
- Simplified OAuth: https://github.com/supabase/supabase-swift/pull/299

## What does official support mean?

Swift developers can now integrate Supabase services seamlessly with official support. This means:

- **Direct assistance from the Supabase team**: Get timely and effective help directly from the developers who build and maintain your tools.
- **Continuously updated libraries**: Stay up-to-date with the latest features and optimizations that are fully tested and endorsed by Supabase.
- **Community and collaboration**: Engage with a broader community of Swift developers using Supabase, share knowledge, and contribute to the library's growth.

## Contributors

We want to give a shout out to the community members who have contributed to the development of the Supabase Swift libraries:

[grdsdev](https://github.com/grdsdev), [satishbabariya](https://github.com/satishbabariya), [AngCosmin](https://github.com/AngCosmin), [thecoolwinter](https://github.com/thecoolwinter), [maail](https://github.com/maail), [gentilijuanmanuel](https://github.com/gentilijuanmanuel), [mbarnach](https://github.com/mbarnach), [mdloucks](https://github.com/mdloucks), [mpross512](https://github.com/mpross512), [SaurabhJamadagni](https://github.com/SaurabhJamadagni), [theolampert](https://github.com/theolampert), [tyirenkyi](https://github.com/tyirenkyi), [tmn](https://github.com/tmn), [multimokia](https://github.com/multimokia), [zunda-pixel](https://github.com/zunda-pixel), [iamlouislab](https://github.com/iamlouislab), [jxhug](https://github.com/jxhug), [james-william-r](https://github.com/james-william-r), [jknlsn](https://github.com/jknlsn), [jknlsn](https://github.com/glowcap), [Colgates](https://github.com/Colgates), [ChristophePRAT](https://github.com/ChristophePRAT), [brianmichel](https://github.com/brianmichel), [junjielu](https://github.com/junjielu).

## Getting started

We've released a [new guide](/docs/guides/getting-started/tutorials/with-swift) to help you get started with the key features available in Supabase Swift.

Or you can jump into our deep dive to use iOS Swift with Postgres & Supabase Auth:

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/enVDRqzmudo"
    title="iOS Swift Database & Auth"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  />
</div>
