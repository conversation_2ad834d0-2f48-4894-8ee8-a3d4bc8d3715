---
title: 'Supabase + Vercel Partnership'
description: "Vercel just added official First-Party Integrations. We're one of them."
author: ka<PERSON><PERSON>_og<PERSON><PERSON>,jonny
image: vercel-supabase/vercel-supa-thumb.png
thumb: vercel-supabase/vercel-supa-thumb.png
categories:
  - product
tags:
  - product
date: '2024-08-28'
toc_depth: 3
---

Vercel just added official [First-Party Integrations](https://vercel.com/blog/introducing-the-vercel-marketplace). We're one of them.

This makes it _a lot_ easier to launch Postgres databases from Vercel with full support for [Vercel Templates](https://vercel.com/templates/next.js/supabase) and integrated billing.

<Quote img="lee-robinson.png" caption="<PERSON>, VP of Product @ Vercel">

Postgres is my favorite database—and Supabase makes Postgres _incredibly_ easy. Plus, having first-party solutions for auth and vector search is so convenient when I'm trying to ship quickly. Now, I can have my Supabase database and my Vercel frontend together in one bill.

</Quote>

## What is the integration?

This integration means that you can manage all your Supabase services directly from the Vercel dashboard. You can create, manage, and delete databases and all the credentials are automatically injected into your Vercel environment.

All the billing is unified in your Vercel bill.

<video
  width="99%"
  autoPlay
  disablePictureInPicture
  loop
  muted
  playsInline
  controls={false}
  className="rounded-md border"
>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/vercel-marketplace/supabase-integration.mp4?t=2024-08-28T16%3A31%3A37.197Z"
    type="video/mp4"
  />
</video>

## Pairing Vercel & Supabase

Vercel + Supabase have a similar DNA - we're focused on making developers more productive, without compromising on performance & scale. Vercel and Supabase are #1 and #2 most popular for [developers shout outs](https://www.producthunt.com/shoutouts/engineering-development) on ProductHunt.

![Vercel and Supabase on Product Hunt](/images/blog/vercel-supabase/producthunt-vercel-supabase.png)

We've found that Supabase and Vercel has been a very popular pairing for scale ups, YC companies, and large enterprises.

<Quote img="zeno-rocha.png" caption="Zeno Rocha, CEO @ Resend">

I don't think we would have grown so quickly without Supabase and Vercel. We have used many different products since we started the company, but Supabase and Vercel are the few services that we still use today. Now, there are 180,000 Resend users sending millions of emails every single day, and even though we outgrew many other products, Supabase and Vercel continue to help scale our company despite our challenges evolving all the time.

</Quote>

## Features

Check out some of these features that make Supabase + Vercel a great combination:

### Pure, Dedicated Postgres

When you launch a Postgres database on Supabase, you get a full instance on dedicated hardware. It's safe, secure, and resilient to noisy neighbors.

### Extended, modular building blocks

Supabase is a [modular platform](/docs/guides/getting-started/architecture#everything-works-in-isolation), offering a number of building blocks to extend Postgres. You get [AI/Vectors](/docs/guides/ai), [Auth](https://supabase.com/docs/guides/auth), [File Storage](/docs/guides/storage), [Realtime](/docs/guides/realtime), and [Edge Functions](/docs/guides/functions).

### Templates

The Vercel [template marketplace](https://vercel.com/templates?search=supabase) is one of our favorite features of the Vercel platform. With a single click you can provision an entire stack in under a minute, and connect it to a GitHub repo for further development. Try it now using our [starter template](https://vercel.com/templates/next.js/supabase).

### Low latency & Read replicas

Supabase runs in 16 different AWS regions, which means that you can choose to run your database as close to your Vercel Functions (and users) as possible. If you have users across the planet, check out [Read Replicas](https://supabase.com/docs/guides/platform/read-replicas).

### Integrated billing

With the new integration, everything is unified in your Vercel bill. All Supabase services will be visible in a single monthly invoice.

## Costs & Pricing

All services created through the Vercel integration are _exactly the same price_ that you'd get on the Supabase platform - including the **2 free databases** that we offer to all developers.

Supabase has [predictable pricing](/pricing) with [spend caps](/docs/guides/platform/cost-control) for developers who are worried about becoming [too successful](https://i.kym-cdn.com/photos/images/newsfeed/001/444/303/2b6.jpg) with their upcoming launch.

<Admonition>

At the time of publishing (28 August 2024), this integration is in Public Alpha. Check the [docs](/docs/guides/platform/vercel-marketplace) for the latest updates.

</Admonition>

## Try it out

The fastest way to get started is to try out the [**Supabase Starter**](https://vercel.com/templates/next.js/supabase) on the Vercel Template marketplace. With a few clicks you get a Next.js App Router template configured with cookie-based auth using Supabase, Postgres, TypeScript, and Tailwind CSS.

<div className="bg-surface-100 py-4 px-5 border rounded-md not-prose">
  <h5 className="text-foreground">Deploy a Next.js app with Supabase Vercel Storage now</h5>
  <p className="text-foreground-light mb-3">Uses the Next.js Supabase Starter Template</p>
  <a href="https://vercel.com/new/clone?demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-image=%2F%2Fimages.ctfassets.net%2Fe5382hct74si%2F7UG4Pvl9its0CqhrpX93n%2F262032f6e408308d3273f5883f369e97%2F68747470733a2f2f64656d6f2d6e6578746a732d776974682d73757061626173652e76657263656c2e6170702f6f70656e67726170682d696d6167652e70.png&demo-title=nextjs-with-supabase&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&stores=%5B%7B%22type%22%3A%22integration%22%2C%22integrationSlug%22%3A%22supabase%22%2C%22productSlug%22%3A%22supabase%22%7D%5D&teamSlug=vercel">
    <img src="https://vercel.com/button" alt="Deploy with Vercel" />
  </a>
</div>
