---
title: 'Cal.com launches Expert Marketplace built with Next.js and Supabase.'
description: Cal.com and Supabase team up to build an open-source platform starter kit for developers.
author: p6<PERSON>-<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,thor_schaeff
image: calcom_platform_starter/calcom_platform_starter_og.png
thumb: calcom_platform_starter/calcom_platform_starter_og.png
categories:
  - developers
tags:
  - open-source
  - partnerships
  - nextjs
date: '2024-06-18'
toc_depth: 2
---

<Admonition>

[Cal.com Platform Starter Kit is live on Product Hunt! Upvote now!](https://go.cal.com/producthunt)

</Admonition>

Contrary to popular belief, due to the [biggest online beef marketing campaign](https://www.linkedin.com/posts/rajiv-ayyangar_the-product-hunt-showdown-of-the-century-activity-7184589230231166977-zqlv?utm_source=share&utm_medium=member_desktop) in modern history, Cal.com and Supabase are actually supa good friends, united by a common mission to build open source software.

So when the Cal.com team reached out about collaborating on their new platform starter kit, we were excited to work together. Finally we could collaborate on a [Product Hunt launch](https://go.cal.com/producthunt) instead of competing against each other.

## What's the stack?

Initially the application was built to be run on SQLite. However, once requirements grew to include file storage, the Cal.com team remembered their Supabase frenemies and luckily, thanks to Prisma and Supabase, switching things over to Postgres three days before launch was a breeze.

## Prisma configuration for usage with Postgres on Supabase

When working with Prisma, your application will connect directly to your Postgres databases hosted on Supabase. To handle connection management efficiently, especially when working with serverless applications like Next.js, Supabase provides a connection pooler called [Supavisor](/blog/supavisor-1-million) to make sure your database runs efficiently with increasing traffic.

The configuration is specified in the `schema.prisma` file where you provide the following connection strings:

```ts schema.prisma
datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_PRISMA_URL")
  directUrl = env("POSTGRES_URL_NON_POOLING")
  schemas   = ["prisma"] // see multi-schema support below
}
```

This loads the relevant Supabase connections strings from your `.env` file

```bash .env
POSTGRES_PRISMA_URL="postgres://postgres.YOUR-PROJECT-REF:[YOUR-PASSWORD]@aws-0-[REGION].pooler.supabase.com:6543/postgres?pgbouncer=true&connection_limit=1" # Transaction Mode
POSTGRES_URL_NON_POOLING="postgres://postgres.YOUR-PROJECT-REF:[YOUR-PASSWORD]@aws-0-[REGION].pooler.supabase.com:5432/postgres"  # Session Mode
```

You can find the values in the [Database settings](/dashboard/project/_/settings/database) of your Supabase Dashboard.

For more details on using Prisma with Supabase, read the [official docs](/partners/integrations/prisma).

### Multischema support in Prisma

In Supabase the `public` schema is exposed via the autogenerated [PostgREST](/docs/guides/api) API, which allows you to connect with your database from any environment that speaks HTTPS using the [Supabase client libraries](/docs/guides/api/rest/client-libs) like [supabase-js](/docs/reference/javascript/introduction) for example.

Since Prisma connects directly to your database, it's advisable to put your data on a separate schema that is not exposed via the API.

We can do this by enabling `multischema` support in the `schema.prisma` file:

```ts schema.prisma
generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

model Account {
  id                String  @id @default(cuid())
  // ...

  @@schema("prisma")
}
```

## React Dropzone and Supabase Storage for profile image uploads

[Supabase Storage](/storage) is an S3 compatible cloud-based object store that allows you to store files securely. It is conveniently integrated with [Supabase Auth](https://supabase.com/auth) allowing you to easily limit access for uploads and downloads.

Cal.com's Platforms Starter Kit runs their authentication on Next.js' [Auth.js](https://authjs.dev/). Luckily though, Supabase Storage is supa flexible, allowing you to easily create signed upload URLs server-side to then upload assets from the client-side -- no matter which tech you choose to use for handling authentication for your app.

To facilitate this, we can create an API route in Next.js to generate these signed URLs:

```ts src/app/api/supabase/storage/route.ts
import { auth } from '@/auth'
import { env } from '@/env'
import { createClient } from '@supabase/supabase-js'

export const dynamic = 'force-dynamic' // defaults to auto
export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session || !session.user.id) {
      return new Response('Unauthorized', { status: 401 })
    }
    const {
      user: { id },
    } = session
    // Generate signed upload url to use on client.
    const supabaseAdmin = createClient(env.NEXT_PUBLIC_SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY)

    const { data, error } = await supabaseAdmin.storage
      .from('avatars')
      .createSignedUploadUrl(id, { upsert: true })
    console.log(error)
    if (error) throw error

    return new Response(JSON.stringify(data), {
      status: 200,
    })
  } catch (e) {
    console.error(e)
    return new Response('Internal Server Error', { status: 500 })
  }
}
```

The `createSignedUploadUrl` method returns a `token` which we can then use on the client-side to upload the file selected by [React Dropzone](https://react-dropzone.js.org/):

```tsx src/app/dashboard/settings/_components/supabase-react-dropzone.tsx
'use client'

import { env } from '@/env'
import { createClient } from '@supabase/supabase-js'
import Image from 'next/image'
import React, { useState } from 'react'
import { useDropzone } from 'react-dropzone'

export default function SupabaseReactDropzone({ userId }: { userId?: string } = {}) {
  const supabaseBrowserClient = createClient(
    env.NEXT_PUBLIC_SUPABASE_URL,
    env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  )
  const { acceptedFiles, fileRejections, getRootProps, getInputProps } = useDropzone({
    maxFiles: 1,
    accept: {
      'image/jpeg': [],
      'image/png': [],
    },
    onDropAccepted: async (acceptedFiles) => {
      setAvatar(null)
      console.log(acceptedFiles)
      const { path, token }: { path: string; token: string } = await fetch(
        '/api/supabase/storage'
      ).then((res) => res.json())

      const { data, error } = await supabaseBrowserClient.storage
        .from('avatars')
        .uploadToSignedUrl(path, token, acceptedFiles[0])
    },
  })

  return (
    <div className="mx-auto mt-4 grid w-full gap-2">
      <div {...getRootProps({ className: 'dropzone' })}>
        <input {...getInputProps()} />
        <p>Drag 'n' drop some files here, or click to select files</p>
        <em>(Only *.jpeg and *.png images will be accepted)</em>
      </div>
    </div>
  )
}
```

## Custom Next.js Image loader for Supabase Storage

Supabase Storage also conveniently integrates with the Next.js Image paradigm, by creating a [custom loader](https://supabase.com/docs/guides/storage/serving/image-transformations#nextjs-loader):

```ts src/lib/supabase-image-loader.ts
import { env } from '@/env'

export default function supabaseLoader({ src, width, quality }) {
  return `${env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${src}?width=${width}&quality=${quality || 75}`
}
```

Now we just need to register the custom loader in the `next.config.js` file:

```js next.config.js
images: {
  loader: "custom",
  loaderFile: "./src/lib/supabase-image-loader.ts",
},
```

and we can start using the Next.js Image component by simply providing the file path within Supabase Storage:

```tsx
<Image
  alt="Expert image"
  className="aspect-square rounded-md object-cover"
  src="your-bucket-name/image.png"
  height="64"
  width="64"
/>
```

## Supabase Vercel Integration for one-click deploys

Supabase also provides a [Vercel Integration]() which makes managing environment variables across branches and deploy previews a breeze. When you connect your Supabase project to your Vercel project, the integration will keep your environment variables in sync.

And when using the [Vercel Deploy Button](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fcalcom%2Fplatform-starter-kit%2Ftree%2Fmain&env=NEXT_PUBLIC_REFRESH_URL,AUTH_SECRET,AUTH_TRUST_HOST,NEXT_PUBLIC_CAL_OAUTH_CLIENT_ID,NEXT_PUBLIC_CAL_API_URL,CAL_SECRET&envDescription=You%20can%20see%20how%20to%20populate%20the%20environment%20variables%20in%20our%20starter%20example%20→&envLink=https%3A%2F%2Fgithub.com%2Fcalcom%2Fplatform-starter-kit%2Ftree%2Fmain%2F.env.example&project-name=cal-platform-starter&repository-name=cal-platform-starter&demo-title=Cal.com%20Experts&demo-description=A%20marketplace%20to%20book%20appointments%20with%20experts&demo-url=https%3A%2F%2Fexperts.cal.com&demo-image=https%3A%2F%2Fgithub.com%2Fcalcom%2Fplatform-starter-kit%2Fassets%2F8019099%2F2e58f8da-a110-4a45-b9a4-dcffb45f9baa&integration-ids=oac_VqOgBHqhEoFTPzGkPd7L0iH6&external-id=https%3A%2F%2Fgithub.com%2Fcalcom%2Fplatform-starter-kit%2Ftree%2Fmain) the integration will automatically create a new Supabase project for you, populate the environment variables, and even run the database migration and seed scripts, meaning you're up and running with a full end-to-end application in no time!

## Contributing to open source

Both Cal.com and Supabase are on a mission to create open source software, therefore this new platform starter kit is of course also open-source, allowing you to spin up your own marketplace with convenient scheduling in minutes! Of course this also means that you are very welcome to contribute additional features to the starter kit! You can [find the repository on GitHub](https://github.com/calcom/platform-starter-kit)!

## Resources

- [Cal.com Platform Starter Kit GitHub repository](https://github.com/calcom/platform-starter-kit)
- [Video explaining the platform starter kit](https://www.youtube.com/watch?v=wwo07ghiNn4)
- [Cal.com](https://cal.com/)
