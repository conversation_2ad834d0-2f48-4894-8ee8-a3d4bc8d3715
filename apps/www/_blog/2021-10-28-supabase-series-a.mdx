---
title: 'Supabase $30m Series A'
description: Supabase just raised $30M, bringing our total funding to $36M.
author: paul_copplestone
image: series-a/supabase-series-a.png
thumb: series-a/supabase-series-a.png
categories:
  - company
tags:
  - supabase
date: '2021-10-28'
toc_depth: 3
---

Supabase [just raised $30M](https://techcrunch.com/2021/09/09/supabase-raises-30m-for-its-open-source-insta-backend/),
bringing our total funding to $36M. How will we spend this? Read on to find out.

## We're growing fast

From the outside, you can see that we're growing fast.
To solidify some of the numbers with internal metrics:

### Database Growth

As of September, we've launched over 50,000 databases. This is _only_ on our hosted platform - it doesn't include our open source offering,
because we don't add telemetry. Active databases are growing 35% per month.

![Supabase has launched more than 50000 databases](/images/blog/series-a/total-databases.png)

### Developers

As of September, over 40,000 developers have signed up to Supabase from some of the world's leading companies.
Because of the versatility of Postgres, they're building everything from
[counter-fraud systems for Fintech](/blog/case-study-xendit) to
[digital platforms powering 200,000 users](/blog/mobbin-supabase-200000-users).

<ImageGrid
  smCols={3}
  mdCols={4}
  lgCols={4}
  images={[
    {
      name: 'wells-fargo',
      image: '/images/company/companies-using-supabase/wells-fargo.png',
    },
    {
      name: 'under-armour',
      image: '/images/company/companies-using-supabase/under-armour.png',
    },
    {
      name: 'audi-logo',
      image: '/images/company/companies-using-supabase/audi-logo.png',
    },
    {
      name: 'capitalone',
      image: '/images/company/companies-using-supabase/capitalone.png',
    },
    {
      name: 'coinbase',
      image: '/images/company/companies-using-supabase/coinbase.png',
    },
    {
      name: 'facebook',
      image: '/images/company/companies-using-supabase/facebook.png',
    },
    {
      name: 'github',
      image: '/images/company/companies-using-supabase/github.png',
    },
    {
      name: 'google',
      image: '/images/company/companies-using-supabase/google.png',
    },
    {
      name: 'gsk',
      image: '/images/company/companies-using-supabase/gsk.png',
    },
    {
      name: 'hewlett-packard',
      image: '/images/company/companies-using-supabase/hewlett-packard.png',
    },
    {
      name: 'hubspot',
      image: '/images/company/companies-using-supabase/hubspot.png',
    },
    {
      name: 'ibm',
      image: '/images/company/companies-using-supabase/ibm.png',
    },
    {
      name: 'instagram',
      image: '/images/company/companies-using-supabase/instagram.png',
    },
    {
      name: 'linkedin',
      image: '/images/company/companies-using-supabase/linkedin.png',
    },
    {
      name: 'microsoft',
      image: '/images/company/companies-using-supabase/microsoft.png',
    },
    {
      name: 'netflix',
      image: '/images/company/companies-using-supabase/netflix.png',
    },
    {
      name: 'notion',
      image: '/images/company/companies-using-supabase/notion.png',
    },
    {
      name: 'red-hat',
      image: '/images/company/companies-using-supabase/red-hat.png',
    },
    {
      name: 'robinhood',
      image: '/images/company/companies-using-supabase/robinhood.png',
    },
    {
      name: 'salesforce',
      image: '/images/company/companies-using-supabase/salesforce.png',
    },
    {
      name: 'santander',
      image: '/images/company/companies-using-supabase/santander.png',
    },
    {
      name: 'shopify',
      image: '/images/company/companies-using-supabase/shopify.png',
    },
    {
      name: 'squarespace',
      image: '/images/company/companies-using-supabase/squarespace.png',
    },
    {
      name: 'twitter',
      image: '/images/company/companies-using-supabase/twitter.png',
    },
  ]}
/>

### Community

Supabase has been in GitHub's top-15 fastest growing open source startups for
[five consecutive quarters](https://twitter.com/kiwicopple/status/1451104569266671619),
and our [Discord](https://discord.supabase.com) has grown to nearly 4000 members since launching just 3 months ago.
Our growth is all organic.

![Supabase Discord](/images/blog/series-a/discord-supabase-community.png)

## About the round

Supabase is an ambitious project. Firebase, to their credit, is a very well-developed platform which offers _a lot_ of functionality.
Building an alternative is a bit like launching five different startups at once.

That said, our approach has been clear from the start: we don't want to re-invent the wheel.
We want to leverage existing open source products wherever we can, improving them by up-streaming changes and employing maintainers
([PostgREST](/blog/supabase-steve-chavez),
[PostgreSQL](https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com)).
This model is the real promise of open source, and we've been lucky to have investors who have backed our approach
since the start - Y Combinator, Mozilla, and our lead investor, Coatue.

Because of their conviction in Supabase, Coatue doubled down on their Seed investment to lead our Series A. We're extremely excited to welcome
[Caryn Marooney](https://www.linkedin.com/in/caryn-marooney/) to our Board.

<Quote img="caryn-marooney.jpeg" caption="Caryn Marooney, Partner at Coatue.">
  <p>We are proud to lead a second consecutive round in Supabase and officially join the board.</p>
  <p>
    We continue to be impressed by Paul and Ant and it has been a pleasure partnering with them. We
    believe that the team has done an impressive job scaling their open-source community and think
    that the strong traction and adoption speaks for itself.
  </p>
</Quote>

### Joining the round

We've had a number of new investors join the round, this time a lot of operators and partners:

- Elad Gill
- Tom Preston-Werner (GitHub cofounder)
- Solomon Hykes (Docker cofounder)
- Alex Solomon (PagerDuty cofounder)
- Guillermo Rauch (Vercel founder)
- Kurt Mackey (Fly cofounder)
- Chris Nguyen (LogDNA cofounder)
- Tod Sacerdoti (Pipedream Founder)
- Alana Anderson (Base Case Capital)
- Astasia Myers (Quiet Capital)
- (and more)

<a href="/company#investors" target="_blank">
  See all investors
</a>

## Building Supabase

Our early positioning is an "open source Firebase alternative" but you might be surprised to learn that this is a small part of the Supabase vision.

Firebase is arguably one of the best tools in the world for building _new products_, but it has a flaw: scalability.

### Phase 1: Start with scalability

The term "open source Firebase alternative" is becoming popular now, and we are often asked: how are we different?

Simple. Supabase has smuggled scalability into the product, perhaps without you noticing.
Every Supabase project is a _full_ Postgres database. It's not "Postgres compatible", it's not "built on top of",
and we don't abstract it. If we did, your applications would face the same scalability issues as Firebase.

Supabase simply makes Postgres easy to use - hopefully easier than any other database platform you've ever used.

We plan to make Postgres the default database for every developer in the world. How do we do that? By making Postgres both easy
(Phase 2: tooling) and scalable (Phase 3: Cloud-Native Postgres).

### Phase 2: Postgres Tooling

User-facing applications usually require tools beyond just a database and frontend framework:

- **APIs:** to provide access to your database from untrusted systems.
- **Authentication:** for users sign ups.
- **File Storage:** for large images and media.

Supabase makes all of this easy. Beyond the database we offer [Auth](/docs/guides/auth),
RESTful and Realtime [APIs](/docs/guides/database/api), a [Storage](/docs/guides/storage)
system for large files, and a bunch of other tools - all wrapped up into a simple-to-use Dashboard.

![Supabase Dashboard](/images/blog/series-a/dashboard.png)

And this is where Supabase really differentiates itself: by doing less. All of these features build on top of PostgreSQL's
existing functionality.

- **Row Level Security:** restrict user access to data and files using Postgres Policies.
- **Realtime data streams:** subscribe to database changes using the logical replication stream.
- **Database Webhooks (previously called "Function Hooks"):** trigger external systems using Postgres triggers and our [async request extension](https://github.com/supabase/pg_net/).
- **RESTful APIs:** query your database [tables](/docs/reference/javascript/select) and [functions](/docs/reference/javascript/rpc) over HTTP, using [PostgREST](https://postgrest.org).

### Phase 3: Cloud-native PostgreSQL

PostgreSQL was created over 30 years ago, and it's one of the safest and most reliable databases in the world.

But modern developers are becoming accustomed to cloud-native services with several characteristics:

- Automatic scaling.
- Low latency anywhere in the world.
- Billing based on usage.

PostgreSQL is still catching up to the modern cloud environment in some areas. What does a Cloud-native PostgreSQL look like?

- **Branching:** developers should be able to "fork" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.
- **Scalable storage:** storage should grow and shrink without the user needing to provision more space themselves.
- **Distributed:** An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).
- **Ephemeral compute:** developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.
- **Snapshots and time-travel:** developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.

This is the future of Supabase - a platform to power your transactional workloads: no matter how big or small; no matter the use-case.
We're building a cloud-native Postgres platform.

## Join us

We have a track record of hiring open source maintainers, PostgREST is maintained by a
[Supabase developer](/blog/supabase-steve-chavez), one of our first hires over a year ago.
We've recently added [another developer](https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com)
with the explicit goal of working within the PostgreSQL community.

If you want to help us build the future of cloud-native Postgres, join us:
[https://about.supabase.com/careers/postgres-experts](https://about.supabase.com/careers/postgres-experts)

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
- Become a **[sponsor](https://github.com/sponsors/supabase)**
