---
title: How <PERSON> is using Supabase to accelerate development of AllPullTogether
description: <PERSON> is learning web development as he builds AllPullTogether, and <PERSON><PERSON><PERSON> is helping him accelerate what he can accomplish.
author: shanerice
image: all-pull-together/all-pull-together-thumb.png
thumb: all-pull-together/all-pull-together-thumb.png
categories:
  - developers
tags:
  - community
  - q-and-a
date: '2022-05-26'
toc_depth: 3
---

# How <PERSON> is using Supabase to accelerate development of AllPullTogether

There are epic stories about how Supabase helps our customers solve their big challenges. Today we want to put a spotlight on one of these stories from <PERSON> as he’s building AllPullTogether. <PERSON> shared this note with our team during an interaction with support:

<Quote img="allpulltogether-mike-lyndon.png" caption="<PERSON> of AllPullTogether">
  …if it weren't for Supabase, I'd probably still be talking about launching my app. Instead, I can
  zoom through the backend stuff and focus on improving my app.
</Quote>

A bit of background on <PERSON>, he’s an experienced visual effects artist who is building AllPullTogether to help digital artists make the asset review process tangible and accessible by reviewing 3D assets in a web browser. <PERSON> is new to web development — he started learning as his life shifted during the early days of the COVID pandemic.

I sat down with <PERSON> to chat about his experience learning web development and how <PERSON>pabase changed his approach on AllPullTogether

---

<Avatar img="shane-rice.png" caption="<PERSON>" />

Mike, you’re using Supabase on a project you’re working on and shared a little bit about your experience
with Supabase in a support ticket with our team. We loved hearing your story and I wanted to dive a little
deeper to see if there was something we could share that would be meaningful to the community.

_If you don't mind, go ahead and introduce yourself._

---

<Avatar img="allpulltogether-mike-lyndon.png" caption="Mike" />

Sure, I’m Mike Lyndon and I am a visual effects artist who turned to web development about a year ago.
I'm working on a 3D review app called AllPullTogether, which lets people upload their 3D assets and invite
art directors or other collaborators to review them.

I’m new to web development. I needed a way to deal with the backend side of things and I came across Supabase

---

<Avatar img="shane-rice.png" caption="Shane" />

Great, thanks Mike. You mentioned you're new to web development.

Can you share a little about your experience
as you've been working through learning and what led you to where you're at with AllPullTogether?

---

<Avatar img="allpulltogether-mike-lyndon.png" caption="Mike" />

Sure! It was part of being in quarantine for a month. I was like, “I need something to do.” I started
learning web development about a year ago. Just super basic stuff. Going through HTML, CSS and JavaScript
with a bunch of Treehouse video tutorials. As I slowly started progressing, I started incorporating the
stuff that I'm more familiar with, which is 3D.

Once I was through that learning phase, I started looking into how I could create a web app with 3D. That’s another layer of complexity in terms of all the different stacks that you can build on, and I was trying to learn all of them at the same time. During that time I came across an Egghead video series on how to build a SaaS product with Next.js, Supabase, and Stripe.

![AllPullTogether's tech stack](/images/blog/all-pull-together/all-pull-together-tech-stack.png)

The course gave me the blueprint I needed. I was like, “Wait a minute. I can do that! I can build that too.” I watched that and started implementing what I learned. That's when I realised that I might have an app on my hands that I can market as a product. Something relatively simple where users can upload their 3D assets and someone else can review it by annotating on top of the asset, which is how AllPullTogether started.

---

<Avatar img="shane-rice.png" caption="Shane" />

When we were talking before we started recording, one of the things you mentioned was through your learning
before AllPullTogether you were working on a different project to teach yourself backend tools.

_Can you talk a little about that and your experience and the difference between what you saw building everything from scratch and what you're able to do with Supabase?_

---

<Avatar img="allpulltogether-mike-lyndon.png" caption="Mike" />

At one point I was working on a health tracker app. Just as a personal project. I've done a fair amount
of Python scripting in the past, and I was using Flask to build out the backend.

I deliberately did everything manually and that included building out the databases, figuring out the schema, marshalling the data, Normalisation. It was a good way to learn the principles of database design and backend stuff, but I realised that I was moving at a pace that was relatively slow. It was also not something that I enjoyed doing. What I like doing is the frontend and user experience stuff. So when I saw the Supabase video training series, I was like, “Okay, let me give this a go.”

What I love about Supabase is I had something up and running in probably less than a day. It takes care of the [authorization](/auth). It let me quickly build out the tables. I learned about row level security, and how that can help separate out the data in terms of who can see what. I love the fact that Supabase is essentially all of these different pieces, all brought together.

It makes it much easier for me to build the entire app. If it weren't for Supabase, I'd probably still be talking about launching my app. Instead, I can zoom through the backend stuff and focus on improving my app.

---

<Avatar img="shane-rice.png" caption="Shane" />

Mike, that's great to hear. What you're sharing is a story that we hear often as solo devs find us. Maybe
they have another full-time job or are working on multiple projects, and they're able to multiply what
they can do because of what Supabase takes off their shoulders.\_

_Before we end our conversation, is there anything else you want to share about your app or your experience with the rest of the community?_

---

<Avatar img="allpulltogether-mike-lyndon.png" caption="Mike" />

I just want to add to that point. One of the things that’s growing in popularity for digital content creation is [multiplayer/real-time](https://multiplayer.dev/).
Developing a collaboration-focused app means this was something already on my todo list. I looked into
trying to do that myself - setting up the WebSockets and the servers - but when I saw Supabase now had
that as well, I thought, “Great, that's one less thing that I need to worry about.”

---

<Avatar img="shane-rice.png" caption="Shane" />

Great. Mike, I appreciate you taking the time and I'm looking forward to watching you build your app
and seeing it grow.\_

---

<Avatar img="allpulltogether-mike-lyndon.png" caption="Mike" />

Thanks.

---

Check out Mike's work on [AllPullTogether](https://we.allpulltogether.online) and follow him on [Twitter](https://twitter.com/mike_lyndon). And if you're not familiar with Supabase sign up and let us know what you're excited to build.
