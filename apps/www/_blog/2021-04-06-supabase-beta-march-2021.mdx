---
title: Supabase Beta March 2021
description: Launch week, Storage, Supabase CLI, Connection Pooling, Supabase UI, and Pricing.
author: paul_copplestone
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: march-2021/release-mar-2021.jpg
thumb: march-2021/release-mar-2021.jpg
categories:
  - product
tags:
  - release-notes
date: '2021-04-06'
video: https://www.youtube.com/v/TtLxxaYE1rA
---

Launch week, Storage, Supabase CLI, Connection Pooling, Supabase UI, and Pricing. Here's what we released last month.

### Quick demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/TtLxxaYE1rA"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Supabase Storage

Need to store images, audio, and video clips? Well now you can do it on [Supabase Storage](/blog/supabase-storage). It's backed by S3 and our new [OSS storage API](https://github.com/supabase/storage-api) written in Fastify and Typescript. Read the [full blog post](/blog/supabase-storage).

![Supabase Storage](/images/blog/storage/ph-1.png)

## Connection Pooling

The Supabase API already handles Connection Pooling, but if you're connecting to your database directly (for example, with Prisma) we now [bundle PgBouncer](/blog/supabase-pgbouncer). Read the [full blog post](/blog/supabase-pgbouncer).

![Connection Pooling](/images/blog/bouncer/pgbouncer-thumb.jpg)

## React UI Component Library

We open sourced our internal UI component library, so that anyone can use and contribute to the Supabase aesthetic. It lives at [ui.supabase.com](https://ui.supabase.com/) . It was also the #1 Product of the Day [on Product Hunt](https://www.producthunt.com/posts/supabase-ui).

![React UI Library](/images/blog/march-2021/supabase-ui.png)

## CLI

Now you can run Supabase locally in the terminal with `supabase start`. We have done some preliminary work on [diff-based schema migrations](/blog/supabase-cli#migrations), and added some new tooling for self-hosting Supabase with Docker. [Blog post here](/blog/supabase-cli).

![Supabase CLI](/images/blog/march-2021/supabase-cli.png)

## Pricing

Our most frequently asked question by far is "ok supabase is sweet, but how much is it going to cost?". TL;DR there's **Free Plan** up to 500mb + 10k auth users, a **Pro** **Plan** at $25/month for 8GB + 100k auth users, and anything additional is charged on a usage basis.

See [Pricing Page](/pricing) for full details and also our blog on why [pricing is hard](/blog/pricing).

![Supabase Pricing](/images/blog/march-2021/supabase-pricing.jpg)

## NFT Platform

Well... not really, but it made for a great [April Fools joke](/blog/supabase-nft-marketplace).

![Supabase NFT marketplace BuyMeth.com](/images/blog/nft/nft-1.png)

## Supabase Dot Com

We are now dot com! We'll be porting across over the next few weeks. Read the origin story behind the name [here](/blog/supabase-dot-com).

![Supabase Dot Com](/images/blog/supabase-dot-com-og.jpg)

## OAuth Scopes

Thanks to a comunity contribution ([@\_mateomorris](https://twitter.com/_mateomorris) and [@Beamanator](https://twitter.com/Beamanator)), Supabase Auth now includes OAuth scopes. These allow you to request elevated access during login. For example, you may want to request access to a list of Repositories when users log in with GitHub. Check out the [Documentation](/docs/reference/javascript/auth-signup#sign-up-with-scopes).

![Oauth Scope](/images/blog/march-2021/oauth-scopes.png)

## Kaizen

- You can now manage your PostgREST configuration inside the Dashboard.
- Our website has been redesigned. Check out our new [Homepage](/) and [Blog](/blog), and our new [Database](/database), [Auth](/auth), and [Storage](/storage) product pages.
- We refactored some of our Filter methods to make them even easier to use. Check out the [Full Text Search](/docs/reference/javascript/textsearch) refactor.
- We have added several new sections to our Docs including: [Local Dev](/docs/guides/local-development), [Self Hosting](/docs/guides/self-hosting), and [Postgres Reference](/docs/reference/postgres/getting-started) docs (all still under development).

## Community

- How to use Supabase inside Replit [[Video](https://www.youtube.com/watch?v=lQ5iIxaYduI)]
- [Connecting Draftbit to Supabase](https://docs.draftbit.com/docs/supabase) by [@amanhimself](https://twitter.com/amanhimself)
- [Creating Users in Next-js](https://t.co/IKcn3mgqW0?amp=1) by [@indigitalcolor](https://twitter.com/indigitalcolor)
- [A Backend for IndieHackers](https://drew.tech/supabase-a-backend-for-indiehackers) by [@dbredvick](https://twitter.com/DBredvick)
- The Firebase Alternative by [Simon Grimm](https://www.youtube.com/user/saimon1924) [[Video](https://www.youtube.com/watch?v=F6VyIXFQVtQ)]
- Ionic Integration by [Simon Grimm](https://www.youtube.com/user/saimon1924) [[Video](https://www.youtube.com/watch?v=pl9XfIWutKE)]
- Flutter Integration by [Aditya Thakur](https://www.youtube.com/channel/UChCAJNpMwoEUYCsE_eSyU4w) by [[Video](https://www.youtube.com/watch?v=fqfHEZvQPlY)]
- Svelte Integration by [Khaerunnisa Isnaeni](https://www.youtube.com/channel/UCqNDj6eQeTLHuEwgEHWOGjw) [[Video](https://www.youtube.com/watch?v=odPYzJJyEJI)]
- An [example app](https://github.com/supabase/supabase/tree/master/examples/user-management) for adding User Profiles to your Next.js app

![Supabase Stars march 2021](/images/blog/march-2021/supabase-stars-march-2021.png)

<small>
  Source: <a href="https://repository.surf/supabase">repository.surf/supabase</a>
</small>

If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).

## Coming Next

Lets say for each new sign up, you want to trigger a slack alert, send them an email after 24 hours.

For this we are working on our own [Workflow engine](/blog/supabase-workflows), it will eventually have a Zapier-like interface inside the dashboard. For now it's a state-machine interpreter (fully compatible with Amazon States Language) written in Elixir, [open source](https://github.com/supabase/workflows), and ready for community contributions.

![Workflows](/images/blog/workflows/workflows-thumb.jpg)

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Subscribe to our [YouTube channel](https://www.youtube.com/c/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
