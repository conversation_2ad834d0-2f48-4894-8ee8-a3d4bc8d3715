---
title: Supabase Beta December 2023
description: 'A Launch Week X rundown with all the fantastic goodies we shipped'
author: ant_wilson
image: beta-update-december-2023/monthly-update-december-2023.jpg
thumb: beta-update-december-2023/monthly-update-december-2023.jpg
categories:
  - product
tags:
  - release-notes
date: '2024-01-05'
toc_depth: 3
---

Happy New Year! We concluded 2023 with [Launch Week X](https://supabase.com/launch-week). Here's a rundown of all the fantastic goodies we shipped... now, let the building commence!

## Day 1 - Supabase Studio: AI Assistant and User Impersonation

![Day 1 - Supabase Studio: AI Assistant and User Impersonation](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/lw-digest-1.png)

Supabase Studio received a major update that reflects our commitment to a SQL-first approach and user-centric development. Awesome features like easy RLS policies with an AI assistant, Postgres Roles, User Impersonation, and much more.

- [Blog post](https://supabase.com/blog/studio-introducing-assistant)
- [Video announcement](https://www.youtube.com/watch?v=hu2SQjvCXIw)

Day 2 - Edge Functions: Node and native npm compatibility

![Day 2 - Edge Functions: Node and native npm compatibility](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/lw-digest%202.png)

Edge Functions now natively supports npm modules and Node built-in APIs. You can directly import millions of popular, commonly used npm modules into your Edge Functions.

- [Blog post](https://supabase.com/blog/edge-functions-node-npm)
- [Video announcement](https://www.youtube.com/watch?v=eCbiywoDORw)

## Day 3 - Supabase Branching

![Day 3 - Supabase Branching](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/lw-digest-3.png)

A Postgres database for every GitHub branch 🤝. Database branching means you can have separate database instances for each feature of your application.

- [Blog post](https://supabase.com/blog/supabase-branching)
- [Video announcement](https://www.youtube.com/watch?v=peXKxavGnBo)

## Day 4 - Supabase Auth: Identity Linking, Hooks, and HaveIBeenPwned integration

![Day 4 - Supabase Auth: Identity Linking, Hooks, and HaveIBeenPwned integration](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/lw-digest-4.png)

We announced several new features for Supabase Auth: Identity Linking, Session Control, Leaked Password Protection, and Auth Hooks with Postgres functions.

- [Blog post](https://supabase.com/blog/supabase-auth-identity-linking-hooks)
- [Video announcement](https://www.youtube.com/watch?v=LF8GABnAFyE)

## Day 5 - Introducing Read Replicas

![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/lw-digest-5.png)

This is a huge one for anyone wanting to serve data closer to the users or distribute loads across multiple databases. Learn how we implemented Read Replicas and how to use them in your projects.

- [Blog post](https://supabase.com/blog/introducing-read-replicas)
- [Video announcement](https://www.youtube.com/watch?v=PX3R1fXjJ2M)

## Launch Week X Hackathon Winners

![Launch Week X Hackathon Winners](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/launch-week-x-hackathon-winners.png)

Going through all 64 projects was cool, the most submissions we've ever had. Picking a winner was a bit tricky though, the quality was impressive.

In the end, we chose [Supafork](https://github.com/chroxify/supafork) as the winner of the Best Overall project 👏. Created by [Christo](https://twitter.com/0xChroxify), SupaFork is inspired by Vercel's Deploy button and it allows an easier setup for self-hosted apps using Supabase.

[Full list of winners](https://supabase.com/blog/launch-week-x-hackathon-winners) | [All the submissions](https://www.madewithsupabase.com/hackathons/launch-week-x)

## Moarrr LWX

As if all that wasn't enough, we shipped even more cool stuff:

- [Fly Postgres, managed by Supabase](https://supabase.com/blog/postgres-on-fly-by-supabase)
- [Supabase Libraries V2: Python, Swift, Kotlin, Flutter, and Typescript](https://supabase.com/blog/client-libraries-v2)
- [Supavisor 1.0: a scalable connection pooler for Postgres](https://supabase.com/blog/supavisor-postgres-connection-pooler)
- [Postgres Language Server: implementing the Parser](https://supabase.com/blog/postgres-language-server-implementing-parser)
- [Supabase Grafana](https://github.com/supabase/supabase-grafana)
- [Supabase Wrappers v0.2: Query Pushdown & Remote Subqueries](https://supabase.com/blog/supabase-wrappers-v02)
- [PostgREST 12](https://supabase.com/blog/postgrest-12)
- [pg_graphql: Postgres functions now supported](https://supabase.com/blog/pg-graphql-postgres-functions)
- [The Supabase Album](https://www.youtube.com/watch?v=r1POD-IdG-I)
- [How design works at Supabase](https://supabase.com/blog/how-design-works-at-supabase)
- [Master thread with all our traditional Twitter Spaces](https://twitter.com/supabase/status/1734190040413061583)
- [Top 10 Launches of LWX (plus cool things from the community](https://supabase.com/blog/launch-week-x-best-launches)

## Extended community highlights

![Community Highlights](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/november2023/nov9.png)

- Supabase Security with our Head of Product and Engineering. [[Podcast](https://softwareengineeringdaily.com/2023/12/20/supabase-security-with-inian-parameshwaran/)]
- Creating a RAG chatbot with Supabase, OpenAI, Python & Langchain. [[Tutorial](https://ayeshasahar.hashnode.dev/creating-a-rag-chatbot-with-supabase-openai-python-langchain)]
- Building an AI powered WebGL experience with Supabase and React Three Fiber. [[Tutorial](https://dev.to/laznic/building-an-ai-powered-webgl-experience-with-supabase-and-react-three-fiber-3dfn)]
- Connect your Streamlit apps to Supabase. [[Tutorial](https://blog.streamlit.io/connect-your-streamlit-apps-to-supabase/)]
- Your Complete Guide To Next.js Authentication With Supabase. [[Video](https://www.youtube.com/watch?v=dhXjHGklaZc)]
- Supabase Launch Week X with Jon Meyers. [[Podcast](https://podrocket.logrocket.com/supabase-launch-week-x-jon-meyers)]
- Integrating Comments in a Next.js Blog with Supabase and Slack. [[Article](https://prismic.io/blog/nextjs-supabase-tutorial)]
- Exploring Supabase Realtime By Building a Game. [[Tutorial](https://www.aleksandra.codes/supabase-game)]
- The Commit Podcast, Ep 3 - Supabase, Postgres, AI, and Launch Weeks with Paul Copplestone. [[Podcast](https://www.youtube.com/watch?v=hLaKO1MIhNI)]
- Logging with Python and Supabase: A Practical Guide. [[Article](https://medium.com/@goncalo.baiao/logging-with-python-and-supabase-a-practical-guide-6b541c453073)]
- How I build my real-time voting app with Next.js and Supabase ⚡️. [[Video](https://www.youtube.com/watch?v=syrjdkU5POo)]
- Multi-User Note App with Supabase Realtime. [[Article](https://blog.kirillinoz.com/multi-user-note-app-with-supabase-realtime)]

## We’re Hiring

Come join one of the fastest-growing open source projects ever 🤗

- [Customer Solution Architect](https://boards.greenhouse.io/supabase/jobs/5027144004)
- [Platform Engineer (Postgres team)](https://boards.greenhouse.io/supabase/jobs/5011466004)
- [Support Engineers across all timezones](https://supabase.com/careers#positions)

## ⚠️ Baking hot meme zone ⚠️

And you just wait for LW11

![Launch Week X Meme](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/dec2023/lwx%20meme.png)

See you next month!
