---
title: Supabase Beta October 2021
description: Three new Auth providers, multi-schema support, and we're gearing up for another Launch Week.
author: paul_copplestone
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: 2021-oct/release-oct-2021.jpg
thumb: 2021-oct/release-oct-2021-cover.jpg
categories:
  - product
tags:
  - release-notes
date: '2021-11-07'
toc_depth: 3
video: https://www.youtube.com/v/yL5WbAKAKjE
---

We released three new Auth providers, multi-schema support, and we're gearing up for another Launch Week.

### Quick recap

Watch a recap of this month's release.

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/yL5WbAKAKjE"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Slack, Spotify, and MessageBird logins

Thanks to [`@HarryET`](https://github.com/supabase/gotrue/pull/245) and our friends at [MessageBird](https://github.com/supabase/gotrue/pull/210)
we have 3 new Auth providers this month:
[Slack](https://supabase.com/docs/guides/auth/social-login/auth-slack),
[Spotify](https://supabase.com/docs/guides/auth/social-login/auth-spotify), and
[MessageBird](https://supabase.com/docs/guides/auth/phone-login/messagebird) phone logins.

![Supabase Auth: Login with Slack](/images/blog/2021-oct/supabase-auth-slack.png)

## Multi-schema support

### Dashboard

Browse data in any database schema using the Schema switcher in the Dashboard.

![Multi-schema support](/images/blog/2021-oct/multi-schema.png)

### API

You can access any schema with your API, after enabling access in the Dashboard.
[Docs](https://supabase.com/docs/reference/javascript/initializing#api-schemas).

![Multi-schema support (API)](/images/blog/2021-oct/supabase-multi-schema-support.png)

## Fresh Docs and Guides

We have a TON of new guides, with videos too.

### Database Functions

Learn about PostgreSQL Functions. [Docs](https://supabase.com/docs/guides/database/functions).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/MJZCCpCYEqk"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Auth Overview

Learn about all the exciting features of Auth within Supabase. [Docs](https://supabase.com/docs/guides/auth).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/6ow_jW4epf8"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### API Features

Learn more about the power of PostgREST for RESTful APIs. [Docs](https://supabase.com/docs/guides/database/api#restful-api).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/rPAJJFdtPw0"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### And more

- Redwood Quickstart: [https://supabase.com/docs/guides/with-redwoodjs](https://supabase.com/docs/guides/with-redwoodjs)
- Expanded Self-hosting: [https://supabase.com/docs/guides/hosting/overview](https://supabase.com/docs/guides/hosting/overview)
- Expanded Auth Reference docs with [serverside functions](https://supabase.com/docs/reference/javascript/auth-api-deleteuser).
- "Before you launch" checklist: [https://supabase.com/docs/going-into-prod](https://supabase.com/docs/going-into-prod)

!["Before you launch" checklist](/images/blog/2021-oct/launch-ckecklist.png)

## Community

There was a lot of activity this month.

### Supabase at Jamstack conf

Supabase attended the Jamstack conf. Watch us catch up with Matt, the cofounder of Netlify (minute 8).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/phC14xfwvjc"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Supabase at Next.js conf

And [Jon](https://twitter.com/_dijonmusters) made a guest appearance at this year's amazing [Next.js Conf](https://nextjs.org/conf).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/GpXEMB1pDRE"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Community Highlights

- Vue 3 | Workout Tracker App - John Komarnicki [video](https://www.youtube.com/watch?v=3tF0fGkd4ho)
- Adalo + Supabase - Flywheel Media [video](https://www.youtube.com/watch?v=YGP1LXctLk4)
- Nuxt 3 Beta + Supabase - BenCodeZen [video](https://www.youtube.com/watch?v=5vB120atiaU)
- Made With Supabase (now on Nuxt 3) - Zernonia [site](https://www.madewithsupabase.com/)
- Nuxt 3 + Tailwind + Supabase - Ekene Eze [video](https://www.youtube.com/watch?v=xbE11CfZpNQ)
- SQL Functions - Răzvan Stătescu [article](https://dev.to/razvanstatescu/how-to-run-custom-sql-queries-using-functions-in-supabase-2nna)
- `supabase-py v0.0.3` released - [repo](https://github.com/supabase-community/supabase-py/releases/tag/v0.0.3)
- `nuxt-supabase v2.2.1` released - [repo](https://github.com/supabase-community/nuxt-supabase)
- `vue-supabase v2.2.3` released - [repo](https://github.com/supabase-community/vue-supabase)

### GitHub

We hit 20K stars!! 21,268 to be exact: [github.com/supabase/supabase](http://github.com/supabase/supabase)

![GitHub](/images/blog/2021-oct/stars.png)

Source: [repository.surf/supabase](https://repository.surf/supabase)

Check out some of our other community stats in our latest [Series A Blog Post](/blog/supabase-series-a).

## Coming Next: Launch Week III

We had Launch Week [numero uno](https://supabase.com/blog/launch-week) in March, and the sequel "[Launch Week II: the SQL](https://supabase.com/blog/supabase-launch-week-sql)" in July.

Now we're going even bigger with the third installment: `Launch Week III: The Trilogy`. Join us on 29th November on our [Discord](https://discord.supabase.com).

![Launch Week III](/images/blog/2021-oct/og-launchweek-3.jpg)

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
- Become a **[sponsor](https://github.com/sponsors/supabase)**
