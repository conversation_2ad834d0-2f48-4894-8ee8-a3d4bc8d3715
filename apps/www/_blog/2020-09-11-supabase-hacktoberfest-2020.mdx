---
title: Supabase Hacktoberfest 2020
description: Join us for a celebration of open source software and learn how to contribute to Supabase.
author: thor_schaeff
author_title: Supabase community contributor
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: hacktoberfest.png
categories:
  - developers
tags:
  - supabase
  - hacktoberfest
date: '09-11-2020'
---

October is looming, and for open source communities and contributors this means one thing: [Hacktoberfest](https://hacktoberfest.digitalocean.com/).

## What is Hacktoberfest?

Hacktoberfest is a time to contribute to open source, improve your skills, and build greater technology in the process. Last year during Hacktoberfest 483,127 pull requests were opened and 61,871 people completed the challenge.

To do our part, Supabase has planned a couple of events and activities throughout Hacktober, and we'd love for you to join us!

## How do I get started?

It's simple, just start contributing to **any** open source project. If you've never contributed to open source, don't worry! Here are some resources to get you started:

- [How to write your first pull request](https://pages.news.digitalocean.com/n/Y0VKC0qX000hY0236IE0qDn)
- [How to create a good commit message](https://pages.news.digitalocean.com/n/wIYDXKVE600000nr20h3r0C)
- [Beginner resources for first-timers](https://pages.news.digitalocean.com/n/kE2b60X00B003IVhYn0cK0D)
- [Hacktoberfest’s quality standards](https://hacktoberfest.digitalocean.com/details/#quality)

## Supabase events

Tune in to our online events.

### GitHub Open Source Friday live stream

- What: [@kiwicopple](https://github.com/kiwicopple) chats with the GitHub team about open source.
- When: Fri, Sep 25, 11:00 AM (Thu, Sep 24, 8:00 PM PT)
- Where: [GitHub Twitch channel](https://www.twitch.tv/github)

### Supabase Hacktoberfest Online Meetup

- What: Join us to learn more about Supabase and how to contribute during Hacktoberfest.
- When: Fri, Oct 2, 9:00 AM SG time (Thu, Oct 1, 6:00 PM PT)
- Where: [YouTube](https://youtu.be/3_xRLTjvEiE) | [Please RSVP here](https://organize.mlh.io/participants/events/4291-hacktoberfest-supabase-meetup)

## Contribute to Supabase and win a Hacktoberfest T-Shirt

We've created a [GitHub project board](https://github.com/orgs/supabase/projects/5) to keep track of what's happening and can enjoy collaborating and hacking together 🥳

You can contribute to one of the existing `hacktoberfest` issue, or you can grab one of the ideas from the notes in the project board and [open a new issue](https://github.com/supabase/supabase/issues/choose) for it. Make sure to tag the newly created issue with the `hacktoberfest` label. This will be important for you to have a chance at getting the limited edition Hacktoberfest T-Shirt. See the [participation rules](https://hacktoberfest.digitalocean.com/details/#rules) for more details.

### I'm a Developer

Awesome, we've got a selection of small and larger [engineering tasks](https://github.com/orgs/supabase/projects/5#column-10773067). From creating examples for Blitz.js and Redwood.js to Postgres templates for various use cases.

### I'm a Designer

Nice, we would love [your help](https://github.com/orgs/supabase/projects/5#column-10773073) with UX improvements, illustrations, loading pages, or [lottie animations](https://lottiefiles.com/).

### I'm a Writer

Yay, we'd love your help with [improving and adding docs](https://github.com/orgs/supabase/projects/5#column-10773075)!

## Happy hacking

We'd love to see you at one of our events and have you contribute to Supabase throughout Hacktoberfest! See you on the interweb 🙂

## Questions / ideas / feedback?

We'd love to hear it, just [tweet at us](https://twitter.com/supabase), or [open an issue](https://github.com/supabase/supabase/issues/choose).
