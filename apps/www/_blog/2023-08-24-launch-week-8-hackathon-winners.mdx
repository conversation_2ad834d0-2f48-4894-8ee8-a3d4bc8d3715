---
title: 'Launch Week 8 Hackathon Winners'
description: Announcing the winners of the Launch Week 8 Hackathon!
author: and<PERSON>_smith,saltcod,tyler_shukert
image: lw8-hackathon-winners/hackathon-winners.png
thumb: lw8-hackathon-winners/hackathon-winners.png
categories:
  - developers
tags:
  - hackathon
date: '2023-08-24'
toc_depth: 2
---

[Launch Week 8](https://supabase.com/launch-week) passed by, but we still have our favorite part left, celebrating the winners of the hackathon! We've had some exceptional results and the quality of these apps are out of this world. You can view all the submissions on [madewithsupabase.com](https://www.madewithsupabase.com/).

And now, without further ado, congratulations to the winners of the Supabase Launch Week 8 Hackathon!

## Best Overall Project

### Winner

[WITAS - Wait is that a sticker?](https://github.com/alex-streza/witas) - by [@alex_streza](https://twitter.com/alex_streza) and [@<PERSON>_Melnic](https://twitter.com/Catalina_Melnic)

"WITAS", an acronym for Wait is that sticker. In a nutshell, it generates stickers with Midjourney.

![WITAS - Wait is that a sticker?](/images/blog/lw8-hackathon-winners/lw8-hackathon-3.jpg)

### Runner Up

[Stumble - Agree with someone](https://github.com/devkrafts/Stumble-more-like-bumble) - by [@ayushjn\_](https://twitter.com/ayushjn_) and [@anushka\_\_shukla](https://twitter.com/anushka__shukla)

"Stumble" lets anyone decide and agree upon something be it a place to eat, a place to hang out, or some activity. Any 2 people can create a link, join a room, and start swiping, the Bumble way until they swipe the same place or activity. This helps in deciding things a lot faster.

![Stumble - Agree with someone](/images/blog/lw8-hackathon-winners/lw8-hackathon-9.jpg)

## Best use of AI

### Winner

[Tweets](https://github.com/Steellgold/tweeets) - by [@Steellgold](https://twitter.com/Steellgold)

Generate tweets using AI, based on your own tweets or your own parameters. Share your tweets models with your friends and the world.

![Tweets](/images/blog/lw8-hackathon-winners/lw8-hackathon.jpg)

### Runner Up

[AI COLOR: colorize photo with AI](https://github.com/EyuCoder/aicolor) - by [@eyucoder](https://twitter.com/eyucoder)

If you're looking to colorize old black and white photos, this AI photo colorizer app can help you bring your memories back to life.

![AI COLOR: colorize photo with AI](/images/blog/lw8-hackathon-winners/lw8-hackathon-4.jpg)

## Most fun / best easter egg

### Winner

[Supafactor: Does your README have it?](https://github.com/danryland/supafactor) - by [@RealDanRyland](https://twitter.com/realdanryland)

Drawing inspiration from Rick and Morty's vibrant visual style and merging it with the excitement of talent shows, I present to you "Supafactor" - a pioneering method for evaluating hackathon entries.

![Supafactor: Does your README have it?](/images/blog/lw8-hackathon-winners/lw8-hackathon-6.jpg)

### Runner Up

[BeerBet](https://github.com/ChouquetteCorp/beerbet) - by [@BilelJegham](https://twitter.com/BilelJegham) and [@PolThm](https://github.com/PolThm)

Experience a whole new way to enjoy your favorite matches with our sports betting app centered around fun and beers, without any money wagered.

![BeerBet](/images/blog/lw8-hackathon-winners/lw8-hackathon-7.jpg)

## Most technically impressive

### Winner

[Openbin](https://github.com/ethndotsh/openbin) - by [@ethndotsh](https://github.com/ethndotsh), [@jack\_\_merrill](https://twitter.com/jack__merrill), [@mathislajs](https://twitter.com/mathislajs) and [@LavenderFoxxo](https://github.com/LavenderFoxxo)

"Openbin" is a Pastebin clone built for command line warriors, with support for both a speedy and accessible Monaco web editor, as well as an efficient Go CLI.

![Openbin](/images/blog/lw8-hackathon-winners/lw8-hackathon-1.jpg)

### Runner Up

[Supalytic](https://github.com/suciptoid/supalytic) - by [@suciptodev](https://twitter.com/suciptodev) and [@andreecy](https://github.com/andreecy)

Supalytic is a website analytic tracking alternative to Google Analytics. It is built with SvelteKit, Supabase for auth and database and deployed on Cloudflare pages.

![Supalytic](/images/blog/lw8-hackathon-winners/lw8-hackathon-5.jpg)

## Most visually pleasing

### Winner

[Doors of Abyss: The Endless Chase](https://github.com/laznic/doors-of-abyss/) - by [@laznic](https://twitter.com/laznic)

"Doors of Abyss: The Endless Chase" is an interactive browser game featuring an original storyline that underwent AI enhancement to refine its language (as the initial creation was during an amateur phase). The game also incorporates AI-generated images, courtesy of Midjourney and Stable Diffusion.

![Doors of Abyss: The Endless Chase](/images/blog/lw8-hackathon-winners/lw8-hackathon-8.jpg)

### Runner Up

[Supadraw - Draw like an artist](https://github.com/arbizen/supadraw) - by [@arbizzen](https://twitter.com/arbizzen)

"Supadraw - Draw like an artist" is an online drawing tool to create drawing and share with other people. Like a drawing? Well there's a like button for you to show respect to your favorite artist. Want to bookmark a drawing? None's stopping you. Go ahead and bookmark your favorite drawings.

![Supadraw - Draw like an artist](/images/blog/lw8-hackathon-winners/lw8-hackathon-2.jpg)

## The Prizes

As promised in the [hackathon announcement article](https://supabase.com/blog/supabase-lw8-hackathon) the winners and runner-ups in each category will receive a limited edition Launch Week 8 swag kit.

![swag](/images/blog/lw8-hackathon/swag.png)

## Get Started Guides

If you're inspired to build, check out some of the latest resources:

- **[Quick Start Guides](https://supabase.com/docs/guides/getting-started)**
- **[Supabase Storage Guide](https://supabase.com/docs/guides/storage)**
- **[Edge Functions Guide](https://supabase.com/docs/guides/functions)**

## More Launch Week 8

- [Day 1 - Hugging Face is now supported in Supabase](/blog/hugging-face-supabase)
- [Day 2 - Supabase Local Dev: migrations, branching, and observability](/blog/supabase-local-dev)
- [Day 3 - Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers](/blog/supabase-studio-3-0)
- [Day 4 - Supabase Integrations Marketplace](/blog/supabase-integrations-marketplace) | [Vercel Integration and Next.js App Router Support](/blog/using-supabase-with-vercel)
- [Day 5 - Supavisor: Scaling Postgres to 1 Million Connections](/blog/supavisor-1-million)
