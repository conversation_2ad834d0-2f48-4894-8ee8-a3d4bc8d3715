---
title: Supabase Beta March 2023
description: We are in full shipping mode 🛥️… Launch Week 7 can’t come quickly enough!
author: ant_wilson
image: 2023-04-08-supabase-beta-update-march/monthly-update-march-2023.jpg
thumb: 2023-04-08-supabase-beta-update-march/monthly-update-march-2023.jpg
categories:
  - product
tags:
  - release-notes
date: '2023-04-08'
toc_depth: 3
---

We are in full shipping mode 🛥️… Launch Week 7 can’t come quickly enough! We have so much stuff that we may have to start earlier, so stay tuned to HackerNews on Sunday.

## Launch Week 7 starts Monday

![Launch Week 7 starts Monday](/images/blog/2023-04-08-supabase-beta-update-march/supabase-launch-week-7.jpg)

Buckle up! We're gearing up to ship new features that you can use to build faster and better apps. There’s still time to generate your ticket and win an LW7 Mechanical Keyboard (and more cool swag).

[Get your ticket](https://supabase.com/launch-week)

## The Supabase AI Hackathon

![Launch Week 7 Supa-AI-Easter-Hackathon](/images/blog/2023-04-08-supabase-beta-update-march/hackathon-launch-week-7.jpg)

Launch Week is about to start and, as usual, we're running an async hackathon alongside it. This time it is Web3 AI-themed. It officially starts now and ends Monday 16th April at 11:59 pm. You can win Supabase keyboards and limited edition Supabase swag!

[All the info](https://supabase.com/blog/launch-week-7-hackathon)

## Next.js OpenAI doc search template

![Next.js OpenAI doc search template](/images/blog/2023-04-08-supabase-beta-update-march/nextjs-openai-doc-search-template.jpg)

After testing this AI stack extensively for our own docs, we're excited to release it as a template for y'all! Includes: Next.js app hosted on Vercel, OpenAI GPT-3 text completion, Postgres DB with pgvector, and Deno Edge Functions.

[Build your own](https://github.com/supabase-community/nextjs-openai-doc-search)

## Unveiling the creation process of our ticket system

![Unveiling the creation process of our ticket system](/images/blog/2023-04-08-supabase-beta-update-march/designing-the-new-ticket-system.jpg)

The ticket system has become an essential component of the Launch Week experience. To make each experience unique, we strive to add a twist. This time, we came up with an AI-fueled design and an infinite scroll of tickets to showcase the community. We documented everything in two blog posts:

- [Infinite scroll with Next.js, Framer Motion, and Supabase](https://supabase.com/blog/infinite-scroll-with-nextjs-framer-motion)
- [Designing with AI: Generating unique artwork for every user](https://supabase.com/blog/designing-with-ai-midjourney)

## SupaClub

![SupaClub](/images/blog/2023-04-08-supabase-beta-update-march/supaclub.jpg)

On April 1st, we announced a new product: SupaClub (pronounced “Super-Clurb”), the world’s first Software Engineering Nightclub. Fyre Fest investor Ja Rule and “Super Bass” songstress Niki Minaj will perform on the opening night, which is now sold out.

[Welcome to the SupaClub](https://supabase.com/blog/supaclub)

## Twitter Space: Launch Week 7 - Kickoff 🚀

After Monday's announcement, we are kicking off LW7 with a very cool Twitter Space, featuring the DevRel team and guests. Bring your questions!

8:00 AM PT | 11:00 AM ET | 4:00 PM UKI

[Set reminder](https://twitter.com/i/spaces/1OwxWwEpaRAxQ)

## Made with Supabase

![Made with Supabase - Markpropmt](/images/blog/2023-04-08-supabase-beta-update-march/made-with-supabase-markprompt.png)

[Markprompt](https://markprompt.com/) | A platform for building GPT-powered prompts. It scans Markdown, Markdoc, and MDX files in your GitHub repo and creates embeddings that you can use to create a prompt. Built with Next.js, Vercel, Open AI, and Supabase by the [Motif team](https://motif.land/).

## Extended Community Highlights

![Community](/images/blog/2022-june/community.jpg)

- Supabase hybrid search (`pgvector` + Full-Text search) with LangChain JS. [[Doc]](https://js.langchain.com/docs/modules/indexes/retrievers/supabase_hybrid)
- Supabase Crash Course For .NET Developers: Building an API From Scratch. [[Video]](https://www.youtube.com/watch?v=uviVTDtYeeE)
- Deno SaaSKit: an open-source SaaS template built with Fresh. [[Blog Post](https://deno.com/blog/announcing-deno-saaskit)]
- Mastering Nuxt: Setting Up Supabase Auth With Nuxt 3. [[Article](https://masteringnuxt.com/blog/setting-up-supabase-auth-with-nuxt-3)]
- Read data from Supabase and plot it in real-time on a Gradio Dashboard. [[Guide](https://gradio.app/creating-a-dashboard-from-supabase-data/)]
- The story behind Alive and kicking. [[Video](https://www.youtube.com/watch?v=hhPiTREYobI)]
- Semantic Search with Supabase and OpenAI. [Video](https://www.youtube.com/watch?v=1dWfQLHUnVU)
- ChatGPT with SvelteKit Forms & Supabase 🤖 Emoji App 🔴 LIVE Coding & Chill. [Video](https://www.youtube.com/watch?v=-hmYEcUaKUw)
- Deploy a React App on App Platform with Self Hosted Supabase. [Tutorial](https://docs.digitalocean.com/developer-center/deploy-a-react-app-on-app-platform-with-self-hosted-supabase/)
- Exploring Supabase's Postgres-powered features for GoLang applications. [Blog Post](https://dev.to/acethecloud/exploring-supabases-postgres-powered-features-for-golang-applications-ldb)
- How to build auth with Next.js App Router and Supabase Auth. [Tutorial](https://www.neorepo.com/blog/how-to-build-auth-with-nextjs-app-router-and-supabase-auth)
- 8 best open-source low-code platforms. [Blog post](https://blog.n8n.io/open-source-low-code-platforms/)
- How to Upload Images to a Supabase Storage Bucket From a Next.js App. [Tutorial](https://www.makeuseof.com/next-js-upload-images-supabase-storage-bucket)

## Meme Zone

React be like...

![Supabase Beta Update Meme](/images/blog/2023-04-08-supabase-beta-update-march/beta-update-march-2023-meme.jpeg)

As always, one of our favorite memes from last month. [Follow us on Twitter](https://twitter.com/supabase) for more.

See you next month!
