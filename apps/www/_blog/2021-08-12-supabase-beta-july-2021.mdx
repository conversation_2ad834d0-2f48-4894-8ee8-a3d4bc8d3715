---
title: Supabase Beta July 2021
description: <PERSON><PERSON>, Vercel Integration, Full text search, and OAuth guides.
author: ant_wilson
author_url: https://github.com/awalias
author_image_url: https://github.com/awalias.png
image: 2021-july/release-july-2021.jpg
thumb: 2021-july/release-july-2021-cover.jpg
categories:
  - product
tags:
  - release-notes
date: '2021-08-12'
toc_depth: 2
video: https://www.youtube.com/v/Vj5fPA-vjfw
---

July ended with our second "Launch Week", where we shipped something every day for a week. There's a lot to get through this time!

### Quick demo

Watch a full demo of this month's releases.

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/Vj5fPA-vjfw"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Launch Week II: The SQL

Following the success of our first Launch Week in March, we finished July with "[Launch Week II: The SQL](/blog/supabase-launch-week-sql)".
The community has been sieving through a slew of bad puns and retro memes to discover the new feature announcements.

![Launch Week II: The SQL](/images/blog/2021-july/supabase-launch-the-sql.png)

## Auth v2 with Phone Auth

Your users can now log in with SMS based mobile auth! We have a Twilio integration ([Guide Here](/docs/guides/auth/phone-login/twilio)) and will be adding more providers soon.

Other Auth updating include, Twitch logins, and the ability to generate invite, recovery, confirmation, and magic links via the API,
for people who want more control over the email templating flow. Read the [blog post here](/blog/supabase-auth-passwordless-sms-login).

![Auth v2 with Phone Auth](/images/blog/2021-july/verify-phone.jpg)

## Storage is now in Beta

Storage updates include Media Streaming, Public Buckets, Directory Uploads, and a Performance Improvements.

Streaming Media in particular opens up a whole new host of potential use cases, learn more about the [updates here](/blog/storage-beta).

![Auth v2 with Phone Auth](/images/blog/2021-july/storage.png)

## Dashboard v2

We made some major new additions to the dashboard including usage statistics, a new project home, and tons of database insights.
Check the [post here](/blog/supabase-reports-and-metrics) on what you get and how we built it.

![Dashboard v2](/images/blog/2021-july/dashboards.png)

## We launched a Discord server

You'll find us hanging out regularly in the #hangout channel.
We even "live-fixed" some production errors in there on Monday night (which occurred literally 1 hour before our first announcement of the week! Typical!).
We're fast approaching 1,500 members so come and join the action! [discord.supabase.com](https://discord.supabase.com)

![We launched a Discord server](/images/blog/2021-july/discord.png)

## PostgreSQL 13

All new Supabase projects will be launched with PostgreSQL 13.3, and we're working on a migration path for old projects.
This gives you [looooaads of new stuff out the box](/blog/supabase-postgres-13).

![PostgreSQL 13](/images/blog/2021-july/postgres-13.png)

## PostgREST v8.0

We worked with our friends at [PostgREST](https://postgrest.org/) to make some [huge improvements](/blog/supabase-community-day#postgrest-version-80).
For those of you who don't know, every Supabase instance comes with a dedicated PostgREST server by default,
which provides the auto-generated CRUD API that we wrap with `supabase-js`.

![PostgREST v8.0](/images/blog/2021-july/postgrest-8.png)

## Flutter/Dart support

Our community driven libs for the fast growing mobile and web framework are now in beta. Learn more by following the [Quickstart guide](/docs/guides/with-flutter).

![Flutter/Dart support](/images/blog/2021-july/dart.png)

## Hackathon

We're running a week long hackathon starting NOW. There are some legit prizes, and you can win in a bunch of different categories.
Check the [full instructions here](/blog/1-the-supabase-hackathon) on how to participate. Submissions close next Friday at midnight PST.

![Hackathon](/images/blog/2021-july/hackathon.png)

## Hooks & Functions

We made an announcement on the progress of functions, and even shipped a few preliminary components, try them out and give us feedback as we continue to move towards this next major milestone.
Read the latest [updates here](/blog/supabase-functions-updates).

![Hooks & Functions](/images/blog/2021-july/hooks.jpg)

## Swag Store

Get your hands on some [Supabase Swag](https://supabase.store/), hand packed and mailed by our team based in Singapore.

![Swag Store](/images/blog/2021-july/swag.png)

## Community

- [@traversymedia](https://twitter.com/traversymedia) made a [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) video
- [@themarcusbattle](https://twitter.com/themarcusbattle/status/1419638564573360130?s=20) wrote a [guide](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging) on sending sms notifications using node.js, Twilio, and Supabase (this is different to sms auth above)
- [@dots_hq](https://twitter.com/dots_hq) launched a slack/discord [community management tool](https://dots.community/) built on Supabase
- [@KennethCassel](https://twitter.com/KennethCassel) launched a [platform for building programming courses](https://www.slip.so/) using Supabase
- [@MobbinDesign](https://twitter.com/MobbinDesign) migrated their UI/UX library app to Supabase, currently [serving 200,000 users](/blog/mobbin-supabase-200000-users)
- To the amazing users who are helping moderate our discord server, ya'll are amazing

**Supabase GitHub Star Growth**

![14200 stars on GitHub.](/images/blog/2021-july/github-growth.png)

<small>
  Source: <a href="https://repository.surf/supabase">repository.surf/supabase</a>
</small>

If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or
[follow us on Twitter](https://twitter.com/supabase).

## Coming Next

Security, stability, performance ... and Functions.

## Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Subscribe to our [YouTube channel](https://www.youtube.com/c/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
