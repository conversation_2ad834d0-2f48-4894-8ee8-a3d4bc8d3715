---
title: 'Open Source Hackathon 2024 winners'
description: Announcing the winners of the Open Source Hackathon 2024!
author: t<PERSON>_shukert
image: oss-hackathon-winners/thumbnail.png
thumb: oss-hackathon-winners/thumbnail.png
launchweek: '11'
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2024-04-30'
toc_depth: 2
---

The [Supabase GA week](https://supabase.com/ga-week) brought us so many cool updates, but the fun isn’t over yet, because we are now announcing the winners of the [Supabase Open Source Hackathon 2024](https://supabase.com/blog/supabase-oss-hackathon)!

We have enjoyed trying out every single submission we have received! You can find all of the submissions on [madewithsupabase.com](https://www.madewithsupabase.com/).

Now, without further ado, let us announce the winners of the hackathon!

## Best overall project

### Winner

[vdbs - vision database sql](https://github.com/xavimondev/vdbs) - by [@xavimonp](https://twitter.com/xavimonp)

vdbs stands for "vision database SQL". It allows you to convert your database diagrams into SQL Schema using the capabilities of Vision API. Once the SQL is ready, you can either copy and paste it right in your Supabase project or run the generated npm command to create the migration file.

![vdbs](/images/blog/oss-hackathon-winners/vdbs.png)

### Runner Up

[Complete Components](https://github.com/gSUz92nc/Complete-Components/) - by [@gSUz92nc](https://github.com/gSUz92nc)

Complete Components is an open-source project that uses AI to assist developers in quickly creating and integrating HTML components with Supabase as the backend and Tailwind CSS for styling. It aims to streamline the development process by leveraging AI to generate tailored HTML components and seamlessly integrate them with Supabase.

![Complete Components](/images/blog/oss-hackathon-winners/complete-components.png)

## Best use of AI

### Winner

[ClosetAi | Your virtual closet, endless possibilities.](https://github.com/ineffablesam/closet-ai#closet-ai--revamp-your-style-effortlessly) - by [@samuelP09301972](https://twitter.com/samuelP09301972), [@teamnextvitap](https://twitter.com/teamnextvitap), and two others

Closet AI: the ultimate style companion. Upload images, choose your desired topwear or bottomwear, and watch as our cutting-edge AI replaces your outfit instantly! Revolutionize your wardrobe with the power of AI and Supabase!

![ClosetAi](/images/blog/oss-hackathon-winners/closetai.png)

### Runner Up

[Plot Twist](https://github.com/NeoFoxxo/plottwist) - by [@NeoFoxMusic](https://twitter.com/NeoFoxMusic), [@bonndubz](https://twitter.com/bonndubz), [@Nidhish18224246](https://twitter.com/Nidhish18224246) and [@SlaviKaraslavov](https://twitter.com/SlaviKaraslavov)

Generate interactive stories where your choices shape the narrative. Start with a prompt, and let our custom AI generate the story and choices you can make. Want to see what you would do if you were stranded on an island? Or if you were a cat who is a pro skateboarder? Provide the idea and you can experience these scenarios and affect the story!

![Plot Twist](/images/blog/oss-hackathon-winners/plot-twist.png)

## Most fun / best easter egg

### Winner

[NamePLAT](https://github.com/Git-Prakhar/npat-game) - by [@Abhishek_cpp](https://twitter.com/Abhishek_cpp) and [@BlackStarGames\_](https://twitter.com/BlackStarGames_)

Name Place Animal Thing (NamePLAT) is a fun and challenging game where players compete to quickly identify a name (country) a place, an animal, and a thing that starts with a given letter, but the twist is you get to choose between 4 Images and the fastest finger gets most points. The game is designed to test players' knowledge, speed, and creativity.

![NamePLAT](/images/blog/oss-hackathon-winners/nameplat.png)

### Runner Up

[Supapaused](https://github.com/jensen/supapaused) - by [@karljensen](https://twitter.com/karljensen)

Supapaused is an app that allows Supabase users to track their paused Supabase instances. You can view when the projects were paused in a timeline.

![Supapaused](/images/blog/oss-hackathon-winners/supapaused.png)

## Most technically impressive

### Winner

[Retorithoughts](https://github.com/Jcampillo1207/Retoritoughts) - by [@Chema12071](https://twitter.com/Chema12071), [@alexf505](https://twitter.com/alexf505) and [@Patuqueson](https://twitter.com/Patuqueson)

Retorithoughts is a trivia game that challenges players to determine the chronological order of historical events. It's fun and an educational tool that tests and expands your knowledge of history.

![Retorithoughts](/images/blog/oss-hackathon-winners/retorithoughts.png)

### Runner Up

[Data Loom](https://github.com/sangonz193/data-loom) - by [@sangonz193](https://github.com/sangonz193)

Data Loom aims to provide a hassle-free and secure way to share files between devices. The platform leverages WebRTC technology, ensuring that your files are transferred directly and securely, with no intermediary server access.

![Data Loom](/images/blog/oss-hackathon-winners/data-loom.png)

## Most visually pleasing

### Winner

[SupaWriter](https://github.com/phibersoft/supawriter.git) - by [@phibersoft](https://github.com/phibersoft)

SupaWriter is a ten-finger typing game that helps you improve your typing speed and accuracy. It also features a leaderboard to see how you are performing compared to others.

![SupaWriter](/images/blog/oss-hackathon-winners/supawriter.png)

### Runner Up

[Echoes of Creation](https://github.com/laznic/echoes-of-creation) - by [@laznic](https://twitter.com/laznic) and [@janireijonen](https://twitter.com/janireijonen)

Echoes of Creation is a digital experience that portrays the excitement and struggles artists have with their creative processes.

![Echoes of Creation](/images/blog/oss-hackathon-winners/echos-of-creation.png)

## The Prizes

![The prize](/images/blog/oss-hackathon-winners/prize.png)

The winner of the best overall project will receive an Apple AirPods, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.

## Getting Started Guides

If you're inspired to build, check out some of the latest resources:

- [Quick Start Guides](https://supabase.com/docs/guides/getting-started)
- [AI & Vectors Guide](https://supabase.com/docs/guides/ai)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
