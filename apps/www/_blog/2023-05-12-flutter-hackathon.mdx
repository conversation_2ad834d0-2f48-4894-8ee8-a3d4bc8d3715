---
title: 'Flutter Hackathon'
description: Build Flutter apps and win limited edition swag.
author: tyler_shukert
image: flutter-hackathon/thumbnail.jpg
thumb: flutter-hackathon/thumbnail.jpg
categories:
  - developers
tags:
  - flutter
  - hackathon
date: '2023-05-12'
toc_depth: 3
---

As new Flutter updates are announced at Google I/O, we are excited to kick off the Flutter hackathon with the help of our partners, [FlutterFlow](https://flutterflow.io/) and [Invertase](https://invertase.io/). The rules are simple: build beautiful apps using our favorite hybrid framework, Flutter!

![Flutter Hackathon Stickers](/images/blog/flutter-hackathon/stickers.png)

## Key Facts

- You have 10 days to build a new app using Flutter/Dart and Supabase in some capacity.
  - Starting 7:00 am PT Friday, May 12th, 2023
  - The submission deadline is 11:59 pm PT on Sunday, May 21st, 2023
- Enter as an individual, or as a team of up to 5 people

## Prizes

There are 5 categories, there will be prizes for:

- Best overall project (Mechanical Keyboards & Swag kit)
- Best FlutterFlow project (FlutterFlow Swag kit & 3 months of FlutterFlow Pro Plan for free)
- Best Dart Edge Project (Swag kit)
- Most technically impressive (Swag kit)
- Most visually pleasing (Swag kit)

There will be a winner and a runner-up prize for each category. Every team member on winning/runner-up teams gets a swag kit.

![Flutter Hackathon keyboard](/images/blog/flutter-hackathon/keyboard.png)

## Submission

Please include an easily accessible demo, or a demo video so that the judges can take a look at the app. For Flutter apps, [Zapp](https://zapp.run/) is a great tool to generate an easily shareable demo link for your app. For a FlutterFlow project, please share a publicly shareable link so that the judges can take a look at the actual app.

You should submit your project using [this form](https://www.madewithsupabase.com/flutter-hackathon) before 11:59 pm Sunday midnight PT May 21st, 2023.

## Judges

[Pooja](https://twitter.com/pooja_bhaumik) from FlutterFlow, [Majid](https://twitter.com/mhadaily) from Invertase, and [Tyler](https://twitter.com/dshukertjr), myself from Supabase will do the honors of judging the winners.
We will be looking for:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

## Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be on multiple teams
- One submission per team
- All design elements, code, etc. for your project must be created **during** the event
- Must use Flutter and Supabase in some capacity
- You must submit before the deadline (no late entries)

## Resources

### Supabase resources

- [Flutter Supabase quick starter guide](https://supabase.com/docs/guides/getting-started/tutorials/with-flutter)
- [Setup Supabase on FlutterFlow docs](https://docs.flutterflow.io/data-and-backend/supabase/supabase-setup)
- [How to setup Supabase auth on FlutterFlow video](https://youtu.be/tL-sLPfWzVE)
- [Dart Edge Guide](https://supabase.com/docs/guides/functions/dart-edge)
- [Getting started with Dart Edge video](https://youtu.be/53jhgrDONW4)

### Community help

The Supabase Team will be taking part in the Hackathon and you'll find us live to build in our discord all week. Please join us by building in public:

- Text channel: [#hackathon](https://discord.gg/UYyweApy)

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com/)

![Discord](/images/blog/flutter-hackathon/discord.png)

## Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code snippets, and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
- Flutter and the related logo are trademarks of Google LLC. Supabase Flutter Hackathon is not affiliated with or otherwise sponsored by Google LLC.
