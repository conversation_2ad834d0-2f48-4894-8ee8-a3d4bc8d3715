---
title: 'Launch Week 6 Hackathon'
description: Build an Open Source Project, Win $1500 and the Supabase Darkmode Keyboard
author: ant_wilson
image: lw6-hackathon/thumbnail.jpg
thumb: lw6-hackathon/thumbnail.jpg
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2022-12-09'
toc_depth: 3
---

The official Supabase Hackathon starts on Friday 9th December at 8am Pacific Time and ends Monday 19th December at 00:01am.

[Launch Week](https://supabase.com/launch-week) is about to kick off and we're running a hackathon in parallel.

You can win $1500 in GitHub sponsorships and an official Supabase Keyboard (extremely limited edition), along with a bunch of other prizes!

See all the [winners from all the previous hackathons](https://supabase.com/blog/tags/hackathon) for inspiration.

![Supabase Keyboard](/images/blog/lw6-hackathon/keyboard.png)

## Key Facts

- You have 10 days to build a new **Open Source** project using Supabase.
  - Starting 8:00am PT Friday 9th December 2022
- Build whatever you want - a project, app, tool, library, anything
- Enter as an individual, or as a team of up to 5 people
- Submission deadline is 00:01am early Monday morning PT 19th December 2022
- There are 5 prize categories.
  - Best overall project will win $1500 in GitHub sponsorships, and a Supabase Keyboard for each team member.
  - Most fun/interesting, best Flutter project, and best storage project will all win a limited edition Hackathon swag kit for each team member!
  - Our friends at [Deno](https://deno.land/) will be judging a special guest category: Best Edge Functions Project! They will be providing some [special swag](https://deno.com/blog/edge-functions-supabase-launch-week-6-hackathon) for the winners!

## Details

### Schedule

- The Hackathon begins at 8:00am PT Friday 9th December 2022
- Work on your project any time for the next 10 days
- Submission deadline (00:01am early Monday morning PT 19th December 2022)

### Prizes

There are 5 chances to win, there will be prizes for:

- Best Overall Project ($1500 in GitHub Sponsorship & Supabase Keyboards)
  - to be paid as 3x$500 GitHub Sponsorships (over 3 months)
- Most Fun/Interesting (Swag kit)
- Best Flutter Project (Swag kit)
- Best Storage Project (Swag kit)
- Best Edge Functions Project (Deno Swag kit)

There will be winner and runner-up prizes for each category. Every team member on winning/runner-up teams gets a supaverified swag kit.

![Swag kit](/images/blog/lw6-hackathon/swagkit.png)

### Submission

You should submit your project using [this form](https://www.madewithsupabase.com/launch-week-6).

### Judges

Best Edge Functions Project category will be judged by our friends at [Deno](https://deno.land/), and other categories will be judged by the Supabase team.
We will be looking for:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

### Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be in multiple teams
- One submission per team
- All design elements, code, etc. for your project must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)

### Community

The Supabase Team will be taking part in the Hackathon and you'll find us live building in our discord all week. Please join us by building in public:

- Text channel: hackathon
- Audio channel: hackathon

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Launch Week

Don't forget to checkout the new feature's being announced as part of [Launch Week](/launch-week).

- [Previous Hackathon Prize Winners](https://supabase.com/blog/launch-week-5-hackathon-winners)
- [Who We Hire at Supabase - Founders Chat](https://youtu.be/-BG9XptyCKI)

### Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
- $1500 prize will be paid by making a GitHub sponsorship to the winning repo, $500/month for 3 months. The goal here is to create a sustainable project that will be continue to be maintained for the duration of the sponsorship period (and hopefully beyond :) )

## More Launch Week 6

- [Day 1: New Supabase Docs, built with Next.js](https://supabase.com/blog/new-supabase-docs-built-with-nextjs)
- [Day 2: Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn)
- [Day 3: Multi-factor Authentication via Row Level Security Enforcement](https://supabase.com/blog/mfa-auth-via-rls)
- [Day 4: Supabase Wrappers, a Postgres FDW framework written in Rust](https://supabase.com/blog/postgres-foreign-data-wrappers-rust)
- [Day 5: Supabase Vault is now in Beta](https://supabase.com/blog/vault-now-in-beta)
- [Community Day](https://supabase.com/blog/launch-week-6-community-day)
- [Point in Time Recovery is now available](https://supabase.com/blog/postgres-point-in-time-recovery)
- [Custom Domain Names are now available](https://supabase.com/blog/custom-domain-names)
- [Wrap Up: everything we shipped](https://supabase.com/blog/launch-week-6-wrap-up)
