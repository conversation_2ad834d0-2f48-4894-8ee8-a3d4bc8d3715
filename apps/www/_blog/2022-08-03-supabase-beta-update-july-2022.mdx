---
title: Supabase Beta July 2022
description: Launch Week Golden Tickets, Flutter SDK 1.0 Developer Preview and more...
author: ant_wilson
image: 2022-july/thumb.png
thumb: 2022-july/thumb.png
categories:
  - product
tags:
  - release-notes
date: '2022-08-03'
toc_depth: 3
---

It’s happening! Launch Week is back ✊. We are preparing lots of product announcements and fun activities. Join us the week of August 15-19 for Launch Week 5️⃣.

We don't ship only during Launch Weeks, so this month we still bring you some great new stuff: Flutter SDK 1.0 (developer preview), Auth Helpers, pg_jsonschema, and more.

## Launch Week Golden Ticket

![Launch week golden ticket](/images/blog/2022-july/teaser.jpg)

Register for [#SupaLaunchWeek 5](https://twitter.com/hashtag/SupaLaunchWeek) and if you are lucky, you may pull a golden ticket and win a limited edition Supabase goodie bag! [Get a ticket](https://supabase.com/launch-week).

## Supabase Flutter SDK 1.0 developer preview

![flutter sdk 1.0](/images/blog/2022-july/flutter-sdk.png)

Supabase Flutter SDK is getting a major update, with a big focus on developer experience. We shared a preview of what is to come so you can help us make it even better with your feedback. [Read the blog.](https://supabase.com/blog/supabase-flutter-sdk-1-developer-preview)

## Revamped Auth Helpers (now with SvelteKit support)

![Sveltekit auth helpers](/images/blog/2022-july/auth-helpers.jpg)

We've been hard at work making an Auth experience that is as smooth as possible for Supabase developers. The new Auth Helpers have a better experience for server-side rendering (SSR) environments, new Github structure, SvelteKit support, and more. [Read the blog](https://supabase.com/blog/supabase-auth-helpers-with-sveltekit-support).

## pg_jsonschema – A Postgres extension for JSON validation

![pg_jsonschema](/images/blog/2022-july/pg_jsonschema.png)

Born as an excuse to play with pgx, pg_jsonschema is a solution we're exploring to allow enforcing more structure on json and jsonb typed postgres columns. [Only 10 lines of code](https://github.com/supabase/pg_jsonschema/tree/fb7ab09bf6050130e8d656f2999ec0f6a3fedc0d) 😎

## hCaptcha integration

![hCaptcha](/images/blog/2022-july/hCaptcha-1.png)

The new hCpatcha integration allows you to implement captcha functionality for auth. Enable hCaptcha protection in the auth settings to stop bots attacks and protect your users' privacy. [Captcha settings](https://supabase.com/docs/reference/tools/reference-auth).

## Updated Settings UI in dashboard

![supabase-updated-settings-ui-dashboard](/images/blog/2022-july/auth-config.jpeg)

We've started updating our settings UI, starting with the Auth settings. You'll now see updated UI for setting up your [Auth providers](https://supabase.com/dashboard/project/_/auth/providers), [Email templates](https://supabase.com/dashboard/project/_/auth/templates), and [SMTP config](https://supabase.com/dashboard/project/_/auth/url-configuration).

## Quick product announcements

-  Email OTP now supports 6-10 digits length. [PR](https://github.com/supabase/gotrue/pull/513)
-  GenerateLink method now returns the email_otp, the hashed_token , the verification_type and the redirect_to. [PR](https://github.com/supabase/gotrue/pull/537)

## Nuxt 3 Quickstart

![nuxt-3-supabase-quickstart](/images/blog/2022-july/Nuxt-Supabase-2.png)

There is a new quickstart in town. The guide, powered by [NuxtSupabase](https://supabase.nuxtjs.org/), shows how to build a simple user management app using Supabase and Nuxt 3, including Database, Auth, Storage, and more. [Get started](https://supabase.com/docs/guides/with-nuxt-3).

## Made with Supabase

![made with supabase](/images/blog/2022-july/buy-me-a-pizza.jpg)

[BuyMeAPizza](https://www.buymea.pizza/) | A brand new, free, and open source service to accept donations in crypto on your website. Built with Next.js, Tailwind, Supabase, and Solana tools.

Discover other projects: [Made with Supabase](https://www.madewithsupabase.com/)

## Community highlights

![Community](/images/blog/2022-july/community--dark.jpg)

The Supabase community is exploding and we’ve reached a point where we can’t fit everything into our monthly newsletter so here’s the full list of community updates from July:

- Netlify announced a new webinar series, Web Ecosystem, and we get to be part of it. [Blog Post](https://www.netlify.com/blog/web-ecosystem-webinar-series/)
- Implementing "seen by" functionality with Postgres. [Blog Post](https://supabase.com/blog/seen-by-in-postgresql)
- Open Source Startup Podcast with our CEO Paul Copplestone. [Podcast](https://anchor.fm/ossstartuppodcast/episodes/E43-Building-Supabase--the-Open-Source-Firebase-Alternative-e1ld637)
- Scaleway listed 40+ of the best open-source tools to build your startup. [Blog Post](https://blog.scaleway.com/40-open-source-projects/)
- Sign-up form with OTP and auto login using Supabase and Arengu. [Guide](https://www.arengu.com/tutorials/sign-up-form-with-otp-and-auto-login-using-supabase)
- Setup Supabase with Nest.js. [Tutorial](https://blog.devgenius.io/setup-supabase-with-nest-js-85041b03ec3a)
- Loading SportsDB data into Supabase. [Tutorial](https://dev.to/supabase/auto-generated-documentation-in-supabase-5e8o)
- Introduction to Supabase: Postgres Database using Python. [Tutorial](https://www.analyticsvidhya.com/blog/2022/07/introduction-to-supabase-postgres-database-using-python/)
- Supabase and SWR to fetch data updates in realtime using React Hooks. [Tutorial](https://dev.to/jakobpotosme/supabase-swr-3icp)
- Protected routes using Next.js Edge Middleware & Supabase. [Tutorial](https://engineering.udacity.com/protected-routes-using-next-js-edge-middleware-supabase-f197ba7f503c)
- Supabase backend demo with Bravo Studio. [Video](https://www.youtube.com/watch?v=ZWQeAmbYFzg)
- Building a live demo with Supabase and AppSmith. [Video](https://www.youtube.com/watch?v=xmb4JrTYhZg)
- Auto-generated documentation in Supabase. [Tutorial](https://dev.to/supabase/auto-generated-documentation-in-supabase-5ecd)
- Speeding Up Bulk Loading in PostgreSQL. [Blog Post](https://dev.to/supabase/speeding-up-bulk-loading-in-postgresql-41g5)
- Installing React-Admin In A Remix App. [Tutorial](https://marmelab.com/blog/2022/07/01/add-react-admin-to-your-remix-app-using-supabase.html)
- How to set up Supabase in React? And Some Useful Tips. [Tutorial](https://dev.to/eminvergil/how-to-setup-supabase-in-react-and-some-useful-tips-40oc)
- My approach to authentication with Next.JS, Prisma & Supabase. [Guide](https://ktra99.hashnode.dev/my-approach-to-authentication-with-nextjs-prisma-supabase)

## We’re hiring

Come join one of the fastest growing open source projects ever 🤗

- [Customer Success (US time zone)](https://boards.greenhouse.io/supabase/jobs/4594393004)
- [Go-To-Market Recruiter](https://boards.greenhouse.io/supabase/jobs/4561503004)
- [View all our openings](https://boards.greenhouse.io/supabase)

---

## Meme Zone

If you made it this far in the blog post you deserve a treat. [Follow us on Twitter](https://twitter.com/supabase) for more.

![Supabase meme june 2022](/images/blog/2022-july/CSS-meme.jpg)

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
