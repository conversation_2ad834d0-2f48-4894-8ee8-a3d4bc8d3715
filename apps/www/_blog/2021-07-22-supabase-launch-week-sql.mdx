---
title: 'Supabase Launch Week II: The SQL'
description: Five days of Supabase. Again.
author: paul_copplestone
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: launch-week-sql/launchweek-2-the-sql-og.jpg
thumb: launch-week-sql/supabase-launch-the-sql.png
categories:
  - product
tags:
  - launch-week
date: '2021-07-22'
toc_depth: 2
---

In March, Supabase held a [Launch Week](/blog/launch-week) where we launched a new product/feature every day for a week.

It may seem crazy to do another launch week just four months later, but we couldn't give up on a good pun, so get ready for **Launch Week II: the SQL**.

## What to expect

Just like last time, we don't want to spoil the surprise. So for now we'll give you a teaser of what to expect. Come back each day to see what we launch, or follow us on Twitter to keep up to date: [@supabase](https://twitter.com/supabase) and follow the hashtag [#supalaunchweek](https://twitter.com/hashtag/supalaunchweek).

## Monday 26 July: Community Day

![Supabase Community](/images/blog/launch-week-sql/community-day.png)

### **When?**

Monday 26 July 2021.

### What?

One of the best things about building Supabase is the community. We're even luckier because we're an amalgamation of tools like Postgres and PostgREST. We're starting the week by shining a spotlight on some of the latest developments from the community.

### Where?

Read the [blog post](/blog/supabase-community-day).

## Tuesday 27 July: Storage v2

![Supabase storage v2](/images/blog/launch-week-sql/storage.png)

### **When?**

Tuesday 27 July 2021.[^1]

### What?

We shipped [Version 1 of Supabase Storage](/blog/supabase-storage) in the last Launch Week, and now we've had 4 months to work on Storage Reloaded.

### Where?

Read the [blog post](/blog/storage-beta).

## Wednesday 28 July: Auth v2

![Supabase Auth v2](/images/blog/launch-week-sql/auth-v2.png)

### **When?**

Wednesday 28 July 2021.[^1]

### What?

What's cooler than Arnie in the 90's? Passwordless logins. We've [already](/docs/guides/auth/social-login/auth-apple) [built](/docs/guides/auth/social-login/auth-bitbucket) [support](/docs/guides/auth/social-login/auth-discord) [for](/docs/guides/auth/social-login/auth-facebook) [eight](/docs/guides/auth/social-login/auth-github) [different](/docs/guides/auth/social-login/auth-gitlab) [Oauth](/docs/guides/auth/social-login/auth-google) [providers](/docs/guides/auth/social-login/auth-twitter), so it's anyone's guess what can happen in Supabase Auth Episode II.

### Where?

Read the [blog post](/blog/supabase-auth-passwordless-sms-login).

## Thursday 29 July: Dashboard v2

![Supabase Dashboards](/images/blog/launch-week-sql/dashboards.png)

### **When?**

Thursday 29 July 2021.

### What?

Who needs a futuristic [Hoverboard from 2015](https://www.youtube.com/watch?v=NRkGYW9JAmQ) when you've got a super slick Dashboard from 2021. We've got some exciting changes coming to the Dashboard.

### Where?

Read the [blog post](/blog/supabase-reports-and-metrics).

## Friday 30 July: One more thing

![Supabase prequels](/images/blog/launch-week-sql/prequel.png)

### **When?**

Friday 30 July 2021.

### What?

We all know that the best [SeQueLs](https://www.reddit.com/r/SequelMemes/) are actually [PreQueLs](https://www.reddit.com/r/PrequelMemes/). We've got a few surprises lined up for Friday that make the Supabase story a bit more complete.

### Where?

Read about [Supabase Hackathon](/blog/supabase-swag-store),
[Supabase Functions Updates](/blog/supabase-functions-updates),
and the Supabase [Swag Store](/blog/1-the-supabase-hackathon).

[^1]:
    When we first announced, Storage was on Wednesday and Auth was on Tuesday. We switched the order of these as we are doing a
    Storage [presentation](https://www.meetup.com/the-monthly-dev-world-class-talks-by-expert-developers/events/278158726/) with
    [daily.dev](https://daily.dev/) on Wednesday.
