---
title: 'The Supabase Hackathon'
description: A whole week of Hacking for Fun and Prizes.
author: ant_wilson
author_url: https://github.com/awalias
author_image_url: https://github.com/awalias.png
image: hackathon/og-supabase-hackathon.png
thumb: hackathon/cover-supabase-hackathon.png
categories:
  - developers
tags:
  - launch-week
  - hackathon
date: '2021-07-30'
toc_depth: 3
---

# The Supabase Hackathon

We've reached the end of [Launch Week II: The SQL](/blog/2021/07/22/supabase-launch-week-sql) - but it's the start of something else...

The first Supabase Hackathon starts **right now**.

## Key Facts

- You have 7 days to build a new **Open Source** project using Supabase
- It can be whatever you want - a project, app, tool, library, anything
- Enter as an individual, or as a team of up to 5 people
- Submission deadline is 11:59pm Friday night PDT (6th August 2021)
- You can win one of these extremely [Limited Edition Olympic Gold](https://supabase.store/products/hackathon-prize) and Silver Supabase T-Shirts

![Gold Tshirt](/images/blog/hackathon/tshirt.png)

## Details

### Schedule

- Opening Ceremony - the Hackathon begins **NOW**! (8am PDT 30th July 2021)
- Work on your project any time this week
- Submission deadline (11.59pm PDT Friday 6th August 2021)
- Judges Deliberate (Saturday + Sunday)
- Medal Ceremony (Winners Announced) (9pm PDT Monday 9th August 2021)

### Prizes

There are 5 chances to win, there will be prizes for:

- Best Overall Project
- Most Visually Pleasing
- Most Technically Impressive
- Most Fun/Interesting
- Best Meme (add it to your README)

There will be a winner and a runner-up prize for each category.

![Gold Tshirt](/images/blog/hackathon/prizes.png)

### Submission

You should submit your project using [this form](https://forms.gle/erYrSFKSErKVBFFC8).

You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:

- link to hosted demo (if applicable)
- list of team members GitHub handles (and Twitter if they have one)
- any demo videos, instructions, or memes
- a brief description of how you used Supabase:
  - to store data?
  - realtime?
  - auth?
  - storage?
- any other info you want the judges to know (motivations/ideas/process)
- optional team photo

### Judges

We have assembled an All Star Judging Panel:

- [@swyx](https://twitter.com/swyx/)
- [@themarcusbattle](https://twitter.com/themarcusbattle)
- The Supabase Team
- One more Mystery Guest

![Judges](/images/blog/hackathon/judges.png)

They will be looking for a few things, including:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

### Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be in multiple teams
- One submission per team
- All design elements, code, etc. for your project must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.

### Community

The Supabase Team will be taking part in the Hackathon and you'll find us live building in our discord all week. We're temporarily adding two channels:

- Text channel: hackathon
- Audio channel: hackathon

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Guides

Here's a collection of resources that will help you get started building with Supabase:

- [Examples and Resources](/docs/guides/examples)
- [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]
- [Flutter Quickstart Guide](/docs/guides/with-flutter)
- [Nextjs Quickstart Guide](/docs/guides/with-nextjs)
- [Using Supabase inside Replit](/blog/using-supabase-replit)
- [Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)
- [Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]
- [Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)
- [How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]
- [Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]
- [Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)

### Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
