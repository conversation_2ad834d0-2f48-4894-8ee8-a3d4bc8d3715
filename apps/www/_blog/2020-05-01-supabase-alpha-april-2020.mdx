---
title: Supabase Alpha April 2020
description: Two months of building
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
categories:
  - product
tags:
  - supabase
date: '06-01-2020'
---

Now in [Supabase](https://supabase.com/dashboard):

- Set up Postgres in less than 2 minutes
- Auto-generated APIs! (they are a bit flaky, go easy)
- Query your database directly from the dashboard
- Analyze your queries and make them faster :rocket:

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/ck5MM_PD4Co"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

## Important Notes

1/ Supabase is NOT production ready. Have a play around and let us know what you think. We don't track ANYTHING (although we do store your github name and email). We'd like to keep it that way, so we rely on your direct feedback to steer our product roadmap.

2/ We are free to use while we're in alpha thanks to the generosity of Digital Ocean's startup program. But the funds aren't unlimited - please shut down the database when you're done :pray:.

3/ The database takes about 2 minutes to build. It's worth it, we promise. If you've ever wanted use Postgres, we're the easiest in the market.

## Coming soon

At Supabase, we're building some amazing tools that make Postgres as easy to use as Firebase. Some of the things we're working on:

#### Simple interface

Why are database interfaces so hard to use? The Supabase team has built products for 70-year-olds, so we're confident we can make something easier for developers:
![Supabase table view](/images/blog/2020/b4o39am95zcl5vl54j75.png)

#### Connectors

Send realtime database changes to other systems, like queues or webhooks (Slack notifications!):
![Supabase connectors](/images/blog/2020/aom5r917s792cc081bbz.png)

#### And more

- ⚡ Realtime listeners! Subscribe to your database just like you would with Firebase.
- 🤖 Instant RESTful APIs that update when you update your schema. Supabase introspects your schema and updates your API and documentation.
- 📓 Auto-documentation for your APIs and Postgres schema. What's better than documentation? Documentation that you don't have to manually keep up to date.

## Follow us

Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard)

Make sure to star us on github! [github.com/supabase/supabase](https://github.com/supabase/supabase)
