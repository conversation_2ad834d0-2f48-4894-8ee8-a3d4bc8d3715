---
title: 'Hacktoberfest Hackathon Winners 2021'
description: Celebrating many amazing projects submitted to our Hacktoberfest Hackathon.
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: hackathon-winners/misc/hacktoberfest-og.png
thumb: hackathon-winners/misc/hacktoberfest-og.png
categories:
  - developers
tags:
  - hacktoberfest
  - hackathon
date: '2021-10-14'
toc_depth: 2
---

Hacktoberfest is in full swing and we've been blown away by [all the incredible projects]() submitted to our second open-source Hackathon.

We've picked our top ten favourite projects across five categories, and believe us, it was a tough decision to make!

Since all submissions were so stellar, we've decided to reward everyone with a [limited edition participation tee](https://supabase.store/products/hacktoberfest-participation-tee).

Now, let us introduce the winner and runner up for each category, and see below for the even more limited edition swag they've won!

Lastly, remember that all projects are open-source, so do check them out and spend the second half of Hacktoberfest contributing to your favourite projects!

## Best Overall Project

### Winner

[Pickle](https://github.com/alizahid/pickle) - by [@alizahid0](https://twitter.com/alizahid0)

Ali built a blazingly fast, privacy and developer focused analytics service. Complete with a dashboard and docs. And the dashboard even syncs events in realtime. Mind blowing stuff!

[![Pickle](/images/blog/hackathon-winners/projects/pickle.png)](https://github.com/alizahid/pickle)

### Runner Up

[og:supa](https://github.com/janniks/ogsupa) - by [@jnnksbrt](https://twitter.com/jnnksbrt)

og:supa is a snazzy site allowing you to generate og:images for your posts to make empty link previews a thing of the past. And the site looks fabulous itself!

[![Fireplace](/images/blog/hackathon-winners/projects/ogsupa.png)](https://github.com/janniks/ogsupa)

## Most Visually Pleasing

### Winner

[CourseBuddy](https://github.com/seufernandez/coursebuddy) - by [@seufernandez](https://twitter.com/seufernandez)

Fernand built a platform for students to share their learnings and notes with each other. A great cause with a great design and some awesome illustrations.

[![CourseBuddy](/images/blog/hackathon-winners/projects/coursebuddy.png)](https://github.com/seufernandez/coursebuddy)

### Runner Up

[Relm](https://github.com/imsaptarshi/relm) - by [@imsaptarshiii](https://twitter.com/imsaptarshiii)

Relm is a platform to help you build, manage, and nurture your communities, with features around managing events, tracking analytics, and sending newsletters. Complete with a slick design and UX.

[![Relm demo](/images/blog/hackathon-winners/projects/relm.png)](https://www.loom.com/share/58f6be6a1f7241a3843b1edc82a3a40a)

## Most Technically Impressive

### Winner

[Feedback](https://github.com/drmzio/feedback) - by [@drmzio](https://twitter.com/drmzio)

Daniel built a feedback widget that you can include on your page to easily collect your user's sentiment about your page. Our feedback for Daniel: we love it!

[![Feedback](/images/blog/hackathon-winners/projects/feedback.png)](https://github.com/drmzio/feedback)

### Runner Up

[Party Parrot as a Service](https://github.com/highlight-run/party-parrot-as-a-service) - by [@c00brill](https://twitter.com/c00brill), [@theJayKhatri](https://twitter.com/theJayKhatri), [@JohnPhamous](https://twitter.com/JohnPhamous)

Who doesn't love a custom party parrot Slack emoji, but who has time to create a gif for each team member?! Let us introduce: Party Parrot as a Service! Just input a picture with your face in it and PPaaS will find it and create a party parrot gif for you!

![Party Parrot Winners](https://camo.githubusercontent.com/96b1888e4ca2b8e4bdd75b865990f639a3fa076e8b06b32cf9eca20f74a0be4d/68747470733a2f2f692e696d6775722e636f6d2f6d6c4a316b5a502e676966)

[![Party Parrot as a Service](/images/blog/hackathon-winners/projects/ppaas.png)](https://www.loom.com/share/8a20eac10ee94708960bd86835e3a000)

## Best Mobile Project

### Winner

[Utility Manager Flutter App](https://github.com/KathirvelChandrasekaran/utility_manager_flutter_supabase) - by [@KathirvelChandrasekaran](https://github.com/KathirvelChandrasekaran)

Kathirvel's [dictionary app](https://github.com/KathirvelChandrasekaran/dictionary_app) won him a [limited edition gold tee](https://supabase.store/products/hackathon-prize?variant=40662411673755) at our last hackathon, and with his not only useful but also brilliantly looking utility manager Flutter app he's able to extend his collection of limited edition Supabase tees. Well done!

[![Dictionary](/images/blog/hackathon-winners/projects/utility.png)](https://github.com/KathirvelChandrasekaran/utility_manager_flutter_supabase)

### Runner Up

[TLWR](https://github.com/croquies/TLWR) - by [@Aqudi](https://github.com/Aqudi), [croquies](https://github.com/croquies)

Taejung and Wonmo built a logger tool for Flutter based apps that logs and visualizes the path a user takes through your app to identify improvements to your user experience.

[![TLWR video demo](/images/blog/hackathon-winners/projects/tlwr.png)](https://youtu.be/Yx4N2bONA44)

## Most Spooky/Fun

### Winner

[Spookd](https://github.com/netgfx/Spookd) - by [@netgfx](https://twitter.com/netgfx)

Michael prototyped a spooky halloween game using Framer and then made it actually playable by connecting Framer with Supabase. Now that's spooky good fun!

[![TLWR video demo](/images/blog/hackathon-winners/projects/spookd.png)](https://youtu.be/8SsWL3unwGI)

### Runner Up

[uwudaily](https://github.com/maggie-j-liu/uwudaily) - by [@maggie-j-liu](https://github.com/maggie-j-liu), [@sampoder](https://github.com/sampoder), and [@eilla1](https://github.com/eilla1)

This project by a group of high school students gave us the feel good vibes. On the one hand with their awesome website that conveniently lets you log your daily vibes and check in on your friends', and on the other hand because high school students are building brilliant things with Supabase. How freaking cool is that?!

[![uwudaily](/images/blog/hackathon-winners/projects/uwu.png)](https://github.com/maggie-j-liu/uwudaily)

## The Prizes

As is now tradition with Supabase Hackathons, the winners will receive an [extremely limited edition gold medal shirt](https://supabase.store/products/hacktoberfest-gold-tee).

![Gold Tshirt](https://cdn.shopify.com/s/files/1/0571/6125/3019/products/gold-shopify-hacktoberfest_940x.png)

The same goes for the [silver medal shirts](https://supabase.store/products/hacktoberfest-silver-tee).

![Silver Tshirt](https://cdn.shopify.com/s/files/1/0571/6125/3019/products/silver-shopify-hacktoberfest_940x.png)

### Participation Prize

It was extremely tough to pick the winners because of all the stellar submissions. As a thank you for sharing your fabulous projects with us and the community, we've decided to award a [limited edition participation tee](https://supabase.store/products/hacktoberfest-participation-tee) to everyone who submitted their projects to the hackathon. We'll be contacting everyone via Twitter or email in the coming days with instructions of how to redeem your extremely limited and valuable shirts.

![Participation Tshirt](https://cdn.shopify.com/s/files/1/0571/6125/3019/products/entrant-shopify-hacktoberfest_940x.png)

## Hang out with us and the community

If you want to join the community and build with us, find other people using Supabase, or if you just want to chill and watch people build, come and join us in Discord!

[Join our Discord](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Get Started Guides

If you're inspired to build, here's a collection of resources that will help you get started building with Supabase:

- [Examples and Resources](/docs/guides/examples)
- [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]
- [Flutter Quickstart Guide](/docs/guides/with-flutter)
- [Nextjs Quickstart Guide](/docs/guides/with-nextjs)
- [Using Supabase inside Replit](/blog/using-supabase-replit)
- [Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)
- [Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]
- [Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)
- [How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]
- [Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]
- [Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)
