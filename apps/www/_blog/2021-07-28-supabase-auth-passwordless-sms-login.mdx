---
title: 'Supabase Auth v2: Phone Auth now available'
description: Phone Auth is available today on all new and existing Supabase projects.
author: kangmingtay
author_url: https://github.com/kangmingtay
author_image_url: https://github.com/kangmingtay.png
image: auth-v2/supabase-auth-v2-og.jpg
thumb: auth-v2/supabase-auth-v2-cover.jpg
categories:
  - product
tags:
  - launch-week
  - auth
date: '2021-07-28'
toc_depth: 3
video: https://www.youtube.com/v/akScoPO01bc
---

Since launching Supabase Auth [last summer](https://news.ycombinator.com/item?id=24072051) it's proven to be a key part of the Supabase Stack.
We receive a constant stream of feature requests and community PRs resulting in a long
list of external providers including GitHub, Discord, Azure, Apple and more.

Supabase Auth is similar to Auth0 and Firebase Auth with one major difference - the user data lives in your own database,
reducing lock-in, and making the auth system more extensible. You can write native PostgreSQL Row Level Security policies to
determine which data your users should (or should not) have access to. It can even be used in conjunction with other Supabase features,
such as [Storage](/storage), to control access for specific files and buckets.

Today we're announcing some major new features for our [fork](https://github.com/supabase/gotrue) of Netlify's [GoTrue](https://github.com/netlify/gotrue) Auth server.

## Phone Auth is here!

Your users can now log in using their mobile with SMS-based OTPs (one-time password).

### Passwordless SMS login

Users can log in using a passwordless SMS based OTP with `supabase-js`, or directly with the Auth API.

![Passwordless SMS login](/images/blog/auth-v2/passwordless-login.png)

After logging in, the user will receive a six-digit One Time Password. The OTP can be easily verified.

![Use SMS and password to login](/images/blog/auth-v2/verify-otp.png)

### SMS login with passwords

Phone Auth can be used in conjunction with a password. Using this flow, your users can subsequently log in
with either an OTP or a phone + password combo.

![SMS login with passwords](/images/blog/auth-v2/phone-and-password-login.png)

### Choose an SMS Provider

Supabase Auth supports [Twilio](https://www.twilio.com/) as an SMS provider, with more options coming soon.
Simply plug your Twilio credentials into your Auth Settings in the Supabase Dashboard to get started.

Check out the [documentation](/docs/guides/auth/phone-login/twilio) to get started with Mobile OTPs,
or watch the [Youtube guide](https://youtu.be/akScoPO01bc).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/akScoPO01bc"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Multi-Factor Auth coming soon

Phone Auth is available today on all new and existing Supabase projects. We've also laid the groundwork for mobile Multi-Factor
Auth and will be offering that as an option soon.

## Even more OAuth providers

The community has contributed tons of OAuth providers, and today we're announcing two more.

Shoutout to [@ph1p](https://github.com/ph1p) who contributed [Twitch](/docs/guides/auth/auth-twitch) as our latest OAuth provider!
The Supabase team added [Discord](/docs/guides/auth/social-login/auth-discord) last month, bringing the total OAuth Providers to ten.

![All Supabase OAuth providers](/images/blog/auth-v2/supabase-oauth-providers.png)

You can request more providers on our [Auth repo](https://github.com/supabase/gotrue) and Pull Requests are, of course, always welcome.

## Generate Confirmation Links

To make life easy for developers, the Supabase hosted platform manages all Auth-related emails, including confirmation,
recovery, invite, and passwordless "magic-link" emails. The templates are customizable and we even offer the ability to bring your own SMTP provider.

Some of our power users require a little more flexibility however. We've had a lot of requests to dynamically generate email content,
especially for sending internationalized emails. To handle situations like these, today we're adding the ability to
generate confirmation, invite, recovery, and magic links via an API endpoint.

We've exposed this functionality in `supabase-js`, and it can be invoked with the use of your `service_role` admin key
(which means you should only be calling this function from a backend and not from the client itself).

![Generate Confirmation Links](/images/blog/auth-v2/generate-links.png)

## What's next?

The next major item on the list is MFA (Multi-Factor Authentication) - which includes TOTP
([Time-Based One Time Password](https://en.wikipedia.org/wiki/Time-based_One-Time_Password)).

Find out how Mobbin is using Supabase Auth to [manage 200,000 users](/blog/mobbin-supabase-200000-users).

Anything else you want to see or can help implement in Auth? Reach out on
[Discord](https://discord.supabase.com/) and give Auth a try by [creating a project](https://supabase.com/dashboard/) on Supabase!
