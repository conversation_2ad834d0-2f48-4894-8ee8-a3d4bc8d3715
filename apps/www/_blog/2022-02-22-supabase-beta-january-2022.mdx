---
title: Supabase Beta January 2022
description: New auth providers, SMS providers, and new videos.
author: paul_copplestone
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: 2022-january/thumb.jpg
thumb: 2022-january/thumb.jpg
categories:
  - product
tags:
  - release-notes
date: '2022-02-22'
toc_depth: 3
video: https://www.youtube.com/v/codAs9-NeHM
---

This month's beta update is more stacked than the Superbowl (\*Supa-bowl) halftime show. Here’s all of the highlights from January...

## New OAuth providers

![New 0Auth Providers](/images/blog/2022-january/new-auth-providers-supabase_monthly-email-jan-2022.png)

We’re continually amazed by how quickly new auth providers are being added into Supabase, and last month, 2 more have been added.

### Notion

Added by [zernonia](https://github.com/zernonia).
You may already know him as the maintainer of [madewithsupabase.com](https://www.madewithsupabase.com/).

### LinkedIn

Added by [riderx](https://github.com/riderx).
You may already be familiar with <PERSON> from his podcast [Indie Makers](https://podcasts.apple.com/us/podcast/indie-makers/id1488437972).

## New SMS providers

![New SMS Providers](/images/blog/2022-january/new-sms-providers-supabase_monthly-email-jan-2022.png)

Along with the new 0Auth providers above, last month saw the addition of two more SMS phone providers to allow you to authenticate users via an SMS OTP (One-Time Password) token.

### Vonage

**[Vonage](https://www.vonage.com/)** is a US-based cloud communications provider.
Added by [devkiran](https://github.com/devkiran) (from [BoxyHQ](https://twitter.com/BoxyHQ)).

### Textlocal

We are now fulfilling a popular request with this Indian-compliant SMS provider, [Textlocal](https://www.textlocal.com/).
Also added by [devkiran](https://github.com/devkiran).

### Other SMS providers

Just a reminder, we also support [Twilio](https://supabase.com/docs/guides/auth/phone-login/twilio) and [MessageBird](https://supabase.com/docs/guides/auth/phone-login/messagebird).

On a final note, we are hiring for an [Auth Engineer](https://about.supabase.com/careers/auth-engineers).

## Query logs with SQL

Supabase logs are more powerful with the [newly added SQL querying](https://github.com/supabase/supabase/pull/4734).

We added [timestamp filtering](https://github.com/supabase/supabase/pull/4904), and you’ll notice our usage charts have [more time spans available](https://github.com/supabase/supabase/pull/4732).

We’re enabling developers to quickly diagnose issues with their projects with powerful logging and observability tools and we have a lot more to come.

## GraphQL v0.1.0

![Graph QL v0.1.0](/images/blog/2022-january/pg_graphql_0.1.0_monthly-email-dec-2021.png)

Last month we released [`pg_graphql`](https://github.com/supabase/pg_graphql) v0.1.0, which includes [Comment Directives](https://supabase.github.io/pg_graphql/configuration/#comment-directives).

We haven’t released GraphQL onto the platform yet because we it's still under heavy development. You can expect availability in the next few months.

Example

```sql
create table account(
    id serial primary key
);

comment on table public.account is
e'@graphql({ "name": "AccountHolder" })';
```

Result

```jsx
// Renames "Account" to "AccountHolder"
type AccountHolder {
  id: Int!
}
```

## New examples

### Remix Auth

It’s all anyone seems to be [talking about](https://twitter.com/jkup/status/1456360115205033989). We genuinely love what [Remix](https://remix.run/) are doing, so it’s only right that we show off how Remix and Supabase work well together.

Check out the new [Remix Auth example](https://github.com/supabase/examples/tree/main/supabase-js-v1/auth/remix-auth), and let us know what you think.

### Expo Todo List

Our React Native example has been correctly updated to be an Expo example.
[Check it out here](https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/expo-todo-list).

## Video: API requests with Database Webhooks

There's no stopping [Jon Meyers](https://jonmeyers.io/videos)! He’s back with an in-depth video on how to easily [automate API requests](https://www.youtube.com/watch?v=codAs9-NeHM&feature=emb_title) using our very own Database Webhooks.

You'll learn how to listen to _any_ database change, then send those changes [in a payload](https://supabase.com/blog/supabase-functions-updates#hook-payload) via HTTP request.

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/codAs9-NeHM"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Set your own support ticket priority

As with any platform, there can be the occasional glitch.

The Supabase Dashboard includes a dedicated support form that goes straight to our support inbox. This support form includes your project information and,
since we all want to see your issues solved, we thought it would make sense that _you_ could set the priority of your support tickets.

This change has drastically improved response times for urgent support tickets.
The form includes extra “urgent” levels for the PRO and Pay As You Go projects.

And, as part of our continued commitment to Support, we are hiring [Support Engineers](https://about.supabase.com/careers/support-and-qa).

## Community

As always, the community has been amazing during the month of February.

### Supabase + Snaplet

Are you maintaining multiple environments? Snaplet helps you copy a production database and clone it into different environments.

Check out the [Supabase clone environments](https://docs.snaplet.dev/tutorials/supabase-clone-environments) tutorial on the Snaplet docs.

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/oPtMMhdhEP4"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Supabase + Retool

Retool has put together a brilliant 10 minute admin panel setup using Supabase with Retool.

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/AgB2-CSrnoI"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Learn with Jason

Jason Lengstorf caught up with our very own [Jon Meyers](https://jonmeyers.io/) on his awesome show, [Learn with Jason](https://www.youtube.com/watch?v=8vqY1KT4TLU), to talk about building an app [with Supabase and Next.js](https://supabase.com/docs/guides/with-nextjs).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/8vqY1KT4TLU"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### New article highlights

- Arctype published a new guide showing how to connect to your Supabase database. [Link](https://arctype.com/postgres/connect/supabase-postgres)
- Marmalabs built a Supabase adapter for react-admin, the frontend framework for building admin applications on top of REST/GraphQL services. [Link](https://github.com/marmelab/ra-supabase)
- HotGlue created a guide showing how easy it is to integrate Salesforce and Supabase. [Link](https://www.notion.so/Supabase-and-hotglue-article-9e7f5583d27c419490ee7d536d6d269d)

### PostgREST

One of Supabase’s key tools that allow a lot of our functionality has had some updates.

- `pg_listen` was removed in favor of PostgREST’s built-in schema reloading.
- PostgREST database connection pool size gets scaled with compute size for better performance for certain workload shapes.

## Coming Next: Launch Week 4

Preparation for Launch Week 4 is underway!

Of course, we can’t tell you what will happen (perhaps because we don’t know ourselves yet), but you can always speculate in the community [Discord server](https://discord.supabase.com/), or even [tweet us your predictions](https://twitter.com/supabase).

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
- Become a **[sponsor](https://github.com/sponsors/supabase)**
