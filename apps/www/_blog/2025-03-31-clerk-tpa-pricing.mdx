---
title: 'Supabase Auth: Bring Your Own Clerk'
description: Use Clerk with Supabase via official third-party auth support
categories:
  - launch-week
  - product
tags:
  - launch-week
  - auth
date: '2025-03-31T00:00:00'
toc_depth: 3
author: stojan
image: lw14-clerk-tpa-pricing/clerk-tpa-pricing-og.png
thumb: lw14-clerk-tpa-pricing/clerk-tpa-pricing-thumb.png
launchweek: 14
---

Today we're expanding our official Third-party Auth integrations to include [Clerk](https://clerk.com).

[Third-party Auth](/docs/guides/auth/third-party/overview) allows you to use external Auth providers with the Supabase as a drop-in replacement for Supabase Auth. This modular design is [intentional](/docs/guides/getting-started/architecture#product-principles), allowing you to pick and choose features of Supabase. Our platform makes it easy to get started with Postgres and _any_ of your favorite tools.

![Clerk TPA diagram](/images/blog/lw14-clerk-tpa-pricing/clerk-tpa-diagram.png)

It was [already possible](/partners/integrations/clerk) to use Clerk with Supabase, however the previous method was a bit of a hack that required sharing your project's secret and JWT templates from <PERSON>. We've worked with the Clerk team on the new implementation. Now you can enjoy better security and the same developer experience you've come to expect from Supabase.

To get started with Clerk and Supabase, visit Clerk's [Connect with Supabase](https://dashboard.clerk.com/setup/supabase) page.

Register your Clerk domain [in the Supabase Dashboard](/dashboard/project/_/auth/third-party) or in the CLI:

```toml
[auth.third_party.clerk]
enabled = true
domain = "example.clerk.accounts.dev"
```

In your JavaScript app all you need to do is write the following code:

```tsx
import { createClient } from '@supabase/supabase-js'

const SUPABASE_URL = 'https://<supabase-project>.supabase.co'
const SUPABASE_ANON_KEY = '<SUPABASE_ANON_KEY>'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  accessToken: () => {
    return Clerk.session?.getToken()
  },
})
```

[Read the docs](/docs/guides/auth/third-party/clerk) to set up Flutter and Swift (iOS) applications, and to learn how to use Postgres Row-level Security (RLS) Policies.

## Third-Party Auth is now a lot cheaper

One more thing: today we're making Third-party Auth cheaper so that it has pricing parity with Supabase Auth.

You can have up to 50,000 MAU on the Free plan, or 100,000 MAU on the Pro plan and $0.00325 per MAU above that number.

|            | Free Plan            | Pro Plan              |
| ---------- | -------------------- | --------------------- |
| Previously | 50 MAUs included     | 50,000 MAUs included  |
| Now        | 50,000 MAUs included | 100,000 MAUs included |

## Get started today

Supabase Auth makes it easy to implement authentication and authorization in your app. We provide client SDKs and API endpoints to help you create and manage users.

- [Read the documentation](/docs/guides/auth/third-party/overview) for Third-party Auth
- [Learn how to use Clerk with Supabase](/docs/guides/auth/third-party/clerk)
- [Sign up for Supabase](/dashboard/sign-up) and get started today
