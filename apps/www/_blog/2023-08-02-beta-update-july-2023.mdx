---
title: Supabase Beta July 2023
description: Launch Week 8 is coming - but we still shipped some goodies during July
author: ant_wilson
image: 2023-08-02-july-2023-beta-update/july-2023-beta-update.png
thumb: 2023-08-02-july-2023-beta-update/july-2023-beta-update.png
categories:
  - product
tags:
  - release-notes
date: '2023-08-02'
toc_depth: 3
---

Next Monday is Launch Week 🎱, where we’ll be announcing a tonne of surprises. In the meantime, here’s a summary of everything we shipped in July. Read on!

## Supabase Launch Week 8

![Supabase Launch Week 8](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/launch-week.png)

Launch Week 8 starts next week. We’ll be announcing one new major feature every day for an entire week (with a few bonus surprises along the way). Get your ticket and tune in on Monday the 7th.

[Get your ticket](https://supabase.com/launch-week)

## New Android Kotlin Library

![New Android Kotlin Library](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/android-kotlin.png?t=2023-08-02T11%3A05%3A46.805Z)

Are you a mobile dev? We now have client libraries for Android. This is thanks to one of our community members - [@TheRealJanGER](https://twitter.com/TheRealJanGER). Try it out, and if you want to help us maintain the library let us know.

[Quickstart](https://supabase.com/docs/guides/getting-started/quickstarts/kotlin)

[Tutorial](https://supabase.com/docs/guides/getting-started/tutorials/with-kotlin)

## pgvector 0.4.0 performance

![pgvector 0.4.0 performance](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/pgvector.png)

There's been a lot of talk about pgvector performance, so we pushed it to the limits to figure out its strengths and limitations. The good news is that work has already [started](https://github.com/pgvector/pgvector/tree/hnsw) on HNSW, and isn’t far away.

[Read the benchmark](https://supabase.com/blog/pgvector-performance)

## Quick product announcements

- [Auth] There is a new OAuth provider in time: Login with Figma. [Docs](https://supabase.com/docs/guides/auth/social-login/auth-figma)
- [Auth] You can now use Resend as a custom SMTP provider. [Docs](https://resend.com/docs/send-with-supabase-smtp)
- [Postgres Tooling] pgsodium 3.1.8 released. [PR](https://github.com/michelp/pgsodium/releases/tag/v3.1.8)
- [Postgres Tooling] PostgREST 11.1 is now live. [Blog post](https://supabase.com/blog/postgrest-11-1-release)

## Egghead course: Twitter Clone with the Next.js App Router

![Egghead course: Twitter Clone with the Next.js App Router](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/egghead-course.png?t=2023-08-02T10%3A55%3A17.908Z)

Should we rebrand to X Clone? Next.js App router is ushering in the next era of web development. What a best way to learn to navigate it than by building a full-stack app?

[Course](https://egghead.io/courses/build-a-twitter-clone-with-the-next-js-app-router-and-supabase-19bebadb)

## Twitter Space: Launch Week 8 - Kickoff 🏈

![Twitter Space](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/twitter-space.png?t=2023-08-02T11%3A05%3A03.838Z)

After Monday's announcement, we are kicking off LW8 with a very cool Twitter Space, featuring the DevRel team and guests. Bring your questions!

10:00 AM PT | 1:00 PM ET | 06:00 PM UK

[Set reminder](https://twitter.com/i/spaces/1DXGyvjkeEeJM)

## Made with Supabase

![Made with Supabase: Snowchat](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/snowchat.png)

[snowChat](https://snowchat.streamlit.app/) | An intuitive and user-friendly application that allows you to interact with your Snowflake data using natural language queries. Built by [Kaarthik Andavar](https://twitter.com/kaarthikcodes) using Python, Streamlit, Open AI, and Supabase Vector.

## Extended Community Highlights

![Community Highlights](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/community-highlights.png)

- Build a Simple Dropbox clone using Deno, DenoKV, Fresh and Supabase. [Video Tutorial](https://www.youtube.com/watch?v=_WerF8YMqks)
- Safeguarding Data Integrity with pg_safeupdate in PostgreSQL and Supabase. [Blog post](https://www.notion.so/809925346af243b3a4ede8774e71472c?pvs=21)
- Supabase for C#: Streamlining Backend Development with Database Magic. [Blog post](https://themurph.hashnode.dev/supabase-csharp)
- Building an MVP app using Supabase. [Blog post](https://www.antstack.com/blog/building-an-mvp-app-using-supabase/)
- Fortify Your Application With Supabase: Exploring Fine-Grained Control with Row-Level Security and OAuth Sign-in. [Article](https://techconative.com/blog/supabase-rls-security-oauth-authentication/)
- Using Supabase authentication in NestJs. [Tutorial](https://blog.iamstarcode.com/using-supabase-authentication-in-nestjs)
- React Native file upload with Supabase Storage. [Tutorial](https://supabase.com/blog/react-native-storage) | [Video](https://www.youtube.com/watch?v=am6w5zEDk_g)
- Safeguarding Data Integrity with pg_safeupdate in PostgreSQL and Supabase. [Blog Post](https://blog.mansueli.com/safeguarding-data-integrity-with-pg-safeupdate-in-postgresql-and-supabase)
- Supabase almost SHUT ME DOWN. [Video](https://www.youtube.com/watch?v=2WHTZa4ENfc)

## Supabase in Times Square

![Supabase in Times Square](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/times-sq.png?t=2023-08-02T11%3A04%3A14.458Z)

The Redpoint team put together InfraRed 100, a report that recognizes 100 transformative companies in cloud infrastructure. We are honoured to be included with so many amazing companies we admire 🙇🏻‍♂️. They even featured us on a billboard in Times Square to celebrate.

[Read the full report](https://www.redpoint.com/infrared/100/)

## We’re Hiring

- [Support Engineer (APAC)](https://boards.greenhouse.io/supabase/jobs/4934378004)
- [FullStack Engineer Billing](https://www.notion.so/Beta-Update-July-Monday-31st-10386eba40934ea597ee5e5b1b81574e?pvs=21)

## ⚠️ Baking hot meme zone ⚠️

If you made it this far in the email you deserve a devilish treat.

NPCs represent…

![July's favorite meme](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/marketing-emails/supabase-meme.png)
