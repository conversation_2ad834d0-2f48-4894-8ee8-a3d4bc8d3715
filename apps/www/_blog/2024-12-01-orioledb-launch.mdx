---
title: 'OrioleDB Public Alpha'
description: 'Launching OrioleDB Public Alpha'
author: pavel
image: 2024-12-01-orioledb-release/og.png
thumb: 2024-12-01-orioledb-release/thumb.png
categories:
  - engineering
tags:
  - supabase-engineering
date: '2024-12-01'
toc_depth: 3
launchweek: '13'
---

# OrioleDB Public Alpha

Today, we're releasing the Public Alpha of [OrioleDB](https://www.orioledb.com/) on the Supabase platform.

### What’s OrioleDB?

OrioleDB is a **storage extension** which uses Postgres' pluggable storage system. It’s designed to be a drop-in replacement for Postgres’ default Heap storage.

You can read more about OrioleDB [here](https://www.orioledb.com/blog/orioledb-beta7-benchmarks) and learn why you might choose it over the default Postgres storage engine.

### Limitations

This initial release is a Public Alpha and you should _not_ use it for Production workloads. The release comes with several limitations:

- The release is restricted to Free organizations. You will not be able to upgrade OrioleDB projects to larger instance sizes. If you want to run OrioleDB on a larger instance we suggest following the [Getting Started](https://www.orioledb.com/docs/usage/getting-started) guide on OrioleDB’s official website.
- Index support is restricted to the Postgres default B-Tree index type. Other indexs like GIN/GiST/BRIN/Hash, and pgvector's HNSW/IVFFlat are not supported.

### Should you use it today?

At this stage, the goal of adding OrioleDB to the platform is to make it easier for testers to give feedback. If you’re running Production workloads, stick to the standard options available.

### Getting started and more info

To get started today, go to [database.new](http://database.new) and choose “Postgres with OrioleDB” under the "Advanced Configuration" section when launching a new database.

<Img
  alt="orioledb-project-create"
  src="/images/blog/2024-12-01-orioledb-release/orioledb-project-create.png"
  captionAlign="left"
  zoomable={false}
/>

If you want to learn more about OrioleDB and their vision for the future, check out the [blog post the OrioleDB team released today](https://www.orioledb.com/blog/orioledb-beta7-benchmarks).
