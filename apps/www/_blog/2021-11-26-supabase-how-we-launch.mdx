---
title: 'How we launch at Supabase'
description: The history and methodology of Supabase Launch Week.
author: ant_wilson
author_url: https://github.com/awalias
author_image_url: https://github.com/awalias.png
image: how-we-launch/how-we-launch-og.jpg
thumb: how-we-launch/how-we-launch-thumb.jpg
categories:
  - company
tags:
  - tech
date: '2021-11-26'
toc_depth: 3
---

Next week is Supabase Launch Week. It's our third Launch Week this year, and is a key part of a Product-Led Growth strategy that has enabled us to increase the number of databases we manage 47% month-on-month for the last 18 months.

We often get asked about how we're able to ship so relentlessly, and being an open source company we thought it appropriate to start "open sourcing" some of our methods around building and shipping. Our user base is constructed of companies and individuals who themselves are building for the web and marketing to enormous audiences. Hopefully they can learn some of our tricks and in turn contribute back to the community with their own launch strategies and tactics, helping us continuously learn and improve.

Before I go into the nuts and bolts of what Launch Week is, and the exact processes we follow when executing one, it's probably useful if I explain how we landed on this methodology in the first place.

![supabase monthly growth](/images/blog/how-we-launch/total-databases-launched.jpg)

### A brief history of growth at Supabase

We started work on Supabase in January 2020. Our growth plan from the start included being accepted into YCombinator. Dev Tool companies who enter YC gain a huge advantage from day one. You're placed in a cohort of hundreds of other early-stage startups, the majority of whom are developing software, and have just been given a bunch of funding, making them ideal early customers! This, however, turned out to be only a small part of what YC ultimately gifted us.

At the time we joined the batch, we had around 80 alpha users of varying levels of activeness. Our plan was to spend the batch iterating the product around these early alpha users, and to do a big public launch a week or so before Demo Day (an event where you pitch to a shiver of investors). Our plan was foiled however, when an early user shared our site on Hacker News. The post stayed on the home page for several days, and ended up being one of the most upvoted dev tools launches ever (second only to Stripe!). Overnight, the number of databases we were hosting increased ten-fold. Bugs, breakages and feature requests flooded in from every angle; a fantastic problem to have for a fledgling startup. Over the course of the next 2 months we worked hard on a few major items, including Supabase Auth, and migrating our entire stack between cloud providers, along with hundreds of other smaller fixes and incremental improvements - both to our product, and to our company.

A few weeks before Demo Day, we announced Auth, and shouted about it as loudly as possible. We posted it everywhere, and recruited our recently onboarded squad of [Technical Angel Investors](https://supabase.com/company#investors) to help us boost our message on social media. The response was solid, and led to an increase in the rate of user acquisition, and activation/retention rates.

Post YC can often be a sink or swim moment for companies. Throughout the batch, you have regular check-ins with world-class operators, your batch mates, and inspirational talks from founders several steps ahead. When you leave, the external pressure drops, and it's up to you to put the frameworks in place to maintain your rate of progress. You no longer have the likes of Michael Siebel asking you why it's taking you so long to get Auth out the door and in the hands of users. When faced with this problem we sat down and said, "why don't we just pretend that we're starting the batch again, and do our best to recreate the conditions of an accelerator internally, complete with our very own Demo Day?". We found the actual YC Demo Day is kind of an arbitrary deadline useful for motivating all kinds of internal initiatives, whether it's fundraising, growth, or product. So we said to the team, let's just choose an arbitrary date 3 months from now, and we'll ship _something_ huge.

We went the whole hog. We had kick-off events, we invited external people to come in and talk to the team about their origin stories, and even had custom swag made up specific to this product cycle. The _something_, turned out to be [Supabase Beta](https://supabase.com/beta), an announcement that marked a huge advancement in the Stability, Performance, and Security of our hosted platform. This announcement resulted in a stark increase in the rate of developer sign-ups and activation. Our "fixed timeline, flexible scope" approach to launching products had proved successful with the team. And what's more, the build-up to launch, and the launch itself, was some of the most fun any of us had had. Our small team was flying high on adrenaline, and everyone could see for themselves the immediate and direct impact their work had on Supabase's viral adoption.

The very next week, still high on adrenaline, we met for a retrospective. The debrief, however, quickly turned into a planning meeting, and the question was raised "what is the MOST ambitious thing we could hope to ship 3 months from now?". A few projects got kicked about until someone suggested, "why just have one launch? Why can't we aim to ship one major feature or announcement every day for a week?". It was quickly agreed that Launch Day would instead become Launch Week.

During the last week of March 2021, we went on to ship [7 major features](/blog/launch-week#friday-one-more-thing). We once again saw an immediate uptick in growth rate which helped solidify our strategy.

More recently we ran "[Launch Week II: The SQL](/blog/supabase-launch-week-sql)", where we bumped the versions of several key products in our stack, and added a [Community Day](https://supabase.com/blog/supabase-community-day) with the aim of boosting visibility and support of various open source tools and projects that we rely on, along with products, frameworks, and tooling built by the rapidly expanding [Supabase Community](https://github.com/supabase-community/).

We're now just a few days away from Launch Week III, and over time have incrementally improved upon many of the processes and tactics that help us maximize output whilst minimizing stress on the team. So let's get into some specifics about how we run a product cycle and the resulting Launch Week.

### The Planning Meeting

Spanning 12 different countries makes it challenging to find a time that works for everyone, but gathering together online is the first step.

We always start the meeting by firing up a Google Jam Board and collectively constructing "The Supabase Universe", this is a giant brain dump of terms and concepts related to Supabase. What we're building, who it's for, who we're trying to hire, what our goals and metrics are. Literally everything that's anything to do with Supabase goes down on this virtual whiteboard. The purpose is to ensure everyone gets out of their daily mode of operation and loads up the business as a whole into their brains. Developers should be loading up marketing concepts, and the marketers should be thinking about engineering. If we're overly focused on one particular area (often engineering), then we risk setting a course in the wrong direction for the entire quarter. It's also a method of getting all the "obvious" stuff down on paper so you can move on and focus the creative efforts of everyone on new ideas; a technique borrowed from the creative industries.

![supabase monthly growth](/images/blog/how-we-launch/supabase-universe.jpg)

Once everyone is warmed up, we dive into the high-level goals and metrics. Are our metrics still relevant? And if so, which ones should we be aiming to boost and why? The more direct a connection between actions and metrics the better, so it's important to remind everyone of what the north star is before we start scheming on how to reach it.

Then comes the main course - what are we going to build or do that's going to move the needle? List down as many ideas as possible, and go through a process of sorting and filtering as you go. Try not to go too deep on any particular idea, in-depth analysis and exploration can be done later in break out sessions, make sure you have someone responsible for pulling you out of these rabbit holes if the team starts to get drawn in to too much detail.

For us, an essential part of this process is the idea that the scope is flexible - at this stage of ideation it's often impossible to tell whether an engineering project is going to take 2 or 102 days. We will always try our best to have it ready for Launch Week, but if not no worries, we'll shout about something else, and include it in the next one.

Lastly we will give some discussion to hiring and any other business, and schedule any follow-up meetings or breakouts required.

### The Kick Off Meeting

After the breakouts we typically have a much clearer idea of what we want to aim for, and if the projects themselves are realistic. Our list at this point often sits around 12 items in length. Some projects will drop out and others will be introduced during the course of the next few months, which is ok. Each project typically has a lead assigned, and another couple of people will register their interest in helping out.

For the kick-off, I always try and come up with some grotesquely garish branding (this is an internal project after all) and find some loose theme to roll with. The purpose of the meeting is to remind everyone of the goals, the timeline, and the current list of announcements we plan to make during the launch.

![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-stickers.jpg)

### Time to Work

Then it's time to get our heads down. One important point to make here is that the 3-month cycle doesn't necessarily replace more traditional project management methodologies. We leave it up to the project leads to decide how to run their project. Some teams opt for kanban boards, and stand-ups. Other projects run like open source repos, with RFCs, issue management, and fully async comms.

Another key thing to mention here is that we very rarely hold back features until Launch Week. Actually, quite the opposite is true, we encourage everyone to ship features as early as possible. We announce features as they ship in our monthly Beta Update blog and newsletter - which is often limited to a blast radius containing existing users. Full write-ups, marketing copy, and broad Top-of-Funnel type marketing efforts will often happen during Launch Week to hype up these features. Actually, we've found that you can launch a new feature many times over and always manage to reach people who either forgot, ignored, or just plain missed it the first few times. We're so close to our own projects that we often overestimate the reach of our online marketing efforts.

In the weeks leading up to Launch Week, we review more closely the list of items we want to Launch and start to plan for what supporting materials we'll need. There are some things such as press, which needs to be organized up to 3 weeks before you plan to Launch, so you better get started early on those items. Also being aware of which third parties are going to be involved is important. Maybe we're shipping an integration with some other tool or company, cross-company communication can be a blocker, so better to start early.

![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-timeline.jpg)

### Pre-Launch Week

One of our learnings from previous Launch Weeks was to no longer ship to prod on the day of the Launch itself :). We had a blast live-debugging prod issues on Discord last time, but this was not so conducive to getting enough sleep. Our Launch Weeks have become a marathon, so it's ideal to get all the major integrations done a week early. Also writing the content and getting someone to edit can take a couple of iterations. This all seems obvious written down here, but it took us a few iterations to start being more regimented.

We have more regular check-ins throughout this week, and make sure everyone is on the same page. We have a process of "swarming" around sticky issues and blockers and a good amount of testing will be done by people who were not involved in the development of a given feature. Particularly around DX, which is an extremely high priority topic at Supabase.

### Launch Week

I can't think of many things more fun than executing on a Supabase Launch Week. It's the culmination of 3 months of epic work from a highly skilled team of folks distributed across every corner of the planet. The awesome thing is that, with most of the features we ship, the person who implemented the code will be the same person who writes the marketing content. This means that the content usually includes deep technical discussion. They're also probably the best person to decide which will be the most effective channels to market that particular feature. As an example, when we launched [ui.supabase.com](http://ui.supabase.com), we made sure to hit up all the front end and design channels, made sure it ranked on Product Hunt, and reached out to some friendly designers to help boost our message. This is very different from the approach we took to marketing our [file storage offering](https://supabase.com/blog/supabase-storage). The technical design discussion of Storage is better suited to channels like Hacker News, and technical conference audiences who may be more interested in DX than UI components.

When it comes to the day of each launch we've gotten into the habit of constructing a finely detailed schedule to follow. It will contain items such as:

- 7:30am Twitter spaces reminder tweet
- 7:55am Product Hunt goes live
- 8:00am Blog post goes live
- 8:05am Post launch tweet 1
- 8:10am Share tweet with Angels
- 8:15am Twitter spaces go live

Of course, it's possible to run this all without such a fine-grained schedule, but the launches can get kind of hectic, so it's easier to not have to think - just do.

### The Rest

Despite being a ton of fun, it's inevitable that some team members will have pulled long days and nights to get their feature out the door, as well as pumping it on as many marketing channels as possible. Launching new things also includes managing support requests, bug fixes, responding to questions, and even managing PRs from the community excited to contribute to new repos or areas of the codebase. This is unsustainable without proper rest so each cycle ends with a good amount of downtime and maintenance mode. Having said that, the adrenaline of Launch Week often spills over into the following days and weeks, so it's not uncommon for members of the team to continue in Launch Mode for a while before taking some downtime.

### The Retrospective

Once the dust has settled, the **most** **important** phase of the entire process can begin. To skip the retro would be to rubbish the entire process, as we would never learn from mistakes or adapt to capitalize on new tactics discovered throughout the preceding weeks and months. We always make sure to cover what went well, what went badly, and what can be improved. This list is then read aloud again at the start of the next planning meeting. The process as it exists today is the product of 4 previous iterations, and is still far from its final form. Amongst other things, our team is growing fast, so it's yet to be seen if the same processes will extend to so many team members, and in which ways they will have to adapt. Lastly, we always make sure to discuss the question "Is Launch Week still relevant?", and "How would we know if we were approaching some local maxima in terms of process and productive output?".

![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-retrospective.jpg)

### Some Other Thoughts

This "internal accelerator" model has worked well within Supabase as a 2-person team and is still proving effective now at 30, but there's no telling whether it would work as well in other organizations. I would be very keen to learn from those who also do something similar.

In some ways, running Supabase is like launching many startups at the same time, we have to build and maintain many different parallel projects in order to match Firebase's incredible suite of products, which could be one reason why it lends itself so well to this acceleration model. And, in fact, being a Firebase alternative is only a small part of [what we want to achieve with Supabase](https://supabase.com/blog/supabase-series-a), so we have many, many more initiatives to run as we grow.

Running an accelerator model may also be why we've had success [hiring so many other founders](https://about.supabase.com/careers/founders). We're able to give a high degree of autonomy to individuals when it comes to managing projects and initiatives. If this kind of work environment sounds interesting to you, we have tons of [open roles](https://about.supabase.com/careers). We've already hired people who joined because they "couldn't stand to watch another Launch Week from the sidelines" and team members often quote experiencing their first Launch Week as one of the highlights of their Supabase journey so far. It clearly has benefits that extend beyond just growth and user activation.

Make sure you follow us on [Twitter](https://twitter.com/supabase), join our [Discord](https://discord.supabase.com), or [sign up for Supabase](https://supabase.com/dashboard) in order to keep up to date on all things Supabase.

Supabase is the Open Source Firebase Alternative.
