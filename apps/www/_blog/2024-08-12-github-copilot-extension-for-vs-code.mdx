---
title: Official Supabase extension for VS Code and GitHub Copilot
description: Today we're launching a new GitHub Copilot extension for VS Code to make your development with Supabase even more delightful.
author: anas-araid,thor_schaeff
image: lw12/day-1/github_copilot_extension-og.png
thumb: lw12/day-1/github_copilot_extension-thumb.png
launchweek: '12'
categories:
  - launch-week
  - developers
tags:
  - launch-week
date: '2024-08-12'
toc_depth: 3
---

Today we're launching a new [GitHub Copilot extension for VS Code](https://marketplace.visualstudio.com/items?itemName=Supabase.vscode-supabase-extension) to make your development with Supabase and VS Code even more delightful, starting with a Copilot-guided experience for [database migrations](/docs/guides/deployment/database-migrations).

![Database Migrations Demo](/images/blog/lw12/day-1/copilot_migration.gif)

The foundation for this extension was created by [<PERSON><PERSON>](https://github.com/anas-araid) during a previous [Launch Week Hackathon](https://twitter.com/anas_araid/status/1736641409094988033). Impressed with their work, we partnered with them to add a ["Chat Participant"](https://code.visualstudio.com/api/extension-guides/chat), an exciting [new feature recently launched](https://code.visualstudio.com/blogs/2024/06/24/extensions-are-all-you-need) by the GitHub and VS Code teams at Microsoft.

## Features

The VS Code extension is quite feature rich:

### GitHub Copilot Chat Participant

The extension provides a [Chat Participant](https://code.visualstudio.com/api/extension-guides/chat) for GitHub Copilot to help with your Supabase questions. Simply type `@supabase` in your Copilot Chat and the extension will include your database schema as context to Copilot.

![Copilot Chat integration demo](/images/blog/lw12/day-1/copilot_chat.gif)

### Copilot-guided database migrations

The extension provides a guided experience to create and apply [database migrations](/docs/guides/deployment/database-migrations). Simply type `@supabase /migration <describe what you want to do>` in your Copilot Chat and the extension will generate a new SQL migration for you.

![Copilot guided database migrations demo](/images/blog/lw12/day-1/copilot_migration.gif)

### Inspect tables & views

Inspect your tables and views, including their columns, types, and data, directly from the editor:

![View tables and views](/images/blog/lw12/day-1/table_view.png)

### List database migrations

See the migration history of your database:

![Migration History](/images/blog/lw12/day-1/migration_history.png)

### Inspect database functions

Inspect your database functions and their SQL definitions:

![Database functions](/images/blog/lw12/day-1/database_functions.png)

### List Storage buckets

List the Storage buckets in your Supabase project.

![Storage buckets](/images/blog/lw12/day-1/storage-buckets.png)

## What's Next?

We're excited to continue adding more features that will make your development experience with Supabase even more delightful - and for this we need your help! If you have any feedback, feature requests, or bug reports, please [open an issue on GitHub](https://github.com/supabase-community/supabase-vscode-extension/issues).

The extension requires you to have the Supabase CLI installed and have your project running locally. In a future release, we will integrate the [Supabase Management API](/docs/reference/api/introduction) into the extension to make connecting to your hosted Supabase projects as seamless as possible.

## Contributing to Supabase

The entire Supabase stack is [fully open source](/open-source), including [this extension](https://github.com/supabase-community/supabase-vscode-extension). In fact, this extension was originally created by [Anas Araid](https://github.com/anas-araid) during a [previous Launch Week Hackathon](https://twitter.com/anas_araid/status/1736641409094988033).

Your contributions, feedback, and engagement in the Supabase community are invaluable, and play a significant role in shaping our future. Thank you for your support!

## Resources

- [Install the extension](https://marketplace.visualstudio.com/items?itemName=Supabase.vscode-supabase-extension)
- [Read the source code](https://github.com/supabase-community/supabase-vscode-extension)
- [Submit a Feature Request](https://github.com/supabase-community/supabase-vscode-extension/issues)
- [Get started with Supabase](https://database.new)
