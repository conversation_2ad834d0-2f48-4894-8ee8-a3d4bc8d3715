---
title: 'supabase-js v2 Released'
description: We've released supabase-js v2. Updated examples, quickstarts, and an improved experience.
author: thor_schaeff
image: supabase-js-v2-release/supabase-js.jpg
thumb: supabase-js-v2-release/supabase-js.jpg
categories:
  - product
tags:
  - launch-week
date: '2022-10-20'
toc_depth: 3
---

During our [last Launch Week](/launch-week) we presented the [release candidate for supabase-js v2](/blog/supabase-js-v2). Since then we've been busy incorporating your feedback, [updating the docs](/docs/reference/javascript/), [examples](https://github.com/supabase/supabase/tree/master/examples), [quickstart guides](/docs/guides/with-nextjs), and putting a [migration guide](/docs/reference/javascript/v1/upgrade-guide) together.

## What is new in v2?

Enhanced TypeScript support built right in was the big one! Now you can use the CLI to generate types, directly from your database.

Plus v2 comes with lots of improvements that solved some of the largest pain-points highlighted by our users. Your feedback help us improve: you speak, we listen.

Read all the updates and the differences with v1 on the [announcement blog post](/blog/supabase-js-v2)

## Acknowledgements

It truly takes a village, well, in this case an entire community, and we want to thank you all for your feedback, and contributions!

Version 2.0 is the result of the combined work of several Supatroopers (Alaister, Andrew, Inian, Joel, Jon, Kang, Bobbie, and Tyler), over 100 contributors to our libraries, and over 450 contributors to our docs and websites.

If you're one of those contributors, thank you!

- [`functions-js`](https://github.com/supabase/functions-js/graphs/contributors) (4)
- [`gotrue-js`](https://github.com/supabase/gotrue-js/graphs/contributors) (47)
- [`postgrest-js`](https://github.com/supabase/postgrest-js/graphs/contributors) (30)
- [`realtime-js`](https://github.com/supabase/realtime-js/graphs/contributors) (16)
- [`storage-js`](https://github.com/supabase/storage-js/graphs/contributors) (17)
- [`supabase-js`](https://github.com/supabase/supabase-js/graphs/contributors) (39)

Special shout outs to: [@vejja](https://github.com/vejja), [@pixtron](https://github.com/pixtron), [@bnjmnt4n](https://github.com/bnjmnt4n), and [@karlseguin](https://github.com/karlseguin).

## Links

- [Documentation](/docs/reference/javascript)
- [Migration Guide](/docs/reference/javascript/v1/upgrade-guide)
- [Quickstart Guides](/docs/guides/with-nextjs)
- [Examples (GitHub)](https://github.com/supabase/supabase/tree/master/examples)
- [Release Notes](/docs/reference/javascript/release-notes)
- [Release Candidate Blogpost](/blog/supabase-js-v2)
- [Auth Helpers](/docs/guides/auth/auth-helpers/)
- [Auth UI](https://supabase.com/docs/guides/auth/auth-helpers/auth-ui)
