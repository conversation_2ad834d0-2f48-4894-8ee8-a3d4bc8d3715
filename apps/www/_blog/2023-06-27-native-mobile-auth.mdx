---
title: Native Mobile Auth Support for Google and Apple Sign in
description: Supabase auth adds full support for native mobile sign in with Apple and Google.
author: stojan,tyler_shukert
image: native-mobile-auth/native-mobile-auth.png
thumb: native-mobile-auth/native-mobile-auth.png
categories:
  - product
tags:
  - auth
date: '2023-06-27'
toc_depth: 3
---

Supabase supports OAuth logins with 17 providers including Apple, Google, Microsoft, GitHub, … But for native mobile apps, this meant that developers had to use a web browser to sign in. It’s not an ideal flow for users, who are already used to signing in with the operating system’s native dialogs when possible. Today, we are excited to announce full native support for Sign in with Apple and Google on iOS and Android. But this is not all! Supabase Auth now can now be used with one-tap sign in methods like: Sign in with Apple JS, Sign in with Google for Web or even in Chrome extensions.

## Native Sign in with Apple and Google

Developers of native iOS and Android apps (using Flutter or React Native) can now take advantage of OS-provided authentication dialogs for Apple and Google. This is available on iOS, macOS, tvOS and watchOS apps in the Apple ecosystem, and all Android variants in the Google ecosystem.

In full transparency, this was always sort-of possible but there were some edge cases that were not covered well with Supabase Auth. We’ve since ironed out the developer experience and made this into a fully supported feature.

Behind the scenes, these native sign in methods use ID tokens. They’re a formalized version of a JWT that is issued by Apple or Google and contain profile information. Supabase Auth now can properly validate the ID tokens and create new or link to existing user accounts based on email similarity.

- Sign in with Apple [[Web](/docs/guides/auth/social-login/auth-apple?platform=web) | [React Native](/docs/guides/auth/social-login/auth-apple?platform=react-native) | [Flutter](/docs/guides/auth/social-login/auth-apple?platform=flutter) | [Swift](/docs/guides/auth/social-login/auth-apple?platform=swift) | [Kotlin](/docs/guides/auth/social-login/auth-apple?platform=kotlin)]
- Sign in with Google [[Web](/docs/guides/auth/social-login/auth-google?platform=web) | [React Native](/docs/guides/auth/social-login/auth-google?platform=react-native) | [Flutter](/docs/guides/auth/social-login/auth-google?platform=flutter) | [Android](/docs/guides/auth/social-login/auth-google?platform=android) | [Chrome Extensions](/docs/guides/auth/social-login/auth-google?platform=chrome-extensions)]

## Sign in with Apple JS, Google One Tap and Chrome Extensions

Although sign in on native platforms was the focus of the team when working on this feature, incidentally we’ve added proper support for Sign in with Apple JS, Google’s One Tap and support for authenticating within Google Chrome extensions.

You can now take advantage of these web frameworks, most notably Google’s One Tap and Automatic Sign-in support for a frictionless onboarding experience for your users.

All you need to do is configure the web frameworks and use the `signInWithIdToken()` method to pass the ID token provided by the Google and Apple libraries.

For example, to use Google One tap you should first [generate an embed code for the Google Sign in Button](https://developers.google.com/identity/gsi/web/tools/configurator). Register this method as the callback that will receive the authentication response from the button:

```tsx
async function handleSignInWithGoogle(response) {
  const { data, error } = await supabase.auth.signInWithIdToken({
    token: response.credential,
    nonce: 'NONCE', // must be the same one as provided in data-nonce (if any)
  })
}
```

## Resources

- [Login with Apple Guide](https://supabase.com/docs/guides/auth/social-login/auth-apple)
- [Login with Google Guide](https://supabase.com/docs/guides/auth/social-login/auth-google)
