---
title: Supabase Beta November 2022
description: We are preparing everything for Launch Week 6, but we still had time to ship some goodies this month!
author: ant_wilson
image: 2022-12-07-november-beta-update/monthly-update-november-2022.jpg
thumb: 2022-12-07-november-beta-update/monthly-update-november-2022.jpg
categories:
  - product
tags:
  - release-notes
date: '2022-12-07'
toc_depth: 3
---

Launch Week 6 is just around the corner! We’re saving most of November’s updated as a surprise for Launch Week, but we still had time to ship some goodies this month. Read on!

## Launch Week 6 tickets

![Launch Week 6 tickets](/images/blog/2022-12-07-november-beta-update/launch-week-6-ticket.jpg)

Next week, we go all out for LW6. It’s 5 days of shipping, including major features requested by the community. You don’t want to miss a thing, so make sure to claim your free ticket (and you might win some very special SupaSwag).

[Get your ticket](https://supabase.com/launch-week)

## Remix Auth Helpers

![Remix Auth Helpers](/images/blog/2022-12-07-november-beta-update/remix-auth-helpers.jpg)

It’s here! The much-awaited Remix Auth Helpers make server-side auth even easier and with a better experience. Up to date with supabase-js V2 and can be used with Typescript.

[Read the docs](https://supabase.com/docs/guides/auth/auth-helpers/remix)

## Edgy Edge Functions

![Edgy Edge Function](/images/blog/2022-12-07-november-beta-update/edgy-edge-functions.jpg)

We launched a new YouTube series, [Edgy Edge Functions](https://www.youtube.com/playlist?list=PL5S4mPUpp4OulD3olUW8Eq1IYKpUbk5Ob), where we take a deep look at a new function every week.

Plus, we published three new functions examples: [Generate OG Images](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/opengraph), [Build API servers using Oak](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/oak-server), and [Generate Screenshots using Puppeteer](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/puppeteer).

## Quick product updates

- Fixed two issues with the `supabase.auth.signOut` function: [cookies not clearing properly](https://github.com/supabase/gotrue/pull/830) and [session not removed](https://github.com/supabase/gotrue-js/pull/541).

- You can now customize magic links by providing access to the `{{ .TokenHash }}`. [PR](https://github.com/supabase/gotrue/pull/707)

- Now you can do redirects from your functions. [PR](https://github.com/supabase/supabase/issues/10325)

- Conda support (`conda install -c conda-forge supabase` and everything else in py)

## Content storm

![The Supabase Content Storm](/images/blog/2022-12-07-november-beta-update/content-storm.jpg)

As a prelude to Launch Week 6, we invited dozens of developers from around the world to create content in different formats and publish everything simultaneously!

[Check the full list](https://supabase.com/blog/the-supabase-content-storm)

## Fireship’s Supabase course

![Fireship’s Supabase course](/images/blog/2022-12-07-november-beta-update/fireship-supabase-course.jpg)

That guy from the [Supaship Youtube](https://www.youtube.com/@Supaship6000) channel made a Supabase Course with Fireship! Zack DeRose did an amazing job with this project-based course that builds a Reddit-inspired web app from scratch with Supabase, PostgreSQL, and React.

[Get it while it's hot](https://fireship.io/courses/supabase/).

## Made with Supabase

![Made with Supabase](/images/blog/2022-12-07-november-beta-update/madewithsupabase.jpg)

[postedbyAI](https://postedby.ai/) | Allows users to create personalized postcards using advanced AI technology. Built with Vercel, OpenAI, and Supabase.

Discover other great projects: [Made with Supabase](https://www.madewithsupabase.com/).

## Extended Community Highlights

![Community](/images/blog/2022-june/community.jpg)

- Supabase in 100 Seconds, by Fireship. [Video](https://www.youtube.com/watch)
- NextAuth Supabase Adapter. [Docs](https://next-auth.js.org/adapters/supabase)
- Flutter Authentication and Authorization with RLS. [Tutorial](https://supabase.com/blog/flutter-authentication-and-authorization-with-rls)
- The pros and cons of using PostgREST. [Podcast](https://postgres.fm/episodes/postgrest)
- Querying Supabase from the edge with Netlify and PolyScale. [Tutorial](https://www.netlify.com/blog/querying-supabase-from-the-edge-with-polyscale/)
- JWT Authentication with Supabase and Zuplo for your API. [Blog](https://zuplo.com/blog/2022/11/15/api-authentication-with-supabase-jwt/) and [Video](https://www.youtube.com/watch?v=UEeSZkV7o_Y&feature=youtu.be)
- Shipping a public API backed by Supabase. [Blog](https://zuplo.com/blog/2022/11/18/shipping-a-public-api-backed-by-supabase) and [Video](https://www.youtube.com/watch?v=GJSkbxMnWxE)
- A Guide to testing on Supabase using pgTAP. [Guide](https://usebasejump.com/blog/testing-on-supabase-with-pgtap)
- [Using Bytebase to manage the database development lifecycle for Supabase databases](https://www.bytebase.com/docs/how-to/integrations/supabase)
- The Best Backend as a Service for your React App. [Article](https://www.freecodecamp.org/news/best-backend-service-react/)
- All You Need to Know About Serverless Functions and the Edge. [Article](https://dev.to/nefejames/all-you-need-to-know-about-serverless-functions-and-the-edge-2bl2)
- #1 Hack for Building JavaScript Portfolio Projects. [Video](https://www.youtube.com/watch?v=3K9VUT7H5sw)
- Livestreaming - Job Board App w/ SvelteKit, Supabase, & Prisma. [Youtube Playlist](https://www.youtube.com/playlist?list=PLq30BP0TIcqU8SlzDK2wX4IaXamQ26kpr)
- How to connect to and insert data into Supabase with Pipedream. [Article](https://pipedream.com/blog/how-to-connect-to-and-insert-data-into-supabase-with-pipedream/) and [Video](https://www.youtube.com/watch?v=l9B8F1n3duk)
- Google Sign-In using Supabase and React Native (Expo). [Tutorial](https://dev.to/fedorish/google-sign-in-using-supabase-and-react-native-expo-14jf)
- Fast and Intuitive analytics for Supabase with DB Zaps. [Video](https://www.youtube.com/watch?v=QlIlqm9eG3w)
- ReactJS + Supabase Fullstack CRUD Application Development. [Youtube Playlist](https://youtube.com/playlist?list=PLl6EcvA_AoxEFqwT_rNs1uZnnf7LSEoji)
- How to connect a ESP8266 to a Streamlit App using Supabase database. [Video](https://www.youtube.com/watch?v=wI209WK3-VM)

## Meme Zone

This month’s meme comes courtesy of Fireship. [Follow us on Twitter](https://twitter.com/supabase) for more.

![Supabase meme November 2022](/images/blog/2022-12-07-november-beta-update/november-beta-update-meme.png)

See you next month!
