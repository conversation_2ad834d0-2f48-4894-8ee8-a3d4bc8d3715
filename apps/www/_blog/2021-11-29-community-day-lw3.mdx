---
title: 'Community Day'
description: Kicking off launch week by highlighting the communities around Supabase.
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: launch-week-three/community-day/supabase-oss.jpg
thumb: launch-week-three/community-day/supabase-oss.jpg
categories:
  - developers
tags:
  - launch-week
  - community
date: '2021-11-29'
toc_depth: 2
video: https://www.youtube.com/v/6ptUrE4QRPQ
---

Supabase combines existing open-source tools with our own open-source contributions to provide a delightful experience for developers. As part of this goal we hope to build a community of communities, bringing together developers from many different tools, as well as new developers looking to get involved with open source.

To kick off launch week we want to showcase some of the communities that make up the Supabase community, highlight some of their updates, and celebrate everyone who contributes their time to the Supabase mission.

So let's get cracking, we've got a lot to cover. 🚀

## Open Source Spotlight: Kong API Gateway

Supabase provides a whole bunch of features that are made up of a [collection of open-source tools](/docs/guides/getting-started/architecture). To orchestrate these different services and functionalities we use the [Kong API Gateway](https://github.com/Kong/kong).

As a user of our hosted offering you don't really need to know all this, but we love showcasing the amazing open-source tools that we work with, and so we've invited [Vik Gamov](https://twitter.com/gAmUssA) to give you a little intro to [Kong](https://github.com/Kong/kong):

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/6ptUrE4QRPQ"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

## PostgreSQL 14 updates

![Postgres-14](/images/blog/whats-new-in-postgres-14/whats-new-in-postgres-14-og.png)

Every web developer knows the importance of choosing a suitable database for building modern apps.
Even though NoSQL, NewSQL, and other types of databases have received a great deal of buzz in the last few years,
relational database management systems (or RDBMS) are still relevant for several critical business use cases and will likely do so in the foreseeable future.
Among the many open-source relational databases available, [PostgreSQL](/docs/guides/database) is a popular choice among developers.
It was named [DBMS of the year in 2020](https://db-engines.com/en/blog_post/85) by
DBEngines, and with every release of PostgreSQL new features are available making it easy for developers and administrators to run their apps.

[Gurjeet](https://twitter.com/0xGurjeet), one of our Postgres engineers, has written up a [blogpost](/blog/whats-new-in-postgres-14) with the most important updates the every dev should know.

## PostgREST 9 updates

![PostgREST-9](/images/blog/launch-week-three/community-day/postgrest-9-og.png)

We've had a lot of feedback on our Postgres APIs, and we've been hard at work with the [PostgREST team](https://github.com/orgs/PostgREST/people) to improve an already-incredible product.

Some new features include

- Inner Joins
- Functions with unnamed parameters
- PostgreSQL 14 compatibility

From tomorrow, every Supabase project will be on PostgREST 9 (including existing projects).

Read about some of the new features in PostgREST 9 [here](/blog/postgrest-9).

## New community partner: GitGuardian

![gitguardian-supabase-partnership.jpeg](/images/blog/launch-week-three/community-day/gitguardian-supabase-partnership.jpeg)

[As you (hopefully) know](/docs/learn/auth-deep-dive/auth-deep-dive-jwts#jwts-in-supabase), keeping your service role key secret is a crucial part of securing your database. But we also know that it can be difficult to make sure it stays secret as your team grows and you don't accidentally leak it. To help you with this, [we've partnered with GitGuardian](https://blog.gitguardian.com/the-detector-of-the-month-november-2021/).

GitGuardian helps developers keep 250+ types of secrets out of source code. Their automated secrets detection and remediation solution secures every step of the development life cycle, helping you monitor your code for sensitive data. Read [their blog post](https://blog.gitguardian.com/the-detector-of-the-month-november-2021/) to learn more.

## Auth updates

![new-oauth-providers.jpg](/images/blog/launch-week-three/community-day/new-oauth-providers.jpg)

The beauty of building in the open is that the community can get involved in adding new OAuth providers, which is exactly what [@TheHarryET](https://twitter.com/TheHarryET) and [MonsterDeveloper](https://github.com/MonsterDeveloper) have been doing. Thanks to them you can now provide sign-in with [Slack](/docs/guides/auth/social-login/auth-slack) as well as [Spotify](/docs/guides/auth/social-login/auth-spotify) to your users, as well as use [MessageBird](/docs/guides/auth/phone-login/messagebird) for phone auth as an alternative to the existing [Twilio](/docs/guides/auth/phone-login/twilio) integration.

## SupaSquad updates

The [SupaSquad](/supasquad) is an official Supabase advocate program where community members help build and manage the Supabase community.

Among many other awesome things, the SupaSquad has been absolutely on fire building [client libraries](/docs/reference/javascript/installing) for all flavors of backends and environments.

![client-libs.jpg](/images/blog/launch-week-three/community-day/client-libs.jpg)

### Python client library updates

- New maintainers [`@Dreinon`](https://github.com/dreinon), [`@anand2312`](https://github.com/anand2312), and [`@leynier`](https://github.com/leynier). 💚
- `gotrue-py` rewritten and now at feature parity with `gotrue-js`.
- `postgrest-py` now support synchronous operations as well instead of just asynchronous operations in the past. Also at feature parity with `postgrest-js`.
- `supabase-py` is now `supabase` (e.g. you do `pip3 install supabase` instead of `pip3 install supabase-py`).
- Storage is working for the Python client library but it's yet to be extracted out as a standalone lib.

## New tutorials and integration guides

Our own [Jon Meyers](https://twitter.com/_dijonmusters) and the broader community have been busy creating awesome tutorials and integration guides to help everyone build even more amazing things with Supabase:

- Jon has added official guides for working with [Auth0](/partners/integrations/auth0) and [Vercel](/partners/integrations/vercel),
- [Jonny Summers-Muir](https://twitter.com/JSummersMuir) has been busy paving the way for [Prisma](/partners/integrations/prisma), and
- [Aman Mittal](https://github.com/amandeepmittal) has hooked us up with a guide for [Draftbit](/partners/integrations/draftbit)!

And below are some of the community one's we've come across recently. If you've created one yourself, please notify us by tagging us in a Tweet!

- [Supabase & Sveltekit - Build Twitter in 75 minutes](https://youtu.be/mPQyckogDYc)
- [Supabase Auth With Next.Js 12 Middleware](https://jitsu.com/blog/supabase-nextjs-middleware#what-were-going-to-build)
- [Vue 3 & Supabase | Workout Tracker App](https://www.youtube.com/watch?v=3tF0fGkd4ho)
- [Use Supabase Auth with Vue.js 3](https://vueschool.io/articles/vuejs-tutorials/use-supabase-auth-with-vue-js-3/)
- [Dynamic Jamstack with Stencil and Supabase](https://ionicframework.com/blog/dynamic-jamstack-with-stencil-and-supabase)

## Developer stories

Anytime we scroll through [madewithsupabase.com](https://www.madewithsupabase.com/) we're absolutely blown away by the awesome things y'all are building with Supabase.

So we reached out to some of our users, requesting a short video to learn more and we were overwhelmed with your gracious submissions. We've started a user stories playlist and we are so excited to see many more of these stories in the future. 💚

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/videoseries?list=PL5S4mPUpp4OuzQN-a_FY3OZQuYo4NmXvb"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

And with that, we officially declare Launch Week as open 🥳 Check back [here](/blog) every day this week to see what new things we are shipping. We can't wait to share them with you 🚀

## Want even more Supabase in your life?

![tiktok.png](/images/blog/launch-week-three/community-day/tiktok.png)

We've got some stellar swag drops and behind the scenes footage for you on our new [TikTok page](https://www.tiktok.com/@supabase.com). Make sure to follow us there as we'll be giving away some swag randomly to 10 peeps who follow us during launch week!
