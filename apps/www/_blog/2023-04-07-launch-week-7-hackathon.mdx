---
title: 'The Supabase AI Hackathon'
description: Build an Open Source Project over 10 days. 5 prize categories.
author: tyler_shukert
image: lw7-hackathon/thumbnail.png
thumb: lw7-hackathon/thumbnail.png
launchweek: '7'
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2023-04-07'
toc_depth: 2
---

With Easter just around the corner, [Launch Week](https://supabase.com/launch-week) is about to start and, as usual, we're running an async hackathon alongside it. This time it’s AI-themed!

The hackathon starts **now** and ends Sunday 16th April at 11:59 pm. You can win extremely limited edition Launch Week themed mechanical keyboards and limited edition Supabase swag.

For some inspiration check out the [Next.js OpenAI Doc Search template](https://github.com/supabase-community/nextjs-openai-doc-search?og=v2) released yesterday and [storing embeddings on Supabase article](https://supabase.com/blog/openai-embeddings-postgres-vector).

Additionally, you can see all the [winners from previous hackathons](https://supabase.com/blog/tags/hackathon).

![Supabase AI Hackathon Meme](/images/blog/lw7-hackathon/supabase-ai-hackathon-meme.png)

## Key Facts

- You have 10 days to build a new **Open Source** project using Supabase in some capacity.
  - Starting 6:00 am PT Friday 7th April 2023
  - The submission deadline is 11:59 pm Sunday midnight PT 16th April 2023
- Enter as an individual, or as a team of up to 5 people
- Build whatever you want - a project, app, tool, library, or anything

## Prizes

There are 5 categories, there will be prizes for:

- Best overall project (Mechanical Keyboards & Swag kit)
- Best use of AI (Swag kit)
- Most fun / best easter egg (Swag kit)
- Most technically impressive (Swag kit)
- Most visually pleasing (Swag kit)

There will be a winner and a runner-up prize for each category. Every team member on winning/runner-up teams gets a Supabase Launch Week swag kit.

![Supabase LW7 Keyboard](/images/blog/lw7-hackathon/keyboard.png)

## Submission

You should submit your project using [this form](https://www.madewithsupabase.com/launch-week-7) before 11:59 pm Sunday midnight PT 16th April 2023. Extra points if you include a simple video demoing the app in the description.

## Judges

The Supabase team will judge the winners for each category.
We will be looking for:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

## Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be on multiple teams
- One submission per team
- It's not a requirement to use AI, it's just a theme
- All design elements, code, etc. for your project must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- (optional) Include a link to a demo video along with the submission for extra points

## Resources

### Supabase resources to get you started

- [OpenAI with Edge Functions YouTube video](https://www.youtube.com/watch?v=29p8kIqyU_Y)
- [Streaming Data in Edge Functions](https://www.youtube.com/watch?v=9N66JBRLNYU)
- [Storing OpenAI embeddings in Postgres with pgvector article](https://supabase.com/blog/openai-embeddings-postgres-vector)
- [pgvector guide](https://supabase.com/docs/guides/database/extensions/pgvector)

### Community help

The Supabase Team will be taking part in the Hackathon and you'll find us live to build in our discord all week. Please join us by building in public:

- Text channel: [#hackathon](https://discord.gg/UYyweApy)
- Audio channel: [#hackathon](https://discord.gg/Vj3mTPwH)

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com/)

![Supabase Discord Channel](/images/blog/lw7-hackathon/discord.png)

## Launch Week

Don't forget to check out the new features being announced as part of [Launch Week](https://supabase.com/launch-week).

- [Previous Hackathon Prize Winners](https://supabase.com/blog/launch-week-6-hackathon-winners)

## Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code snippets, and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
