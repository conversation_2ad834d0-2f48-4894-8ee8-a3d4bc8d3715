---
title: 'Launch Week 7 Hackathon Winners'
description: Announcing the winners of the Launch Week 7 Hackathon!
author: tyler_shukert
image: lw7-hackathon-winners/lw7-hackathon-winner.jpg
thumb: lw7-hackathon-winners/lw7-hackathon-winner.jpg
categories:
  - developers
tags:
  - hackathon
date: '2023-04-24'
toc_depth: 2
---

[Launch Week 7](https://supabase.com/launch-week) passed by, but we still have our favorite part left, celebrating the winners of the hackathon! We added an AI theme to our hackathon, and the results were exceptional. The quality of the apps was out of this world.

And now, without further ado, congratulations to the winners of Supabase Launch Week 7 Hackathon!

## Best Overall Project

### Winner

[Page Assist](https://github.com/n4ze3m/page-assist) - by [@n4ze3m](https://twitter.com/n4ze3m)

Revolutionize your browsing experience with PageAssist, an open-source Chrome extension that allows you to easily chat with any webpage using the power of ChatGPT API.

![Page Assist](/images/blog/lw7-hackathon-winners/01_page_assist.png)

### Runner Up

[rect](https://github.com/Vandivier/rect) - by [@<PERSON>Vandivier](https://twitter.com/JohnVandivier)

Rect takes short-form social media content as an input, and produces four kinds of educational output material. Specifically, Rect is an AI-powered assistant that will produce flash cards, quizzes, slideshows, and a chatbot assistant.

![rect](/images/blog/lw7-hackathon-winners/02_rect.png)

## Best use of AI

### Winner

[Supathreads](https://github.com/asrvd/supathreads) - by [@\_asheeshh](https://twitter.com/_asheeshh)

Supathreads creates concise and professional Twitter threads from your blog posts within seconds using the power of AI.

![Supathreads](/images/blog/lw7-hackathon-winners/03supathreads.png)

### Runner Up

[ChatVox · Chat With Any Video](https://github.com/JimmyLv/ChatVox) - by [@Jimmy_JingLv](https://twitter.com/Jimmy_JingLv)

ChatVox takes the YouTube/Podcast link you enter or the local Video/Audio file (WIP) you upload on the page, then you can chat with the video to obtain information about it.

![ChatVox · Chat With Any Video](/images/blog/lw7-hackathon-winners/04_chatvox.png)

## Most fun / best easter egg

### Winner

[Replay.ai - spotlight search for your browsing history](https://github.com/KamaniBhavin/replay) - by [@bhavinkamani\_](https://twitter.com/bhavinkamani_)

Replay is a Chrome extension that lets you search through all the pages in your browsing history to find the one you can't remember. It uses cosine similarity search to find the page based on the content of the page and the search query provided by the user.

![Replay.ai - spotlight search for your browsing history](/images/blog/lw7-hackathon-winners/05_replayai.png)

### Runner Up

[Groove AI - AI Generated Drum Patterns](https://github.com/danryland/groove-ai) - by [@RealDanRyland](https://twitter.com/RealDanRyland)

Groove AI generates drum patterns powered by AI and randomly plays the pattern whenever you open the page. You can modify the pattern by toggling each beat.

![Groove AI - AI Generated Drum Patterns](/images/blog/lw7-hackathon-winners/06_grooveai.png)

## Most technically impressive

### Winner

[Supa0SQL](https://github.com/Gabrielpanga/supa0sql) - by [@gpangantes](https://twitter.com/gpangantes)

Supa0SQL is a simple, easy-to-use, and fast Natural Language to Chart tool. It's meant for non-technical users to be able to quickly and easily create charts from their data without having to learn SQL.

![Supa0SQL](/images/blog/lw7-hackathon-winners/07supa0sql.png)

### Runner Up

[Schedurio - An open-source tweet Scheduler.](https://github.com/Ansh-Rathod/schedurio) - by [@anshrathodfr](https://twitter.com/anshrathodfr)

Schedurio is very helpful for Twitter content creators who are paying monthly for services like Hypefury, Feedhive, and many others, now they can use the Schedurio open-source app on their desktop by adding their own API keys. currently available for the Macos and web.

![Schedurio](/images/blog/lw7-hackathon-winners/08_schedurio.png)

## Most visually pleasing

### Winner

[Generation: Hotdog](https://github.com/laznic/generation-hotdog) - by [@laznic](https://twitter.com/laznic)

Generation: Hotdog generates hotdog images from 5 emojis. It uses both OpenAI and Stable Diffusion APIs to generate the end results. First, it'll use the selected emojis to generate a description of a hotdog via OpenAI, and then it'll also translate that text to Japanese Kanji (also via OpenAI).

![Generation: Hotdog](/images/blog/lw7-hackathon-winners/09_generation_hotdog.png)

### Runner Up

[Tune Twisters](https://github.com/JohnPhamous/tune-twisters) - by [@johnphamous](https://twitter.com/johnphamous)

Tune Twisters is a simple game where it plays a random song in reverse and you have to guess the name of the song.

![Tune Twisters](/images/blog/lw7-hackathon-winners/10_twisters.png)

## The Prizes

As promised in the [hackathon announcement article](https://supabase.com/blog/launch-week-7-hackathon) the winners of the Best overall project will be granted the extremely limited edition Supabase Launch Week 7 Keyboard!

![keyboard](/images/blog/lw7-hackathon-winners/keyboard.png)

Each member of the winners and runner-ups in other categories will receive a limited edition Launch Week 7 swag kit.

![swag](/images/blog/lw7-hackathon-winners/swag.png)

### Get Started Guides

If you're inspired to build, check out some of the latest resources:

- **[Quick Start Guides](https://supabase.com/docs/guides/getting-started)**
- **[Supabase Storage Guide](https://supabase.com/docs/guides/storage)**
- **[Edge Functions Guide](https://supabase.com/docs/guides/functions)**
