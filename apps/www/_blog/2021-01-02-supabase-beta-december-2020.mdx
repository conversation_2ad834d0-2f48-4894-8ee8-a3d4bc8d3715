---
title: Supabase Beta December 2020
description: Ten months of building.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: supabase-december-2020.png
thumb: supabase-december-2020.png
categories:
  - product
tags:
  - supabase
date: '01-02-2021'
video: https://www.youtube.com/v/ofSm4BJkZ1g
---

After 10 hectic months of building, Supabase is now in Beta.

### Quick demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/ofSm4BJkZ1g"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Supabase is now in Beta

We spent months working on Performance, Security, and Reliability. Read more on our [Beta Page](/beta).

![This image shows our Beta Page.](/images/blog/blog/dec-beta.png)

### Improve your docs inline

Add comments and descriptions to your Tables directly from our auto-generated docs. Descriptions are stored as PostgreSQL comments (https://postgresql.org/docs/current/sql-comment.html), and are exposed over your OpenAPI spec.

<video width="99%" autoPlay muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/update-docs.mp4"
    type="video/mp4"
  />
</video>

### Table View now has realtime changes

Any updates that happen to your database are reflected in the Table View immediately.

<video width="99%" autoPlay muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/realtime-updates.mp4"
    type="video/mp4"
  />
</video>

### Table Pagination

Our table view now has pagination - better for working with large data sets.

<video width="99%" autoPlay muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/table-pagination.mp4"
    type="video/mp4"
  />
</video>

### Supabase raised a Seed Round

We raised $6M from Y Combinator, Mozilla, and Coatue. You can read more on [TechCrunch](https://techcrunch.com/2020/12/15/supabase-raises-6m-for-its-open-source-firebase-alternative).

### Kaizen

- Supabase is now 26% faster in regions which support Graviton (1460 reqs/s up from 1167 reqs/s)
- We launched a new region in Sao Paulo.
- Postgres Array Support. You can now edit Native Postgres array items in the grid editor or the side panel.
- We added better support for your custom Database Types.
- Fixed some buggy keyboard commands. We're continuously improving key commands in the Table editor.

### Community

- We were featured on the GitHub release radar. [Link](https://github.blog/2020-12-07-release-radar-dec-2020/)
- [@kiwicopple](https://twitter.com/kiwicopple) appeared on [Open Core Summit](https://2020.opencoresummit.com/)
- [@aaronksaunders](https://twitter.com/aaronksaunders) created a video series on Supabase + Vue. [Link](https://twitter.com/aaronksaunders/status/1339981480202743811).
- [@CodeByCorey](https://twitter.com/CodeByCorey) tracks realtime page views using Supabase. [Link](https://twitter.com/CodeByCorey/status/1344650699645325312).
- [@ffbass](https://github.com/ffabss) started working on gotrue-java. [Link](https://github.com/supabase/gotrue-java).
- We've grown more than 50% (GitHub star count) since moving into Beta. [Link](https://twitter.com/supabase/status/1345410714836594693)

![This image shows GitHub star growth.](/images/blog/blog/dec-starcount.png)

If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).

### Coming next

We've go a lot of exciting things planned for Q1 2021. We're already planning out Supabase Storage and a Supabase CLI for better local development. Let us know if there's something you want us to release as a priority!

We also have something exciting planned with Vercel and Stripe ... [stay tuned](https://twitter.com/rauchg/status/1331021818681978881).

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
