---
title: 'Spot: a video sharing app built with Flutter'
description: Spot is a geolocation-based video-sharing app with some social networking features.
author: rory_wilding
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: spot/og-spot-flutter-supabase.jpg
thumb: spot/cover-spot-flutter-supabase.jpg
categories:
  - engineering
tags:
  - flutter
date: '2021-07-27'
toc_depth: 2
---

<PERSON>rn why <PERSON> decided Flutter and Supabase are the perfect tech-stack shipping innovative ideas fast.

[<PERSON>](https://twitter.com/dshukertjr) is an indie developer who has been building websites and applications for seven years.
<PERSON> is a Firebase & Flutter expert who recently discovered Supabase. After falling in love with <PERSON><PERSON><PERSON>,
<PERSON> decided to launch his latest app. [Spot](https://www.producthunt.com/posts/spot-2d300f54-7a0a-4dbf-aee2-4a75311217cc)
is a geolocation-based video-sharing app with some social networking features.

## NoSQL doesn't mean No <PERSON><PERSON><PERSON> loves how easy it is to create high-quality web and mobile applications with Firebase. For the past few years he has honed
his Flutter, Angular, and Firebase skills to quickly ship mobile and web apps. But the more his development expertise grew,
the more he understood the limitations of Firebase.

Before starting a project, <PERSON> is never sure if the project will grow. It could be an overnight success, or it might need to
pivot in a different direction. He finds this is particularly difficult with Firebase as he faces a denormalization dilemma.
Denormalization means combining the data into a single table to make data retrieval faster. Google promotes denormalization
as best practice for a snappier app experience. Denormalisation in Firebase takes work and requires additional development
time. For new projects, it is often unclear if they will reach a scale justifying the effort denormalization requires.
In relational databases like Postgres, data is normalized, meaning reduced data redundancy with maintained data integrity.

> Read a full [comparison of Firebase vs Supabase](https://supabase.com/alternatives/supabase-vs-firebase)

## Supabase + Flutter: a match made in heaven

When Tyler discovered Supabase on Twitter, he was intrigued by the promise of Postgres with Firebase-like features.
He wanted the benefits of SQL and Relational schemas, and decided to build an app to test Supabase.

Tyler had been thinking about building Spot, a mobile app for sharing geo-based video content. When he previously planned Spot,
he felt like hacking around Firebase would be too much effort. After researching the PostGIS extension for geospatial data,
he realized Supabase was the perfect fit. As an avid Flutter fan, Supabase's Dart library was a bonus.
Tyler loves the open-source nature of Supabase and immediately began contributing to the Dart library to enable functionality that he needed.

Tyler decided that Spot would be perfect to showcase the power of Supabase and Flutter. Since he's active in the open-source community,
he decided to open-source the whole project, making Spot one of the most interesting Supabase examples.

![Spot screenshots.](/images/blog/spot/spot-screenshots.jpg)

## Comparing Firebase to Supabase

Although Spot seems simple, it has a lot of complicated components. Implementing social "Likes" requires `count` functionality,
which is notoriously tricky in Firebase. In Supabase, because it's just Postgres under the hood, `count` is implemented
using a simple SQL `count` query.

Supabase has Row-Level Security, which Tyler uses as a filter - something he can't do with Firebase.

Spot uses Supabase Storage for managing large video files. Firebase Storage [has a reputation](https://stackoverflow.com/questions/43851742/firebase-storage-very-slow-compared-to-firebase-hosting) for slowness until it is public, which was a privacy concern for Tyler. With Supabase Storage, this wasn't an issue - storage is performant and secure.

The Firebase API pricing model makes it is difficult to predict pricing upfront for new projects. With Supabase,
Tyler doesn't need to worry about how many API calls his project makes.

Supabase gives Spot speed, performance, and predictable pricing.

As of today, Tyler has around 3000 installs for Spot and is getting ready to add new functionality. He is confident Supabase,
Postgres, and his data structure will enable future development.

<Quote img="tyler-spot.jpg" caption="Tyler Shukert, creator of Spot.">
  <p>
    As soon as I saw Supabase I knew it was the perfect technology to use alongside Flutter to build
    apps faster than ever. For apps like Spot where we handle user submitted files, it is very easy
    to manage them securely.
  </p>
  <p>
    Supabaseを最初発見した時にこれとFlutterを組み合わせれば今まで以上に爆速でアプリ開発ができるだろうと思いました。
    Spotのようにユーザーがファイルを投稿するアプリの場合でもセキュアに取り扱うのが簡単で本当に助かります。
  </p>
</Quote>

## Spot is Open Source

With Supabase, Tyler can use Flutter to build a performant app quickly at a low cost. Tyler is a massive fan of the Supabase
experience and has even joined the SupaSquad.

> Update: After being part of the SupaSquad, Tyler joined Supabase full time as part of the DevRel squad, focusing on Flutter. This is some of his latest work: [Flutter Tutorial: building a Flutter chat app
> ](https://supabase.com/blog/flutter-tutorial-building-a-chat-app) and [Flutter Authentication and Authorization with RLS](https://supabase.com/blog/flutter-authentication-and-authorization-with-rls).

You can download and install spot for [iOS](https://apps.apple.com/us/app/spot-videos/id1564675926?utm_source=supabase&utm_campaign=static)
and [Android](https://play.google.com/store/apps/details?id=app.spotvideo&utm_source=supabase&utm_campaign=static)
to see just how snappy his setup is.
Tyler has fully open-sourced his example - you can [clone the repo](https://github.com/dshukertjr/spot)
and use it as the basis of your next big idea.
