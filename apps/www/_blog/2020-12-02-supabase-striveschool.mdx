---
title: Supabase Partners With Strive School To Help Teach Open Source
description: Supabase Partners With Strive School To Help Teach Open Source To The Next Generation Of Developers
author: rory_wilding
author_title: Supabase
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: supabase-strive-school.png
thumb: supabase-strive-school.png
categories:
  - company
tags:
  - supabase
  - striveschool
date: '12-02-2020'
---

### Supabase and Strive School - YC Summer 20 batchmates

We are proud to announce we are teaming up with fellow YC Summer 20 Alumni [Strive School](https://strive.school/), to help teach the next generation of software developers about open source software development.

![Supabase and Strive are partnering up to teach OSS.](/images/blog/strive-supabase.png)

Strive School is on a mission to train the next generation of Computer Engineers, starting from Europe.

They've designed a new type of program which they brand a "Master-camp" - combining the depth of a masters degree with the pacing of a coding Bootcamp, they train engineers over a time span of eight months, and their students pay only once they get hired.

<PERSON><PERSON> and <PERSON>, the founders, launched Strive School to help individuals gain the skills employers need and expect from a solid early career developer, which are often not taught during traditional computer science degrees and almost impossible to learn fully by yourself.

Their business model means the better students, who want it the most, have a shot at making it - regardless of their socio-economic background.

### Working together to help students learn open source

Strive School has an initial focus on full-stack Web Engineering and AI Engineering. They deeply care about open-source and help their students understand the importance of open-source software in the modern workforce. Almost all technology companies use open-source software, so Strive School sees it as essential that their students leave the course with an understanding of open source development.

At Supabase, we live and breathe open source. Our focus is on building the open-source Firebase alternative. Wherever possible, we integrate and support the best-in-class existing open-source tools and communities. If those tools don't exist, we build and open-source them ourselves.

We are huge Postgres fans and believe that Supabase will make learning and implementing relational databases a better experience for code school students.

We will be working closely with Strive School in the coming months to produce exclusive content for their code school students. We are also happy to announce we will be taking on a Strive school graduate as an intern at Supabase. This isn't a role we were actively hiring for, but we have been so impressed with the standard of their graduates that we didn't want to miss out of the chance to work with talent from Strive School.

### Launching the Supabase Student Developer Pack with Strive School

As part of this process, we have put together a Student Developer Pack. Strive School are the perfect partner to launch our Student Developer Pack with as we both feel passionate about the importance of open-source software to empower developers. We are looking forward to working closely together in the coming months to help their students embrace the world of open-source.

The Supabase Student Development pack includes:

- Credits for students to use Supabase free for 2-years
- Access to our Slack Community channel, full of high profile developer advocates and open-source contributors
- Founder office hours with students
- Access to limited edition Supabase Swag

If you running a code school or university and want to learn more about the Supabase Student Developer Pack, then reach <NAME_EMAIL>
