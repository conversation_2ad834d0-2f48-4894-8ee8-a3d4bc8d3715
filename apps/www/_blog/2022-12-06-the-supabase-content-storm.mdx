---
title: The Supabase Content Storm
description: We worked with +30 content creators to drop a mountain of content simultaneously.
author: ramiro
image: 2022-12-06-content-storm/content-storm.jpg
thumb: 2022-12-06-content-storm/content-storm.jpg
categories:
  - company
tags:
  - announcements
date: '2022-12-06'
toc_depth: 3
---

We're always trying to innovate at Supabase, so when we started planning [Launch Week 6](https://supabase.com/launch-week), we brainstormed ideas for a campaign to promote it that would involve the community. We took a bit of inspiration from things like Weezer’s YouTube takeover and came up with the concept of a Content Storm ⛈.

## What is the Content Storm?

The idea was to get many different external content creators as possible to create Supabase-related content and _publish everything on the same day_. We contacted some well known Youtubers, community members, makers building with Supabase, the SupaSquad, and generally anyone within the Supabase community. We wanted this to be an opportunity for them to build their personal brand or promote their products and startups.

## The aftermath of the storm

More than 30 creators participated in creating content in different formats and everything was published today (Tuesday 6th of December). Here is the full list of every piece of content and its author:

- [A quick look at Supabase](https://twitter.com/Rorstro/status/1600047388852293632) by <PERSON><PERSON>.
- [Live streaming: Supabase and Sveltekit](https://www.youtube.com/@JamesQQuick/streams) by James Q Quick.
- [Perform Database Migration Using Github Actions & Supabase CLI](https://www.youtube.com/watch?v=iCkdtXSeq7A) by Kunal Kushwaha.
- [Supabase Authentication & Realtime Database with Flutter](https://www.youtube.com/watch?v=UTSSYPOd5A0) by Pooja Bhaumik.
- [Why I Switched From AWS Cognito To Supabase The Week Before My Startup Launched](https://medium.com/@sleeplessfox/why-i-switched-from-aws-cognito-to-supabase-the-week-before-my-startup-launched-67fdc1eccb8c) by Danilo à Tellinghusen.
- [Supabase Crash Course For Python Developers](https://www.youtube.com/watch?v=M6cfT2pqpSc) by Patrick Loeber.
- [Hoppscotch with GraphQL](https://www.youtube.com/watch?v=HoBXQng3aK4) by Jamie Barton.
- [Supabase with TypeScript: using tRPC and Prisma to achieve end-to-end typesafety](https://noahflk.com/blog/supabase-typescript-trpc) by Noah.
- [How to Connect Browser Extensions to Supabase](https://akoskm.com/how-to-connect-browser-extensions-to-supabase) by Ákos Kőműves.
- [A Chili Cookoff with Rust, Rocket, Render, and Supabase](https://bradcypert.com/chili-cookoff-with-rust-rocket-render-and-supabase/) by Brad Cypert.
- [Authenticating users with Remix and Supabase](https://makerkit.dev/blog/tutorials/remix-supabase-auth) by Giancarlo Buomprisco.
- [Automatic Spotify Playlist Creation with Pipedream and Supabase](https://www.ianwootten.co.uk/2022/12/06/automatic-spotify-playlist-creation-with-pipedream-and-supabase/) by Ian Wootten.
- [Accept Payments For Your African-Based Business Using Supabase Edge Functions and Paystack](https://blog.hijabicoder.dev/accept-payments-for-your-african-based-business-using-supabase-edge-functions-and-paystack) by Fatuma Abdullahi.
- [Creating AvatarAI.me using Supabase and Cerebrium](https://blog.cerebrium.ai/creating-avatarai-me-using-supabase-and-cerebrium-1bf69440c4a7) by Cerebrium.
- [From Zero to Hero: Building Next.js Projects](https://www.youtube.com/watch?v=LR1i_8QjXyY) by Gui Bibeau.
- [Set up a monorepo with Supabase and Turborepo](https://philipp.steinroetter.com/posts/supabase-turborepo) by Philipp Steinrötter.
- [Twitter clone with Supabase](https://www.youtube.com/watch?v=JnUzV-PpopU) by Code with Guillaume.
- [How Supabase Powers Mockury](https://blog.ikirill.com/how-supabase-powers-mockury) by Kirill Inoz.
- [How to add SIGN-IN WITH APPLE using IONIC ANGULAR and SUPABASE](https://www.youtube.com/watch?v=xhzVyso-9OM) by [Simon Grimm].
- [SvelteKit + Supabase v2 Auth with Magic Links, Providers, Email + Password, & more! 🔐🔴 LIVE](https://www.youtube.com/watch?v=XuyMr4pM4B8) by Johnny Magrippis
- [How we use Supabase at Brick](https://dev.to/vinzent/how-we-use-supabase-at-brick-3a5d) by Vinzent.
- [How Kuiq is using Supabase and serving around 100 APIs](https://kuiq.io/blog/how-kuiq-is-using-supabase-and-serving-around-100-apis) by Taishi
- [How I created an interactive website with React, Chakra UI + Supabase](https://medium.com/@limjoshen/how-i-created-a-personal-website-with-react-chakra-ui-supabase-39661852746a) by Joshen Lim.
- [5 benefits of using Supabase for your SaaS](https://supastarter.dev/blog/five-benefits-of-supabase) by Jonathan Wilke.
- [Getting into the holiday spirit with Astro, React, and Supabase](https://www.aleksandra.codes/astro-supabase) by Aleksandra
- [How I build register and login page with Supabase](https://surajondev.com/2022/12/06/how-i-build-register-and-login-page-using-supabase/) by Suraj Vishwakarma.
- [What the hell is Supabase?](https://www.youtube.com/watch?v=syOnFdFC_5U) by Ayesha Sahar.
- [Comment utiliser Supabase en No-Code avec Weweb ?](https://www.youtube.com/watch?v=g2TLd8vvs0E) by Thomas Buyle.
- [How I used Supabase and n8n for my background tasks](https://mathias.rocks/blog/2022-12-06-how-I-used-supabase/) by Mathias Mitchel
- [Build An Image Gallery With Supabase Storage and React (Upload Images with Supabase)](https://www.youtube.com/watch?v=8tfdY0Sf2rA) by Coopercodes
- [Twitter thread showing how to build a web app](https://twitter.com/thisisisheanesu/status/1600257146792181766) by Isheanesu.
- [This service will run your entire app](https://www.instagram.com/reel/Cl1KWbtDGMR/) by Simon Baker (@allthecode).

It's a lot! There are tons of great resources, going through everything would take a couple of days, so we recommend bookmarking this page! If you like the content, make sure to give feedback to the author and follow them on social.

And don't miss Launch Week 6, we've been working hard to make it our best Launch Week yet 🚀. Get your tickets [here](https://supabase.com/launch-week).
