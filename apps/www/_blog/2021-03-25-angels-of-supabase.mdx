---
title: Angels of Supabase
description: Meet the investors of Supabase.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: angels-of-supabase.png
thumb: angels-of-supabase.png
categories:
  - company
tags:
  - fundraising
date: '2021-03-25'
---

Supabase is an open source Firebase alternative. We're on a mission to build an incredible developer experience around
the world's best open source tools.

To build an amazing developer experience, we need developers to guide us. So after finishing Y Combinator, we took a
deliberate approach to fundraising. We loaded our Seed round with developers, technical founders, and developer advocates:
CTOs who have seen scale, community managers, and hackers. We amassed over 20 angel investors - almost all technical.

The Supabase Team is at full capacity preparing for [Launch Week](/blog/launch-week) and since we're all short on time we decided -
why not ask our Angels to write a fundraising post with us?

So here is our unconventional fundraising announcement, written with a few of the Angels of Supabase.

## <PERSON> (Instagram)

<img
  className="rounded border border-default"
  src="/images/blog/angels/mike-krieger-supabase.png"
  alt="<PERSON>rieger Supabase"
/>

Cofounder & CTO of Instagram. [@mikeyk](https://twitter.com/mikeyk)

#### How many hours per week do you spend coding?

~30 these days

#### Why did you invest in Supabase?

Supabase is building tools that are foundational to almost any interactive, modern app, and doing so in a way that's
really aligned with how I think of building tech stacks. For example, choosing Postgres (and deeply understanding
Postgres' features) as the underlying datastore.

#### Do you write SQL in uppercase or lowercase?

Uppercase but I'm coming around to lowercase!

#### What was the first program you wrote and what was it written with?

A website that I built so I could skip out on writing a school paper instead — HTML and Image `<map>`s for interactivity.

#### What's the worst programming error you've ever made?

On launch day for Instagram, the site was down for a good chunk of time because we hadn't set a favicon.ico _and_
we had Django set up to email us on all errors. Cue storm of email sends every time someone loaded our homepage…and a very unhappy web server.

#### If you could only use one programming language for the rest of your life, what would it be?

Python — though I am very intrigued by Rust.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

Stay curious and don't feel like everything you build has to be "the next big thing"; so many times I've worked on a
fun side project or exploration and ended up learning something I applied to more "serious" work.

## Cassidy Williams (Netlify)

<img
  className="rounded border border-default"
  src="/images/blog/angels/cassidy-williams-supabase.png"
  alt="Cassidy Williams Supabase"
/>

Principal Developer Experience Engineer at Netlify. Outside of that, makes memes, classes, talks, and content for
developers. [@cassidoo](https://twitter.com/cassidoo)

#### How many hours per week do you spend coding?

Probably around 20, depending on the week. Sometimes more!

#### Why did you invest in Supabase?

It's a good tech stack and a tool that I think fills a need for a lot of folks.

#### Do you write SQL in uppercase or lowercase?

Uppercase!

#### What was the first program you wrote and what was it written with?

I started making websites as a teenager, and programmed my graphing calculator with good ol' BASIC!

#### When was the first time you used a computer and what did you do with it?

Oh I was a kid, and definitely used it only for CD-ROM games in those dial-up days.

#### Would you rather fight 100 duck-sized horses or 1 horse-sized duck?

100 duck-sized horses, because I'm a weakling and could probably do better with the smaller things as long as
they aren't too organized.

#### If you could only use one programming language for the rest of your life, what would it be?

Probably JavaScript. It's gotten to that level of power that I could apply it to almost anything.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

Get a calendar you'll actually use and stick to your to-do lists!

## Thorsten Schaeff (Stripe)

<img
  className="rounded border border-default"
  src="/images/blog/angels/thor-supabase.png"
  alt="Thorsten Schaeff Supabase"
/>

Helps developers grow the GDP of the Internet as a Developer Advocate at Stripe. [@thorwebdev](https://twitter.com/thorwebdev)

#### How many hours per week do you spend coding?

Really depends on the projects I'm working on. Sometimes I spend a good amount of time making videos.

#### Why did you invest in Supabase?

The team | The Culture | The DX

#### Do you write SQL in uppercase or lowercase?

lowercase - I prefer not to scream at my DB.

#### What was the first program you wrote and what was it written with?

My first proper project was probably at uni, building an app to alert wind surfers of great wind conditions.
Was built with Python on a Raspberry Pi, jQuery and PhoneGap

#### When was the first time you used a computer and what did you do with it?

When I was 9 years old my dad signed me up for an HTML summer camp. That's how it all started :D

#### Would you rather fight 100 duck-sized horses or 1 horse-sized duck?

I think there's a market for duck-sized horses. Would capture and sell them. Also duck's have some nasty claws,
wouldn't be a good idea to make them bigger!

#### If you could only use one programming language for the rest of your life, what would it be?

I'd do anything and everything in TypeScript if I could!

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

Get involved with and start contributing to open-source. Start creating content about your open-source contributions,
and I'm certain that it will pay off!

## Brian Douglas (GitHub)

<img
  className="rounded border border-default"
  src="/images/blog/angels/brian-douglas-supabase.png"
  alt="Brian Douglas Supabase"
/>

Staff Developer Advocate at GitHub. [@bdougieyo](https://twitter.com/bdougieyo)

#### How many hours per week do you spend coding?

~10 hours a week.

#### Why did you invest in Supabase?

Supabase is scratching an itch for me as a developer. I see the value of having a manageable database based on SQL,
but I don't only have limited time to code during the week and I don't want that to get sucked into the maintenance
and management of that data. I also was a big Firebase fan, and love the developer ergonomics if offered to me as an early user.

#### Do you write SQL in uppercase or lowercase?

Always uppercase, but don't ask me what the acronym stands for.

#### What was the first program you wrote and what was it written with?

The first function programming was an Android app in Java that was just a picture of a cat. When you pet the cat it purred,
it was part of an Android tutorial when I thought I wanted to be an Android dev. I later got an iPhone and decided to never
write Java again when I found Ruby on Rails. I built a Yelp clone for churches, but bailed on that when I took a full time dev role.

#### When was the first time you used a computer and what did you do with it?

My first time using a computer was in second grade and it was to play wolfenstein. We had a computer that we could use in
the office of the apartment complex I grew up in. One of the older kids installed the game and showed the other kids how
to run in from powershell. Probably not the best game to introduce to a 7 year old, but here we are.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

I would tell them to start with creating short form content every day. It easier to make content on things you just
learned, so do that. It is also easier to get hired when you already have content.

## Justin Gage (Retool)

<img
  className="rounded border border-default"
  src="/images/blog/angels/justin-gauge-supabase.png"
  alt="Justin Gage Supabase"
/>

Growth at Retool. Writes [a newsletter](https://technically.dev/) that helps people understand software engineering and tech.
[@itunpredictable](https://twitter.com/itunpredictable)

#### How many hours per week do you spend coding?

It depends – some weeks I’m mostly writing and coordinating, other weeks I’m writing a lot of code. Lets say 20 hours on a good week :)

#### Why did you invest in Supabase?

I had been working on the Firebase integration at Retool for a bit, and kept wondering why it was (a) closed source
and (b) NoSQL only. Over the span of a week, something like 5 or 6 of my developer friends asked me what I thought
about Supabase, so I checked it out – after using the product for a side project, investing was an easy decision.

#### Do you write SQL in uppercase or lowercase?

Lowercase, obviously. I wrote thousands of queries when I worked at DigitalOcean, and we didn’t have time to capitalize things.

#### If you could only use one programming language for the rest of your life, what would it be?

SQL (particularly standard Postgres syntax) is the best language on the planet. Fight me.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

Eh, I can’t really give advice, I haven’t figured anything out myself yet!

## Iheanyi Ekechukwu (PlanetScale)

<img
  className="rounded border border-default"
  src="/images/blog/angels/iheanyi-ekechukwu-supabase.png"
  alt="Iheanyi Ekechukwu Supabase"
/>

Senior Software Engineer at PlanetScale. [@kwuchu](https://twitter.com/kwuchu)

#### How do you pronounce your name?

<video width="99%" controls={true}>
  <source src="/images/blog/angels/kwuchu.mp4" type="video/mp4" playsInline />
</video>

#### How many hours per week do you spend coding?

Pretty much, the majority of my week is spent coding. I'd say realistically, around 30-ish hours a week or so.

#### Why did you invest in Supabase?

The tagline about Supabase being an "open-source Firebase alternative" had me intrigued. As I looked more deeply into
the product, I was impressed by the well-thought out design and also how easy it is to use the product as well.

#### Do you write SQL in uppercase or lowercase?

Uppercase.

#### When was the first time you used a computer and what did you do with it?

During summers as a child, my dad would take me to work with him at the community college he taught at. I'd be sitting
in his office while he was in lectures and actually just use his computer to play that 3D Space Pinball game. I think
I was 5 or 6 years old at the time.

#### If you could only use one programming language for the rest of your life, what would it be?

Honestly, I would probably choose Go. It's a pretty dope language for building out systems.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

Always be learning. There's so many things to learn as an engineer and the job is constantly about learning new things.
Never settle, never get comfortable, learn as much as you can consistently.

## Zach Waterfield (On Deck)

<img
  className="overflow-hidden rounded border border-default"
  src="/images/blog/angels/zach-waterfield-supabase.png"
  alt="Zach Waterfield Supabase"
/>

Engineer at OnDeck, Network Leader at Village Global, Founder of Kopa (W19). [@zlwaterfield](https://twitter.com/zlwaterfield)

#### How many hours per week do you spend coding?

Anywhere from 10 to 50 depending on the week

#### Why did you invest in Supabase?

I have many ideas, and spinning up a backend, db, etc., is so daunting, so I started using Firebase, but it was very
frustrating. When I saw Supabase I instantly knew that was the solution. The ability to use Postgres directly from the
client with authentication built-in was a game-changer. I think it could become the go-to that early-stage companies
build on to simplify their MVPs.

#### Do you write SQL in uppercase or lowercase?

UPPERCASE

#### When was the first time you used a computer and what did you do with it?

Weirdly enough, I didn't own a computer until I was in grade 12 and didn't learn to code until I was in University.
I was the only one in a class of 400+ freshman engineers that had never coded before.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

Network, network, network. Everything I do and all my successes are based on my network. Whether that be from YC, my
university peers, OnDeck, Twitter, etc. Focus on building your network, and good opportunities will start to pop up.

## Shawn Wang (Temporal)

<img
  className="m-0 overflow-hidden rounded border object-cover p-0 border-default "
  src="/images/blog/angels/swyx-supabase.png"
  alt="Shawn Wang Supabase"
/>

Head of Developer Experience at Temporal. [@swyx](https://twitter.com/swyx)

#### How many hours per week do you spend coding?

10

#### Why did you invest in Supabase?

I was extremely impressed by the founders and love the idea of offering a beautiful, integrated developer experience atop
of proven, scalable, open source foundations.

#### Do you write SQL in uppercase or lowercase?

Uppercase if formal, lowercase if lazy!

#### What was the first program you wrote and what was it written with?

We had a computer class in middle school and we were assigned to write a pancake stacking program in BASIC. I was really
obsessed by it and was the only person to submit this assignment complete with a graphical user interface (in BASIC!).
Sadly I didn't see myself as a programmer until 20 years later.

#### If someone wanted to do what you do, what's a piece of advice you'd give them?

[Learn in Public](https://www.swyx.io/LIP) :)

## By the way

We [raised](https://techcrunch.com/2020/12/15/supabase-raises-6m-for-its-open-source-firebase-alternative/) a $6M seed
round led by Coatue, Mozilla, and YC.

Next week is [Launch Week](/blog/launch-week) and we'll be launching one new thing every day for a week.

[Check it out](/blog/launch-week)!
