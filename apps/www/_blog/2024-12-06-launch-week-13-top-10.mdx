---
title: 'Top 10 Launches of Launch Week 13'
description: Highlights from Launch Week 13
author: paul_copplestone
image: launch-week-13/wrap-up/og-top-10.png
thumb: launch-week-13/wrap-up/thumb-top-10.png
launchweek: '13'
categories:
  - launch-week
tags:
  - launch-week
date: '2024-12-06'
toc_depth: 3
---

Here are the top 10 launches from the past week. _You won't believe #7._

## #1: Supabase Cron

Supabase Cron is a new Postgres module for creating and managing recurring tasks. It is deeply integrated with the rest of the Supabase toolset.

[Read more →](/blog/supabase-cron)

## #2: Supabase AI Assistant v2

We overhauled the AI Assistant in the Dashboard and gave it a bunch more “abilities”: from writing Postgres Functions to creating entire databases.

[Read more →](/blog/supabase-ai-assistant-v2)

## #3: Edge Functions: Background Tasks, Ephemeral Storage, and WebSockets

A feature tri-fecta: Edge Functions now support Websockets for things like OpenAI's Realtime API, ephemeral storage for things like zip files, and Background Tasks that continue to run after you've sent a response to your users.

[Read more →](/blog/edge-functions-background-tasks-websockets)

## #4: CLI v2: Config as Code

We released v2 of the CLI, adding support for Configuration as Code. You can now use the CLI in your GitHub actions to keep all of your project configuration in sync.

## #5: High Performance Disks

With advanced disks you can store up to 60 TB of data with 100x improved durability, and provision up to 5x more IOPS than the default disks we offer.

[Read more →](/blog/high-performance-disks)

## #6: Restore to a new Project

You can now launch new projects from the backups of any of your existing projects. This is particularly helpful if you want to do some data engineering without impacting your production database.

[Read more →](/blog/restore-to-a-new-project)

## #7: Bring your own LLM: database.build

"postgres.new" is now ["database.build"](https://database.build) and you can still run everything in your own browser. We've added 3 major features: Bring-your-own-LLM, Live Share, and they ability to deploy your [pglite](https://pglite.dev/) to Supabase.

[Read more →](/blog/database-build-v2)

## #8: Oriole Beta7 - high performance Postgres

We're building a new storage engine for Postgres that's much faster than the current storage engine. We've released some benchmarks and made it available on the Platform.

[Read more →](https://www.orioledb.com/blog/orioledb-beta7-benchmarks)

## #9: Supabase Queues

A Postgres-native, durable Message Queue with guaranteed delivery, improving the scalability and resiliency of your applications.

[Read more →](/blog/supabase-queues)

## #10: Mega Launch Week

This Launch Week is special because we didn't do it alone. This time, more than 20 other devtools joined us to launch their products and features throughout the week. Some things are just more fun when you do them together.

[Read more →](https://launchweek.dev/lw/MEGA)

## Launch Week continues

Next week we have a couple of activities for you to get involved with:

### Hack the Base: Capture the Flag

Want to test hacking skills? We're running a Capture the Flag event called [Hack the Base](https://ctf.supabase.com/). It's a free event that anyone can participate in.

[Read more →](/blog/hack-the-base)

### Launch Week 13: Hackathon

We're running a virtual hackathon starting **now** where you can win prizes for building the best projects using Supabase. We'll be announcing the winners on December 15th.

[Read more →](/events/launch-week-13-hackathon)
