---
title: Supabase Beta December 2022
description: This month the Beta Update is a Launch Week 6 Special, where we review the cascade of announcements.
author: ant_wilson
image: 2023-01-05-december-beta-update/monthly-update-december2022.jpg
thumb: 2023-01-05-december-beta-update/monthly-update-december2022.jpg
categories:
  - product
tags:
  - release-notes
date: '2023-01-05'
toc_depth: 3
---

Happy New Year! Hope you are off to a great start. We closed 2022 on a high note with [Launch Week 6](https://supabase.com/launch-week) and a cascade of announcements. Here is everything we shipped so you can try them all 💻

## Day 1: New Docs

![Day 1: New Docs](/images/blog/2023-01-05-december-beta-update/docs.jpg)

Documentation is super important to us, it's part of the product, and the Supabase docs needed a refresh. That's why we worked on a new redesign, better navigation, better reference docs, and we rebuilt the site using Next.js.

- [Blog Post](https://supabase.com/blog/new-supabase-docs-built-with-nextjs)
- [Video announcement](https://www.youtube.com/watch?v=Q1Amk6iDlF8)

## Day 2: Storage v2 - Image resizing and Smart CDN

![Day 2: Storage v2 - Image resizing and Smart CDN](/images/blog/2023-01-05-december-beta-update/storage-v2-image-rezising.jpg)

We introduced three new features for Supabase Storage: Image resizing, webhooks, and a Smart CDN. These features are designed to work together to deliver a next-gen image resizing system.

- [Blog Post](https://supabase.com/blog/storage-image-resizing-smart-cdn)
- [Video announcement](https://www.youtube.com/watch?v=NpEl20iuOtg)

## Day 3 - Multi-factor Authentication via Row Level Security Enforcement

![Day 3 - Multi-factor Authentication via Row Level Security Enforcement](/images/blog/2023-01-05-december-beta-update/multi-factor-authentication.png)

We released Multi-factor Authentication (MFA) for everyone, including the option to enforce it via RLS 🔐. Plus, in preparation for releasing SAML, we're "dogfooding" the feature with the introduction of Single Sign On (SSO) on our dashboard.

- [Blog post](https://supabase.com/blog/mfa-auth-via-rls)
- [Video announcement](https://www.youtube.com/watch?v=He7LI2mv9v0)

## Day 4 - Supabase Wrappers, a Postgres FDW framework

![Day 4 - Supabase Wrappers, a Postgres FDW framework](/images/blog/2023-01-05-december-beta-update/supabase-wrappers.jpg)

Foreign Data Wrappers are a core feature of PostgreSQL. With Supabase Wrappers, we've extended this feature to query other databases or any other external system (including third-party APIs), using SQL.

- [Blog post](https://supabase.com/blog/postgres-foreign-data-wrappers-rust)
- [Video announcement](https://www.youtube.com/watch?v=QA2qC5F-4OU)

## Day 5 - The Vault is now in Beta

![Day 5 - The Vault is now in Beta](/images/blog/2023-01-05-december-beta-update/vault.jpg)

The Vault is a new Postgres extension that makes it safe and easy to store encrypted secrets and encrypt other stored data in your database.

- [Blog post](https://supabase.com/blog/vault-now-in-beta)
- [Video announcement](https://www.youtube.com/watch?v=QHLPNDrdN2w)

## Community Day

![Community Day](/images/blog/2023-01-05-december-beta-update/community.jpg)

It's starting to feel like Supabase is really just a "community of communities" 🤗. Far too much has happened in the last 3 months for us to possibly feature it all here, so here's a collection of the highlights!

[Blog post](https://supabase.com/blog/launch-week-6-community-day)

## Postgres Ecosystem

We are all in on Postgres. That is why contributing to the ecosystem is always a priority. Launch Week 6 was no exception, with several announcements:

![Postgres Ecosystem](/images/blog/2023-01-05-december-beta-update/postgres-ecosystem.jpg)

- [pg_graphql v1.0](https://supabase.com/blog/pg-graphql-v1)
- [PostgREST 11 pre-release](https://supabase.com/blog/postgrest-11-prerelease)
- [What's new in Postgres 15\?](https://supabase.com/blog/new-in-postgres-15)
- [pg_crdt - an experimental CRDT extension for Postgres](https://supabase.com/blog/postgres-crdt)

## Custom Domains and Point-in-Time Recovery

![Custom Domains and Point-in-Time Recovery](/images/blog/2023-01-05-december-beta-update/custom-domains-pitr.jpg)

Two key features to level up your projects are now available: Custom domains allow you to use your own domain for your Supabase project, while PITR provides the ability for a database to be restored at any specified point in time.

- [Custom Domains](https://supabase.com/blog/custom-domain-names)
- [Point in Time Recovery](https://supabase.com/blog/postgres-point-in-time-recovery)

## Hackathon Winners

![Hackathon Winners](/images/blog/2023-01-05-december-beta-update/lw-6-hackathon.jpg)

It was great to see so many high-quality projects. The decision wasn't easy, but in the end, the panel of judges chose Depulso as the winner of the Best Overall Project.

Congratulations 👏 to [@\_iamkarthikeyan](https://twitter.com/_iamkarthikeyan) and [@SindhuMansi](https://twitter.com/SindhuMansi) who get each a Supabase Darkmode Keyboard and the $1500 Github sponsorship!

- [Full list of Winners](https://supabase.com/blog/launch-week-6-hackathon-winners)
- [See all the submissions](https://www.madewithsupabase.com/launch-week-6)

## Extended Community Highlights

![Community](/images/blog/2022-june/community.jpg)

- FlutterFlow and Supabase integration. [Video](https://www.youtube.com/watch?v=hw9Q-NjASbU)
- ClickHouse and PostgreSQL - a match made in data heaven. [Article](https://clickhouse.com/blog/migrating-data-between-clickhouse-postgres)
- Build any Front-end like Admin Panel or CRM for your Supabase. [Article](https://blog.jetadmin.io/how-to-build-any-front-end-such-as-admin-panel-or-crm-for-your-supabase-in-minutes/)
- Everything Svelte: a course with all you need to know to build a modern web application. [Course](https://www.everythingsvelte.com/)
- Edgy Edge Functions: a look at a new Edge Function per week. [Youtube Playlist](https://www.youtube.com/playlist?list=PL5S4mPUpp4OulD3olUW8Eq1IYKpUbk5Ob)
- Pixels, an app that provides canvas to create pixel art. [App](https://pixels.refine.dev/) | [Github Repo](https://github.com/refinedev/refine/tree/next/examples/pixels)
- Supabase Angular authentication with RxJS Observables. [Tutorial](https://dev.to/kylerummens/supabase-angular-authentication-with-rxjs-observables-3d0f)
- Create Calendar Events in React With Google Calendar API and Supabase. [Video Tutorial](https://www.youtube.com/watch?v=tgcCl52EN84)
- Going Serverless with Supabase. [Part 1](https://dev.to/davepar/going-serverless-with-supabase-103h)
- Supabase a Complete Backend Solution. [Video](https://www.youtube.com/watch?v=Ve0qE55fw0Y)
- Supabase OAuth with SvelteKit (Discord, Google, GitHub). [Video](https://www.youtube.com/watch?v=KfezTtt2GsA)

## Meme Zone

As always, one of our favorite memes from last month. [Follow us on Twitter](https://twitter.com/supabase) for more.

![Supabase Beta December Meme](/images/blog/2023-01-05-december-beta-update/beta-update-december-meme.jpeg)

See you next month!
