---
title: 'Supabase Launch Week X Hackathon'
description: Build an Open Source Project over 10 days. 5 prize categories.
author: ant_wilson
image: lwx-hackathon/ogimage.png
thumb: lwx-hackathon/ogimage.png
launchweek: x
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2023-12-05'
toc_depth: 2
---

[Supabase Launch Week X](https://supabase.com/launch-week) is about to start and we're running an async hackathon alongside it.

The hackathon starts Friday 8th December at 09:00 am PT and ends Sunday 17th December at 11:59 pm PT. You can win extremely limited edition Launch Week swag and add your name to the Supabase Hackathon Hall of Fame.

For some inspiration check out all the [winners from previous hackathons](https://supabase.com/blog/tags/hackathon).

This is the perfect excuse to "Build in a weekend, scale to millions". Since you retain all the rights to your submissions, you can use the hackathon as a launch pad for your new Startup ideas, side-project, or indie hack.

![Supabase Hackathon Meme](/images/blog/lwx-hackathon/supabase-hackathon-meme.jpg)

## Key Facts

- You have 10 days to build a new **Open Source** project using Supabase in some capacity.
  - Starting 09:00 am PT Friday 8th December 2023
  - The submission deadline is 11:59 pm Sunday midnight PT 17th December 2023
- Enter as an individual, or as a team of up to 5 people
- Build whatever you want - a project, app, tool, library. Anything.

## Prizes

![Supabase LWX Swag](/images/blog/lwx-hackathon/prize1.jpg)

There are 5 categories, there will be prizes for:

- Best overall project
- Best use of AI
- Most fun / best easter egg
- Most technically impressive
- Most visually pleasing

There will be a winner and a runner-up prize for each category. Every team member on winning/runner-up teams gets a Supabase Launch Week swag kit.

![Supabase LWX Swag](/images/blog/lwx-hackathon/prize2.jpg)

## Submission

You should submit your project via [MadewithSupabase](https://madewithsupabase.com/hackathons/launch-week-x) (the form will be live once the hackathon starts) before 11:59 pm Sunday midnight PT 17th December 2023. Extra points if you include a simple video demoing the app in the description.

## Judges

The Supabase team will judge the winners for each category.
We will be looking for:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
- FUN! 😃

## Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be on multiple teams
- One submission per team
- It's not a requirement to use AI
- All design elements, code, etc. for your project must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- (optional) Include a link to a demo video along with the submission for extra points

## Resources

### Supabase resources to get you started

- [OpenAI with Edge Functions YouTube video](https://www.youtube.com/watch?v=29p8kIqyU_Y)
- [Streaming Data in Edge Functions](https://www.youtube.com/watch?v=9N66JBRLNYU)
- [Storing OpenAI embeddings in Postgres with pgvector article](https://supabase.com/blog/openai-embeddings-postgres-vector)
- [pgvector guide](https://supabase.com/docs/guides/database/extensions/pgvector)

### Community help

The Supabase Team will be taking part in the Hackathon and you'll find us live to build in our discord all week. Please join us by building in public:

- Text channel: [#hackathon](https://discord.gg/UYyweApy)
- Audio channel: [#hackathon](https://discord.gg/Vj3mTPwH)

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com/)

![Supabase Discord Channel](/images/blog/lw7-hackathon/discord.png)

## Launch Week

Don't forget to check out the new features being announced as part of [Launch Week](https://supabase.com/launch-week).

- [Previous Hackathon Prize Winners](https://supabase.com/blog/tags/hackathon)

## Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required.
- By making a submission you grant Supabase permission to use screenshots, code snippets, and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
