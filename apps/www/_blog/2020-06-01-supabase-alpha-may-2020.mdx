---
title: Supabase Alpha May 2020
description: Three months of building
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
categories:
  - product
tags:
  - supabase
date: '06-01-2020'
video: https://www.youtube.com/v/e4qXmcEFaUs
---

It has been a monster month at [Supabase](/) and we're back with a video update. It's a longer video this month because we have a couple of housekeeping items.

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/e4qXmcEFaUs"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

## Housekeeping

We got into Y Combinator :tada:. The cohort has just started and it's fully remote so it will be a unique experience, even by normal YC standards.

Normally we wouldn't mention this until our official YC launch in September but the cat's out of the bag since @vira28 [posted about us on Hacker News](https://news.ycombinator.com/item?id=23319901).

We've seen a huge response from the tech community. We're still 'very alpha' so the attention may have been better in September but everyone has been patient with the bugs and instability. It just goes to show: ship early and often because nobody knows when you might get picked up.

## Updates

OK now for the important part - as promised, we will continue to release our updates here in the DEV community first. Skip to 1m18s in the video if you just want to see what we've been up to.

### Fresh UI

We've revamped the UI to fit in some more features that we're building.

![Fresh UI](/images/blog/2020/jzzm7u56ns6ega9uyc4j.png)

### Table View

This one is still very unstable, but we wanted to ship it anyway. It's a little hidden away so you'll have to hunt for it :). Give us a couple of months and we promise this will be as good as (better than?) Airtable.

![Table view](/images/blog/2020/jalcaoz4lsp2b6wegah5.png)

#### And more

- ⚡ Realtime listeners! Subscribe to your database just like you would with Firebase.
- 🤖 Instant RESTful APIs that update when you update your schema. Supabase introspects your schema and updates your API and documentation.
- 📓 Auto-documentation for your APIs and Postgres schema. What's better than documentation? Documentation that you don't have to manually keep up to date.

## Follow us

Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard)

Make sure to star us on github! [github.com/supabase/supabase](https://github.com/supabase/supabase)
