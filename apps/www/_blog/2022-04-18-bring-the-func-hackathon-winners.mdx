---
title: 'Bring the Func Hackathon Winners 2022'
description: Celebrating many amazing OSS Hackathon projects using GraphQL and Edge Functions.
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: hackathon-winners/bring-the-func/funcwinners.png
thumb: hackathon-winners/bring-the-func/funcwinners.png
categories:
  - developers
tags:
  - community
  - hackathon
date: '2022-04-18'
toc_depth: 2
---

To cool down from [our latest launch week](/blog/supabase-launch-week-four) we (virtually) sat down with y'all and started hacking, and once again we absolutely loved your hackathon submissions and are excited to send some extremely limited swag your way and plant some trees for each of your submitted projects!

As always, it was challenging to pick the winners from all the great submissions! You can see all the amazing projects submitted on [madewithsupabase.com](https://www.madewithsupabase.com/bring-the-func). And now, without further ado, let's look at some of our favourites in more detail:

## Most Impressive GraphQL Project

### Winner

[mintbaseXsupabase](https://github.com/3lLobo/mintbaseXsupabase) - by [@Flowolfpro](https://twitter.com/Flowolfpro) and [@the_reshma](https://twitter.com/the_reshma)

Think NFTs meet Instagram, this app lets you browse, like, and comment on NFTs.

[![mintbaseXsupabase](https://madewithsupabase-git-bring-the-func-zernonia.vercel.app/api/resize?link=mintbasexsupabase-1dttd-162639349-3f334480-f8a9-4b58-88b1-0a6688a16ffapng&w=1000)](https://github.com/3lLobo/mintbaseXsupabase)

### Runner Up

[Pgm Dictionary](https://github.com/n4ze3m/PgmDictionary) - by [@n4ze3m](https://twitter.com/n4ze3m)

This UrbanDictionary clone uses Next.js, Mantine, and Supabase to power a community dictionary project.

A great idea that deserves some silver tees!

[![Pgm Dictionary](https://madewithsupabase-git-bring-the-func-zernonia.vercel.app/api/resize?link=pgm-26sp1-screenzy-1649267250672png&w=1000)](https://github.com/n4ze3m/PgmDictionary)

## Most Interesting Usage Of Edge Functions

### Winner

Wen NFT - by @gbibeaul [^1]

Next up is the category for our newest feature, Edge Functions! The winner here is Wen NFT, which helps anyone manage NFT communities by helping to prove ownership for community access.

[![Wen NFT](https://madewithsupabase-git-bring-the-func-zernonia.vercel.app/api/resize?link=wen-nft-37xz7-screenshot-2022-04-03-at-19-31-44-web3-fullstackpng&w=1000)](https://github.com/gbibeaul/supabase-wen)

### Runner Up

[DocuPool](https://github.com/emilioschepis/docupool) - by [@emilioschepis](https://twitter.com/emilioschepis) and brother Federico.

Docupool is our runner up in the Edge Functions category. Emilio and brother Federico built Docupool to let you share documents and notes, and unlock other people’s content with tokens you collect through the app.

[![DocuPool](https://madewithsupabase-git-bring-the-func-zernonia.vercel.app/api/resize?link=docupool-document-sharing-platform-svuxo-screen-shot-2022-04-10-at-222753png&w=1000)](https://github.com/emilioschepis/docupool)

## Most Fun / Most Visually Pleasing

### Winner

[AbileneX](https://github.com/okezieuc/abilenex) - by [@okeziechiedozie](https://twitter.com/okeziechiedozie)

Our final category is Most Fun or Visually Pleasing, and AbileneX is our winner. This free tool creates links you can share to gather feedback on your ideas.

[![AbileneX](https://madewithsupabase-git-bring-the-func-zernonia.vercel.app/api/resize?link=abilenex-the-revolutionary-idea-feedback-tool-7iaib-cover-imagepng&w=1000)](https://github.com/okezieuc/abilenex)

### Runner Up

[Kardow](https://github.com/Kardow/kardow-app) - by [SimonGingras18](https://twitter.com/SimonGingras18)

Kardow is our runner up for Most Fun or Visually Pleasing. This project management tool lets you customize your UI while you add and manage your tasks.

[![Kardow](https://madewithsupabase-git-bring-the-func-zernonia.vercel.app/api/resize?link=kardow-1ust9-kardow-templatepng&w=1000)](https://github.com/Kardow/kardow-app)

## The Prizes

As is now tradition with Supabase Hackathons, the winners will receive an extremely limited edition gold medal shirt.

![Gold Tshirt](/images/blog/hackathon-winners/bring-the-func/gold_function_tee.png)

And the runner uppers will receive the same shirt but in silver. These shirts are limited to winners of the hackathon and will never be produced again. Keep them safe like your favourite NFTs!

## Hang out with us and the community

If you want to join the community and build with us, find other people using Supabase, or if you just want to chill and watch people build, come and join us in Discord!

[Join our Discord](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Get Started Guides

If you're inspired to build, check out some of the latest resources:

- Getting started with [GraphQL on Supabase](https://supabase.com/blog/graphql-now-available#getting-started)
- [GraphQL HackerNews clone](https://supabase-graphql-example.vercel.app/)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
- [Edge Functions Quickstart video](https://youtu.be/5OWH9c4u68M)
- [Supabase CLI local development guide](https://supabase.com/docs/guides/local-development)
- [Edge Functions examples](https://github.com/supabase/supabase/tree/master/examples/edge-functions)

[^1]: Updated on June 14 2022 for search optimization: Removed links as account does not exist anymore on GitHub
