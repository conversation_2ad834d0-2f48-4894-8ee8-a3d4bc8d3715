---
title: 'Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers'
description: Supabase Studio now comes with an AI assisted SQL Editor, schema diagrams, and much more.
launchweek: '8'
categories:
  - product
tags:
  - launch-week
  - studio
  - AI
date: '2023-08-09'
toc_depth: 3
author: alaister,gregnr,joshen<PERSON>
image: launch-week-8/day-3/og-day3.jpg
thumb: launch-week-8/day-3/thumb-day3.jpg
---

Supabase Studio 3.0 is here, with some huge new features, including a brand new Supabase AI, integrated right into our SQL Editor. If you hate writing SQL, you'll love this update.

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/51tCMQPiitQ"
    title="YouTube video player"
    frameBorder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  ></iframe>
</div>

Here's the highlight reel:

<ul>
  {/* prettier-ignore */}
  <li>[**Supabase AI in the SQL Editor**](#supabase-ai-right-in-the-sql-editor): inline AI, always ready to help</li>
  <li>[**Schema Visualizer**](#schema-visualizer): — see all your table schemas visually.</li>
  <li>[**Role Management**](#role-management): — fine-grained access to table data</li>
  <li>[**Shared SQL Snippets**](#shared-sql-snippets): — share your snippets with the team</li>
  <li>[**Database Migration UI**](#database-migration-ui): — your database, with receipts</li>
  <li>[**Wrappers UI**](#wrappers-ui): — easily query foreign data</li>
</ul>

## Supabase AI, right in the SQL Editor

<video width="99%" autoPlay loop muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/lw8/supabase-ai.mp4"
    type="video/mp4"
  />
</video>

In Launch Week 7, [we added Supabase AI to the Studio](https://supabase.com/blog/supabase-studio-2.0). Through our ⌘K menu, you could ask Supabase AI to do all sorts of common tasks — create tables, views, and indexes, write database functions, write RLS policies, and more.

After this release, we had two key realizations:

1. people love having computers write their SQL for them!
2. many of you are using the SQL Editor as the heart (and engine!) of your projects.

Today, we're releasing a huge improvement to our SQL Editor. First up, we've added Supabase AI directly into the editor. It's always accessible, and ready to help. As before, you can give it a prompt (`create an orders table for me`) and it will return the SQL for you, but now it does so much more.

Supabase AI is aware of the SQL snippet in the editor and can modify it for you. You can ask it to change `customers` to `customer_orders`, for example. You can interact with the code the same way you would converse with ChatGPT until it's just right.

<Img
  src="/images/blog/launch-week-8/day-3/create-orders.png"
  alt="Create a table"
  zoomable={false}
/>

Next, we've added a diff view for changes that Supabase AI makes to your SQL snippet. You can tell Supabase AI what you want changed, and visualize it as you would a Git diff. From this view, you can accept or reject the diffs, and keep asking Supabase AI to make changes until you're satisfied.

<Img
  src="/images/blog/launch-week-8/day-3/create-orders-diff.png"
  alt="Create a table diff"
  zoomable={false}
/>

We've wondered for a long time how to make it easier to teach developers how to use SQL. It's fortunate we didn't solve this problem too quickly, as it turns out that AI does a much better job than we could do ourselves.

With Supabase AI, you won't even need the whole weekend to scale to millions. Head over to the [SQL Editor](https://supabase.com/dashboard/project/_/sql) and give it a try!

In the coming months, we're looking to sprinkle Supabase AI through more parts of the Studio. With Postgres under the hood, there's so much we can do with SQL and a little bit of AI to help you move fast. Keep an eye out for the Supabase AI icon, you never know where it will pop up next.

![Supabase AI](/images/blog/launch-week-8/day-2/supabase-ai-loading-animation.gif)

---

Along with these huge AI features, we also added a bunch of new improvements elsewhere around the Studio. Several of these features have come either from requests from the community or are contributions by community members themselves.

<div className="bg-alternative border rounded-lg px-10 py-2 italic">

📢 Many of the features and enhancements below came from user requests
[Please keep them coming](https://github.com/orgs/supabase/discussions/categories/feature-requests)!

</div>

## Schema Visualizer

<Img src="/images/blog/launch-week-8/day-3/visualizer.png" alt="Schema Visualizer" />

For a while now, many Supabase users have been using [Zernonia's](https://github.com/zernonia) [Supabase Schema visualization tool](https://supabase-schema.vercel.app/). While this was an amazing tool, many users wanted to see something like this directly integrated into the Studio.

We opened an [Issue for it on Github](https://github.com/supabase/supabase/issues/15585) and within a day or two the wheels were in motion. After a couple of weeks, the feature was polished up and merged. It's inspiring to see the power of open source at work. This feature wasn't trivial, and to see community members take it from feature request to production in just a couple of weeks is mind-blowing. Unquestionably, we have one of the best open source communities out there. Huge thanks to [kamilogorek](https://github.com/kamilogorek) and [adiologydev](https://github.com/adiologydev) for their work on this!

Special thanks as well to [Zernonia](https://twitter.com/zernonia) for providing the inspiration for this great new feature! OSS FTW.

## Role Management

Postgres has built-in support for managing users and roles, and this release, we're happy to release a UI for it in the Studio. This is another extremely common feature request, fulfilled almost completely by a community member.

A few months back, we saw this [PR](https://github.com/supabase/supabase/pull/13745) come in out of the blue from [HTMHell](https://github.com/HTMHell). They built the entire thing with zero help or direction from our team. We were blown away. We had some changes to make on the backend to properly accommodate the UI, and now we're almost ready to get this out into the wild!

<Img src="/images/blog/launch-week-8/day-3/role-management.png" alt="Role management" />

Due to the security focus of this feature, we want to make sure we do a very thorough job of testing, so we're hoping to make this generally available in the next week or so.

Massive thanks to [HTMHell](https://github.com/HTMHell) (amazing handle btw) for the work on this!

## Shared SQL Snippets

Speaking of commonly requested features, this one has to be in the all-time top 5. Your beautiful, hand-crafted SQL snippets used to be yours and yours alone. Now you can share them with team members and let them bask in your technical prowess.

<Img
  src="/images/blog/launch-week-8/day-3/share-query.png"
  alt="Share SQL Snippets"
  zoomable={false}
/>

You can create a set of project-wide snippets for doing common tasks, making it faster to collaborate and build. To share a snippet, just take a personal snippet that ~~Supabase AI~~ you wrote and share it with the project. It will show up in a new Project Snippets list that's visible to everyone on the team.

Teamwork makes the dream work!

## Database Migration UI

We're releasing a new UI for working with database migrations right from the Studio. Database migrations give you a way to update your database using version-controlled SQL files. They describe changes that you want to make to your database, and also keep track of what changes have been made to your database over time.

As migrations get run against your project from the CLI, you can see information in the Studio about when the migration was run, by who and what changes were made. [See the documentation](https://supabase.com/docs/guides/deployment/database-migrations) to get started with migrations.

<Img src="/images/blog/launch-week-8/day-3/migrations.png" alt="Migrations UI" />

## Wrappers UI

During Launch Week 6, we [announced](https://supabase.com/blog/postgres-foreign-data-wrappers-rust) Supabase Wrappers — a framework for building foreign data wrappers with Postgres. Wrappers allow your Supabase project to act as a one-stop hub for your data.

When we released Wrappers, we had support for just two providers — Stripe and Firebase. We're now up to 6! This round, we're happy to release support for S3, ClickHouse, BigQuery, and Logflare! Wrappers add a mind-bending level of extensibility to Supabase projects. You can pull data straight into your projects as though they were normal Supabase tables — you can even query them with our client libraries. It's a whole new world of possibilities.

<Img src="/images/blog/launch-week-8/day-3/wrappers.png" alt="Wrappers" />

## Wrapping Up

We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from [Feature Requests](https://github.com/orgs/supabase/discussions/categories/feature-requests) on GitHub. Thanks to everyone who has taken the time to submit these, and encourage submissions for anything else you'd like to see.

## More Launch Week 8

- [Supabase Local Dev: migrations, branching, and observability](https://supabase.com/blog/supabase-local-dev)
- [Hugging Face is now supported in Supabase](https://supabase.com/blog/hugging-face-supabase)
- [Launch Week 8](https://supabase.com/launch-week)
- [Coding the stars - an interactive constellation with Three.js and React Three Fiber](https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber)
- [Why we'll stay remote](https://supabase.com/blog/why-supabase-remote)
- [Postgres Language Server](https://github.com/supabase/postgres_lsp)
