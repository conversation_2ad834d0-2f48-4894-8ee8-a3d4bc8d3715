---
title: Supabase Beta August 2022
description: Launch Week Special. See everything we shipped, plus winners of the Hackathon and the extended Community Highlights
author: ant_wilson
image: 2022-august/monthly-update-august-2022.jpg
thumb: 2022-august/monthly-update-august-2022.jpg
categories:
  - product
tags:
  - release-notes
date: '2022-09-07'
toc_depth: 3
---

This month the Beta Update is a Launch Week special. #SupaLaunchWeek 5 just happened and it was a big one, so we revisit everything we shipped, congratulate the winners of the Supa Hackathon, and of course we have the extended the Community Highlights.

## Day 1 - Supabase CLI v1 and Management API Beta

![Day 1 - Supabase CLI v1 and Management API Beta](/images/blog/2022-august/CLI.png)

The Supabase CLI is now in v1.0 (including the ability to generate TypeScript types 🎉). We also released the Management API Beta, a REST API that opens the door to a whole new suite of integrations (Zapier, Terraform, Pulumi, you name it). Full programmatic control of your projects and orgs is on the way.

[Blog Post](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)

[Video Announcement](https://www.youtube.com/watch?v=OpPOaJI_Z28)

## Day 2 - supabase-js v2 Release Candidate

![Day 2 - supabase-js v2 Release Candidate](/images/blog/2022-august/supabase_js.png)

**[supabase-js v2](https://github.com/supabase/supabase-js)** focuses on "quality-of-life" improvements for developers and includes Type Support, new Auth Methods, async Auth overhaul, improvements for Edge Functions, and more. We couldn't have done this without our amazing Community, so thanks a lot to everyone who contributed.

Try it out by running `npm i @supabase/supabase-js@rc`

[Blog Post](https://supabase.com/blog/supabase-js-v2)

[Video Announcement](https://youtu.be/iqZlPtl_b-I)

## Day 3 - Supabase is SOC2 compliant

![Day 3 - Supabase is SOC2 compliant](/images/blog/2022-august/security.png)

Our customers can rest assured knowing their information is secure and private 🔒. The blog post explains the process we went through to get there and is very useful for anyone building a SaaS product.

[Blog Post](https://supabase.com/blog/supabase-soc2)

[Security at Supabase](https://supabase.com/security)

[Video Announcement](https://youtu.be/6bGQotxisoY)

## Day 4 - Realtime: Multiplayer Edition

![Day 4 - Realtime: Multiplayer Edition](/images/blog/2022-august/realtime.png)

Next level Realtime is here ⚡️. Presence and Broadcast are two key blocks developers can use to build the digital experiences users want. All projects now have access to these features. Try it out in combination with supabase-js v2.

[Blog Post](https://supabase.com/blog/supabase-realtime-multiplayer-general-availability)

[Video Announcement](https://youtu.be/CGZr5tybW18)

## Day 5 - Community Day and One More Thing

![Day 5 - Community Day and One More Thing](/images/blog/2022-august/day-5.png)

We wrapped Launch Week 5 with contributors, partners, and friends and the traditional One More Thing... that was actually SIX more things: [Supabase Vault](https://supabase.com/blog/supabase-vault), Auth UI, Dashboard permissions, [JSON schema validation](https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation), pg_graphql v0.4.0, MFA early-access.

[Community Day Blog Post](https://supabase.com/blog/launch-week-5-community-day)

[Community Day Video Announcement](https://www.youtube.com/watch?v=s9UePQjLT0U)

[One More Thing Blog Post](https://supabase.com/blog/launch-week-5-one-more-thing)

## Launch Week 5 Hackathon

![Launch Week 5 Hackathon](/images/blog/2022-august/hackathon.png)

We had a huge amount of open source submissions 🤯. The selection process was not easy as it wasn't only quantity, but also quality. After a thorough review, we declared [Supabase Cache Helpers](https://github.com/psteinroe/supabase-cache-helpers) the overall winner of the $1500 GitHub sponsorship and Gold SupaCap. Congratulations to [@psteinroe](https://twitter.com/psteinroe) 👏

[Full list of winners](https://supabase.com/blog/launch-week-5-hackathon-winners)

[Check all the submissions in Made with Supabase](https://www.madewithsupabase.com/launch-week-5)

---

## Platform Updates

The following changes to the Supabase Platform will take effect from September 11th at 7 pm PDT.

- HTTP API requests to Supabase will automatically be redirected to HTTPS.
- The API Key was passed to Supabase both in the `Authorization` header and in a separate `apiKey` header. This led to confusion among new users of Supabase who used the API directly. It is no longer required to send the anon key or service key via the `apiKey` header. If you are using Supabase via our client libraries, no change is required from your side.

## Supabase Migration Guides

![Supabase Migration Guides](/images/blog/2022-august/migration-to-supabase-guides.jpg)

Our guides and tools make it super easy to migrate your projects to Supabase:

- [Firebase Auth Migration](https://supabase.com/docs/guides/migrations/firebase-auth)
- [Firestore Data Migration](https://supabase.com/docs/guides/migrations/firestore-data)
- [Firebase Storage Migration](https://supabase.com/docs/guides/migrations/firebase-storage)
- [Migrate from Heroku to Supabase](https://supabase.com/docs/guides/migrations/heroku)

## Webinar: How Netlify and Supabase Enables “Supa” DX

![Webinar: How Netlify and Supabase Enables “Supa” DX](/images/blog/2022-august/webinar.jpg)

Our friends from Netlify invited Ant to their new webinar series. He and Netlify's VP of Partnerships & Ecosystems, Steven Larsen, will show you how to:

- Deploy Supabase's open source backend together with Netlify
- Build User Management without handing over user data to any third parties
- Upload files and folders to the cloud without needing to tack on additional tools

[Save your seat.](https://www.netlify.com/resources/webinars/how-netlify-supabase-enables-supa-dx/)

## Extended Community Highlights

![Community](/images/blog/2022-june/community.jpg)

- Inian shared our journey with Cloudflare. [Video](https://twitter.com/Cloudflare/status/1557728943901675520)
- Supabase Tips: Introduction to Supabase Storage. [Video](https://www.youtube.com/watch?v=J9mTPY8rIXE)
- Zack DeRose playing around with Supabase and Nx. [Part 1](https://www.youtube.com/watch?v=OTh5GBBfr4E) | [Part 2](https://www.youtube.com/watch?v=_5gJi_xwpzk)
- A new guide showing how to combine Supabase and Directus. [Guide](https://supabase.com/partners/integrations/directus)
- Supabase with Flutter course on raywenderlich. [Course](https://www.raywenderlich.com/33619647-supabase-with-flutter)
- Build a full-stack app with Next.js and Supabase on the LogRocket blog. [Tutorial](https://blog.logrocket.com/build-full-stack-app-next-js-supabase/)
- Supabase Crash Course by The Net Ninja. [Video Courses](https://www.youtube.com/watch?v=ydz7Dj5QHKY&list=PL4cUxeGkcC9hUb6sHthUEwG7r9VDPBMKO)
- How to build a Grocery Application with Webflow CMS using DhiWise. [Tutorial](https://dev.to/saloni137/how-to-build-a-grocery-application-with-webflow-cms-using-dhiwise-1a72)
- How To Create a Full Stack app with SolidJS, Supabase, and TailwindCSS [Video and Tutorial.](https://blog.chetanverma.com/how-to-create-a-full-stack-app-with-solidjs-supabase-and-tailwindcss)
- The WalletConnect Cloud now supports Sign in with Ethereum. [Announcement](https://twitter.com/TheHarryET/status/1559861021845643265).
- Building In Public: Cartta Tech Stack. [Article](https://dev.to/fvaldes33/building-in-public-cartta-tech-stack-5en0)
- Supabase + Vue 3 in 12 minutes. [Video](https://www.youtube.com/watch?v=YN32uVqAXw8&feature=emb_title)

## We're hiring

Come join one of the fastest growing open source projects ever 🤗

- [Lead Billing Engineer](https://boards.greenhouse.io/supabase/jobs/4652333004)
- [Customer Success (US time zone)](https://boards.greenhouse.io/supabase/jobs/4594393004)
- [Support Engineers](https://boards.greenhouse.io/supabase/jobs/4191650004)
- [View all our openings](https://boards.greenhouse.io/supabase)

---

## Meme Zone

If you made it this far in the blog post you deserve a treat. [Follow us on Twitter](https://twitter.com/supabase) for more.

![Supabase meme august 2022](/images/blog/2022-august/supabase-beta-update-august-2022-meme.png)

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
