---
title: 'Branching now Publicly Available'
description: 'Supabase Branching is now available on Pro Plan and above.'
author: alais<PERSON>,qiao
image: ga-week/branching-publicly-available/og.png
thumb: ga-week/branching-publicly-available/thumb.png
categories:
  - product
tags:
  - launch-week
  - database
date: '2024-04-15'
toc_depth: 3
launchweek: '11'
---

tl;dr: Supabase Branching is now in open beta! You can enable it on any project that's Pro Plan or above.

## What is Branching?

Branching is a seamless integration of Git with your development workflow, extending beyond your local environment to a remote database. Leveraging Git, particularly focusing on GitHub initially, each time a Pull Request is opened, a corresponding "Preview Environment" is spawned.

<Img
  alt="Every Supabase Preview is a dedicated instance, with a full suite of Supabase services."
  src={{
    light: '/images/blog/lwx-supabase-branching/isolated-instances--light.png',
    dark: '/images/blog/lwx-supabase-branching/isolated-instances--dark.png',
  }}
  caption="Every Supabase Preview is a dedicated instance, with a full suite of Supabase services."
  captionAlign="left"
/>

Preview Branches are essentially full Supabase instances. Every push triggers migrations from the `./supabase/migrations` folder, ensuring team synchronization and a shared source of truth. When you merge a Pull Request, your migrations are applied to the Production database.

We [announced Branching](https://supabase.com/blog/supabase-branching) a few months ago in our previous Launch Week, with a deep dive on a few of the features like data seeding, integrations with Vercel, and seamless handling of environment variables. Since launching Branching for early-access we've worked with early users of all sizes. Today we're making Branching available to everyone.

## New Features

Our open Beta introduces a number of requested features:

### Edge Function support

Branching now deploys your Edge Functions along with your migrations. Any Functions added or changed in your `./supabase/functions` will automatically be deployed without any extra configuration.

### Monorepo support

You can now set a custom Supabase directory path which allows for monorepo support. You can also choose to only spin up new branches when there are changes inside your Supabase directory. See all the configuration settings in your projects [here](https://supabase.com/dashboard/project/_/settings/integrations).

### Persistent branches

<Img
  alt="Every Supabase Preview is a dedicated instance, with a full suite of Supabase services."
  src={{
    light: '/images/blog/ga-week/branching-publicly-available/persistent-branches--light.jpg',
    dark: '/images/blog/ga-week/branching-publicly-available/persistent-branches--dark.jpg',
  }}
  captionAlign="left"
/>

We had quite a few users of branching request for long-running branches so we added the concept of persistent branches. In persistent mode, a branch will remain active even after the underlying PR merges or closes.

Please note that branches should still be treated as replaceable at any time. Persistent or ephemeral Branches should not be used for production data.

## Feedback

A special thank you to all our early-access branching users who provided lots of actionable feedback. Our feature development was largely driven by the direct feedback from our users.

We still have many features to add to branching before 1.0, so please continue [sending us your feedback](https://github.com/orgs/supabase/discussions/18937)!

## Getting Started

You can easily get started with Branching by following our [Getting Started Guide](https://supabase.com/docs/guides/platform/branching#how-to-use-supabase-branching).
