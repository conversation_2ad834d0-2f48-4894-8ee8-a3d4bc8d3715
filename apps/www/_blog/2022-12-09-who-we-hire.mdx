---
title: 'Who We Hire at Supabase'
description: Traits we look for to maintain a culture of shipping fast and often
video: https://www.youtube.com/v/-BG9XptyCKI
author: ant_wilson
image: who-we-hire.jpg
thumb: who-we-hire-cover.jpg
categories:
  - company
tags:
  - launch-week
date: '2022-12-09'
toc_depth: 3
youtubeHero: https://www.youtube-nocookie.com/embed/-BG9XptyCKI
---

It’s been 12 months since I wrote [How we launch at Supabase](https://supabase.com/blog/supabase-how-we-launch), which details our Launch strategy. This methodology helped us grow our hosted platform 46% month-over-month for the first 18 months since [first launching on Hacker News](https://news.ycombinator.com/item?id=23319901) in summer 2020.

In the last 12 months we’ve continued to grow the number of databases deployed on our platform by a further 3.5x and increased revenue by 1300%, all thanks to a relentless team and intense shipping schedule.

We’ve upgraded our methodology in a few ways, which I’ll touch on briefly, but it’s become clear in the time that we’ve been running Supabase that whilst methodology is important - having the right people on the bus is paramount. It’s the team who are relentlessly pushing us to do more, better, faster.

Here I’ll discuss some of the traits that have shaped culture at Supabase, and defined what we look for in new candidates.

![A chart showing growth at Supabase](/images/blog/how-it-started.jpg)

Here’s some traits we’ve observed in people at Supabase that make them effective team members:

### Extreme effectiveness when working asynchronously

People who require a lot of face-to-face collaboration are unlikely to be successful at Supabase. Major projects have gone from inception, through implementation, to marketing and launch without a single synchronous meeting. Our team is now 60 people spread across 25 different countries. We focus on hiring the best person for the job regardless of geography. A nice side effect of this is excellent global support coverage (everyone from the CFO to the infra team has “front line support” listed as the first item in their job description) and a true appreciation of decreasing latency globally and not just for `us-east-1`.

With so many timezones, a dependence on scheduled face time would kill our momentum, so meetings are used as a last resort. This isn’t to say that there are zero recurring meetings at Supabase, but in our ideal world there would be.

### Egoless

A willingness to do the [schlep](http://www.paulgraham.com/schlep.html) is essential to working at Supabase. Behind every shiny new feature we ship is thousands of hours of ruthless hard work and maintenance. The best people in the company have come to be known as “Supa-unblockers”; people who take work off your plate rather than piling it on. And being senior is no exception. We don’t have managers, which means all individuals need to:

- do IC (independent contributor) work as their primary job.
- self manage, and develop the skills necessary to ruthlessly prioritise tasks with the business goals always in mind.
- have the full picture - everyone in the team has access to our metrics and we share a long list of product principles which enables everyone to make high-level decisions autonomously.

### Kaizen mindset

[Kaizen](https://en.wikipedia.org/wiki/Kaizen) in a Supabase context is the idea that each process should be continuously and incrementally improved, with small and frequent changes being preferred over larger ones. This is true for all process, from company culture to product updates. It allows us to chunk down large projects (with the bonus that new starters can start contributing in their first week) and ensures smooth gradual changes to culture.

### Default to action

The very first day of Supabase (6th Jan 2020), I met Copple for an all-day white-boarding session where we explored in detail everything that we thought Supabase could be, and how we were going to achieve it. We dumped down ideas on fund raising, product, competition, marketing, everything. At the end of what felt like a marathon, around the point that most people would pop the kettle on and start rummaging in the cupboards for the last remaining packet of Custard Creams (just me?), Copple opened his laptop and immediately started work. It was a simple act, but in that moment he set the pace for development at Supabase. Never would there ever be a whiff of “Let’s meet next week to discuss”. The words we encourage people to default to instead is “why not now?”.

### Unreasonable levels of ambition

When you find all of the above traits in a single person, your definition of the word ‘feasible’ changes dramatically. At the start of every product cycle, we ask the question “what is the most ambitious thing we can hope to achieve in the next 3 months?”, and the team never fail to push the boundary on what we previously considered reasonable. Maybe it’s because we work in Dev Tools, but it’s amazing to watch developers productise solutions to their own problems. The right people don’t need to run a customer survey to know what to work on next, they’re obsessed with solving their own problems, have strong opinions on what great looks like, and won’t stop until they get there.

### A founder mindset

We have a bias towards people who previously ran their own companies. This typically hints towards high levels of personal ambition. Ex-founders tend to be very flexible in their role. They can identify low hanging fruit and change focus quickly to address the needs of the business as a whole. If the person is technical, it also reduces the translation needed across the tech and business sides of the company. Something we especially value are team members who take on responsibility without asking and without being asked. Founders do this regularly. Clearly these traits are not only found in people who’ve started companies, but it can be an indication. It’s not the credentials that we value most, but the mindset. We expect everyone who works at Supabase to act like a business owner, and to solidify this part of everyone’s compensation package is an ESOP apportionment with [a 10 year exercise window](https://github.com/holman/extended-exercise-windows).

---

By now, it should be no surprise that I estimate our product and growth momentum is actually 90% about the people, and only 10% about structure. But I’ll detail the structure anyway, since it’s still an important part of keeping people unblocked.

## Team structure

We often joke that building Supabase is like building 5 startups simultaneously, due to the number of modular products that make up the Supabase stack (Database, Auth, Realtime, etc.). Our culture has grown to support this idea - with an extremely high degree of autonomy bestowed to teams (and in fact, to everyone who works here).

### Independent teams

Previously there was a lot of crossover when it came to who worked on what, in the early days we had more products to manage than people. But these days the product team is broken roughly into:

- Postgres / PostgREST
- Platform
- Auth
- Storage
- Realtime
- Functions
- CLI / API
- QA (cross cutting)
- Docs (cross cutting)
- Design (cross cutting)
- Front End (cross cutting)

With some of these breaking down further into sub-teams (e.g. Realtime is actually more like an Elixir team that handles Observability, Logflare, and more).

Inter-team communication is one of the harder problems we’ve had to solve over the past year. As the teams grow and move faster internally, how do we make sure they can stay in sync with the rest of the org? For example, things like Front End, QA, and Docs need to have a level of consistency to ensure Supabase is a coherent product. One way we’ve solved this is by making sure the cross-cutting teams act like a consultancy rather than a service. When a product team like Auth require some Front End work, it’s very rarely a case of “throwing tasks over the fence” to the cross cutting teams, rather those teams will consult with the product team on how they can work together to get a project over the line. Another example is the docs team. They won’t write all the docs for a feature, that’s still the job of the team that implemented the feature (it’s important that we view docs as part of the product itself and not a byproduct). The team in question will consult with Team Docs on where the docs should live, and the format they should take to maintain coherence across all products in the stack.

Staying aligned on goals is also key. Each team is aware of the overall business metrics, but also the number of active, and paying projects that make use of their product’s APIs. It’s up to them to dig into the data and best decide how to boost their metrics, and to establish a hypothesis of how their numbers drive the business goals.

We receive a firehose of user feedback across multiple channels including Dashboard widget, support, socials, Discord, customer interviews, dogfooding. This feedback is triaged and piped directly to the relevant team for them to assess.

Ultimately, a team and the individuals within should have all the tools they need to be able to make decisions quickly and independently. We’re also not prescriptive on how teams organise their work, it’s up to them to decide.

### Cadence

Thanks to the Agile Manifesto, most software teams for the last decade ended up running some variation of a 2 week sprint on an endless loop, and yes its as exhausting as it sounds. Due to our desire to [recreate a Y-combinator-like environment internally](https://supabase.com/blog/supabase-how-we-launch) we landed on something that’s closer to 3 month product cycles. We found that it allows for more ambitious goal setting, accounts for the realities of people being sick or on holiday, and also allows teams to work through different phases.

An intense shipping schedule can lead to an accrual of technical debt. To solve for this during the last year we introduced a series of “Kaizen Weeks” that run within the first month of each 3 month cycle. The three weeks we ran as part of the last one were:

- QA week
- Docs week
- Performance week

The goal of each week wasn’t only to make step changes in each area, but to find ways to bake this work into the team’s ongoing workflow. Performance testing for example requires some upfront work to get things up and running, but once this is done it’s fairly straightforward to automate this as part of your CI/CD pipeline.

---

Monday 12th December is Supabase Launch Week 6 where you can find out what we’ve been grinding on for the last 3 months. All the details will be revealed here: [supabase.com/launch-week](https://supabase.com/launch-week)

If you’d like to work with us, [check out our careers page](https://supabase.com/careers).
