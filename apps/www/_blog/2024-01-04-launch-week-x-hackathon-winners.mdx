---
title: 'Launch Week X Hackathon Winners'
description: Announcing the winners of the Launch Week X Hackathon!
launchweek: x
categories:
  - developers
tags:
  - launch-week
  - hackathon
date: '2024-01-04'
toc_depth: 3
author: andrew_smith,tyler_shukert
image: lwx-hackathon-winners/lwx-hackathon-winners.png
thumb: lwx-hackathon-winners/lwx-hackathon-winners.png
---

We ended 2023 shipping tons of features in [Launch Week X](https://supabase.com/launch-week), but the fun hasn’t ended yet, because we get to announce the winners of our [LWX Hackathon](https://supabase.com/blog/supabase-hackathon-lwx)!

We enjoyed looking at all 64 projects, the most submissions we ever had. You can view all the submissions on [madewithsupabase.com](https://www.madewithsupabase.com/).

Now, without further ado, here are the winners of the LWX Hackathon!

## Best overall project

### Winner

[Supafork](https://github.com/chroxify/supafork) - by [@0xChroxify](https://twitter.com/0xChroxify)

Supafork is an open-source project that allows you to easily clone Supabase projects. Inspired by [Vercel's Deploy](https://vercel.com/docs/deployments/deploy-button) button it provides a similar experience for Supabase projects, allowing for easier setup of self-hosted apps using Supabase.

![supafork](/images/blog/lwx-hackathon-winners/supafork.jpg)

### Runner Up

[fastify-supabase](https://github.com/psteinroe/fastify-supabase) - by [@psteinroe](https://twitter.com/psteinroe)

A Fastify plugin to use authenticated Supabase clients in your API.

![fastify](/images/blog/lwx-hackathon-winners/fastify.jpg)

## Best use of AI

### Winner

[AI Video Search Engine (AVSE)](https://github.com/yoeven/ai-video-search-engine) - by [@yoeven](https://github.com/yoeven)

AVSE is a search engine for videos. It converts the video transcriptions into embeddings and a search is performed against them making it a unique search engine where you can perform search based on the video content.

![AI Video Search Engine](/images/blog/lwx-hackathon-winners/ai-video-search-engine.jpg)

### Runner Up

[Voice-Activated RAG System](https://github.com/ArtisanLabs/vocode-python/tree/465-supabase-rag-support/apps/voice-rag) - by [@arpagon](https://twitter.com/arpagon)

Voice-Activated RAG System is a voice conversational agent, taking on the role of an IT Architect, marking a significant advancement in AI-driven dialog systems. Programmed for voice interaction, it provides an intuitive and natural user experience, mirroring the consultation one would expect from a human IT professional.

![Voice-Activated RAG System](/images/blog/lwx-hackathon-winners/voice-activated-rag-system.jpg)

## Most fun / best easter egg

### Winner

[DocQuiz](https://github.com/elpnt/docquiz) - by [@elpntdev](https://twitter.com/elpntdev)

DocQuiz is a quiz generator based on web documents. It helps to test how much you know before reading a text or how much you understand after reading it.

![DocQuiz](/images/blog/lwx-hackathon-winners/doc-quiz.jpg)

### Runner Up

[Wordbuzz](https://github.com/arbizen/wordbuzz) - by [@arbizzen](https://twitter.com/arbizzen)

Wordbuzz is a two-player online game powered by supabase realtime that allows you to match words from over 100K most used words. Practicing vocabulary has never been this easy.

![Wordbuzz](/images/blog/lwx-hackathon-winners/wordbuzz.jpg)

## Most technically impressive

### Winner

[Supabase CLI for Visual Studio Code](https://github.com/anas-araid/vscode-supabase-cli) by [@anas_araid](https://twitter.com/anas_araid)

Supabase CLI for Visual Studio Code makes working with your local database instance much easier. You can create migrations, inspect tables, views, and functions, and run some common Supabase commands directly from your editor.

![supabase-cli](/images/blog/lwx-hackathon-winners/supabase-cli.jpg)

### Runner Up

[pg_render](https://github.com/mkaski/pg_render) - by [@kaskimies](https://twitter.com/kaskimies)

Rethink how you do web development with `pg_render`, a PostgreSQL extension crafted with Rust and pgrx. Enter the realm of Database-centric development, where your database schema is the sole source of truth—from data model to UI views.

![pg_render](/images/blog/lwx-hackathon-winners/pg-render.jpg)

## Most visually pleasing

### Winner

[Mirror of Loss](https://github.com/laznic/mirror-of-loss) - by [@laznic](https://twitter.com/laznic)

Mirror of Loss is a WebGL experience and a Baldur's Gate 3 / Forgotten Realms fan project. It's powered by AI: all the sprites, textures, and imagery were generated via Stable Diffusion (some with the help of OpenAI & GPT-4), and background music by Stable Audio.

![Mirror of Loss](/images/blog/lwx-hackathon-winners/mirror-of-loss.jpg)

### Runner Up

[1982 vs. the future](https://github.com/rblalock/1982vsthefuture) - by [@rblalock](https://twitter.com/rblalock)

The year is 1982. You're a computer programmer working for an agency that has access to all the world's secrets. Your terminal is the single most important terminal in the world. You must protect it from anyone.

![1982 vs. the future](/images/blog/lwx-hackathon-winners/1982-vs-the-future.jpg)

## The Prizes

The winners in each category will receive a Launch Week X Keyboard, while the runners-up will be awarded a limited edition swag kit.

![LWX Hackathon Prize](/images/blog/lwx-hackathon-winners/prize.png)

## Getting Started Guides

If you're inspired to build, check out some of the latest resources:

- **[Quick Start Guides](https://supabase.com/docs/guides/getting-started)**
- **[AI & Vectors Guide](https://supabase.com/docs/guides/ai)**
- **[Edge Functions Guide](https://supabase.com/docs/guides/functions)**
