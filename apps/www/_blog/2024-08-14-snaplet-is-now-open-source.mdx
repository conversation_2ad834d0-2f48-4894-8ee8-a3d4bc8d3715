---
title: 'Snap<PERSON> is now open source'
description: '<PERSON>naplet is closing their business and opening their source code'
author: paul_copplestone
image: lw12/day-3/og-snaplet.png
thumb: lw12/day-3/thumb-snaplet.png
launchweek: '12'
categories:
  - launch-week
tags:
  - engineering
date: '2024-08-14'
toc_depth: 3
---

Startups are hard. One of our favorite startups, <PERSON><PERSON><PERSON>, is [shutting down](https://www.snaplet.dev/post/snaplet-is-shutting-down). Despite that, they built an amazing team (some who now work at Supabase) and some incredible products.

One way to ensure that your products out-live your business is to open source what you've built. I'm a huge fan of what Snap<PERSON> built so I reached out to [<PERSON>](https://x.com/appfactory/) to see if <PERSON><PERSON><PERSON> were interested in open sourcing. He said yes:

<Quote img="peter-snaplet.webp" caption="<PERSON>, Founder of Snaplet">
  I built Snaplet because I believe developers write better software when they have access to
  production-like data. Although the company is closing, my belief remains strong, so we are
  open-sourcing the tools we've built.
</Quote>

## Open source products

There are 3 main tools that they are releasing under the MIT license:

### Copycat

Copycat generates fake data. It's like faker.js, but deterministic: for any given input it'll always produce the same output. For example, if you generate an email with the user ID `user_1234`, the next time you generate an email with that email it will be the same, _guaranteed:_

![Fake data](/images/blog/lw12/day-3/snaplet-fake-data.png)

- Repo: [github.com/snaplet/copycat](https://github.com/snaplet/copycat)

### Snaplet Seed

Seed generates realistic synthetic data based off a database schema. It automatically determines the values in your database so you don't have to define each value. For example, if you want to generate a 3 `comments` for one of your `posts` you simply point it at your schema and let it handle the rest:

![Seed data](/images/blog/lw12/day-3/snaplet-seed-data.png)

- Repo: [github.com/snaplet/seed](https://github.com/snaplet/seed)
- Docs: [docs.snaplet.dev/seed](https://snaplet-seed.netlify.app/seed)

### Snapshot

Snapshot is for capturing, transforming, and restoring snapshots of your database. It's like an advanced version of pg_dump/pg_restore. It has a particularly neat feature called “subsetting”. Point it at a database table and it tell it how much data you need. To maintain referential integrity, subsetting traverses tables, selecting all the rows that are connected to the target table through foreign key relationships:

![Subset data](/images/blog/lw12/day-3/snaplet-subset.png)

- Repo: [github.com/snaplet/snapshot](https://github.com/snaplet/snapshot)
- Docs: [docs.snaplet.dev/snapshot](https://snaplet-snapshot.netlify.app/snapshot)

## The future of Snaplet tech

The Snaplet team who joined Supabase have been helping Peter to migrate these projects to open source. Over the next few weeks we'll move these into the Supabase GitHub org and pick up the ongoing maintenance.

We prefer to keep products decoupled (it's one of our [Principles](/docs/guides/getting-started/architecture#everything-works-in-isolation)), so you'll always be able to use these tools independently from Supabase. We can also see a lot of value providing a deep integration, so we've already started adding their [Snaplet Seed](/docs/guides/local-development/seeding-your-database#generating-seed-data) to our official docs. This is just a start, watch this space!

## Where's Peter now?

Peter is back at [RedwoodJS](https://redwoodjs.com/), which he co-founded before Snaplet. He's been working on React Server Components, coming soon to Redwood.
