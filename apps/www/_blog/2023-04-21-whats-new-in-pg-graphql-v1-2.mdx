---
title: "What's New in pg_graphql v1.2"
description: 'New Features in the v1.2 release of pg_graphql'
launchweek: '7'
categories:
  - postgres
tags:
  - postgres
  - graphql
  - planetpg
date: '2023-04-21'
toc_depth: 3
author: oli_rice
image: launch-week-6/pggraphql/og-pg-graphql.png
thumb: launch-week-6/pggraphql/og-pg-graphql.png
---

It's been 4 months since the [1.0.0 release of pg_graphql](https://supabase.com/blog/pg-graphql-v1).
Since then, we’ve pushed several features to improve the APIs that `pg_graphql` produces.

In this article, we’ll walk through those features and show examples of each.

<div className="bg-gray-300 rounded-lg px-6 py-2 italic">

📢 These features are only available on projects with Postgres version `*********` or higher. For help with upgrading, please review the [migrating and upgrading projects guide](https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects).

</div>

### View Support

Prior to `v1.1`, `pg_graphql` would only reflect standard tables. Since then, views, materialized views, and foreign tables are now also reflected in the GraphQL schema.

For example:

```sql
create view "ProjectOwner" as
  select
    acc.id,
    acc.name
  from
    account as acc
    join role as r on r.id = acc.role_id
  where acc.role = 'project_owner';
```

Since all entities exposed by `pg_graphql` require primary keys, we must define that constraint for the view. We do that using a comment directive:

```sql
comment on view "ProjectOwner"
  is '@graphql({"primary_key_columns": ["id"]})';
```

Which yields the GraphQL type:

```graphql
type ProjectOwner implements Node {
  nodeId: ID!
  id: UUID!
  name: String
}
```

With associated `Edge` and `Connection` types. That enables querying via:

```graphql
{
  projectOwnerCollection(first: 2) {
    edges {
      node {
        nodeId
        name
      }
    }
  }
}
```

Additionally, simple views automatically support mutation events like inserts and updates. You might use these to migrate underlying tables while maintaining backwards compatibility with previous API versions.

## Filtering

Filtering in SQL is endlessly flexible. We’ve taken two incremental steps to bring more of that flexibility to the GraphQL interface.

### `is null` and `is not null`

Handling `null` values can be tricky in both SQL and GraphQL. However, there are similarities we can take advantage of.
In `pg_graphql`, every scalar data type has its own filter type, such as `IntFilter` and `StringFilter`.
Each of these filter types now includes an `is` argument, which allows you to filter based on whether a value is null or not null.
You can do this by using `{is: NULL}` for `null` values and `{is: NOT_NULL}` for non-`null` values.

```graphql
enum FilterIs {
    NULL
    NOT_NULL
}

type IntFilter {
    ...
    is: FilterIs
}
```

For example:

```graphql
{
  blogCollection(filter: { name: {is: NULL}}) {
    ...
  }
}
```

to return all `blog`s where the `name` is `null`.

### **`like`**, **`ilike`**, and **`startsWith`**

Text filtering options in `pg_graphql` have historically been restricted to equality checks. The hesitation was due to concerns about exposing a default filter that is difficult to index. The combination of [citext](https://www.postgresql.org/docs/current/citext.html) and [PGroonga available on the platform](https://supabase.com/docs/guides/database/extensions/pgroonga) solves those scalability risks and enabled us to expand the `StringFilter` with options for `like` `ilike` and `startsWith`.

```graphql
input StringFilter {
  eq: String
  ...
  startsWith: String
  like: String
  ilike: String
}
```

Note that `startsWith` filters should be preferred where appropriate because they can leverage simple B-Tree indexes to improve performance.

```graphql
{
  generalLedgerCollection(filter: { identifierCode: { startsWith: "BX1:" } }) {
    edges {
      node {
        nodeId
        identifierCode
        amount
      }
    }
  }
}
```

### GraphQL directives `@skip` and `@include`

The GraphQL spec has evolved over time. Although the spec is clear, it is common for GraphQL servers to selectively omit some chunks of functionality. For example, some frameworks intentionally do not expose an introspection schema as a form of security through obscurity.

`pg_graphql` aims to be unopinionated and adhere exactly to the spec. The `@skip` and `@include` directives are part of the [GraphQL core specification](https://spec.graphql.org/October2021/#sec--skip) and are now functional.

The **`@skip`** directive in GraphQL is used to conditionally skip a field or fragment during query execution based on a Boolean variable. It can be used to make the query more efficient by reducing the amount of data retrieved from the server.

The **`@include`** directive is the mirror of **`@skip`** where a field or fragment is conditionally included depending on the value of a Boolean variable.

Here's an example of how the **`@skip`** directive can be used in a GraphQL query:

```graphql
query getBooks($includeDetails: Boolean!) {
  booksCollection {
    edges {
      node {
        id
        title
        description @skip(if: $includeDetails)
      }
    }
  }
}
```

### User Defined Descriptions

Users can now use the [comment directive system to assign descriptions](https://supabase.github.io/pg_graphql/configuration/#description) to tables, views and columns.

```sql
create table public.book(
    id int primary key,
    title text not null
);

comment on table public.book
is e'@graphql({"description": "a library book"})';

comment on column public.book.title
is e'@graphql({"description": "the title of the book"})';
```

GraphQL IDEs, such as GraphiQL render those descriptions, allowing developers to provide clearer API documentation.

<Img
  src="/images/blog/2023-04-19-pg_graphql-v1_2/description_directive.png"
  wide={false}
  alt="description comment directive"
/>

## Roadmap

The headline features we aim to launch in coming releases of `pg_graphql` include:

1. Support for user-defined functions: [GitHub issue](https://github.com/supabase/pg_graphql/issues/222)
2. Support for nested inserts: [GitHub issue](https://github.com/supabase/pg_graphql/issues/294)
3. An alternative approach to computed relationships based on SQL functions returning **`SET OF`** rather than comment directives (compatible with PostgREST)

## More pg_graphql

- [Introducing pg_graphql: A GraphQL extension for PostgreSQL](https://supabase.com/blog/pg-graphql)
- [GraphQL is now available in Supabase](https://supabase.com/blog/graphql-now-available)
- [pg_graphql v1.0](https://supabase.com/blog/pg-graphql-v1)
- [pg_graphql: Postgres functions now supported](https://supabase.com/blog/pg-graphql-postgres-functions)
