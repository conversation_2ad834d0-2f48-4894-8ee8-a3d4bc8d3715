---
title: Supabase Launches NFT Marketplace
description: A fully encrypted NFT platform to protect and transact your digital assets
author: ant_wilson
author_title: Supabase
author_url: https://github.com/awalias
author_image_url: https://github.com/awalias.png
image: nft/nft-3.png
thumb: nft/nft-3.png
categories:
  - company
tags:
  - supabase
  - nfts
date: '04-01-2021'
---

### Crypto at Supabase

Most people don’t know this but the Supabase team has a long history of involvement in the NFT game. In 2018, <a href="https://www.blockpunk.net/en/collection/paul-copplestone" target="_blank">Copple</a> and <a href="https://www.blockpunk.net/en/collection/rory-wilding" target="_blank">Rory</a> were minted as NFTs on <a href="https://www.blockpunk.net" target="_blank">Blockpunk.net</a>, an Ethereum based Anime-NFT platform that four Supabase core team members helped build. We also helped in the building of the world’s first tokenised anime premiere - <a href="https://www.animationmagazine.net/anime/first-tokenized-anime-film-vevara-in-your-dream-debuts/" target="_blank">Vevara in Your Dream</a>.

The team were also founding members of the <a href="https://github.com/awalias/guacchain" target="_blank">Guacchain Foundation</a>, an economic experiment in a deflationary-based monetary system, inspired by the fact that Avacados ripen faster when stored together.

NFTs are now capturing the imagination of speculators everywhere, but NFTs as they exist today have a major flaw. We call it the _Copy-Paste Problem_. The Copy-Paste problem is when a token contains a URL to an image hosted on the web, or even on IPFS; there is nothing stopping someone who does not own the token, from heading to that URL, and just downloading the image for themselves.

As our latest foray into NFTs we’re announcing <a href="https://buymeth.com/" target="_blank">BuyMeth.com</a>, (Meth = [M]isleading [E]ncrypted [Th]umbnails) an NFT marketplace that solves the copy-paste problem of NFTs today.

![Supabase NFT marketplace BuyMeth.com](/images/blog/nft/nft-1.png)

### How does it work?

Each NFT stores a blurhash representation of a full image, but also a link to the full encrypted version of the image stored on IPFS. The full image is always encrypted with the key of the _current_ owner.

When a sale is initiated i.e. the seller accepts a buyers offer, Metamask re-encrypts the image with the public key of the buyer, and uploads it to IPFS, the url in the NFT is updated and the buyer assigned as the new owner. After the sale, a 1 week challenge period is initiated where the proceeds of the sale are kept in an escrow contract. The buyer can then decrypt the contents of the image, and can run an automated verification function to verify that the image received (when hashed) does indeed produce the publically available blurhash (or thumbnail). If it does not, then the buyer can submit a claim to the sales contract, along with a proof of the invalid file, and claim back the proceeds.

![Most popular NFTs](/images/blog/nft/nft-2.png)

As a way to dis-incentivise previous owners of the image to not reveal the original image, we bake in a royalty mechanism, whereby previous owners receive a fraction of the sale price each time, the artwork changes hands.

Despite the undeniable value of such a platform, this announcement is of course an April Fools joke. We are not launching a Supabase NFT platform; but we are [Launching a TON of other stuff this week](/blog/launch-week).
