---
title: 'Flutter Hackathon Winners'
description: Announcing the winners of the Flutter Hackathon!
author: tyler_shukert
image: flutter-hackathon-winners/flutter-hackathon-winners.png
thumb: flutter-hackathon-winners/flutter-hackathon-winners.png
categories:
  - developers
tags:
  - hackathon
  - community
date: '2023-05-29'
toc_depth: 2
---

Last week we wrapped up the Flutter Hackathon, and we were blown away by the results. Today, I have the honor of announcing the winners, along with our good friends [<PERSON><PERSON>](https://twitter.com/mhadaily) from [Invertase](https://invertase.io/) and [Pooja](https://twitter.com/pooja_bhaumik) from [FlutterFlow](https://flutterflow.io/).

Now, without further ado, let's look at some of our favorites in more detail:

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/AazB9mQetkw"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  ></iframe>
</div>

## Best Overall Project

### Winner

[Syno](https://github.com/ineffablesam/Syno) - by [@samuelP09301972](https://twitter.com/samuelP09301972)

Syno changes the way you consume video content. Say goodbye to lengthy videos and say hello to concise, accurate summaries that capture the essence of each video. Experience the future of video summarization with Syno.

![Syno](/images/blog/flutter-hackathon-winners/syno.jpg)

### Runner Up

[Study Genius](https://github.com/Salomondiei08/StudyAI-App.git) - by [@salomon_diei](https://twitter.com/salomon_diei)

Study Genius is a mobile application that build a personalized learning experience for students. It provides a comprehensive platform for effective study, collaboration and personalized support for students.

![Study Genius](/images/blog/flutter-hackathon-winners/study-genius.jpg)

## Best FlutterFlow project

### Winner

[Lord Of The Memes](https://lord-of-the-memes.flutterflow.app/) - by [@mohonishc](https://twitter.com/mohonishc)

Lord of the Memes is a fun interactive game to be played with friends where you have to react to various situations with meme gifs and compete to be crowned as the ultimate Meme Lord.

![Lord Of The Memes](/images/blog/flutter-hackathon-winners/lord-of-the-memes.jpg)

### Runner Up

[Colabity](https://app.flutterflow.io/project/cocolab-2n64lp) - by [@hichamics](https://twitter.com/hichamics)

Colabity is an app that matches creative minds with each other to work on similar interest-based projects.

![Colabity](/images/blog/flutter-hackathon-winners/colabity.jpg)

## Best Dart Edge Project

### Winner

[ChatGlobe](https://chatglobe.vercel.app/) - by [@tmhk_tnht](https://twitter.com/tmhk_tnht)

ChatGlobe an application that allows you to chat with people around the world in your own language. It translates the messages into multiple languages using Dart Edge and OpenAI API as they are sent.

![ChatGlobe](/images/blog/flutter-hackathon-winners/chatglobe.jpg)

### Runner Up

[Adventure Kingdom](https://github.com/Anujeshdahiya/adventurekingdom/tree/flutterflow) - by [@anujeshd](https://twitter.com/anujeshd), [@rishikhatri47](https://twitter.com/rishikhatri47), and [@Harshit_1320](https://twitter.com/Harshit_1320)

Adventure Kingdom is an app for theme park in which you can check different park attraction informations.

![Adventure Kingdom](/images/blog/flutter-hackathon-winners/adventure-kingdom.jpg)

## Most technically impressive

### Winner

[Accident Detection](https://github.com/Guneetsinghtuli/supabase) - by [@Guneetsingh02](https://twitter.com/Guneetsingh02)

Accident Detection is an app for detecting road accidents using machine learning techniques and utilizing inputs from a variety of sensors, such as accelerometers and GPS to detect and analyze data related to accidents, such as changes in speed, direction, and impact force.

![Accident Detection](/images/blog/flutter-hackathon-winners/accident-detection.jpg)

### Runner Up

[IntelliTask](https://noga-dev.github.io/intellitask/) - by [@noga_dev](https://twitter.com/noga_dev)

IntelliTask is a simple semi-automated todo app where the AI decides how important the task is and when it should be due.

![IntelliTask](/images/blog/flutter-hackathon-winners/intellitask.jpg)

## Most visually pleasing

### Winner

[Caffeio](https://github.com/curregoz/caffeio-app) - by [@caurregoz](https://twitter.com/caurregoz)

Caffeio an app for coffee aficionados. Explore a variety of coffee brewing methods like V60, AeroPress, and Chemex, with comprehensive guides and assistance to elevate your coffee experience. Uncover the captivating stories behind each method, deepening your appreciation for your favorites.

![Caffeio](/images/blog/flutter-hackathon-winners/caffeio.jpg)

### Runner Up

[Decision Tales](https://zi30067ti310.zapp.page/) - by [@JudoUergens](https://twitter.com/JudoUergens)

Decision Tales is an app that let's you follow your own path through fascinating stories. Each story has many possible outcomes, which you untangle with your decisions.

![Decision Tales](/images/blog/flutter-hackathon-winners/decision-tales.jpg)

## The Prizes

[@samuelP09301972](https://twitter.com/samuelP09301972), the creator of Syno will receive a limited Flutter-themed Supabase keyboard.

![Flutter Keyboard](/images/blog/flutter-hackathon-winners/keyboard.png)

Each member of the winning and runner-up projects will receive a limited edition Flutter Hackathon swag pack including a t-shirt as proof of winning in the Flutter Hackathon.

![Flutter Hackathon t-shirt](/images/blog/flutter-hackathon-winners/flutter-hackathon-t-shirt.png)

### Get Started Guides

If you missed the hackathon, but still want to build awesome apps using Flutter and Supabase, here are some resources to get you started:

- [Flutter Quick Start Guide](https://supabase.com/docs/guides/with-flutter)
- [Supabase Flutter documentation](https://supabase.com/docs/reference/dart/introduction)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
- [Setup Supabase on FlutterFlow guide](https://docs.flutterflow.io/data-and-backend/supabase/supabase-setup)
- [How to Set Up Supabase Authentication in FlutterFlow video](https://www.youtube.com/watch?v=tL-sLPfWzVE)
