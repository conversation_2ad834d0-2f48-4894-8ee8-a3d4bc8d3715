---
title: 'Supabase Beta November 2021: Launch Week Recap'
description: We wrapped up November with Supabase's third Launch Week. Here's all the awesome stuff that got shipped ...
author: ant_wilson
author_url: https://github.com/awalias
author_image_url: https://github.com/awalias.png
image: 2021-nov/release-nov-2021.jpg
thumb: 2021-nov/release-nov-2021-cover.jpg
categories:
  - product
tags:
  - release-notes
date: '2021-12-15'
toc_depth: 3
---

We wrapped up November with Supabase's third Launch Week. It also happens to be 12 months since we moved from alpha to beta.

Here's all the awesome stuff that got shipped during Launch Week...

## Supabase Studio

We [open sourced the Dashboard](/blog/supabase-studio) - it's now included in Self-Hosting, and the community are now able to contribute,
which is going to help us move even faster! Expect many more updates to the dashboard over the coming weeks and months! [Read the code for yourself.](https://github.com/supabase/supabase/tree/master/studio)

![Supabase Studio](/images/blog/2021-nov/studio.jpg)

## We acquired Logflare

We were early Logflare users, and found it to be one of the most impressive products we'd ever used. So impressive in fact, that we wanted to offer it's capabilities to our entire user base.
You'll now find fully searchable Supabase API and database logs within the dashboard, and we're also in the process of open sourcing the codebase. [Read more about the acquisition](/blog/supabase-acquires-logflare).

![We acquired Logflare](/images/blog/2021-nov/logflare.png)

## Realtime Row Level Security

[Security rules now work for Realtime](/blog/realtime-row-level-security-in-postgresql).
All of your row level security policies now work with the realtime API, so you can limit streams and subscriptions on a per user basis (or any configuration you want!).

![Realtime Row Level Security](/images/blog/2021-nov/realtime.png)

## GraphQL is coming to Supabase

We heard you! And built a Postgres extension for [querying your database with GraphQL](/blog/pg-graphql).
This means that each GraphQL request is resolved by a single SQL statement and hits the theoretical minimum achievable network IO overhead of any GraphQL to SQL resolver.

![GraphQL is coming to Supabase](/images/blog/2021-nov/graphql.png)

## Supabase now runs on PostgreSQL 14

One of the most exciting things to happen in databases this year was the release of PG 14.
Which is now the default database used on our hosted platform. Read more here about [why we're so excited about it](/blog/whats-new-in-postgres-14).

![Supabase now runs on PostgreSQL 14](/images/blog/2021-nov/pg14.png)

## PostgREST 9.0

PostgREST is the tool we use to introspect your schema and make your database available over a RESTful API.
We also sponsor and help maintain this amazing open source project. Version 9.0 was just released and has also been rolled out to all new and existing Supabase projects on our hosted platform.
[Check out the blog post](/blog/postgrest-9) by Steve Chavez, the maintainer of PostgREST.

![PostgREST 9.0 ](/images/blog/2021-nov/postgrest9.png)

## Building a SaaS?

We have [a course for that](https://egghead.io/courses/build-a-saas-product-with-next-js-supabase-and-stripe-61f2bc20).
You'll learn how to combine Next.js, Stripe, and Supabase to launch your business in no time at all.
It's honestly mind blowing how quickly you can get products out the door with this stack. Oh and it's free.

![Building a SaaS? ](/images/blog/2021-nov/course.jpg)

## New Storage CDN

Serving your files in no time - Get eye wateringly fast load times for your media files. [Read all about the benefits](/blog/launch-week-three-friday-five-more-things#supabase-cdn).

![New Storage CDN](/images/blog/2021-nov/too_damn_high.png)

## How we Launch

We "open sourced" our launch methodology - Learn about how we plan, execute, and retrospect launch weeks internally. [Read the post](/blog/supabase-how-we-launch).
supabase monthly growth

![How we Launch](/images/blog/2021-nov/supabase-universe.jpg)

## Community

### Highlights

- Community Day [blog](/blog/community-day#new-tutorials-and-integration-guides)
- The SupaSquad has been busy [blog](/blog/community-day#supasquad-updates)
- 10 startups on what they're building with Supabase [video](https://www.youtube.com/watch?v=QAm1x7KaLq4&list=PL5S4mPUpp4OuzQN-a_FY3OZQuYo4NmXvb&t=3s)
- New official integration guides [blog](https://supabase.com/blog/community-day#new-tutorials-and-integration-guides)
- Add Supabase in Teta [video](https://www.youtube.com/watch?v=lt-Vebxk-Mk)
- Auth flow with Remix and Supabase [video](https://www.youtube.com/watch?v=-KiH8feOHSc)
- Discovering Supabase [video](https://www.youtube.com/watch?v=WToGeMIdoSY)
- Next.js with Magic Links [blog](https://dev.to/dailydevtips1/authenticating-nextjs-with-supabase-auth-magic-links-363e)
- Next.js + Auth0 + Supabase [blog](https://auth0.com/blog/using-nextjs-and-auth0-with-supabase/)

### TikTok

Supabase is [now on TikTok](https://www.tiktok.com/@supabase.com?) - Watch us ship to some questionable beats.

![GitHub](/images/blog/2021-nov/tiktok.png)

### GitHub

We hit 25K stars!! [github.com/supabase/supabase](http://github.com/supabase/supabase)

![GitHub](/images/blog/2021-nov/stars.png)

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
- Become a **[sponsor](https://github.com/sponsors/supabase)**
