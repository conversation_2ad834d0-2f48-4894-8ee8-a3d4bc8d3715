---
title: Xendit Built a Counter-Fraud Watchlist for the Fintech Industry
description: See how Xendit use Supabase to build a full-text search engine.
author: rory_wilding
author_title: Supabase
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: supabase-xendit.png
thumb: supabase-xendit-thumb.jpg
categories:
  - company
tags:
  - fintech
date: '12-02-2020'
---

[Xendit](https://www.xendit.co/) is one of South East Asia's largest payment processors. They use Supabase to run automated checks against international sanctions lists.

### About Xendit

Xendit is a leading payment gateway for Indonesia and Southeast Asia. They enable businesses to accept payments in Indonesia with a single integration for credit and debit cards, e-wallets, and bank transfer.

### Counter-fraud

As a payment processor, Xendit are responsible for verifying that all transactions are legal. Any transactions which are suspicions must be verified against a strict set of criteria, and the parties involved need to be checked against international sanctions lists. This is a critical anti-money-laundering operation and needs to be performed in realtime to prevent any delays on legitimate payments.

### Why they chose Supabase

Xendit needed something fast. Something that was cheaper than using the global players like Worldcheck or Refinitiv. Xendit already uses Postgres for a lot of their critical infrastructure, and so Xendit team are familiar with the technology and comfortable in it's ability to scale.

### What they built

Xendit parses international sanctions lists from the UN and the Indonesian government and loads them into Supabase. Since Supabase provides a full Postgres server, they can then use the [Trigram](https://www.postgresql.org/docs/current/pgtrgm.html) extension to perform full-text search on the lists, with a relevance score on every search.

Supabase was perfect for their use case, as they needed something built fast. The full solution was built and in production in less than one week.

> The full solution was built and in production in less than one week.

Xendit created a database function for searching, which they are able to call directly using their Python clients. They have plans to iterate on the current implementation using more advanced techniques, like machine learning, but for now the Supabase system has been in Production for 9 months without a problem.
