---
title: 'Top 10 Launches of Launch Week 12'
description: Highlights from Launch Week 12
author: paul_copplestone
image: lw12/day-5/OG_top-10-lw.png
thumb: lw12/day-5/thumb_top-10-lw.png
launchweek: '12'
categories:
  - launch-week
tags:
  - launch-week
date: '2024-08-16'
toc_depth: 3
---

There's always a lot to cover in Launch Weeks. Here are the top 10, ranked by my own statistical reasoning.

### #10 Snaplet is now open source

Snaplet is now closed, but their source code is open. They are releasing 3 tools under the MIT license for copying data, seeding databases, and taking database snapshots.

[Read more.](/blog/snaplet-is-now-open-source)

### #9 pg_replicate

Use pg_replicate to copy data (full table copies and CDC) from Postgres to any other data system. Today it supports BigQuery, DuckDb, and MotherDuck, with more sinks will be added in future.

[See it on HackerNews.](https://news.ycombinator.com/item?id=41209994)

### #8 vec2pg

A new CLI utility for migrating data from vector databases to Supabase, or any Postgres instance with [pgvector](https://github.com/pgvector/pgvector). Today it works with Pinecone and Qdrant - more will be added in future.

[Read more.](/blog/vec2pg)

### #7 Official Supabase extension for VS Code and GitHub Copilot

We launched a new GitHub Copilot extension for VS Code to make your development with Supabase and VS Code even more delightful.

[Read more.](/blog/github-copilot-extension-for-vs-code)

### #6 Python official support

Python libs are now officially supported in Supabase. We've seen a huge rise in Python developers (and contributors) driven mostly by AI and ML, and this will make it easier for them to use Supabase.

[Read more.](/blog/python-support)

### #5 We released Log Drains

We released Log Drains so that developers can export logs generated by their Supabase products to external destinations, such as Datadog or custom HTTP endpoints.

[Read more.](/blog/log-drains)

### #4 Realtime: Broadcast and Presence Authorization

We added authorization for Realtime's Broadcast and Presence. You write RLS Policies to allow or deny clients' access to your Broadcast and Presence Channels.

[Read more.](/blog/supabase-realtime-broadcast-and-presence-authorization)

### #3 Auth: Bring-your-own Auth0, Cognito, or Firebase

This was actually a few different announcements: support for third-party Auth providers; Phone-based Multi-factor Authentication (SMS and Whatsapp); and new Auth Hooks for SMS and email.

[Read more.](/blog/third-party-auth-mfa-phone-send-hooks)

### #2 Build Postgres Wrappers with Wasm

Today we're releasing support for [Wasm (WebAssembly)](https://webassembly.org/) Foreign Data Wrapper. With this feature, anyone can create a FDW and share it with the Supabase community. You can build Postgres interfaces to anything on the internet.

[Read more.](/blog/postgres-foreign-data-wrappers-with-wasm)

### #1: postgres.new: In-browser Postgres with an AI interface

[database.build](https://database.build) (formerly postgres.new) is an in-browser Postgres sandbox with AI assistance. With database.build, you can instantly spin up an unlimited number of Postgres databases that run directly in your browser (and soon, deploy them to S3).

## One more thing: a Supabase Book

There's now an entire book written about Supabase. [David Lorenz](https://x.com/activenode) spent a year working on it and we think it's one of the most thorough Supabase resources on the internet. If you're interested in leveling up your Supabase skills, you can support David and [purchase the book here](https://supa.guide).

[Read more.](/blog/supabase-book-by-david-lorenz)
