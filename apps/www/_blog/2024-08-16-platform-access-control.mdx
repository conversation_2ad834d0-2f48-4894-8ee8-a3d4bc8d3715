---
title: 'Introducing New Platform Access Control'
description: Granular permissions for adding users to specific projects in an Supabase organization.
author: hieu,joshen<PERSON>
image: lw12/day-5/OG_platform-access-control.png
thumb: lw12/day-5/thumb_platform-access-control.png
launchweek: '12'
categories:
  - launch-week
  - developers
  - platform
tags:
  - launch-week
  - tech
date: '2024-08-16'
toc_depth: 3
---

At Supabase, we're constantly striving to provide the tools developers need to build secure, reliable applications. Our latest update focuses on an area that's critical to both security and reliability: Platform Access Control.

We're excited to announce the rollout of our new granular access control features which allows giving users access to specific projects instead of the entire organization.

![Configuring project roles for a user](/images/blog/lw12/day-5/access-control.png)

### Why Platform Access Control matters

Managing who can access what within your project isn't just a convenience — it's essential for maintaining security and ensuring that your software development lifecycle (SDLC) is followed and availability guarantees are met. While Supabase already provides a robust data security framework through Row-Level Security (RLS), we recognized a gap when it came to managing platform-level access. Our new Platform Access Control feature fills that gap by offering Role-Based Access Control (RBAC) to the Supabase platform and management APIs.

### Granular control at your fingertips

With Platform Access Control, Supabase now offers a way to manage permissions at the both the organization and project levels.

A user can either have permissions assigned for the whole organization or for specific projects. The roles remain the same as before:

- **Owner:** Full control over everything
- **Administrator:** Similar to the Owner role with some restrictions on update organization settings, transferring projects and modifying owners.
- **Developer:** Cannot modify any settings but has full access to modifying the content like updating the database, uploading storage objects, etc.
- **Read-Only:** Ideal for stakeholders or team members who need visibility into the project without the ability to make changes.

For a more exhaustive list of actions allowed for each role, check out the [access control docs](/docs/guides/platform/access-control#organization-scoped-roles-vs-project-scoped-roles).

### Unlocking the full potential of your team

With these new features, Supabase is making it easier than ever to ensure that every team member has the right level of access. By assigning specific roles, you can reduce the risk of accidental changes, streamline workflows, and maintain a high level of security across your projects. If you're part of a growing team, consider upgrading to an Enterprise Plan to take full advantage of these powerful new tools.

### Get started

To start using the new Platform Access Control features, check out our updated documentation [here](/docs/guides/platform/access-control).
