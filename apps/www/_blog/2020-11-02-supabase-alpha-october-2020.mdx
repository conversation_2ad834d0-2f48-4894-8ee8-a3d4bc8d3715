---
title: Supabase Alpha October 2020
description: Eight months of building.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
image: supabase-october-2020.png
categories:
  - product
tags:
  - supabase
date: '11-02-2020'
video: https://www.youtube.com/v/1gNDMMsUPI0
---

We're now 8 months into building Supabase. We're focused on performance, stability, and reliability but that hasn't prevented us from shipping some great features.

### Quick demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/1gNDMMsUPI0"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Supabase.js 1.0

In the lead-up to our Beta launch, we've [released](/blog/improved-dx) `supabase-js` version 1.0 and it comes with some major Developer Experience improvements. We received a lot of feedback from the community and we've incorporated it into our client libraries for our 1.0 release.

Check out the [blog post](/blog/improved-dx) to learn more.

### More powerful SQL Editor

Although it was only intended to be a temporary feature, the SQL Editor has become one of the most useful features of Supabase. This month we decided to make give it some attention, adding Tabs and making it full-screen. This is the first of many updates, we've got some exciting things planned for the SQL Editor.

![This image shows a SQL Editor with tabs. Originally our SQL editor was very basic, but we're moving towards something very powerful.](/images/blog/sql-editor.png)

### Key commands for Power Users

For the heavy table editor users, we've gone ahead and added a bunch of key commands and keyboard shortcuts so you can zip around and manipulate your tables faster than ever.

![This image shows some of the keyboard shortcuts we introduced on the table editor.](/images/blog/keyboard-shortcuts.png)

### Magic Links

One of the most requested Auth features was the ability to send magic links that your users can use to log in. You can use this with new or existing users, and alongside passwords or stand alone.

![This image shows a template where developers can edit the magic links email which is sent to their users on sign up.](/images/blog/magic-links.png)

### Kaizen

- We have new and improved [docs](/docs/reference/javascript/installing).
- We converted [realtime-js](https://github.com/supabase/realtime-js/) to TypeScript.
- Dashboard Performance: we heavily optimised our dashboard routes.
- With the help of the community, we [closed a lot of issues](https://github.com/orgs/supabase/projects/5) during Hacktoberfest.
- We have started [benchmarking](https://github.com/supabase/benchmarks) all the open source tools we use. We'll publish the results this month.

### Community

We had a crazy month during Hacktoberfest. In case you missed it, here are some of the highlights:

- [@kiwicopple](https://twitter.com/kiwicopple) chatted to Jeff on the [Software Engineering Daily podcast](https://softwareengineeringdaily.com/2020/10/15/supabase-open-source-firebase-with-paul-copplestone/).
- [Tezos](https://twitter.com/TezosBakingBad/status/1318212875035512835) started using our PostgREST libraries for their blockchain API: [https://pro.tzkt.io/](https://pro.tzkt.io/)
- Supabase hosted a [Hacktoberfest meetup](https://www.youtube.com/watch?v=3_xRLTjvEiE).
- [@kiwicopple](https://twitter.com/kiwicopple) appeared on the [Open Sauced channel](https://www.youtube.com/watch?v=PHmiWXDx9-w) with [@bdougieYO](https://twitter.com/bdougieYO)
- [@thorwebdev](https://twitter.com/thorwebdev) shows the [Engineers.sg](http://engineers.sg) group how to [build a realtime Slack clone](https://engineers.sg/video/building-a-slack-clone-with-authentication-and-realtime-data-syncing-using-supabase-io-singaporejs--4119) with Supabase

If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).

### Coming next

Our focus is still moving from Alpha to Beta and we'll be improving stability, reliability, and performance. Our Supabase.js 1.0 launch was part of that, and we've created a [Benchmarks](https://github.com/supabase/benchmarks/) repository where we'll be measuring the performance of all the open source tools we use.

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
