---
title: 'Hack the Base! with Supabase'
description: 'Play cool games, win cool prizes.'
author: stephen_morgan
image: launch-week-13/hack-the-base-dec-2024/og.png
thumb: launch-week-13/hack-the-base-dec-2024/thumb.png
launchweek: '13'
categories:
  - launch-week
tags:
  - launch-week
  - security
date: '2024-12-06'
---

We are announcing our first-ever Capture the Flag challenge, [**Hack the Base**](https://ctf.supabase.com). Whether you're a seasoned hacker or just starting out, this challenge is for you.

## What is a Capture the Flag?

A Capture the Flag (CTF) is a cybersecurity competition where participants solve a series of challenges to earn points. In this case, you'll be tasked with finding flags in our education partners news site, picking apart how it works and the secrets it may be hiding.

## What's in it for you?

- **Prizes:** We're offering [prizes](https://supabase.store) for the top performers.
- **Recognition:** Showcase your skills to the community and see yourself on the leaderboard.
- **Learning Opportunity:** Learn about common security vulnerabilities and how to find them.

## Challenges

We've designed challenges for both beginner and advanced hackers. Get on the board with the easy challenges, and then move on to the more difficult ones. There will be no hints to start with, but we'll be releasing hints via social media throughout the week to help you along the way.

## Competition Rules and Flag Submission

For detailed rules and to submit your flags, visit our [dedicated CTF website](https://ctf.supabase.com). You'll also be able to track your progress on the leaderboard and see how you stack up against other participants.
The challenge will kick off on December 8th 1pm PT and run until December 14th 1pm PT, be there or be square!

## Join our Bug Bounty Program

If you're interested in finding vulnerabilities in Supabase year-round, check out our [Bug Bounty Program](https://supabase.com/.well-known/security.txt). We're always looking for talented security researchers to help us improve the security of our platform.

## Get Hacking!

We can't wait to see what you can do! Good luck, and happy hacking!

---
