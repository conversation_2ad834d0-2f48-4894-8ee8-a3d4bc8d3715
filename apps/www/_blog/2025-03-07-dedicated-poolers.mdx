---
title: 'Dedicated Poolers'
description: "A dedicated pgbouncer instance that's co-located with your database for maximum performance and reliability."
author: paul_copplestone
image: dedicated-poolers/dedicated-poolers-og.png
thumb: dedicated-poolers/dedicated-poolers-og.png
categories:
  - product
  - postgres
tags:
  - postgres
date: '2025-03-07'
toc_depth: 3
---

Today we're announcing **Dedicated Poolers** - a Postgres connection pooler that's co-located with your database for maximum performance and reliability.

<Admonition type="info">

Don't know what a Pooler is? Check out our [docs](/docs/guides/database/connecting-to-postgres#serverside-poolers).

</Admonition>

This is available today for select customers, and will be generally available by 20th March, 2025. If you want to be notified when it's ready, [sign up here](https://forms.supabase.com/dedicated-pooler).

### Supabase Dedicated Pooler

The Dedicated Pooler is a [PgBouncer](https://www.pgbouncer.org/) instance that's co-located with your Postgres database. This will require you to connect with IPv6 or, if that's not an option, you can use the [IPv4 add-on](/guides/platform/ipv4-address).

The dedicated pooler is isolated to your own project and grants you fine-grained control over the configuration.

### Connecting to your database

This gives you now 3 options for connecting to your database:

<Img
  alt="3 different connection options"
  src={{
    dark: '/images/blog/dedicated-poolers/connection-options.png',
    light: '/images/blog/dedicated-poolers/connection-options.png',
  }}
  caption="3 different connection options"
/>

1. **Direct connections (free):** recommended for when you are connecting using servers.
2. **Shared Pooler (free):** recommended for when you are connecting using serverless functions (like Next.js or AWS Lambda)
3. **Dedicated Pooler (Pro Plan and above):** recommended for when you are connecting using serverless functions and you start to scale up. It is available on the Pro Plan and above.

## Choosing between Supavisor and PgBouncer

A year ago AWS [started charging for IPv4 addresses](/blog/ipv6). We introduced [Supavisor](/blog/supavisor-postgres-connection-pooler) to ensure that you didn't need to pay to connect to your database if your network didn't support IPv6. Only ~45% of the world have adopted IPv6, so this was a great solution for many.

<Img
  alt="IPv6 adoption"
  src={{
    dark: '/images/blog/dedicated-poolers/ipv6-adoption.png',
    light: '/images/blog/dedicated-poolers/ipv6-adoption.png',
  }}
  caption="Source: https://www.google.com/intl/en/ipv6/statistics.html"
/>

In the recent months, our platform has seen unprecedented growth. Tens of thousands of new developers are pouring into Supabase every week, doing weird and wonderful things with their databases:

<Img
  alt="3 different connection options"
  src={{
    dark: '/images/blog/dedicated-poolers/signups.png',
    light: '/images/blog/dedicated-poolers/signups.png',
  }}
  caption="Supabase weekly sign ups"
/>

Introducing Dedicated Poolers gives you the flexibility to choose the right connection type for your use case. If you need dedicated hardware, you can now opt for a Dedicated Pooler on the Pro Plan and above for lower latency, better performance, and higher reliability.

### Getting started

Dedicated Poolers are available today for our Enterprise customers, and will be generally available by 20th March, 2025. If you want to be notified when it's ready, [sign up here](https://forms.supabase.com/dedicated-pooler).
