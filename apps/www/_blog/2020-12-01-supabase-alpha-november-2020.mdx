---
title: Supabase Alpha November 2020
description: Nine months of building.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
image: supabase-november-2020.png
categories:
  - product
tags:
  - supabase
date: '12-01-2020'
video: https://www.youtube.com/v/unC_de7iytA
---

We've been building for 9 months now, and we're getting even closer to Beta.

### Quick demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/unC_de7iytA"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

### Add users

You can now add users manually from your dashboard.

![This image shows how to invite a new user directly from the dashboard.](/images/blog/invite-users.gif)

### User admin

You can also perform admin functions on existing users - send password reset emails, magic links, and delete users.

![This image shows how to delete a user directly from the dashboard.](/images/blog/delete-users.gif)

### Even more powerful SQL Editor

Last month we [announced](/blog/supabase-alpha-october-2020#more-powerful-sql-editor) an improved SQL Editor, and this month we've taken it even further. The SQL Editor is now a full Monaco editor, like you'd find in VS Code. Build your database directly from the browser.

![This image shows our improved SQL Editor.](/images/blog/supabase-monaco-editor.png)

### Status page

We added a [Status Page](https://status.supabase.com/) which tracks the uptime and latency of the Supabase platform.

![This image shows our new status page.](/images/blog/status-page.png)

### Kaizen

- We completed a security audit by DigitalXRAID.
- Email confirmations now enabled by default for signups.
- Updated [Benchmarking Suite](https://github.com/supabase/benchmarks/) to include more realistic workloads, on various different servers (results published soon).
- You can now set/edit/remove Foreign Keys via the table editor.

### Community

We had a crazy month during Hacktoberfest. In case you missed it, here are some of the highlights:

- We have an exciting Vercel x Stripe x Supabase [collaboration underway](https://twitter.com/rauchg/status/1331021818681978881).
- [@elrhomariyounes](https://github.com/elrhomariyounes) started helping [@acupofjose](https://github.com/acupofjose) with the [postgrest-csharp](https://github.com/supabase/postgrest-csharp) extension
- [@duncanhealy](https://github.com/duncanhealy) is [updating the Redwood example](https://github.com/redwoodjs/redwood/pull/1474) to work with Supabase.js v1.0

If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).

### Coming next

Look out for a big announcement on December 3rd. We'll also be presenting in the [Open Core Summit](https://2020.opencoresummit.com/) on the 16th of December.

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
