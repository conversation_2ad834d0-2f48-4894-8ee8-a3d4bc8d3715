---
title: 'Kicking off the Holiday Hackdays'
description: Build cool stuff and celebrate open-source software with us during the Holiday Hackdays!
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: launch-week-three/holiday-hackdays-og.png
thumb: launch-week-three/holiday-hackdays-og.png
categories:
  - developers
tags:
  - hackathon
  - community
date: '2021-12-03'
toc_depth: 3
---

While not everyone will be celebrating the holidays this December, we can all be united in celebrating open-source software, and to do so we're kicking off the "Holiday Hackdays" (loosely inspired by the [12 Pubs of Christmas](https://www.irelandbeforeyoudie.com/12-pubs-of-christmas-rules-tips-everything-you-need-for-a-great-night/), only a bit healthier).

Starting today at 7am PT we'll be hacking together for the next 10 days until 23:59pm PT on Sunday December 12th. And because we've launched [row-level security for our Realtime API](https://supabase.com/blog/realtime-row-level-security-in-postgresql) earlier this week, we'd love for you to use it in your Hackathon project. May it be to build a [Santa Tracker](https://www.freecodecamp.org/news/create-your-own-santa-tracker-with-gatsby-and-react-leaflet/) or that secure family chat app to bring all your relatives together for the festive season, be creative and most importantly have fun 🥳

[As always](https://supabase.com/blog/hacktoberfest-hackathon-winners-2021#the-prizes), we'll have some extremely limited edition swag to give away that can only be won by participating in the Hackathon!

The Supabase team will be participating as well, for example [Jon](https://twitter.com/_dijonmusters) and I, inspired by [Egghead's "Holiday Course Release Extravaganza"](https://egghead.io/20-days), will be building an Advent Calendar for all the [awesome community content](https://supabase.com/blog/community-day#new-tutorials-and-integration-guides) you've been creating. Get ready for a cool open-source project which will be using a lot of the hottest Supabase features! To make sure you don't miss this one, follow us on [Twitter](https://twitter.com/supabase) and subscribe to our [YouTube channel](https://www.youtube.com/supabase). We'll see you there.

## Details

### Timeline

- **Friday Dec 3rd at 07:00am PT:** Last day of Launch Week. Join us on [Twitter Spaces](https://twitter.com/i/spaces/1eaKbNDPEOYKX?s=20) for the kickoff announcement.
- Build your project during the next 10 days and hang out with the community [on Discord](https://discord.gg/bnncdqgBSS).
- **Sunday Dec 12th at 11:59pm PT:** Submission deadline
- Judges Deliberate (Monday)
- We'll be contacting and announcing the winners [on Twitter](https://twitter.com/supabase) throughout the week after.

<blockquote class="twitter-tweet" data-theme="dark">
  <p lang="en" dir="ltr">
    Hacktoberfest Hackathon Swag finally arrived! 🥇🥈🥨 see the winners here:
    <a href="https://t.co/wgRBu92Bq4">https://t.co/wgRBu92Bq4</a>
    <a href="https://t.co/i1eD1pdA8I">pic.twitter.com/i1eD1pdA8I</a>
  </p>
  &mdash; Supabase (@supabase) <a href="https://twitter.com/supabase/status/1463355998928736262?ref_src=twsrc%5Etfw">November 24, 2021</a>
</blockquote> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

### Prize categories

There are multiple chances to win, there will be prizes for:

- Most impressive usage of the Realtime API features
- Most fun / holiday themed project

There will be a winner and a runner-up prize for each category.

### Submission

Submit your project via [madewithsupabase.com/holiday-hackdays](https://www.madewithsupabase.com/holiday-hackdays)

You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:

- link to hosted demo (if applicable)
- list of team members github handles (and twitter if they have one)
- any demo videos, instructions, or memes
- a brief description of how you used Supabase:
  - to store data?
  - realtime?
  - auth?
  - storage?
- any other info you want the judges to know (motivations/ideas/process)
- _optional_ team photo

### Judging & announcement of winners

The Supabase team will excitedly review what you've built. They will be looking for a few things, including:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

We'll be contacting and announcing winners [on Twitter](https://twitter.com/supabase) throughout the week after submission closes.

<blockquote class="twitter-tweet" data-dnt="true" data-theme="dark">
  <p lang="en" dir="ltr">
    Absolutely buzzing to have won the
    <a href="https://twitter.com/supabase?ref_src=twsrc%5Etfw">@supabase</a> hackathon! 🥳
    <a href="https://t.co/rm5HBuju73">https://t.co/rm5HBuju73</a>
  </p>
  &mdash; Josh Cawthorne (@cawthornejosh) <a href="https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw">August 10, 2021</a>
</blockquote> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

### Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be in multiple teams
- One submission per team
- All design elements, code, etc. for your project/feature must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.

### Community & help

Hang out with the Supabase team and community on Discord:

- Text channel: hackathon
- Audio channel: hackathon

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

Join our Discord: [discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)

![Discord Hangout](/images/blog/hackathon/community.png)

### Resources & Guides

Here's a collection of resources that will help you get started building with Supabase:

- Need some inspiration? [See what folks built last time](/blog/hackathon-winners)!
- [Examples and Resources](/docs/guides/examples)
- [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]
- [Flutter Quickstart Guide](/docs/guides/with-flutter)
- [Nextjs Quickstart Guide](/docs/guides/with-nextjs)
- [Using Supabase inside Replit](/blog/using-supabase-replit)
- [Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)
- [Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]
- [Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)
- [How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]
- [Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]
- [Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)

### Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
