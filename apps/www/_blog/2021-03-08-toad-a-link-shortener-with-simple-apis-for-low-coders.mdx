---
title: Toad, a link shortener with simple APIs for low-coders
description: An easy-to-use link shortening tool with simple APIs
author: rory_wilding
author_title: Supabase
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: toadli-og.jpg
thumb: toadli-website.jpg
categories:
  - engineering
tags:
  - supabase
date: '03-08-2021'
---

<PERSON> is an engineer, investor, and founder of Kopa - a short term furnished rental marketplace with operations in over 400 cities in the USA. <PERSON>'s latest project, [Toad](https://toadli.co/), is an easy-to-use link shortening tool with simple APIs, aimed to empower low-coders. Toad provides a simple dashboard, analytics, and API designed with low-coders in mind.

Learn why <PERSON> used Supabase as part of his serverless SAAS setup.

### Toad APIs for low Coders

Zach loves link shorteners. However, for low-coders, link shorteners tend to have cumbersome APIs limiting their utility. He wanted to build a link shortening tool with simple APIs that are simple for no-coders to use.

## The power of a serverless Supa-stack

Zach started this project with a low-management overhead in mind. He needed a robust and scalable stack that doesn't need refactoring when his service user volume grows. <PERSON> knew that a back-end as-a-service abstracts away many of the dev-ops tasks, such as provisioning server instances and setting up databases. From experience, he knew that Firebase would be too cumbersome.

Zach decided that combining Supabase, Lambda functions, Next.js, TailwindCSS, and Vercel would be the perfect Supa-stack to achieve his goal of low-management overhead. This setup removes significant dev-ops headaches with no need to think about continuous integration - when he wants to make a change, it is good to go. As a bonus, this setup is virtually free until he reaches significant user volumes.

### Build it once, use it forever

Zach was surprised how rapidly he was able to build and deploy Toadli. He now has a template project that he plans to customise for future SaaS services. Zach has a stack that is scalable, reusable, and low-management. Toad has early paying customers, and traffic volumes are increasing as low-coders become aware of how easy Toad APIs are to use.

<Quote img="zach-waterfield.png" caption="Zach Waterfield - Toad">
  Full Serverless is an amazing developer experience. You can get high-quality results fast,
  especially when Supabase abstracts away all the back-end headaches
</Quote>

## Spend more time creating and less time managing back ends

Thanks to Supabase, Zach is able to focus on the creative process and launch services like [Toad](https://toadli.co/) with all the functionality he wants to offer. He has peace of mind that his stack is low-management and will scale as his project continues to gain traction.
