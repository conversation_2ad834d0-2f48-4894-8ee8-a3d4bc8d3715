---
title: Supabase Dot Com
description: The Supabase Domain name is changing.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: supabase-dot-com-og.jpg
thumb: supabase-dot-com-og.jpg
categories:
  - product
tags:
  - supabase
date: '2021-04-02'
---

_tl;dr_ During the next week, we'll be migrating from "supabase.io" to "supabase.com".

There are no changes required on your end. Your API URLs will remain on "supabase.co", and only our website URLs will change.

The rest of this post talks a bit about the origin of the name, but there's nothing important to know.

## SupaWhat?

At the end of 2019 we released a Postgres [Realtime](https://github.com/supabase/realtime) engine on GitHub.
We created this in my previous company after migrating from Firebase to Postgres.

It started gaining traction (if you count "GitHub stars" as traction) and so I tapped my friend, [<PERSON><PERSON>](https://twitter.com/<PERSON><PERSON><PERSON><PERSON><PERSON>),
on the shoulder to see if he wanted to build an open source company with me (he was building another start up at the time).

The company we envisaged had Postgres at the core. We searched for every "dot com" we could think of with "base" in the name - hyperbase,
superbase, suprabase, uberbase. It turns out this is what every database start does. We couldn't find any good "dot coms".
And so we moved on to "dot io" domains.

Anybody who knows Ant, knows that he [loves memes](/blog/launch-week). While we were brainstorming
we found that "supabase.io" was available.

To be honest, this name wasn't a strong contender, but it served a very important purpose - it was extremely "meme-able" because
it sounds similar to Nikki Minaj's [Super Bass](https://youtu.be/4JipHEz53sU).

We chose it as a placeholder and found it was, indeed, very funny to send each other Nikki memes while we brainstormed the business.

![Booom booom booom booom](/images/blog/nikki-supabase.jpg)

Note to anyone starting a business, don't use a placeholder. You'll never change it.

Luckily for us, the name [grew on us](https://news.ycombinator.com/item?id=23325430) and now we love it.

## Supabase Dot Com

When we joined Y Combinator, they gave this advice:

> Except in unusual circumstances (like [Twitch.tv](http://twitch.tv/)), if you don't have the .com version of your name,
> you should probably change it.

We decided to try purchasing [supabase.com](http://supabase.com) from the current owner. The YC forums were full of founders
recommending a particular Domain Specialist who would help us negotiate and purchase the domain. The only catch - he would take
a percentage of the purchase price.

This struck us as odd. The incentives were more aligned to the domain seller than the purchaser.

We're not afraid to roll up our sleeves at Supabase, so we decided we could handle it ourselves.

We were in for a huge surprise. After searching on the (old) [supabase.com](http://supabase.com) website, we found a company address.
And it was literally 100m from my home.

Of all the places in the world that the domain could be registered, the owner was within yelling-distance. But we didn't yell. We simply
asked, and after some negotiation, the owner agreed to sell it.

We were always willing to walk away from the Supabase brand if we needed to, but the price was reasonable for both parties. There really
isn't much more to it than that.

## What's next?

Over the next few weeks, we'll update our domain name across most of the sites. If you're using a Supabase database and API, you're on
a ".co" domain so your apps won't be affected at all.

You may also find some [easter eggs](https://twitter.com/AntWilson/status/1354343248098070530) appear ...
