---
title: 'Holiday Hackdays Winners 2021'
description: Celebrating many amazing projects submitted to our Holiday Hackdays Hackathon.
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: hackathon-winners/holiday-hackdays/holiday-hackdays-winners-og.png
thumb: hackathon-winners/holiday-hackdays/holiday-hackdays-winners-og.png
categories:
  - developers
tags:
  - community
  - hackathon
date: '2021-12-17'
toc_depth: 2
---

To cool down from [our latest launch week](/blog/beta-november-2021-launch-week-recap) we (virtually) [sat down with y'all](https://youtu.be/TijBVcV4jXw) and started hacking, and once again we absolutely loved your hackathon submissions and are excited to send some extremely limited swag your way!

As always, it was challenging to pick the winners from all the great submissions! You can see all the amazing projects submitted on [madewithsupabase.com](https://www.madewithsupabase.com/holiday-hackdays). And now, without further ado, let's look at some of our favourites in more detail:

## Best Overall Project

### Winner

[Swappy.one](https://github.com/zernonia/swappy-one) - by [@zernonia](https://twitter.com/zernonia)

Zernonia is one of our [SupaSquad](https://supabase.com/supasquad) members and continuously blows our minds with his creativity for the open-source projects he creates. For example, he's also the mastermind behind [madewithsupabase.com](https://www.madewithsupabase.com/), one of our favourite resources to see what y'all are building.

Now he can add a gold tee to his collection, and we're starting to run out of swag ideas to show our appreciation! (Send us some ideas on [Twitter](https://twitter.com/supabase)!)

[![Swappy.one](https://www.madewithsupabase.com/api/resize?link=Swappy.one-rsx33-Frame%2030.png&w=1000)](https://github.com/zernonia/swappy-one)

### Runner Up

[Chivel](https://github.com/lalit2005/chivel) - by [@lalitcodes](https://twitter.com/lalitcodes) and [@ChapagainAshik](https://twitter.com/ChapagainAshik)

Chivel is a tool to quickly generate a landing page for your YouTube channel. For example check out this snazzy page that they've generated for the Supabase channel: [supabase.chivel.tk](https://supabase.chivel.tk/)!

A great idea that deserves some silver tees!

[![Chivel](https://www.madewithsupabase.com/api/resize?link=Chivel-a03sx-ogimage.png&w=1000)](https://github.com/lalit2005/chivel)

## Best Realtime Project

### Winner

[rtPoll](https://github.com/emilioschepis/rtpoll) - by [@emilioschepis](https://twitter.com/emilioschepis) & brother Federico.

Brothers Emilio and Federico built a platform to quickly create polls and share them around. Seeing poll results is great, but you know what's even better? Exactly, seeing poll results in realtime!

[![rtPoll](https://madewithsupabase.com/api/resize?link=RT%20Poll%20-%20Realtime%20Polls-bpl8s-rtpoll-rt.png&w=1000)](https://github.com/emilioschepis/rtpoll)

### Runner Up

[e2ee-chat](https://github.com/arnu515/supabase-e2ee-chat) - by [@arnu5152](https://twitter.com/arnu5152)

A chat app is a fairly obvious use case for realtime syncing, but this project caught our eye because the chat is end-to-end encrypted!

[![e2ee-chat](https://www.madewithsupabase.com/api/resize?link=End-to-end%20encrypted%20chat-eap1qg-Screenshot%20from%202021-12-12%2014-32-08.png&w=1000)](https://github.com/arnu515/supabase-e2ee-chat)

## Best Christmas Themed

### Winner

[Santa Banter](https://github.com/Keoooo/santa-banter) - by [@AndyKeogh](https://twitter.com/AndyKeogh)

Not only do we love a good rhyme, but we also love Christmas themed jokes, so Andy's Santa Banter checks all the boxes. Head over there now to submit your best jokes!

[![Santa Banter](https://madewithsupabase.com/api/resize?link=Santa%20Banter%20-dc8wef-Screenshot%202021-12-07%20at%2009.59.08.png&w=1000)](https://github.com/Keoooo/santa-banter)

### Runner Up

[Holiday Sweater](https://github.com/Morel-Tech/ugly_sweater_app) - by [@mtwichel](https://twitter.com/mtwichel)

Ugly sweaters are an integral part of the Christmas Holidays. With Marcus' awesome Flutter app you can now vote on your favourite sweaters from around the globe!

[![Holiday Sweater](https://madewithsupabase.com/api/resize?link=Holiday%20Sweater%20Contest-pjm3r-Screen%20Shot%202021-12-12%20at%2010.52.24%20PM.png&w=1000)](https://github.com/Morel-Tech/ugly_sweater_app)

## Most Visually Pleasing

### Winner

[the get list](https://github.com/glowdex/wishlist) - by [@glowdexapp](https://twitter.com/glowdexapp)

This visually stunning website allows you to create a wishlist to share with your friends so they don't need to wreck their brains for gift ideas. Here, have a look at what I want this Christmas: [thegetlist.co/list/thorwebdev](https://www.thegetlist.co/list/thorwebdev).

[![the get list](https://www.madewithsupabase.com/api/resize?link=thegetlist.png&w=1000)](https://github.com/glowdex/wishlist)

### Runner Up

[card creator](https://github.com/maggie-j-liu/card-creator) - by [maggie-j-liu](https://github.com/maggie-j-liu), [Arash](https://github.com/arashnrim), [@eiilla11](https://twitter.com/eiilla11), and [@\_pranavnt](https://twitter.com/_pranavnt)

This team of high school hackers just keeps knocking it out of the park again. In our [Hacktoberfest Hackathon](https://supabase.com/blog/hacktoberfest-hackathon-winners-2021#most-spookyfun) they already made it onto the podium. This time around they built this stunning greeting card platform that lets you send well wishes to your friends around the globe in no time. We love it!

[![card creator](https://madewithsupabase.com/api/resize?link=Card%20Creator-vs9hc-Screen%20Shot%202021-12-12%20at%2011.12.23%20PM.png&w=1000)](https://youtu.be/Yx4N2bONA44)

## The Prizes

As is now tradition with Supabase Hackathons, the winners will receive an extremely limited edition gold medal shirt.

![Gold Tshirt](/images/blog/hackathon-winners/holiday-hackdays/holiday-hackdays-winners-shirt.png)

And the runner uppers will receive the same shirt but in silver. These shirts are limited to winners of the hackathon and will never be produced again. Keep them safe like your favourite NFTs!

## Hang out with us and the community

If you want to join the community and build with us, find other people using Supabase, or if you just want to chill and watch people build, come and join us in Discord!

[Join our Discord](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Get Started Guides

If you're inspired to build, check out some of the latest resources:

- [Build a SaaS product with Next.js, Supabase and Stripe](https://egghead.io/courses/build-a-saas-product-with-next-js-supabase-and-stripe-61f2bc20)
- [Supabase & Sveltekit - Build Twitter in 75 minutes](https://youtu.be/mPQyckogDYc)
- [Supabase Auth With Next.Js 12 Middleware](https://jitsu.com/blog/supabase-nextjs-middleware#what-were-going-to-build)
- [Vue 3 & Supabase | Workout Tracker App](https://www.youtube.com/watch?v=3tF0fGkd4ho)
- [Use Supabase Auth with Vue.js 3](https://vueschool.io/articles/vuejs-tutorials/use-supabase-auth-with-vue-js-3/)
- [Dynamic Jamstack with Stencil and Supabase](https://ionicframework.com/blog/dynamic-jamstack-with-stencil-and-supabase)
