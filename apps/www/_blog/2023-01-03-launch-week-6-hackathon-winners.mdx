---
title: 'Launch Week 6 Hackathon Winners'
description: Announcing the winners of the Launch Week 6 Hackathon!
author: and<PERSON>_smith,tyler_shukert
image: lw6-hackathon-winners/hackathon-winners.png
thumb: lw6-hackathon-winners/hackathon-winners.png
categories:
  - developers
tags:
  - hackathon
  - community
date: '2023-01-03'
toc_depth: 2
---

We ended 2022 shipping tons of features in [Launch Week 6](https://supabase.com/launch-week), but the fun hasn’t ended yet, because we get to announce the winners of our [Hackathon No.6](https://supabase.com/blog/launch-week-6-hackathon)!

As usual, we enjoyed looking at all the awesome projects, and this time we got to share the excitement with the folks from Deno, who judged the [Best Edge Functions Project](https://deno.com/blog/edge-functions-supabase-launch-week-6-hackathon#Winners). You can view all the submissions on [madewithsupabase.com](https://www.madewithsupabase.com/).

And now, without further ado, congratulations to the winners of Supabase Launch Week 6 Hackathon!

## Best Overall Project

### Winner

[Depulso](https://github.com/KarthikeyanRanasthala/depulso/) - by [@\_iamkarthikeyan](https://twitter.com/_iamkarthikeyan) and [@SindhuMansi](https://twitter.com/SindhuMansi).

Depulso is a CLI and a web app to easily and quickly setup static site deployments. Each deployment gets assigned a .depulso.site subdomain with SSL.

![Depulso](/images/blog/lw6-hackathon-winners/depulso.png)

### Runner Up

[Supa SQL App](https://github.com/rodgetech/supa-sql-app) - by [@luis_rodge](https://twitter.com/luis_rodge).

Supa SQL App turns human language into SQL queries. Simply tell it what you want to do and it will generate the SQL query for you.

![Supa SQL App](/images/blog/lw6-hackathon-winners/supa-sql-app.png)

## Most Fun/Interesting

### Winner

[HTTP Sense](https://github.com/http-sense/http-sense) - by [@tchyut_p](https://twitter.com/tchyut_p) and [@nkitsaini](https://github.com/nkitsaini).

HTTP Sense is a reverse proxy and network monitoring tool that provides comprehensive network monitoring for your backend servers and microservices. It provides a secure and high-performance gateway that can be used to monitor incoming traffic.

![HTTP Sense](/images/blog/lw6-hackathon-winners/http-sense.png)

### Runner Up

[supabase-js-rs](https://github.com/wa1aric/supabase-js-rs) - by [@wa1aric](https://twitter.com/wa1aric).

supabase-js-rs is a rust bindings for Supabase JavaScript library via WebAssembly. You can import it in your Rust WASM project to interact with Supabase.

![supabase-js-rs](/images/blog/lw6-hackathon-winners/supabase-js-rs.png)

## Best Flutter Project

### Winner

[supaquiz - Multiplayer trivia/quiz game](https://github.com/yallurium/supaquiz) - by [@yallurium](https://twitter.com/yallurium).

Supaquiz is a realtime multiplayer trivia/quiz game where you can host or join games with other players remotely.

![supaquiz - Multiplayer trivia/quiz game](/images/blog/lw6-hackathon-winners/supaquiz.png)

## Best Storage Project

### Winner

[Lookout](https://github.com/luxecraft/lookout) - by [Aman](https://twitter.com/Amanrk28), [Nivas](https://twitter.com/nithsua), [Hari](https://twitter.com/_harisaran_), and [Sabesh](https://twitter.com/sabeshbharathi).

Lookout is an application that lets you discover images from across various media by searching them using their metadata - their properties, content inside the image, textual data found inside the image etc.

![Lookout](/images/blog/lw6-hackathon-winners/lookout.png)

### Runner Up

[Penelope AI](https://github.com/taishikato/Penelope-AI) - by [@taishik\_](https://twitter.com/taishik_).

Penelope AI is an AI writing assistant that can summarize, paraphrase, create stories, and auto-complete from your writing.

![Penelope AI - Unleash the power of your writing with the most sophisticated AI writing assistant.](/images/blog/lw6-hackathon-winners/penelope-ai.png)

## Best Edge Functions Project

### Winner

[Vista](https://github.com/zernonia/vista) - by [@zernonia](https://twitter.com/zernonia).

Vista is a video transcoder that adds subtitles from the audio. The design of the subtitles are customizable to fit the theme of the video.

![Vista](/images/blog/lw6-hackathon-winners/vista.png)

### Runner Up

[Supa Que](https://github.com/suciptoid/supaque) - by [@suciptodev](https://twitter.com/suciptodev).

Supa Que is a serverless queue worker.

![Supa Que](/images/blog/lw6-hackathon-winners/supaque.png)

## The Prizes

As promised in the [hackathon announcement article](https://supabase.com/blog/launch-week-6-hackathon) the winners of the Best overall project will be granted the extremely limited edition Supabase Darkmode Keyboard!

![Supabase Keyboard](/images/blog/lw6-hackathon/keyboard.png)

Each members of the winners in other categories will receive a limited edition supaverified swag kit, and the winners of the Best Edge Functions projects will receive swag kits from the Deno team.

![Swag kit](/images/blog/lw6-hackathon/swagkit.png)

The swag kits are limited edition and extremely valuable, so keep them safe! Again, congratulations on all the winners!

## Hang out with us and the winners of the Hackathon

To celebrate the massive success of the Hackathon, we are hosting a Discord Stage Channel and inviting the winners and everyone who participated. Join us on Thursday, Jan 5th at 7:00 AM PST.

[**Join the Discord event**](https://discord.gg/8gGcMp5x?event=1059852103754989659)

![Join Supabase Discord Channel](/images/blog/hackathon/community.png)

### Get Started Guides

If you're inspired to build, check out some of the latest resources:

- [**supabase-js v2**](https://supabase.com/blog/supabase-js-v2)
- [**Supabase Storage Guide**](https://supabase.com/docs/guides/storage)
- [**Flutter Quick Start Guide**](https://supabase.com/docs/guides/with-flutter)
- [**Supabase CLI local development guide**](https://supabase.com/docs/guides/local-development)
- [**Edge Functions Guide**](https://supabase.com/docs/guides/functions)
