---
title: Roboflow.com choose Supabase to power Paint.wtf leaderboard
description: Learn how Roboflow.com used Supabase to build their Paint.wtf leaderboard
author: rory_wilding
author_title: Supabase
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: roboflow-og.png
thumb: roboflow-website.png
categories:
  - company
tags:
  - AI
date: '02-09-2021'
---

<PERSON> is the founder of [Roboflow](https://roboflow.com/?ref=supabase), a startup helping developers build computer vision into their applications. Their solution allows developers, with zero computer vision experience, to go from images to a trained computer vision model in minutes.

Learn how <PERSON> and <PERSON><PERSON><PERSON> used Supabase to launch [Paint.wtf](https://paint.wtf), a product which survived traffic generated from the front page of Hacker News, Reddit, and Product Hunt.

![Supabase and Strive are partnering up to teach OSS.](/images/blog/roboflow-website.png)

## From idea to a launch in a weekend

Brad and a friend ([<PERSON>](https://twitter.com/erikdoingthings)) wanted to experiment with OpenAI's new [CLIP](https://openai.com/blog/clip/) model through a side project they could build in a weekend. CLIP classifies a wide range of images by flipping image classification into a text similarity task. Current image classifiers are limited because they are trained on a fixed number of categories. In contrast, CLIP learns from the raw text describing the images meaning the classifier isn't limited by labels and supervised learning. The team recognised that CLIP opens up a vast range of use cases that have been difficult previously due to the time required to collect images and train the model.

Roboflow settled on a straightforward concept: they prompt users to draw an image which is then fed into CLIP. The AI then judges how close the drawing is to the given prompt and assigns it a score. Users' performance is tracked on a leaderboard for each prompt and users can see how well they performed against their peers according to the model.

## Leaderboards that Count

Brad and the team needed a leaderboard to make this idea work. While their first intuition was to use Firebase, it lacks the built-in functionality for counting the number of documents in a collection - a critical function for implementing their leaderboard design.

![Supabase and Strive are partnering up to teach OSS.](/images/blog/roboflow-stat.png)

You might export data to BigQuery, or implement an increment function on collection change, however PostgreSQL has great built in support for counting and Brad felt like this would be the perfect opportunity to test out Supabase and get the functionality he needed for the leaderboards to work reliably and without implementing add-ons.

## One weekend to break the internet

Brad and the team built the product overnight and launched it on Hacker News. Paint.wtf went on to make it to Hacker News and Reddit's first page, and getting traction on Product Hunt. As a result, they had to handle serious user volumes; at its peak, users were submitting over 2 new drawings every second.

Over 100K users submitted drawings in a 24 hour span. At this point, Brad knew he had picked the right setup for his leaderboard as even with this massive spike in usage it continued to perform reliably so his users could get accurate and up to date rankings for their submissions.

![Supabase and Strive are partnering up to teach OSS.](/images/blog/roboflow-gallery.png)

Paint.Wtf has continued to get sustained coverage in the media and has continued to pick up new and unexpected use cases. For example, remote teams are using Paint.wtf as part of their daily ice breaker activities during COVID to keep up team social cohesion.

![Supabase and Strive are partnering up to teach OSS.](/images/blog/roboflow-quote.png)

## Ship fast, and carry on scaling

Thanks to Supabase, Roboflow could launch quickly and keep scaling despite the huge user volumes their innovative project generated overnight.
