---
title: 'Supabase Series B'
description: Supabase raised $80M in May, bringing our total funding to $116M.
author: paul_copplestone
image: series-b/supabase-series-b.png
thumb: series-b/supabase-series-b.png
categories:
  - company
tags:
  - supabase
date: '2022-08-12'
toc_depth: 3
video: https://www.youtube.com/v/4t_63HT3rZY
---

Supabase raised $80M in May, bringing our total funding to $116M. This comes one year after our Series A, and so we're revisiting the plans
we outlined in our [Series A blog post](https://supabase.com/blog/supabase-series-a) tk hold ourselves accountable for the promises we made.

## Where we've been

We shared a few metrics in our Series A post. Since then we've grown a lot.

### Database growth

At Series A we'd launched over 50,000 PostgreSQL databases (on our hosted platform).

![Series A databases](/images/blog/series-b/databases-2021.png)

As of today, we've launched over 150,000 PostgreSQL databases.

![Series B databases](/images/blog/series-b/databases-2022.png)

### Developer Signups

At Series A we had 40,000 developers.

![Series A developers](/images/blog/series-b/developers-2021.png)

Today we have more than 110,000 developers.

![Series B Developers](/images/blog/series-b/developers-2022.png)

### Community

We've seen a lot of growth in our community:

- Discord: 4000 → 8000
- Twitter: 18,000 → 37,000
- GitHub Stars: 19,000 → 36,000

[Supabase vs React](https://star-history.com/#supabase/supabase&facebook/react&Timeline) (GitHub stars)

![Supabase vs React](/images/blog/series-b/supabase_react_github.png)

It's early days but we're doing well for a database company.

## About the round

Our Series B is led by [Felicis](https://www.felicis.com/), an amazing team of people who support the long-term approach we're taking to build an open source business.

<Quote img="aydin.jpeg" caption="Aydin Senkut, Founder and Managing Partner of Felicis.">
  <p>
    We are super excited to be investing in Supabase and partner with the team on their next phase
    of growth.
  </p>
  <p>
    Supabase's team is made up of 15% former founders and 70% developers, and they deeply understand
    the pain points that developers overcome to rapidly develop products. Supabase truly enables
    developers to build their applications without repeating the same tedious tasks and manage their
    application's database, authentication, storage, and edge functions.
  </p>
</Quote>

Joining the round are Coatue (who led our Series A & Seed round), [Lightspeed](https://lsvp.com/), and [Square Peg Capital](https://www.squarepegcap.com/).

## Giving back

Supabase is a collaborative company. We aim to support existing open source tools before developing anything ourselves.
When we do develop ourselves, we try to include our community as maintainers in a variety of ways. We've been experimenting
with several models to support the open source ecosystem.

### Open collective

To date, we've [paid over $80,000](https://opencollective.com/supabase#category-BUDGET) to contributors and maintainers through our open collective.
This is for everything from maintaining our libraries to moderating our Discord.

We've had some amazing open source stories like [Isaiah](https://github.com/Isaiah-Hamilton), a 15-year old who started contributing after his school day.
Or [Olyno](https://github.com/Olyno), who created a Supabase Discord on their own initiative and is now an official moderator.
Or [Zernonia](https://github.com/zernonia/), who created [madewithsupabase.com](https://www.madewithsupabase.com/) which is now central to every Supabase Hackathon.

### PostgREST

[PostgREST](https://postgrest.org) is a automatically generate RESTful APIs from your Postgres schema, and it's one of the core pieces of the Supabase stack.
As well as being a gold sponsor, Supabase hired [Steve](https://github.com/steve-chavez/), the maintainer of PostgREST in 2020 to work primarily on PostgREST.
This model has been surprisingly successful and we hope that more companies will consider this approach for supporting open source projects.

### Elixir Type support

Elixir is a functional programming language that enables huge horizontal scalability. We use it at Supabase for our Realtime
engine and our logging infrastructure. Recently [announced by José](https://twitter.com/josevalim/status/1535008937640181760?s=20&t=BLqeO2YpdhYfCZxgsmlNug) at ElixirConf EU,
the Elixir team is investigating a type system. Supabase is sponsoring this research and development.

### Deno

[Functions](https://supabase.com/edge-functions) was one of the most demanded features of Supabase, and we explored a huge number of options before settling on Deno.
Deno checked all the boxes - most important for us was their approach to open source. Both Supabase and Netlify now support Deno Edge Functions,
and we hope that our backing will help convince others to adopt the Deno runtime too.

### Database encryption

A teaser for next week's [Launch Week](https://supabase.com/launch-week) - a few months ago we hired [Michel](https://github.com/michelp),
the maintainer of [pgsodium](https://github.com/michelp/pgsodium), to re-think what a modern database encryption system and
database secrets management would look like. Since then he has released two major versions of
pgsodium: [2.0](https://www.postgresql.org/about/news/pgsodium-200-modern-cryptography-for-postgresql-2389/)
and [3.0](https://github.com/michelp/pgsodium/releases/tag/v3.0.0) to
support [Transparent Column Encryption](https://github.com/michelp/pgsodium#transparent-column-encryption).

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/4t_63HT3rZY"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

<small>
  <i>
    Founder Chat: Paul Copplestone and Ant Wilson discuss the several ways we give back to the
    community, and what Open Source means for Supabase.
  </i>
</small>

## Where we're going

In our Series A post, we outlined our plans for Supabase [in three phases](https://supabase.com/blog/supabase-series-a#building-supabase):

- Phase 1: [Start with scalability](https://supabase.com/blog/supabase-series-a#phase-1-start-with-scalability)
- Phase 2: [Postgres Tooling](https://supabase.com/blog/supabase-series-a#phase-2-postgres-tooling)
- Phase 3: [Cloud-native PostgreSQL](https://supabase.com/blog/supabase-series-a#phase-3-cloud-native-postgresql)

We're now moving into Phase 3, with the same goals that we outlined in 2021:

- **Branching:** <br />
  Developers should be able to "fork" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.
- **Scalable storage:** <br />
  Storage should grow and shrink without the user needing to provision more space themselves.
- **Distributed:** <br />
  An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).
- **Ephemeral compute:** <br />
  Developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.
- **Snapshots and time-travel:** <br />
  Developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.

We have a lot of work ahead of us, but we're excited to announce one of the ways that we're supporting the community is through our investment into OrioleDB.

OrioleDB is a [PostgreSQL extension](https://github.com/OrioleDB/OrioleDB) which solves a lot
of [“wicked” problems](https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf) with Postgres.

While it _currently_ requires some modifications to the Postgres core (about 1000 lines), the goal is to upstream those changes so
that _anyone_ can build a Storage extension like OrioleDB. Giving developers low-level access to the underlying storage capabilities of
Postgres will unlock a slew of new capabilities in Postgres.
You can read more about OrioleDB's plans for Snapshots and Branching [in their wiki](https://github.com/OrioleDB/OrioleDB/wiki/Database-branching).

Supabase has invested $500K into OrioleDB to support their efforts, and we've hired a developer at Supabase to work full-time on OrioleDB, with more to come.
[Apply here](https://boards.greenhouse.io/supabase/jobs/4307456004) if you're interested in working on Postgres full time.

<div className="bg-gray-300 rounded-lg p-6 italic">
 Note: we are not running OrioleDB on the Supabase platform. Our promise to you is “no vendor-lockin”, and therefore we will never run a fork of Postgres.
 In the future, if all of the OrioleDB core changes are up-streamed, then we might offer it on the platform.

If you want to try out OrioleDB today, you can switch the Postgres docker image to OrioleDB in the [self-hosted setup](https://github.com/supabase/supabase/blob/ec6085b8f852a903f2f45e715add1377cf89d850/docker/docker-compose.yml#L159).

</div>

## Join us

Achieving the goals we've outlined above will be a long journey requiring collaboration from many companies besides Supabase.

If you want to help build the future of cloud-native Postgres, [join us](https://boards.greenhouse.io/supabase/jobs/4307456004) at Supabase.
If you're already working towards the same goals, reach out and let's do it together.

## By the way

Launch Week 5 is starting Monday 15th August, we will launch one new feature every day for a week. If you want to follow along check out [supabase.com/launch-week](https://supabase.com/launch-week).

- [Launch Week 5 Hackathon](https://supabase.com/blog/launch-week-5-hackathon)
- [Day 1 - Supabase CLI v1 and Management API Beta](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)
- [Youtube video - Supabase CLI v1 and Management API Beta](https://www.youtube.com/watch?v=OpPOaJI_Z28&feature=emb_title)
- [Day 2 - supabase-js v2 Release Candidate](https://supabase.com/blog/supabase-js-v2)
- [Youtube Video - supabase-js v2 Release Candidate](https://www.youtube.com/watch?v=iqZlPtl_b-I)
- [Day 3 - Supabase is SOC2 compliant](https://supabase.com/blog/supabase-soc2)
- [Youtube video - Security Day](https://www.youtube.com/watch?v=6bGQotxisoY)
