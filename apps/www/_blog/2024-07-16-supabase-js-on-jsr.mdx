---
title: 'Announcing Supabase on JSR'
description: 'Supabase is now available on the open source JavaScript Registry (JSR).'
author: soed<PERSON><PERSON>,thor_schaeff
image: jsr_supabase/jsr_supabase.png
thumb: jsr_supabase/jsr_supabase.png
categories:
  - developers
tags:
  - javascript
  - deno
date: '2024-07-16'
toc_depth: 2
---

<Admonition>

Prefer Den<PERSON>'s awesome visuals? Read the announcement on [<PERSON><PERSON>'s blog](https://deno.com/blog/supabase-on-jsr).

</Admonition>

[JSR](https://jsr.io/) is a modern [open source](https://github.com/jsr-io/jsr) JavaScript registry that simplifies publishing and importing JavaScript and TypeScript modules. JSR supports publishing TypeScript source code, auto-generating documentation and type definition files, provenance attestation for more security, and can be used with npm-like package managers. Since its launch, over 250 new packages are being published each week.

We're thrilled to announce that our [JavaScript client library](https://jsr.io/@supabase/supabase-js) is now available on JSR.

As many of you know, our JavaScript library, `supabase-js`, is composed of [many sub-clients](https://github.com/supabase-community#client-libraries) that let you query your Supabase [database](/database), subscribe to [real-time events](/realtime), upload and download [files](/storage), manage [auth](/auth), invoke Deno powered [Edge Functions](/edge-functions), and [more](/vector). It's fully isomorphic and can be used across any environment that speaks JavaScript and HTTPS, such as browsers, servers, and [the edge](https://deno.com/blog/the-future-of-web-is-on-the-edge)!

Using supabase-js via JSR offers an excellent developer experience, with first class TypeScript support, auto-generated documentation accessible in your code editor, and more.

## Installing Supabase

You can get started with Supabase using the `deno add` command:

```bash
deno add @supabase/supabase-js
```

Or using npm:

```bash
npx jsr add @supabase/supabase-js
```

The above commands will generate a `deno.json` file, listing all your project dependencies.

```json deno.json
{
  "imports": {
    "@supabase/supabase-js": "jsr:@supabase/supabase-js@2"
  }
}
```

You can then import the client library to your `main.ts` file:

```js main.ts
import { createClient } from '@supabase/supabase-js'

const supabase = createClient('https://xyzcompany.supabase.co', 'public-anon-key')

console.log('Supabase Instance: ', supabase)
```

Finally, you can run the following command to execute:

```bash
deno run -A main.ts
```

Check out the [`supabase-js` README](https://jsr.io/@supabase/supabase-js) to see how to use it in other environments.

## What's next?

With the Supabase client on JSR, you can easily and quickly add authentication or persistent storage to your projects, which can run in any JavaScript environment!

- [Check out JSR](https://jsr.io/) the JavaScript registry built for the modern web.
- Read the announcement on [Deno's blog](https://deno.com/blog/supabase-on-jsr).
