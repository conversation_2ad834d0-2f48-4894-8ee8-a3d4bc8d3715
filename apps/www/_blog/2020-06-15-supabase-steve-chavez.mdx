---
title: <PERSON> has joined Supabase
description: <PERSON> joins Supabase to help build Auth.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
categories:
  - developers
tags:
  - supabase
  - hiring
date: '06-15-2020'
---

This month we welcome [<PERSON>](https://github.com/steve-chavez) to the team. <PERSON> is a maintainer of PostgREST, one of the core tools which makes Supabase possible.

<PERSON> and I have been in contact since he [added one of my old blog posts](https://github.com/PostgREST/postgrest-docs/commit/3dde972d31519d3afc319015bce1dc0f73adeb8e#commitcomment-35312841) to the PostgREST docs. He has a rich history in open source and we're excited to have him in our team.

<PERSON> is helping us build one of our most demanded features - our Auth system. We will support him to build some exciting features which he has planned for PostgREST, including enhanced PostGIS support.
