---
title: "Keeping Tabs on What's New in Supabase Studio"
description: Tabs in the Editors! And upgrades to AI Assistant, SQL, and Logs
author: jord<PERSON>,jonny,josh<PERSON><PERSON>,saxon_fletcher
image: lw14-tabs-dashboard-updates/tabs-dashboard-updates-og.png
thumb: lw14-tabs-dashboard-updates/tabs-dashboard-updates-thumb.png
categories:
  - launch-week
tags:
  - launch-week
  - studio
date: '2025-04-02T00:00:00'
toc_depth: 3
launchweek: 14
---

We've had a busy few months working on Studio improvements and new features—big and small—to help you build, debug, and ship faster.

Here's a quick rundown of what's new:

- [**Tabs!**](#tabs): The Table Editor and SQL Editor now have tabs!

- [**Customizable Reports**](#customizable-reports): create reports to show
  data exactly how you want to see it

- [**SQL Blocks in Custom reports**](#sql-blocks-in-custom-reports): use SQL Snippets in blocks inside your custom reports

- [**Inline SQL Editor**](#inline-sql-editor): use a mini SQL Editor anywhere in the Dashboard

- [**Multiple AI Assistant chats**](#multiple-ai-assistant-chats): maintain history of your Assistant chats

- [**Logs improvements**](#logs-improvements): more detailed inspect panel, and stacked charts

### Tabs!

This has been a common request for a long time, and should make working with data much easier. We've added Tabs to our two most-used tools: the Table Editor and the SQL Editor.

### Tabs in the Table Editor

In the Table Editor, you can open multiple tables at a time and easily switch between them using tabs. This makes it easier to compare data, edit schemas, or reference related tables without losing your place. Enabled under Feature Previews ([see below](#enabling-feature-previews))

<Img
  wide
  alt="Table Editor tabs"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/table-editor-tabs.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/table-editor-tabs-light.jpg',
  }}
/>

Tabs work the same as in VS Code, opening in preview mode. This is useful if you're quickly browsing files and don't want every visited file to have its own tab. A new tab will only be dedicated to that file when you start editing or simply click into it. Preview mode is indicated by _italics_ in the tab heading.

The New Tab page also gives you quick access to create a new table or open a recently visited one.

<Img
  wide
  alt="Creating a new tab"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/new-tab.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/new-tab-light.jpg',
  }}
/>

### Tabs in the SQL Editor

In the SQL Editor, you can now write and run multiple scripts at a time, without having to constantly change between snippets. The SQL Editor tabs also have preview mode, so you can quickly flip through snippets without leaving a bunch of tabs to clean up after. Enabled under Feature Previews ([see below](#enabling-feature-previews))

<Img
  wide
  alt="SQL Editor tabs"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/sql-editor-tabs.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/sql-editor-tabs-light.jpg',
  }}
/>

Multiple tabs will make it easier to work across datasets, debug, or compare different queries, all without losing your place.

## Customizable reports

Reports in the Dashboard recently got a refresh. You can now resize and reorder chart blocks, giving you full control over the layout. It's perfect for crafting reports that look exactly how you want.

<Img
  wide
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/customizable-reports.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/customizable-reports-light.jpg',
  }}
/>

## SQL blocks in custom reports

We've also added inline SQL execution within blocks, so you can run your own queries directly and build fully customized, data-driven reports. Just create a snippet in the SQL Editor and it will be available to use here.

<Img
  wide
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/custom-reports-sql.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/custom-reports-sql-light.jpg',
  }}
/>

You can show your data as a table of results:

<Img
  wide
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/custom-reports-sql-query.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/custom-reports-sql-query-light.jpg',
  }}
/>

Or display them as a chart:

<Img
  wide
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/refinements-completed.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/refinements-completed-light.jpg',
  }}
/>

The sky is the limit for these. You could query a Foreign Data Wrapper, join multiple tables, create a View to highlight key information, and much more.

## Inline SQL Editor

You can now run SQL from anywhere in the Dashboard via the Inline SQL Editor. You can query and modify tables, add triggers, functions, RLS policies, and anything else you can do from the main SQL Editor, anywhere in the Dashboard. Enabled under Feature Previews ([see below](#enabling-feature-previews)).

<Img
  wide
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/inline-sql-editor.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/inline-sql-editor-light.jpg',
  }}
/>

## Multiple AI Assistant chats

The AI Assistant now lets you create and store multiple chats. Create, rename, switch to and delete chats, all without losing your place. Chats are scoped to the current project, so switching your project also switches chat history. The chat history is stored in local storage.

<Img
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/chat-history.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/chat-history-light.jpg',
  }}
/>

## Logs improvements

We've updated the [log detail panel](https://github.com/supabase/supabase/pull/33536) to show more info in the API log detail:

<Img
  wide
  alt="New Logs detail panel"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/log-detail-panel.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/log-detail-panel-light.jpg',
  }}
/>

You can also quickly add a property or value from the detail panel to search and filter the results:

<Img
  alt="Logs property search"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/logs-search.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/logs-search-light.jpg',
  }}
/>

And we've added http status to available filters to help you narrow in on specific logs while debugging:

<Img
  alt="Customizable reports"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/http-filters.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/http-filters-light.jpg',
  }}
/>

## Enabling Feature Previews

Tabs and the Inline SQL Editor can be enabled via Feature Preview. Click your user avatar in the bottom right and click Feature previews.

<Img
  alt="Click your user avatar"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/avatar.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/avatar-light.jpg',
  }}
/>

<Img
  alt="Enable features via Preview"
  src={{
    dark: '/images/blog/lw14-tabs-dashboard-updates/feature-preview.jpg',
    light: '/images/blog/lw14-tabs-dashboard-updates/feature-preview-light.jpg',
  }}
/>

## Get started

You can see all these improvements in the Supabase Dashboard now.

- [Get started with Supabase](https://supabase.com/dashboard/sign-in?returnTo=%2Fprojects) and try it out today
