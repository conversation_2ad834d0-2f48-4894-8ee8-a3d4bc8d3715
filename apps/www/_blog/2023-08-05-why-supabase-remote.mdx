---
title: "Why we'll stay remote"
description: Offices are making a comeback, just not at Supabase.
author: ant_wilson
image: remote/og.jpg
thumb: remote/thumb.jpg
launchweek: '8'
categories:
  - company
tags:
  - launch-week
date: '2023-08-05'
toc_depth: 2
---

There’s been a major U-turn in people’s attitudes towards remote this year. In 2020-2021 we had the ‘Remote Revelation’ where everyone and their dog realized they can do their job perfectly well from the comfort of their own home (oftentimes in the company of said dog).

Now in 2023, there’s been whiplash back to the office with larger companies fighting especially hard to convince their employees to return to their desks.

Supabase started in January 2020, and the plan was always to be fully remote. In some ways COVID gave us a competitive advantage in that we could offer a stable and supportive remote working environment to those whose employers were struggling to adapt. Even now as companies fight for people to go back to the office, we’re attracting a lot of talented people who moved out of major cities and would rather hop somewhere supportive rather than relocate or commute.

There are good arguments on both sides. And while you might point out that it’s probably too impractical for Supabase to switch to in-person now anyway (we’re already in >25 countries), the argument I’m making here is that if we were starting again tomorrow, we would do it all the same.

## No Commute

During my first job out of college I spent an average of 2 hours every day driving to the office and back. This was valuable thinking time, it allowed me to rediscover the entire Weezer back catalog. Being in the days before (good) podcasts it gave me time to listen to the Today programme on Radio 4 and catch up on what was happening in the world. Despite all this however, it struck me as one of the most clearly insane things we force humans to do on a daily basis - spend an average of 20% of their waking hours driving or on public transport. Many people report that the first 2 hours after waking are their most productive; are we really asking them to spend those precious hours commuting? It does make some sense from the companies perspective, because the company doesn’t pay for this time, nor do they usually directly pay for the cost of the commute (which can often take a hefty chunk out of your wage). In exchange they get the benefit of extra productivity from the portion of the population who require a lot of face time in order to get their job done. While I miss listening to Pinkerton on repeat, it turned out there are other ways to get back my thinking/music/podcast time and that is going for a long walk in the woods, or commuting to the gym.

## Requiring a lot of face time to get your job done

There’s a large portion of the population who fit this description, and without casting any negative judgement, we have to be open and honest with candidates that if they’re in this camp - they’ll likely be unhappy working at Supabase. In the early days we thought that these people could adapt, or perhaps be able to satisfy their need for face to face interactions outside of work, but on the whole it wasn’t the case. We actively try to reduce the number of meetings happening across the company (both in person and via video call) which increases friction with these folks. By not supporting this group, we’re reducing the size of the population from which we can hire, but overall our hiring pool is still very much larger than the average in-person company. Thanks to the likes of Rippling, Remote, and Deel, we can extend our talent search globally - and it turns out there are a hell of a lot of _extremely_ talented and highly motivated people living outside of New York, London, and San Francisco (don’t worry we take folks from these cities as well!). After all, the goal of hiring should be to source the absolute best person for the job. Statistically speaking it’s unlikely that this person lives within an hour drive to your office.

## More take home pay

For the major US cities, we pay somewhere in the 60-80th percentile range, but for almost everywhere else we _easily_ pay in the 95th percentile (in many cases in the 99th percentile) because we don’t do geo-adjusted pay. If you want to live off the beaten path, where the air is clean, and the taxes are low - then that’s entirely up to you. We won’t lower your pay and you’ll end up with much more cash in your pocket at the end of the month. We’re strongly of the opinion that you should get the same pay for the same job, geo-adjusted pay always seemed a bit bizarre to me. It’s undeniable that major cities have a higher concentration of technical talent, but these people are taxed accordingly. If not on their income, then most certainly on their rent and other expenses.

## Won’t somebody think of the young people

An argument I often hear for being in the office, is that young people joining the workforce can’t learn the ways of the working world as quickly when working remotely because they don’t get as much exposure to senior members of the team. This may be true. In my experience, young people seem much better adapted to working remotely than people who have been in the workforce for several decades, however it doesn’t solve for the more experienced folks being visible and available for the juniors to learn from. At Supabase, we very rarely hire juniors. That’s not to say we don’t hire young people, since there are plenty of highly skilled young people who can out-ship people twice their age. But in terms of team make-up, our team is very highly skewed towards seniors and leads. So we don’t really suffer much from this issue (if it exists).

## Global Coverage

Supabase now has team members in more than 25 countries. One benefit of this as an infrastructure provider is that we get 24 hour support coverage for free. If your database is showing signs of trouble at 3am, you don’t need to worry because we have team members on the clock and actively responding to system alerts. It also means that the shipping quite literally never stops.

When interviewing candidates a common question we get is around working hours, and the honest answer is that we don’t care when you work. With the number of different timezones we operate across it would be impossible for us to track anyway. Some people prefer to start their day at 5am, and others prefer to work late into the night. That’s fine by us.

Most of the people we hire have a background in open source and contribute to large projects like PostgreSQL where most things run asynchronously. They are well adapted to working on complex designs independently. It seems that this style of open source is becoming more prevalent thanks in part to the meteoric success of GitHub. As a consequence, the pool of talent is undoubtedly growing.

Another huge benefit of hiring from anywhere is that you end up with an incredibly diverse team. This helps when selling to a global audience of developers, and ensures diversity of thought and a broad range of perspectives.

## Won’t somebody think of the commercial real-estate investors

A huge and obvious advantage of not having an office… is not having to pay for an office. Dedicated offices can get pretty expensive, so I’m not surprised large companies resent sitting on acres of unused office space. But fear not, here at Supabase we’ve uncovered an ingenious solution to keeping team members happy and at the same time supporting our local real estate developers. We give everyone the option of a desk at a local co-working space. In cities where we have many employees they tend to opt for the WeWork floating desk. They can also access WeWork locations globally when they travel (other co-working spaces are available). This adds some operating costs, but it’s far more affordable than a dedicated space, and a lot of people also choose not to take us up on the offer and prefer to work from their kitted out spaces at home, a local coffee shop, or a Brazilian beach resort.

## The Human Connection

When you save so much on office space it extends the budget for whole company off-sites (on-sites?). At the end of the day we’re all human, and we know the importance of getting to know your team mates on a human level. That’s why once per year we get everyone together for 11 days. This year we went to Bali.

On top of whole company trips, folks often self organize co-working trips to places like Malaysia, San Francisco, or Austin, Texas which helps with getting to know folks outside of your immediate team.

## Productivity

After all is said and done, companies will still ask “but where will my team be most productive?”. To be honest I think the Supabase team would be just as productive in an office as they would be from home, but would we have been able to hire these same folks without a remote working policy? Absolutely not.

A lot of people’s hesitations around remote seem to stem from not having trust in their team to get things done without adult supervision. However, if I know anything about high performing teams, it’s that you need to have mutual trust (which breeds psychological safety), and a shared model of what success looks like (metrics anyone?). Without these you’re going to struggle to produce results office or no office.

At Supabase we do what works for Supabase, and for many companies being in a dedicated office or hybrid will undoubtably be what’s best for them.

The book that shaped my thinking on remote more than any other was Spolsky’s ‘Smart and Gets Things Done’ (I don’t even know if it’s in print any more?), but the summary is this:

Hire people who are:

- Smart
- Get Things Done

Everything else is a Nice to Have. Including proximity.

Lastly, if you hate wasting time commuting and want to work with some of the best engineers in the world, [we’re hiring](https://supabase.com/careers).

Read more:

- [Who we hire at Supabase](https://supabase.com/blog/who-we-hire)
- [Supabase Careers](https://supabase.com/careers)
- [See what's new in Supabase](https://supabase.com/launch-week)
