---
title: 'Launch Week 5 Hackathon Winners'
description: Announcing the winners of the Launch Week 5 Hackathon!
author: tyler_shukert
image: lw5-hackathon-winners/launch-week-5-hackathon-winners.jpeg
thumb: lw5-hackathon-winners/launch-week-5-hackathon-winners.jpeg
categories:
  - developers
tags:
  - hackathon
  - community
date: '2022-08-25'
toc_depth: 2
---

During [Launch Week 5](https://supabase.com/launch-week), we shipped a lot of features, which was exciting, but what is more exciting is reviewing all the awesome hackathon submissions you all created for [Launch Week 5 Hackathon!](https://supabase.com/blog/launch-week-5-hackathon)

It is always tough to pick the winner, but this time around it was extra hard because of the amount of incredible projects: there were 46 submitted projects, making this our most popular hackathon yet. You will be able to view all of the submissions of this hackathon on [madewithsupabase.com](https://www.madewithsupabase.com/).

And now, without further ado, congratulations to the winners of Supabase Launch Week 5 Hackathon!

## Best Overall Project

### Winner

[Supabase Cache Helpers](https://github.com/psteinroe/supabase-cache-helpers) - by [@psteinroe](https://twitter.com/psteinroe).

Supabase Cache Helpers is a caching utility library that works by turning your Supabase query into cache keys and provides methods for retrieving and mutating data on your Supabase database.

![Supabase Cache Helpers](/images/blog/lw5-hackathon-winners/supabase-cache-helpers.png)

### Runner Up

[Super UI](https://github.com/pheralb/superui) - by [Pablo Hdez](https://twitter.com/pheralb_), [Nacho Aldama](https://twitter.com/srdrabx), [David Huertas](https://twitter.com/ikurotime) and [Juan Rojas](https://twitter.com/tmchein)

Super UI is a React UI component library for React, but it also lets you customize and create your own components from your browser.

![Super UI](/images/blog/lw5-hackathon-winners/super-ui.png)

## Most Visually Pleasing

### Winner

[Hop - A Tinder-like job board app](https://github.com/taishikato/hop) - by [@taishikato](https://twitter.com/taishik_)

Hop is a Tinder-like job board app that you can hit the button or arrow keys to decide to pass or add it to your favourite job posting list.

![Hop - A Tinder-like job board app](/images/blog/lw5-hackathon-winners/hop.png)

### Runner Up

[Vesta](https://www.github.com/n4ze3m/vesta) - by [@n4ze3m](https://twitter.com/n4ze3m)

Vesta is an open source Google Keep alternative that helps you to save notes and links without any hassle.

![vesta](/images/blog/lw5-hackathon-winners/vesta.png)

## Most Technically Impressive

### Winner

[Otentik Authenticator](https://github.com/riipandi/otentik-authenticator-desktop) - by [@riipandi](https://twitter.com/riipandi)

Otentik is a security desktop app to manage your 2-step verification tokens when logging into your online services so that you can complete your login flow within your computer.

![Otentik Authenticator](/images/blog/lw5-hackathon-winners/otentik.png)

### Runner Up

[Stripe Sync](https://github.com/lawrencecchen/stripe-sync) - by [@lawrencecchen](https://github.com/lawrencecchen)

Stripe Sync allows you to automatically sync your stripe data into your Supabase database using Edge Functions.

![Stripe Sync](/images/blog/lw5-hackathon-winners/stripe-sync.png)

## Most Fun/Interesting

### Winner

[That Hot Dog Game](https://github.com/laznic/hotdogs) - by [@laznic](https://twitter.com/laznic)

Play a classic hot dog eating contest with your friends! That Hot Dog Game uses facial recognition to detect “eating” and whoever can eat the most hot dogs in a given time wins!

![That Hot Dog Game](/images/blog/lw5-hackathon-winners/hot-dog-game.png)

### Runner Up

[Quizgrad (Quiz App)](https://github.com/mehulsatardekar/Quizgrad) - by [Mehul Satardekar](https://twitter.com/MehulSatardekar)

Quizgrad is a fun quiz app where you can compete against other players on quizzes from different categories.

![Quizgrad (Quiz App)](/images/blog/lw5-hackathon-winners/quiz-grad.png)

## Best Flutter Project

### Winner

[Quid Faciam Hodie?](https://github.com/Myzel394/quid_faciam_hodie) - by [@Myzel394](https://github.com/Myzel394)

Quid Faciam Hodie? (in English: “What did I do today?”) is a video based journaling app that allows you to save moments of your day and later relive them again!

![Quid Faciam Hodie?](/images/blog/lw5-hackathon-winners/quid-faciam-hodie.png)

### Runner Up

[Potato](https://github.com/Rohithgilla12/potato) - by [Rohith Gilla](https://github.com/Rohithgilla12)

A productivity manager application where you can leave a note right from your desktop. Designed to sync across your devices and supports macOS!

![Potato](/images/blog/lw5-hackathon-winners/potato.png)

## Best Realtime Project

### Winner

[RepoWatch](https://github.com/vvidday/repo-watch) - by [@vvidday](https://github.com/vvidday)

With RepoWatch, you can view any updates on across all the repositories in the Supabase GitHub org in realtime.

![RepoWatch](/images/blog/lw5-hackathon-winners/repo-watch.png)

### Runner Up

[Cowofi](https://github.com/Cowofi/cowofi) - by [@itsalb3rt](https://twitter.com/alhidalgodev), [@chakrihacker](https://twitter.com/chakrihacker), [@rafieltq](https://twitter.com/rafiel_tq) and [@pcabreram1234](https://twitter.com/pcabreram1234)

CoWoFi is an application that helps managers, executives and the general public who need to rent an ideal space for their work team.

![Cowofi](/images/blog/lw5-hackathon-winners/cowofi.png)

## The Prizes

As promised in the [hackathon announcement article](https://supabase.com/blog/launch-week-5-hackathon), each members of the winners will receive a limited edition golden Supabase cap!

![Golden Supabase Cap](/images/blog/lw5-hackathon/cap.png)

And the runner uppers will receive the same cap but in silver. These caps are limited edition and [extremely valuable](https://supabase.store/products/gold-supacap), so keep them safe! Again, congratulations on all the winners!

## Hang out with us and the winners of the Hackathon

To celebrate the massive success of the Hackathon, we are hosting a Twitter Space with and inviting the winners and everyone who participated. Join us on Tuesday 30 August at 7:00 AM PST

[Set a reminder](https://twitter.com/i/spaces/1LyxBoXywLjKN)

And if you want to be part of the community and build with us, find other people using Supabase, or if you just want to chill and watch people build, come and join us in Discord!

![Join Supabase Discord Channel](/images/blog/hackathon/community.png)

**[Join our Discord](https://discord.supabase.com/)**

### Get Started Guides

If you're inspired to build, check out some of the latest resources:

- [**supabase-js v2 Release Candidate**](https://supabase.com/blog/supabase-js-v2)
- [**Supabase Realtime Guide**](https://supabase.com/docs/guides/realtime)
- [**Flutter Quick Start Guide**](https://supabase.com/docs/guides/with-flutter)
- [**Supabase CLI local development guide**](https://supabase.com/docs/guides/local-development)
- [**Edge Functions Guide**](https://supabase.com/docs/guides/functions)
