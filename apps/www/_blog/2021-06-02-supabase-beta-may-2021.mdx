---
title: Supabase Beta May 2021
description: Apple &amp; Twitter Logins, Supabase Grid, Go &amp; Swift Libraries.
author: paul_copplestone
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: 2021-may/release-may-2021.jpg
thumb: 2021-may/release-may-2021.jpg
categories:
  - product
tags:
  - release-notes
date: '2021-06-02'
video: https://www.youtube.com/v/qETcl3SUfzU
---

Apple & Twitter Logins, Supabase Grid, Go & Swift Libraries. Lots of great stuff to try out this month.

### Quick demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/qETcl3SUfzU"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Apple logins are now available

Did you know: if you ship an app to the App Store with any third-party logins, you're required to enable Apple logins as well? Now you can with Supabase Auth.

![Apple OAuth is now available in Supabase](/images/blog/2021-may/supabase-apple-login.png)

## Twitter logins are now available

You can also use Twitter as an OAuth provider with Supabase Auth. Twitter has a very archaic OAuth implementation, so this one took awhile.

![Twitter OAuth is now available in Supabase](/images/blog/2021-may/supabase-twitter-login.png)

## New storage policy editor

We shipped a new Policy Editor for managing Row Level Security on your Storage. We provide some templates to simplify the process for new developers.

![New Policy Editor](/images/blog/2021-may/storage-policies.png)

## Supabase Grid

We are still working on Open Sourcing our Dashboard, and took another step closer by publicly releasing a new [Supabase Grid](https://github.com/supabase/grid). It's not ready to use outside of the Supabase ecosystem, but over time we hope to make it usable with any Postgres Database.

![This is an image of the new Supabase Grid](/images/blog/2021-may/table-grid.png)

## Japan (Tokyo) 🇯🇵 is now available as a region

There are a huge number of Supabase developers in Japan and China, and at their request we've launched Tokyo as a region.

![Tokyo is now availabel as a region](/images/blog/2021-may/japan-region.png)

## Return data as CSV

You can now [retrieve your data](/docs/reference/javascript/select#return-data-as-csv) as Comma Separated Values. Thanks to [@andreivreja](https://github.com/andreivreja) for the [awesome PR](https://github.com/supabase/postgrest-js/pull/187).

![Download data as a CSV](/images/blog/2021-may/return-data-as-csv.png)

## New Go Libraries

The community started developing the Go libraries. [postgrest-go](https://github.com/supabase/postgrest-go) is completed, [@Yusuf_Papurcu](https://twitter.com/Yusuf_Papurcu) and [@muratmirgun](https://twitter.com/muratmirgun) are working on the remaining libraries.

![Supabase Go Libraries](/images/blog/2021-may/supabase-go-support.png)

## New Swift Libraries

The community started developing the Swift libraries too. [@satishbabariya](https://twitter.com/satishbabariya) is making huge progress on
[`storage-swift`](https://github.com/supabase/storage-swift),
[`gotrue-swift`](https://github.com/supabase/gotrue-swift),
[`realtime-swift`](https://github.com/supabase/realtime-swift), and
[`supabase-swift`](https://github.com/supabase/supabase-swift).

![Supabase Swift Libraries](/images/blog/2021-may/supabase-swift-support.png)

## Build in Public

We started a weekly 1-hour live stream where we build in public.

- [Build in Public 001](https://www.youtube.com/watch?v=p561ogKZ63o): Building a "Next.js + Supabase" Tutorial
- [Build in Public 002](https://twitter.com/p_phiri/status/1397815806990372865?s=20): `maybeSingle()` and "React + Supabase" Tutorial

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/p561ogKZ63o"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Community

- `@p_phiri` made his first OSS contribution, and documented it on YouTube. [Twitter](https://twitter.com/p_phiri/status/1397815806990372865?s=20).
- `@sonnylazuardi` built an awesome 3d globe using Supabase. [Twitter](https://twitter.com/sonnylazuardi/status/1397141285664792578?s=20).
- `@yallurium` released Part 2 of "Going Full Stack with Flutter and Supabase". [Twitter](https://twitter.com/yallurium/status/1396850742724632582?s=20).
- `@coderinblack` built an Audio Social Platform using Supabase. [Twitter](https://twitter.com/coderinblack/status/1396199050207121408).
- `@adityathakurxd` built a Flutter + Supabase starter kit. [GitHub](https://github.com/adityathakurxd/supabase_flutter).
- `@dshukertjr` built a geo-tagged video sharing app with Flutter + Supabase. [GitHub](https://github.com/dshukertjr/spot)
- `@JonoYeong` created a 6-minute overview of Supabase. [YouTube](https://www.youtube.com/watch?v=1F240hR7nHU). [Twitter](https://twitter.com/JonoYeong/status/1398451556723294208).
- Everyone who has produced Supabase content is now receiving their [swag](https://twitter.com/coderinblack/status/1397042488586559490?s=20). Send a link to your blog, app, or <NAME_EMAIL> along with your address and we'll make sure you're included in the next drop.

**Supabase GitHub Star Growth**

![13223 stars on GitHub.](/images/blog/2021-may/supabase-stars-may-2021.png)

<small>
  Source: <a href="https://repository.surf/supabase">repository.surf/supabase</a>
</small>

If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).

## Coming Next

We're planning another Launch Week at the end of July. We'll probably have a quiet month of shipping this month so that we can get everything prepared for July.

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Subscribe to our [YouTube channel](https://www.youtube.com/c/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
