---
title: Announcing Data Preservation Service
description: Secure, reliable and timeless backups
author: long
image: 2024-04-01-pg-paper-dump/pg-paper-dump-thumb.png
thumb: 2024-04-01-pg-paper-dump/pg-paper-dump-thumb.png
categories:
  - company
tags:
  - announcements
date: '2024-04-01'
toc_depth: 3
---

We live in a world where data is as critical as air and water, powering everything from global enterprises to personal projects. At Supabase, we create products that are not just cutting-edge but also secure, reliable, and now, timeless.

Today, we are excited to introduce a groundbreaking service that bridges the digital divide with the most enduring medium known to humankind: paper. Meet [pg_paper_dump](https://github.com/supabase-community/pg_paper_dump), Supabase’s answer to the ultimate data preservation conundrum.

## Why Paper?

In the digital era, the threat landscape is constantly evolving, with new vulnerabilities emerging at a pace that's hard to keep up with. While digital backups are the norm, they are susceptible to cyber-attacks, hardware failures, and obsolescence.

This vulnerability led us to think outside the digital box and into a realm that is impervious to hacking, immune to electromagnetic pulses, and resistant to time itself: physical paper.

![Historical Paper Backup on Papyrus](/images/blog/2024-04-01-pg-paper-dump/historical-paper-backup.webp)

Historical data backup on Papyrus that inspired `pg_paper_dump`.

## How does `pg_paper_dump` work?

### Backup Process

1. **Select Your Data:** Choose the databases you wish to back up with `pg_paper_dump`.
2. **Encode and Print:** Our system encodes your data with Comic Sans and prints it onto durable paper.
3. **Secure Storage:** We store your paper backups in a location of your choice or utilize our secure Arctic Vault for maximum security.

### Restoration Process

1. **Select your recovery point:** Thanks to point-in-time recovery, we can restore your data up until any specific page.
2. **Restore:** Our Mechanical Turks are trained to decode data encoded in Comic Sans and will input your data with reasonable human-accuracy into your destination database.

### Scaling data preservation with containers

Our Analog Engineering team has developed a container orchestration protocol to coordinate the backup and restore process of large databases.

![Scaling analog data preservation with container orchetration](/images/blog/2024-04-01-pg-paper-dump/paper-container.jpeg)

## Who is pg_paper_dump for?

- Companies that use fax machines.
- Future SOC 4 compliant companies.
- [Vogons](https://en.m.wikipedia.org/wiki/Vogon).

## Features and Benefits

- **Security:** Provides a level of security that is fundamentally beyond the reach of digital vulnerabilities.
- **Eco-conscious:** Our materials are selected for minimal environmental impact without compromising on durability or legibility.
- **Simplicity of Restoration:** When the time comes, restoring your data is as simple as taking the printed codes and typing them back into Supabase’s SQL editor.

## Availability

[Sign up](https://forms.supabase.com/pg-paper-dump) for our open beta to get access to `pg_paper_dump` now or try it out our self-hosted solution.

As all of our products, we have open-sourced the code and made it available to the public to be used without any restrictions [here](https://github.com/supabase-community/pg_paper_dump).

## The Future is Here

We are offering a bridge between these worlds, providing a backup solution that stands the test of time. Join us in redefining data security and making history, one sheet at a time.
