---
title: Supabase Beta Pricing
description: Supabase launches Beta pricing structure
author: rory_wilding
image: pricing-og.jpg
thumb: launch-week-pricing.jpg
categories:
  - company
tags:
  - supabase
date: '03-29-2021'
---

## Pricing is hard

Many developers have been waiting for [pricing](/pricing) before building on Supabase. It has taken this long to announce pricing for one simple reason: pricing is hard. We've spent countless hours discussing and testing different models. We consulted users, OSS veterans, and SaaS Gurus. We explored and ruled out a number of options including: no Free Plan, pure usage based, pay-per-seat, pay-per-row, and "bolt-on" feature based pricing.

Supabase is a suite of integrated products - should we compare our Postgres databases with Amazon RDS? Do we benchmark our Auth pricing against Auth0 or Okta?

So we decided to do what we always do: ship it, share it, and then listen to our users' feedback.

## TLDR

We are launching [pricing](/pricing) with three plans:

1. Free - for up to four hobby-level projects
2. Pro - $25 per project per month, for more serious projects
3. Usage based - for those who need more resource than what's on the Pro Plan

## Goals for our pricing structure

Our goals for pricing are simple:

- Continue offering free Supabase projects for Students, Hobbyists, and Early Adopters
- Price based on predictable metrics (no shock bills at the end of the month)
- Enable and grow with our most successful users, providing a pricing model that supports their growth

We came up with a 3-plan model to offer predictability, and then iterated through numerous user interviews to answer the questions: "how much do we charge in each plan, and what features go in each plan?". To answer these questions we considered:

1. How much value does Supabase create compared to alternatives including Firebase, authentication providers like Okta and Auth0, API, and storage providers?
2. How much time does Supabase save developers?
3. How can we make Supabase as accessible as possible?
4. As developers ourselves, how do we feel about our proposed pricing?
5. The underlying cost of development and maintenance of Supabase.

As a result, we've arrived at a pricing model that brings predictability, is sustainable, and enables us to continue to offer a Free Plan.

Our model allows anybody to come in and experience the full power of the Supabase stack without first needing to put down a credit card.

## Launching with a Free Plan

Many of our early users are building or migrating their passion projects to Supabase. Offering a Free Plan enables this. We also support a large number of students learning SQL for the first time, and fledgling startups which haven't started earning any revenue yet.

Developers need time to build their side projects, and they don't want to pay for a database while they do it. At the same time, hosting Postgres databases is expensive. So we decided to take the same strategy that we always have: ship, share it, and iterate.

No company gets the pricing right first time. It would be arrogant to believe that we will. What we can promise you is this: as we learn and grow we plan to offer more value for less, not the other way around.

Our success depends on the success of developers using Supabase. In short, if our users' projects succeed then Supabase adoption will continue. That is exactly what we want.

We will issue credits for those who signed up during alpha, beta, and those who sign up through to the end of Launch Week[^1]. If you are not on board yet, [you can sign up here.](http://www.supabase.io) Projects on select incubators will also get credits, as founders we know the importance of credits when getting something new off the ground. We will continue to support code schools and their students. If you need Supabase credits to support your project, reach out to us here and we will do our best to help you.

## Making pricing predictable

We know that Firebase pricing can be [problematic](https://medium.com/madhash/how-not-to-get-a-30k-bill-from-firebase-37a6cb3abaca).

That's why we made our pricing as predictable as possible. You can quickly understand which pricing plan you fit into, without having to forecast how many API requests you're going to make this quarter.

What does this mean for you as a developer?

- You can make as many API calls as you need to (within the constraints of your database). We already have users making millions of requests per day.
- We have been deliberately generous with authentication and storage on the Free Plan so that you get as much of the Supabase experience as possible.
- We're starting with soft-limits for usage billing to prevent unexpected charges.
- We'll reach out to you before changing your billing plan to see if it makes sense for your project.
- On our "Pay as you go" plan we will work with you to forecast billing before making a commitment.

## Next steps

In the next few days, you will see your usage appear in the dashboard with an upgrade button.

Check back throughout the week to see what else we're launching [here](/blog/launch-week).

Sign up for the Supabase Beta [here](https://supabase.com/dashboard).

And check out the full pricing details [here](/pricing). And we'd love to hear your opnions on our Pricing! You can let us <NAME_EMAIL>.

[^1]: Updated on Aug 17 2021 for clarity: the referenced Launch Week ended on the 4th of April 2021. Users that signed up prior to that date can request credits.
