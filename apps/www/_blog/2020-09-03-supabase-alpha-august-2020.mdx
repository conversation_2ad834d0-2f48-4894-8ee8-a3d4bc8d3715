---
title: Supabase Alpha August 2020
description: Six months of building
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
categories:
  - product
tags:
  - supabase
date: '09-03-2020'
---

We're 6 months into building our hosted database platform and we've made some major improvements to our auth system and table view.

### Quick Demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.loom.com/embed/a43084629c7e47828e3a292b60719393"
    frameBorder="0"
    allowFullScreen
  ></iframe>
</div>

### Easily create tables

Set up tables and columns directly from the table view.

<video width="99%" autoPlay loop muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/new-tables.mp4"
    type="video/mp4"
  />
</video>

### Invite your team

You can now invite team members to your organisation.

<video width="99%" autoPlay loop muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/invite-team.mp4"
    type="video/mp4"
  />
</video>

### Auth: Email Confirmations

You can now enable Email Confirmations for new users. This can be toggled on or off and the template for this email can be edited via the dashboard.

<video width="99%" autoPlay loop muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/confirm-email.mp4"
    type="video/mp4"
  />
</video>

### TypeScript support

The biggest communty contribution to date, [@thorwebdev](https://twitter.com/thorwebdev) added TypeScript support to Supabase. He even [live streamed the process](https://twitter.com/thorwebdev/status/1292722189788016641).

![This git shows how TypeScript makes it even easier to use Supabase, through VSCode's intellisense.](/images/blog/typescript-support.gif)

### Kaizen

We have a number of small improvements:

- supabase-js now has [UMD support](https://github.com/supabase/supabase/pull/156)
- We significantly [improved our docs](/docs). Try out the new search!
- All of our awesome sponsors are now listed [on our OSS page](/open-source).

### From the community

The release of Auth last month accelerated our community growth, based on [Orbit Levels.](https://github.com/orbit-love/orbit-model)

![This image shows our community growth. In August we had about 250 active members across GitHub and Twitter.](/images/blog/community-august.png)

- We received our first enterprise sponsor. Thanks to [@briannekimmel](https://twitter.com/briannekimmel) from [@WorkLifeVC](https://twitter.com/WorkLifeVC) for becoming an [Enterprise Sponsor](https://github.com/sponsors/supabase).
- [@amorriscode](https://github.com/amorriscode) added Redwood support! [[Pull Request](https://github.com/redwoodjs/redwood/pull/1033)]
- [@swyx](https://twitter.com/swyx) trying out Supabase with Color Search Engine with Supabase [[Live Stream](https://twitter.com/swyx/status/1300538001508896768)]
- Python Support: [@lqmanh](https://github.com/lqmanh) has agreed to lead the python support. He's already deployed [postgrest-py](https://github.com/supabase/postgrest-py) which is the first step towards a functioning supabase-py client lib.
- [@phamhieu](https://github.com/phamhieu) built a [Live Map Tracker](https://github.com/phamhieu/supabase-realtime-map) App using Supabase + Leafletjs

### Coming next

Our focus now is to move from Alpha to Beta. This involves stabilising our Auth API, improving dashboard performance, and benchmarking all of our systems. This could take more than one month, as we have a number of company-related activities to wrap up ([we just finished YC](https://techcrunch.com/2020/08/25/here-are-the-94-companies-from-y-combinators-summer-2020-demo-day-2/)!).

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
