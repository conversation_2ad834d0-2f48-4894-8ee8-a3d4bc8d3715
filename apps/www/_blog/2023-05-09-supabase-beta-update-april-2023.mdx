---
title: Supabase Beta April 2023
description: A review of Launch Week 7 and more exciting updates from last month.
author: ant_wilson
image: 2023-05-09-beta-update-april/monthly-update-april-2023.jpg
thumb: 2023-05-09-beta-update-april/monthly-update-april-2023.jpg
categories:
  - product
tags:
  - release-notes
date: '2023-05-09'
toc_depth: 3
---

Brace yourself, this is one of the most feature-packed Beta Updates we've published so far.

[Launch Week 7](https://supabase.com/launch-week) was a massive success with great feedback from the community. We also gave away lots of mechanical keyboards, including the Hackathon winner which we announce here!

## Day 1 - Supabase Logs: open source logging server

![Day 1 - Supabase Logs: open source logging server](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day-1.jpg)

Logflare is the hub of analytics streams for Supabase. We are open sourcing it so that you can self-host your own Logging infrastructure.

[Blog Post](https://supabase.com/blog/supabase-logs-self-hosted)\
[Video overview](https://www.youtube.com/watch?v=Ai2BjHV36Ng)

## Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions

![Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day2.jpg)

You can now self-host Edge Functions and run them in local development using our new Edge Runtime. We published a guide showing how to self-host Edge Functions with Fly and what more is coming![⚡](https://fonts.gstatic.com/s/e/notoemoji/15.0/26a1/32.png)️

[Blog post](https://supabase.com/blog/edge-runtime-self-hosted-deno-functions)\
[Video overview](https://www.youtube.com/watch?v=cPGxPl1lx4Y)

## Day 3 - Storage v3: Resumable Uploads with support for 50GB files

![Day 3 - Storage v3: Resumable Uploads with support for 50GB files](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day3.jpg)

Supabase Storage received many of the most requested features from our users: Resumable Uploads, Quality Filters, Next.js support, and WebP support.

[Blog post](https://supabase.com/blog/storage-v3-resumable-uploads)\
[Video overview](https://www.youtube.com/watch?v=pT2PcZFq_M0)

## Day 4 - Supabase Auth: SSO, Mobile, and Server-side support

![Day 4 - Supabase Auth: SSO, Mobile, and Server-side support](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day4.jpg)

On day 4, we introduced SSO with SAML 2.0, PKCE, and Sign in with Apple for iOS. It felt like acronym day, but it was actually Auth day!

[Blog post](https://supabase.com/blog/supabase-auth-sso-pkce)\
[Video overview](https://www.youtube.com/watch?v=hAwJeR6mhB0)

## Day 5 - Supabase Studio 2.0 with new AI features

![Day 5 - Supabase Studio 2.0 with new AI features](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-studio.jpg)

Supabase Studio got a major upgrade that goes from redesigns to improved developer experience, and new tools. We have the features people have been asking for and new capabilities that will change the way you work.

[Blog Post](https://supabase.com/blog/supabase-studio-2.0)\
[Video overview](https://www.youtube.com/watch?v=0rcNqHt5KWU)

## Community highlights

![Community highlights](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-community.jpg)

Our community defines us. We're honored to work with, sponsor, and support incredible people and tools 💜. Our CEO wrote a highlight of the last 3 months.

[Blog post](https://supabase.com/blog/launch-week-7-community-highlights)

## Introducing dbdev: PostgreSQL Package Manager

![dbdev: PostgreSQL Package Manager](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-dbdev.jpg)

[database.dev](https://database.dev/) fills the same role for PostgreSQL as `npm` for JavaScript or `pip` for Python, it enables publishing libraries and applications for repeatable deployment. Our goal is to create an open ecosystem for packaging and discovering SQL.

[Blog post](https://supabase.com/blog/dbdev)

## More product announcements

- Trusted Language Extensions for Postgres. [[Blog post]](https://supabase.com/blog/pg-tle)
- What's New in pg_graphql v1.2. [[Blog post]](https://supabase.com/blog/whats-new-in-pg-graphql-v1-2)
- GitHub Discussions are now a new knowledge source for search & AI (Troubleshooting category only for now). [[Check it out]](https://supabase.com/docs)
- New API report with routing information for each chart, making it easier to debug API calls. [[PR]](https://github.com/supabase/supabase/pull/14063)
- Storage permission changes: the developer role is now allowed to update the storage settings (previously was only owner and admin). [[PR]](https://github.com/supabase/supabase/pull/13883)

## Launch Week 7 Hackathon winners

![Launch Week 7 Hackathon winners](/images/blog/2023-05-09-beta-update-april/newsletter-hackathon.jpg)

The community is loving `pgvector` to build AI apps so we decided to make it part of the traditional Launch Week Hackathon. The quality of the apps was out of this world, it wasn't easy, but in the end, we selected [Page Assist](https://github.com/n4ze3m/page-assist) - by [@n4ze3m ](https://twitter.com/n4ze3m)as the winner of the Best Overall Project.

[Full list of Winners](https://supabase.com/blog/launch-week-7-hackathon-winners)

[See all the submissions](https://www.madewithsupabase.com/launch-week-7)

## Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.

![Mendable logo](/images/blog/2023-05-09-beta-update-april/customer-stories-mendable.png)

With Supabase's pg_vector, Mendable.ai could build a more cost-effective solution that is just as performant - if not more performant - than other vector databases.

[Read the full story](https://supabase.com/customers/mendableai)

## From the community

![Community](/images/blog/2022-june/community.jpg)

- FlutterFlow now supports Supabase Authentication. [Video guide](https://www.youtube.com/watch?v=tL-sLPfWzVE)
- Supabase + ClickHouse: Combining the Best of the OLTP and OLAP Worlds. [Webinar](https://www.youtube.com/watch?v=LDWEsw41Zko)
- Our friend Guillaume put together the most incredible course about Supabase, with the in and outs of the platform. [Full course](https://www.youtube.com/watch?v=8DTOTT7q0XA)
- Supabase + LangChain starter template for building full stack AI apps. [Template](https://github.com/langchain-ai/langchain-template-supabase)
- Creating a Books Tracker App with .NET MAUI and Supabase. [Article](https://hackernoon.com/creating-a-books-tracker-app-with-net-maui-and-supabase)
  - Vanta Case Study - Supabase turns trust into a revenue-generating opportunity with Vanta. [Case Study](https://www.vanta.com/customers/supabase)

## Meme Zone

As always, one of our favorite memes from last month. [Follow us on Twitter](https://twitter.com/supabase) for more.

![Beta Update Meme](images/blog/2023-05-09-beta-update-april/beta-update-april-2023-meme.png)
