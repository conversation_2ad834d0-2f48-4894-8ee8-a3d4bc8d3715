---
title: Getting started with Ruby on Rails and Postgres on Supabase
description: 'Learn how to create a new Rails app and connect it to a Supabase Postgres database.'
author: thor_schaeff
image: getting-started/rails/rails.jpg
thumb: getting-started/rails/rails.jpg
categories:
  - engineering
tags:
  - postgres
  - developers
  - ruby
date: '2024-01-29'
toc_depth: 3
---

Every Supabase project comes with a full [Postgres](https://www.postgresql.org/) database, a free and open source database which is considered one of the world's most stable and advanced databases.

Postgres is an ideal choice for your Ruby on Rails applications as Rails ships with a built-in Postgres adapter!

In this post we'll start from scratch, creating a new Rails project, connecting it to our Supabase Postgres database, and interacting with the database using the Rails Console.

<Admonition type="note" label="Need help migrating?">

Supabase is one of the best [free alternatives to Heroku Postgres](/alternatives/supabase-vs-heroku-postgres). See [this guide](/docs/guides/resources/migrating-to-supabase/heroku) to learn how to migrate from Heroku to Supabase.

There's also a [Heroku to Supabase migration tool](https://migrate.supabase.com/) available to migrate in just a few clicks.

</Admonition>

If you prefer video guide, you can follow along below. And make sure to subscribe to the [Supabase YouTube channel](https://www.youtube.com/channel/UCNTVzV1InxHV-YR0fSajqPQ?view_as=subscriber&sub_confirmation=1)!

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/quMYT5Byxe4"
    title="YouTube video player"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  />
</div>

## Create a Rails Project

Make sure your Ruby and Rails versions are up to date, then use `rails new` to scaffold a new Rails project. Use the `-d=postgresql` flag to set it up for Postgres.

Go to the [Rails docs](https://guides.rubyonrails.org/getting_started.html) for more details.

```bash Terminal
rails new blog -d=postgresql
```

## Set up the Postgres connection details

Go to [database.new](https://database.new) and create a new Supabase project. Save your database password securely.

When your project is up and running, navigate to the [database settings](https://supabase.com/dashboard/project/_/settings/database) to find the URI connection string.

Rails ships with a Postgres adapter included, you can simply configure it via the environment variables. You can find the database URL in your [Supabase Dashboard](/dashboard/project/_/settings/database).

```bash Terminal
export DATABASE_URL=postgres://postgres.xxxx:<EMAIL>:5432/postgres
```

## Create and run a database migration

Rails includes Active Record as the ORM as well as database migration tooling which generates the SQL migration files for you.

Create an example `Article` model and generate the migration files.

```bash Terminal
bin/rails generate scaffold Article title:string body:text
bin/rails db:migrate
```

## Use the Model to interact with the database

You can use the included Rails console to interact with the database. For example, you can create new entries or list all entries in a Model's table.

```bash Terminal
bin/rails console
```

```rb irb
article = Article.new(title: "Hello Rails", body: "I am on Rails!")
article.save # Saves the entry to the database

Article.all
```

## Start the app

```bash Terminal
bin/rails server
```

Run the development server. Go to http://127.0.0.1:3000 in a browser to see your application running.

## Update the app to show articles

Currently the app shows a nice development splash screen, let's update this to show our articles from the database:

```rb config/routes.rb
Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  root "articles#index"
end
```

## Deploy to Fly.io

In order to start working with Fly.io, you will need `flyctl`, our CLI app for managing apps. If you've already installed it, carry on. If not, hop over to the [installation guide](https://fly.io/docs/hands-on/install-flyctl/). Once that's installed you'll want to [log in to Fly](https://fly.io/docs/getting-started/log-in-to-fly/).

### Provision Rails with Fly.io

To configure and launch the app, you can use `fly launch` and follow the wizard.

When asked "Do you want to tweak these settings before proceeding?" select `y` and set Postgres to `none` as we will be providing the Supabase database URL as a secret.

### Set the connection string as secret

Use the Fly.io CLI to set the Supabase database connection URI from above as a sevret which is exposed as an environment variable to the Rails app.

```bash Terminal
fly secrets set DATABASE_URL=$DATABASE_URL
```

### Deploy the app

Deploying your application is done with the following command:

```bash Terminal
fly deploy
```

This will take a few seconds as it uploads your application, builds a machine image, deploys the images, and then monitors to ensure it starts successfully. Once complete visit your app with the following command:

```bash Terminal
fly apps open
```

That's it! You're Rails app is up and running with Supabase Postgres and Fly.io!

## Conclusion

Supabase is the ideal platform for powering your Postgres database for your Ruby on Rails applications! Every Supabase project comes with a full Postgres database and a good number of [useful extensions](/docs/guides/database/extensions)!

Try it out now at [database.new](https://database.new)!

## More Supabase

- [Migration guides](/docs/guides/resources#migrate-to-supabase)
- [Options for connecting to your Postgres database](/docs/guides/database/connecting-to-postgres)
