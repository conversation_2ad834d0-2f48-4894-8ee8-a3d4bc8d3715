---
title: Supabase Beta Sept 2021
description: Hackathon, Aborting request, UI updates, and now Hiring.
author: paul_copplestone
author_url: https://github.com/kiwicopple
author_image_url: https://github.com/kiwicopple.png
image: 2021-sept/release-sept-2021.jpg
thumb: 2021-sept/release-sept-2021-cover.jpg
categories:
  - product
tags:
  - release-notes
date: '2021-10-04'
toc_depth: 3
video: https://www.youtube.com/v/5fsKMTeBKKY
---

Did you know it's been 2 years since the [first commit](https://github.com/supabase/realtime/commit/175f649784147af80acfc9ff5be9d160285c76ea) to Realtime, our real-time engine for Postgres? Before we even existed as a company!
We spent this month updating docs and content, improving UX, and [onboarding Developer Advocates](https://twitter.com/thorwebdev/status/1441041268411277322)!

## Hackathon v2

To kick off [Hacktoberfest](https://hacktoberfest.digitalocean.com/), another Supabase Hackathon is happening [right now](/blog/2021/09/28/supabase-hacktoberfest-hackathon-2021). You've got another 7 days to be in to win a limited edition t-shirt.

![Hackathon](/images/blog/2021-sept/hacktober.png)

## Abort Requests

We added support for [AbortController](https://developer.mozilla.org/en-US/docs/Web/API/AbortController) in our Javascript library so that you can abort long-running queries. [[Docs](/docs/reference/javascript/select#aborting-requests-in-flight)]

![Abort Requests](/images/blog/2021-sept/Supabase_abort_requests.png)

## Improved table management

We've made a number of changes to the Dashboard to expose some great features of PostgreSQL including:

### Column types

We've improved the column Type field so that it supports your [custom types](https://www.postgresql.org/docs/current/sql-createtype.html).

![Column types](/images/blog/2021-sept/columns.png)

### Is Unique

We've made it simple to add a unique constraint when creating or editing a table.

![Is Unique](/images/blog/2021-sept/is-unique.png)

### Edit columns

By popular request, you can now view all columns in a table at a glance and edit them in bulk.

![Edit columns](/images/blog/2021-sept/edit-columns.png)

## Cross-schema relationships

We updated our [grid](https://github.com/supabase/grid) to support relationships across multiple schemas.

![Cross-schema relationships](/images/blog/2021-sept/relationships.png)

## Improved Auth Docs

We've revamped the Auth docs - The docs are now broken down into [Authentication](/docs/guides/auth/social-login/auth-apple) and [Authorization](/docs/guides/auth/row-level-security), and organized alongside our [Deep Dive](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) series.

![Auth Docs](/images/blog/2021-sept/auth-docs.png)

## Low Code demo

Low Code demo, Using Supabase with [Clutch.io](http://clutch.io/) - [@\_dijonmusters](https://twitter.com/_dijonmusters) ran a [session at General Assembly](https://www.youtube.com/watch?t=642&v=5fsKMTeBKKY) showing how to use these two tools together to create apps using a low code approach.

<div className="video-container">
  <iframe
    className="video-with-border w-full"
    src="https://www.youtube-nocookie.com/embed/5fsKMTeBKKY"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

## Community

There was a lot of new content this month.

### Videos

- How I built the #10 product in 30 days - [Gary Tokman [video]](https://www.youtube.com/watch?v=CS1myTKJBR4)
- GitHub OAuth flow with Supabase + React - [Nader Dabit [video]](https://www.youtube.com/watch?v=jSqTzZk9UMg)
- The Best Stack for Web Developers - [Christopher Kapic [video]](https://www.youtube.com/watch?v=-2s_87QkPEI)
- Flutter + Supabase Course - [Abhishvek [video]](https://www.youtube.com/watch?v=PjVG6QtUYw4)
- React Native Mobile Auth - [Aaron Saunders [video]](https://www.youtube.com/watch?v=aBuB-Q6vHDE)
- Self Hosting Supabase - [Kelvin Pompey [video]](https://www.youtube.com/watch?v=HCqta43JHkU)
- Teta Flutter Frontend builder - [Teta Team [product]](https://www.youtube.com/watch?v=rooTglpUuvE)
- Magic Link + Route Controls in Next.js - [Nader Dabit [Video]](https://www.youtube.com/watch?v=oXWImFqsQF4)
- Supabase in 6 minutes - [Nader Dabit [video]](https://www.youtube.com/watch?v=ogEitL8RwtQ)

### Twitter

We hit 16.5k followers. [Follow us](https://twitter.com/supabase/status/1441428275176247302) there for advance frontend tips and 👁️⚡👁️

![Twitter](/images/blog/2021-sept/twitter.png)

### GitHub

Not far from 20K stars: [github.com/supabase/supabase](http://github.com/supabase/supabase)

![GitHub](/images/blog/2021-sept/stars.png)

Source: [repository.surf/supabase](https://repository.surf/supabase)

### Discord

Our Discord is growing fast. Come hangout with 3500+ developers building on Supabase today: [discord.supabase.com](http://discord.supabase.com)

![Discord](/images/blog/2021-sept/discord.png)

## Hiring

We're Hiring [SREs](https://about.supabase.com/careers/sre). We're fully remote and we love Open Source. [See open roles](https://about.supabase.com/careers).

![hiring](/images/blog/2021-sept/hiring.png)

## Coming Next

We're warming up for another Launch Week! Last time was "[Launch Week II: the SQL](/blog/supabase-launch-week-sql)". We're going to need another month to come up with a good pun again, so we'll aim for November.

## Get started

- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**
- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**
- Follow us **[on Twitter](https://twitter.com/supabase)**
- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**
- Become a **[sponsor](https://github.com/sponsors/supabase)**
