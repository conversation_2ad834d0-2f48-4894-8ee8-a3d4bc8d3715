---
title: 'Top 10 Launches of Launch Week 14'
description: Highlights from Launch Week 14
author: wenbo
image: launch-week-14/wrap-up/og.png
thumb: launch-week-14/wrap-up/thumb.png
launchweek: '14'
categories:
  - launch-week
tags:
  - launch-week
date: '2025-04-04T00:00:02'
toc_depth: 3
---

Here are the top 10 launches from the past week.

It was very hard to rank them, they’re all #1s in my book, so I may or may not have enlisted AI to rank them for me. Speaking of AI, make sure to check out #5 and #10.

## #1: Deploy Edge Functions from Dashboard

You can now create, test, edit, and deploy Edge Functions directly from the Supabase Dashboard without having to spin up the CLI and Docker. We’ve also included templates for common use cases like uploading files to Supabase Storage, OpenAI proxying, and Stripe WebHooks.

[Read more →](/blog/supabase-edge-functions-deploy-dashboard-deno-2-1)

## #2: Realtime Broadcast from Database Scales Database Changes

We’re extended Realtime Broadcast to enable sending messages from database triggers to give you better control over the database change payload while making sure that the workflow is secure and scalable.

[Read more →](/blog/realtime-broadcast-from-database)

## #3: Route Data API Requests to the Nearest Read Replica

You can now route your Data API (PostgREST) requests to the nearest Read Replica to minimize network latency. This is available today as the default for all load balancer endpoints.

[Read more →](/blog/data-api-nearest-read-replica)

## #4: Introducing the Supabase UI Library

Building in a weekend becomes much easier with our official UI Library - a collection of ready-to-use components built on top of [shadcn/ui](https://ui.shadcn.com) and integrated with Supabase products like Auth, Storage, and Realtime.

[Read more →](/blog/supabase-ui-library)

## #5: Official MCP Server

We’re launching an official MCP server so you can connect your favorite AI tools, like Claude and Cursor, with Supabase and perform tasks such as creating projects, fetching project configuration, querying data using SQL queries, and so much more.

[Read more →](/blog/mcp-server)

## #6: Declarative Schemas for Simpler Database Management

We’re simplifying database management with declarative schemas - version-controlled, source of truth for your database structure in the form of `.sql` files. This reduces errors, maintains consistency across environments, and increases development velocity.

[Read more →](/blog/declarative-schemas)

## #7: Bringing Clerk Auth to Supabase

We’ve been working with the Clerk team to make Clerk a Supabase Third-party Auth integration. This means that users can seamlessly connect Clerk and interact with Supabase and its Data API, Storage, Realtime and Edge Functions services without having to migrate auth providers.

[Read more →](/blog/clerk-tpa-pricing)

## #8: Postgres Language Server

Supabase contributors [psteinroe](https://x.com/psteinroe) and [juleswritescode](https://x.com/juleswritescode) have officially launched a Language Server Protocol (LSP) implementation for Postgres to make SQL tooling more reliable and easier to use. The initial release includes autocompletion, syntax error highlighting, type-checking, and a linter and it’s available today as a VSCode extension and npm package.

[Read more →](/blog/postgres-language-server)

## #9: Dedicated Poolers

We’re launching a Postgres connection pooler that’s co-located with your database via pgBouncer for better performance and reliability. This is only available on paid plans and gives you three options to connect to your database: direct connections, shared pooler via Supavisor, and dedicated pooler via pgBouncer.

[Read more →](/blog/dedicated-poolers)

## #10: We Invite AI Builders to Partner With Us

Supabase has become the default backend for AI Builders like Lovable, Bolt, and Tempo, and with good reason. We are easy to integrate with, we have all the primitives to build full-stack apps, and we can scale when those apps take off.

We invite more AI Builders to come and integrate with us so their users can build in a weekend and scale to millions.

[Read more →](/solutions/ai-builders)

## Hackathon Ends April 6

Make sure you get your submissions in for the hackathon by April 6 at 11:59 PM PT. You can read the announcement [here](https://x.com/supabase/status/1905603458742505516).
