---
title: 'Hackathon: Bring the Func(🕺)'
description: Build open-source projects with our latest features, win limited edition swag and plant a tree!
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: launch-week-4/friday-hackathon/hackathon-fun-og.png
thumb: launch-week-4/friday-hackathon/hackathon-fun-og.png
categories:
  - developers
tags:
  - hackathon
  - community
date: '2022-04-01T16:00:00'
toc_depth: 3
---

We can’t believe that it’s already a wrap on our [fourth launch week](https://supabase.com/blog/supabase-launch-week-four)! To keep the party and excitement going, and as [is now tradition](https://supabase.com/blog/community-day#launch-week-iv---what-to-expect), starting at 9am PT today we'll be hacking together for the next 10 days until 23:59pm PT on Sunday April 10th. And because we’ve launched [GraphQL support](https://supabase.com/blog/graphql-now-available) and [Supabase Edge Functions](https://supabase.com/blog/supabase-enterprise), these will be the major themes and therefore categories for the hackathon.

For some inspiration, check out [TheGuildDev](https://twitter.com/TheGuildDev)’s [GraphQL HackerNews clone](https://supabase-graphql-example.vercel.app/), and [Tyler’s](https://twitter.com/dshukertjr) [Flutter](https://github.com/supabase-community/flutter-stripe-payments-with-supabase-functions) and [Thor’s](https://twitter.com/thorwebdev) [React Native](https://github.com/supabase-community/expo-stripe-payments-with-supabase-functions) apps with Supabase Edge Functions, or have a browse through [madewithsupabase.com](http://madewithsupabase.com)!

As always, we'll have some extremely limited edition swag to give away that can only be won by participating in the Hackathon!

![hackathon_bring_the_func.png](/images/blog/launch-week-4/friday-hackathon/hackathon_bring_the_func.png)

We’re absolutely stoked to see what amazing things y’all will build and submit to [madewithsupabase.com](https://www.madewithsupabase.com/bring-the-func).

Finally, we're delighted to be partnering with [the Future Forest Company](https://thefutureforestcompany.com/) to plant a tree for every project that is being submitted for the Hackathon. Let’s build some cool open-source projects and start the SupaForest at the same time!

## Details

### Timeline

- **Friday April 1st at 08:30am PT:** Last day of Launch Week. Join us on [Twitter Spaces](https://twitter.com/i/spaces/1BdxYwWBzeDGX) for the kickoff announcement.
- Build your project during the next 10 days and hang out with the community [on Discord](https://discord.gg/bnncdqgBSS).
- **Sunday April 10th at 11:59pm PT:** Submission deadline
- Judges Deliberate (Monday)
- We'll be contacting and announcing the winners [on Twitter](https://twitter.com/supabase) throughout the week after.

<blockquote class="twitter-tweet" data-theme="dark">
  <p lang="en" dir="ltr">
    Hacktoberfest Hackathon Swag finally arrived! 🥇🥈🥨 see the winners here:
    <a href="https://t.co/wgRBu92Bq4">https://t.co/wgRBu92Bq4</a> <a href="https://t.co/i1eD1pdA8I">pic.twitter.com/i1eD1pdA8I</a>
  </p>
  &mdash; Supabase (@supabase) <a href="https://twitter.com/supabase/status/1463355998928736262?ref_src=twsrc%5Etfw">November 24, 2021</a>
</blockquote> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

### Prize categories

There are multiple chances to win, there will be prizes for:

- Most impressive GraphQL project (must use [pg_graphql](https://supabase.com/blog/graphql-now-available#getting-started))
- Most interesting usage of [Edge Functions]()
- Most fun / visually pleasing (get creative)

There will be a winner and a runner-up prize for each category. And for each project submitted we'll plant a tree and provide you with a certificate via [the future forest company](https://thefutureforestcompany.com/) to include in your project.

### Submission

Submit your project via [madewithsupabase.com/bring-the-func](https://www.madewithsupabase.com/bring-the-func)

You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:

- link to hosted demo (if applicable)
- list of team members github handles (and twitter if they have one)
- any demo videos, instructions, or memes
- a brief description of how you used Supabase:
  - to serve data via REST or GraphQL?
  - Realtime?
  - Auth?
  - Storage?
  - Functions on the edge?
- any other info you want the judges to know (motivations/ideas/process)
- _optional_ team photo
- _optional_ a meme

### Judging & announcement of winners

The Supabase team will excitedly review what you've built. They will be looking for a few things, including:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

We'll be contacting and announcing winners [on Twitter](https://twitter.com/supabase) throughout the week after submission closes.

<blockquote class="twitter-tweet" data-dnt="true" data-theme="dark">
  <p lang="en" dir="ltr">
    Absolutely buzzing to have won the
    <a href="https://twitter.com/supabase?ref_src=twsrc%5Etfw">@supabase</a> hackathon! 🥳 <a href="https://t.co/rm5HBuju73">https://t.co/rm5HBuju73</a>
  </p>
  &mdash; Josh Cawthorne (@cawthornejosh) <a href="https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw">August 10, 2021</a>
</blockquote> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

### Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be in multiple teams
- One submission per team
- All design elements, code, etc. for your project/feature must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.

### Community & help

Hang out with the Supabase team and community on Discord:

- Text channel: hackathon
- Audio channel: hackathon

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

Join our Discord: [discord.supabase.com](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Resources & Guides

Here's a collection of resources that will help you get started building with Supabase:

- Getting started with [GraphQL on Supabase](https://supabase.com/blog/graphql-now-available#getting-started)
- [GraphQL HackerNews clone](https://supabase-graphql-example.vercel.app/)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
- [Edge Functions Quickstart video](https://youtu.be/5OWH9c4u68M)
- [Supabase CLI local development guide](https://supabase.com/docs/guides/local-development)
- [Edge Functions examples](https://github.com/supabase/supabase/tree/master/examples/edge-functions)

### Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
