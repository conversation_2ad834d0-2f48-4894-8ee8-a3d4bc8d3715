---
title: 'Open Source Hackathon Winners'
description: Let the medal ceremony begin for the best projects submitted during the Supabase Hackathon.
author: ant_wilson
author_url: https://github.com/awalias
author_image_url: https://github.com/awalias.png
image: hackathon-winners/misc/winners-og.png
thumb: hackathon-winners/misc/winners-og.png
categories:
  - developers
tags:
  - hackathon
date: '2021-08-09'
toc_depth: 2
---

# The Medal Ceremony

We've reached the end of the first ever Supabase Hackathon and the entries have all been incredible. We received way more entrants than expected and therefore it took our judging panel a little longer than expected to get through their deliberations.

Thanks to everyone who joined in, and made this hackathon awesome. We're already looking forward to the next one. But for now, let's dish out some medals.

Don't forget that all the projects here are Open Source, so you can always dive into the code, PR, or fork projects you find interesting.

## Best Overall Project

### Winner

[Work From](https://github.com/joshcawthorne/work-from) - by [@cawthornejosh](https://twitter.com/cawthornejosh)

Josh's project allows employees to log which days and hours they'll be working and gives everyone on the team and within the company an overview of who is working when. The animations, UI/UX of this submission combined with the fact that it was built within a single week was absolutely mind-blowing!

[![Work From](/images/blog/hackathon-winners/projects/workday.png)](https://github.com/joshcawthorne/work-from)

### Runner Up

[Fireplace](https://github.com/0xDebabrata/fireplace) - by [@0xDebabrata](https://twitter.com/0xDebabrata), [@\_anishbasu](https://twitter.com/_anishbasu), [@DebMondalX](https://github.com/DebMondalX)

Fireplace makes great use of Supabase Storage for a shared video streaming experience, the killer feature here was the ability to share play, pause, and skip events across multiple users.

[![Fireplace](/images/blog/hackathon-winners/projects/fireplace.png)](https://github.com/0xDebabrata/fireplace)

## Most Visually Pleasing

### Winner

[Flutter Dictionary App](https://github.com/KathirvelChandrasekaran/dictionary_app) - by [@KathirvelChandrasekaran](https://github.com/KathirvelChandrasekaran)

The judges found the design of this Flutter app to be super impressive, the animations and color palette are especially top drawer!

[![Dictionary](/images/blog/hackathon-winners/projects/dictionary.png)](https://github.com/KathirvelChandrasekaran/dictionary_app)

### Runner Up

[Avatar Village](https://github.com/dshukertjr/avatar-village) - by [@dshukertjr](https://twitter.com/dshukertjr)

Tyler's app allows you to come together and chat with strangers in the browser, the feature we loved most was the randomized avatar generation (click the button in the top right to try it out)

[![tyler chat](/images/blog/hackathon-winners/projects/tyler.png)](https://github.com/dshukertjr/avatar-village)

## Most Technically Challenging

### Winner

[Feature.so](https://github.com/richiemcilroy/feature) - by [@richiemcilroy](https://twitter.com/richiemcilroy)

Boss work from Richie here - simply add a snippet to your app, then build and manage the components of your application from the Feature dashboard. An amazing example of where no-code is going.

[![Feature dot so](/images/blog/hackathon-winners/projects/featureso.png)](https://github.com/richiemcilroy/feature)

### Runner Up

[Flutter Add-ons](https://github.com/bdlukaa/supabase_addons) - by [@bdlukaa](https://twitter.com/bdlukaa)

Hook up your supabase credentials and Flutter app and Add-ons will start collecting metrics, analytics, and crashalytics, all in a neat dashboard with great visualization tools. Unbelievable that bdlukaa was able to build this in the space of a week.

[![Add Ons](/images/blog/hackathon-winners/projects/addons.png)](https://github.com/bdlukaa/supabase_addons)

## Most Fun/Interesting

### Winner

[Peckish Peach](https://github.com/Ngineer101/peckish-peach) - by [@nwbotha](https://twitter.com/nwbotha)

Add at 4 or more ingredients to your list and Peckish Peach will use ML to generate a full recipe. You can have some real fun adding random foods together and seeing what the algo comes up with. Bonus points for the adorable Peach gif!

[![Peach recipes](/images/blog/hackathon-winners/projects/peach1.png)](https://github.com/Ngineer101/peckish-peach)

### Runner Up

[Tamagotreal](https://github.com/mgilangjanuar/tamagotreal) - by [@mgilangjanuar](https://twitter.com/mgilangjanuar)

Instagram for Pets! The best bit is all the interactions inside the app are from the perspective of your pet. We were loving the Tamagotchi throwback!

[![tamogochi real insta](/images/blog/hackathon-winners/projects/tamog.png)](https://github.com/mgilangjanuar/tamagotreal)

## Best Meme

### Winner

This cheeky one from lautaro had us tickled.

[Bittermate Meme](https://github.com/imlautaro/bittermate#meme) - by [@mgilangjanuar](https://twitter.com/dev_lautaro)

### Runner Up

[Multiple Memes](https://github.com/rotimi-best/sveltedoc) - by [@rotimi-best](https://github.com/rotimi-best)

![meme runner up 2](/images/blog/hackathon-winners/misc/meme3.jpg) - by [@wycowley](https://github.com/wycowley), by [@SonavAgarwal](https://github.com/SonavAgarwal)

## The Prizes

All gold medal winners will be contacted and sent one of these limited edition gold Supabase tees (one per team member), and all runners up will receive the same design in silver. Prize winners should keep an eye on their emails so we can collect T-Shirt size and shipping details.

![Gold Tshirt](/images/blog/hackathon/tshirt.png)

### Other Notable Projects

Here's a list of Projects that made the shortlist or otherwise caught the judges eye:

- [comradery](https://github.com/carlomigueldy/comradery)
- [furniture-exchange](https://github.com/dhaiwat10/furniture-exchange)
- [notes-overflow](https://github.com/DharmarajX24/notes-overflow/)
- [accio](https://github.com/fabianferno/accio)
- [discussbase](https://github.com/hilmanski/discussbase)
- [supabubbabase](https://github.com/ivanq3w/supabubbabase)
- [angular-starters](https://github.com/jneterer/angular-starters)
- [newbaseql](https://github.com/jonas-kgomo/newbaseql)
- [hartup](https://github.com/Rahat-ch/hartup)
- [ambient](https://github.com/sambarrowclough/ambient)
- [stories_w_supabase](https://github.com/semihpolat/stories_w_supabase)
- [Tokenized](https://github.com/Senkottuvelan/Tokenized)
- [fttx app](https://github.com/fttx-gr/app)
- [secret-diary](https://github.com/zernonia/secret-diary)

### Participation Prize

We also decided that everyone else should receive some participation prize, so we will be in touch with all the participants via email to collect your shipping details.

If you want to join the community and build with us, find other people using Supabase, or if you just want to chill and watch people build, come and join us in Discord!

[Join our Discord](https://discord.supabase.com)

![Discord Hangout](/images/blog/hackathon/community.png)

### Get Started Guides

If you're inspired to build, here's a collection of resources that will help you get started building with Supabase:

- [Examples and Resources](/docs/guides/examples)
- [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]
- [Flutter Quickstart Guide](/docs/guides/with-flutter)
- [Nextjs Quickstart Guide](/docs/guides/with-nextjs)
- [Using Supabase inside Replit](/blog/2021/03/11/using-supabase-replit)
- [Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)
- [Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]
- [Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)
- [How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]
- [Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]
- [Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)
