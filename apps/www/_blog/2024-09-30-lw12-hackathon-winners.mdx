---
title: 'Supabase Launch Week 12 Hackathon Winners'
description: Announcing the winners of the Supabase Launch Week 12 Hackathon.
author: tyler_shukert
image: lw12/hackathon-winners/thumbnail.png
thumb: lw12/hackathon-winners/thumbnail.png
launchweek: '12'
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2024-09-30'
toc_depth: 2
---

The [Launch Week 12](https://supabase.com/launch-week) brought us a whole new set of Supabase features to build with. And to showcase what developers can build with Supabase, we announced the [Launch Week 12 hackathon](https://supabase.com/blog/supabase-lw12-hackathon) a while back, and it’s finally time to announce the winners!

The entire Supabase team had the pleasure of going through each amazing project, and we were so impressed with the quality of them. You can check out all of the submissions [here](https://www.hackathon.dev/lw12). Now, without a further ado, let’s take a look at the winners!

## Best overall project

### Winner

[Whisker Jam](https://github.com/c-o-l-i-n/whisker-jam) by [@n0t_buddy](https://twitter.com/n0t_buddy)

🐱🎸 Create a realtime virtual rock band made up of cats! Host the jam session on a TV, and join on your phone.

![whisker-jam](/images/blog/lw12/hackathon-winners/whisker-jam.jpg)

### Runner Up

[Spiceblow - Raycast Extension for Database Management](https://github.com/raycast/extensions/pull/14559) by [Tommy](https://twitter.com/__morse)

Database management directly inside Raycast. Search, update, delete and insert in your Supabase database from Raycast.

![Spiceblow](/images/blog/lw12/hackathon-winners/Spiceblow.jpg)

## Best use of AI

### Winner

[SupExplain](https://github.com/rbkayz/supexplain) by [Bharat](https://twitter.com/rbkayz)

An AI Postgres Query Plan Explainer that helps visualize & optimize your queries with AI.

![supexplain](/images/blog/lw12/hackathon-winners/supexplain.jpg)

### Runner Up

[Breaking Bad](https://github.com/CodyVal/breaking-bad) by [Val](https://twitter.com/valentinprgnd), and [Cody](https://twitter.com/codyrathor)

Small tool to track npm packages and ask questions about them.

![breaking-bad.jpg](/images/blog/lw12/hackathon-winners/breaking-bad.jpg)

## Most fun / best easter egg

### Winner

[Minecraft Server](https://github.com/Steellgold/minecraft-lw12) by [Gaëtan Huszovits](https://twitter.com/Steellgold), and [Romain San Vicente](https://twitter.com/RomainSav_)

A Laser Game server on Minecraft (bedrock edition) and a real-time statistics site with Supabase

![minecraft-server](/images/blog/lw12/hackathon-winners/minecraft-server.jpg)

### Runner Up

[Orora](https://github.com/alex-streza/rora) by [Alex](https://twitter.com/alex_streza), and [Catalina](https://twitter.com/Catalina_Melnic)

Orora is a northern lights platform that's almost as beautiful as the aurora itself. Real-time stats & map visualization

![orora](/images/blog/lw12/hackathon-winners/orora.jpg)

## Most technically impressive

### Winner

[supagen (Supabase Generator)](https://github.com/supagen/supagen) by [Andika](https://twitter.com/andikadeveloper), [Ezza](https://twitter.com/Ralkarr), and [David](https://github.com/davidsarono)

A CLI tool to automate manual effort/repetitive things when using Supabase.

![supagen](/images/blog/lw12/hackathon-winners/supagen.jpg)

### Runner Up

[Github RAG](https://github.com/XamHans/github-rag) by [Johannes Hayer](https://twitter.com/Hayer_Hans)

This project allows you to chat with your starred GitHub repositories to easily find the repos you need. It utilizes RAG

![github-rag](/images/blog/lw12/hackathon-winners/github-rag.jpg)

## Most visually pleasing

### Winner

[Today I Learned](https://github.com/Gautamp11/today-i-learned) by [Gautam](https://twitter.com/gautam1133p1)

A fun and knowledgeable app made with react for sharing facts backed by Supabase.

![today-i-learned](/images/blog/lw12/hackathon-winners/today-i-learned.jpg)

### Runner Up

[Aura](https://github.com/Arjun544/aura) by [Arjun Mahar](https://twitter.com/Arjun_Mahar1)

Aura is an AI mood-tracking application built with Flutter & Supabase. It helps users monitor their emotional well-being

![aura](/images/blog/lw12/hackathon-winners/aura.jpg)

## The Prizes

![keyboard](/images/blog/lw12/hackathon-winners/keyboard.png)

The winner of the best overall project will receive a mechanical keyboard, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.

## Getting Started Guides

If you're inspired to build, check out some of the latest resources:

- [Quick Start Guides](https://supabase.com/docs/guides/getting-started)
- [AI & Vectors Guide](https://supabase.com/docs/guides/ai)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
