---
title: 'Launch Week 8 Community Highlights'
description: Highlights from the community for the past 4 months.
launchweek: '8'
categories:
  - launch-week
tags:
  - launch-week
date: '2023-08-11'
toc_depth: 3
author: paul_copplestone
image: launch-week-8/day-5-community/community-highlights-OG.jpg
thumb: launch-week-8/day-5-community/community-highlights-thumbs.jpg
---

Supabase aims to be as collaborative as possible - working with, sponsoring, and supporting as many open source tools as possible. In many ways, we're more like a "community of communities". Here are a few highlights from the past 4 months.

## State of the community

We've seen a number of milestones this Launch Week. We passed 50,000 stars on GitHub ([and getting close to 55k!](https://github.com/supabase/supabase)), putting Supabase in the top 160 most-popular repositories. We reached 80k followers on [𝕏witter](https://twitter.com/supabase), 17k [Discord](https://discord.supabase.com) members, and 14k [YouTube](https://youtube.com/supabase) subscribers. We are exploring new platforms (thanks to [<PERSON>'s](https://twitter.com/yuricodesbot) arrival) - [Instagram](https://www.instagram.com/supabasecom/), [TikTok](https://www.tiktok.com/@supabase.com), and [Threads](https://www.threads.net/@supabasecom).

To our incredible community, thank you. Here are a few ways that you've contributed to the growth:

### Stack Overflow Survey

![Stack Overflow Survey](/images/blog/launch-week-8/day-5-community/stack-overflow-survey.png)

This was the first year that Supabase appeared on the Stack Overflow survey for [Databases](https://survey.stackoverflow.co/2023/#most-popular-technologies-database) as the 17th “most used”. As a bonus, Postgres took the #1 spot for the first time, and we hope we can continue to contribute to that trend.

## InfraRed 100 and Times Square

![Supabase in Times Square](https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fmarketing-emails%2Ftimes-sq.png%3Ft%3D2023-08-02T11%253A04%253A14.458Z&w=3840&q=75)

We were featured in Redpoint's [InfraRed 100](https://www.redpoint.com/infrared/100/), a report that recognizes 100 transformative companies in cloud infrastructure. We are honored to be included there with so many amazing companies we admire. We even made an appearance in Times Square.

[Read the full report](https://www.redpoint.com/infrared/100/)

### Community Meetups

![Community Meetups](/images/blog/launch-week-8/day-5-community/launch-week-8-meetups.png)

Supabase started as a remote company, and almost everything we've done so far has been digital. This Launch Week included in-person meetups throughout the world, 100% organized by the community. We had meetups in 5 countries, with hundreds of attendees across the world. It has been a successful experiment, something we'll consider for next time. Thanks to Fatuma, Isheanesu, Daniel, Philip, and Thor for organizing!

### New Kotlin Library

![New Kotlin Library](/images/blog/launch-week-8/day-5-community/kotlin-lib.jpg)

Are you a mobile dev? We now have client libraries for Android. This is thanks to one of our community members - [@TheRealJanGER](https://twitter.com/TheRealJanGER). Check out the [Tutorial](/blog/native-mobile-auth) and [Docs](/docs/reference/kotlin/introduction), and feel free to contribute any changes that you need to build with Kotlin.

### Contributors

There are so many humans who have made meaningful contributions to the Supabase ecosystem since our last Launch Week:

- We've continued growing our [community maintainers](https://github.com/orgs/supabase-community/teams), with a particular focus on Mobile. We now have docs for [Swift](/docs/reference/swift/introduction) and [Kotlin](/docs/reference/kotlin/introduction).
- [kamilogorek](https://github.com/kamilogorek) and [adiologydev](https://github.com/adiologydev) for integrating Schema Visualizer into the Studio. And another shoutout to the legend, [Zernonia](https://twitter.com/zernonia), for providing the inspiration.
- As always, a big shoutout to [Gary](https://github.com/GaryAustin1) and [Olyno](https://github.com/olyno) - official moderators of our [Discord](https://discord.supabase.com/) and overall incredibly helpful people. The community would not be the same without them.

## Ecosystem partners

The past few months we had a focus on integrations and welcomed a lot of new partners.

### Integrations Marketplace

We've been running our [Integrations Marketplace](/partners) in “stealth mode” for about a year now and has now grown to [over 60 integrations](/partners/integrations). In the last couple of months, we've added amazing products like [N8N](/partners/integrations/n8n), [Passage by 1 Password](/partners/integrations/passageidentity), and [Refine](/partners/integrations/refine_dev). This Launch Week we've made it easy to [build an integration](/docs/guides/platform/oauth-apps/build-a-supabase-integration#create-an-oauth-app) using OAuth2 and the Management API in the new [Supabase Integrations Marketplace](/blog/supabase-integrations-marketplace). We've started with a few partners to help us build and test the OAuth functionality, including [Cloudflare](/partners/integrations/cloudflare-workers), [Resend](/partners/integrations/resend), [Snaplet](/partners/integrations/snaplet), [Trigger.dev](/partners/integrations/triggerdotdev), [Windmill](/partners/integrations/windmill), and [Vercel](/partners/integrations/vercel).

### pgvector

[pgvector](github.com/pgvector/pgvector/) adds vector capabilities to Postgres. We continue to work with the pgvector creator, [Andrew Kane](https://twitter.com/andrewkane), and others contributing to the project. In the past few months, we've added robust benchmarks to show how to [scale your ivfflat workloads](/blog/pgvector-performance) and established guidance on the [best models](/blog/fewer-dimensions-are-better-pgvector) to use with pgvectors. With v0.5.0 coming soon, we're extremely excited about the improved HNSW index. Performance and recall improvement are looking [significantly better](https://jkatz05.com/post/postgres/pgvector-hnsw-performance/) than the current ivfflat index in pgvector.

### PostgREST

Last month we rolled out support across the platform [PostgREST 11.1](/blog/postgrest-11-1-release). A huge shoutout to [Steve Chavez](https://twitter.com/_steve_chavez) for his continued support as the primary maintainer of the project. [PostgREST 11.2](https://github.com/PostgREST/postgrest/releases/tag/v11.2.0) was released yesterday, adding [Domain Representations](https://postgrest.org/en/v11.2/references/api/domain_representations.html), and we'll roll it out to platform soon.

### LlamaIndex

In one of the most community-driven contributions ever, a tweet from [us](https://twitter.com/kiwicopple/status/1664377669935349760?s=20), and a tweet from [LlamaIndex](https://twitter.com/jerryjliu0/status/1664413942045904896), led to a contribution from [a16z](https://twitter.com/stuffyokodraws/status/1664532449815302145?s=20). And a few days later we had a [Supabase Vector Store implementation in LlamaIndex](https://gpt-index.readthedocs.io/en/stable/examples/vector_stores/SupabaseVectorIndexDemo.html).

### Transloadit

The team at Transloadit are committed to building an open protocol for resumable uploads, to make it the standard for file uploads on the internet. We used two of their [tus-node-server](https://github.com/tus/tus-node-server/) for our recent [updates to Storage](/blog/storage-v3-resumable-uploads) and contributed back a few important changes as a result. [Check out their post about our collaboration](https://transloadit.com/blog/2023/08/casestudy-supabase/).

### Mozilla

We are huge fans of Mozilla at Supabase, so it was an honor when they chose [Supabase Vector](/vector) to build AI Help, a ChatGPT-like interface to the most popular developer docs on the internet: **[MDN](https://developer.mozilla.org/en-US/)**. [Learn more in their official announcement](https://developer.mozilla.org/en-US/blog/introducing-ai-help/).

## Framework support

Supabase is easier to use in your favorite frameworks.

### Next.js Supabase Starter template

We added support for Next.js 13's App router, including Cookie-based Auth; styled authentication forms with Tailwind CSS; examples for Client Components, server components, route handlers, server actions; and - of course - 100% TypeScript. Get started with one simple command:

```bash
npx create-next-app -e with-supabase
```

### Nuxt Supabase module

The team at Nuxt [added support](https://twitter.com/_larbish/status/1687044609522778112) for the PKCE Auth Flow in their [Supabase x Nuxt module](https://supabase.nuxtjs.org/), making it even more secure. This module is a simple wrapper around [supabase-js](https://github.com/supabase/supabase-js) to enable usage and integration within Nuxt.

### Tamagui Takeout

The Tamagui team launched [TakeOut](https://tamagui.dev/takeout), a web and mobile starter that takes featuring everything you need to launch - including a nice backend, powered by Supabase.

### PowerSync

The team at PowerSync just dropped [an (extremely attractive) integration](https://docs.powersync.co/integration-guides/supabase-+-powersync) for building offline-first Flutter apps. This uses Postgres' built-in Publication functionality to track changes, which are then synced to an offline SQLite database.

## Courses

### Modern Full Stack course

Probably the most comprehensive Supabase resource on the internet, the team at Modern Full Stack just released a 7 chapter, 21 lesson [course on Supabase](https://modernfullstack.com/course/mfs401). The course includes:

1. **Supabase Foundations**
2. **Supabase Authentication**
3. **A Masterclass on Supabase Databases**
4. **Exploring the Supabase Storage API**
5. **Supabase Realtime**
6. **Unleashing Supabase's Edge Functions**
7. **Achieving Mastery of Supabase**

[Get started with Modern Full Stack.](https://modernfullstack.com/course/mfs401)

### EggHead: Build a 𝕏witter clone course

Our very own (and very talented) [Jon Meyers](https://twitter.com/jonmeyers_io) has released a new EggHead course that steps you through the process of creating a [Twitter clone with Next.js App Router and Supabase](https://egghead.io/courses/build-a-twitter-clone-with-the-next-js-app-router-and-supabase-19bebadb). You'll learn about:

1. Cookie-based Auth
2. Generating Typescript definitions from your PostgreSQL schema
3. Tailwind CSS and styling
4. Optimistic UI updates
5. Subscribing to realtime database changes
6. Configuring Supabase Auth to use cookies
7. Using Row Level Security (RLS) policies to implement Authorization

[Get started on EggHead.](https://egghead.io/courses/build-a-twitter-clone-with-the-next-js-app-router-and-supabase-19bebadb)

## Bonus track: Supabase Album

And finally, I promised Sam (CEO of Planetscale) that we'd [release an album](https://twitter.com/kiwicopple/status/1664118998169014273) before the end of the year, otherwise I'd give him 1% of my equity.

![The bet between Sam, Planetscale's CEO, and Copple](/images/blog/launch-week-8/day-5-community/supabase-album.png)

As promised Sam, here is the [Official Supabase Album](https://www.youtube.com/watch?v=dQw4w9WgXcQ)
