---
title: 'Epsilon3 Self-Host Supabase To Revolutionize Space Operations '
description: Learn how the team at Epsilon3 use Supabase to help teams execute secure and reliable operations in an industry that project spend runs into the billions.
author: rory_wilding
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: epsilon3/og-epsilon3.jpg
thumb: epsilon3/cover-epsilon3.jpg
categories:
  - company
tags:
  - supabase
date: '2021-07-26'
toc_depth: 2
---

Epsilon3 is a USA-based Y Combinator startup. They digitize paper-based procedures in the space industry using telemetry data,
simplifying testing and operations. Their product is rapidly becoming the OS for space projects and complex operations.

Learn how the team at Epsilon3 uses Supabase to help teams execute secure and reliable operations in an industry where project
spend runs into the billions.

### Space Operations

Epsilon3's customers run testing and operational procedures in real-time. Epsilon3's solution allows online collaboration as a team,
instead of being siloed and separated which is an increasingly important requirement in a post-pandemic working world.
Epsilon3 is powering an industry where reliability is paramount. In the space industry, missions cost billions and need to run reliably.

![Epsilon3 digitizes paper-based processes.](/images/blog/epsilon3/epsilon3-product.gif)

Epsilon3's software saves operators time and reduces errors to improve complex operations. Their customers are using their software and
successfully sending satellites into space and are about to use Epsilon3's software for an operational Rocket Launch. As a result, their
reputation for improving the quality standard for complex operations has led to demand from other industries. The team is receiving
enquiries from clients in the robotics, logistics and medical equipment sectors who need a structured and standardized way of running
procedures and keeping audit trails.

## Moving fast in regulated markets

As a startup, Epsilon3 needs to move fast and scale. They require security, reliability, and performance.

The team has a background in engineering from Northrop, Google, and SpaceX and understands the technical benefits Postgres offers as a
battle-tested open-source relational database.

Real-time functionality is also a key for their customers to see how running procedures are progressing as the changes occur.

The space industry is highly regulated, adding complexity. For example, in the USA, deployments must be in an ITAR-compliant way
(for instance AWS GovCloud supports ITAR compliance) while customers in countries outside the USA may need to host their data within their own
country. These customer requirements mean Epsilon3 must be able to offer flexible hosting options, including on-premises deployment.

<Quote
  img="aaron-epsilon3.png"
  caption="Aaron Sullivan, ex-Google engineer, Stanford University Alumni, Principal Software Engineer Epsilon3."
>
  <p>Billion dollar missions need to run reliably and securely.</p>
  <p>
    We didn’t just want something that works, we wanted a solution implementing best practices. We
    also needed a setup that we could self-host to meet compliance needs and allow on-premises
    deployment options for our customers.
  </p>
  <p>
    We use Supabase because they give us an open-source scalable back-end built by database experts
    that we can self-host.
  </p>
  <p>
    Supabase takes out the mental effort from our back-end infrastructure so we can focus on our
    customers needs.
  </p>
</Quote>

### Supabase gives Postgres users Superpowers

The team implemented a self-hosted Supabase setup. They use AWS with a self-hosted Supabase API powering an RDS Postgres instance.
This setup gives a balance of rapid development, reduced DevOps, and flexible hosting options. As Supabase uniquely offers real-time for
Postgres, it allows their customers to see updates from running procedures. Supabase offers well-supported client libraries that make
Postgres Row Level Security easy to use, which is a critical item on the Epsilon3 roadmap.

All this was easy for the team to set up, even with self-hosting, so the team was able to focus on what they do best&mdash;helping their customers
run complex operations smoothly. With this implementation, Epsilon3 gets all the benefits of Postgres, plus the smooth developer experience and
support that Supabase is known for, without compromising their compliance needs.

![Epsilon3 uses Supabase for hosting their critical infrastructure.](/images/blog/epsilon3/supabase-epsilon3-architecture.png)

### Self-hosted Supabase, a superpower

Self-hosting Supabase allows the crew at Epsilon3 to move fast. They are revolutionizing space operations securely and reliably in real-time whilst
staying compliant. The team is now seeing demand from other industries and is confident in their scaling capability because they know it's
just Postgres under the hood.

Check out the [Epsilon3 website](https://www.epsilon3.io/) to learn more about the team and their OS for space operations. If you are interested
in self-hosting Supabase then you can **contact us** to learn more.
