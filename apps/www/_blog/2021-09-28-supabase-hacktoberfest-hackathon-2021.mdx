---
title: 'Supabase Hacktoberfest Hackathon 2021'
description: We're running another Supabase Hackathon during Hacktoberfest!
author: thor_schaeff
author_url: https://github.com/thorwebdev
author_image_url: https://github.com/thorwebdev.png
image: hacktoberfest-hackathon/hacktoberfest_banner.png
thumb: hacktoberfest-hackathon/hacktoberfest_banner.png
categories:
  - developers
tags:
  - hacktoberfest
  - hackathon
date: '2021-09-28'
toc_depth: 3
---

# Supabase Hacktoberfest Hackathon 2021

We were absolutely blown away by [all the amazing projects](/blog/hackathon-winners) you built during our [first Supabase Hackathon](/blog/1-the-supabase-hackathon), that we were looking for an excuse to run our next virtual open-source hackathon, and we found it in the upcoming [Hacktoberfest](https://hacktoberfest.digitalocean.com/).

## Here's the plan

- On **Friday Oct 1st at 08:00am PT** we're kicking things off with our Hacktoberfest Discord Hangout. Join us in the #hackathon channel on our Discord server: [https://discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)
- Then you have 10 days to build a new **open-source** project with Supabase or contribute to one of our [supabase-community projects](https://github.com/supabase-community?q=topic%3AHacktoberfest&type=&language=&sort=) that have the `hacktoberfest` topic.
- It can be whatever you want - a project, mobile app, tool, library, anything
- Enter as an individual, or as a team of up to 5 people
- Submission deadline is **Sunday Oct 10th at 11:59pm PT**
- Besides [earning your Hacktoberfest shirt](https://hacktoberfest.digitalocean.com/resources/participation), you can win some extremely limited edition Supabase swag ✨ (see [here](https://twitter.com/supabase/status/1440737587895799809?s=21) what folks won last time!)

## Details

### Timeline

- **Friday Oct 1st at 08:00am PT:** Opening Ceremony in the #hackathon channel [on Discord](https://discord.gg/bnncdqgBSS).
- Build your project during the next 10 days and hang out with the community [on Discord](https://discord.gg/bnncdqgBSS).
- **Sunday Oct 10th at 11:59pm PT:** Submission deadline
- Judges Deliberate (Monday)
- We'll be contacting and announcing the winners [on Twitter](https://twitter.com/supabase) throughout the week after.

<blockquote class="twitter-tweet" data-dnt="true" data-theme="dark">
  <p lang="en" dir="ltr">
    Who else deserves the Gold Supabase Tee? 👀
    <a href="https://t.co/XPWw02kZad">https://t.co/XPWw02kZad</a>
  </p>
  &mdash; Supabase (@supabase) <a href="https://twitter.com/supabase/status/1440737587895799809?ref_src=twsrc%5Etfw">September 22, 2021</a>
</blockquote> <script async src="https://platform.twitter.com/widgets.js" charSet="utf-8"></script>

### Prize categories

There are 5 chances to win, there will be prizes for:

- Best Overall Project
- Most Visually Pleasing
- Most Technically Impressive
- Best mobile project (can user Flutter, React Native, Ionic, etc.)
- Most Spooky/Fun (Halloween is coming up!)

There will be a winner and a runner-up prize for each category.

### Submission

Submit your project via [madewithsupabase.com/hacktoberfest](https://www.madewithsupabase.com/hacktoberfest)

You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:

- link to hosted demo (if applicable)
- list of team members github handles (and twitter if they have one)
- any demo videos, instructions, or memes
- a brief description of how you used Supabase:
  - to store data?
  - realtime?
  - auth?
  - storage?
- any other info you want the judges to know (motivations/ideas/process)
- _optional_ team photo

### Judging & announcement of winners

The Supabase team will excitedly review what you've built. They will be looking for a few things, including:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
  - deep usage of a single feature or
  - broad usage are both ok
- FUN! 😃

We'll be contacting and announcing winners [on Twitter](https://twitter.com/supabase) throughout the week after submission closes.

<blockquote class="twitter-tweet" data-dnt="true" data-theme="dark">
  <p lang="en" dir="ltr">
    Absolutely buzzing to have won the
    <a href="https://twitter.com/supabase?ref_src=twsrc%5Etfw">@supabase</a> hackathon! 🥳 <a href="https://t.co/rm5HBuju73">https://t.co/rm5HBuju73</a>
  </p>
  &mdash; Josh Cawthorne (@cawthornejosh) <a href="https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw">August 10, 2021</a>
</blockquote> <script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

### Rules

- Team size 1-5 (all team members on winning teams will receive a prize)
- You cannot be in multiple teams
- One submission per team
- All design elements, code, etc. for your project/feature must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.

### Community & help

Hang out with the Supabase team and community on Discord:

- Text channel: hackathon
- Audio channel: hackathon

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

Join our Discord: [discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)

![Discord Hangout](/images/blog/hackathon/community.png)

### Resources & Guides

Here's a collection of resources that will help you get started building with Supabase:

- Need some inspiration? [See what folks built last time](/blog/hackathon-winners)!
- [Examples and Resources](/docs/guides/examples)
- [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]
- [Flutter Quickstart Guide](/docs/guides/with-flutter)
- [Nextjs Quickstart Guide](/docs/guides/with-nextjs)
- [Using Supabase inside Replit](/blog/using-supabase-replit)
- [Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)
- [Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]
- [Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)
- [How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]
- [Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]
- [Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)

### Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required
- By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
