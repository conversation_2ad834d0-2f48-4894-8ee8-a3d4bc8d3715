---
title: 'Supabase Launch Week 13 Hackathon Winners'
description: Announcing the winners of the Supabase Launch Week 13 Hackathon.
author: tyler_shukert
image: launch-week-13/hackathon-winners/lw13-hackathon.png
thumb: launch-week-13/hackathon-winners/lw13-hackathon.png
launchweek: '13'
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2024-12-20'
toc_depth: 2
---

[Launch Week 13](https://supabase.com/launch-week) brought an incredible array of new Supabase features that got developers' creative engines revving. To harness this excitement, we challenged our amazing community with the Launch Week 13 Hackathon - and wow, did they deliver!

The submissions were truly impressive, showcasing exceptional technical skill and creativity. Our team thoroughly enjoyed reviewing each project, making it challenging to select winners from such a strong pool of entries. You can browse through all the submissions [here](https://www.hackathon.dev/lw13). For this hackathon, we're excited to have collaborated with StackBlitz to offer special prizes for projects built with bolt.new! Now, let's meet the winners!

## Best overall project

### Winner

[Brainrot GPT](https://github.com/tfrank11/brainrot-gpt-web) by [Tim<PERSON>](https://twitter.com/timmyf111), and [Tiara](https://github.com/tjanuar)

Brainrot GPT uses AI to turn PDFs into short form video summaries. Perfect if you have a sub-30 second attention span.

![Brainrot GPT](/images/blog/launch-week-13/hackathon-winners/brainrot-gpt.png)

### Runner Up

[SupaSketch](https://github.com/swymbnsl/supasketch) by [Swayam bansal](https://twitter.com/swymbnsl), and [Anchal](https://twitter.com/AnchalGupta1117)

SupaSketch: Sketch and compete with friends in an AI and Supabase powered multiplayer drawing challenge!

![SupaSketch](/images/blog/launch-week-13/hackathon-winners/supasketch.png)

## Best use of AI

### Winner

[Stravation](https://github.com/aaronByrne1/Stravation) by [Aaron Byrne](https://github.com/aaronbyrne1)

AI-driven motivation, BLE heart rate tracking, & real-time connectivity to power your runs.

![Stravation](/images/blog/launch-week-13/hackathon-winners/stravation.png)

### Runner Up

[Kontra](https://github.com/Tammilore/ai-contract-analyzer) by [Tami](https://twitter.com/blacqee_)

AI-powered contract analysis tool that detects risky clauses and protects you from unfair legal agreements.

![Kontra](/images/blog/launch-week-13/hackathon-winners/kontra.png)

## Most fun / best easter egg

### Winner

[Gaze into the Abyss](https://github.com/laznic/gaze-into-the-abyss) by [Niklas Lepistö](https://twitter.com/laznic)

An experimental project where participants join virtual rooms and track each other's eye movements and blinks using

![Gaze into the Abyss](/images/blog/launch-week-13/hackathon-winners/gaze-into-the-abyss.png)

### Runner Up

[smart-style](https://github.com/JoseChipanaTica/smart-style) by [JoseChipana](https://twitter.com/josepaulct)

This project enables users to generate outfits based on their own clothing

![smart-style](/images/blog/launch-week-13/hackathon-winners/smart-style.png)

## Most visually pleasing

### Winner

[Munchwise](https://github.com/asrvd/munchwise) by [Ashish](https://twitter.com/_asheeshh)

Munchwise is an AI based calorie/meal tracking app, use natural language to track daily calorie & macro goals!

![Munchwise](/images/blog/launch-week-13/hackathon-winners/munchwise.png)

### Runner Up

[Students.fyi - We help people make better decisions](https://github.com/imprakashraghu/students.fyi) by [Prakash](https://twitter.com/imprakashraghu)

A consolidated view of an individual's career path where one could make decisions based on previous outcomes.

![Students.fyi](/images/blog/launch-week-13/hackathon-winners/students-fyi.png)

## Best project built with bolt.new

### Winner

[Screensaver](https://github.com/anishsrinivasan/screensaver) by [Anish Kumar Srinivasan](https://twitter.com/iamAnish), and [Zeeshan Rahman](https://github.com/zeeshan-ra)

A beautiful ambient video screensaver app featuring high-quality visuals, built with bolt.new and powered by Supabase.

![Screensaver](/images/blog/launch-week-13/hackathon-winners/screensaver.png)

### Runner Up

[Custom Sticker Designer](https://github.com/mmcspiritt/bolt-stickers) by [Matt McSpiritt](https://twitter.com/mmcspiritt)

An easy-to-use sticker designer made with Bolt, Supabase, Stripe, and Twilio Segment.

![Custom Sticker Designer](/images/blog/launch-week-13/hackathon-winners/custom-sticker-designer.png)

## The Prizes

![bolt.new hoodie](/images/blog/launch-week-13/hackathon/bolt-hoodie.jpeg)

Each winner and runner-up will receive a Supabase swag kit, and the winner and the runner-up of the best bolt.new project will receive a bolt.new hoodie.

## Getting Started Guides

If you're inspired to build, check out some of the latest resources:

- [Quick Start Guides](https://supabase.com/docs/guides/getting-started)
- [AI & Vectors Guide](https://supabase.com/docs/guides/ai)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
