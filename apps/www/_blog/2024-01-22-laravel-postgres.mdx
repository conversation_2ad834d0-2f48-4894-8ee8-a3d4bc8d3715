---
title: Getting started with <PERSON><PERSON> and <PERSON><PERSON><PERSON>
description: 'Learn how to create a new Laravel PHP app and connect it to a Supabase PostgreSQL database.'
author: thor_schaeff
image: getting-started/laravel/laravel.jpg
thumb: getting-started/laravel/laravel.jpg
categories:
  - engineering
tags:
  - postgres
  - developers
  - php
date: '2024-01-22'
toc_depth: 3
---

Every Supabase project comes with a full [Postgres](https://www.postgresql.org/) database, a free and open source database which is considered one of the world's most stable and advanced databases.

Postgres is an ideal choice for your Laravel PHP applications as Laravel ships with a Postgres adapter built in!

In this post we'll start from scratch, creating a new Laravel application, setting up the Laravel Breeze starter kit for user authentication, and connecting it to our Supabase Postgres database.

<Admonition type="note" label="Need help migrating?">

Supabase is one of the best [free alternatives to Heroku Postgres](/alternatives/supabase-vs-heroku-postgres). See [this guide](/docs/guides/resources/migrating-to-supabase/heroku) to learn how to migrate from Heroku to Supabase.

There's also a [Heroku to Supabase migration tool](https://migrate.supabase.com/) available to migrate in just a few clicks.

</Admonition>

If you prefer video guide, you can follow along below. And make sure to subscribe to the [Supabase YouTube channel](https://www.youtube.com/channel/UCNTVzV1InxHV-YR0fSajqPQ?view_as=subscriber&sub_confirmation=1)!

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/dg1suKt-7D4"
    title="YouTube video player"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  />
</div>

## Create a Laravel Project

Make sure your PHP and Composer versions are up to date, then use `composer create-project` to scaffold a new Laravel project.

See the [Laravel docs](https://laravel.com/docs/10.x/installation#creating-a-laravel-project) for more details.

```bash Terminal
composer create-project laravel/laravel example-app
```

## Install the Authentication template

Install [Laravel Breeze](https://laravel.com/docs/10.x/starter-kits#laravel-breeze), a simple implementation of all of Laravel's [authentication features](https://laravel.com/docs/10.x/authentication).

```bash Terminal
composer require laravel/breeze --dev
php artisan breeze:install
```

Note: this template does not use [Supabase Auth](/auth) but rather Laravel's built in Auth system. This means that [Supabase Auth pricing](/pricing) does not apply. You'd only be billed for Database resources used in this case.

## Set up the Postgres connection details

Go to [database.new](https://database.new) and create a new Supabase project. Save your database password securely.

When your project is up and running, navigate to the [database settings](/dashboard/project/_/settings/database) to find the URI connection string.

Laravel ships with a Postgres adapter out of the box, you can simply configure it via the environment variables. You can find the database URL in your [Supabase Dashboard](/dashboard/project/_/settings/database).

```bash .env
DB_CONNECTION=pgsql
DATABASE_URL=postgres://postgres.xxxx:<EMAIL>:5432/postgres
```

### Change the default schema

By default Laravel uses the `public` schema. We recommend changing this as supabase exposes the `public` schema as a [data API](/docs/guides/api).

You can change the schema of your Laravel application by modifying the `search_path` variable `config/database.php`:

```php config/database.php
'pgsql' => [
    'driver' => 'pgsql',
    'url' => env('DATABASE_URL'),
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '5432'),
    'database' => env('DB_DATABASE', 'forge'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'charset' => 'utf8',
    'prefix' => '',
    'prefix_indexes' => true,
    'search_path' => 'laravel',
    'sslmode' => 'prefer',
],
```

## Run the database migrations

Laravel ships with database migration files that set up the required tables for Laravel Authentication and User Management.

```bash Terminal
php artisan migrate
```

## Start the app

```bash Terminal
php artisan serve
```

Run the development server. Go to http://127.0.0.1:8000 in a browser to see your application. You can also navigate to http://127.0.0.1:8000/register and http://127.0.0.1:8000/login to register and log in users.

## Conclusion

Supabase is the ideal platform for powering your Postgres database for your Laravel applications! Every Supabase project comes with a full Postgres database and a good number of [useful extension](/docs/guides/database/extensions)!

Try it out now at [database.new](https://database.new)!

## More Supabase

- [Migration guides](/docs/guides/resources#migrate-to-supabase)
- [Options for connecting to your Postgres database](/docs/guides/database/connecting-to-postgres)
