---
title: 'Launch Week 7 Community Highlights'
description: We're honored to work with, sponsor, and support incredible people and tools. Here is a highlight of the last 3 months.
launchweek: '7'
categories:
  - launch-week
tags:
  - launch-week
date: '2023-04-14'
toc_depth: 3
author: paul_copplestone
image: launch-week-7/day-5-community-highlights/day-5-community-highlights-og.jpg
thumb: launch-week-7/day-5-community-highlights/day-5-community-highlights-thumb.jpg
---

Supabase is a collaborative company. We work with, sponsor, and support as many open source tools as possible. Here’s a few of this community highlights from the past 3 months.

## Ecosystem Partners

![Ecosystem Partners](/images/blog/launch-week-7/day-5-community-highlights/day-5-community-highlights-partners.png)

Our [Integrations Marketplace](/docs/guides/integrations) continues to grow. This past few months has brought a number of new partners and collaborations:

### AWS RDS

We’ve worked with the RDS team to build out the pg_tle extension and tooling and integrating it into Supabase. These changes are a stepping stone towards running the Supabase stack on AWS RDS. Special shoutout to [@jkatz](https://github.com/jkatz), who was an instrumental collaborator and the rest of the RDS team who have been helping to transition [PL/Rust](https://github.com/tcdi/plrust) into a Trusted Language.

[Read the press announcement on Amazon.com.](https://aws.amazon.com/blogs/opensource/supabase-makes-extensions-easier-for-developers-with-trusted-language-extensions-for-postgresql/)

[Read about pg_tle at Supabase and our collaboration with AWS.](https://www.notion.so/6f1bac83308846488ee7b01ae5c20200)

[Read about our experimental database registry, database.dev.](/blog/dbdev)

### Clickhouse

Along with our new Clickhouse [Foreign Data Wrapper](https://supabase.github.io/wrappers/clickhouse/), our friends at Clickhouse created guides showing the bidirectional capabilities between Supabase Postgres and Clickhouse Cloud. This is a perfect pairing when you’re consolidating your operational data with your analytical data, or if you have long-running queries that you need to offload to an analytical database like Clickhouse.

[Read the guide.](https://clickhouse.com/blog/migrating-data-between-clickhouse-postgres)

### LangChain

It’s always fun when two open source communities join forces. LangChain is an open source framework for developing applications powered by language models. This week, to support the Supabase AI Hackathon, they released a [Supabase x LangChain template](https://github.com/langchain-ai/langchain-template-supabase), one of the fastest ways to build AI applications. Their community recently made two contributions for Supabase: adding Supabase as a [Vector Store](https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase), and a neat [Hybrid Search](https://js.langchain.com/docs/modules/indexes/retrievers/supabase-hybrid) algorithm using Postgres’ Full Text Search. The also

[Read the integration docs.](https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/supabase)

[Clone the template.](https://github.com/langchain-ai/langchain-template-supabase)

### Vercel + OpenAI

Do we even need to introduce Vercel and OpenAI? We have a new template in the [Vercel Marketplace](https://vercel.com/templates/next.js/openai-doc-search-starter) for creating GPT-search for your MDX files. The templates takes all the `.mdx` files in the `pages`directory and processes them to use as custom context within [OpenAI Text Completion](https://platform.openai.com/docs/guides/completion) prompts.

[View the template.](https://vercel.com/templates/next.js/openai-doc-search-starter)

### Doppler

Doppler is a fully managed SecretOps platform that helps centralize, orchestrate, and rotate secrets in any environment. Your team can mitigate and remediate risks while using tools that fit natively into existing developer workflows.They just released an integration to automatically sync all of your team's secrets to Supabase.

[Check out the docs.](https://docs.doppler.com/docs/supabase)

### Redwood

The RedwoodJS team has contributed a new framework [Quick Start](/docs/guides/getting-started/quickstarts/redwoodjs) to our docs that will get you from zero to fullstack app with Prisma in just 2 minutes. Big shoutout to [@dthyresson](https://github.com/dthyresson) who continues to be a valuable contributor across many open source communities.

[Read the QuickStart.](/docs/guides/getting-started/quickstarts/redwoodjs)

### Dart Edge for Supabase Edge Functions

Dart Edge, built by [Invertase](https://invertase.io/), allows developers to write edge functions in Dart. Previously, Flutter developers had to use TypeScript for their edge functions, as it was the only supported language. However, with Dart Edge, Flutter developers can write backend code using their favorite programming language, Dart!

[Check out the guide.](/docs/guides/functions/dart-edge)

### T3 Turbo x Supabase

T3 Turbo is a type-safe, full-stack template to create Next.js and Expo React Native applications. Using our recent [Mobile Auth updates](/blog/supabase-auth-sso-pkce), the T3 community has helped build the [Supabase Create T3 Turbo template](https://github.com/supabase-community/create-t3-turbo), the fastest and easiest way to get started with Supabase and the T3 Stack. Huge shout out to [Julius](https://twitter.com/jullerino) who single-handedly created the template!

[Use the template.](https://github.com/supabase-community/create-t3-turbo)

## Self-hosting with Digital Ocean and Stackgres

![Self-hosting with Digital Ocean and Stackgres](/images/blog/launch-week-7/day-5-community-highlights/day-5-community-highlights-digitalocean-and-stackgres.png)

This Launch Week had a big focus on self-hosting, adding support for self-hosted [Logs](/blog/supabase-logs-self-hosted) and [Edge Functions](/blog/edge-runtime-self-hosted-deno-functions). Along with our official [self-hosting guides](/docs/guides/self-hosting) (and existing community support for [AWS](https://github.com/supabase-community/supabase-on-aws), [k8s](https://github.com/supabase-community/supabase-kubernetes), [Traefik](https://github.com/supabase-community/supabase-traefik), and [Terraform](https://github.com/supabase-community/supabase-terraform)), we have a couple of new entrants into the community:

- [Digital Ocean](https://github.com/digitalocean/supabase-on-do)
- [Stackgres](https://stackgres.io/blog/running-supabase-on-top-of-stackgres/)

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/dDhy6pk282U"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  ></iframe>
</div>

We’re looking for more self-hosting maintainers, especially for the self-hosted guides. Everything we do is MIT, Apache2, or PostgreSQL licensed, but our team is relatively small and we don’t have expertise for every deployment strategy that our community might want. If you are willing to help out on a regular basis please reach out!

## Postgres Extensions and improvements

![Postgres Extensions and improvements](/images/blog/launch-week-7/day-5-community-highlights/day-5-community-highlights-extensions.png)

The community continues to add and improve the extensions and configuration for our [Postgres image](https://github.com/supabase/postgres). Here are a few of the crowd favorites:

### pgvector: Open-source vector similarity search for Postgres

Just as it says on the label: `pgvector` enables vector functionality within Postgres. It’s been, by far, our most popular addition to date as it can be used to store OpenAI embeddings and serves as an alternative to Pinecone and Milvus.

[Read the announcement.](/blog/openai-embeddings-postgres-vector)

### **HypoPG: Hypothetical indexes**

HypoPG allows you to create hypothetical indexes that are visible to the PostgreSQL query
planner, with no resource cost (CPU, disk, memory). This allows users to “try out” an index without waiting for the server to build them. We have big plans for this extension - we’ll integrate this functionality into the dashboard with an Index Advisor to help you optimize slow-running queries.

[Read the docs.](/docs/guides/database/extensions/hypopg)

### pg_tle: Install your own extensions

We’ve been working with the RDS team to make Postgres _even more_ extensible. While you typically need to wait for Cloud Providers to add extension support to their Postgres offering, pg*tle is a step towards making this \_user managed*. We’ve even built a proof-of-concept registry (similar to NPM or Cargo) where you can upload extensions and share them with the community.

Link to blog post.

### PGroonga v3.0

PGroonga is an extension implementing full-text search in multiple languages. In v3, PGroonga becomes fully compatible with the Supabase _extensions_ schema deprecating the need to explicitly write a schema for operators. It also increases support for _text_ and _varchar_ array types. They just released this in time for Supabase Launch Week, so we’ll roll it out to the platform in the next month.

[Read the announcement.](https://groonga.org/en/blog/2023/04/13/pgroonga-3.0.0.html)

### LZ4 compression

PG has supported LZ4 page and row compression since v14, with WAL compression added in v15. LZ4 is faster than the standard pglz method. Supabase is now fully-compatible with this option. This is especially useful for TOAST values and WAL compression.

### ICU compatibility

Supabase Postgres has been configured with ICU support. This makes it possible to use ICU locales as collation providers. ICU supports many languages and makes the ordering of text type attributes according to these language rules.

## Client Libraries and utilities

![Client Libraries and utilities](/images/blog/launch-week-7/day-5-community-highlights/day-5-community-highlights-libraries.png)

It’s becoming easier to build applications with any language.

### Support for Swift, Python, and C#

In the past few months, the community has rallied and shipped 3 new client libraries, complete with Docs:

- Swift:
  - [Documentation](/docs/reference/swift/introduction) | [Source Code](https://github.com/supabase/supabase-swift)
  - Shout-out to: [grsouza](https://github.com/grsouza) & [@maail](https://github.com/maail)
- Python:
  - [Documentation](/docs/reference/python/initializing) | [Source Code](https://github.com/supabase-community/supabase-py)
  - Shout-out to: @
- C#
  - [Documentation](/docs/reference/csharp/introduction) | [Source Code](https://github.com/supabase-community/supabase-csharp)
  - Maintainers: [@acupofjose](https://github.com/acupofjose), [@veleek](https://github.com/veleek), and [@rhuanbarros](https://github.com/rhuanbarros)

These libraries are in various stages of readiness, so if you find anything that can be improved don’t hesitate to jump into the source code and help out.

### Supabase Cache Helpers

[Philipp](https://github.com/psteinroe) just released 1.0 of “Supabase Cache Helpers”, a collection of cache utilities for Supabase. With just a line of code, you can simplify the logic of **f**etching, subscribing to updates, and mutating data as well as storage objects in your project, and have all the amazing features of SWR or React Query. The cache helpers parse queries into a unique and definite query keys, automatically populating the query cache with every mutation using implicit knowledge of the schema.

[Check it out on GitHub.](https://github.com/psteinroe/supabase-cache-helpers)

[Read the docs.](https://supabase-cache-helpers.vercel.app/)

### SupaShim by Roboflow

The team at [Roboflow](https://roboflow.com/) just released [`supashim`](https://github.com/roboflow/supashim), which hooks into the Firebase calls from your app and translates them into the Supabase equivalents. With supashim, you can take an existing Firebase app, swap out the `firebase` global for `supashim`, then point it to a self-hosted Supabase instance without changing any application code. Pretty neat!

[Check it out on GitHub.](https://github.com/roboflow/supashim)

## Giving back

We’ve been upstreaming as many changes as possible for Supabase stack.

### Postgres Patches

We’ve submitted a number of patches to the Postgres Core:

- Added the “USER SET” parameter values for `pg_db_role_setting`. This GUC variable lets ordinary roles set placeholder variables, when permission requirements are not known yet [[Patch](https://www.postgresql.org/message-id/flat/20230102154240.GL1153%40telsasoft.com#c04239ecde38752a3132236306784250)]
- Added support for custom tuple slots in the custom executor nodes. This is useful for custom table access methods. [[Patch](https://www.postgresql.org/message-id/flat/CAPpHfduJUU6ToecvTyRE_yjxTS80FyPpct4OHaLFk3OEheMTNA%40mail.gmail.com)]
- Optimizations for LWlock waiting queue scalability. Long LWlock wait queues were not effective at unlocking. We’ve submitted proposals to make the queue lockless (still under community consideration). [Discussion [one](https://www.postgresql.org/message-id/flat/CALT9ZEEz%2B%3DNepc5eti6x531q64Z6%2BDxtP3h-h_8O5HDdtkJcPw%40mail.gmail.com), [two](https://www.postgresql.org/message-id/flat/20221120204310.xywrhyxyytsajuuq%40awork3.anarazel.de), [three](https://www.postgresql.org/message-id/flat/20221027165914.2hofzp4cvutj6gin%40awork3.anarazel.de)]

### OrioleDB Patches

We continue to support the development of [OrioleDB](https://github.com/orioledb/orioledb), who are building scalable storage mechanisms for Postgres using table access methods. The OrioleDB extension is now version [Alpha12](https://github.com/orioledb/orioledb/releases/tag/alpha12) and compatible with Postgres 15.

This release includes various improvements and fixes, including parallel sequential scan of OrioleDB relations. This allows multi-CPU systems to speed up select queries that involve sequential scans. It’s an analogue of heap parallel scan in Postgres core but for index-organized tables. With this base, we’re now implementing methods to build Orioledb indexes using many parallel workers.

[Read the release notes.](https://github.com/orioledb/orioledb/releases/tag/alpha12)

## Contributors

We also want to highlight all the humans who have made meaningful contributions to the Supabase ecosystem since our last Launch Week:

- [Gary](https://github.com/GaryAustin1) and [Olyno](https://github.com/olyno) - official moderators of our [Discord](https://discord.supabase.com) and overall incredibly helpful people. The community would not be the same without them
- [Bruno D'Luka](https://github.com/bdlukaa) - Caring for the details of Flutter SDK so that we can provide the best DX for Flutter devs
- [CodewithGuillaume](https://twitter.com/blackevilgoblin) - Creating a series of YouTube videos explaining different components of Supabase.
- [Daniel Mossaband](https://github.com/DanMossa) - Worked on fixing bugs and adding features to bring the mobile auth to Supabase
- [David Plugge](https://github.com/david-plugge) - Working on improving the [auth-helpers](https://github.com/supabase/auth-helpers) and fixing issues users report.
- [Jonathan Wilke](https://twitter.com/jonathan_wilke) - built [SupaStarter.dev](https://supastarter.dev/). A SaaS for building SaaSes.
- [grsouza](https://github.com/grsouza) & [Maail](https://github.com/maail) - Built the Swift libraries and documentation.
- [Raphaël Moreau](https://twitter.com/rphlmr) - Built the [supa-stripe-stack](https://github.com/rphlmr/supa-stripe-stack) for Remix to get started building your SaaS in one command. Also the creator the [supa-fly-stack](https://github.com/rphlmr/supa-fly-stack) and [supa-remix-stack](https://github.com/rphlmr/supa-remix-stack)
- [Vinzent](https://github.com/Vinzent03) - Constantly smashing it on the Flutter SDK adding major features such as MFA.
- [Tiniscule](https://twitter.com/tiniscule) - for his open source [Supabase Starter kit](https://github.com/usebasejump/basejump).

## Community

And you, our community. Thank you for using Supabase, reading our blog posts, and showing your support. With [over 48,000 GitHub stars](https://github.com/supabase/supabase), we are now in the top 180 most-popular repositories in all of GitHub. We still have a long way to go, but it’s a lot more fun doing it collaboratively.
