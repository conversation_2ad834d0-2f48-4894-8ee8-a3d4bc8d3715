---
title: Supabase Alpha September 2020
description: Seven months of building.
author: paul_copplestone
author_title: Supabase
author_url: https://github.com/kiwicopple
author_image_url: https://avatars2.githubusercontent.com/u/10214025?s=400&u=c6775be2ae667e2acae3ccd347fed62bb3f5b3e7&v=4
image: supabase-september-2020.png
categories:
  - product
tags:
  - supabase
date: '10-03-2020'
---

We're now 7 months into building Supabase. This update is a big one!

### Quick demo

Watch a full demo:

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.loom.com/embed/c7d66ae1f4c1458d964147c5c58aad59"
    frameBorder="0"
    allowFullScreen
  ></iframe>
</div>

### Third-party logins

We've released OAuth logins! You can now enable third-party logins on your app for Bitbucket, GitHub, GitLab, or Google.

![This is a picture of the supabase dashboard with OAuth logins](/images/blog/supabase-oauth-logins.png)

### Clone tables

You can duplicate your tables, just like you would inside a spreadsheet.

<video width="99%" autoPlay muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/duplicate-tables.mp4"
    type="video/mp4"
  />
</video>

### Enable and disable extensions

Extensions are easier to use. You can enable Postgres extensions with the click of a button.

<video width="99%" autoPlay muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/toggle-extensions.mp4"
    type="video/mp4"
  />
</video>

### Save your favorite queries

The SQL editor now stores your query history in your browser. You can also save your favorite queries to run later!

<video width="99%" autoPlay muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/favorites.mp4"
    type="video/mp4"
  />
</video>

### GitHub Discussions

Supabase was given access to [GitHub Discussions](https://github.com/supabase/supabase/discussions)! This is the place for you to ask questions or show off what you've built with Supabase.

![This is a screenshot of our GitHub Discussions, a new feature by GitHub.](/images/blog/supabase-github-discussions.png)

### Kaizen

- Our dashboard now uses [Next.js automatic static optimization](https://nextjs.org/docs/advanced-features/automatic-static-optimization) - so it should be noticeably more responsive.
- We created an Isomorphic [`gotrue-js`](https://github.com/supabase/gotrue-js/) TypeScript library for interacting with Netlify's [GoTrue server](https://github.com/netlify/gotrue). This will soon be bundled into `supabase-js`
- We migrated our [`postgrest-js`](https://github.com/supabase/postgrest-js/) library to TypeScript, and it will soon be bundled into `supabase-js`

### From the community

- @kiwicopple [appeared on GitHub's Twitch channel](https://www.twitch.tv/github/video/751281550) for Open Source Friday with @MishManners
- @kiwicopple [discussed pricing for Open Source](https://www.youtube.com/watch?v=PLhI6cccBQA) with GitLab CEO Sid Sijbrandij
- @Thorwebdev [presented Supabase Auth at the SingaporeJS](https://www.youtube.com/watch?v=LUMxJ4w-MUU) meetup
- [realtime-py](https://github.com/lionellloh/realtime-py) is underway thanks to [@lionellloh](https://github.com/lionellloh) & [@j0](https://github.com/j0)
- [postgrest-dart](https://github.com/supabase/postgrest-dart) was shipped by [@phamhieu](https://github.com/phamhieu)
- [postgrest-csharp](https://github.com/supabase/postgrest-csharp) is being implemented by [@acupofjose](https://github.com/acupofjose)

### Hacktoberfest

There's been [plenty of drama](https://hacktoberfest.digitalocean.com/hacktoberfest-update) during Hacktoberfest, but Supabase is committed to helping new contributors get started with open source. We've already seen a lot of issues closed by new members to our community and first-time contributors. If you're looking for ways to get involved, read our Hacktoberfest [blog post](/blog/supabase-hacktoberfest-2020), check our [live stream](https://www.youtube.com/watch?v=3_xRLTjvEiE&t=60s), and then dive into our [project board](https://github.com/orgs/supabase/projects/5).

![This is an image of our hacktoberfest project board on GitHub.](/images/blog/supabase-hacktoberfest-board.png)

### Coming next

Our focus now is to move from Alpha to Beta and we'll be improving stability, reliability, and performance. We've created a [Benchmarks](https://github.com/supabase/benchmarks/) repository, where we'll be measuring the performance of all the open source tools we use.

### Get started

- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)
- Make sure to [star us on GitHub](https://github.com/supabase/supabase)
- Follow us [on Twitter](https://twitter.com/supabase)
- Become a [sponsor](https://github.com/sponsors/supabase)
