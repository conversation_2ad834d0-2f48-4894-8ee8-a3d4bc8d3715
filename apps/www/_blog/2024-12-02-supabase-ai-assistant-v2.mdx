---
title: 'Supabase AI Assistant v2'
description: An evolution of how we approach AI within the Supabase dashboard
author: saxon_fletcher,joshenlim
image: launch-week-13/day-1-ai-assistant-v2/og.jpg
thumb: launch-week-13/day-1-ai-assistant-v2/thumb.jpg
categories:
  - developers
  - postgres
tags:
  - AI
  - postgres
date: '2024-12-02'
toc_depth: 3
launchweek: '13'
---

Today we are releasing Supabase Assistant v2 in the Dashboard - a global assistant with several new abilities:

1. Postgres schema design
1. Data queries and charting
1. Error debugging
1. Postgres RLS Policies: create and edit
1. Postgres Functions: create and edit
1. Postgres Triggers: create and edit
1. SQL to `supabase-js` conversion

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/_fdP-aaTHgw"
    title="We have improved Supabase AI ... A lot!"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  />
</div>

## A new, unified approach to AI

Our new Assistant is more extensible, using a flexible system of components, tools, and APIs. You can provide context manually (e.g. an RLS Policy) or automatically based on whichever page you're visiting in the Dashboard (e.g. the specific table you're working on).

The result is a single panel that's persistent across the entire Dashboard. It sits alongside your workspace and can be called upon when needed (`cmd+i`!). It automatically retrieves context for your prompt and can be provided with extra context similar to other AI tools like Cursor and GitHub Copilot.

## New abilities in Supabase Assistant v2

Let's take a look at new abilities in this release.

### Schema design

If you are creating something new, the Assistant can guide or inspire you. It will show you how to structure your database and generate all the SQL queries to set it up.

<Img
  alt="Design new database schemas"
  src="/images/blog/launch-week-13/day-1-ai-assistant-v2/design.png"
/>

### Writing SQL

Like our previous Assistant, the new Assistant will help you write queries based on your schema. This version has better contextual understanding and can provide more accurate suggestions.

### Debug your queries

Writing SQL can be tough. You can use the new Assistant to debug database errors directly through the SQL Editor or within the Assistant panel.

<Img
  alt="Debug your queries with AI"
  src="/images/blog/launch-week-13/day-1-ai-assistant-v2/debug.png"
/>

### Discover data insights

The new Assistant can run queries directly. This can be a useful (and fun) way to query your data through natural language. Basic select queries run automatically, and results are displayed within the conversation in tabular form or chart form. The chart axis are picked intuitively by the Assistant. No data is sent to the underlying LLM, only your schema structure. This is a helpful tool for folks who are not comfortable with SQL but are still interested in analyzing data insights.

<Img alt="Query data with AI" src="/images/blog/launch-week-13/day-1-ai-assistant-v2/query.png" />

### SQL to REST

Once your database is set up, you probably want to connect to it directly or with one of our client libraries. If you're using our `supabase-js` library, we've added a helpful tool to convert an SQL query to supabase-js client code. Simply ask the Assistant to convert a query, and it will respond with either a complete snippet for you to copy or a combination of function + RPC call. This is powered by the [sql-to-rest](/docs/guides/api/sql-to-rest) tool.

<Img
  alt="Convert SQL to supabase-js code"
  src="/images/blog/launch-week-13/day-1-ai-assistant-v2/js.png"
/>

### RLS Policies: Protect your database

Use the Assistant to suggest, create or modify RLS Policies. Simply explain the desired behavior and the Assistant will generate a new Policy using the context of your database schema and existing policies. To edit an existing policy, click “edit with Assistant” within your Policy list. The Assistant will be provided the appropriate context for you to start prompting.

<Img
  alt="Create and edit RLS Policies with AI"
  src="/images/blog/launch-week-13/day-1-ai-assistant-v2/policy.png"
/>

### Postgres Functions and Triggers

Suggest, create or update functions and triggers in a similar way to policies. Just describe what you want or select “Edit with Assistant” from your Function or Trigger list.

<Img
  alt="Create and edit functions and triggers with AI"
  src="/images/blog/launch-week-13/day-1-ai-assistant-v2/functions.png"
/>

## Feedback

This release gives us a foundation to build off and incorporate into other parts of your database journey. Where are you struggling the most when using Postgres? How might the Assistant help you? Send us your thoughts, ideas, concerns via the feedback form in the Dashboard.

## How to access

Supabase Assistant v2 is available today.
Go to a Project and hit `cmd + i`, or alternatively click the Assistant icon in the top right toolbar.
