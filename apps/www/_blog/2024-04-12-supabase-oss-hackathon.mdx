---
title: 'Supabase Open Source Hackathon 2024'
description: Build an Open Source Project over 10 days. 5 prize categories.
author: ant_wilson
image: oss-hackathon/thumbnail.png
thumb: oss-hackathon/thumbnail.png
launchweek: '11'
categories:
  - launch-week
tags:
  - launch-week
  - hackathon
date: '2024-04-12'
toc_depth: 2
---

[To celebrate the Supabase Special Announcement on April 15th](https://supabase.com/special-announcement) we're running a special 10 day open source hackathon.

The hackathon starts right now! (Friday 12th April at 09:00 am PT) and ends Sunday 21st April at 11:59 pm PT. You could win a set of Apple AirPods along with extremely limited edition Supabase swag and add your name to the Supabase Hackathon Hall of Fame.

For some inspiration check out all the [winners from previous hackathons](https://supabase.com/blog/tags/hackathon).

This is the perfect excuse to "Build in a weekend, scale to millions". Since you retain all the rights to your submissions, you can use the hackathon as a launch pad for your new Startup ideas, side-project, or indie hack.

## Key Facts

- You have 10 days to build a new **Open Source** project using Supabase in some capacity.
  - Starting 09:00 am PT Friday 12 April 2024
  - The submission deadline is 11:59 pm Sunday midnight PT 21st April 2024
- Enter as an individual, or as a team of up to 4 people
- Build whatever you want - a project, app, tool, library. Anything.

## Prizes

There are 5 categories, there will be prizes for:

- Best overall project (one set of Apple AirPods for each team member!)
- Best use of AI (Swag kit)
- Most fun / best easter egg (Swag kit)
- Most technically impressive (Swag kit)
- Most visually pleasing (Swag kit)

There will be a winner and a runner-up prize for each category. Every team member on winning/runner-up teams gets a Supabase Launch Week swag kit.

![Supabase Swag](/images/blog/oss-hackathon/prize.png)

## Submission

You should submit your project using [this form](https://madewithsupabase.com/hackathons/open-source-2024) before 11:59 pm Sunday midnight PT 21st April 2024. Extra points if you include a simple video demoing the app in the description.

## Judges

The Supabase team will judge the winners for each category.
We will be looking for:

- creativity/inventiveness
- functions correctly/smoothly
- visually pleasing
- technically impressive
- use of Supabase features
- FUN! 😃

## Rules

- Team size 1-4 (all team members on winning teams will receive a prize)
- You cannot be on multiple teams
- One submission per team
- It's not a requirement to use AI
- All design elements, code, etc. for your project must be created **during** the event
- All entries must be Open Source (link to source code required in entry)
- Must use Supabase in some capacity
- Can be any language or framework
- You must submit before the deadline (no late entries)
- (optional) Include a link to a demo video along with the submission for extra points

## Resources

### Supabase resources to get you started

- [Real world RAG, the missing pieces for your AI application](https://www.youtube.com/watch?v=ibzlEQmgPPY)
- [Simplify complex SQL queries with Views](https://www.youtube.com/watch?v=IOYFS-2lFjU&t)
- [Supabase Swift Client Library](https://supabase.com/docs/reference/swift/introduction)
- [Row Level Security in Supabase](https://supabase.com/docs/guides/database/postgres/row-level-security)

### Community help

The Supabase Team will be taking part in the Hackathon and you'll find us live to build in our discord all week. Please join us by building in public:

- Text channel: [#hackathon](https://discord.gg/UYyweApy)
- Audio channel: [#hackathon](https://discord.gg/Vj3mTPwH)

If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!

[Join our Discord](https://discord.supabase.com/)

![Supabase Discord Channel](/images/blog/lw7-hackathon/discord.png)

- [Previous Hackathon Prize Winners](https://supabase.com/blog/tags/hackathon)

## Additional Info

- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required.
- By making a submission you grant Supabase permission to use screenshots, code snippets, and/or links to your project or content of your README on our Twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.
