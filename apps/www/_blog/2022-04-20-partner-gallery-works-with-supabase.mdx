---
title: Works With Supabase - announcing our Partner Gallery
description: Introducing our Partner Gallery - open source and made with Supabase.
author: alais<PERSON>,shane<PERSON>,thor_schaeff
image: partner-gallery/partner-showcase-thumb.png
thumb: partner-gallery/partner-showcase-thumb.png
categories:
  - company
tags:
  - community
  - partnerships
date: '2022-04-20'
toc_depth: 3
---

# Introducing our Partner Gallery - open source and made with Supabase

Supabase is a collaborative effort across many open-source projects, and we’re incredibly fortunate that you all have rallied around us to make Supabase what it is today.

To keep track of all the great tools and people that “Work With Supabase”, we’ve built a [Partner Gallery](/partners) that showcases integration partners who are extending how Supabase works, as well as experts who are certified to help you build new, exciting things.

![partner-gallery-screenshot.png](/images/blog/partner-gallery/partner-gallery-screenshot.png)

As with anything at Supabase, the Partner Gallery is built on top of the [Supabase stack](/docs/guides/getting-started/architecture) and is, of course, open-source. If you’re looking to build a partner gallery yourself, feel free to use our template as a start: [github.com/supabase-community/partner-gallery-example](https://github.com/supabase-community/partner-gallery-example). It’s packed with goodies like [Postgres Full Text Search](/docs/guides/database/full-text-search), `next-image` with Supabase Storage, and sending emails based on an insert trigger with Supabase Edge Functions.

# Partner showcase - new integrations and expert services

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/videoseries?list=PL5S4mPUpp4OtKHdiuNkt8GewVo3dc-oHW"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

## Expert services

Some agencies and developer experts have specialized experience building with Supabase. If you’re looking for the right partner to bring your application to life, check out the experts on [supabase.com/partners/experts](/partners/experts).

If you have expertise developing with Supabase and want to offer your expert services, you can [apply to become a partner today](/partners/integrations#become-a-partner).

Please join us in welcoming our first two experts!

### Morrow

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/uPN5BbIuJw8"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Morrow is an app development agency headquartered in Bristol, UK. Their mission is to consult, build and support cutting-edge innovation.

Their specialisms are working with Supabase, React Native, Netlify and Swell to produce bespoke solutions that are robust and beautiful.

Morrow was awarded UK App Dev Agency of the Year 2022.

### Quinence

![Quinence](/images/blog/partner-gallery/expert-quinence.png)

Quinence is a boutique product development studio from Singapore.

They build digital products that delight. You give them an idea, and they can deliver the complete experience with hyper-scalable infrastructure.

## Integrations

There are so many fantastic tools out there making it easier than ever to build stunning applications or helping you with a specific workflow. We’re ecstatic that many of them chose to integrate with Supabase to give you even more superpowers.

We’re starting off with a selection across auth, developer tools, and low-code tools. If you’ve built a tool that integrates with Supabase, we’d love to [hear from you](/partners/integrations#become-a-partner)!

## Auth

### Clerk

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/vLSynn8_SN4"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Clerk authenticates users, manages session tokens, and provides user management functionality that can be used in combination with the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.

### Stytch

![Stytch](/images/blog/partner-gallery/integration-stytch.png)

Stytch provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience.

## DevTools

### Prisma

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/YjvQsNEOs60"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Prisma is an open source next-generation ORM. It consists of the following parts:

- Prisma Client: Auto-generated and type-safe query builder for Node.js & TypeScript.
- Prisma Migrate: Migration system.
- Prisma Studio: GUI to view and edit data in your database.

### Snaplet

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/64Z6Ku0t334"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Snaplet is a developer tool that copies a Postgres database, transforming personal information, so that you can safely code against actual data. This functionality makes it possible to easily achieve environment parity in Supabase.

### Vercel

![Vercel](/images/blog/partner-gallery/integration-vercel.png)

Vercel enables developers to build and publish wonderful things. They build products for developers and designers. And those who aspire to become one. This makes it the perfect tool for building web apps with Supabase.

### pgMustard

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/IGYcN1Hvc84"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

pgMustard speeds up your journey from knowing which query is a problem to working out what can be done about it.

Reading EXPLAIN ANALYZE output can be tough.

Even if you know how, Postgres includes so much useful information, it is easy to miss something important. And for queries that do a lot, the plans are even more useful-yet-time-consuming.

## Low-Code

### Clutch

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/ZZE9BabdTxE"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

What can I achieve with Clutch & Supabase? Anything you can think of where a database would be necessary for your project. Examples include building a blog, real-time chat app, booking platform, and todo list, Supabase, and Clutch provides all the necessary tools to build your projects quickly and efficiently.

### Appsmith

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/SfAxGVjJ6Q0"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Appsmith is an open-source framework for building internal tools. It lets you drag-and-drop UI components to build pages, connect to any API, database or GraphQL source and write logic with JavaScript objects.

### Draftbit

![Draftbit](/images/blog/partner-gallery/integration-draftbit.png)

Draftbit is a "pro-code" low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.

### Plasmic

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/nFWUCNbCFhI"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

By connecting Supabase with Plasmic, a visual builder for the web, you can build data-backed applications without code. While many users leverage Plasmic to quickly launch and iterate on landing pages, Plasmic can be used as a general-purpose visual builder for React, which can be used to design and implement fully featured read-write applications.

### Teta

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/FAqQxT-peuc"
    frameBorder="0"
    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Teta is an online cooperative app builder. It allows designers and developers to collaborate in real-time on the design and development of mobile applications, through a convenient visual interface that allows the export of Flutter code.
