---
title: 'Introducing the Supabase UI Library'
description: 'Drop complex components into your projects in seconds.'
author: i<PERSON><PERSON><PERSON>,jonny,saxon_fletcher,saltcod
image: launch-week-14/day-1-supabase-ui-library/og.jpg
thumb: launch-week-14/day-1-supabase-ui-library/thumb.jpg
categories:
  - launch-week
tags:
  - launch-week
  - design
date: '2025-03-31T00:00:01'
toc_depth: 3
launchweek: 14
---

We're excited to release an [official Supabase UI Library](https://supabase.com/ui)—a collection of ready-to-use components built on top of [shadcn/ui](https://ui.shadcn.com/). Designed for flexibility, these components can be dropped into any Next.js, React Router, TanStack Start, or plain React app.

<div className="video-container mb-8">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/2TIuUjkCDFE"
    title="Introducing Supabase UI Library"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"
    allowfullscreen
  />
</div>

Installing components from the Supabase UI Library is as easy as installing a component from shadcn/ui.

<Img
  wide
  alt="Install"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/install.jpg',
    light: '/images/blog/launch-week-14/day-1-supabase-ui-library/install-light.png',
  }}
/>

The library is 100% shadcn/ui compatible by leveraging the [component registry](https://x.com/shadcn/status/1829646556318089509) feature. It follows the conventions for theming and reuses existing components like buttons and inputs.

Our UI registry is a collection of reusable components designed for use in several popular React frameworks. Components are styled with shadcn/ui and Tailwind CSS and are completely customizable.

<Img
  wide
  alt="UI Library files"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/folder-structure.jpg',
    light: '/images/blog/launch-week-14/day-1-supabase-ui-library/folder-structure-light.jpg',
  }}
/>

It's designed to take both the time and the pain out of building complex functionality like user sign-up in your apps. All components work with new or existing projects.

## What's included?

- [Supabase client](https://supabase.com/ui/docs/nextjs/client) initialization for client-side and server-side use
- [Password-based authentication](https://supabase.com/ui/docs/nextjs/password-based-auth)
- [File Upload Dropzone](https://supabase.com/ui/docs/nextjs/dropzone)
- [Realtime Cursor Sharing](https://supabase.com/ui/docs/nextjs/realtime-cursor)
- [Current User Avatar](https://supabase.com/ui/docs/nextjs/current-user-avatar)
- [Realtime Avatar Stack](https://supabase.com/ui/docs/nextjs/realtime-avatar-stack)
- [Realtime Chat](https://supabase.com/ui/docs/tanstack/realtime-chat)

We intend to release more Supabase UI Library components, and we'd love to get your feedback. Got a favorite component you want for your applications? Let us know on [X](https://x.com/supabase) or [Discord](https://discord.supabase.com/), or we'd be happy to get a [pull request](https://github.com/supabase/supabase).

Let's look at some of the components.

## Password-based authentication

Setting up authentication in your projects can be complicated and time-consuming. The Password-Based Authentication block provides all the necessary components and pages to implement a secure, user-friendly authentication flow in your projects in seconds.

<Img
  wide
  alt="Authentication files"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-password-based-auth-02.jpg',
    light:
      '/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-password-based-auth-02-light.jpg',
  }}
/>

It includes everything you need to get started—fully built components for signing up, signing in, resetting a password, and handling forgotten passwords. These components are styled, responsive, and production-ready out of the box, so you don't have to worry about the design or flow. Just drop them into your project, and you're ready to sign up users.

<Img
  alt="Sign up"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-password-based-auth.jpg',
    light:
      '/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-password-based-auth-light.jpg',
  }}
/>

## Easy file uploads

The File Upload Dropzone component lets you add file upload and storage in your application in seconds. It features drag-and-drop support,
multiple file uploads, file size and count limits, image previews and MIME type restrictions.

File upload components are often complicated to set up. Spend your time working on what happens after the files are on the server.

<Img
  alt="File upload dropzone"
  src={{
    dark: "/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-dropzone.jpg",
    light: "/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-dropzone-light.jpg"
  }}

/>

## Realtime cursor sharing

The Realtime Cursor component gets you started building multiplayer experiences in your applications. You can just drop this component into your project and you're ready to use Realtime in seconds.

<Img
  alt="Realtime cursors"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-realtime-cursors.jpg',
    light:
      '/images/blog/launch-week-14/day-1-supabase-ui-library/supabase-ui-realtime-cursors-light.png',
  }}
/>

## See who's online

With the User Avatar and Realtime Avatar Stack components, you can add Realtime Presence to your apps in a few minutes. See who's online in your collaborative apps, just like in Notion or Figma.

<Img
  alt="Who's Online"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/whos-online.jpg',
    light: '/images/blog/launch-week-14/day-1-supabase-ui-library/whos-online-light.png',
  }}
/>

## Realtime Chat

The Realtime Chat component is a complete chat interface, letting users exchange messages in real-time within a shared room. It features real-time, low-latency updates, message synchronization, message persistence support, customizable message appearance, automatic scroll-to-bottom on new messages and more.

<Img
  alt="Realtime Chat"
  src={{
    dark: '/images/blog/launch-week-14/day-1-supabase-ui-library/realtime-chat.jpg',
    light: '/images/blog/launch-week-14/day-1-supabase-ui-library/realtime-chat-light.png',
  }}
/>

## Rules for AI Code Editors for Supabase

Alongside our UI components, we're also shipping a curated set of LLM rules tailored for Supabase and Postgres. These rules help AI code editors understand and work with features like Row Level Security (RLS), Postgres functions, RLS policies, and Supabase Edge Functions. These rules help guide models toward best practices and valid syntax, improving your developer experience. Install them all in a single command.

---

## Why build on shadcn/ui?

[Shadcn/ui](https://ui.shadcn.com/) has been the [top project](https://risingstars.js.org/2024/en#section-all) on JavaScript Rising Stars for two years running—and for good reason. It offers a unique approach: instead of installing a component library as a dependency, you copy and paste the actual component code into your project. You get complete control over customization, styling, and behavior, with components that feel like part of your project.

## Get started today

Skip the boilerplate and long setup times—focus on what really matters: building and shipping fast. Explore the Supabase UI Component Library, drop it into your projects, and let us know what you think. Be sure to [submit components](https://github.com/orgs/supabase/discussions/34578) you want to see next!

Visit the [Supabase UI Library](https://supabase.com/ui).
