---
title: 'Supabase is now HIPAA and SOC2 Type 2 compliant'
description: 'This documents our journey from SOC2 Type 1 to SOC2 Type2 and HIPAA compliance. You can start building healthcare apps on Supabase today.'
launchweek: '8'
categories:
  - company
tags:
  - launch-week
  - security
date: '2023-08-11'
toc_depth: 3
author: inian
image: launch-week-8/day-5/OG-day5-compliance.jpg
thumb: launch-week-8/day-5/thumb-day5-compliance.jpg
---

While we weren’t [planning](https://news.ycombinator.com/item?id=35526018) to do anything official for this announcement, [the customer is always right](https://news.ycombinator.com/item?id=35555756).

Supabase is now officially SOC2 Type 2 and HIPAA compliant.

That’s all you need to know. The rest of this blog post will give you some background and what to expect if you’re planning to go through the same process.

## SOC2

We previously [discussed](https://supabase.com/blog/supabase-soc2) the process the SOC2 Type 1. To recap,

1. Type 1 certification verifies adherence to the guidelines at a specific _point in time_.
2. Type 2 certification verifies compliance over a _period of time_.

We received our Type 2 certification on May 22nd of this year and we plan to conduct annual Type 2 audits to ensure adherence to SOC2 guidelines.

We used the same auditor for the Type 2 certification and knew mostly what to expect. Some of our internal processes needed to change to make it easier to the evidence for our auditor. Examples of the kind of requests our auditor would ask for:

- List of all incidents which happened in a given time period. The postmortem for a few incidents from that list that the auditor chooses.
- List of all access requests in a given time period.
- List of vulnerabilities and when they were fixed
- List of Data deletion requests and evidence that a particular request was actioned on within our SLA.

Some of these were readily available in [Vanta](https://www.vanta.com/), our compliance monitoring tool. For others, we had to develop new processes to ensure that this information was all readily available. The Type 2 audit involves gathering a lot more evidence than the Type 1 audit. If we didn’t have proper systems and process in place before the audit, it would have been painful during evidence collection.

## HIPAA

Health Insurance Portability and Accountability Act (HIPAA) is a US law that sets national standards for protecting individuals' medical records and personal health information. Companies building applications with sensitive healthcare data must comply with HIPAA to ensure the security and privacy of patients' information.

A couple of definitions before go further. A **covered entity** refers to healthcare providers, health plans, and health insurance companies. **Business associates** are entities that perform certain functions with protected health information (PHI) on behalf of a covered entity. Both Business Associates and Covered entities are covered (pun intended) under HIPAA. Supabase is a business associate and our customers handling PHI can either be covered entities or other business associates.

We receive many requests from users who want to build healthcare apps on top of Supabase. Since you can [self-host Supabase](https://supabase.com/docs/guides/self-hosting), we often [encourage](https://github.com/orgs/supabase/discussions/1219) these users to do so. Starting today, these users have the option to use our hosted platform too with the HIPAA add-on 🎉.

### Going from SOC2 to HIPAA

<Img
  src="/images/blog/launch-week-8/day-5/soc2-to-hipaa.png"
  alt="soc2 to HIPAA"
  zoomable={false}
/>

Going from zero to a SOC2 certification was much harder, than going from SOC2 to HIPAA.

We used the same auditor to streamline the process. Many of the controls required for HIPAA compliance could be mapped to the testing they had already done for SOC2. Additional evidence for encryption, audit logs, business continuity and disaster recovery exercises was unnecessary since the auditor already had access to it. And some of the HIPAA checks such as Facility Access Controls were not applicable to us since [we are a remote company](https://supabase.com/blog/why-supabase-remote).

We also had to sign a Business Associate Agreement (BAA) with all of our vendors who would have access to PHI, such as AWS, and ensure that we follow their terms listed in the agreements. For example, when using AWS to store PHI, we could only use their [HIPAA Eligible Services](https://aws.amazon.com/compliance/hipaa-eligible-services-reference/). There were similar requirements from the other vendors we use and to ensure that we were complying with all their requirements.

Similarly when you sign a BAA with us, you have some responsibilities you agree to when using Supabase to store PHI. These are documented in our [shared responsibility doc](/docs/guides/platform/shared-responsibility-model#managing-healthcare-data).

We made a significant change to our incident management process for HIPAA. The [HIPAA Breach Notification](https://www.hhs.gov/hipaa/for-professionals/breach-notification/index.html) rules have strict requirements for handling breaches. For instance, business associates are required to notify the covered entity within 60 days of a breach. This also required us to appoint a HIPAA Security officer who would be responsible for reviewing any breach for PHI disclosure and communicating it’s impact to the Covered Entity.

![Data Breach Declaration](/images/blog/launch-week-8/day-5/data-breach-2.png)

The not-so-fun but important stuff included updating a bunch of our policies to cover HIPAA requirements such as enforcing automatic log-offs as part of our workstation security policy. Shopping for a good Technology Errors and Omissions Insurance plan was another boring but important thing we had to finalize in the off-chance that we get hacked despite all our measures. [HIPAA fines](https://www.strongdm.com/blog/hipaa-violation-penalties) are no joke, you could rake up to 1.9 million dollars per violation per calendar year depending the severity of the breach!

## Build Healthcare Apps on Supabase

If you want to start developing healthcare apps on Supabase, reach out to our team [here](https://forms.supabase.com/hipaa2) to sign our BAA. We are excited to see what you build!

## More Launch Week 8

- [Supabase Local Dev: migrations, branching, and observability](https://supabase.com/blog/supabase-local-dev)
- [Hugging Face is now supported in Supabase](https://supabase.com/blog/hugging-face-supabase)
- [Launch Week 8](https://supabase.com/launch-week)
- [Coding the stars - an interactive constellation with Three.js and React Three Fiber](https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber)
- [Why we'll stay remote](https://supabase.com/blog/why-supabase-remote)
- [Postgres Language Server](https://github.com/supabase/postgres_lsp)
