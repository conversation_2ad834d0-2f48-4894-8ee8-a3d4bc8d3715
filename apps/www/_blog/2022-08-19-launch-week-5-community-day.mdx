---
title: 'Community Day'
description: Wrapping up Launch Week 5 with contributors, partners, and friends.
author: and<PERSON>_smith,j<PERSON><PERSON><PERSON>_<PERSON><PERSON>,thor_<PERSON><PERSON><PERSON><PERSON>,tyler_shukert
image: lw5-community-day/community_day_og.jpg
thumb: lw5-community-day/community_day_og.jpg
categories:
  - developers
tags:
  - launch-week
  - community
date: '2022-08-19'
toc_depth: 2
video: https://www.youtube.com/v/4A6RKPp1b70
---

Supabase combines existing open-source tools with our own open-source contributions to provide a delightful experience for developers of all skill levels. Just as versatile as our platform, we're building a community of communities, bringing together developers from many different backgrounds, as well as new developers looking to get involved with open source.

To wrap up Launch Week 5, we're showcasing some of the communities and contributors that make up the Supabase community, highlighting their awesome work, and celebrating everyone who contributes their time to the Supabase mission. 💚

---

## Launch Week 5 - a new era?!

If you're an avid Launch Week participant, you might have been surprised this time because we usually kick-off with Community Day on Mondays.
And we previously closed Launch Week with a bunch more things on Friday, and the start of our virtual Hackathon.

This time, we're switching things around:

- [Monday to Thursday](/launch-week) are the big-ticket launches.
- Friday, we highlight all the additional “one more thing(s)” that we've launched, as well as shine a light on our awesome community contributions.
- As you [might have noticed](/blog/launch-week-5-hackathon), we now run our virtual Hackathon throughout Launch Week, and as always you can win some awesome limited edition swag, and for the first time ever, there's a $1500 GitHub sponsorship awarded to the best overall project!

<blockquote class="twitter-tweet" data-theme="dark">
  <p lang="en" dir="ltr">
    Thanks to <a href="https://twitter.com/supabase?ref_src=twsrc%5Etfw">@supabase</a> for this
    little beauty (the hat, not the cat).
    <br />
    <br />
    Now I've just got to pry it away from Peanut 🥜 😂
    <a href="https://t.co/POBdVZrF5N">pic.twitter.com/POBdVZrF5N</a>
  </p>
  &mdash; Daniel Cranney 🇬🇧 (@danielcranney) <a href="https://twitter.com/danielcranney/status/1557249589182447616?ref_src=twsrc%5Etfw">August 10, 2022</a>
</blockquote>
<script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>

_Our limited edition swag is approved by professional swag testers like Peanut!_

### Launch Week Landing Page & Golden Tickets

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/4_duDgQeCyM?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

We now have a shiny new [Launch Week page](https://supabase.com/launch-week), your one-stop shop for all things Launch Week, where you can find each day's feature as well as highlights from the past Launch Weeks.

We've taken a page out of [Vercel's Virtual Events Starter Kit](https://vercel.com/templates/next.js/virtual-event-starter-kit), and generated over 3000 virtual tickets. Thank you all for participating, we love [seeing your tickets on Twitter](https://twitter.com/hashtag/SupaLaunchWeek?src=hashtag_click&f=live)! To make this work, we've adapted the Virtual Events Starter Kit and [added in a Supabase Database provider](https://github.com/vercel/virtual-event-starter-kit/pull/88) as an alternative to the Redis database option.

We've added a Golden Ticket mechanic, distributing some special edition launch week swag to some lucky winners. If you're interested in how we've built this, check out the video above with a brief explanation.

---

## Open Source Spotlight

### pg_jsonschema

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/4A6RKPp1b70?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

For our Open Source Spotlight we're hearing from [Dmitry Dygalo](https://twitter.com/Stranger6667) from [schemathesis.io](https://schemathesis.io/) who works on a multitude of open-source projects.
He's the author and maintainer of [jsonschema-rs](https://github.com/Stranger6667/jsonschema-rs),
which is the foundation for our new [pg_jsonschema extension](https://github.com/supabase/pg_jsonschema), a PostgreSQL extension providing JSON Schema validation.

[Learn about pg_jsonschema](/blog/pg-jsonschema-a-postgres-extension-for-json-validation).

### Postgrest v10

![PostgREST v10](/images/blog/lw5/postgrest-v10.png)

Today, PostgREST v10 was [released](https://github.com/PostgREST/postgrest/releases/tag/v10.0.0).
In this [blog post](/blog/postgrest-v10) we explore some of its new features: EXPLAIN and Improved Relationship Detection.

[Read the blog post](/blog/postgrest-v10).

---

## New courses & tutorials

### Free Egghead course: Cache Supabase data at the Edge with Cloudflare Workers and KV Storage

[![Egghead course](/images/blog/lw5-community-day/egghead_og.png)](https://egghead.io/courses/cache-supabase-data-at-the-edge-with-cloudflare-workers-and-kv-storage-883c7959)

Caching is one of the hardest problems in Computer Science!

Unless you pair Supabase with Cloudflare, then it is actually quite simple!

Check out [Jon Meyer's](https://twitter.com/jonmeyers_io), entirely free egghead course — Cache Supabase data at the Edge with Cloudflare Workers and KV Storage — dropping right now!

Learn how to:

- Use Wrangler CLI to interface with Cloudflare
- Proxy Supabase requests through Cloudflare Workers
- Bind a KV Store to cache data
- Cache response data from Supabase
- Automatically revalidate cache when data changes
- Continue performing work after a response has been sent

All that in only 28 mins — or 14 mins on 2x speed!

### Realtime Trello Board with Angular by Simon Grimm

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/shv7uT_NM7Y?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

[Simon Grimm](https://twitter.com/schlimmson) from [ionicacademy.com](http://ionicacademy.com/) & [devdactic.com](http://devdactic.com/) is releasing a new course for all the Angular fans out there!

- [Subscribe](https://www.youtube.com/simongrimmdev_) to Simon's YouTube where the course will be released soon!
- Find the code on GitHub: [github.com/saimon24/supabase-trello-angular](https://github.com/saimon24/supabase-trello-angular)

---

## Integrations, Expert Partners, and Developer Stories

Since releasing our [Partners](/partners) page last Launch Week, we've had a lot of new partners reach out to join the community.

### CodeSandbox Projects Beta

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/cTNUjAQ06AE?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

The new [CodeSandbox Projects Beta](https://projects.codesandbox.io/) let's you run your projects and turn them into a link you can share with anyone. Developers visiting your link can not only see your running code, they can click “fork” and get an exact copy of that environment so they can easily contribute back to your masterpiece. CodeSandbox integrates directly with GitHub, so you get a unique development environment for every PR you and your contributors open!

You can do all of this from a web browser, from [within VSCode](https://marketplace.visualstudio.com/items?itemName=CodeSandbox-io.codesandbox-projects), or via the [native iPad app](https://apps.apple.com/us/app/codesandbox/id1423330822)! Give it a try with the [Supabase example project](https://codesandbox.io/p/github/codesandbox/codesandbox-supabase-example/main?file=%2FREADME.md)!

### Dashibase

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/WAYA6-qBfPs?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

[Dashibase](https://dashibase.com/) is a no-code internal tools builder with a Notion-like UI. With the latest update, you can easily build CRUD dashboards on top of your Supabase projects, set read/write access on your data sources, and share your dashboards with your team. Stop building dashboards from scratch.

### Replicache

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/0lBxH6d38FM?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Replicache is a JavaScript framework for building high performance, collaborative, offline-first web apps.

It's inspired by apps like [Linear](https://linear.app/) and [Superhuman](https://superhuman.com/). When the user takes an action in one of these apps, it happens instantly, with no server round-trip, and changes from other users appear in realtime.

Replicache works with Supabase for storage, and Supabase Realtime for its [poke message feature](https://doc.replicache.dev/byob/poke). Read [the guide](https://doc.replicache.dev/deploy-vercel-supabase) to learn how to [deploy to Vercel and Supabase](https://doc.replicache.dev/deploy-vercel-supabase)!

### [Zapp.run](http://Zapp.run) by Invertase

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/ctSlcU2i9WA?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Zapp is a free online sandbox environment for building Flutter applications in the browser. With Zapp, you can enjoy building Flutter apps as though in your favorite IDE, especially if you are a VSCode user; you should feel at home.

You can install any pub packages and start developing, of course including Supabase! You can sign up for our early access program [here](https://www.notion.so/Social-media-strategy-1f06ed5695fe497ba6782a5ce007b04f)!

### Feelantera

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/Am9egV4Hgi4?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

Feelantera is a creative digital agency based in Indonesia. They craft web applications with cutting-edge technology, and are one of our new [Supabase Experts](https://supabase.com/partners/experts). They [recently open-sourced](https://twitter.com/feelantera/status/1556909234478059521) their Supabase Edge Functions powered URL shortener on [GitHub](https://github.com/feelantera/fltr-link).

If you're building with Supabase and are looking for some support, check out their [Expert listing](https://supabase.com/partners/feelantera) and reach out to them.

Are you also experienced in building with Supabase and interested in helping others? Apply to [become and Expert Partner](https://supabase.com/partners/experts#become-a-partner)!

### Snappify

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/rg1RMiv6uuc?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

snappify is a powerful design tool that helps you to explain and share your code snippets in many beautiful ways.

The editor lets you operate on a free canvas where you can arrange multiple code snippets and annotate them with arrow and text elements to explain specific parts of your code. You can style everything to your own needs and give your code presentations a personal touch by adding your social media branding.

Head over to [snappify.io](https://t.co/hdO3oY38If) and give it a try on your own - all the basic features are available for free.

### WalletConnect Cloud: You Can Now Sign In with Ethereum

![WalletConnect](/images/blog/lw5-community-day/walletconnect_og.png)

[WalletConnect](https://walletconnect.com/) is an open source protocol for connecting decentralised applications to mobile wallets with QR code scanning or deep linking. This week they have launched the option for users to sign-in to their cloud platform with Ethereum. They are building this on top of GoTrue with the aim to contribute it back to our GoTrue fork to make it available to all Supabase users. If you're interested in this, you can follow the [PR here](https://github.com/supabase/gotrue/pull/282).

To explore the infrastructure backing the project, check out the repository [here](https://github.com/walletconnect/CloudSIWE). To try it out, create an account on the WalletConnect [cloud platform](https://cloud.walletconnect.com/). To read more about the feature, head on over to the [WalletConnect blog](https://medium.com/walletconnect/walletconnect-cloud-you-can-now-sign-in-with-ethereum-7563578686c2).

## Hackathon

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/rI3Ik7GyYEw?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

We hope all of these awesome projects and stories gave you plenty of inspiration and you're ready to build in a weekend and scale to millions, as our [hackathon is still running until Sunday](https://supabase.com/blog/launch-week-5-hackathon) night PT!

---

## Oh - and one more thing!

Well, actually a couple more things! As it is now tradition, we have too many things for one week, so at the end of the week we compile everything that didn't get its own spotlight day, into a nice little [box of treats](/blog/launch-week-5-one-more-thing).

---

## That's a wrap

Here we are. What a week indeed! Launch Week 5 was so much fun, thank you for participating and sharing what you're working on, and most importantly thank you for building with Supabase! 💚

We'll be jumping into our own hackathon projects now, and we'll see you next week for the highlight reel and the hackathon winner announcements, so make sure to subscribe on [Twitter](https://twitter.com/Supabase), and [YouTube](https://www.youtube.com/supabase) to not miss out! And do come hang out and hack together in [Discord](https://discord.supabase.com/)! See you there 👁️⚡️👁
