---
title: 'Community Day'
description: Wrapping up Launch Week 6 with contributors, partners, and friends.
author: and<PERSON>_smith,j<PERSON><PERSON><PERSON>_<PERSON><PERSON>,thor_<PERSON><PERSON><PERSON><PERSON>,tyler_shukert
image: lw6-community/community-day.png
thumb: lw6-community/community-day.png
categories:
  - developers
tags:
  - launch-week
  - community
date: '2022-12-16'
toc_depth: 3
video: https://www.youtube.com/v/hw9Q-NjASbU
---

Supabase is a collaborative company. We work with, sponsor, and support as many open source tools as possible. The great thing about this approach is that each tool brings their own community and it's starting to feel like Supabase is really just a “community of communities”. Far too much has happened in the Supabase community in the last 3 months for us to possibly feature it all here, so here's a collection of the highlights!

## New Integrations

While we've been working hard on new features, the community has been developing a healthy [integrations marketplace](https://supabase.com/partners/integrations/integrations). Since Supabase is just Postgres, any tool which works with Postgres works with Supabase (no additional changes required). Today we're adding a few major players to the roster:

### FlutterFlow

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/hw9Q-NjASbU?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

[FlutterFlow](https://flutterflow.io/) is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.

An integration between FlutterFlow and Supabase has been long awaited by the community. Starting today, you can build your apps with FlutterFlow and use Supabase for your backend!

This is just the beginning of our integration with FlutterFlow. We're working closely with the FlutterFlow team to deliver more features!

Watch John from the FlutterFlow team demonstrate how you can [get started with FlutterFlow and Supabase](https://youtu.be/hw9Q-NjASbU).

### OneSignal

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/mw0DLwItue4?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

[OneSignal](https://onesignal.com/) is a messaging platform allowing you to deliver push notifications, in-app messages, SMS, and emails to your users.

Combining OneSignal with Supabase, you can send cloud messages to your users with any database change.

We have guides on how to get started with [Next.js and OneSignal](https://supabase.com/partners/integrations/onesignal), and [Flutter and OneSignal](https://github.com/OneSignalDevelopers/onesignal-supabase-sample-integration-supabase).

In the video above, William from OneSignal will show you how you can easily integrate OneSignal with your Supabase app.

### NextAuth

<div className="video-container">
  <iframe
    className="w-full"
    src="https://www.youtube-nocookie.com/embed/EdYQ9fF-hz4?modestbranding=1&autohide=1&showinfo=0&controls=0"
    title="YouTube video player"
    frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowfullscreen
  ></iframe>
</div>

[NextAuth.js](https://next-auth.js.org/) is an open source authentication solution for [Next.js](https://nextjs.org/). It allows you to bring your own Database so that you own your user data.

The amazing NextAuth community who have built a [Supabase Adapter](https://next-auth.js.org/adapters/supabase) that makes it easy to use NextAuth.js with Supabase, and enables the use of [Row Level Security (RLS) policies](https://next-auth.js.org/adapters/supabase#enabling-row-level-security-rls).

In [this community spotlight video](https://youtube.com/EdYQ9fF-hz4), [Thang Huu Vu](https://twitter.com/thanghvu), the lead maintainer of NextAuth.js, gives an overview and brief history of NextAuth, followed by a tutorial on how to set up the new Supabase Adapter.

## Postgres Ecosystem

### PGroonga Multilingual Search extension

![PGroonga](/images/blog/lw6-community/pgroonga.jpg)

Over the past few years, we've had a number of people ask if we can add Full Text Search for other languages. PGroonga has been on our radar for a long time now, so when we reached out to them we were surprised to learn that _no_ Postgres platform currently supports them. Today we're fixing this by becoming the first.

PostgreSQL of course features excellent [Full Text Search](https://supabase.com/docs/guides/database/full-text-search) out-the-box, but it can be lacking if you need to search against languages with non-Roman characters such as Japanese, Chinese and so on. For this you can use the PGroonga extension! It's already installed on all new projects, just head to the [Database tab](https://supabase.com/dashboard/project/_/database/extensions) on the dashboard and search or scroll for 'PGroonga' and hit enable to get started. Once enabled you can head to [their excellent documentation](https://pgroonga.github.io/tutorial/) on how to start making queries.

PGroonga is backed by a collection of incredible maintainers, contributors, and a broad and diverse [community](https://pgroonga.github.io/community/).

### PostgreSQL 15

![PG15](/images/blog/lw6-community/pg15.png)

The PostgreSQL community [released](https://www.postgresql.org/docs/current/release-15.html) version 15 (stable) in October 2022. All Projects created on the Supabase platform from today will be on version 15. If you want to upgrade one of your existing Projects, check out our [Upgrading](https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects#upgrade-your-project) documentation, and if you want to see what's new, check out : XXX

Read [what's new in **Postgres 15?**](https://supabase.com/blog/new-in-postgres-15)

### PostgREST version 11 pre-release

![Postgrest 11](/images/blog/lw6-community/postgrest.png)

PostgREST 11 is not wrapped up yet but you can try a pre-release locally with the Supabase CLI. Some of the new features include: spreading related tables, related orders and anti-joins. Read on more about these on:

[Learn more about PostgREST 11](https://supabase.com/blog/postgrest-11-prerelease)

## Made with Supabase

![Made With Supabase](/images/blog/lw6-community/made.jpg)

Perhaps you've seen [madewithsupabase.com](https://www.madewithsupabase.com/), but did you know that the site was made and is maintained by an [open source contributor](https://twitter.com/zernonia) (during our first hackathon)? Since then, there have been over 300 apps and websites listed. Here's some of our favourites:

### LottieFiles

LottieFiles built a [Mobile Motion Canvas](https://www.youtube.com/watch?v=QoN7l_Wq_S4) where you can create and share social stories, posts, stickers, & more.

### Mobbin

Mobbin use Supabase Auth to power their [UI/UX library of Mobile and Web screenshots](https://mobbin.com/browse/ios/apps). Last year they [migrated 200k users from Firebase to Supabase Auth](https://supabase.com/blog/mobbin-supabase-200000-users).

### sound.xyz

[sound.xyz](https://sound.xyz) is platform for artists to drop tracks to their fans first, users support the artists they love directly and stake their claim on being there before everyone else.

### Topaz

[Topaz](https://topaz.network/) is one of the fastest growing NFT networks and chose supabase as a backend to ensure they won't lose customers during peak traffic periods.

### Repeat.dev

[Repeat.dev](https://repeat.dev) is a platform for webhook based task automation. It has a built in scheduler, and can handle emails and pdf generation.

### Dots

[Dots](https://www.dots.community/) is a tool for interactive onboarding and support flows across Slack and Discord. Dots uses Supabase so they don't have to worry about managing a backend, and can focus on helping their users build amazing community experiences.

### Fleeting Notes

[Fleeting Notes](https://www.fleetingnotes.app/) allows you to take linked notes within your browser or on your phone, then sync the notes with Obsidian. They wrote an awesome guide on how they [migrated 1000+ users from Firebase to Supabase](https://fleetingnotes.app/posts/migrating-from-firebase-to-supabase/).

## Content Creators

![Content Creators](/images/blog/lw6-community/content-storm.png)

One of the most exciting developments this year is the number of content creators and developers helping _others_ to get started with Supabase. Ultimately, this means more developers using Postgres. There's really too many of these awesome folks to feature, but here's a few who have gone above and beyond:

### Content Storm

More than 30+ creators featured in our [Content Storm](https://supabase.com/blog/the-supabase-content-storm). These folks all produced brand new content in the build up to Launch Week and dropped it simultaneously!

### Supaship

![Content Creators](/images/blog/lw6-community/supaship.jpg)

Supaship built a whole [Supabase course](https://supaship.io/). It's absolutely jam-packed with advice on how to get the most out of Supabase (and his meme game is absolute fire!).

### Jon Meyers for Egghead

![Content Creators](/images/blog/lw6-community/egghead.jpg)

Check out Jon's brand new, entirely free course on egghead to learn all about the Supabase Auth Helpers. Throughout the course, you will build a realtime chat application with Remix and learn how to:

- use Supabase server-side in loaders and actions
- query and mutate data as an authenticated user with Supabase
- sign users in with GitHub OAuth
- enforce authorization with Row Level Security (RLS) policies
- merge client and server state with realtime events
- deploy a Remix app to Vercel

[Build a Realtime Chat App with Remix and Supabase](https://egghead.io/courses/build-a-realtime-chat-app-with-remix-and-supabase-d36e2618)

### Everything Svelte

![Content Creators](/images/blog/lw6-community/svelte.jpg)

[Everything Svelte](https://www.everythingsvelte.com/) is a Sveltekit and Supabase course by [Amy](https://twitter.com/selfteachme). You'll learn everything you need to know to build a modern web application.

> In Everything Svelte, participants will learn EVERYTHING they need to know to build a full-stack application in SvelteKit and Supabase. We cover topics such as Tailwind styles, Svelte animations and transitions, search, testing, creating components, managing state, Stripe integration, sending emails, GitHub Actions, database modeling, and deployment. Supabase, in particular, really shines as we leverage its authentication system, row level security, local storage, and database APIs.

## Getting started templates

### Base Jump

[usebasejump.com](https://usebasejump.com/) is "The fastest way to launch Supabase apps with Next.js". Basejump is open source. Contributions are both awesome and encouraged!

### supastarter

[supastarter.dev](https://supastarter.dev/) is the SaaS starter template for Next.js and Supabase. Save time and focus on your business with this scalable and production-ready starter template for your SaaS. It includes authentication, i18n, subscriptions, landing page and much more!

## Community libraries

![Community org](/images/blog/lw6-community/eyes.jpg)

The Supabase-Community org is home to 80(!) repos built and maintained by the community. It houses everything from [Client Libraries](https://github.com/supabase-community/#client-libraries), to [Auth Helpers](https://github.com/supabase-community/#supabase-auth-helpers), [Self Hosting tooling](https://github.com/supabase-community/#self-hosting), and [Utilities](https://github.com/supabase-community/#utilities).

An awesome recent addition from the AWS team is [supabase-on-aws](https://github.com/supabase-community/supabase-on-aws), a self-hosting starter kit for AWS.

## Community Growth

![Community Growth Chart](/images/blog/lw6-community/graph.jpg)

At the start of this year [@kiwicopple tweeted](https://twitter.com/kiwicopple/status/1478338320346267648) how far we had come as a community. Since then we've grown to one of the [most popular](https://github.com/search?q=stars%3A%3E42000&type=repositories) repos on GitHub (top 220) and have grown by:

- GitHub stars: 25K → 60K (across all repos)
- Hosted Databases: 75K → 250K
- Contributors: 271 → 750 (across all repos)
- Launch Weeks: 3 → 6
- Discord: 0 → 11k

## Giving back

At less than 3 years old we're still finding our feet, but one of the things we're most proud of is our contribution back to open source. Technically everyone working at Supabase is an open source contributor, so it's hard to quantify exactly the value we've been contributing to the open source ecosystem. To name a few of the more obvious ways:

- Paid out over $100,000 to contributors via [Open Collective](https://opencollective.com/supabase).
- Invested in $500,000 into [OrioleDB](https://github.com/orioledb/orioledb) to accelerate the Table Access API in Postgres.
- We financially sponsor open source tools, like [PostgREST](https://opencollective.com/supabase-postgrest) and [PGroonga](https://opencollective.com/clear-code), and research initiatives like [Elixir type system](https://twitter.com/josevalim/status/1535008937640181760?s=20&t=BLqeO2YpdhYfCZxgsmlNug).
- We employ maintainers of the external tools which we use, including PostgREST and pgsodium (to work on those tools)!
- Our team has contributed several patches to the Postgres core.
- We help to drive open source adoption on tools like [Deno](https://deno.land/).
- We collaborated with the [Snaplet](https://www.snaplet.dev/) team to develop and open source [Postgres WASM](https://supabase.com/blog/postgres-wasm).
- And possibly the biggest: we've maintained our OSI-compliant stance. With the exception of some of our cloud-infra & billing code, the Supabase ecosystem is all MIT, Apache 2.0, or PostgreSQL licensed.

We plan to continue our open and collaborative trend. There are many companies working on exciting ideas in the database ecosystem, and there are far too many problems for the Supabase team to solve alone. If you're interested in working with us to solve these problems, don't ever hesitate to reach out.

## Getting involved

- Want to contribute? Dive into any of our [repos](https://github.com/supabase/) to get started.
- Want to work in open source? We don't have many openings today, but keep this URL in mind in the future when you're job-hunting: [supabase.com/careers](https://supabase.com/careers)
- Want to try out Supabase? [Join the Hackathon](https://supabase.com/blog/launch-week-6-hackathon) and win some nice prizes.
- Want to support us? Just a [GitHub star](https://github.com/supabase/supabase) is enough ⭐.

## More Launch Week 6

- [Day 1: New Supabase Docs, built with Next.js](https://supabase.com/blog/new-supabase-docs-built-with-nextjs)
- [Day 2: Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn)
- [Day 3: Multi-factor Authentication via Row Level Security Enforcement](https://supabase.com/blog/mfa-auth-via-rls)
- [Day 4: Supabase Wrappers, a Postgres FDW framework written in Rust](https://supabase.com/blog/postgres-foreign-data-wrappers-rust)
- [Day 5: Supabase Vault is now in Beta](https://supabase.com/blog/vault-now-in-beta)
- [Point in Time Recovery is now available](https://supabase.com/blog/postgres-point-in-time-recovery)
- [Custom Domain Names are now available](https://supabase.com/blog/custom-domain-names)
- [Wrap Up: everything we shipped](https://supabase.com/blog/launch-week-6-wrap-up)
