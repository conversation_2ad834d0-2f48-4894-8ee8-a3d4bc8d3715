---
title: 'High Performance Disk'
description: Store up to 60 TB of data with 100x improved durability and 5x more IOPS
author: pcnc,jonny,gregpapas
image: launch-week-13/high-performance-disks/og.jpg
thumb: launch-week-13/high-performance-disks/thumb.jpg
categories:
  - product
tags:
  - launch-week
  - postgres
date: '2024-12-05T00:00:01'
toc_depth: 3
launchweek: '13'
---

High Performance disks store up to 60 TB of data with 100x improved durability, and provision up to 5x more IOPS than the default disks we offer.

## A Two-Pronged Approach to Disk Scalability

We've been tackling disk scalability from two angles. On the software side, our implementation of [Oriole DB's index-organized tables](https://www.orioledb.com/blog/orioledb-beta7-benchmarks) significantly reduces disk I/O operations, improving performance without additional hardware resources.

On the infrastructure side we've added new disk options that allow for advanced scaling of your Postgres databases.

## Expanded Capacity

One of the most significant improvements is our increased storage capacity. We've moved beyond our previous 16 TB limit, now offering up to 60 TB of storage for your largest databases. But with greater capacity comes the need for enhanced performance - particularly in how quickly your database can read and write data. This makes IOPS (Input/Output Operations Per Second) especially important.

To address these needs, our new High Performance disks can handle up to 80,000 IOPS - a 5x increase from the 16,000 IOPS limit of our General Purpose disks.

## Understanding Performance: IOPS and Throughput

IOPS is a critical metric that measures how many read and write operations your database can perform each second. Think of it as the "speed limit" for your database's ability to access stored data. Higher IOPS means faster database operations, which translates to better application performance, especially for data-intensive workloads.

Throughput, measured in MiB/s (Mebibytes per second), is equally important as it determines how much total data can flow through your disk at once. While IOPS tells you how many individual read/write operations can happen per second, throughput determines the total volume of data that can be moved. With our General Purpose disks, you start with a baseline throughput of 125 MiB/s, which can be provisioned up to 1,000 MiB/s. Our High Performance disks automatically scale throughput with IOPS, providing better performance for data-intensive workloads.

Effective throughput and IOPS also depends on your compute instance size. You can read more about these interdependencies in our [compute and disk documentation](https://supabase.com/docs/guides/platform/compute-and-disk#compute-size).

## Durability: Keeping Your Data Safe

Another benefit of our High Performance disks is increased durability. Our new disks offer 99.999% durability, a 100x increase over our standard disk. This means that if you use High Performance Disk, you will almost never need to worry about disk failure — say goodbye to recovery from backups.

## Consolidated Disk and Compute User Interface

With these advanced options comes complexity—both in the number of options available, and how they interplay with compute settings. To address this we've redesigned our disk management interface to coexist and interoperate with our compute upgrade UI. When designing the new UI, we adhered to the following principles:

### Transparent Billing

- Real-time cost updates as you adjust your disk configuration

<Img
  alt="Real-time cost updates as you adjust your disk configuration"
  src="/images/blog/launch-week-13/high-performance-disks/high-performance-disks-billing-1.png"
/>

- Clear breakdown of how each change affects your bill

<Img
  alt="Clear breakdown of how each change affects your bill"
  src="/images/blog/launch-week-13/high-performance-disks/high-performance-disks-billing-2.png"
/>

### Updated Disk Size Insights

The Disk Size Usage graph breaks down the space used by the Database, Write-Ahead Log, and the System, rather than simply showing "used space."

<Img
  alt="Updated disk size insights"
  src="/images/blog/launch-week-13/high-performance-disks/high-performance-disks-insights.png"
/>

### Preventing Footguns

Effective IOPS is limited by both your compute add-on and disk configuration and it is technically possible to over provision the disk throughput and IOPS with the instance not being able to make full use of it. For example, to achieve maximum IOPS (80,000), you'll need a 16XL or larger compute instance. The dashboard warns you when it detects scenarios like these.

<Img
  alt="Preventing footguns"
  src="/images/blog/launch-week-13/high-performance-disks/high-performance-disks-footgun.png"
/>

## Pricing

The pricing for High Performance Disk starts at $0.195 per GB, and you can provision IOPS at $0.119 per IOPS. The storage pricing for General Purpose disks remains unchanged, and you can provision IOPS at $0.024 per IOPS and 0.095$ per Mbps throughput.

For more details on pricing breakdown vs. General Purpose Disk, check out our [pricing page](https://supabase.com/pricing).

## Getting Started

Ready to try out our new disk options? Visit [Compute and Disk](https://supabase.com/dashboard/project/_/settings/compute-and-disk) in the Supabase Dashboard.

We're excited to see what you'll build with these new capabilities. As always, we're committed to providing the tools you need to scale your applications effectively while maintaining the simplicity and developer experience you've come to expect from Supabase.
