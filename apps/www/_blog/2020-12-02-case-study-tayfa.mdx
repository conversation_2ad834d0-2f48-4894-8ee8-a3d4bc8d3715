---
title: TAYFA Built a No-Code Website Builder in Seven Days
description: See how Tayfa went from idea to paying customer in less than 30 days.
author: rory_wilding
author_title: Supabase
author_url: https://github.com/roryw10
author_image_url: https://github.com/roryw10.png
image: /images/blog/supabase-tayfa.png
categories:
  - company
tags:
  - no-code
date: '12-02-2020'
---

[**TAYFA**](https://usetayfa.com) is the fastest no-code approach to create and iterate on bespoke frontends. Learn how its solo founder, [<PERSON><PERSON> Banskota](https://twitter.com/sarupbanskota?lang=en), was able to launch quickly with [Supabase](/), Next.js, and Vercel.

### Meet the founder

<PERSON><PERSON>'s background is in open-source, web frontends, and devtools. In the summer of 2020, he was rapidly building MVPs as he explored different problem spaces. <PERSON><PERSON> found that reusing his frontend work across projects, wasn't as simple as he'd have liked.

![This image shows a quote from <PERSON><PERSON> - Frontends should be as simple as: Sign-up, drag & drop, publish.](/images/blog/sarup-tayfa.png)

- Zero to MVP: **7 Days**
- Idea to 1st Payment: **30 Days**
- Creating Scalable Backend APIs: **30 mins**

### The Maker Journey

When Sarup found an interested customer, TAYFA was still a fuzzy idea in his head. As a workflow tool, the best way to demonstrate the concept was to build an MVP. His customer needed the product yesterday — there was no time to waste.

He started out with a first iteration using Next.js and Vercel for the frontend — technologies he loved, and Firebase because he was familiar with it. However, he soon realised that for his enterprise customers, self-hosting capabilities would be a deal-breaker. Moreover, he'd require sensible RESTful APIs for developing the subsequent iterations of TAYFA.

One option was to do it the "old-school" way: Postgres on a custom server, or MongoDB. But that would mean losing momentum by getting bogged down setting up a backend.

### Supabase helped shortcut development time

Luckily, Sarup discovered Supabase through the Vercel community. Open-source and built on top of Postgres, it was perfect for his future self-hosting needs and also offered him an API out of the box. The current version of TAYFA is the third iteration, and also takes advantage of Supabase Auth, an out of the box auth layer.

![This image shows a quote from his customers - The speed at which TAYFA is evolving is crazy. I've seen ideas translate into live features 10 minutes after speaking on the phone.](/images/blog/tanmai-tayfa.png)

> _"If my product helps customers move fast, then I need to move fast._
>
> _Luckily with Supabase, I haven't had to worry about the health of my backend systems, and can focus on what I enjoy most: shipping frontend."_
>
> Sarup Banskota, founder of TAYFA

### Prototype fast, and keep going

Thanks to Supabase, Sarup can focus on what he does best and get his final product in front of his customer. There was no need to worry about choosing and setting up back-ends, Sarup could focus on launching fast.
