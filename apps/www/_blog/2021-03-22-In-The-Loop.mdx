---
title: Developers stay up to date with intheloop.dev
description: <PERSON>rn why <PERSON> is building intheloop.dev with Supabase
author: rory_wilding
image: intheloop-supabase.jpg
thumb: intheloop-supabase.jpg
categories:
  - developers
tags:
  - supabase
  - postgres
  - open-source
date: '03-22-2021'
---

<PERSON> is a freelance engineer who has been programming since he was 12 years old. His latest project, intheloop.dev, provides a single gateway for developers to stay up to date.

## At the leading edge of Developer updates

As a developer, <PERSON> loves to stay up to date with the latest technology. Within his network, he also finds himself the go-to person to help his colleagues stay up to speed by searching through Twitter, staying active in forums, and searching out different tech communities. He knows this insight will help others stay up to date at scale, so created intheloop.dev.

Intheloop.dev is a single point of entry for developers to stay up to date on the topics they care about by aggregating feeds from various sources. It serves as an on-ramp to finding the communities you care about, monitor releases and change logs, critical tweets, and blogs of interest.

<PERSON> is launching with coverage on three packages - React, Kotlin and Vite. He is also part of the [#buildinpublic](https://twitter.com/search?q=%23buildinpublic&src=typed_query) movement, In The Loop is open-source - you can [check it out on GitHub](https://github.com/kevcodez/intheloop). Kevin also developed open-source [Gotrue](https://github.com/supabase/gotrue-kt) and [PostgREST](https://github.com/supabase/postgrest-kt) Kotlin clients for Supabase.

![intheloop.dev screenshot](/images/blog/intheloop-screenshot.jpg)

## Sometimes Firebase just isn't enough

With over seven years of experience working with relational databases, Kevin knows how powerful Postgres is. Kevin is a Firebase user and knows first hand that he can build fast with Firebase. However, straightforward tasks in Postgres like count functionality, foreign key relations and database normalisation are either insanely difficult or impossible to achieve using Firebase. With Supabase, he gets the benefits of Postgres with the development pace of Firebase. Because he uses Supabase as the backend, it will be easy to add more features in future, like user accounts, when he is ready.

## Supabase gives Developers Postgres Supapowers

Kevin loves building with Supabase because of SQL and other Postgres features like constraints, views, and materialised views. Using all these features quickly and efficiently is amazing for Kevin when he knows Supabase is just as easy to integrate as Firebase.

<Quote img="intheloop-kevin.png" caption="Kevin Grüneberg  - Creator of intheloop.dev">
  Supabase building on top of Postgres is a true superpower for developers. Postgres is already
  insanely powerful, and having much more functionality out of the box means I can build quickly and
  continue to scale
</Quote>

## Launch fast and have confidence in adding more functionality with Supabase

With Supabase, Kevin can focus on his idea's core rather than worrying about his tech stack. Intheloop is just getting started with additional features coming like notifications, following topics, and developers to build a personal digest which is where the real time-saver begins. With Postgres under the hood, Kevin can add functionality to delight his users without worrying about his data model. You can sign up to stay in the loop [here](https://intheloop.dev/).
