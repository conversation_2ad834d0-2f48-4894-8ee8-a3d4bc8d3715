# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
!.env
.env.local
.env.development.local
.env.test.local
.env.staging.local
.env.production.local

.vercel

/public/dashboard
# Sentry Auth Token
.sentryclirc
