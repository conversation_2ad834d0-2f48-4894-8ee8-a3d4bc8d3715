.monaco-editor {
  @apply relative overflow-visible;
  -webkit-text-size-adjust: 100%;
}

.monaco-editor-alt.monaco-editor-alt,
.monaco-editor-alt > .monaco-editor {
  --vscode-editor-background: hsl(var(--background-surface-300)) !important;
  --vscode-editorGutter-background: hsl(var(--background-surface-300)) !important;
}

.monaco-editor-alt .current-line {
  border: 0 !important;
}

[data-theme='dark'] .monaco-editor,
[data-theme='dark'] .monaco-diff-editor {
  --vscode-editor-background: hsl(var(--background-surface-100)) !important;
  --vscode-editorGutter-background: hsl(var(--background-surface-100)) !important;
  color-scheme: dark;
}
.monaco-editor,
.monaco-diff-editor {
  --vscode-editor-background: hsl(var(--background-dash-sidebar)) !important;
  --vscode-editorGutter-background: hsl(var(--background-dash-sidebar)) !important;
}

.gutter {
  @apply bg-border-control;
  cursor: row-resize;
}

.grid-monaco-editor,
.monaco-editor p,
label,
div,
section,
span.th.tr.td.textarea {
  /* @apply text-sm; */
}

.grid-monaco-editor,
.monaco-editor label {
  font-weight: inherit;
  text-transform: none;
  padding: inherit;
  width: inherit;
  align-self: inherit;
}

.grid-monaco-editor,
.monaco-editor label:hover {
  cursor: inherit;
}

.find-widget {
  margin-right: 30px;
}

.grid-monaco-editor,
.monaco-editor .suggest-widget {
  .main {
    max-height: none;
    height: inherit;
    width: 100%;
    margin: inherit;
    padding: inherit;
    overflow: inherit;
  }

  .monaco-list-row.focused .main {
    background: var(--vscode-quickInput-background);
  }
}
