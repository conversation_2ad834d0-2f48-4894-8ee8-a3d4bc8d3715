/* // date picker */

.react-datepicker__input-container input {
  @apply block w-full text-base md:text-sm bg-white border border-background-surface-100 rounded shadow-sm;
}

.react-datepicker-popper {
  @apply z-40 w-72 text-sm bg-surface-100 shadow px-3 py-2 border-2 border-background rounded;
}

.react-datepicker-left {
  @apply absolute left-0 right-auto top-11 transform-none #{!important};
}

.react-datepicker-right {
  @apply absolute right-0 left-auto top-11 transform-none #{!important};
}

.react-datepicker__portal {
  @apply absolute z-10 w-72 text-sm transform-none bg-studio shadow px-3 py-2 top-12 right-0 border-2 border-background rounded;
}

.react-datepicker__month-container {
  @apply flex flex-col;
}

.react-datepicker__month {
  @apply flex flex-col;
  @apply -space-y-px;
}

.react-datepicker__current-month {
  @apply ml-2.5 text-lg font-semibold text-border-stronger;
}

.react-datepicker__week {
  @apply flex -space-x-px;
}

.react-datepicker__day-names {
  @apply flex -space-x-px text-foreground-lighter font-normal text-center text-xs;
}

.react-datepicker__day-name {
  @apply w-10 h-10 flex items-center justify-center py-1 rounded-full;
}

.react-datepicker__navigation {
  @apply absolute top-2;
}

.react-datepicker__navigation--previous {
  @apply right-12 w-10 h-10 rounded transition flex items-center justify-center hover:bg-studio;
}

.react-datepicker__navigation--next {
  @apply right-4 w-10 h-10 rounded transition flex items-center justify-center hover:bg-studio;
}

.react-datepicker__day {
  @apply w-10 h-10 flex items-center font-normal justify-center text-sm leading-loose transition text-foreground-light cursor-pointer;
  /* @apply mb-1 py-1; */
  @apply -space-y-px;
  @apply border border-default;
}

.react-datepicker__day--disabled {
  @apply cursor-not-allowed opacity-40 hover:bg-transparent;
}

.react-datepicker__day--outside-month {
  @apply text-border-stronger;
}

.react-datepicker__day--in-range {
  @apply bg-brand-400 text-scale-fixed-100 border-brand-300 text-foreground;
}

.react-datepicker__day--in-selecting-range {
  @apply bg-brand-400 text-scale-fixed-100 border-brand-400 text-foreground;
}

.react-datepicker__day--selecting-range-start,
.react-datepicker__day--selecting-range-end {
  @apply bg-brand-300 text-foreground hover:bg-brand-400;
}

.react-datepicker__day--selected,
.react-datepicker__day--selected.react-datepicker__day--highlighted {
  @apply bg-brand text-foreground;
}

.react-datepicker__day--range-start,
.react-datepicker__day--range-end {
  @apply bg-brand text-foreground;
}

.react-datepicker__day--highlighted {
  @apply bg-overlay-hover text-foreground-light;
}

.react-datepicker__aria-live {
  @apply sr-only;
}
