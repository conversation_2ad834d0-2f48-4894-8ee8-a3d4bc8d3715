.sb-grid {
  @apply flex h-full flex-col;
}

.sb-grid-fill-container:after {
  content: '';
  display: inline-block;
  width: 100%;
}

.rdg-cell {
  @apply flex;
  @apply text-grid;
  @apply border-secondary border-r border-b;
  @apply text-foreground;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: inherit;
}

// Apply parent bg colour while editing
.rdg-cell.rdg-editor-container > * {
  background-color: inherit;
}

.rdg-cell > div[draggable='true'] {
  @apply h-full;
}

.rdg-cell-frozen-last {
  @apply border-secondary border-r-2 shadow-none;
}

.rdg-cell[aria-selected='true'] {
  box-shadow: inset 0 0 0 1px #24b47e;
}

.rdg {
  @apply box-border select-none overflow-x-auto overflow-y-scroll bg-dash-canvas;
  @apply border-t border-r-0 border-l-0;
  contain: strict;
  -webkit-user-select: none;
  font-size: 14px;
}

.rdg *,
.rdg ::after,
.rdg ::before {
  box-sizing: inherit;
}

@supports not (contain: strict) {
  .rdg {
    position: relative;
    z-index: 0;
  }
}

.rdg-focus-sink {
  position: sticky;
  top: 0;
  left: 0;
  height: 0;
  width: 0;
  outline: 0;
}

/*
  Row header
*/

.rdg-header-row {
  @apply bg-surface-200 select-none border-none;
}

.rdg-header-row {
  height: var(--header-row-height);
  line-height: var(--header-row-height);
  top: 0;
}

.rdg-header-row .rdg-cell {
  @apply bg-surface-200 border-default border-b;
}

.rdg-header-row .rdg-cell p {
  @apply text-typography-body-light [[data-theme*=dark]_&]:text-typography-body-dark;
}
.rdg-header-row .rdg-cell .react-contextmenu-wrapper {
  @apply flex h-full items-center px-2 transition duration-100 ease-in-out;
}
.rdg-header-row .rdg-cell .react-contextmenu-wrapper:hover {
  @apply bg-surface-200;
}
.rdg-header-row .rdg-checkbox {
  @apply bg-surface-200 border-default rounded-sm border;
}
.rdg-header-row .rdg-checkbox-input:checked + .rdg-checkbox {
  @apply border-default border-4 bg-green-900;
}
.rdg-header-row .rdg-checkbox-input:focus + .rdg-checkbox {
  box-shadow: none;
}

.rdg-cell .Select {
  max-height: 30px;
  font-size: 12px;
  font-weight: 400;
}

/*
  Row
*/

.rdg-row {
  @apply bg-dash-sidebar transition-colors;
  @apply hover:bg-surface-200;
}

/* edit button */
.rdg-row__select-column__edit-action {
  @apply opacity-0;
}
.rdg-row:hover .rdg-row__select-column__edit-action {
  @apply opacity-100;
}

.rdg-row__select-column__edit-action:hover {
  opacity: 1 !important;
}

/* select row */
.rdg-row[aria-selected='true'] {
  @apply bg-surface-200;
}

.tab-cursor {
  cursor: pointer !important;
}

/*
  Checkbox
*/

.sb-grid {
  /* reset styles */
  [type='checkbox'] {
    webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
  }

  [type='checkbox']:checked {
    @apply bg-foreground;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='hsl(0, 0%, 100%)'%3E%3Cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3E%3C/svg%3E") !important;
  }

  [type='checkbox']:indeterminate {
    @apply bg-foreground;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='hsl(0, 0%, 100%)'%3E%3Crect x='3' y='7' width='10' height='2'/%3E%3C/svg%3E") !important;

    [data-theme='dark'] & {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='hsl(0, 0%, 0%)'%3E%3Crect x='3' y='7' width='10' height='2'/%3E%3C/svg%3E") !important;
    }
  }

  [type='checkbox'] {
    @apply cursor-pointer rounded border border-solid;

    @apply text-foreground border-strong transition-all;
    @apply hover:border-foreground focus:ring-0 focus:outline-none;

    @apply bg-control;

    margin-top: 2px;
  }
}

/* Dark mode */
[data-theme='dark'] .sb-grid [type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='hsl(0, 0%, 0%)'%3E%3Cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3E%3C/svg%3E") !important;
}

/*
  React Contexify
*/

.react-contexify {
  position: fixed;
  opacity: 0;
  user-select: none;
  background-color: #fff;
  box-sizing: border-box;
  box-shadow: 0px 10px 30px -5px rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 6px 0;
  min-width: 200px;
  z-index: 100;
}
.react-contexify__submenu--is-open,
.react-contexify__submenu--is-open > .react-contexify__item__content {
  color: white;
  background-color: #4393e6;
}
.react-contexify__submenu--is-open > .react-contexify__submenu {
  pointer-events: initial;
  opacity: 1;
}
.react-contexify .react-contexify__submenu {
  position: absolute;
  /* negate padding */
  top: -6px;
  pointer-events: none;
  transition: opacity 0.275s;
}
.react-contexify__submenu-arrow {
  margin-left: auto;
  font-size: 12px;
}
.react-contexify__will-leave--disabled {
  pointer-events: none;
}
.react-contexify__item {
  cursor: pointer;
  position: relative;
}
.react-contexify__item:focus {
  outline: 0;
}
.react-contexify__item:not(.react-contexify__item--disabled):hover
  > .react-contexify__item__content,
.react-contexify__item:not(.react-contexify__item--disabled):focus
  > .react-contexify__item__content {
  color: white;
  background-color: #4393e6;
}
.react-contexify__item:not(.react-contexify__item--disabled):hover > .react-contexify__submenu {
  pointer-events: initial;
  opacity: 1;
}
.react-contexify__item--disabled {
  cursor: default;
  opacity: 0.5;
}
.react-contexify__item__content {
  font-size: 13px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  color: #333;
  position: relative;
}

.rdg__segmented-control {
  @apply border-default relative mx-2 h-8 rounded-md border;
}

.rdg__segmented-control__button {
  @apply absolute top-0 inline-flex h-full items-center justify-center;
  @apply text-xs font-medium;
  @apply text-border-muted;
  @apply active:bg-alternative hover:text-white focus:z-10 focus:outline-none;
  @apply transition duration-150 ease-in-out;
  @apply cursor-pointer;
  z-index: 1;
}

.rdg__segmented-control__button--option {
  @apply text-background;
}

.rdg__segmented-control__button--right {
  @apply right-0;
}

.rdg__segmented-control__button--left {
  @apply left-0;
}

.rdg__segmented-control__label {
  @apply bg-overlay-hover border-control z-0 inline-block h-full transform rounded border shadow transition duration-200 ease-in-out;
}

.rdg__segmented-control__label--left {
  @apply translate-x-0;
}

.rdg__segmented-control__label--right {
  @apply translate-x-12;
}

.rdg__segmented-control__options-label {
  @apply uppercase;
  color: inherit;
}

/*
  DropdownControl
*/

.dropdown-control {
  @apply overflow-auto;
}

.dropdown-control .sbui-typography {
  @apply block px-2 py-4;
}

/*
  NullValue
*/

.null-value {
  @apply block;
}

/*
  CheckboxEditor
*/

.sb-grid-checkbox-editor {
  @apply flex h-full w-full;
}

.sb-grid-checkbox-editor__input {
  @apply h-4 w-4;
  outline: 4px auto -webkit-focus-ring-color;
}

/*
  JsonEditor
*/

.sb-grid-json-editor__trigger {
  @apply text-grid overflow-hidden overflow-ellipsis px-2;
}

/*
  NumberEditor
*/

.sb-grid-number-editor {
  @apply h-full w-full px-2;
}

/*
  SelectEditor
*/

.sb-grid-select-editor {
  button {
    @apply border-none rounded-none shadow-none;
    box-shadow: none !important;
  }
}

/*
  TextEditor
*/

.sb-grid-text-editor__trigger {
  @apply text-grid overflow-hidden overflow-ellipsis px-2;
}

/*
  TimeEditor
*/

.sb-grid-time-editor {
  @apply h-full w-full px-2;
}

/*
  Footer
*/

.sb-grid-footer {
  @apply bg-surface-100 flex h-10 items-center justify-between px-2;
}

.sb-grid-footer__inner {
  @apply flex items-center;
}

/*
  ForeignKeyFormatter
*/

.sb-grid-foreign-key-formatter {
  @apply flex w-full items-center justify-between;
}

.sb-grid-foreign-key-formatter__text {
  @apply m-0 flex-grow overflow-hidden overflow-ellipsis;
}

/*
  ColumnHeader
*/

.sb-grid-column-header {
  @apply flex h-full w-full items-center justify-between;
}

.sb-grid-column-header--cursor {
  @apply cursor-default;
}

.sb-grid-column-header__inner {
  @apply flex items-center gap-2 overflow-hidden overflow-ellipsis;
}

.sb-grid-column-header__inner__name {
  @apply text-foreground overflow-hidden overflow-ellipsis text-xs select-text;
}

.sb-grid-column-header__inner__format {
  @apply text-xs;
  @apply overflow-hidden overflow-ellipsis font-normal;
  @apply text-foreground-light;
}

.sb-grid-column-header__inner__primary-key {
  @apply flex rotate-45 transform items-center;
}

.sb-grid-column-header__inner svg {
  @apply border-brand text-brand;
}

/*
  Grid
*/

.sb-grid-grid--loading {
  @apply flex justify-center bg-transparent;
}

.sb-grid-grid--loading__inner {
  @apply flex items-center;
}

.sb-grid-grid--loading__inner__text {
  @apply m-8;
}

/*
  SelectColumn
*/

.sb-grid-select-cell__formatter {
  @apply flex h-full w-full items-center justify-between;
}

.sb-grid-select-cell__header {
  @apply flex h-full w-full items-center justify-between;
}

.sb-grid-select-cell__header__input {
  /* @apply focus:ring-brand-300; */
  @apply border-background-surface-100;
}

/*
  Header
*/

.sb-grid-header {
  @apply bg-surface-100 flex h-10 justify-between px-2;
}

.sb-grid-header__inner {
  @apply flex items-center space-x-2;
}

.sb-grid-header__inner__divider {
  @apply py-2;
}

.row_header__selected-rows {
  @apply ml-2 mr-2;
}

/*
  StatusLabel
*/

.sb-grid-status-label {
  @apply text-grid text-white;
}

.sb-grid-status-label__no-msg {
  @apply flex h-5 w-5;
}

.sb-grid-status-label__no-msg > div {
  @apply m-auto h-2 w-2 rounded-full bg-green-900;
}

/*
  Empty value
*/

.sb-grid-empty-value {
  @apply block;
}

/*
header/sort/SortDropdown
*/

.sb-grid-sort-popover {
  @apply w-96;
}

.sb-grid-dropdown__empty {
  @apply py-2;
}

.sb-grid-dropdown__empty__text {
  @apply block;
}

.sb-grid-dropdown__item-trigger {
  @apply my-1;
}

/*
header/sort/SortRow
*/

.sb-grid-sort-row {
  @apply flex justify-between space-x-3;
}

.sb-grid-sort-row__item {
  @apply flex items-center space-x-3;
}

.sb-grid-sort-row__item__remove {
  @apply bg-transparent p-0 hover:bg-transparent;
}

.sb-grid-sort-row__item__label {
  @apply flex items-center space-x-2;
}

.sb-grid-sort-row__item_toogle {
  @apply flex w-28 items-center gap-0 space-x-3;
}

.sb-grid-sort-row__item__move {
  @apply flex cursor-move;
}
