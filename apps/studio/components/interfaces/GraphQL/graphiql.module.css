.graphiqlContainer .graphiqlSessions {
  margin: 0;
  border-radius: 0;
}

.graphiqlContainer .graphiqlSession {
  padding: 0;
}

.graphiqlContainer .graphiqlEditors {
  border-radius: 0;
}

.graphiqlContainer .graphiqlEditors:global(.full-height) {
  margin-top: calc(0px - var(--session-header-height));
}

.graphiqlContainer .graphiqlQueryEditor {
  @apply border-b border-default;
}

.graphiqlContainer .graphiqlSessionHeader {
  background-color: hsl(var(--color-base));
  @apply border-b border-default;
}

.graphiqlContainer .graphiqlResponseSingleTab {
  margin-top: calc(24px - var(--session-header-height));
}

.graphiqlContainer .graphiqlResponseMultiTab {
  padding-top: 16px;
}

.graphiqlContainer .graphiqlSidebar {
  @apply border-l border-default;
}

.graphiqlHorizontalDragBar {
  @apply border-l border-default;
}

.graphiqlAddTabWrapper {
  padding: var(--px-12);
  @apply z-10;
}

/* Variables */

:global(body.graphiql-dark) .graphiqlContainer {
  --color-base: 0, 0%, 11%;
  --color-primary: 153, 50%, 50%;
}

:global(body.graphiql-light) .graphiqlContainer {
  --color-base: 210, 17%, 98%;
  --color-primary: 153, 50%, 50%;
}

.graphiqlContainer {
  --font-family: theme(fontFamily.sans);
  --font-family-mono: theme(fontFamily.mono);
}
