import { ChevronRight } from 'lucide-react'
import { UseFormReturn } from 'react-hook-form'

import Panel from 'components/ui/Panel'
import { CreateProjectForm } from 'pages/new/[slug]'
import {
  Badge,
  cn,
  Collapsible_Shadcn_,
  CollapsibleContent_Shadcn_,
  CollapsibleTrigger_Shadcn_,
  FormControl_Shadcn_,
  FormField_Shadcn_,
  FormItem_Shadcn_,
  RadioGroupStacked,
  RadioGroupStackedItem,
} from 'ui'
import { Admonition } from 'ui-patterns'
import { FormItemLayout } from 'ui-patterns/form/FormItemLayout/FormItemLayout'

interface SecurityOptionsProps {
  form: UseFormReturn<CreateProjectForm>
  layout?: 'vertical' | 'horizontal'
  collapsible?: boolean
}

export const SecurityOptions = ({
  form,
  layout = 'horizontal',
  collapsible = true,
}: SecurityOptionsProps) => {
  const content = (
    <>
      <FormField_Shadcn_
        name="dataApi"
        control={form.control}
        render={({ field }) => (
          <>
            <FormItemLayout
              layout={layout}
              className="[&>div>label]:!break-normal"
              label="What connections do you plan to use?"
            >
              <FormControl_Shadcn_>
                <RadioGroupStacked
                  // Due to radio group not supporting boolean values
                  // value is converted to boolean
                  onValueChange={(value) => field.onChange(value === 'true')}
                  defaultValue={field.value.toString()}
                >
                  <FormItem_Shadcn_ asChild>
                    <FormControl_Shadcn_>
                      <RadioGroupStackedItem
                        value="true"
                        className="[&>div>div>p]:text-left [&>div>div>p]:text-xs"
                        label="Data API + Connection String"
                        description="Connect to Postgres via autogenerated HTTP APIs or the Postgres protocol"
                      />
                    </FormControl_Shadcn_>
                  </FormItem_Shadcn_>
                  <FormItem_Shadcn_ asChild>
                    <FormControl_Shadcn_>
                      <RadioGroupStackedItem
                        label="Only Connection String"
                        value="false"
                        description="Use Postgres without the autogenerated APIs"
                        className={cn(
                          !form.getValues('dataApi') && '!rounded-b-none',
                          '[&>div>div>p]:text-left [&>div>div>p]:text-xs'
                        )}
                        onClick={() => form.setValue('useApiSchema', false)}
                      />
                    </FormControl_Shadcn_>
                  </FormItem_Shadcn_>
                </RadioGroupStacked>
              </FormControl_Shadcn_>
              {!form.getValues('dataApi') && (
                <Admonition
                  className="rounded-t-none"
                  type="warning"
                  title="Data API will effectively be disabled"
                >
                  PostgREST which powers the Data API will have no schemas available to it.
                </Admonition>
              )}
            </FormItemLayout>
          </>
        )}
      />

      {form.getValues('dataApi') && (
        <FormField_Shadcn_
          name="useApiSchema"
          control={form.control}
          render={({ field }) => (
            <>
              <FormItemLayout className="mt-6" layout={layout} label="Data API Configuration">
                <FormControl_Shadcn_>
                  <RadioGroupStacked
                    defaultValue={field.value.toString()}
                    onValueChange={(value) => field.onChange(value === 'true')}
                  >
                    <FormItem_Shadcn_ asChild>
                      <FormControl_Shadcn_>
                        <RadioGroupStackedItem
                          value="false"
                          // @ts-ignore
                          label={
                            <>
                              Use public schema for Data API
                              <Badge color="scale" className="ml-2">
                                Default
                              </Badge>
                            </>
                          }
                          // @ts-ignore
                          description={
                            <>
                              Query all tables in the <code className="text-xs">public</code> schema
                            </>
                          }
                          className="[&>div>div>p]:text-left [&>div>div>p]:text-xs"
                        />
                      </FormControl_Shadcn_>
                    </FormItem_Shadcn_>
                    <FormItem_Shadcn_ asChild>
                      <FormControl_Shadcn_>
                        <RadioGroupStackedItem
                          value="true"
                          label="Use dedicated API schema for Data API"
                          // @ts-ignore
                          description={
                            <>
                              Query allowlisted tables in a dedicated{' '}
                              <code className="text-xs">api</code> schema
                            </>
                          }
                          className="[&>div>div>p]:text-left [&>div>div>p]:text-xs"
                        />
                      </FormControl_Shadcn_>
                    </FormItem_Shadcn_>
                  </RadioGroupStacked>
                </FormControl_Shadcn_>
              </FormItemLayout>
            </>
          )}
        />
      )}
      <p className="text-xs text-foreground-lighter mt-3">
        These settings can be changed after the project is created via the project's settings
      </p>
    </>
  )

  const collapsibleContent = (
    <Collapsible_Shadcn_>
      <CollapsibleTrigger_Shadcn_ className="group/advanced-trigger font-mono uppercase tracking-widest text-xs flex items-center gap-1 text-foreground-lighter/75 hover:text-foreground-light transition data-[state=open]:text-foreground-light">
        Security options
        <ChevronRight
          size={16}
          strokeWidth={1}
          className="mr-2 group-data-[state=open]/advanced-trigger:rotate-90 group-hover/advanced-trigger:text-foreground-light transition"
        />
      </CollapsibleTrigger_Shadcn_>
      <CollapsibleContent_Shadcn_
        className={cn(
          'pt-5 data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down'
        )}
      >
        {content}
      </CollapsibleContent_Shadcn_>
    </Collapsible_Shadcn_>
  )

  return (
    <Panel.Content className={!collapsible ? 'p-0' : ''}>
      {collapsible ? collapsibleContent : content}
    </Panel.Content>
  )
}
