// [<PERSON><PERSON>] Obtained from https://gist.github.com/tadast/8827699
export const COUNTRY_LAT_LON = {
  AF: { lat: 33, lon: 65 },
  AX: { lat: 60.116667, lon: 19.9 },
  AL: { lat: 41, lon: 20 },
  DZ: { lat: 28, lon: 3 },
  AS: { lat: -14.3333, lon: -170 },
  AD: { lat: 42.5, lon: 1.6 },
  AO: { lat: -12.5, lon: 18.5 },
  AI: { lat: 18.25, lon: -63.1667 },
  AQ: { lat: -90, lon: 0 },
  AG: { lat: 17.05, lon: -61.8 },
  AR: { lat: -34, lon: -64 },
  AM: { lat: 40, lon: 45 },
  AW: { lat: 12.5, lon: -69.9667 },
  AU: { lat: -27, lon: 133 },
  AT: { lat: 47.3333, lon: 13.3333 },
  AZ: { lat: 40.5, lon: 47.5 },
  BS: { lat: 24.25, lon: -76 },
  BH: { lat: 26, lon: 50.55 },
  BD: { lat: 24, lon: 90 },
  BB: { lat: 13.1667, lon: -59.5333 },
  BY: { lat: 53, lon: 28 },
  BE: { lat: 50.8333, lon: 4 },
  BZ: { lat: 17.25, lon: -88.75 },
  BJ: { lat: 9.5, lon: 2.25 },
  BM: { lat: 32.3333, lon: -64.75 },
  BT: { lat: 27.5, lon: 90.5 },
  BO: { lat: -17, lon: -65 },
  BQ: { lat: 12.183333, lon: -68.233333 },
  BA: { lat: 44, lon: 18 },
  BW: { lat: -22, lon: 24 },
  BV: { lat: -54.4333, lon: 3.4 },
  BR: { lat: -10, lon: -55 },
  IO: { lat: -6, lon: 71.5 },
  BN: { lat: 4.5, lon: 114.6667 },
  BG: { lat: 43, lon: 25 },
  BF: { lat: 13, lon: -2 },
  MM: { lat: 22, lon: 98 },
  BI: { lat: -3.5, lon: 30 },
  KH: { lat: 13, lon: 105 },
  CM: { lat: 6, lon: 12 },
  CA: { lat: 60, lon: -95 },
  CV: { lat: 16, lon: -24 },
  KY: { lat: 19.5, lon: -80.5 },
  CF: { lat: 7, lon: 21 },
  TD: { lat: 15, lon: 19 },
  CL: { lat: -30, lon: -71 },
  CN: { lat: 35, lon: 105 },
  CX: { lat: -10.5, lon: 105.6667 },
  CC: { lat: -12.5, lon: 96.8333 },
  CO: { lat: 4, lon: -72 },
  KM: { lat: -12.1667, lon: 44.25 },
  CD: { lat: 0, lon: 25 },
  CG: { lat: -1, lon: 15 },
  CK: { lat: -21.2333, lon: -159.7667 },
  CR: { lat: 10, lon: -84 },
  CI: { lat: 8, lon: -5 },
  HR: { lat: 45.1667, lon: 15.5 },
  CU: { lat: 21.5, lon: -80 },
  CW: { lat: 12.166667, lon: -68.966667 },
  CY: { lat: 35, lon: 33 },
  CZ: { lat: 49.75, lon: 15.5 },
  DK: { lat: 56, lon: 10 },
  DJ: { lat: 11.5, lon: 43 },
  DM: { lat: 15.4167, lon: -61.3333 },
  DO: { lat: 19, lon: -70.6667 },
  EC: { lat: -2, lon: -77.5 },
  EG: { lat: 27, lon: 30 },
  SV: { lat: 13.8333, lon: -88.9167 },
  GQ: { lat: 2, lon: 10 },
  ER: { lat: 15, lon: 39 },
  EE: { lat: 59, lon: 26 },
  ET: { lat: 8, lon: 38 },
  FK: { lat: -51.75, lon: -59 },
  FO: { lat: 62, lon: -7 },
  FJ: { lat: -18, lon: 175 },
  FI: { lat: 64, lon: 26 },
  FR: { lat: 46, lon: 2 },
  GF: { lat: 4, lon: -53 },
  PF: { lat: -15, lon: -140 },
  TF: { lat: -43, lon: 67 },
  GA: { lat: -1, lon: 11.75 },
  GM: { lat: 13.4667, lon: -16.5667 },
  GE: { lat: 42, lon: 43.5 },
  DE: { lat: 51, lon: 9 },
  GH: { lat: 8, lon: -2 },
  GI: { lat: 36.1833, lon: -5.3667 },
  GR: { lat: 39, lon: 22 },
  GL: { lat: 72, lon: -40 },
  GD: { lat: 12.1167, lon: -61.6667 },
  GP: { lat: 16.25, lon: -61.5833 },
  GU: { lat: 13.4667, lon: 144.7833 },
  GT: { lat: 15.5, lon: -90.25 },
  GG: { lat: 49.5, lon: -2.56 },
  GW: { lat: 12, lon: -15 },
  GN: { lat: 11, lon: -10 },
  GY: { lat: 5, lon: -59 },
  HT: { lat: 19, lon: -72.4167 },
  HM: { lat: -53.1, lon: 72.5167 },
  VA: { lat: 41.9, lon: 12.45 },
  HN: { lat: 15, lon: -86.5 },
  HK: { lat: 22.25, lon: 114.1667 },
  HU: { lat: 47, lon: 20 },
  IS: { lat: 65, lon: -18 },
  IN: { lat: 20, lon: 77 },
  ID: { lat: -5, lon: 120 },
  IR: { lat: 32, lon: 53 },
  IQ: { lat: 33, lon: 44 },
  IE: { lat: 53, lon: -8 },
  IM: { lat: 54.23, lon: -4.55 },
  IL: { lat: 31.5, lon: 34.75 },
  IT: { lat: 42.8333, lon: 12.8333 },
  JM: { lat: 18.25, lon: -77.5 },
  JP: { lat: 36, lon: 138 },
  JE: { lat: 49.21, lon: -2.13 },
  JO: { lat: 31, lon: 36 },
  KZ: { lat: 48, lon: 68 },
  KE: { lat: 1, lon: 38 },
  KI: { lat: 1.4167, lon: 173 },
  KP: { lat: 40, lon: 127 },
  KR: { lat: 37, lon: 127.5 },
  XK: { lat: 42.583333, lon: 21 },
  KW: { lat: 29.3375, lon: 47.6581 },
  KG: { lat: 41, lon: 75 },
  LA: { lat: 18, lon: 105 },
  LV: { lat: 57, lon: 25 },
  LB: { lat: 33.8333, lon: 35.8333 },
  LS: { lat: -29.5, lon: 28.5 },
  LR: { lat: 6.5, lon: -9.5 },
  LY: { lat: 25, lon: 17 },
  LI: { lat: 47.1667, lon: 9.5333 },
  LT: { lat: 56, lon: 24 },
  LU: { lat: 49.75, lon: 6.1667 },
  MO: { lat: 22.1667, lon: 113.55 },
  MK: { lat: 41.8333, lon: 22 },
  MG: { lat: -20, lon: 47 },
  MW: { lat: -13.5, lon: 34 },
  MY: { lat: 2.5, lon: 112.5 },
  MV: { lat: 3.25, lon: 73 },
  ML: { lat: 17, lon: -4 },
  MT: { lat: 35.8333, lon: 14.5833 },
  MH: { lat: 9, lon: 168 },
  MQ: { lat: 14.6667, lon: -61 },
  MR: { lat: 20, lon: -12 },
  MU: { lat: -20.2833, lon: 57.55 },
  YT: { lat: -12.8333, lon: 45.1667 },
  MX: { lat: 23, lon: -102 },
  FM: { lat: 6.9167, lon: 158.25 },
  MD: { lat: 47, lon: 29 },
  MC: { lat: 43.7333, lon: 7.4 },
  MN: { lat: 46, lon: 105 },
  ME: { lat: 42, lon: 19 },
  MS: { lat: 16.75, lon: -62.2 },
  MA: { lat: 32, lon: -5 },
  MZ: { lat: -18.25, lon: 35 },
  NA: { lat: -22, lon: 17 },
  NR: { lat: -0.5333, lon: 166.9167 },
  NP: { lat: 28, lon: 84 },
  AN: { lat: 12.25, lon: -68.75 },
  NL: { lat: 52.5, lon: 5.75 },
  NC: { lat: -21.5, lon: 165.5 },
  NZ: { lat: -41, lon: 174 },
  NI: { lat: 13, lon: -85 },
  NE: { lat: 16, lon: 8 },
  NG: { lat: 10, lon: 8 },
  NU: { lat: -19.0333, lon: -169.8667 },
  NF: { lat: -29.0333, lon: 167.95 },
  MP: { lat: 15.2, lon: 145.75 },
  NO: { lat: 62, lon: 10 },
  OM: { lat: 21, lon: 57 },
  PK: { lat: 30, lon: 70 },
  PW: { lat: 7.5, lon: 134.5 },
  PS: { lat: 32, lon: 35.25 },
  PA: { lat: 9, lon: -80 },
  PG: { lat: -6, lon: 147 },
  PY: { lat: -23, lon: -58 },
  PE: { lat: -10, lon: -76 },
  PH: { lat: 13, lon: 122 },
  PN: { lat: -24.7, lon: -127.4 },
  PL: { lat: 52, lon: 20 },
  PT: { lat: 39.5, lon: -8 },
  PR: { lat: 18.25, lon: -66.5 },
  QA: { lat: 25.5, lon: 51.25 },
  RE: { lat: -21.1, lon: 55.6 },
  RO: { lat: 46, lon: 25 },
  RU: { lat: 60, lon: 100 },
  RW: { lat: -2, lon: 30 },
  BL: { lat: 17.897728, lon: -62.834244 },
  SH: { lat: -15.9333, lon: -5.7 },
  KN: { lat: 17.3333, lon: -62.75 },
  LC: { lat: 13.8833, lon: -61.1333 },
  MF: { lat: 18.075278, lon: -63.06 },
  PM: { lat: 46.8333, lon: -56.3333 },
  VC: { lat: 13.25, lon: -61.2 },
  WS: { lat: -13.5833, lon: -172.3333 },
  SM: { lat: 43.7667, lon: 12.4167 },
  ST: { lat: 1, lon: 7 },
  SA: { lat: 25, lon: 45 },
  SN: { lat: 14, lon: -14 },
  RS: { lat: 44, lon: 21 },
  SC: { lat: -4.5833, lon: 55.6667 },
  SL: { lat: 8.5, lon: -11.5 },
  SG: { lat: 1.3667, lon: 103.8 },
  SX: { lat: 18.033333, lon: -63.05 },
  SK: { lat: 48.6667, lon: 19.5 },
  SI: { lat: 46, lon: 15 },
  SB: { lat: -8, lon: 159 },
  SO: { lat: 10, lon: 49 },
  ZA: { lat: -29, lon: 24 },
  GS: { lat: -54.5, lon: -37 },
  SS: { lat: 8, lon: 30 },
  ES: { lat: 40, lon: -4 },
  LK: { lat: 7, lon: 81 },
  SD: { lat: 15, lon: 30 },
  SR: { lat: 4, lon: -56 },
  SJ: { lat: 78, lon: 20 },
  SZ: { lat: -26.5, lon: 31.5 },
  SE: { lat: 62, lon: 15 },
  CH: { lat: 47, lon: 8 },
  SY: { lat: 35, lon: 38 },
  TW: { lat: 23.5, lon: 121 },
  TJ: { lat: 39, lon: 71 },
  TZ: { lat: -6, lon: 35 },
  TH: { lat: 15, lon: 100 },
  TL: { lat: -8.55, lon: 125.5167 },
  TG: { lat: 8, lon: 1.1667 },
  TK: { lat: -9, lon: -172 },
  TO: { lat: -20, lon: -175 },
  TT: { lat: 11, lon: -61 },
  TN: { lat: 34, lon: 9 },
  TR: { lat: 39, lon: 35 },
  TM: { lat: 40, lon: 60 },
  TC: { lat: 21.75, lon: -71.5833 },
  TV: { lat: -8, lon: 178 },
  UG: { lat: 1, lon: 32 },
  UA: { lat: 49, lon: 32 },
  AE: { lat: 24, lon: 54 },
  GB: { lat: 54, lon: -2 },
  UM: { lat: 19.2833, lon: 166.6 },
  US: { lat: 38, lon: -97 },
  UY: { lat: -33, lon: -56 },
  UZ: { lat: 41, lon: 64 },
  VU: { lat: -16, lon: 167 },
  VE: { lat: 8, lon: -66 },
  VN: { lat: 16, lon: 106 },
  VG: { lat: 18.5, lon: -64.5 },
  VI: { lat: 18.3333, lon: -64.8333 },
  WF: { lat: -13.3, lon: -176.2 },
  EH: { lat: 24.5, lon: -13 },
  YE: { lat: 15, lon: 48 },
  ZM: { lat: -15, lon: 30 },
  ZW: { lat: -20, lon: 30 },
}

export const SPECIAL_CHARS_REGEX = /^[^@:\/]*$/
