// [<PERSON><PERSON>] Just FYI I do think that we should merge this with whats in SidePanelEditor.constants.ts
// Such that dashboard can work with _all_ native Postgres data types as listed here
// https://supabase.com/docs/guides/database/tables#data-types
// But for now, just make sure users cannot created enum types that share the same name as a native type

import {
  DATE_TYPES,
  JSON_TYPES,
  NUMERICAL_TYPES,
  OTHER_DATA_TYPES,
  TEXT_TYPES,
  TIMESTAMP_TYPES,
  TIME_TYPES,
} from 'components/interfaces/TableGridEditor/SidePanelEditor/SidePanelEditor.constants'

export const NATIVE_POSTGRES_TYPES = [
  ...NUMERICAL_TYPES,
  ...TEXT_TYPES,
  ...JSON_TYPES,
  ...TIMESTAMP_TYPES,
  ...TIME_TYPES,
  ...DATE_TYPES,
  ...OTHER_DATA_TYPES,
  'bigint',
  'bigserial',
  'bit',
  'bit varying',
  'boolean',
  'box',
  'bytea',
  'char',
  'character',
  'character varying',
  'cidr',
  'circle',
  'date',
  'double precision',
  'inet',
  'int',
  'integer',
  'interval',
  'line',
  'lseg',
  'macaddr',
  'macaddr8',
  'money',
  'numeric',
  'decimal',
  'path',
  'pg_lsn',
  'pg_snapshot',
  'point',
  'polygon',
  'real',
  'smallint',
  'smallserial',
  'serial',
  'time with timezone',
  'timestamp with timezone',
  'tsquery',
  'tsvector',
  'txid_snapshot',
  'xml',
]
