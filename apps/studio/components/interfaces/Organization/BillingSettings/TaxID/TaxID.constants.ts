// List of available Tax IDs as reflected in Stripe's web portal
// This was manually ported over so there may be a chance of mistakes
// Last updated as of 29th March 2022.
// The code may not necessarily match with the name (ref SE_VAT)
// https://stripe.com/docs/api/customer_tax_ids/create
export interface TaxId {
  name: string
  type: string
  country: string
  countryIso2: string
  placeholder: string
  vatPrefix?: string
}

export const TAX_IDS: TaxId[] = [
  {
    name: 'AE TRN',
    type: 'ae_trn',
    country: 'United Arab Emirates',
    placeholder: '***********2345',
    countryIso2: 'AE',
  },
  {
    name: 'AT VAT',
    type: 'eu_vat',
    country: 'Austria',
    placeholder: 'ATU12345678',
    vatPrefix: 'ATU',
    countryIso2: 'AT',
  },
  {
    name: 'AD NRT',
    type: 'ad_nrt',
    country: 'Andorra',
    placeholder: 'A-123456-Z',
    countryIso2: 'AD',
  },
  {
    name: 'AR CUIT',
    type: 'ar_cuit',
    country: 'Argentina',
    placeholder: '12-3456789-01',
    countryIso2: 'AR',
  },
  {
    name: 'AU ABN',
    type: 'au_abn',
    country: 'Australia',
    placeholder: '***********',
    countryIso2: 'AU',
  },
  {
    name: 'AU ARN',
    type: 'au_arn',
    country: 'Australia',
    placeholder: '***********3',
    countryIso2: 'AU',
  },
  {
    name: 'BE VAT',
    type: 'eu_vat',
    country: 'Belgium',
    placeholder: 'BE0*********',
    vatPrefix: 'BE',
    countryIso2: 'BE',
  },
  {
    name: 'BO TIN',
    type: 'bo_tin',
    country: 'Bolivia',
    placeholder: '*********',
    countryIso2: 'BO',
  },
  {
    name: 'BG VAT',
    type: 'eu_vat',
    country: 'Bulgaria',
    placeholder: 'BG0*********',
    vatPrefix: 'BG',
    countryIso2: 'BG',
  },
  {
    name: 'BR CNPJ',
    type: 'br_cnpj',
    country: 'Brazil',
    placeholder: '01.234.456/5432-10',
    countryIso2: 'BR',
  },
  {
    name: 'BR CPF',
    type: 'br_cpf',
    country: 'Brazil',
    placeholder: '123.456.789-87',
    countryIso2: 'BR',
  },
  {
    name: 'CA BN',
    type: 'ca_bn',
    country: 'Canada',
    placeholder: '*********',
    countryIso2: 'CA',
  },
  {
    name: 'CA GST/HST',
    type: 'ca_gst_hst',
    country: 'Canada',
    placeholder: '*********RT0002',
    countryIso2: 'CA',
  },
  {
    name: 'CA PST-BC',
    type: 'ca_pst_bc',
    country: 'Canada',
    placeholder: 'PST-1234-5678',
    countryIso2: 'CA',
  },
  {
    name: 'CA PST-MB',
    type: 'ca_pst_mb',
    country: 'Canada',
    placeholder: '123456-7',
    countryIso2: 'CA',
  },
  {
    name: 'CA PST-SK',
    type: 'ca_pst_mb',
    country: 'Canada',
    placeholder: '1234567',
    countryIso2: 'CA',
  },
  {
    name: 'CA QST',
    type: 'ca_qst',
    country: 'Canada',
    placeholder: '*********0TQ1234',
    countryIso2: 'CA',
  },
  {
    name: 'CH VAT',
    type: 'ch_vat',
    country: 'Switzerland',
    placeholder: 'CHE-123.456.789 MWST',
    vatPrefix: 'CHE',
    countryIso2: 'CH',
  },
  {
    name: 'CL TIN',
    type: 'cl_tin',
    country: 'Chile',
    placeholder: '12.345.678-K',
    countryIso2: 'CL',
  },
  {
    name: 'CN TIN',
    type: 'cn_tin',
    country: 'China',
    placeholder: '***********2345678',
    countryIso2: 'CN',
  },
  {
    name: 'CO NIT',
    type: 'co_nit',
    country: 'Colombia',
    placeholder: '123.456.789-0',
    countryIso2: 'CO',
  },
  {
    name: 'CR TIN',
    type: 'cr_tin',
    country: 'Costa Rica',
    placeholder: '1-234-567890',
    countryIso2: 'CR',
  },
  {
    name: 'CY VAT',
    type: 'eu_vat',
    country: 'Cyprus',
    placeholder: 'CY12345678Z',
    vatPrefix: 'CY',
    countryIso2: 'CY',
  },
  {
    name: 'CZ VAT',
    type: 'eu_vat',
    country: 'Czech Republic',
    placeholder: 'CZ*********0',
    vatPrefix: 'CZ',
    countryIso2: 'CZ',
  },
  {
    name: 'DE VAT',
    type: 'eu_vat',
    country: 'Germany',
    placeholder: 'DE*********',
    vatPrefix: 'DE',
    countryIso2: 'DE',
  },
  {
    name: 'DK VAT',
    type: 'eu_vat',
    country: 'Denmark',
    placeholder: 'DK12345678',
    vatPrefix: 'DK',
    countryIso2: 'DK',
  },
  {
    name: 'DO RCN',
    type: 'do_rcn',
    country: 'Dominican Republic',
    placeholder: '123-4567890-1',
    countryIso2: 'DO',
  },
  {
    name: 'EE VAT',
    type: 'eu_vat',
    country: 'Estonia',
    placeholder: 'EE*********',
    vatPrefix: 'EE',
    countryIso2: 'EE',
  },
  {
    name: 'ES CIF',
    type: 'es_cif',
    country: 'Spain',
    placeholder: 'A12345678',
    countryIso2: 'ES',
  },
  {
    name: 'ES VAT',
    type: 'eu_vat',
    country: 'Spain',
    placeholder: 'ESX1234567Z',
    vatPrefix: 'ES',
    countryIso2: 'ES',
  },
  {
    name: 'FI VAT',
    type: 'eu_vat',
    country: 'Finland',
    placeholder: 'FI12345678',
    vatPrefix: 'FI',
    countryIso2: 'FI',
  },
  {
    name: 'FR VAT',
    type: 'eu_vat',
    country: 'France',
    placeholder: 'FRAB*********',
    vatPrefix: 'FR',
    countryIso2: 'FR',
  },
  {
    name: 'GB VAT',
    type: 'eu_vat',
    country: 'United Kingdom',
    placeholder: 'GB*********',
    vatPrefix: 'GB',
    countryIso2: 'GB',
  },
  {
    name: 'GE VAT',
    type: 'ge_vat',
    country: 'Georgia',
    placeholder: '*********',
    countryIso2: 'GE',
  },
  {
    name: 'EL VAT',
    type: 'eu_vat',
    country: 'Greece',
    countryIso2: 'GR',
    placeholder: 'EL*********',
    vatPrefix: 'EL',
  },
  {
    name: 'HK BR',
    type: 'hk_br',
    country: 'Hong Kong SAR China',
    placeholder: '12345678',
    countryIso2: 'HK',
  },
  {
    name: 'HR VAT',
    type: 'eu_vat',
    country: 'Croatia',
    placeholder: 'HR***********',
    vatPrefix: 'HR',
    countryIso2: 'HR',
  },
  {
    name: 'HU VAT',
    type: 'eu_vat',
    country: 'Hungary',
    placeholder: 'HU***********',
    vatPrefix: 'HU',
    countryIso2: 'HU',
  },
  {
    name: 'ID NPWP',
    type: 'id_npwp',
    country: 'Indonesia',
    placeholder: '12.345.678.9-012.345',
    countryIso2: 'ID',
  },
  {
    name: 'IE VAT',
    type: 'eu_vat',
    country: 'Ireland',
    placeholder: 'IE1234567AB',
    vatPrefix: 'IE',
    countryIso2: 'IE',
  },
  {
    name: 'IL VAT',
    type: 'il_vat',
    country: 'Israel',
    placeholder: '000012345',
    countryIso2: 'IL',
  },
  {
    name: 'IN GST',
    type: 'in_gst',
    country: 'India',
    placeholder: '12ABCDE3456FGZH',
    countryIso2: 'IN',
  },
  {
    name: 'IS VAT',
    type: 'is_vat',
    country: 'Iceland',
    placeholder: '123456',
    countryIso2: 'IS',
  },
  {
    name: 'IT VAT',
    type: 'eu_vat',
    country: 'Italy',
    placeholder: 'IT***********',
    vatPrefix: 'IT',
    countryIso2: 'IT',
  },
  {
    name: 'JP CN',
    type: 'jp_cn',
    country: 'Japan',
    placeholder: '***********34',
    countryIso2: 'JP',
  },
  {
    name: 'KE PIN',
    type: 'ke_pin',
    country: 'Kenya',
    placeholder: 'P000111111A',
    countryIso2: 'KE',
  },
  {
    name: 'JP RN',
    type: 'jp_rn',
    country: 'Japan',
    placeholder: '12345',
    countryIso2: 'JP',
  },
  {
    name: 'KR BRN',
    type: 'kr_brn',
    country: 'Korea',
    placeholder: '123-45-67890',
    countryIso2: 'KR',
  },
  {
    name: 'LI UID',
    type: 'li_uid',
    country: 'Liechtenstein',
    placeholder: 'CHE*********',
    countryIso2: 'LI',
  },
  {
    name: 'LT VAT',
    type: 'eu_vat',
    country: 'Lithuania',
    placeholder: 'LT***********3',
    vatPrefix: 'LT',
    countryIso2: 'LT',
  },
  {
    name: 'LU VAT',
    type: 'eu_vat',
    country: 'Luxembourg',
    placeholder: 'LU12345678',
    vatPrefix: 'LU',
    countryIso2: 'LU',
  },
  {
    name: 'LV VAT',
    type: 'eu_vat',
    country: 'Latvia',
    placeholder: 'LV***********',
    vatPrefix: 'LV',
    countryIso2: 'LV',
  },
  {
    name: 'MT VAT',
    type: 'eu_vat',
    country: 'Malta',
    placeholder: 'MT12345678',
    vatPrefix: 'MT',
    countryIso2: 'MT',
  },
  {
    name: 'MX RFC',
    type: 'mx_rfc',
    country: 'Mexico',
    placeholder: 'ABC010203AB9',
    countryIso2: 'MX',
  },
  {
    name: 'MY FRP',
    type: 'my_frp',
    country: 'Malaysia',
    placeholder: '12345678',
    countryIso2: 'MY',
  },
  {
    name: 'MY ITN',
    type: 'my_itn',
    country: 'Malaysia',
    placeholder: 'C *********0',
    countryIso2: 'MY',
  },
  {
    name: 'MY SST',
    type: 'my_sst',
    country: 'Malaysia',
    placeholder: 'A12-3456-78912345',
    countryIso2: 'MY',
  },
  {
    name: 'NL VAT',
    type: 'eu_vat',
    country: 'Netherlands',
    placeholder: 'NL*********B12',
    vatPrefix: 'NL',
    countryIso2: 'NL',
  },
  {
    name: 'NO VAT',
    type: 'no_vat',
    country: 'Norway',
    placeholder: '*********MVA',
    countryIso2: 'NO',
  },
  {
    name: 'PE RUC',
    type: 'pe_ruc',
    country: 'Peru',
    placeholder: '***********',
    countryIso2: 'PE',
  },
  {
    name: 'PH TIN',
    type: 'ph_tin',
    country: 'Philippines',
    placeholder: '***********2',
    countryIso2: 'PH',
  },
  {
    name: 'NZ GST',
    type: 'nz_gst',
    country: 'New Zealand',
    placeholder: '*********',
    countryIso2: 'NZ',
  },
  {
    name: 'PL VAT',
    type: 'eu_vat',
    country: 'Poland',
    placeholder: 'PL*********0',
    vatPrefix: 'PL',
    countryIso2: 'PL',
  },
  {
    name: 'PT VAT',
    type: 'eu_vat',
    country: 'Portugal',
    placeholder: 'PT*********',
    vatPrefix: 'PT',
    countryIso2: 'PT',
  },
  {
    name: 'RO VAT',
    type: 'eu_vat',
    country: 'Romania',
    placeholder: 'RO*********1',
    vatPrefix: 'RO',
    countryIso2: 'RO',
  },
  {
    name: 'RU INN',
    type: 'ru_inn',
    country: 'Russia',
    placeholder: '*********1',
    countryIso2: 'RU',
  },
  {
    name: 'RU KPP',
    type: 'ru_kpp',
    country: 'Russia',
    placeholder: '*********',
    countryIso2: 'RU',
  },
  {
    name: 'SA VAT',
    type: 'sa_vat',
    country: 'Saudi Arabia',
    placeholder: '***********2345',
    countryIso2: 'SA',
  },
  {
    name: 'RS PIB',
    type: 'rs_pib',
    country: 'Serbia',
    placeholder: '*********',
    countryIso2: 'RS',
  },
  {
    name: 'SE VAT',
    type: 'eu_vat',
    country: 'Sweden',
    placeholder: 'SE***********3',
    vatPrefix: 'SE',
    countryIso2: 'SE',
  },
  {
    name: 'SG GST',
    type: 'sg_gst',
    country: 'Singapore',
    placeholder: 'M12345678X',
    countryIso2: 'SG',
  },
  {
    name: 'SG UEN',
    type: 'sg_uen',
    country: 'Singapore',
    placeholder: '*********F',
    countryIso2: 'SG',
  },
  {
    name: 'SI VAT',
    type: 'eu_vat',
    country: 'Slovenia',
    placeholder: 'SI12345678',
    vatPrefix: 'SI',
    countryIso2: 'SI',
  },
  {
    name: 'SK VAT',
    type: 'eu_vat',
    country: 'Slovakia',
    placeholder: 'SK*********1',
    vatPrefix: 'SK',
    countryIso2: 'SK',
  },
  {
    name: 'TH VAT',
    type: 'th_vat',
    country: 'Thailand',
    placeholder: '***********34',
    countryIso2: 'TH',
  },
  {
    name: 'TR TIN',
    type: 'tr_tin',
    country: 'Turkey',
    placeholder: '0*********',
    countryIso2: 'TR',
  },
  {
    name: 'TW VAT',
    type: 'tw_vat',
    country: 'Taiwan',
    placeholder: '12345678',
    countryIso2: 'TW',
  },
  {
    name: 'UA VAT',
    type: 'ua_vat',
    country: 'Ukraine',
    placeholder: '*********',
    countryIso2: 'UA',
  },
  {
    name: 'US EIN',
    type: 'us_ein',
    country: 'United States',
    placeholder: '12-3456789',
    countryIso2: 'US',
  },
  {
    name: 'XI VAT',
    type: 'eu_vat',
    country: 'United Kingdom (Northern Ireland)',
    placeholder: 'XI*********',
    vatPrefix: 'XI',
    countryIso2: 'GB',
  },
  {
    name: 'ZA VAT',
    type: 'za_vat',
    country: 'South Africa',
    placeholder: '4*********',
    countryIso2: 'ZA',
  },
  {
    name: 'UY RUC',
    type: 'uy_ruc',
    country: 'Uruguay',
    placeholder: '***********2',
    countryIso2: 'UY',
  },
  {
    name: 'VE RIF',
    type: 've_rif',
    country: 'Venezuela',
    placeholder: 'A-12345678-9',
    countryIso2: 'VE',
  },
  {
    name: 'VN TIN',
    type: 'vn_tin',
    country: 'Vietnam',
    placeholder: '*********0',
    countryIso2: 'VN',
  },
]
