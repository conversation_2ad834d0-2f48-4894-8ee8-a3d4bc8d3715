import { cn } from 'ui'

const Checkbox = () => (
  <div className="w-[10%] h-full flex items-center border-r border-default px-2">
    <div className="w-3 h-3 rounded border border-control" />
  </div>
)

const Row = ({
  className,
  col1,
  col2,
  col3,
}: {
  className?: string
  col1: string
  col2: string
  col3: string
}) => (
  <div className={cn('h-[30px] flex items-center bg-studio border-b border-default', className)}>
    <Checkbox />
    <div className="w-[15%] h-full flex items-center border-r border-default px-2">
      <p className="text-xs">{col1}</p>
    </div>
    <div className="w-[45%] h-full flex items-center border-r border-default px-2">
      <p className="text-xs">{col2}</p>
    </div>
    <div className="w-[30%] h-full flex items-center px-2">
      <p className="text-xs">{col3}</p>
    </div>
  </div>
)

const GetStartedHero = () => {
  return (
    <div className="w-full max-w-[500px] h-full pb-10 lg:pb-0 relative pointer-events-none">
      <div
        className={cn(
          'w-[290px] lg:w-[400px] h-[180px] bg-alternative border border-default',
          'rounded-t px-4 py-3 space-y-1 overflow-hidden'
        )}
      >
        <div className="text-xs font-mono space-x-4 flex items-center">
          <span className="text-foreground-light">1</span>
          <p className="text-blue-900">
            create table <span className="text-foreground">todos {`(`}</span>
          </p>
        </div>
        <div className="text-xs font-mono space-x-8 flex items-center">
          <span className="text-foreground-light">2</span>
          <p className="text-foreground">
            id <span className="text-blue-900">bigint generated by default</span>,
          </p>
        </div>
        <div className="text-xs font-mono space-x-8 flex items-center">
          <span className="text-foreground-light">3</span>
          <p className="text-foreground">
            task <span className="text-blue-900">text</span>,
          </p>
        </div>
        <div className="text-xs font-mono space-x-8 flex items-center">
          <span className="text-foreground-light">4</span>
          <p className="text-foreground">
            status <span className="text-blue-900">status default 'Not Started'</span>,
          </p>
        </div>
        <div className="text-xs font-mono space-x-8 flex items-center">
          <span className="text-foreground-light">5</span>
          <p className="text-foreground">
            user_id <span className="text-blue-900">uuid references auth.users not null</span>,
          </p>
        </div>
        <div className="text-xs font-mono space-x-8 flex items-center">
          <span className="text-foreground-light">6</span>
          <p className="text-foreground">
            inserted_at <span className="text-blue-900">timestamp with time zone</span>,
          </p>
        </div>
        <div className="text-xs font-mono space-x-8 flex items-center">
          <span className="text-foreground-light">7</span>
          <p className="text-foreground">
            updated_at <span className="text-blue-900">timestamp with time zone</span>,
          </p>
        </div>
        <div className="text-xs font-mono space-x-4 flex items-center">
          <span className="text-foreground-light">8</span>
          <p className="text-blue-900">{`);`}</p>
        </div>
      </div>
      <div
        className={cn(
          'w-[260px] lg:w-[320px] h-[160px] lg:h-[220px] bg-surface-100 border border-default',
          'absolute right-0 top-[50px] lg:-top-[40px] rounded-t overflow-y-hidden'
        )}
      >
        <Row col1="id" col2="task" col3="status" className="h-[24px] bg-surface-100" />
        <Row col1="1" col2="Create a project" col3="Complete" />
        <Row col1="2" col2="Read documentation" col3="Complete" />
        <Row col1="3" col2="Build application" col3="In progress" />
        <Row col1="4" col2="Connect Supabase" col3="In progress" />
        <Row col1="5" col2="Deploy project" col3="Not started" />
        <Row col1="6" col2="Get users" col3="Not started" />
        <Row col1="7" col2="Upgrade to Pro" col3="Not started" />
      </div>
    </div>
  )
}

export default GetStartedHero
