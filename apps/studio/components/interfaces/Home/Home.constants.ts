export const CLIENT_LIBRARIES = [
  {
    language: 'JavaScript',
    officialSupport: true,
    releaseState: undefined,
    docsUrl: 'https://supabase.com/docs/reference/javascript/installing',
    gitUrl: 'https://github.com/supabase/supabase-js',
  },
  {
    language: 'Flutter',
    officialSupport: true,
    releaseState: undefined,
    docsUrl: 'https://supabase.com/docs/reference/dart/installing',
    gitUrl: 'https://github.com/supabase/supabase-flutter',
  },
  {
    language: 'Python',
    officialSupport: true,
    releaseState: 'Alpha',
    docsUrl: 'https://supabase.com/docs/reference/python/initializing',
    gitUrl: 'https://github.com/supabase/supabase-py',
  },
  {
    language: 'C#',
    officialSupport: false,
    releaseState: undefined,
    docsUrl: 'https://supabase.com/docs/reference/csharp/installing',
    gitUrl: 'https://github.com/supabase-community/supabase-csharp',
    altIconName: 'c-sharp',
  },
  {
    language: 'Swift',
    officialSupport: true,
    releaseState: undefined,
    docsUrl: 'https://supabase.com/docs/reference/swift/initializing',
    gitUrl: 'https://github.com/supabase/supabase-swift',
  },
  {
    language: 'Kotlin',
    officialSupport: false,
    releaseState: undefined,
    docsUrl: 'https://supabase.com/docs/reference/kotlin/installing',
    gitUrl: 'https://github.com/supabase-community/supabase-kt',
  },
]

export const EXAMPLE_PROJECTS = [
  {
    framework: 'Svelte',
    title: 'Svelte kanban board',
    description: 'A Trello clone using Supabase as the storage system.',
    url: 'https://github.com/joshnuss/supabase-kanban',
    type: 'app',
  },
  {
    framework: 'nextjs',
    title: 'Next.js Realtime chat app',
    description: 'Next.js Slack clone app using Supabase realtime subscriptions',
    url: 'https://github.com/supabase/supabase/tree/master/examples/slack-clone/nextjs-slack-clone',
    type: 'app',
  },
  {
    framework: 'nextjs',
    title: 'Next.js Subscription and Auth',
    description: 'The all-in-one starter kit for high-performance SaaS applications.',
    url: 'https://github.com/vercel/nextjs-subscription-payments',
    type: 'app',
  },
  {
    framework: 'Expo',
    title: 'Expo Starter',
    description: 'Template bottom tabs with auth flow (Typescript)',
    url: 'https://github.com/codingki/react-native-expo-template/tree/master/template-typescript-bottom-tabs-supabase-auth-flow',
    type: 'mobile',
  },
  {
    framework: 'NestJS',
    title: 'NestJS example',
    description: 'NestJS example using Supabase Auth',
    url: 'https://github.com/hiro1107/nestjs-supabase-auth',
    type: 'app',
  },
  {
    framework: 'React',
    title: 'React realtime chat app',
    description: 'Example app of real-time chat using supabase realtime api',
    url: 'https://github.com/shwosner/realtime-chat-supabase-react',
    type: 'app',
  },
  {
    framework: 'nextjs',
    title: 'Next.js todo list app',
    description: 'Next.js todo list example',
    url: 'https://github.com/supabase/supabase/tree/master/examples/todo-list/nextjs-todo-list',
    type: 'app',
  },
  {
    framework: 'Svelte',
    title: 'Svelte todo list app',
    description: 'Sveltejs todo with TailwindCSS and Snowpack',
    url: 'https://github.com/supabase/supabase/tree/master/examples/todo-list/sveltejs-todo-list',
    type: 'app',
  },
  {
    framework: 'Flutter',
    title: 'Flutter chat app',
    description: 'A Flutter chat app built with supabase-flutter',
    url: 'https://github.com/supabase-community/flutter-chat',
    type: 'mobile',
  },
  {
    framework: 'Swift',
    title: 'Swift user management app',
    description: 'Swift user management app using supabase-swift',
    url: 'https://github.com/supabase/supabase-swift/tree/main/Examples/UserManagement',
    type: 'mobile',
  },
  {
    framework: 'Swift',
    title: 'Swift Slack Clone',
    description: 'Swift Slack clone app using supabase-swift',
    url: 'https://github.com/supabase/supabase-swift/tree/main/Examples/SlackClone',
    type: 'mobile',
  },
  {
    framework: 'Flutter',
    title: 'Flutter MFA app',
    description: 'A Flutter app demonstrating how to implement MFA',
    url: 'https://github.com/supabase/supabase/tree/master/examples/auth/flutter-mfa',
  },
  {
    framework: 'NuxtJS',
    title: 'NuxtJS todo list app',
    description: 'NuxtJS todo app example',
    url: 'https://github.com/nuxt-modules/supabase/tree/main/demo',
  },
]
