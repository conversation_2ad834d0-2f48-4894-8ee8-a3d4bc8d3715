<?xml version="1.0" encoding="UTF-8"?>
<svg width="107px" height="112px" viewBox="0 0 107 112" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 59.1 (86144) - https://sketch.com -->
    <title>Group</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="32" height="32"></rect>
        <rect id="path-3" x="0" y="0" width="32" height="32"></rect>
        <rect id="path-5" x="0" y="0" width="32" height="32"></rect>
        <rect id="path-7" x="0" y="0" width="32" height="32"></rect>
        <rect id="path-9" x="0" y="0" width="32" height="32"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Group" transform="translate(0.725557, -11.000000)">
            <polygon id="Rectangle" fill="#404040" points="17.2744429 66 54.2744429 53.2768099 91.2744429 66 54.2744429 78.4696017"></polygon>
            <polygon id="Rectangle" fill="#404040" points="17.2744429 66 54.2744429 53.2768099 54.2744429 78.4696017"></polygon>
            <polygon id="Rectangle" fill="#9A9A9A" points="-1.6795256e-13 51.6636125 38.5238299 39.4235592 54.2744429 53.2768099 17.2744429 65.8732058"></polygon>
            <polygon id="Rectangle" fill="#8B8B8B" points="69.6714788 41.0959162 105.129824 52.6483825 91.2744429 65.8732058 54.5488858 53.4496466"></polygon>
            <g id="image-filled" transform="translate(35.774443, 64.750000) rotate(-13.000000) translate(-35.774443, -64.750000) translate(18.274443, 49.000000)" fill-rule="nonzero">
                <rect id="Rectangle" stroke="#727272" fill="#888888" x="0" y="0" width="35" height="30.625" rx="2"></rect>
                <path d="M29.716563,12.2570055 L35,17.0602671 L35,17.0602671 L35,27.7083333 C35,29.3191639 33.6941639,30.625 32.0833333,30.625 L3.09610653,30.625 L3.09610653,30.625 L11.8121678,19.1480546 C12.713849,17.9607584 14.3657745,17.6445878 15.6421544,18.4150136 L18.9583333,20.4166667 L18.9583333,20.4166667 L25.5136882,12.5482153 C26.5447601,11.3106105 28.383886,11.1431825 29.6214908,12.1742544 C29.6537739,12.20115 29.6854721,12.2287402 29.716563,12.2570055 Z" id="Rectangle" fill="#727272"></path>
                <circle id="Oval" fill="#727272" cx="10.2083333" cy="10.2083333" r="4.375"></circle>
            </g>
            <g id="video-filled" transform="translate(49.774443, 75.312500) rotate(-7.000000) translate(-49.774443, -75.312500) translate(32.274443, 60.000000)" fill-rule="nonzero">
                <rect id="Rectangle" fill="#888888" x="0" y="0" width="35" height="30.625" rx="2"></rect>
                <path d="M19.3614068,11.9160992 L22.8351072,18.8635001 C23.1952999,19.5838854 22.903306,20.4598668 22.1829207,20.8200596 C21.9804232,20.9213083 21.7571333,20.9740199 21.5307342,20.9740199 L14.5833333,20.9740199 C13.777918,20.9740199 13.125,20.3211018 13.125,19.5156866 C13.125,19.2892875 13.1777116,19.0659975 13.2789603,18.8635001 L16.7526608,11.9160992 C17.1128535,11.1957139 17.9888349,10.9037201 18.7092203,11.2639127 C18.9914477,11.4050265 19.220293,11.6338718 19.3614068,11.9160992 Z" id="Triangle" fill="#727272" transform="translate(18.057193, 16.041826) rotate(90.000000) translate(-18.057193, -16.041826) "></path>
                <path d="M32.0833333,0 C33.6941639,0 35,1.30583615 35,2.91666667 L35,27.7083333 C35,29.3191639 33.6941639,30.625 32.0833333,30.625 L2.91666667,30.625 C1.30583615,30.625 0,29.3191639 0,27.7083333 L0,2.91666667 C0,1.30583615 1.30583615,0 2.91666667,0 L32.0833333,0 Z M26.25,1.45833333 L8.75,1.45833333 C7.21238904,1.45833333 5.95267587,2.64815511 5.84133337,4.15732575 L5.83333333,4.375 L5.83333333,26.25 C5.83333333,27.787611 7.02315511,29.0473241 8.53232575,29.1586667 L8.75,29.1666667 L26.25,29.1666667 C27.787611,29.1666667 29.0473241,27.9768449 29.1586667,26.4676742 L29.1666667,26.25 L29.1666667,4.375 C29.1666667,2.76416948 27.8608305,1.45833333 26.25,1.45833333 Z M3.64583333,23.3333333 L2.1875,23.3333333 C1.78479238,23.3333333 1.45833333,23.6597923 1.45833333,24.0625 L1.45833333,24.0625 L1.45833333,28.4375 C1.45833333,28.8402077 1.78479238,29.1666667 2.1875,29.1666667 L2.1875,29.1666667 L3.64583333,29.1666667 C4.04854096,29.1666667 4.375,28.8402077 4.375,28.4375 L4.375,28.4375 L4.375,24.0625 C4.375,23.6597923 4.04854096,23.3333333 3.64583333,23.3333333 L3.64583333,23.3333333 Z M32.8125,23.3333333 L31.3541667,23.3333333 C30.951459,23.3333333 30.625,23.6597923 30.625,24.0625 L30.625,24.0625 L30.625,28.4375 C30.625,28.8402077 30.951459,29.1666667 31.3541667,29.1666667 L31.3541667,29.1666667 L32.8125,29.1666667 C33.2152077,29.1666667 33.5416667,28.8402077 33.5416667,28.4375 L33.5416667,28.4375 L33.5416667,24.0625 C33.5416667,23.6597923 33.2152077,23.3333333 32.8125,23.3333333 L32.8125,23.3333333 Z M3.64583333,16.0416667 L2.1875,16.0416667 C1.78479238,16.0416667 1.45833333,16.3681257 1.45833333,16.7708333 L1.45833333,16.7708333 L1.45833333,21.1458333 C1.45833333,21.548541 1.78479238,21.875 2.1875,21.875 L2.1875,21.875 L3.64583333,21.875 C4.04854096,21.875 4.375,21.548541 4.375,21.1458333 L4.375,21.1458333 L4.375,16.7708333 C4.375,16.3681257 4.04854096,16.0416667 3.64583333,16.0416667 L3.64583333,16.0416667 Z M32.8125,16.0416667 L31.3541667,16.0416667 C30.951459,16.0416667 30.625,16.3681257 30.625,16.7708333 L30.625,16.7708333 L30.625,21.1458333 C30.625,21.548541 30.951459,21.875 31.3541667,21.875 L31.3541667,21.875 L32.8125,21.875 C33.2152077,21.875 33.5416667,21.548541 33.5416667,21.1458333 L33.5416667,21.1458333 L33.5416667,16.7708333 C33.5416667,16.3681257 33.2152077,16.0416667 32.8125,16.0416667 L32.8125,16.0416667 Z M3.64583333,8.75 L2.1875,8.75 C1.78479238,8.75 1.45833333,9.07645904 1.45833333,9.47916667 L1.45833333,9.47916667 L1.45833333,13.8541667 C1.45833333,14.2568743 1.78479238,14.5833333 2.1875,14.5833333 L2.1875,14.5833333 L3.64583333,14.5833333 C4.04854096,14.5833333 4.375,14.2568743 4.375,13.8541667 L4.375,13.8541667 L4.375,9.47916667 C4.375,9.07645904 4.04854096,8.75 3.64583333,8.75 L3.64583333,8.75 Z M32.8125,8.75 L31.3541667,8.75 C30.951459,8.75 30.625,9.07645904 30.625,9.47916667 L30.625,9.47916667 L30.625,13.8541667 C30.625,14.2568743 30.951459,14.5833333 31.3541667,14.5833333 L31.3541667,14.5833333 L32.8125,14.5833333 C33.2152077,14.5833333 33.5416667,14.2568743 33.5416667,13.8541667 L33.5416667,13.8541667 L33.5416667,9.47916667 C33.5416667,9.07645904 33.2152077,8.75 32.8125,8.75 L32.8125,8.75 Z M3.64583333,1.45833333 L2.1875,1.45833333 C1.78479238,1.45833333 1.45833333,1.78479238 1.45833333,2.1875 L1.45833333,2.1875 L1.45833333,6.5625 C1.45833333,6.96520762 1.78479238,7.29166667 2.1875,7.29166667 L2.1875,7.29166667 L3.64583333,7.29166667 C4.04854096,7.29166667 4.375,6.96520762 4.375,6.5625 L4.375,6.5625 L4.375,2.1875 C4.375,1.78479238 4.04854096,1.45833333 3.64583333,1.45833333 L3.64583333,1.45833333 Z M32.8125,1.45833333 L31.3541667,1.45833333 C30.951459,1.45833333 30.625,1.78479238 30.625,2.1875 L30.625,2.1875 L30.625,6.5625 C30.625,6.96520762 30.951459,7.29166667 31.3541667,7.29166667 L31.3541667,7.29166667 L32.8125,7.29166667 C33.2152077,7.29166667 33.5416667,6.96520762 33.5416667,6.5625 L33.5416667,6.5625 L33.5416667,2.1875 C33.5416667,1.78479238 33.2152077,1.45833333 32.8125,1.45833333 L32.8125,1.45833333 Z" id="Combined-Shape" fill="#727272"></path>
            </g>
            <g id="audio-filled" transform="translate(67.774443, 66.500000) rotate(11.000000) translate(-67.774443, -66.500000) translate(52.274443, 51.000000)" fill="#888888" fill-rule="nonzero" stroke="#6F6F6F">
                <path d="M30.4926751,2.15980127 L30.4926751,3.5883727 L30.4916751,3.60816221 L30.4926751,23.5883727 C30.4926751,26.6708922 28.0519127,29.1833412 24.9975752,29.2985321 L24.7783894,29.3026584 C21.6224766,29.3026584 19.0641037,26.7442856 19.0641037,23.5883727 C19.0641037,20.4324598 21.6224766,17.874087 24.7783894,17.874087 C25.2717358,17.874087 25.7504798,17.9366068 26.2071309,18.0541557 L26.2066751,5.28916221 L11.9206751,6.19716221 L11.9212466,25.4455156 C11.9212466,25.5507127 11.9050029,25.6521158 11.874887,25.7473538 C11.5164582,28.5582509 9.11547274,30.7312298 6.20696085,30.7312298 C3.05104799,30.7312298 0.492675137,28.172857 0.492675137,25.0169441 C0.492675137,21.8610313 3.05104799,19.3026584 6.20696085,19.3026584 C6.70020715,19.3026584 7.17885708,19.3651529 7.63542453,19.4826557 L7.63553228,3.15980127 C7.63553228,2.60751652 8.08324753,2.15980127 8.63553228,2.15980127 L8.71874605,2.15826839 C8.82933837,2.12377779 8.94501944,2.10184869 9.06410371,2.09427389 L29.0641037,0.822099447 C29.8530819,0.771913551 30.4926751,1.37082306 30.4926751,2.15980127 Z" id="Combined-Shape"></path>
            </g>
            <polygon id="Rectangle" fill="#9A9A9A" points="17.2744429 66 54.2744429 78.4696017 54.2744429 122.469602 17.2744429 110"></polygon>
            <polygon id="Rectangle" fill="#B0B0B0" transform="translate(72.774443, 94.234801) scale(-1, 1) translate(-72.774443, -94.234801) " points="54.2744429 66 91.2744429 78.4696017 91.2744429 122.469602 54.2744429 110"></polygon>
            <polygon id="Rectangle" fill="#8B8B8B" points="17.2744429 66 54.2744429 78.4696017 37.3329548 93.2446257 1.31450406e-13 80.3759293"></polygon>
            <polygon id="Rectangle" fill="#9A9A9A" points="54.2744429 78.4696017 91.2744429 66 106.345958 79.6223129 68.2965835 92.098224"></polygon>
            <g id="file-filled" transform="translate(54.274443, 16.000000)" fill-rule="nonzero">
                <path d="M2.94117647,0 L14.7058824,0 L14.7058824,0 L25,10.2941176 L25,27.9411765 C25,29.5655434 23.6831904,30.8823529 22.0588235,30.8823529 L2.94117647,30.8823529 C1.31680956,30.8823529 0,29.5655434 0,27.9411765 L0,2.94117647 C0,1.31680956 1.31680956,0 2.94117647,0 Z" id="Rectangle" stroke="#6F6F6F" fill="#888888"></path>
                <path d="M14.7058824,0 L25,10.2941176 L17.6470588,10.2941176 C16.0226919,10.2941176 14.7058824,8.97730809 14.7058824,7.35294118 L14.7058824,0 L14.7058824,0 Z" id="Triangle" fill="#727272"></path>
            </g>
            <g id="closedhand" transform="translate(57.274443, 0.000000)">
                <g id="Clipped">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <g id="SVGID_1_"></g>
                    <path d="M12.6,13 C13.1,12.8 14,12.9 14.3,13.5 C14.5,14 14.7,14.7 14.7,14.6 C14.7,14.2 14.7,13.4 14.8,13 C14.9,12.7 15.1,12.4 15.5,12.3 C15.8,12.2 16.1,12.2 16.4,12.2 C16.7,12.3 17,12.5 17.2,12.7 C17.6,13.3 17.6,14.6 17.6,14.5 C17.7,14.2 17.7,13.3 17.9,12.9 C18,12.7 18.4,12.5 18.6,12.4 C18.9,12.3 19.3,12.3 19.6,12.4 C19.8,12.4 20.2,12.7 20.3,12.9 C20.5,13.2 20.6,14.2 20.7,14.6 C20.7,14.7 20.8,14.2 21,13.9 C21.4,13.3 22.8,13.1 22.9,14.5 C22.9,15.2 22.9,15.1 22.9,15.6 C22.9,16.1 22.9,16.4 22.9,16.8 C22.9,17.2 22.8,18.1 22.7,18.5 C22.6,18.8 22.3,19.5 22,19.9 C22,19.9 20.9,21.1 20.8,21.7 C20.7,22.3 20.7,22.3 20.7,22.7 C20.7,23.1 20.8,23.6 20.8,23.6 C20.8,23.6 20,23.7 19.6,23.6 C19.2,23.5 18.7,22.8 18.6,22.5 C18.4,22.2 18.1,22.2 17.9,22.5 C17.7,22.9 17.2,23.6 16.9,23.6 C16.2,23.7 14.8,23.6 13.8,23.6 C13.8,23.6 14,22.6 13.6,22.2 C13.3,21.9 12.8,21.4 12.5,21.1 L11.7,20.2 C11.4,19.8 10.7,19.3 10.5,18.2 C10.3,17.3 10.3,16.8 10.5,16.4 C10.7,16 11.2,15.8 11.4,15.8 C11.6,15.8 12.1,15.8 12.3,15.9 C12.5,16 12.6,16.1 12.8,16.3 C13,16.6 13.1,16.8 13,16.4 C12.9,16.1 12.7,15.8 12.6,15.4 C12.5,15 12.2,14.5 12.2,13.9 C11.7,13.9 11.8,13.3 12.6,13" id="Path" fill="#FFFFFF" fill-rule="nonzero" mask="url(#mask-2)"></path>
                </g>
                <g id="Clipped">
                    <mask id="mask-4" fill="white">
                        <use xlink:href="#path-3"></use>
                    </mask>
                    <g id="SVGID_1_"></g>
                    <path d="M12.6,13 C13.1,12.8 14,12.9 14.3,13.5 C14.5,14 14.7,14.7 14.7,14.6 C14.7,14.2 14.7,13.4 14.8,13 C14.9,12.7 15.1,12.4 15.5,12.3 C15.8,12.2 16.1,12.2 16.4,12.2 C16.7,12.3 17,12.5 17.2,12.7 C17.6,13.3 17.6,14.6 17.6,14.5 C17.7,14.2 17.7,13.3 17.9,12.9 C18,12.7 18.4,12.5 18.6,12.4 C18.9,12.3 19.3,12.3 19.6,12.4 C19.8,12.4 20.2,12.7 20.3,12.9 C20.5,13.2 20.6,14.2 20.7,14.6 C20.7,14.7 20.8,14.2 21,13.9 C21.4,13.3 22.8,13.1 22.9,14.5 C22.9,15.2 22.9,15.1 22.9,15.6 C22.9,16.1 22.9,16.4 22.9,16.8 C22.9,17.2 22.8,18.1 22.7,18.5 C22.6,18.8 22.3,19.5 22,19.9 C22,19.9 20.9,21.1 20.8,21.7 C20.7,22.3 20.7,22.3 20.7,22.7 C20.7,23.1 20.8,23.6 20.8,23.6 C20.8,23.6 20,23.7 19.6,23.6 C19.2,23.5 18.7,22.8 18.6,22.5 C18.4,22.2 18.1,22.2 17.9,22.5 C17.7,22.9 17.2,23.6 16.9,23.6 C16.2,23.7 14.8,23.6 13.8,23.6 C13.8,23.6 14,22.6 13.6,22.2 C13.3,21.9 12.8,21.4 12.5,21.1 L11.7,20.2 C11.4,19.8 10.7,19.3 10.5,18.2 C10.3,17.3 10.3,16.8 10.5,16.4 C10.7,16 11.2,15.8 11.4,15.8 C11.6,15.8 12.1,15.8 12.3,15.9 C12.5,16 12.6,16.1 12.8,16.3 C13,16.6 13.1,16.8 13,16.4 C12.9,16.1 12.7,15.8 12.6,15.4 C12.5,15 12.2,14.5 12.2,13.9 C11.7,13.9 11.8,13.3 12.6,13 Z" id="Path" stroke="#000000" stroke-width="0.75" stroke-linejoin="round" mask="url(#mask-4)"></path>
                </g>
                <g id="Clipped">
                    <mask id="mask-6" fill="white">
                        <use xlink:href="#path-5"></use>
                    </mask>
                    <g id="SVGID_1_"></g>
                    <line x1="19.6" y1="20.7" x2="19.6" y2="17.3" id="Path" stroke="#000000" stroke-width="0.75" stroke-linecap="round" mask="url(#mask-6)"></line>
                </g>
                <g id="Clipped">
                    <mask id="mask-8" fill="white">
                        <use xlink:href="#path-7"></use>
                    </mask>
                    <g id="SVGID_1_"></g>
                    <line x1="17.6" y1="20.7" x2="17.5" y2="17.3" id="Path" stroke="#000000" stroke-width="0.75" stroke-linecap="round" mask="url(#mask-8)"></line>
                </g>
                <g id="Clipped">
                    <mask id="mask-10" fill="white">
                        <use xlink:href="#path-9"></use>
                    </mask>
                    <g id="SVGID_1_"></g>
                    <line x1="15.6" y1="17.3" x2="15.6" y2="20.7" id="Path" stroke="#000000" stroke-width="0.75" stroke-linecap="round" mask="url(#mask-10)"></line>
                </g>
            </g>
        </g>
    </g>
</svg>