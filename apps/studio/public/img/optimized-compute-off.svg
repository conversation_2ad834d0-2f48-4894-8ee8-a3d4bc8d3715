<svg width="160" height="96" viewBox="0 0 160 96" fill="none" xmlns="http://www.w3.org/2000/svg">

<path d="M-1257 -1077H1833V-1079H-1257V-1077ZM1834 -1076V1741H1836V-1076H1834ZM1833 1742H-1257V1744H1833V1742ZM-1258 1741V-1076H-1260V1741H-1258ZM-1257 1742C-1257.55 1742 -1258 1741.55 -1258 1741H-1260C-1260 1742.66 -1258.66 1744 -1257 1744V1742ZM1834 1741C1834 1741.55 1833.55 1742 1833 1742V1744C1834.66 1744 1836 1742.66 1836 1741H1834ZM1833 -1077C1833.55 -1077 1834 -1076.55 1834 -1076H1836C1836 -1077.66 1834.66 -1079 1833 -1079V-1077ZM-1257 -1079C-1258.66 -1079 -1260 -1077.66 -1260 -1076H-1258C-1258 -1076.55 -1257.55 -1077 -1257 -1077V-1079Z" fill="white" fill-opacity="0.1"/>
<g clip-path="url(#clip0_105_14273)">
<path d="M165.974 49.8945L144.481 62.1762L165.974 74.4579L187.467 62.1762L165.974 49.8945Z" stroke="#282828"/>
<path d="M15.5204 62.3682L-5.97253 74.6499L15.5204 86.9315L37.0134 74.6499L15.5204 62.3682Z" stroke="#282828"/>
<path d="M37.0126 50.0864L15.5197 62.3681L37.0126 74.6498L58.5056 62.3681L37.0126 50.0864Z" stroke="#282828"/>
<path d="M15.5204 37.8047L-5.97253 50.0864L15.5204 62.3681L37.0134 50.0864L15.5204 37.8047Z" stroke="#282828"/>
<path d="M37.0126 25.5229L15.5197 37.8046L37.0126 50.0863L58.5056 37.8046L37.0126 25.5229Z" stroke="#282828"/>
<path d="M58.5087 62.3682L37.0157 74.6499L58.5087 86.9315L80.0017 74.6499L58.5087 62.3682Z" stroke="#282828"/>
<path d="M80.0009 50.0864L58.5079 62.3681L80.0009 74.6498L101.494 62.3681L80.0009 50.0864Z" stroke="#282828"/>
<path d="M58.5087 37.8047L37.0157 50.0864L58.5087 62.3681L80.0017 50.0864L58.5087 37.8047Z" stroke="#282828"/>
<path d="M101.493 62.368L80.0001 74.6496L101.493 86.9313L122.986 74.6496L101.493 62.368Z" stroke="#282828"/>
<path d="M80.0009 74.6494L58.5079 86.9311L80.0009 99.2128L101.494 86.9311L80.0009 74.6494Z" stroke="#282828"/>
<path d="M58.5087 86.9312L37.0157 99.2128L58.5087 111.495L80.0017 99.2128L58.5087 86.9312Z" stroke="#282828"/>
<path d="M37.0126 74.6494L15.5197 86.9311L37.0126 99.2128L58.5056 86.9311L37.0126 74.6494Z" stroke="#282828"/>
<path d="M80.0009 25.5229L58.5079 37.8046L80.0009 50.0863L101.494 37.8046L80.0009 25.5229Z" stroke="#282828"/>
<path d="M101.493 37.8046L80.0001 50.0863L101.493 62.3679L122.986 50.0863L101.493 37.8046Z" stroke="#282828"/>
<path d="M122.985 50.0864L101.492 62.3681L122.985 74.6498L144.478 62.3681L122.985 50.0864Z" stroke="#282828"/>
<path d="M-5.9717 50.0863L-27.4647 62.368L-5.9717 74.6497L15.5213 62.368L-5.9717 50.0863Z" stroke="#282828"/>
<path d="M58.5087 13.2412L37.0157 25.5229L58.5087 37.8046L80.0017 25.5229L58.5087 13.2412Z" stroke="#282828"/>
<path d="M80.0009 0.959473L58.5079 13.2412L80.0009 25.5229L101.494 13.2412L80.0009 0.959473Z" stroke="#282828"/>
<path d="M101.493 13.2412L80.0001 25.5229L101.493 37.8046L122.986 25.5229L101.493 13.2412Z" stroke="#282828"/>
<path d="M122.985 25.5229L101.492 37.8046L122.985 50.0863L144.478 37.8046L122.985 25.5229Z" stroke="#282828"/>
<path d="M144.477 37.8047L122.984 50.0864L144.477 62.3681L165.97 50.0864L144.477 37.8047Z" stroke="#282828"/>
<path d="M144.477 62.1763L122.984 74.458L144.477 86.7397L165.97 74.458L144.477 62.1763Z" stroke="#282828"/>
<path d="M122.985 74.6494L101.492 86.9311L122.985 99.2128L144.478 86.9311L122.985 74.6494Z" stroke="#282828"/>
<path d="M101.493 86.7394L80.0001 99.0211L101.493 111.303L122.986 99.0211L101.493 86.7394Z" stroke="#282828"/>
<g filter="url(#filter0_b_105_14273)">
<path d="M58.5089 50.2762L80.1111 62.7482L80.1111 75.2202L58.5089 62.7482L58.5089 50.2762Z" fill="#161616"/>
<path d="M58.5089 50.2762L80.1111 62.7482L80.1111 75.2202L58.5089 62.7482L58.5089 50.2762Z" stroke="#3E3E3E" stroke-linejoin="bevel"/>
</g>
<g filter="url(#filter1_b_105_14273)">
<path d="M80.1105 62.7489L101.713 50.2769L101.713 62.7487L80.1105 75.2207L80.1105 62.7489Z" fill="#161616"/>
<path d="M80.1105 62.7489L101.713 50.2769L101.713 62.7487L80.1105 75.2207L80.1105 62.7489Z" stroke="#3E3E3E" stroke-linejoin="bevel"/>
</g>
<g filter="url(#filter2_b_105_14273)">
<path d="M80.1144 37.8047L101.717 50.2767L80.1144 62.7487L58.5122 50.2767L80.1144 37.8047Z" fill="#161616"/>
<path d="M80.1144 37.8047L101.717 50.2767L80.1144 62.7487L58.5122 50.2767L80.1144 37.8047Z" stroke="#3E3E3E" stroke-linejoin="bevel"/>
</g>
</g>
<defs>
<filter id="filter0_b_105_14273" x="57.6901" y="49.3905" width="23.2397" height="26.7155" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="0.192876"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_105_14273"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_105_14273" result="shape"/>
</filter>
<filter id="filter1_b_105_14273" x="79.2917" y="49.3911" width="23.2397" height="26.7154" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="0.192876"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_105_14273"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_105_14273" result="shape"/>
</filter>
<filter id="filter2_b_105_14273" x="57.3077" y="36.7832" width="45.6133" height="26.987" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="0.385753"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_105_14273"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_105_14273" result="shape"/>
</filter>
<clipPath id="clip0_105_14273">
<rect width="160" height="96" fill="white"/>
</clipPath>
</defs>
</svg>
