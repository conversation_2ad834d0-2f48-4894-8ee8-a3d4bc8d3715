// @ts-nocheck
// This file is autogenerated by scripts/build-registry.ts
// Do not edit this file directly.
import * as React from "react"

export const Index: Record<string, any> = {
  "default": {
    "ConfirmationModal": {
      name: "ConfirmationModal",
      type: "components:fragment",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/../../packages/ui-patterns/Dialogs/ConfirmationModal")),
      source: "",
      files: ["registry/default//Dialogs/ConfirmationModal.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "TextConfirmModal": {
      name: "TextConfirmModal",
      type: "components:fragment",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/../../packages/ui-patterns/Dialogs/TextConfirmModal")),
      source: "",
      files: ["registry/default//Dialogs/TextConfirmModal.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "ConfirmDialog": {
      name: "ConfirmDialog",
      type: "components:fragment",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/../../packages/ui-patterns/Dialogs/ConfirmDialog")),
      source: "",
      files: ["registry/default//Dialogs/ConfirmDialog.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "accordion-demo": {
      name: "accordion-demo",
      type: "components:example",
      registryDependencies: ["accordion"],
      component: React.lazy(() => import("@/registry/default/example/accordion-demo")),
      source: "",
      files: ["registry/default/example/accordion-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "admonition-demo": {
      name: "admonition-demo",
      type: "components:example",
      registryDependencies: ["admonition"],
      component: React.lazy(() => import("@/registry/default/example/admonition-demo")),
      source: "",
      files: ["registry/default/example/admonition-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "admonition-warning": {
      name: "admonition-warning",
      type: "components:example",
      registryDependencies: ["admonition"],
      component: React.lazy(() => import("@/registry/default/example/admonition-warning")),
      source: "",
      files: ["registry/default/example/admonition-warning.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "admonition-destructive": {
      name: "admonition-destructive",
      type: "components:example",
      registryDependencies: ["admonition"],
      component: React.lazy(() => import("@/registry/default/example/admonition-destructive")),
      source: "",
      files: ["registry/default/example/admonition-destructive.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "alert-demo": {
      name: "alert-demo",
      type: "components:example",
      registryDependencies: ["alert"],
      component: React.lazy(() => import("@/registry/default/example/alert-demo")),
      source: "",
      files: ["registry/default/example/alert-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "alert-destructive": {
      name: "alert-destructive",
      type: "components:example",
      registryDependencies: ["alert"],
      component: React.lazy(() => import("@/registry/default/example/alert-destructive")),
      source: "",
      files: ["registry/default/example/alert-destructive.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "alert-dialog-demo": {
      name: "alert-dialog-demo",
      type: "components:example",
      registryDependencies: ["alert-dialog","button"],
      component: React.lazy(() => import("@/registry/default/example/alert-dialog-demo")),
      source: "",
      files: ["registry/default/example/alert-dialog-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "aspect-ratio-demo": {
      name: "aspect-ratio-demo",
      type: "components:example",
      registryDependencies: ["aspect-ratio"],
      component: React.lazy(() => import("@/registry/default/example/aspect-ratio-demo")),
      source: "",
      files: ["registry/default/example/aspect-ratio-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "avatar-demo": {
      name: "avatar-demo",
      type: "components:example",
      registryDependencies: ["avatar"],
      component: React.lazy(() => import("@/registry/default/example/avatar-demo")),
      source: "",
      files: ["registry/default/example/avatar-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "badge-demo": {
      name: "badge-demo",
      type: "components:example",
      registryDependencies: ["badge"],
      component: React.lazy(() => import("@/registry/default/example/badge-demo")),
      source: "",
      files: ["registry/default/example/badge-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "badge-destructive": {
      name: "badge-destructive",
      type: "components:example",
      registryDependencies: ["badge"],
      component: React.lazy(() => import("@/registry/default/example/badge-destructive")),
      source: "",
      files: ["registry/default/example/badge-destructive.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "badge-outline": {
      name: "badge-outline",
      type: "components:example",
      registryDependencies: ["badge"],
      component: React.lazy(() => import("@/registry/default/example/badge-outline")),
      source: "",
      files: ["registry/default/example/badge-outline.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "badge-secondary": {
      name: "badge-secondary",
      type: "components:example",
      registryDependencies: ["badge"],
      component: React.lazy(() => import("@/registry/default/example/badge-secondary")),
      source: "",
      files: ["registry/default/example/badge-secondary.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-demo": {
      name: "button-demo",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-demo")),
      source: "",
      files: ["registry/default/example/button-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-sizes": {
      name: "button-sizes",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-sizes")),
      source: "",
      files: ["registry/default/example/button-sizes.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-default": {
      name: "button-default",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-default")),
      source: "",
      files: ["registry/default/example/button-default.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-warning": {
      name: "button-warning",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-warning")),
      source: "",
      files: ["registry/default/example/button-warning.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-secondary": {
      name: "button-secondary",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-secondary")),
      source: "",
      files: ["registry/default/example/button-secondary.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-destructive": {
      name: "button-destructive",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-destructive")),
      source: "",
      files: ["registry/default/example/button-destructive.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-outline": {
      name: "button-outline",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-outline")),
      source: "",
      files: ["registry/default/example/button-outline.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-ghost": {
      name: "button-ghost",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-ghost")),
      source: "",
      files: ["registry/default/example/button-ghost.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-link": {
      name: "button-link",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-link")),
      source: "",
      files: ["registry/default/example/button-link.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-with-icon": {
      name: "button-with-icon",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-with-icon")),
      source: "",
      files: ["registry/default/example/button-with-icon.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-loading": {
      name: "button-loading",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-loading")),
      source: "",
      files: ["registry/default/example/button-loading.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-icon": {
      name: "button-icon",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-icon")),
      source: "",
      files: ["registry/default/example/button-icon.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "button-as-child": {
      name: "button-as-child",
      type: "components:example",
      registryDependencies: ["button"],
      component: React.lazy(() => import("@/registry/default/example/button-as-child")),
      source: "",
      files: ["registry/default/example/button-as-child.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "calendar-demo": {
      name: "calendar-demo",
      type: "components:example",
      registryDependencies: ["calendar"],
      component: React.lazy(() => import("@/registry/default/example/calendar-demo")),
      source: "",
      files: ["registry/default/example/calendar-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "calendar-form": {
      name: "calendar-form",
      type: "components:example",
      registryDependencies: ["calendar","form","popover"],
      component: React.lazy(() => import("@/registry/default/example/calendar-form")),
      source: "",
      files: ["registry/default/example/calendar-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "checkbox-demo": {
      name: "checkbox-demo",
      type: "components:example",
      registryDependencies: ["checkbox"],
      component: React.lazy(() => import("@/registry/default/example/checkbox-demo")),
      source: "",
      files: ["registry/default/example/checkbox-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "checkbox-disabled": {
      name: "checkbox-disabled",
      type: "components:example",
      registryDependencies: ["checkbox"],
      component: React.lazy(() => import("@/registry/default/example/checkbox-disabled")),
      source: "",
      files: ["registry/default/example/checkbox-disabled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "checkbox-form-multiple": {
      name: "checkbox-form-multiple",
      type: "components:example",
      registryDependencies: ["checkbox","form"],
      component: React.lazy(() => import("@/registry/default/example/checkbox-form-multiple")),
      source: "",
      files: ["registry/default/example/checkbox-form-multiple.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "checkbox-form-single": {
      name: "checkbox-form-single",
      type: "components:example",
      registryDependencies: ["checkbox","form"],
      component: React.lazy(() => import("@/registry/default/example/checkbox-form-single")),
      source: "",
      files: ["registry/default/example/checkbox-form-single.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "checkbox-with-text": {
      name: "checkbox-with-text",
      type: "components:example",
      registryDependencies: ["checkbox"],
      component: React.lazy(() => import("@/registry/default/example/checkbox-with-text")),
      source: "",
      files: ["registry/default/example/checkbox-with-text.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "collapsible-demo": {
      name: "collapsible-demo",
      type: "components:example",
      registryDependencies: ["collapsible"],
      component: React.lazy(() => import("@/registry/default/example/collapsible-demo")),
      source: "",
      files: ["registry/default/example/collapsible-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "combobox-demo": {
      name: "combobox-demo",
      type: "components:example",
      registryDependencies: ["command"],
      component: React.lazy(() => import("@/registry/default/example/combobox-demo")),
      source: "",
      files: ["registry/default/example/combobox-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "combobox-dropdown-menu": {
      name: "combobox-dropdown-menu",
      type: "components:example",
      registryDependencies: ["command","dropdown-menu","button"],
      component: React.lazy(() => import("@/registry/default/example/combobox-dropdown-menu")),
      source: "",
      files: ["registry/default/example/combobox-dropdown-menu.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "combobox-form": {
      name: "combobox-form",
      type: "components:example",
      registryDependencies: ["command","form"],
      component: React.lazy(() => import("@/registry/default/example/combobox-form")),
      source: "",
      files: ["registry/default/example/combobox-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "combobox-popover": {
      name: "combobox-popover",
      type: "components:example",
      registryDependencies: ["combobox","popover"],
      component: React.lazy(() => import("@/registry/default/example/combobox-popover")),
      source: "",
      files: ["registry/default/example/combobox-popover.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "combobox-responsive": {
      name: "combobox-responsive",
      type: "components:example",
      registryDependencies: ["combobox","popover","drawer"],
      component: React.lazy(() => import("@/registry/default/example/combobox-responsive")),
      source: "",
      files: ["registry/default/example/combobox-responsive.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "command-demo": {
      name: "command-demo",
      type: "components:example",
      registryDependencies: ["command"],
      component: React.lazy(() => import("@/registry/default/example/command-demo")),
      source: "",
      files: ["registry/default/example/command-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "command-dialog": {
      name: "command-dialog",
      type: "components:example",
      registryDependencies: ["command","dialog"],
      component: React.lazy(() => import("@/registry/default/example/command-dialog")),
      source: "",
      files: ["registry/default/example/command-dialog.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-demo": {
      name: "commandmenu-demo",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-demo")),
      source: "",
      files: ["registry/default/example/commandmenu-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-badge": {
      name: "commandmenu-badge",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-badge")),
      source: "",
      files: ["registry/default/example/commandmenu-badge.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-icon": {
      name: "commandmenu-icon",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-icon")),
      source: "",
      files: ["registry/default/example/commandmenu-icon.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-hidden": {
      name: "commandmenu-hidden",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-hidden")),
      source: "",
      files: ["registry/default/example/commandmenu-hidden.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-force": {
      name: "commandmenu-force",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-force")),
      source: "",
      files: ["registry/default/example/commandmenu-force.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-conditional": {
      name: "commandmenu-conditional",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-conditional")),
      source: "",
      files: ["registry/default/example/commandmenu-conditional.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-subpage": {
      name: "commandmenu-subpage",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-subpage")),
      source: "",
      files: ["registry/default/example/commandmenu-subpage.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "commandmenu-subpage-custom": {
      name: "commandmenu-subpage-custom",
      type: "components:example",
      registryDependencies: ["commandmenu"],
      component: React.lazy(() => import("@/registry/default/example/commandmenu-subpage-custom")),
      source: "",
      files: ["registry/default/example/commandmenu-subpage-custom.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "context-menu-demo": {
      name: "context-menu-demo",
      type: "components:example",
      registryDependencies: ["context-menu"],
      component: React.lazy(() => import("@/registry/default/example/context-menu-demo")),
      source: "",
      files: ["registry/default/example/context-menu-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "date-picker-demo": {
      name: "date-picker-demo",
      type: "components:example",
      registryDependencies: ["button","calendar","popover"],
      component: React.lazy(() => import("@/registry/default/example/date-picker-demo")),
      source: "",
      files: ["registry/default/example/date-picker-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "date-picker-form": {
      name: "date-picker-form",
      type: "components:example",
      registryDependencies: ["button","calendar","form","popover"],
      component: React.lazy(() => import("@/registry/default/example/date-picker-form")),
      source: "",
      files: ["registry/default/example/date-picker-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "date-picker-with-presets": {
      name: "date-picker-with-presets",
      type: "components:example",
      registryDependencies: ["button","calendar","popover","select"],
      component: React.lazy(() => import("@/registry/default/example/date-picker-with-presets")),
      source: "",
      files: ["registry/default/example/date-picker-with-presets.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "date-picker-with-range": {
      name: "date-picker-with-range",
      type: "components:example",
      registryDependencies: ["button","calendar","popover"],
      component: React.lazy(() => import("@/registry/default/example/date-picker-with-range")),
      source: "",
      files: ["registry/default/example/date-picker-with-range.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "dialog-demo": {
      name: "dialog-demo",
      type: "components:example",
      registryDependencies: ["dialog"],
      component: React.lazy(() => import("@/registry/default/example/dialog-demo")),
      source: "",
      files: ["registry/default/example/dialog-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "dialog-close-button": {
      name: "dialog-close-button",
      type: "components:example",
      registryDependencies: ["dialog","button"],
      component: React.lazy(() => import("@/registry/default/example/dialog-close-button")),
      source: "",
      files: ["registry/default/example/dialog-close-button.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "dialog-centered-off": {
      name: "dialog-centered-off",
      type: "components:example",
      registryDependencies: ["dialog","button"],
      component: React.lazy(() => import("@/registry/default/example/dialog-centered-off")),
      source: "",
      files: ["registry/default/example/dialog-centered-off.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "drawer-demo": {
      name: "drawer-demo",
      type: "components:example",
      registryDependencies: ["drawer"],
      component: React.lazy(() => import("@/registry/default/example/drawer-demo")),
      source: "",
      files: ["registry/default/example/drawer-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "drawer-dialog": {
      name: "drawer-dialog",
      type: "components:example",
      registryDependencies: ["drawer","dialog"],
      component: React.lazy(() => import("@/registry/default/example/drawer-dialog")),
      source: "",
      files: ["registry/default/example/drawer-dialog.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "dropdown-menu-demo": {
      name: "dropdown-menu-demo",
      type: "components:example",
      registryDependencies: ["dropdown-menu"],
      component: React.lazy(() => import("@/registry/default/example/dropdown-menu-demo")),
      source: "",
      files: ["registry/default/example/dropdown-menu-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "dropdown-menu-checkboxes": {
      name: "dropdown-menu-checkboxes",
      type: "components:example",
      registryDependencies: ["dropdown-menu","checkbox"],
      component: React.lazy(() => import("@/registry/default/example/dropdown-menu-checkboxes")),
      source: "",
      files: ["registry/default/example/dropdown-menu-checkboxes.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "dropdown-menu-radio-group": {
      name: "dropdown-menu-radio-group",
      type: "components:example",
      registryDependencies: ["dropdown-menu","radio-group"],
      component: React.lazy(() => import("@/registry/default/example/dropdown-menu-radio-group")),
      source: "",
      files: ["registry/default/example/dropdown-menu-radio-group.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "hover-card-demo": {
      name: "hover-card-demo",
      type: "components:example",
      registryDependencies: ["hover-card"],
      component: React.lazy(() => import("@/registry/default/example/hover-card-demo")),
      source: "",
      files: ["registry/default/example/hover-card-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-demo": {
      name: "input-demo",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-demo")),
      source: "",
      files: ["registry/default/example/input-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-disabled": {
      name: "input-disabled",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-disabled")),
      source: "",
      files: ["registry/default/example/input-disabled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-file": {
      name: "input-file",
      type: "components:example",
      registryDependencies: ["input"],
      component: React.lazy(() => import("@/registry/default/example/input-file")),
      source: "",
      files: ["registry/default/example/input-file.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-form": {
      name: "input-form",
      type: "components:example",
      registryDependencies: ["input","button","form"],
      component: React.lazy(() => import("@/registry/default/example/input-form")),
      source: "",
      files: ["registry/default/example/input-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-with-button": {
      name: "input-with-button",
      type: "components:example",
      registryDependencies: ["input","button"],
      component: React.lazy(() => import("@/registry/default/example/input-with-button")),
      source: "",
      files: ["registry/default/example/input-with-button.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-with-label": {
      name: "input-with-label",
      type: "components:example",
      registryDependencies: ["input","button","label"],
      component: React.lazy(() => import("@/registry/default/example/input-with-label")),
      source: "",
      files: ["registry/default/example/input-with-label.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-with-text": {
      name: "input-with-text",
      type: "components:example",
      registryDependencies: ["input","button","label"],
      component: React.lazy(() => import("@/registry/default/example/input-with-text")),
      source: "",
      files: ["registry/default/example/input-with-text.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-otp-demo": {
      name: "input-otp-demo",
      type: "components:example",
      registryDependencies: ["input-otp"],
      component: React.lazy(() => import("@/registry/default/example/input-otp-demo")),
      source: "",
      files: ["registry/default/example/input-otp-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-otp-pattern": {
      name: "input-otp-pattern",
      type: "components:example",
      registryDependencies: ["input-otp"],
      component: React.lazy(() => import("@/registry/default/example/input-otp-pattern")),
      source: "",
      files: ["registry/default/example/input-otp-pattern.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-otp-separator": {
      name: "input-otp-separator",
      type: "components:example",
      registryDependencies: ["input-otp"],
      component: React.lazy(() => import("@/registry/default/example/input-otp-separator")),
      source: "",
      files: ["registry/default/example/input-otp-separator.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-otp-controlled": {
      name: "input-otp-controlled",
      type: "components:example",
      registryDependencies: ["input-otp"],
      component: React.lazy(() => import("@/registry/default/example/input-otp-controlled")),
      source: "",
      files: ["registry/default/example/input-otp-controlled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "input-otp-form": {
      name: "input-otp-form",
      type: "components:example",
      registryDependencies: ["input-otp","form"],
      component: React.lazy(() => import("@/registry/default/example/input-otp-form")),
      source: "",
      files: ["registry/default/example/input-otp-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "label-demo": {
      name: "label-demo",
      type: "components:example",
      registryDependencies: ["label"],
      component: React.lazy(() => import("@/registry/default/example/label-demo")),
      source: "",
      files: ["registry/default/example/label-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "menubar-demo": {
      name: "menubar-demo",
      type: "components:example",
      registryDependencies: ["menubar"],
      component: React.lazy(() => import("@/registry/default/example/menubar-demo")),
      source: "",
      files: ["registry/default/example/menubar-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "navigation-menu-demo": {
      name: "navigation-menu-demo",
      type: "components:example",
      registryDependencies: ["navigation-menu"],
      component: React.lazy(() => import("@/registry/default/example/navigation-menu-demo")),
      source: "",
      files: ["registry/default/example/navigation-menu-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "navigation-menu-responsive": {
      name: "navigation-menu-responsive",
      type: "components:example",
      registryDependencies: ["navigation-menu"],
      component: React.lazy(() => import("@/registry/default/example/navigation-menu-responsive")),
      source: "",
      files: ["registry/default/example/navigation-menu-responsive.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "popover-demo": {
      name: "popover-demo",
      type: "components:example",
      registryDependencies: ["popover"],
      component: React.lazy(() => import("@/registry/default/example/popover-demo")),
      source: "",
      files: ["registry/default/example/popover-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "progress-demo": {
      name: "progress-demo",
      type: "components:example",
      registryDependencies: ["progress"],
      component: React.lazy(() => import("@/registry/default/example/progress-demo")),
      source: "",
      files: ["registry/default/example/progress-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-demo": {
      name: "radio-group-demo",
      type: "components:example",
      registryDependencies: ["radio-group"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-demo")),
      source: "",
      files: ["registry/default/example/radio-group-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-form": {
      name: "radio-group-form",
      type: "components:example",
      registryDependencies: ["radio-group","form"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-form")),
      source: "",
      files: ["registry/default/example/radio-group-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-stacked-demo": {
      name: "radio-group-stacked-demo",
      type: "components:example",
      registryDependencies: ["radio-group"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-stacked-demo")),
      source: "",
      files: ["registry/default/example/radio-group-stacked-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-stacked-form": {
      name: "radio-group-stacked-form",
      type: "components:example",
      registryDependencies: ["radio-group","form"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-stacked-form")),
      source: "",
      files: ["registry/default/example/radio-group-stacked-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-card-demo": {
      name: "radio-group-card-demo",
      type: "components:example",
      registryDependencies: ["radio-group"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-card-demo")),
      source: "",
      files: ["registry/default/example/radio-group-card-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-card-form": {
      name: "radio-group-card-form",
      type: "components:example",
      registryDependencies: ["radio-group","form"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-card-form")),
      source: "",
      files: ["registry/default/example/radio-group-card-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "radio-group-card-with-children": {
      name: "radio-group-card-with-children",
      type: "components:example",
      registryDependencies: ["radio-group","form"],
      component: React.lazy(() => import("@/registry/default/example/radio-group-card-with-children")),
      source: "",
      files: ["registry/default/example/radio-group-card-with-children.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "resizable-demo": {
      name: "resizable-demo",
      type: "components:example",
      registryDependencies: ["resizable"],
      component: React.lazy(() => import("@/registry/default/example/resizable-demo")),
      source: "",
      files: ["registry/default/example/resizable-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "resizable-demo-with-handle": {
      name: "resizable-demo-with-handle",
      type: "components:example",
      registryDependencies: ["resizable"],
      component: React.lazy(() => import("@/registry/default/example/resizable-demo-with-handle")),
      source: "",
      files: ["registry/default/example/resizable-demo-with-handle.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "resizable-vertical": {
      name: "resizable-vertical",
      type: "components:example",
      registryDependencies: ["resizable"],
      component: React.lazy(() => import("@/registry/default/example/resizable-vertical")),
      source: "",
      files: ["registry/default/example/resizable-vertical.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "resizable-handle": {
      name: "resizable-handle",
      type: "components:example",
      registryDependencies: ["resizable"],
      component: React.lazy(() => import("@/registry/default/example/resizable-handle")),
      source: "",
      files: ["registry/default/example/resizable-handle.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "scroll-area-demo": {
      name: "scroll-area-demo",
      type: "components:example",
      registryDependencies: ["scroll-area"],
      component: React.lazy(() => import("@/registry/default/example/scroll-area-demo")),
      source: "",
      files: ["registry/default/example/scroll-area-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "scroll-area-horizontal-demo": {
      name: "scroll-area-horizontal-demo",
      type: "components:example",
      registryDependencies: ["scroll-area"],
      component: React.lazy(() => import("@/registry/default/example/scroll-area-horizontal-demo")),
      source: "",
      files: ["registry/default/example/scroll-area-horizontal-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "select-demo": {
      name: "select-demo",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-demo")),
      source: "",
      files: ["registry/default/example/select-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "select-scrollable": {
      name: "select-scrollable",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-scrollable")),
      source: "",
      files: ["registry/default/example/select-scrollable.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "select-form": {
      name: "select-form",
      type: "components:example",
      registryDependencies: ["select"],
      component: React.lazy(() => import("@/registry/default/example/select-form")),
      source: "",
      files: ["registry/default/example/select-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "separator-demo": {
      name: "separator-demo",
      type: "components:example",
      registryDependencies: ["separator"],
      component: React.lazy(() => import("@/registry/default/example/separator-demo")),
      source: "",
      files: ["registry/default/example/separator-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "sheet-demo": {
      name: "sheet-demo",
      type: "components:example",
      registryDependencies: ["sheet"],
      component: React.lazy(() => import("@/registry/default/example/sheet-demo")),
      source: "",
      files: ["registry/default/example/sheet-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "sheet-side": {
      name: "sheet-side",
      type: "components:example",
      registryDependencies: ["sheet"],
      component: React.lazy(() => import("@/registry/default/example/sheet-side")),
      source: "",
      files: ["registry/default/example/sheet-side.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "skeleton-demo": {
      name: "skeleton-demo",
      type: "components:example",
      registryDependencies: ["skeleton"],
      component: React.lazy(() => import("@/registry/default/example/skeleton-demo")),
      source: "",
      files: ["registry/default/example/skeleton-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "skeleton-card": {
      name: "skeleton-card",
      type: "components:example",
      registryDependencies: ["skeleton"],
      component: React.lazy(() => import("@/registry/default/example/skeleton-card")),
      source: "",
      files: ["registry/default/example/skeleton-card.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "slider-demo": {
      name: "slider-demo",
      type: "components:example",
      registryDependencies: ["slider"],
      component: React.lazy(() => import("@/registry/default/example/slider-demo")),
      source: "",
      files: ["registry/default/example/slider-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "sonner-demo": {
      name: "sonner-demo",
      type: "components:example",
      registryDependencies: ["sonner"],
      component: React.lazy(() => import("@/registry/default/example/sonner-demo")),
      source: "",
      files: ["registry/default/example/sonner-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "sonner-types": {
      name: "sonner-types",
      type: "components:example",
      registryDependencies: ["sonner"],
      component: React.lazy(() => import("@/registry/default/example/sonner-types")),
      source: "",
      files: ["registry/default/example/sonner-types.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "sonner-upload": {
      name: "sonner-upload",
      type: "components:example",
      registryDependencies: ["sonner"],
      component: React.lazy(() => import("@/registry/default/example/sonner-upload")),
      source: "",
      files: ["registry/default/example/sonner-upload.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "switch-demo": {
      name: "switch-demo",
      type: "components:example",
      registryDependencies: ["switch"],
      component: React.lazy(() => import("@/registry/default/example/switch-demo")),
      source: "",
      files: ["registry/default/example/switch-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "switch-form": {
      name: "switch-form",
      type: "components:example",
      registryDependencies: ["switch","form"],
      component: React.lazy(() => import("@/registry/default/example/switch-form")),
      source: "",
      files: ["registry/default/example/switch-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "table-demo": {
      name: "table-demo",
      type: "components:example",
      registryDependencies: ["table"],
      component: React.lazy(() => import("@/registry/default/example/table-demo")),
      source: "",
      files: ["registry/default/example/table-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "tabs-demo": {
      name: "tabs-demo",
      type: "components:example",
      registryDependencies: ["tabs"],
      component: React.lazy(() => import("@/registry/default/example/tabs-demo")),
      source: "",
      files: ["registry/default/example/tabs-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "textarea-demo": {
      name: "textarea-demo",
      type: "components:example",
      registryDependencies: ["textarea"],
      component: React.lazy(() => import("@/registry/default/example/textarea-demo")),
      source: "",
      files: ["registry/default/example/textarea-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "textarea-disabled": {
      name: "textarea-disabled",
      type: "components:example",
      registryDependencies: ["textarea"],
      component: React.lazy(() => import("@/registry/default/example/textarea-disabled")),
      source: "",
      files: ["registry/default/example/textarea-disabled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "textarea-form": {
      name: "textarea-form",
      type: "components:example",
      registryDependencies: ["textarea","form"],
      component: React.lazy(() => import("@/registry/default/example/textarea-form")),
      source: "",
      files: ["registry/default/example/textarea-form.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "textarea-with-button": {
      name: "textarea-with-button",
      type: "components:example",
      registryDependencies: ["textarea","button"],
      component: React.lazy(() => import("@/registry/default/example/textarea-with-button")),
      source: "",
      files: ["registry/default/example/textarea-with-button.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "textarea-with-label": {
      name: "textarea-with-label",
      type: "components:example",
      registryDependencies: ["textarea","label"],
      component: React.lazy(() => import("@/registry/default/example/textarea-with-label")),
      source: "",
      files: ["registry/default/example/textarea-with-label.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "textarea-with-text": {
      name: "textarea-with-text",
      type: "components:example",
      registryDependencies: ["textarea","label"],
      component: React.lazy(() => import("@/registry/default/example/textarea-with-text")),
      source: "",
      files: ["registry/default/example/textarea-with-text.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-group-demo": {
      name: "toggle-group-demo",
      type: "components:example",
      registryDependencies: ["toggle-group"],
      component: React.lazy(() => import("@/registry/default/example/toggle-group-demo")),
      source: "",
      files: ["registry/default/example/toggle-group-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-group-disabled": {
      name: "toggle-group-disabled",
      type: "components:example",
      registryDependencies: ["toggle-group"],
      component: React.lazy(() => import("@/registry/default/example/toggle-group-disabled")),
      source: "",
      files: ["registry/default/example/toggle-group-disabled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-group-lg": {
      name: "toggle-group-lg",
      type: "components:example",
      registryDependencies: ["toggle-group"],
      component: React.lazy(() => import("@/registry/default/example/toggle-group-lg")),
      source: "",
      files: ["registry/default/example/toggle-group-lg.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-group-outline": {
      name: "toggle-group-outline",
      type: "components:example",
      registryDependencies: ["toggle-group"],
      component: React.lazy(() => import("@/registry/default/example/toggle-group-outline")),
      source: "",
      files: ["registry/default/example/toggle-group-outline.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-group-sm": {
      name: "toggle-group-sm",
      type: "components:example",
      registryDependencies: ["toggle-group"],
      component: React.lazy(() => import("@/registry/default/example/toggle-group-sm")),
      source: "",
      files: ["registry/default/example/toggle-group-sm.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-group-single": {
      name: "toggle-group-single",
      type: "components:example",
      registryDependencies: ["toggle-group"],
      component: React.lazy(() => import("@/registry/default/example/toggle-group-single")),
      source: "",
      files: ["registry/default/example/toggle-group-single.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-demo": {
      name: "toggle-demo",
      type: "components:example",
      registryDependencies: ["toggle"],
      component: React.lazy(() => import("@/registry/default/example/toggle-demo")),
      source: "",
      files: ["registry/default/example/toggle-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-disabled": {
      name: "toggle-disabled",
      type: "components:example",
      registryDependencies: ["toggle"],
      component: React.lazy(() => import("@/registry/default/example/toggle-disabled")),
      source: "",
      files: ["registry/default/example/toggle-disabled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-lg": {
      name: "toggle-lg",
      type: "components:example",
      registryDependencies: ["toggle"],
      component: React.lazy(() => import("@/registry/default/example/toggle-lg")),
      source: "",
      files: ["registry/default/example/toggle-lg.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-outline": {
      name: "toggle-outline",
      type: "components:example",
      registryDependencies: ["toggle"],
      component: React.lazy(() => import("@/registry/default/example/toggle-outline")),
      source: "",
      files: ["registry/default/example/toggle-outline.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-sm": {
      name: "toggle-sm",
      type: "components:example",
      registryDependencies: ["toggle"],
      component: React.lazy(() => import("@/registry/default/example/toggle-sm")),
      source: "",
      files: ["registry/default/example/toggle-sm.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toggle-with-text": {
      name: "toggle-with-text",
      type: "components:example",
      registryDependencies: ["toggle"],
      component: React.lazy(() => import("@/registry/default/example/toggle-with-text")),
      source: "",
      files: ["registry/default/example/toggle-with-text.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "tooltip-demo": {
      name: "tooltip-demo",
      type: "components:example",
      registryDependencies: ["tooltip"],
      component: React.lazy(() => import("@/registry/default/example/tooltip-demo")),
      source: "",
      files: ["registry/default/example/tooltip-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-blockquote": {
      name: "typography-blockquote",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-blockquote")),
      source: "",
      files: ["registry/default/example/typography-blockquote.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-demo": {
      name: "typography-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-demo")),
      source: "",
      files: ["registry/default/example/typography-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-h1": {
      name: "typography-h1",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-h1")),
      source: "",
      files: ["registry/default/example/typography-h1.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-h2": {
      name: "typography-h2",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-h2")),
      source: "",
      files: ["registry/default/example/typography-h2.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-h3": {
      name: "typography-h3",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-h3")),
      source: "",
      files: ["registry/default/example/typography-h3.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-h4": {
      name: "typography-h4",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-h4")),
      source: "",
      files: ["registry/default/example/typography-h4.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-inline-code": {
      name: "typography-inline-code",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-inline-code")),
      source: "",
      files: ["registry/default/example/typography-inline-code.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-large": {
      name: "typography-large",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-large")),
      source: "",
      files: ["registry/default/example/typography-large.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-lead": {
      name: "typography-lead",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-lead")),
      source: "",
      files: ["registry/default/example/typography-lead.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-list": {
      name: "typography-list",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-list")),
      source: "",
      files: ["registry/default/example/typography-list.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-muted": {
      name: "typography-muted",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-muted")),
      source: "",
      files: ["registry/default/example/typography-muted.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-p": {
      name: "typography-p",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-p")),
      source: "",
      files: ["registry/default/example/typography-p.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-small": {
      name: "typography-small",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-small")),
      source: "",
      files: ["registry/default/example/typography-small.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "typography-table": {
      name: "typography-table",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/typography-table")),
      source: "",
      files: ["registry/default/example/typography-table.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "mode-toggle": {
      name: "mode-toggle",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/mode-toggle")),
      source: "",
      files: ["registry/default/example/mode-toggle.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-demo": {
      name: "text-confirm-dialog-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-demo")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-with-info-alert": {
      name: "text-confirm-dialog-with-info-alert",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-with-info-alert")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-with-info-alert.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-with-warning-alert": {
      name: "text-confirm-dialog-with-warning-alert",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-with-warning-alert")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-with-warning-alert.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-with-destructive-alert": {
      name: "text-confirm-dialog-with-destructive-alert",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-with-destructive-alert")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-with-destructive-alert.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-with-size": {
      name: "text-confirm-dialog-with-size",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-with-size")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-with-size.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-with-children": {
      name: "text-confirm-dialog-with-children",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-with-children")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-with-children.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "text-confirm-dialog-with-cancel-button": {
      name: "text-confirm-dialog-with-cancel-button",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/text-confirm-dialog-with-cancel-button")),
      source: "",
      files: ["registry/default/example/text-confirm-dialog-with-cancel-button.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-demo": {
      name: "form-item-layout-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-demo")),
      source: "",
      files: ["registry/default/example/form-item-layout-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-with-select": {
      name: "form-item-layout-with-select",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-with-select")),
      source: "",
      files: ["registry/default/example/form-item-layout-with-select.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-with-horizontal": {
      name: "form-item-layout-with-horizontal",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-with-horizontal")),
      source: "",
      files: ["registry/default/example/form-item-layout-with-horizontal.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-with-switch": {
      name: "form-item-layout-with-switch",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-with-switch")),
      source: "",
      files: ["registry/default/example/form-item-layout-with-switch.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-with-checkbox": {
      name: "form-item-layout-with-checkbox",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-with-checkbox")),
      source: "",
      files: ["registry/default/example/form-item-layout-with-switch.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-with-checkbox-list": {
      name: "form-item-layout-with-checkbox-list",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-with-checkbox-list")),
      source: "",
      files: ["registry/default/example/form-item-layout-with-switch.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-before-label": {
      name: "form-item-layout-before-label",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-before-label")),
      source: "",
      files: ["registry/default/example/form-item-layout-before-label.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "form-item-layout-after-label": {
      name: "form-item-layout-after-label",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/form-item-layout-after-label")),
      source: "",
      files: ["registry/default/example/form-item-layout-after-label.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "info-tooltip-demo": {
      name: "info-tooltip-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/info-tooltip-demo")),
      source: "",
      files: ["registry/default/example/info-tooltip-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "inner-side-menu-demo": {
      name: "inner-side-menu-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/inner-side-menu-demo")),
      source: "",
      files: ["registry/default/example/inner-side-menu-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "inner-side-menu-static-titles": {
      name: "inner-side-menu-static-titles",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/inner-side-menu-static-titles")),
      source: "",
      files: ["registry/default/example/inner-side-menu-static-titles.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "inner-side-menu-loading": {
      name: "inner-side-menu-loading",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/inner-side-menu-loading")),
      source: "",
      files: ["registry/default/example/inner-side-menu-loading.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "inner-side-menu-multiple-sections": {
      name: "inner-side-menu-multiple-sections",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/inner-side-menu-multiple-sections")),
      source: "",
      files: ["registry/default/example/inner-side-menu-multiple-sections.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "inner-side-menu-empty": {
      name: "inner-side-menu-empty",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/inner-side-menu-empty")),
      source: "",
      files: ["registry/default/example/inner-side-menu-empty.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "inner-side-menu-with-search": {
      name: "inner-side-menu-with-search",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/inner-side-menu-with-search")),
      source: "",
      files: ["registry/default/example/inner-side-menu-with-search.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toc-demo": {
      name: "toc-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/toc-demo")),
      source: "",
      files: ["registry/default/example/toc-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "toc-single-demo": {
      name: "toc-single-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/toc-single-demo")),
      source: "",
      files: ["registry/default/example/toc-single-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-demo": {
      name: "multi-select-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-demo")),
      source: "",
      files: ["registry/default/example/multi-select-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-disabled": {
      name: "multi-select-disabled",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-disabled")),
      source: "",
      files: ["registry/default/example/multi-select-disabled.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-badge-limit-wrap": {
      name: "multi-select-badge-limit-wrap",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-badge-limit-wrap")),
      source: "",
      files: ["registry/default/example/multi-select-badge-limit-wrap.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-badge-limit": {
      name: "multi-select-badge-limit",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-badge-limit")),
      source: "",
      files: ["registry/default/example/multi-select-badge-limit.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-combobox": {
      name: "multi-select-combobox",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-combobox")),
      source: "",
      files: ["registry/default/example/multi-select-combobox.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-combobox-creatable": {
      name: "multi-select-combobox-creatable",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-combobox-creatable")),
      source: "",
      files: ["registry/default/example/multi-select-combobox-creatable.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-deletable-badge": {
      name: "multi-select-deletable-badge",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-deletable-badge")),
      source: "",
      files: ["registry/default/example/multi-select-deletable-badge.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "multi-select-inline-search-input": {
      name: "multi-select-inline-search-input",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/multi-select-inline-search-input")),
      source: "",
      files: ["registry/default/example/multi-select-inline-search-input.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "tree-view-demo": {
      name: "tree-view-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/tree-view-demo")),
      source: "",
      files: ["registry/default/example/tree-view-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "tree-view-edit": {
      name: "tree-view-edit",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/tree-view-edit")),
      source: "",
      files: ["registry/default/example/tree-view-edit.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "tree-view-directories": {
      name: "tree-view-directories",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/tree-view-directories")),
      source: "",
      files: ["registry/default/example/tree-view-directories.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "tree-view-multi-select": {
      name: "tree-view-multi-select",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/tree-view-multi-select")),
      source: "",
      files: ["registry/default/example/tree-view-multi-select.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "modal-demo": {
      name: "modal-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/modal-demo")),
      source: "",
      files: ["registry/default/example/modal-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "modal-aligned-footer": {
      name: "modal-aligned-footer",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/modal-aligned-footer")),
      source: "",
      files: ["registry/default/example/modal-aligned-footer.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "modal-custom-footer": {
      name: "modal-custom-footer",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/modal-custom-footer")),
      source: "",
      files: ["registry/default/example/modal-custom-footer.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "modal-hide-footer": {
      name: "modal-hide-footer",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/modal-hide-footer")),
      source: "",
      files: ["registry/default/example/modal-hide-footer.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "assistant-chat-demo": {
      name: "assistant-chat-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/assistant-chat-demo")),
      source: "",
      files: ["registry/default/example/assistant-chat-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "assistant-chat-commands": {
      name: "assistant-chat-commands",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/assistant-chat-commands")),
      source: "",
      files: ["registry/default/example/assistant-chat-commands.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "color-usage-surface-www-and-docs": {
      name: "color-usage-surface-www-and-docs",
      type: "docs:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/color-usage-surface-www-and-docs")),
      source: "",
      files: ["registry/default/example/color-usage-surface-www-and-docs.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "color-usage-surface-studio": {
      name: "color-usage-surface-studio",
      type: "docs:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/color-usage-surface-studio")),
      source: "",
      files: ["registry/default/example/color-usage-surface-studio.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "color-usage-surface-studio-frame": {
      name: "color-usage-surface-studio-frame",
      type: "docs:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/color-usage-surface-studio-frame")),
      source: "",
      files: ["registry/default/example/color-usage-surface-studio-frame.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-bar-demo": {
      name: "chart-bar-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/chart-bar-demo")),
      source: "",
      files: ["registry/default/example/chart-bar-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-bar-demo-grid": {
      name: "chart-bar-demo-grid",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/chart-bar-demo-grid")),
      source: "",
      files: ["registry/default/example/chart-bar-demo-grid.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-bar-demo-axis": {
      name: "chart-bar-demo-axis",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/chart-bar-demo-axis")),
      source: "",
      files: ["registry/default/example/chart-bar-demo-axis.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-bar-demo-tooltip": {
      name: "chart-bar-demo-tooltip",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/chart-bar-demo-tooltip")),
      source: "",
      files: ["registry/default/example/chart-bar-demo-tooltip.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-bar-demo-legend": {
      name: "chart-bar-demo-legend",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/chart-bar-demo-legend")),
      source: "",
      files: ["registry/default/example/chart-bar-demo-legend.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-tooltip-demo": {
      name: "chart-tooltip-demo",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/chart-tooltip-demo")),
      source: "",
      files: ["registry/default/example/chart-tooltip-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "expanding-textarea-demo": {
      name: "expanding-textarea-demo",
      type: "docs:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/expanding-textarea-demo")),
      source: "",
      files: ["registry/default/example/expanding-textarea-demo.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "logs-bar-chart": {
      name: "logs-bar-chart",
      type: "components:example",
      registryDependencies: undefined,
      component: React.lazy(() => import("@/registry/default/example/logs-bar-chart")),
      source: "",
      files: ["registry/default/example/logs-bar-chart.tsx"],
      category: "undefined",
      subcategory: "undefined",
      chunks: []
    },
    "chart-area-axes": {
      name: "chart-area-axes",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-axes")),
      source: "__registry__/default/block/chart-area-axes.tsx",
      files: ["registry/default/block/chart-area-axes.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-default": {
      name: "chart-area-default",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-default")),
      source: "__registry__/default/block/chart-area-default.tsx",
      files: ["registry/default/block/chart-area-default.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-gradient": {
      name: "chart-area-gradient",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-gradient")),
      source: "__registry__/default/block/chart-area-gradient.tsx",
      files: ["registry/default/block/chart-area-gradient.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-icons": {
      name: "chart-area-icons",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-icons")),
      source: "__registry__/default/block/chart-area-icons.tsx",
      files: ["registry/default/block/chart-area-icons.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-interactive": {
      name: "chart-area-interactive",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-interactive")),
      source: "__registry__/default/block/chart-area-interactive.tsx",
      files: ["registry/default/block/chart-area-interactive.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-legend": {
      name: "chart-area-legend",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-legend")),
      source: "__registry__/default/block/chart-area-legend.tsx",
      files: ["registry/default/block/chart-area-legend.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-linear": {
      name: "chart-area-linear",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-linear")),
      source: "__registry__/default/block/chart-area-linear.tsx",
      files: ["registry/default/block/chart-area-linear.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-stacked-expand": {
      name: "chart-area-stacked-expand",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-stacked-expand")),
      source: "__registry__/default/block/chart-area-stacked-expand.tsx",
      files: ["registry/default/block/chart-area-stacked-expand.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-stacked": {
      name: "chart-area-stacked",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-stacked")),
      source: "__registry__/default/block/chart-area-stacked.tsx",
      files: ["registry/default/block/chart-area-stacked.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-area-step": {
      name: "chart-area-step",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-area-step")),
      source: "__registry__/default/block/chart-area-step.tsx",
      files: ["registry/default/block/chart-area-step.tsx"],
      category: "Charts",
      subcategory: "Area",
      chunks: []
    },
    "chart-bar-active": {
      name: "chart-bar-active",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-active")),
      source: "__registry__/default/block/chart-bar-active.tsx",
      files: ["registry/default/block/chart-bar-active.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-default": {
      name: "chart-bar-default",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-default")),
      source: "__registry__/default/block/chart-bar-default.tsx",
      files: ["registry/default/block/chart-bar-default.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-horizontal": {
      name: "chart-bar-horizontal",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-horizontal")),
      source: "__registry__/default/block/chart-bar-horizontal.tsx",
      files: ["registry/default/block/chart-bar-horizontal.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-interactive": {
      name: "chart-bar-interactive",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-interactive")),
      source: "__registry__/default/block/chart-bar-interactive.tsx",
      files: ["registry/default/block/chart-bar-interactive.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-label-custom": {
      name: "chart-bar-label-custom",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-label-custom")),
      source: "__registry__/default/block/chart-bar-label-custom.tsx",
      files: ["registry/default/block/chart-bar-label-custom.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-label": {
      name: "chart-bar-label",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-label")),
      source: "__registry__/default/block/chart-bar-label.tsx",
      files: ["registry/default/block/chart-bar-label.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-mixed": {
      name: "chart-bar-mixed",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-mixed")),
      source: "__registry__/default/block/chart-bar-mixed.tsx",
      files: ["registry/default/block/chart-bar-mixed.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-multiple": {
      name: "chart-bar-multiple",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-multiple")),
      source: "__registry__/default/block/chart-bar-multiple.tsx",
      files: ["registry/default/block/chart-bar-multiple.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-negative": {
      name: "chart-bar-negative",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-negative")),
      source: "__registry__/default/block/chart-bar-negative.tsx",
      files: ["registry/default/block/chart-bar-negative.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-bar-stacked": {
      name: "chart-bar-stacked",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-bar-stacked")),
      source: "__registry__/default/block/chart-bar-stacked.tsx",
      files: ["registry/default/block/chart-bar-stacked.tsx"],
      category: "Charts",
      subcategory: "Bar",
      chunks: []
    },
    "chart-line-default": {
      name: "chart-line-default",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-default")),
      source: "__registry__/default/block/chart-line-default.tsx",
      files: ["registry/default/block/chart-line-default.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-dots-colors": {
      name: "chart-line-dots-colors",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-dots-colors")),
      source: "__registry__/default/block/chart-line-dots-colors.tsx",
      files: ["registry/default/block/chart-line-dots-colors.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-dots-custom": {
      name: "chart-line-dots-custom",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-dots-custom")),
      source: "__registry__/default/block/chart-line-dots-custom.tsx",
      files: ["registry/default/block/chart-line-dots-custom.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-dots": {
      name: "chart-line-dots",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-dots")),
      source: "__registry__/default/block/chart-line-dots.tsx",
      files: ["registry/default/block/chart-line-dots.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-interactive": {
      name: "chart-line-interactive",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-interactive")),
      source: "__registry__/default/block/chart-line-interactive.tsx",
      files: ["registry/default/block/chart-line-interactive.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-label-custom": {
      name: "chart-line-label-custom",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-label-custom")),
      source: "__registry__/default/block/chart-line-label-custom.tsx",
      files: ["registry/default/block/chart-line-label-custom.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-label": {
      name: "chart-line-label",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-label")),
      source: "__registry__/default/block/chart-line-label.tsx",
      files: ["registry/default/block/chart-line-label.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-linear": {
      name: "chart-line-linear",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-linear")),
      source: "__registry__/default/block/chart-line-linear.tsx",
      files: ["registry/default/block/chart-line-linear.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-multiple": {
      name: "chart-line-multiple",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-multiple")),
      source: "__registry__/default/block/chart-line-multiple.tsx",
      files: ["registry/default/block/chart-line-multiple.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-line-step": {
      name: "chart-line-step",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-line-step")),
      source: "__registry__/default/block/chart-line-step.tsx",
      files: ["registry/default/block/chart-line-step.tsx"],
      category: "Charts",
      subcategory: "Line",
      chunks: []
    },
    "chart-pie-donut-active": {
      name: "chart-pie-donut-active",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-donut-active")),
      source: "__registry__/default/block/chart-pie-donut-active.tsx",
      files: ["registry/default/block/chart-pie-donut-active.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-donut-text": {
      name: "chart-pie-donut-text",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-donut-text")),
      source: "__registry__/default/block/chart-pie-donut-text.tsx",
      files: ["registry/default/block/chart-pie-donut-text.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-donut": {
      name: "chart-pie-donut",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-donut")),
      source: "__registry__/default/block/chart-pie-donut.tsx",
      files: ["registry/default/block/chart-pie-donut.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-interactive": {
      name: "chart-pie-interactive",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-interactive")),
      source: "__registry__/default/block/chart-pie-interactive.tsx",
      files: ["registry/default/block/chart-pie-interactive.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-label-custom": {
      name: "chart-pie-label-custom",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-label-custom")),
      source: "__registry__/default/block/chart-pie-label-custom.tsx",
      files: ["registry/default/block/chart-pie-label-custom.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-label-list": {
      name: "chart-pie-label-list",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-label-list")),
      source: "__registry__/default/block/chart-pie-label-list.tsx",
      files: ["registry/default/block/chart-pie-label-list.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-label": {
      name: "chart-pie-label",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-label")),
      source: "__registry__/default/block/chart-pie-label.tsx",
      files: ["registry/default/block/chart-pie-label.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-legend": {
      name: "chart-pie-legend",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-legend")),
      source: "__registry__/default/block/chart-pie-legend.tsx",
      files: ["registry/default/block/chart-pie-legend.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-separator-none": {
      name: "chart-pie-separator-none",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-separator-none")),
      source: "__registry__/default/block/chart-pie-separator-none.tsx",
      files: ["registry/default/block/chart-pie-separator-none.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-simple": {
      name: "chart-pie-simple",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-simple")),
      source: "__registry__/default/block/chart-pie-simple.tsx",
      files: ["registry/default/block/chart-pie-simple.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-pie-stacked": {
      name: "chart-pie-stacked",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-pie-stacked")),
      source: "__registry__/default/block/chart-pie-stacked.tsx",
      files: ["registry/default/block/chart-pie-stacked.tsx"],
      category: "Charts",
      subcategory: "Pie",
      chunks: []
    },
    "chart-radar-default": {
      name: "chart-radar-default",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-default")),
      source: "__registry__/default/block/chart-radar-default.tsx",
      files: ["registry/default/block/chart-radar-default.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-dots": {
      name: "chart-radar-dots",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-dots")),
      source: "__registry__/default/block/chart-radar-dots.tsx",
      files: ["registry/default/block/chart-radar-dots.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-grid-circle-fill": {
      name: "chart-radar-grid-circle-fill",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-grid-circle-fill")),
      source: "__registry__/default/block/chart-radar-grid-circle-fill.tsx",
      files: ["registry/default/block/chart-radar-grid-circle-fill.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-grid-circle-no-lines": {
      name: "chart-radar-grid-circle-no-lines",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-grid-circle-no-lines")),
      source: "__registry__/default/block/chart-radar-grid-circle-no-lines.tsx",
      files: ["registry/default/block/chart-radar-grid-circle-no-lines.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-grid-circle": {
      name: "chart-radar-grid-circle",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-grid-circle")),
      source: "__registry__/default/block/chart-radar-grid-circle.tsx",
      files: ["registry/default/block/chart-radar-grid-circle.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-grid-custom": {
      name: "chart-radar-grid-custom",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-grid-custom")),
      source: "__registry__/default/block/chart-radar-grid-custom.tsx",
      files: ["registry/default/block/chart-radar-grid-custom.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-grid-fill": {
      name: "chart-radar-grid-fill",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-grid-fill")),
      source: "__registry__/default/block/chart-radar-grid-fill.tsx",
      files: ["registry/default/block/chart-radar-grid-fill.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-grid-none": {
      name: "chart-radar-grid-none",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-grid-none")),
      source: "__registry__/default/block/chart-radar-grid-none.tsx",
      files: ["registry/default/block/chart-radar-grid-none.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-icons": {
      name: "chart-radar-icons",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-icons")),
      source: "__registry__/default/block/chart-radar-icons.tsx",
      files: ["registry/default/block/chart-radar-icons.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-label-custom": {
      name: "chart-radar-label-custom",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-label-custom")),
      source: "__registry__/default/block/chart-radar-label-custom.tsx",
      files: ["registry/default/block/chart-radar-label-custom.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-legend": {
      name: "chart-radar-legend",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-legend")),
      source: "__registry__/default/block/chart-radar-legend.tsx",
      files: ["registry/default/block/chart-radar-legend.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-lines-only": {
      name: "chart-radar-lines-only",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-lines-only")),
      source: "__registry__/default/block/chart-radar-lines-only.tsx",
      files: ["registry/default/block/chart-radar-lines-only.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-multiple": {
      name: "chart-radar-multiple",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-multiple")),
      source: "__registry__/default/block/chart-radar-multiple.tsx",
      files: ["registry/default/block/chart-radar-multiple.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radar-radius": {
      name: "chart-radar-radius",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radar-radius")),
      source: "__registry__/default/block/chart-radar-radius.tsx",
      files: ["registry/default/block/chart-radar-radius.tsx"],
      category: "Charts",
      subcategory: "Radar",
      chunks: []
    },
    "chart-radial-grid": {
      name: "chart-radial-grid",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radial-grid")),
      source: "__registry__/default/block/chart-radial-grid.tsx",
      files: ["registry/default/block/chart-radial-grid.tsx"],
      category: "Charts",
      subcategory: "Radial",
      chunks: []
    },
    "chart-radial-label": {
      name: "chart-radial-label",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radial-label")),
      source: "__registry__/default/block/chart-radial-label.tsx",
      files: ["registry/default/block/chart-radial-label.tsx"],
      category: "Charts",
      subcategory: "Radial",
      chunks: []
    },
    "chart-radial-shape": {
      name: "chart-radial-shape",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radial-shape")),
      source: "__registry__/default/block/chart-radial-shape.tsx",
      files: ["registry/default/block/chart-radial-shape.tsx"],
      category: "Charts",
      subcategory: "Radial",
      chunks: []
    },
    "chart-radial-simple": {
      name: "chart-radial-simple",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radial-simple")),
      source: "__registry__/default/block/chart-radial-simple.tsx",
      files: ["registry/default/block/chart-radial-simple.tsx"],
      category: "Charts",
      subcategory: "Radial",
      chunks: []
    },
    "chart-radial-stacked": {
      name: "chart-radial-stacked",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radial-stacked")),
      source: "__registry__/default/block/chart-radial-stacked.tsx",
      files: ["registry/default/block/chart-radial-stacked.tsx"],
      category: "Charts",
      subcategory: "Radial",
      chunks: []
    },
    "chart-radial-text": {
      name: "chart-radial-text",
      type: "components:block",
      registryDependencies: ["card","chart"],
      component: React.lazy(() => import("@/registry/default/block/chart-radial-text")),
      source: "__registry__/default/block/chart-radial-text.tsx",
      files: ["registry/default/block/chart-radial-text.tsx"],
      category: "Charts",
      subcategory: "Radial",
      chunks: []
    },
  },
}
