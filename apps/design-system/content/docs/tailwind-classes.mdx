---
title: Tailwind Classes
description: Icons system breakdown. Copy values of Icons.
---

Supabase is migrating to use tailwind classes that use CSS properties.
This is to support the concept of theming, so that:

1. It is easy for the team to update the theme without changing 100s of files.
2. We could offer custom or additional themes in future.

## Primitives

We (the Supabase Design team ✨) define primitive color values using Figma Variables.

- `background`
- `foreground`
- `border`
- `brand`
- `destructive`
- `warning`

These values are exported from Figma as a .json file and transformed into tailwind utilities through some scripts under `packages/ui/internals/tokens`.

Any of these colors are available on any tailwind utility that accepts color. For example:

```jsx
// this uses the default foreground color
<span className="text-foreground"></span>

// this uses the 'light' foreground color
<span className="text-foreground-light"></span>

// this uses the default warning color
<span className="text-warning"></span>

// this uses the '500' warning color
<span className="text-warning-500"></span>
```

## Shorthands

We support shorthand classes for `background`, `foreground` and `border`. For example:

- `text-muted` is the same as `text-foreground-muted`
- `bg-surface` is the same as `bg-background-surface-100`
- `border-strong` is the same as `border-border-strong`

This will stop you from needing to 'double type' the same utlity name.

You might notice we use `foreground` and `background` as prefixes, this is only because it is a Design Token standard, where as tailwind uses `bg` and `text`.

## Mixing colors

Even with shorthands, remember you can use any color. For example, `foreground-light` can also be applied on borders and backgrounds as `border-foreground-light` and `bg-foreground-light`, if needed.
Similarly, background and border primitives can be used on other tailwind utilities.

```jsx
// use the text light color for some text
<span className="text-light"></span>

// we can use the same color for a background
<div className="bg-foreground-light">
    <span>I have the same color background as the left example</span>
</div>
```

## Opacity support

All the colors are generated using tailwind opacity, like `hsl(--background-default) / var(--tw-bg-opacity)`.
This means all the color options in our themes can support opacity rules. For example:

```jsx
<div className="flex gap-3">
  <div className="w-4 h-4 rounded-full bg-surface-300"></div>
  <div className="w-4 h-4 rounded-full bg-surface-300/90"></div>
  <div className="w-4 h-4 rounded-full bg-surface-300/80"></div>
  <div className="w-4 h-4 rounded-full bg-surface-300/75"></div>
  <div className="w-4 h-4 rounded-full bg-surface-300/50"></div>
</div>
```

## Usage

The following tailwind classes are a combination of tailwind utilities and our primitives. They're under development and are likely to change but most are now in use and will probably be permanent fixtures.

### Foreground (Text)

| Value                  | Usage                                  |
| ---------------------- | -------------------------------------- |
| `foreground-{DEFAULT}` | Default text (**DEFAULT** is optional) |
| `foreground-light`     | Light text                             |
| `foreground-lighter`   | Lighter text                           |
| `foreground-muted`     | Muted text                             |

Examples:

```jsx
<div className="text-foreground">/div>
<div className="text-foreground-light">/div>
<div className="text-foreground-lighter">/div>
<div className="text-foreground-muted">/div>
<div className="bg-foreground-light">/div>
```

---

### Background

| Value                         | Usage                                                        |
| ----------------------------- | ------------------------------------------------------------ |
| `background-{DEFAULT}`        | Main body background (**DEFAULT** is optional)               |
| `background-surface-100`      | Panels and surfaces on the same level of the main background |
| `background-surface-200`      | Surfaces that overlap the main content (ex. dropdowns)       |
| `background-surface-300`      | Surfaces that are stacked above background-surface-200       |
| `background-alternative`      | Alternative background (inverted)                            |
| `background-overlay`          | Overlays, Dropdowns, Popovers                                |
| `background-control`          | Inputs, Radios, Checkboxes                                   |
| `background-button-{DEFAULT}` | Button default                                               |

The `background` part can be omitted when used on the `bg` tailwind utility.

Examples:

```
bg-surface-100
bg-overlay
bg-alternative
text-background-surface-100
```

---

### Border

| Value                     | Usage                                    |
| ------------------------- | ---------------------------------------- |
| `border-{DEFAULT}`        | Default border (**DEFAULT** is optional) |
| `border-secondary`        | Secondary border                         |
| `border-alternative`      | Alternative border (inverted)            |
| `border-overlay`          | Overlays, Dropdowns, Popovers            |
| `border-control`          | Inputs, Radios, Checkboxes               |
| `border-strong`           | Hover, Focus                             |
| `border-stronger`         | Highlighted border                       |
| `border-button-{DEFAULT}` | Button default border                    |
| `border-button-hover`     | Button default border hover              |

Examples:

```
border-overlay
border-alternative
text-border-control
```

### Brand

| Value     |
| --------- |
| `200`     |
| `300`     |
| `400`     |
| `500`     |
| `DEFAULT` |
| `600`     |
| `button`  |

### Destructive

| Value     |
| --------- |
| `200`     |
| `300`     |
| `400`     |
| `500`     |
| `DEFAULT` |
| `600`     |

### Warning

| Value     |
| --------- |
| `200`     |
| `300`     |
| `400`     |
| `500`     |
| `DEFAULT` |
| `600`     |
