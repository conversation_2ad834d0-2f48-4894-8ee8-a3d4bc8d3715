---
title: Admonition
description: Displays a callout for user attention.
fragment: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/alert
  api: https://www.radix-ui.com/docs/primitives/components/alert#api-reference
---

<ComponentPreview
  name="admonition-demo"
  className="[&_.preview>[data-orientation=vertical]]:sm:max-w-[70%]"
  peekCode
  wide
/>

## Usage

```tsx
import { Admonition } from 'ui-patterns/admonition'
```

```tsx
<Admonition
  type="default"
  title="Is it accessible?"
  description="Yes. It adheres to the WAI-ARIA design pattern."
/>
```

## Examples

### Warning

<ComponentPreview
  name="admonition-warning"
  className="[&_.preview>[data-orientation=vertical]]:sm:max-w-[70%]"
/>

### Destructive

<ComponentPreview
  name="admonition-destructive"
  className="[&_.preview>[data-orientation=vertical]]:sm:max-w-[70%]"
/>
