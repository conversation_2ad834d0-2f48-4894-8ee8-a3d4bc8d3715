---
title: Form Item Layout
description: A helper component that provides a layout for form items.
component: true
fragment: true
---

<ComponentPreview
  name="form-item-layout-demo"
  description="A helper component that provides a layout for form items."
  peekCode
  showDottedGrid
  wide
/>

## Usage

This component is to help with the layout of form items. It automatically provides `FormItem`, `FormLabel`, `FormMessage` and `FormDescription`.
The styling and layout of these components can be customized by passing in the components as props.

The components that can be replaced in `react-hook-form` atoms are highlighted below:

```tsx showLineNumbers {1-2,6-8}
<FormItem_Shadcn_>
  <FormLabel_Shadcn_>Username</FormLabel_Shadcn_>
  <FormControl_Shadcn_>
    <Input placeholder="shadcn" {...field} />
  </FormControl_Shadcn_>
  <FormDescription_Shadcn_>This is your public display name.</FormDescription_Shadcn_>
  <FormMessage_Shadcn_ />
</FormItem_Shadcn_>
```

Using `FormItemLayout` it can look like this:

```tsx
<FormItemLayout label="Username" description="This is your public display name">
  <FormControl_Shadcn_>
    <Input placeholder="mildtomato" {...field} />
  </FormControl_Shadcn_>
</FormItemLayout>
```

Please note that you must still use `FormControl_Shadcn_` to wrap input fields.

## Examples

## With Select component

<ComponentPreview
  name="form-item-layout-with-select"
  description="A helper component that provides a layout for form items."
  showDottedGrid
/>

## Horizontal layout

This is useful for forms inside a `<SidePanel/>` / `<Sheet/>` component.

<ComponentPreview
  name="form-item-layout-with-horizontal"
  description="A helper component that provides a layout for form items."
  showDottedGrid
/>

## With Switch component

<ComponentPreview
  name="form-item-layout-with-switch"
  description="A helper component that provides a layout for form items."
  showDottedGrid
/>

## With Checkbox component

<ComponentPreview
  name="form-item-layout-with-checkbox"
  description="A helper component that provides a layout for form items."
  showDottedGrid
/>

## List of items as Checkboxes

<ComponentPreview
  name="form-item-layout-with-checkbox-list"
  description="A helper component that provides a layout for form items."
  showDottedGrid
/>

## Before label

You can insert any `React.Node` or string before the label using the `beforeLabel` prop.

<ComponentPreview
  name="form-item-layout-before-label"
  description="Insert react.node or string before the label."
  showDottedGrid
/>

## After label

You can insert any `React.Node` or string after the label using the `afterLabel` prop.

<ComponentPreview
  name="form-item-layout-after-label"
  description="Insert react.node or string after the label."
  showDottedGrid
/>
