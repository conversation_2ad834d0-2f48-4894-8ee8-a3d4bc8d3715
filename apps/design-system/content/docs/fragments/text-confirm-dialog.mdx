---
title: Text Confirm Dialog
description: A modal dialog that interrupts the user with important content and expects a response.
component: true
---

<ComponentPreview
  name="text-confirm-dialog-demo"
  description="An alert with an icon, title and description. The title says 'Heads up!' and the description is 'You can add components to your app using the cli.'."
  peekCode
  showDottedGrid
  wide
/>

## Examples

### With Info Alert

<ComponentPreview
  name="text-confirm-dialog-with-info-alert"
  description="An alert with an icon, title and description. The title says 'Heads up!' and the description is 'You can add components to your app using the cli.'."
/>

### With warning Alert

<ComponentPreview
  name="text-confirm-dialog-with-warning-alert"
  description="An alert with a destructive variant. The title says 'Delete this item?' and the description is 'This action cannot be undone.'."
/>

### With destructive Alert

<ComponentPreview
  name="text-confirm-dialog-with-destructive-alert"
  description="An alert with a destructive variant. The title says 'Delete this item?' and the description is 'This action cannot be undone.'."
/>

### With cancel button

<ComponentPreview
  name="text-confirm-dialog-with-cancel-button"
  description="An alert with a destructive variant. The title says 'Delete this item?' and the description is 'This action cannot be undone.'."
/>

### With children

<ComponentPreview
  name="text-confirm-dialog-with-children"
  description="An alert with a destructive variant. The title says 'Delete this item?' and the description is 'This action cannot be undone.'."
/>

### With size

<ComponentPreview
  name="text-confirm-dialog-with-size"
  description="An alert with a destructive variant. The title says 'Delete this item?' and the description is 'This action cannot be undone.'."
/>
