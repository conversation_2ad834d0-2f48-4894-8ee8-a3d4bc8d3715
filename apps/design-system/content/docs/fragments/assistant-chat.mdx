---
title: Assistant Chat
description: <PERSON><PERSON>oke Assistant chat text area, with support for a commands Popover.
component: true
---

<ComponentPreview name="assistant-chat-demo" peekCode wide />

## Usage

```tsx
import { AssistantChatForm } from 'ui-patterns/AssistantChat'
```

```tsx
<AssistantChatForm
  icon={<Box strokeWidth={1.5} size={24} className="text-foreground-muted" />}
  value={value}
  loading={loading}
  disabled={loading}
  onValueChange={(e) => setValueState(e.target.value)}
  onSubmit={async (event) => {
    event.preventDefault()
    handleSubmit(event)
  }}
/>
```

## Commands

The Assistant Chat support a commands Popover that can be used to display a list of available commands.

It requires a useState and a ref though for it to work correctly.
This is so that the input is still in focus, and the user can still interact with the Popover with the keyboard.

```tsx
const [commandsOpen, setCommandsOpen] = useState<boolean>(false)
const textAreaRef = createRef<HTMLTextAreaElement>()

<AssistantCommandPopover
  open={commandsOpen}
  setOpen={setCommandsOpen}
  textAreaRef={textAreaRef}
>
  <AssistantChatForm
    commandsOpen={commandsOpen}
    setCommandsOpen={setCommandsOpen}
  />
</AssistantCommandPopover>
```

<ComponentPreview name="assistant-chat-commands" />
