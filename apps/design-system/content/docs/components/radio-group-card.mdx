---
title: Radio Group Card
description: A set of checkable buttons—known as radio buttons—where no more than one of the buttons can be checked at a time.
component: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/radio-group
  api: https://www.radix-ui.com/docs/primitives/components/radio-group#api-reference
source:
  radix: true
---

<ComponentPreview name="radio-group-card-demo" peekCode wide />

## Usage

```tsx
import { RadioGroupCard, RadioGroupCardItem } from 'ui'
```

```tsx
<RadioGroupCard defaultValue="comfortable">
  <RadioGroupCardItem value="default" id="r1" label="Default" />
  <RadioGroupCardItem value="comfortable" id="r2" label="Comfortable" />
  <RadioGroupCardItem value="compact" id="r3" label="Compact" />
</RadioGroupCard>
```

## Examples

### With children

<ComponentPreview name="radio-group-card-with-children" />

### Form

<ComponentPreview name="radio-group-card-form" />
