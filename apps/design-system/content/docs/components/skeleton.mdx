---
title: Skeleton
description: Use to show a placeholder while content is loading.
component: true
source:
  shadcn: true
---

<ComponentPreview name="skeleton-demo" peekCode wide />

## Installation

<Tabs defaultValue="cli">

<TabsList>
  <TabsTrigger value="cli">CLI</TabsTrigger>
  <TabsTrigger value="manual">Manual</TabsTrigger>
</TabsList>
<TabsContent value="cli">

```bash
npx shadcn-ui@latest add skeleton
```

</TabsContent>

<TabsContent value="manual">

<Steps>

<Step>Copy and paste the following code into your project.</Step>

<ComponentSource name="skeleton" />

<Step>Update the import paths to match your project setup.</Step>

</Steps>

</TabsContent>

</Tabs>

## Usage

```tsx
import { Skeleton } from '@/components/ui/skeleton'
```

```tsx
<Skeleton className="w-[100px] h-[20px] rounded-full" />
```

## Examples

### Card

<ComponentPreview name="skeleton-card" />
