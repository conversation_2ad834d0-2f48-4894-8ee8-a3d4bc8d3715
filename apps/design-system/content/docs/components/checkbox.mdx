---
title: Checkbox
description: A control that allows the user to toggle between checked and not checked.
component: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/checkbox
  api: https://www.radix-ui.com/docs/primitives/components/checkbox#api-reference
source:
  radix: true
  shadcn: true
---

<ComponentPreview name="checkbox-demo" peekCode wide />

## Installation

<Tabs defaultValue="cli">

<TabsList>
  <TabsTrigger value="cli">CLI</TabsTrigger>
  <TabsTrigger value="manual">Manual</TabsTrigger>
</TabsList>
<TabsContent value="cli">

```bash
npx shadcn-ui@latest add checkbox
```

</TabsContent>

<TabsContent value="manual">

<Steps>

<Step>Install the following dependencies:</Step>

```bash
npm install @radix-ui/react-checkbox
```

<Step>Copy and paste the following code into your project.</Step>

<ComponentSource name="checkbox" />

<Step>Update the import paths to match your project setup.</Step>

</Steps>

</TabsContent>

</Tabs>

## Usage

```tsx
import { Checkbox } from '@/components/ui/checkbox'
```

```tsx
<Checkbox />
```

## Examples

### With text

<ComponentPreview name="checkbox-with-text" />

### Disabled

<ComponentPreview name="checkbox-disabled" />

### Form

<ComponentPreview name="checkbox-form-single" />

<ComponentPreview name="checkbox-form-multiple" />
