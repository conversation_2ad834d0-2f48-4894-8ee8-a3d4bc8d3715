---
title: Sonner
description: An opinionated toast component for React.
component: true
links:
  doc: https://sonner.emilkowal.ski
source:
  shadcn: true
---

<ComponentPreview name="sonner-demo" peekCode wide />

## About

Sonner is built and maintained by [em<PERSON><PERSON><PERSON><PERSON>\_](https://twitter.com/em<PERSON><PERSON><PERSON>ski_).

## Installation

<Steps>

<Step>Install the following dependencies:</Step>

```bash
npm install sonner next-themes
```

<Step>Copy and paste the following code into your project.</Step>

<ComponentSource name="sonner" />

<Step>Add the Toaster component</Step>

```tsx title="app/layout.tsx" {1,9}
import { Toaster } from '@/components/ui/sonner'

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head />
      <body>
        <main>{children}</main>
        <Toaster />
      </body>
    </html>
  )
}
```

</Steps>

## Usage

```tsx
import { toast } from 'sonner'
```

```tsx
toast('Event has been created.')
```

## Expand

You can change the amount of toasts visible through the visibleToasts prop.

```tsx
<Toaster expand={true | false} />
```

You can use the Input below to try it out.

<SonnerExpandConfig />

## Position

You can change the amount of toasts visible through the visibleToasts prop.

```tsx
<Toaster
  position={
    'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center'
  }
/>
```

You can use the Input below to try it out.

<SonnerPositionConfig />

## Types

You can customize the type of toast you want to render, and pass an options object as the second argument.

<ComponentPreview name="sonner-types" />

## Upload example

<ComponentPreview name="sonner-upload" />
