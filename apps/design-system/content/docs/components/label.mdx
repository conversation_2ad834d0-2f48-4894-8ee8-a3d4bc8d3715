---
title: Label
description: Renders an accessible label associated with controls.
component: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/label
  api: https://www.radix-ui.com/docs/primitives/components/label#api-reference
source:
  radix: true
  shadcn: true
---

<ComponentPreview name="label-demo" peekCode wide />

<Admonition
  variant="warning"
  title="Do not use this Label component in a Form"
>

Please use [FormLabel](/design-system/docs/components/form#anatomy) if you are using [Form](/design-system/docs/components/form) based components.

</Admonition>

## Usage

```tsx
import { Label_Shadcn_ } from '@/components/ui/label'
```

```tsx
<Label_Shadcn_ htmlFor="email">Your email address</Label_Shadcn_>
```
