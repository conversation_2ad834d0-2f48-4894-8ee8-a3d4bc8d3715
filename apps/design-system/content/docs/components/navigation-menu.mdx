---
title: Navigation Menu
description: A collection of links for navigating websites.
component: true
links:
  doc: https://www.radix-ui.com/docs/primitives/components/navigation-menu
  api: https://www.radix-ui.com/docs/primitives/components/navigation-menu#api-reference
source:
  radix: true
  shadcn: true
---

<ComponentPreview name="navigation-menu-demo" peekCode wide />

## Installation

<Tabs defaultValue="cli">

<TabsList>
  <TabsTrigger value="cli">CLI</TabsTrigger>
  <TabsTrigger value="manual">Manual</TabsTrigger>
</TabsList>
<TabsContent value="cli">

```bash
npx shadcn-ui@latest add navigation-menu
```

</TabsContent>

<TabsContent value="manual">

<Steps>

<Step>Install the following dependencies:</Step>

```bash
npm install @radix-ui/react-navigation-menu
```

<Step>Copy and paste the following code into your project.</Step>

<ComponentSource name="navigation-menu" />

<Step>Update the import paths to match your project setup.</Step>

</Steps>

</TabsContent>

</Tabs>

## Usage

```tsx
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuIndicator,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuViewport,
} from '@/components/ui/navigation-menu'
```

```tsx
<NavigationMenu>
  <NavigationMenuList>
    <NavigationMenuItem>
      <NavigationMenuTrigger>Item One</NavigationMenuTrigger>
      <NavigationMenuContent>
        <NavigationMenuLink>Link</NavigationMenuLink>
      </NavigationMenuContent>
    </NavigationMenuItem>
  </NavigationMenuList>
</NavigationMenu>
```

## Examples

### Link Component

When using the Next.js `<Link />` component, you can use `navigationMenuTriggerStyle()` to apply the correct styles to the trigger.

```tsx
import { navigationMenuTriggerStyle } from '@/components/ui/navigation-menu'
```

```tsx {3-5}
<NavigationMenuItem>
  <Link href="/docs" legacyBehavior passHref>
    <NavigationMenuLink className={navigationMenuTriggerStyle()}>Documentation</NavigationMenuLink>
  </Link>
</NavigationMenuItem>
```

See also the [Radix UI documentation](https://www.radix-ui.com/docs/primitives/components/navigation-menu#with-client-side-routing) for handling client side routing.

### With horizontal scroll

We can wrap a the `<NavigationMenuList />` in a `<ScrollArea />` component to make the menu scroll.

However, then we must also make sure the `<NavigationMenuContent />` is positioned correctly and rendered outside the scroll area.
We set `renderViewport={false}` on the `<NavigationMenu />` component to prevent the viewport from being rendered.
And then add our own Viewport with `<NavigationMenuViewport />`.

Then all our content panels will render themselves outside the scroll area and inside the viewport.

You can also add props to `<NavigationMenuViewport />` to style it as you like, including the `containerProps` prop to add classes to the container div element inside the viewport.

```tsx {1, 2, 11-13}
<NavigationMenu renderViewport={false}>
  <ScrollArea>
    <NavigationMenuList>
      <NavigationMenuItem>
        <NavigationMenuTrigger>Item One</NavigationMenuTrigger>
        <NavigationMenuContent>
          <NavigationMenuLink>Link</NavigationMenuLink>
        </NavigationMenuContent>
      </NavigationMenuItem>
    </NavigationMenuList>
    <ScrollBar />
  </ScrollArea>
  <NavigationMenuViewport containerProps={{ className: 'w-full' }} />
</NavigationMenu>
```

<ComponentPreview name="navigation-menu-responsive" />
