{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "tsconfig/base.json",
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "incremental": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@ui/*": ["./../../packages/ui/src/*"], // handle ui package paths
      "contentlayer/generated": ["./.contentlayer/generated"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    ".contentlayer/generated",
    "./../../packages/ui/src/**/*.d.ts"
  ],
  "exclude": ["node_modules", "./scripts/build-registry.mts"]
}
