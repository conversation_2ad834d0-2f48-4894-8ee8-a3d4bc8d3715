{"name": "design-system", "version": "0.1.0", "private": true, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --port 3003", "build": "pnpm run content:build && pnpm run build:registry && pnpm run build:llms && next build", "build:registry": "tsx --tsconfig ./tsconfig.scripts.json ./scripts/build-registry.mts && prettier --log-level silent --write \"registry/**/*.{ts,tsx,mdx}\" --cache", "build:llms": "tsx --tsconfig ./tsconfig.scripts.json ./scripts/build-llms-txt.ts", "start": "next start", "lint": "next lint", "content:build": "contentlayer2 build", "clean": "rimraf node_modules .next .turbo", "typecheck": "contentlayer2 build && tsc --noEmit -p tsconfig.json"}, "dependencies": {"@hookform/resolvers": "^3.1.1", "contentlayer2": "0.4.6", "eslint-config-supabase": "workspace:*", "icons": "workspace:*", "jotai": "^2.8.0", "lucide-react": "*", "markdown-wasm": "^1.2.0", "next": "catalog:", "next-contentlayer2": "0.4.6", "next-themes": "^0.3.0", "react": "^18.2.0", "react-docgen": "^7.0.3", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-inlinesvg": "^4.0.4", "react-wrap-balancer": "^1.1.0", "recharts": "^2.12.7", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.9.0", "rehype-slug": "^6.0.0", "remark": "^14.0.3", "remark-code-import": "^1.2.0", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "ts-morph": "^22.0.0", "ui": "workspace:*", "ui-patterns": "workspace:*", "unist-util-visit": "^5.0.0", "zod": "^3.22.4"}, "devDependencies": {"@shikijs/compat": "^1.1.7", "@types/lodash.template": "4.5.0", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.0.1", "config": "workspace:^", "mdast-util-toc": "^6.1.1", "postcss": "^8", "rimraf": "^4.1.3", "shiki": "^1.1.7", "tailwindcss": "^3.3.0", "tsconfig": "workspace:*", "tsx": "^4.19.3", "typescript": "~5.5.0", "unist-builder": "3.0.0"}}