{
  "$schema": "https://turbo.build/schema.json",
  "extends": ["//"],
  "tasks": {
    "codegen:examples": {
      "inputs": ["../../examples/**"],
      "outputs": ["examples/**"]
    },
    "codegen:references": {
      "inputs": ["spec/**"],
      "outputs": ["features/docs/generated/**"]
    },
    "build": {
      "dependsOn": ["^build", "codegen:examples", "codegen:references"],
      "env": [
        "ANALYZE",
        "NEXT_PUBLIC_SUPABASE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY",
        "NEXT_PUBLIC_MISC_URL",
        "NEXT_PUBLIC_MISC_USE_URL",
        "NEXT_PUBLIC_MISC_ANON_KEY",
        "NEXT_PUBLIC_MISC_USE_ANON_KEY",
        "NODE_ENV",
        "NEXT_PUBLIC_API_URL",
        "NEXT_PUBLIC_BASE_PATH",
        "NEXT_PUBLIC_SITE_URL",
        "NEXT_PUBLIC_DEV_AUTH_PAGE",
        "NEXT_PUBLIC_IS_PLATFORM",
        "NEXT_PUBLIC_VERCEL_ENV",
        "NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA",
        // These envs are used in the packages
        "NEXT_PUBLIC_STORAGE_KEY",
        "NEXT_PUBLIC_AUTH_DEBUG_KEY",
        "NEXT_PUBLIC_AUTH_PERSISTED_KEY",
        "NEXT_PUBLIC_AUTH_NAVIGATOR_LOCK_KEY",
        "NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL",
        "NEXT_PUBLIC_GOTRUE_URL",
        "NEXT_PUBLIC_SUPABASE_ANON_KEY",
        // These envs are technically passthrough env vars because they're only used on the server side of Nextjs
        "ASSET_CDN_S3_ENDPOINT",
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY",
        "DOCS_GITHUB_APP_ID",
        "DOCS_GITHUB_APP_INSTALLATION_ID",
        "DOCS_GITHUB_APP_PRIVATE_KEY",
        "DOCS_REVALIDATION_KEYS",
        "DOCS_REVALIDATION_OVERRIDE_KEYS",
        "FORCE_ASSET_CDN",
        "OPENAI_API_KEY",
        "SEARCH_GITHUB_APP_ID",
        "SEARCH_GITHUB_APP_INSTALLATION_ID",
        "SEARCH_GITHUB_APP_PRIVATE_KEY",
        "SITE_NAME",
        "SUPABASE_SECRET_KEY"
      ],
      "inputs": ["$TURBO_DEFAULT$"],
      "outputs": [".next/**", "!.next/cache/**"]
    }
  }
}
