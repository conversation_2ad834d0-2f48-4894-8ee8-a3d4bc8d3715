const template = `
---
id: usage
slug: /usage
title: Usage
toc_max_heading_level: 3
---

{/* <!-- AUTOGENERATED: DO NOT EDIT DIRECTLY --> */}

<%- info.description %>

<% commands.forEach(function(command){ %>

{/* <!-- AUTOGENERATED: DO NOT EDIT DIRECTLY --> */}

<br /><br />

<%- command.heading %>

{/* <!-- AUTOGENERATED: DO NOT EDIT DIRECTLY --> */}

<%- command.description %>


<% if(command.usage){ %>
<%- command.usage %>
<% }; %>

{/* <!-- AUTOGENERATED: DO NOT EDIT DIRECTLY --> */}

<% if(command.subcommands.length > 0){ %>
**Available Commands**

<% command.subcommandList.forEach(function(subcommand){ %>
- <a href="#<%- subcommand.id %>"><code><%- subcommand.title %></code></a>
<% }); %>
<% } %>


{/* <!-- AUTOGENERATED: DO NOT EDIT DIRECTLY --> */}

**Options**

<%- command.options %>

<% }); %>

{/* <!-- AUTOGENERATED: DO NOT EDIT DIRECTLY --> */}
`.trim()

export default template
