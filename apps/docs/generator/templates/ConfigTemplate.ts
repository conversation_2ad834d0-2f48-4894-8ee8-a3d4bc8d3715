const template = `
---
id: config
slug: /config
title: Configuration
toc_max_heading_level: 3
---

<!-- AUTOGENERATED: DO NOT EDIT DIRECTLY -->

<%= info.description %>

<% sections.forEach(function(section){ %>

<!-- AUTOGENERATED: DO NOT EDIT DIRECTLY -->

## <%= section.title %> [#<%= section.id %>]

<% section.parameters.forEach(function(parameter){ %>

### \`<%- parameter.title %>\` [#<%= parameter.id %>]

<%- parameter.description %>

<ul class="method-list-group not-prose">
<li class="method-list-item-label flex items-center gap-4">Required: <code><%= parameter.required %></code></li>
<li class="method-list-item-label flex items-center gap-4">Default: <code><%- parameter?.default ? parameter?.default : 'None' %></code></li><% if(parameter?.links?.length){ %>
<li>
<h3>See also:</h3>
<ul><% parameter.links?.forEach(function(link){ %><li><a href="<%- link.link %>"><%- link.name %></a></li><% }); %></ul>
</li>
<% } %>
</ul>

<br />
<% }); %>

<% }); %>
`.trim()

export default template
