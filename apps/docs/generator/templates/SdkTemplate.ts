const template = `
---
id: usage
title: Usage
toc_max_heading_level: 2
---

<!-- AUTOGENERATED: DO NOT EDIT DIRECTLY IF THIS IS VERSION "next" -->

<%- info.description %>


<!-- AUTOGENERATED: DO NOT EDIT DIRECTLY IF THIS IS VERSION "next" -->


<% functions.forEach(function(fn){ %>

## <%- fn.summary %>

<%- fn.usage %>

<% if (fn.attributes) { %>

### Attributes

<% fn.attributes.forEach(function(attribute){ %>
- <%- attribute.id %>
<% }); %>

<% } %>

### Notes

<%- fn.description %>

### Examples

<% fn.examples.forEach(function(example){ %>

#### <%- example.summary %>

<%- example.code %>

<% }); %>

<% }); %>


`.trim()

export default template
