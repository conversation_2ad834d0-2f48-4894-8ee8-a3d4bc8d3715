graph TD;
  InitS((Setup flow)) --> SAAL1[/Session is AAL1/] --> Enroll[Enroll API] --> ChallengeAPI[Challenge API] --> Scan[/Code sent to User/] --> Enter[User: Enter code] --> Verify[Verify API] --> Check{{Is code correct?}}
  Check -->|Yes| AAL2[/Upgrade to AAL2/] --> Done((Done))
  Check -->|No| Enter
  InitA((Login flow)) --> SignIn([User: Sign-in]) --> AAL1[/Upgrade to AAL1/] --> ListFactors[List Factors API]
  ListFactors -->|1 or more factors| OpenAuth([User: Select phone factor]) --> Enter
  ListFactors -->|0 factors| Setup[[Setup flow]]
