<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 715.6328125 968.234375" style="max-width: 100%;" xmlns="http://www.w3.org/2000/svg" width="100%" id="graph-div" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-div{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#graph-div .error-icon{fill:#552222;}#graph-div .error-text{fill:#552222;stroke:#552222;}#graph-div .edge-thickness-normal{stroke-width:2px;}#graph-div .edge-thickness-thick{stroke-width:3.5px;}#graph-div .edge-pattern-solid{stroke-dasharray:0;}#graph-div .edge-pattern-dashed{stroke-dasharray:3;}#graph-div .edge-pattern-dotted{stroke-dasharray:2;}#graph-div .marker{fill:#333333;stroke:#333333;}#graph-div .marker.cross{stroke:#333333;}#graph-div svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-div .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#graph-div .cluster-label text{fill:#333;}#graph-div .cluster-label span,#graph-div p{color:#333;}#graph-div .label text,#graph-div span,#graph-div p{fill:#333;color:#333;}#graph-div .node rect,#graph-div .node circle,#graph-div .node ellipse,#graph-div .node polygon,#graph-div .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#graph-div .flowchart-label text{text-anchor:middle;}#graph-div .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-div .node .label{text-align:center;}#graph-div .node.clickable{cursor:pointer;}#graph-div .arrowheadPath{fill:#333333;}#graph-div .edgePath .path{stroke:#333333;stroke-width:2.0px;}#graph-div .flowchart-link{stroke:#333333;fill:none;}#graph-div .edgeLabel{background-color:#e8e8e8;text-align:center;}#graph-div .edgeLabel rect{opacity:0.5;background-color:#e8e8e8;fill:#e8e8e8;}#graph-div .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#graph-div .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#graph-div .cluster text{fill:#333;}#graph-div .cluster span,#graph-div p{color:#333;}#graph-div div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#graph-div .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#graph-div :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="graph-div_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="graph-div_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="graph-div_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="graph-div_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="graph-div_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="graph-div_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-InitS LE-SAAL1" id="L-InitS-SAAL1-0" d="M142.477,91.367L142.477,95.534C142.477,99.701,142.477,108.034,142.543,115.567C142.609,123.101,142.741,129.835,142.807,133.201L142.873,136.568"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SAAL1 LE-Enroll" id="L-SAAL1-Enroll-0" d="M142.977,180.867L142.893,184.951C142.81,189.034,142.643,197.201,142.56,204.567C142.477,211.934,142.477,218.501,142.477,221.784L142.477,225.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Enroll LE-ShowQR" id="L-Enroll-ShowQR-0" d="M142.477,269.367L142.477,273.534C142.477,277.701,142.477,286.034,142.477,293.484C142.477,300.934,142.477,307.501,142.477,310.784L142.477,314.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ShowQR LE-Scan" id="L-ShowQR-Scan-0" d="M142.477,358.367L142.477,364.534C142.477,370.701,142.477,383.034,142.477,394.484C142.477,405.934,142.477,416.501,142.477,421.784L142.477,427.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Scan LE-Enter" id="L-Scan-Enter-0" d="M142.477,471.367L142.477,475.534C142.477,479.701,142.477,488.034,155.495,496.113C168.512,504.192,194.548,512.017,207.566,515.929L220.584,519.842"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Enter LE-Verify" id="L-Enter-Verify-0" d="M261.99,560.367L255.889,564.534C249.787,568.701,237.585,577.034,231.484,584.484C225.383,591.934,225.383,598.501,225.383,601.784L225.383,605.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Verify LE-Check" id="L-Verify-Check-0" d="M225.383,649.367L225.383,653.534C225.383,657.701,225.383,666.034,230.839,673.95C236.296,681.867,247.209,689.366,252.665,693.116L258.122,696.865"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Check LE-AAL2" id="L-Check-AAL2-0" d="M291.043,738.867L290.96,744.951C290.876,751.034,290.71,763.201,290.698,774.651C290.686,786.101,290.829,796.834,290.901,802.201L290.972,807.568"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-AAL2 LE-Done" id="L-AAL2-Done-0" d="M291.043,851.867L290.96,855.951C290.876,860.034,290.71,868.201,290.626,875.567C290.543,882.934,290.543,889.501,290.543,892.784L290.543,896.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-Check LE-Enter" id="L-Check-Enter-0" d="M319.596,699.867L325.614,695.617C331.632,691.367,343.668,682.867,349.685,671.201C355.703,659.534,355.703,644.701,355.703,629.867C355.703,615.034,355.703,600.201,350.331,589.115C344.96,578.03,334.216,570.693,328.845,567.025L323.473,563.356"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-InitA LE-SignIn" id="L-InitA-SignIn-0" d="M542.279,90.168L542.279,94.535C542.279,98.901,542.279,107.634,542.279,115.284C542.279,122.934,542.279,129.501,542.279,132.784L542.279,136.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-SignIn LE-AAL1" id="L-SignIn-AAL1-0" d="M542.279,180.367L542.279,184.534C542.279,188.701,542.279,197.034,542.345,204.567C542.411,212.101,542.543,218.835,542.609,222.201L542.675,225.568"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-AAL1 LE-ListFactors" id="L-AAL1-ListFactors-0" d="M542.779,269.867L542.696,273.951C542.613,278.034,542.446,286.201,542.363,293.567C542.279,300.934,542.279,307.501,542.279,310.784L542.279,314.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ListFactors LE-OpenAuth" id="L-ListFactors-OpenAuth-0" d="M506.499,358.367L495.184,364.534C483.869,370.701,461.239,383.034,449.924,394.484C438.609,405.934,438.609,416.501,438.609,421.784L438.609,427.067"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-OpenAuth LE-Enter" id="L-OpenAuth-Enter-0" d="M438.609,471.367L438.609,475.534C438.609,479.701,438.609,488.034,425.591,496.113C412.573,504.192,386.538,512.017,373.52,515.929L360.502,519.842"></path><path marker-end="url(#graph-div_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-ListFactors LE-Setup" id="L-ListFactors-Setup-0" d="M578.059,358.367L589.374,364.534C600.689,370.701,623.319,383.034,634.706,394.567C646.092,406.101,646.235,416.834,646.307,422.201L646.379,427.568"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(290.54296875, 775.3671875)" class="edgeLabel"><g transform="translate(-11.32421875, -12)" class="label"><foreignObject height="24" width="22.6484375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">Yes</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(355.703125, 629.8671875)" class="edgeLabel"><g transform="translate(-9.3984375, -12)" class="label"><foreignObject height="24" width="18.796875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">No</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(438.609375, 395.3671875)" class="edgeLabel"><g transform="translate(-62.16796875, -12)" class="label"><foreignObject height="24" width="124.3359375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">1 or more factors</span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(645.94921875, 395.3671875)" class="edgeLabel"><g transform="translate(-31.5390625, -12)" class="label"><foreignObject height="24" width="63.078125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel">0 factors</span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(142.4765625, 45.68359375)" data-id="InitS" data-node="true" id="flowchart-InitS-62" class="node default default flowchart-label"><circle height="39" width="91.3671875" r="45.68359375" ry="0" rx="0" style=""></circle><g transform="translate(-38.18359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="76.3671875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Setup flow</span></div></foreignObject></g></g><g transform="translate(142.4765625, 160.8671875)" data-id="SAAL1" data-node="true" id="flowchart-SAAL1-63" class="node default default flowchart-label"><polygon style="" transform="translate(-60.71484375,19.5)" class="label-container" points="-13,0 114.9296875,0 134.4296875,-39 6.5,-39"></polygon><g transform="translate(-53.21484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="106.4296875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Session is AAL1</span></div></foreignObject></g></g><g transform="translate(142.4765625, 249.8671875)" data-id="Enroll" data-node="true" id="flowchart-Enroll-64" class="node default default flowchart-label"><rect height="39" width="83.3046875" y="-19.5" x="-41.65234375" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-34.15234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="68.3046875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Enroll API</span></div></foreignObject></g></g><g transform="translate(142.4765625, 338.8671875)" data-id="ShowQR" data-node="true" id="flowchart-ShowQR-65" class="node default default flowchart-label"><rect height="39" width="115.84375" y="-19.5" x="-57.921875" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-50.421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.84375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Show QR code</span></div></foreignObject></g></g><g transform="translate(142.4765625, 451.8671875)" data-id="Scan" data-node="true" id="flowchart-Scan-66" class="node default default flowchart-label"><rect height="39" width="284.953125" y="-19.5" x="-142.4765625" ry="19.5" rx="19.5" style=""></rect><g transform="translate(-130.1015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="260.203125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User: Scan QR code in authenticator</span></div></foreignObject></g></g><g transform="translate(290.54296875, 540.8671875)" data-id="Enter" data-node="true" id="flowchart-Enter-67" class="node default default flowchart-label"><rect height="39" width="144.8125" y="-19.5" x="-72.40625" ry="19.5" rx="19.5" style=""></rect><g transform="translate(-60.03125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="120.0625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User: Enter code</span></div></foreignObject></g></g><g transform="translate(225.3828125, 629.8671875)" data-id="Verify" data-node="true" id="flowchart-Verify-68" class="node default default flowchart-label"><rect height="39" width="171.84375" y="-19.5" x="-85.921875" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-78.421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="156.84375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Challenge + Verify API</span></div></foreignObject></g></g><g transform="translate(290.54296875, 718.8671875)" data-id="Check" data-node="true" id="flowchart-Check-69" class="node default default flowchart-label"><polygon style="" transform="translate(-73.515625,19.5)" class="label-container" points="9.75,0 137.28125,0 147.03125,-19.5 137.28125,-39 9.75,-39 0,-19.5"></polygon><g transform="translate(-56.265625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.53125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Is code correct?</span></div></foreignObject></g></g><g transform="translate(290.54296875, 831.8671875)" data-id="AAL2" data-node="true" id="flowchart-AAL2-71" class="node default default flowchart-label"><polygon style="" transform="translate(-66.8203125,19.5)" class="label-container" points="-13,0 127.140625,0 146.640625,-39 6.5,-39"></polygon><g transform="translate(-59.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="118.640625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Upgrade to AAL2</span></div></foreignObject></g></g><g transform="translate(290.54296875, 926.80078125)" data-id="Done" data-node="true" id="flowchart-Done-72" class="node default default flowchart-label"><circle height="39" width="50.8671875" r="25.43359375" ry="0" rx="0" style=""></circle><g transform="translate(-17.93359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="35.8671875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Done</span></div></foreignObject></g></g><g transform="translate(542.279296875, 45.68359375)" data-id="InitA" data-node="true" id="flowchart-InitA-75" class="node default default flowchart-label"><circle height="39" width="88.96875" r="44.484375" ry="0" rx="0" style=""></circle><g transform="translate(-36.984375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="73.96875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Login flow</span></div></foreignObject></g></g><g transform="translate(542.279296875, 160.8671875)" data-id="SignIn" data-node="true" id="flowchart-SignIn-76" class="node default default flowchart-label"><rect height="39" width="115.453125" y="-19.5" x="-57.7265625" ry="19.5" rx="19.5" style=""></rect><g transform="translate(-45.3515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90.703125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User: Sign-in</span></div></foreignObject></g></g><g transform="translate(542.279296875, 249.8671875)" data-id="AAL1" data-node="true" id="flowchart-AAL1-77" class="node default default flowchart-label"><polygon style="" transform="translate(-66.8203125,19.5)" class="label-container" points="-13,0 127.140625,0 146.640625,-39 6.5,-39"></polygon><g transform="translate(-59.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="118.640625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Upgrade to AAL1</span></div></foreignObject></g></g><g transform="translate(542.279296875, 338.8671875)" data-id="ListFactors" data-node="true" id="flowchart-ListFactors-78" class="node default default flowchart-label"><rect height="39" width="124.40625" y="-19.5" x="-62.203125" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-54.703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.40625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">List Factors API</span></div></foreignObject></g></g><g transform="translate(438.609375, 451.8671875)" data-id="OpenAuth" data-node="true" id="flowchart-OpenAuth-80" class="node default default flowchart-label"><rect height="39" width="207.3125" y="-19.5" x="-103.65625" ry="19.5" rx="19.5" style=""></rect><g transform="translate(-91.28125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="182.5625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User: Open authenticator</span></div></foreignObject></g></g><g transform="translate(645.94921875, 451.8671875)" data-id="Setup" data-node="true" id="flowchart-Setup-83" class="node default default flowchart-label"><polygon style="" transform="translate(-45.68359375,19.5)" class="label-container" points="0,0 91.3671875,0 91.3671875,-39 0,-39 0,0 -8,0 99.3671875,0 99.3671875,-39 -8,-39 -8,0"></polygon><g transform="translate(-38.18359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="76.3671875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Setup flow</span></div></foreignObject></g></g></g></g></g></svg>