{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "predev": "pnpm run codegen:references && pnpm run codegen:examples", "dev": "concurrently --kill-others \"next dev --port 3001\" \"pnpm run dev:watch:troubleshooting\"", "dev:watch:troubleshooting": "node ./scripts/troubleshooting/watch.mjs", "dev:secrets:pull": "AWS_PROFILE=supabase-dev node ../../scripts/getSecrets.js -n local/docs", "prebuild": "pnpm run codegen:references && pnpm run codegen:examples", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:sitemap": "tsx ./internals/generate-sitemap.ts", "build:llms": "tsx ./scripts/llms.ts", "postbuild": "pnpm run build:sitemap && pnpm run build:llms && ./../../scripts/upload-static-assets.sh", "start": "next start", "lint": "next lint", "lint:mdx": "supa-mdx-lint content --config ../../supa-mdx-lint.config.toml", "typecheck": "tsc --noEmit", "pretest": "pnpm run codegen:examples", "test": "vitest --exclude \"**/*.smoke.test.ts\"", "test:smoke": "pnpm run codegen:references && vitest -t \"prod smoke test\"", "embeddings": "tsx scripts/search/generate-embeddings.ts", "embeddings:refresh": "pnpm run embeddings --refresh", "troubleshooting:sync": "node features/docs/Troubleshooting.script.mjs", "last-changed": "tsx scripts/last-changed.ts", "last-changed:reset": "pnpm run last-changed -- --reset", "codegen:examples": "cp -r ../../examples ./examples", "codegen:references": "tsx features/docs/Reference.generated.script.ts", "codemod:frontmatter": "node ./scripts/codemod/mdx-meta.mjs && prettier --write \"content/**/*.mdx\"", "clean": "rimraf node_modules features/docs/generated examples"}, "dependencies": {"@har-sdk/openapi-sampler": "^2.2.0", "@mdx-js/loader": "^2.1.5", "@mdx-js/react": "^2.3.0", "@next/bundle-analyzer": "^14.2.3", "@next/mdx": "^14.2.3", "@octokit/auth-app": "^7.0.0", "@octokit/core": "^6.0.0", "@octokit/graphql": "^8.0.0", "@octokit/plugin-paginate-graphql": "^4.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-collapsible": "^1.0.3", "@supabase/supabase-js": "catalog:", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.13.4", "acorn": "^8.11.3", "acorn-typescript": "^1.4.13", "ai-commands": "workspace:*", "common": "workspace:*", "common-tags": "^1.8.2", "config": "workspace:*", "eslint-config-supabase": "workspace:*", "framer-motion": "^11.0.3", "github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "hast-util-has-property": "^2.0.1", "hast-util-to-html": "^9.0.0", "icons": "workspace:*", "isbot": "^5.1.2", "js-yaml": "^3.14.1", "jsrsasign": "^11.0.0", "katex": "^0.16.21", "libpg-query": "15.2.0", "lodash": "^4.17.21", "lucide-react": "*", "mdast": "^3.0.0", "mdast-util-from-markdown": "^1.2.0", "mdast-util-gfm": "^2.0.2", "mdast-util-mdx": "^2.0.0", "mdast-util-mdx-jsx": "^2.0.0", "mdast-util-to-hast": "^12.3.0", "mdast-util-to-markdown": "^1.5.0", "mdast-util-to-string": "^3.1.1", "micromark-extension-gfm": "^2.0.3", "micromark-extension-mdxjs": "^1.0.0", "next": "catalog:", "next-mdx-remote": "^4.4.1", "next-plugin-yaml": "^1.0.1", "next-themes": "^0.3.0", "nuqs": "^1.19.1", "openai": "^4.20.1", "openapi-fetch": "0.12.4", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.12", "react-intersection-observer": "^9.5.3", "react-markdown": "^8.0.3", "react-use": "^17.5.0", "rehype-katex": "^7.0.0", "rehype-slug": "^5.1.0", "remark": "^14.0.2", "remark-emoji": "^3.1.2", "remark-gfm": "^3.0.1", "remark-math": "^6.0.0", "server-only": "^0.0.1", "shared-data": "workspace:*", "toml": "^3.0.0", "ui": "workspace:*", "ui-patterns": "workspace:*", "unist-builder": "^3.0.1", "unist-util-filter": "^4.0.1", "unist-util-visit": "^4.1.2", "uuid": "^9.0.1", "valtio": "^1.12.0", "yaml": "^2.4.5", "zod": "^3.22.4"}, "devDependencies": {"@aws-sdk/client-secrets-manager": "^3.410.0", "@supabase/supa-mdx-lint": "0.2.6-alpha", "@types/common-tags": "^1.8.4", "@types/estree": "1.0.5", "@types/hast": "^2.3.4", "@types/jsrsasign": "^10.5.12", "@types/mdast": "^3.0.0", "@types/node": "catalog:", "@types/react": "^18.2.24", "@types/react-copy-to-clipboard": "^5.0.4", "@types/unist": "^2.0.6", "api-types": "workspace:*", "cheerio": "^1.0.0-rc.12", "chokidar": "^4.0.3", "concurrently": "^9.1.2", "config": "workspace:*", "dotenv": "^16.0.3", "ejs": "^3.1.10", "globby": "^13.2.2", "openapi-types": "^12.1.3", "shiki": "^3.2.1", "simple-git": "^3.24.0", "slugify": "^1.6.6", "smol-toml": "^1.3.1", "tsconfig": "workspace:*", "tsx": "^4.19.3", "twoslash": "^0.3.1", "typescript": "~5.5.0", "unist-util-visit-parents": "5.1.3", "vite": "^6.2.4", "vite-tsconfig-paths": "^4.3.2", "vitest": "^3.0.5"}}