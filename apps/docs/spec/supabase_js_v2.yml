openref: 0.1

info:
  id: reference/supabase-js
  title: Supabase Javascript Client
  description: |

    Supabase JavaScript.

  definition: spec/enrichments/tsdoc_v2/combined.json
  specUrl: https://github.com/supabase/supabase/edit/master/apps/docs/spec/supabase_js_v2.yml
  slugPrefix: '/'
  libraries:
    - id: 'JavaScript'
      version: '0.0.1'

functions:
  - id: initializing
    title: 'Initializing'
    $ref: '@supabase/supabase-js.index.SupabaseClient.constructor'
    description: |
      You can initialize a new Supabase client using the `createClient()` method.

      The Supabase client is your entrypoint to the rest of the Supabase functionality
      and is the easiest way to interact with everything we offer within the Supabase ecosystem.

    examples:
      - id: create-client
        name: Creating a client
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          // Create a single supabase client for interacting with your database
          const supabase = createClient('https://xyzcompany.supabase.co', 'public-anon-key')
          ```
      - id: with-custom-domain
        name: With a custom domain
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          // Use a custom domain as the supabase URL
          const supabase = createClient('https://my-custom-domain.com', 'public-anon-key')
          ```
      - id: with-additional-parameters
        name: With additional parameters
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          const options = {
            db: {
              schema: 'public',
            },
            auth: {
              autoRefreshToken: true,
              persistSession: true,
              detectSessionInUrl: true
            },
            global: {
              headers: { 'x-my-custom-header': 'my-app-name' },
            },
          }
          const supabase = createClient("https://xyzcompany.supabase.co", "public-anon-key", options)
          ```
      - id: api-schemas
        name: With custom schemas
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          const supabase = createClient('https://xyzcompany.supabase.co', 'public-anon-key', {
            // Provide a custom schema. Defaults to "public".
            db: { schema: 'other_schema' }
          })
          ```
        description: |
          By default the API server points to the `public` schema. You can enable other database schemas within the Dashboard.
          Go to [Settings > API > Exposed schemas](/dashboard/project/_/settings/api) and add the schema which you want to expose to the API.

          Note: each client connection can only access a single schema, so the code above can access the `other_schema` schema but cannot access the `public` schema.
      - id: custom-fetch-implementation
        name: Custom fetch implementation
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          const supabase = createClient('https://xyzcompany.supabase.co', 'public-anon-key', {
            global: { fetch: fetch.bind(globalThis) }
          })
          ```
        description: |
          `supabase-js` uses the [`cross-fetch`](https://www.npmjs.com/package/cross-fetch) library to make HTTP requests,
          but an alternative `fetch` implementation can be provided as an option.
          This is most useful in environments where `cross-fetch` is not compatible (for instance Cloudflare Workers).
      - id: react-native-options-async-storage
        name: React Native options with AsyncStorage
        code: |
          ```js
          import 'react-native-url-polyfill/auto'
          import { createClient } from '@supabase/supabase-js'
          import AsyncStorage from "@react-native-async-storage/async-storage";

          const supabase = createClient("https://xyzcompany.supabase.co", "public-anon-key", {
            auth: {
              storage: AsyncStorage,
              autoRefreshToken: true,
              persistSession: true,
              detectSessionInUrl: false,
            },
          });
          ```
        description: |
          For React Native we recommend using `AsyncStorage` as the storage implementation for Supabase Auth.
      - id: react-native-options-secure-storage
        name: React Native options with Expo SecureStore
        code: |
          ```js
          import 'react-native-url-polyfill/auto'
          import { createClient } from '@supabase/supabase-js'
          import AsyncStorage from '@react-native-async-storage/async-storage';
          import * as SecureStore from 'expo-secure-store';
          import * as aesjs from 'aes-js';
          import 'react-native-get-random-values';

          // As Expo's SecureStore does not support values larger than 2048
          // bytes, an AES-256 key is generated and stored in SecureStore, while
          // it is used to encrypt/decrypt values stored in AsyncStorage.
          class LargeSecureStore {
            private async _encrypt(key: string, value: string) {
              const encryptionKey = crypto.getRandomValues(new Uint8Array(256 / 8));

              const cipher = new aesjs.ModeOfOperation.ctr(encryptionKey, new aesjs.Counter(1));
              const encryptedBytes = cipher.encrypt(aesjs.utils.utf8.toBytes(value));

              await SecureStore.setItemAsync(key, aesjs.utils.hex.fromBytes(encryptionKey));

              return aesjs.utils.hex.fromBytes(encryptedBytes);
            }

            private async _decrypt(key: string, value: string) {
              const encryptionKeyHex = await SecureStore.getItemAsync(key);
              if (!encryptionKeyHex) {
                return encryptionKeyHex;
              }

              const cipher = new aesjs.ModeOfOperation.ctr(aesjs.utils.hex.toBytes(encryptionKeyHex), new aesjs.Counter(1));
              const decryptedBytes = cipher.decrypt(aesjs.utils.hex.toBytes(value));

              return aesjs.utils.utf8.fromBytes(decryptedBytes);
            }

            async getItem(key: string) {
              const encrypted = await AsyncStorage.getItem(key);
              if (!encrypted) { return encrypted; }

              return await this._decrypt(key, encrypted);
            }

            async removeItem(key: string) {
              await AsyncStorage.removeItem(key);
              await SecureStore.deleteItemAsync(key);
            }

            async setItem(key: string, value: string) {
              const encrypted = await this._encrypt(key, value);

              await AsyncStorage.setItem(key, encrypted);
            }
          }

          const supabase = createClient("https://xyzcompany.supabase.co", "public-anon-key", {
            auth: {
              storage: new LargeSecureStore(),
              autoRefreshToken: true,
              persistSession: true,
              detectSessionInUrl: false,
            },
          });
          ```
        description: |
          If you wish to encrypt the user's session information, you can use `aes-js` and store the encryption key in Expo SecureStore. The `aes-js` library, a reputable JavaScript-only implementation of the AES encryption algorithm in CTR mode. A new 256-bit encryption key is generated using the `react-native-get-random-values` library. This key is stored inside Expo's SecureStore, while the value is encrypted and placed inside AsyncStorage.

          Please make sure that:
          - You keep the `expo-secure-storage`, `aes-js` and `react-native-get-random-values` libraries up-to-date.
          - Choose the correct [`SecureStoreOptions`](https://docs.expo.dev/versions/latest/sdk/securestore/#securestoreoptions) for your app's needs. E.g. [`SecureStore.WHEN_UNLOCKED`](https://docs.expo.dev/versions/latest/sdk/securestore/#securestorewhen_unlocked) regulates when the data can be accessed.
          - Carefully consider optimizations or other modifications to the above example, as those can lead to introducing subtle security vulnerabilities.

  - id: auth-api
    title: 'Overview'
    notes: |
      - The auth methods can be accessed via the `supabase.auth` namespace.
      - By default, the supabase client sets `persistSession` to true and attempts to store the session in local storage. When using the supabase client in an environment that doesn't support local storage, you might notice the following warning message being logged:

        > No storage option exists to persist the session, which may result in unexpected behavior when using auth. If you want to set `persistSession` to true, please provide a storage option or you may set `persistSession` to false to disable this warning.

        This warning message can be safely ignored if you're not using auth on the server-side. If you are using auth and you want to set `persistSession` to true, you will need to provide a custom storage implementation that follows [this interface](https://github.com/supabase/gotrue-js/blob/master/src/lib/types.ts#L1027).
      - Any email links and one-time passwords (OTPs) sent have a default expiry of 24 hours. We have the following [rate limits](/docs/guides/platform/going-into-prod#auth-rate-limits) in place to guard against brute force attacks.
      - The expiry of an access token can be set in the "JWT expiry limit" field in [your project's auth settings](/dashboard/project/_/settings/auth). A refresh token never expires and can only be used once.

    examples:
      - id: create-auth-client
        name: Create auth client
        isSpotlight: true
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          const supabase = createClient(supabase_url, anon_key)
          ```
      - id: create-auth-client-server-side
        name: Create auth client (server-side)
        isSpotlight: false
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          const supabase = createClient(supabase_url, anon_key, {
            auth: {
              autoRefreshToken: false,
              persistSession: false,
              detectSessionInUrl: false
            }
          })
          ```

  - id: sign-up
    title: 'signUp()'
    $ref: '@supabase/auth-js.GoTrueClient.signUp'
    notes: |
      - By default, the user needs to verify their email address before logging in. To turn this off, disable **Confirm email** in [your project](/dashboard/project/_/auth/providers).
      - **Confirm email** determines if users need to confirm their email address after signing up.
        - If **Confirm email** is enabled, a `user` is returned but `session` is null.
        - If **Confirm email** is disabled, both a `user` and a `session` are returned.
      - When the user confirms their email address, they are redirected to the [`SITE_URL`](/docs/guides/auth/redirect-urls) by default. You can modify your `SITE_URL` or add additional redirect URLs in [your project](/dashboard/project/_/auth/url-configuration).
      - If signUp() is called for an existing confirmed user:
        - When both **Confirm email** and **Confirm phone** (even when phone provider is disabled) are enabled in [your project](/dashboard/project/_/auth/providers), an obfuscated/fake user object is returned.
        - When either **Confirm email** or **Confirm phone** (even when phone provider is disabled) is disabled, the error message, `User already registered` is returned.
      - To fetch the currently logged-in user, refer to [`getUser()`](/docs/reference/javascript/auth-getuser).
    examples:
      - id: sign-up
        name: Sign up with an email and password
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.signUp({
            email: '<EMAIL>',
            password: 'example-password',
          })
          ```
        response: |
          ```json
          // Some fields may be null if "confirm email" is enabled.
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {},
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "<EMAIL>",
                  "email_confirmed_at": "2024-01-01T00:00:00Z",
                  "phone": "",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {
                    "provider": "email",
                    "providers": [
                      "email"
                    ]
                  },
                  "user_metadata": {},
                  "identities": [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z"
                }
              }
            },
            "error": null
          }
          ```
      - id: sign-up-phone
        name: Sign up with a phone number and password (SMS)
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.signUp({
            phone: '*********',
            password: 'example-password',
            options: {
              channel: 'sms'
            }
          })
          ```
      - id: sign-up-phone-whatsapp
        name: Sign up with a phone number and password (whatsapp)
        isSpotlight: true
        description: |
          The user will be sent a WhatsApp message which contains a OTP. By default, a given user can only request a OTP once every 60 seconds. Note that a user will need to have a valid WhatsApp account that is linked to Twilio in order to use this feature.
        code: |
          ```js
          const { data, error } = await supabase.auth.signUp({
            phone: '*********',
            password: 'example-password',
            options: {
              channel: 'whatsapp'
            }
          })
          ```
      - id: sign-up-with-additional-user-metadata
        name: Sign up with additional user metadata
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.signUp(
            {
              email: '<EMAIL>',
              password: 'example-password',
              options: {
                data: {
                  first_name: 'John',
                  age: 27,
                }
              }
            }
          )
          ```
      - id: sign-up-with-redirect
        name: Sign up with a redirect URL
        description: |
          - See [redirect URLs and wildcards](/docs/guides/auth#redirect-urls-and-wildcards) to add additional redirect URLs to your project.
        code: |
          ```js
          const { data, error } = await supabase.auth.signUp(
            {
              email: '<EMAIL>',
              password: 'example-password',
              options: {
                emailRedirectTo: 'https://example.com/welcome'
              }
            }
          )
          ```

  - id: on-auth-state-change
    title: 'onAuthStateChange()'
    $ref: '@supabase/auth-js.GoTrueClient.onAuthStateChange'
    notes: |
      - Subscribes to important events occurring on the user's session.
      - Use on the frontend/client. It is less useful on the server.
      - Events are emitted across tabs to keep your application's UI up-to-date. Some events can fire very frequently, based on the number of tabs open. Use a quick and efficient callback function, and defer or debounce as many operations as you can to be performed outside of the callback.
      - **Important:** A callback can be an `async` function and it runs synchronously during the processing of the changes causing the event. You can easily create a dead-lock by using `await` on a call to another method of the Supabase library.
        - Avoid using `async` functions as callbacks.
        - Limit the number of `await` calls in `async` callbacks.
        - Do not use other Supabase functions in the callback function. If you must, dispatch the functions once the callback has finished executing. Use this as a quick way to achieve this:
          ```js
          supabase.auth.onAuthStateChange((event, session) => {
            setTimeout(async () => {
              // await on other Supabase function here
              // this runs right after the callback has finished
            }, 0)
          })
          ```
      - Emitted events:
        - `INITIAL_SESSION`
          - Emitted right after the Supabase client is constructed and the initial session from storage is loaded.
        - `SIGNED_IN`
          - Emitted each time a user session is confirmed or re-established, including on user sign in and when refocusing a tab.
          - Avoid making assumptions as to when this event is fired, this may occur even when the user is already signed in. Instead, check the user object attached to the event to see if a new user has signed in and update your application's UI.
          - This event can fire very frequently depending on the number of tabs open in your application.
        - `SIGNED_OUT`
          - Emitted when the user signs out. This can be after:
            - A call to `supabase.auth.signOut()`.
            - After the user's session has expired for any reason:
              - User has signed out on another device.
              - The session has reached its timebox limit or inactivity timeout.
              - User has signed in on another device with single session per user enabled.
              - Check the [User Sessions](/docs/guides/auth/sessions) docs for more information.
          - Use this to clean up any local storage your application has associated with the user.
        - `TOKEN_REFRESHED`
          - Emitted each time a new access and refresh token are fetched for the signed in user.
          - It's best practice and highly recommended to extract the access token (JWT) and store it in memory for further use in your application.
            - Avoid frequent calls to `supabase.auth.getSession()` for the same purpose.
          - There is a background process that keeps track of when the session should be refreshed so you will always receive valid tokens by listening to this event.
          - The frequency of this event is related to the JWT expiry limit configured on your project.
        - `USER_UPDATED`
          - Emitted each time the `supabase.auth.updateUser()` method finishes successfully. Listen to it to update your application's UI based on new profile information.
        - `PASSWORD_RECOVERY`
          - Emitted instead of the `SIGNED_IN` event when the user lands on a page that includes a password recovery link in the URL.
          - Use it to show a UI to the user where they can [reset their password](/docs/guides/auth/passwords#resetting-a-users-password-forgot-password).

    examples:
      - id: listen-to-auth-changes
        name: Listen to auth changes
        isSpotlight: true
        code: |
          ```js
          const { data } = supabase.auth.onAuthStateChange((event, session) => {
            console.log(event, session)

            if (event === 'INITIAL_SESSION') {
              // handle initial session
            } else if (event === 'SIGNED_IN') {
              // handle sign in event
            } else if (event === 'SIGNED_OUT') {
              // handle sign out event
            } else if (event === 'PASSWORD_RECOVERY') {
              // handle password recovery event
            } else if (event === 'TOKEN_REFRESHED') {
              // handle token refreshed event
            } else if (event === 'USER_UPDATED') {
              // handle user updated event
            }
          })

          // call unsubscribe to remove the callback
          data.subscription.unsubscribe()
          ```
      - id: listen-to-sign-out
        name: Listen to sign out
        description: |
          Make sure you clear out any local data, such as local and session storage, after the client library has detected the user's sign out.
        code: |
          ```js
          supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_OUT') {
              console.log('SIGNED_OUT', session)

              // clear local and session storage
              [
                window.localStorage,
                window.sessionStorage,
              ].forEach((storage) => {
                Object.entries(storage)
                  .forEach(([key]) => {
                    storage.removeItem(key)
                  })
              })
            }
          })
          ```
      - id: store-provider-tokens
        name: Store OAuth provider tokens on sign in
        description: |
          When using [OAuth (Social Login)](/docs/guides/auth/social-login) you sometimes wish to get access to the provider's access token and refresh token, in order to call provider APIs in the name of the user.

          For example, if you are using [Sign in with Google](/docs/guides/auth/social-login/auth-google) you may want to use the provider token to call Google APIs on behalf of the user. Supabase Auth does not keep track of the provider access and refresh token, but does return them for you once, immediately after sign in. You can use the `onAuthStateChange` method to listen for the presence of the provider tokens and store them in local storage. You can further send them to your server's APIs for use on the backend.

          Finally, make sure you remove them from local storage on the `SIGNED_OUT` event. If the OAuth provider supports token revocation, make sure you call those APIs either from the frontend or schedule them to be called on the backend.
        code: |
          ```js
          // Register this immediately after calling createClient!
          // Because signInWithOAuth causes a redirect, you need to fetch the
          // provider tokens from the callback.
          supabase.auth.onAuthStateChange((event, session) => {
            if (session && session.provider_token) {
              window.localStorage.setItem('oauth_provider_token', session.provider_token)
            }

            if (session && session.provider_refresh_token) {
              window.localStorage.setItem('oauth_provider_refresh_token', session.provider_refresh_token)
            }

            if (event === 'SIGNED_OUT') {
              window.localStorage.removeItem('oauth_provider_token')
              window.localStorage.removeItem('oauth_provider_refresh_token')
            }
          })
          ```
      - id: react-user-session-context
        name: Use React Context for the User's session
        description: |
          Instead of relying on `supabase.auth.getSession()` within your React components, you can use a [React Context](https://react.dev/reference/react/createContext) to store the latest session information from the `onAuthStateChange` callback and access it that way.
        code: |
          ```js
          const SessionContext = React.createContext(null)

          function main() {
            const [session, setSession] = React.useState(null)

            React.useEffect(() => {
              const {data: { subscription }} = supabase.auth.onAuthStateChange(
                (event, session) => {
                  if (event === 'SIGNED_OUT') {
                    setSession(null)
                  } else if (session) {
                    setSession(session)
                  }
                })

              return () => {
                subscription.unsubscribe()
              }
            }, [])

            return (
              <SessionContext.Provider value={session}>
                <App />
              </SessionContext.Provider>
            )
          }
          ```
      - id: listen-to-password-recovery-events
        name: Listen to password recovery events
        code: |
          ```js
          supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'PASSWORD_RECOVERY') {
              console.log('PASSWORD_RECOVERY', session)
              // show screen to update user's password
              showPasswordResetScreen(true)
            }
          })
          ```
      - id: listen-to-sign-in
        name: Listen to sign in
        code: |
          ```js
          supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_IN') console.log('SIGNED_IN', session)
          })
          ```
      - id: listen-to-token-refresh
        name: Listen to token refresh
        code: |
          ```js
          supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'TOKEN_REFRESHED') console.log('TOKEN_REFRESHED', session)
          })
          ```
      - id: listen-to-user-updates
        name: Listen to user updates
        code: |
          ```js
          supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'USER_UPDATED') console.log('USER_UPDATED', session)
          })
          ```

  - id: sign-in-anonymously
    title: 'signInAnonymously()'
    $ref: '@supabase/auth-js.GoTrueClient.signInAnonymously'
    notes: |
      - Returns an anonymous user
      - It is recommended to set up captcha for anonymous sign-ins to prevent abuse. You can pass in the captcha token in the `options` param.
    examples:
      - id: sign-in-anonymously
        name: Create an anonymous user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.signInAnonymously({
            options: {
              captchaToken
            }
          });
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "",
                "phone": "",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {},
                "user_metadata": {},
                "identities": [],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": true
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "",
                  "phone": "",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {},
                  "user_metadata": {},
                  "identities": [],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "is_anonymous": true
                }
              }
            },
            "error": null
          }
          ```
      - id: sign-in-anonymously-with-user-metadata
        name: Create an anonymous user with custom user metadata
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.signInAnonymously({
            options: {
              data
            }
          })
          ```

  - id: sign-in-with-password
    title: 'signInWithPassword()'
    $ref: '@supabase/auth-js.GoTrueClient.signInWithPassword'
    notes: |
      - Requires either an email and password or a phone number and password.
    examples:
      - id: sign-in-with-email-and-password
        name: Sign in with email and password
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'example-password',
          })
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {},
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "<EMAIL>",
                  "email_confirmed_at": "2024-01-01T00:00:00Z",
                  "phone": "",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {
                    "provider": "email",
                    "providers": [
                      "email"
                    ]
                  },
                  "user_metadata": {},
                  "identities": [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z"
                }
              }
            },
            "error": null
          }
          ```
      - id: sign-in-with-phone-and-password
        name: Sign in with phone and password
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithPassword({
            phone: '+***********',
            password: 'some-password',
          })
          ```
  - id: sign-in-with-otp
    title: 'signInWithOtp()'
    $ref: '@supabase/auth-js.GoTrueClient.signInWithOtp'
    notes: |
      - Requires either an email or phone number.
      - This method is used for passwordless sign-ins where a OTP is sent to the user's email or phone number.
      - If the user doesn't exist, `signInWithOtp()` will signup the user instead. To restrict this behavior, you can set `shouldCreateUser` in `SignInWithPasswordlessCredentials.options` to `false`.
      - If you're using an email, you can configure whether you want the user to receive a magiclink or a OTP.
      - If you're using phone, you can configure whether you want the user to receive a OTP.
      - The magic link's destination URL is determined by the [`SITE_URL`](/docs/guides/auth/redirect-urls).
      - See [redirect URLs and wildcards](/docs/guides/auth#redirect-urls-and-wildcards) to add additional redirect URLs to your project.
      - Magic links and OTPs share the same implementation. To send users a one-time code instead of a magic link, [modify the magic link email template](/dashboard/project/_/auth/templates) to include `{{ .Token }}` instead of `{{ .ConfirmationURL }}`.
      - See our [Twilio Phone Auth Guide](/docs/guides/auth/phone-login?showSmsProvider=Twilio) for details about configuring WhatsApp sign in.
    examples:
      - id: sign-in-with-email
        name: Sign in with email
        isSpotlight: true
        description: The user will be sent an email which contains either a magiclink or a OTP or both. By default, a given user can only request a OTP once every 60 seconds.
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithOtp({
            email: '<EMAIL>',
            options: {
              emailRedirectTo: 'https://example.com/welcome'
            }
          })
          ```
        response: |
          ```json
          {
            "data": {
              "user": null,
              "session": null
            },
            "error": null
          }
          ```
      - id: sign-in-with-sms-otp
        name: Sign in with SMS OTP
        isSpotlight: false
        description: The user will be sent a SMS which contains a OTP. By default, a given user can only request a OTP once every 60 seconds.
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithOtp({
            phone: '+***********',
          })
          ```
      - id: sign-in-with-whatsapp-otp
        name: Sign in with WhatsApp OTP
        isSpotlight: false
        description: The user will be sent a WhatsApp message which contains a OTP. By default, a given user can only request a OTP once every 60 seconds. Note that a user will need to have a valid WhatsApp account that is linked to Twilio in order to use this feature.
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithOtp({
            phone: '+***********',
            options: {
              channel:'whatsapp',
            }
          })
          ```
  - id: sign-in-with-oauth
    title: 'signInWithOAuth()'
    $ref: '@supabase/auth-js.GoTrueClient.signInWithOAuth'
    notes: |
      - This method is used for signing in using a third-party provider.
      - Supabase supports many different [third-party providers](/docs/guides/auth#configure-third-party-providers).
    examples:
      - id: sign-in-using-a-third-party-provider
        name: Sign in using a third-party provider
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'github'
          })
          ```
        response: |
          ```json
          {
            data: {
              provider: 'github',
              url: <PROVIDER_URL_TO_REDIRECT_TO>
            },
            error: null
          }
          ```
      - id: sign-in-using-a-third-party-provider-with-redirect
        name: Sign in using a third-party provider with redirect
        isSpotlight: false
        description: |
          - When the third-party provider successfully authenticates the user, the provider redirects the user to the URL specified in the `redirectTo` parameter. This parameter defaults to the [`SITE_URL`](/docs/guides/auth/redirect-urls). It does not redirect the user immediately after invoking this method.
          - See [redirect URLs and wildcards](/docs/guides/auth#redirect-urls-and-wildcards) to add additional redirect URLs to your project.
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'github',
            options: {
              redirectTo: 'https://example.com/welcome'
            }
          })
          ```
      - id: sign-in-with-scopes
        name: Sign in with scopes and access provider tokens
        isSpotlight: false
        description: |
          If you need additional access from an OAuth provider, in order to access provider specific APIs in the name of the user, you can do this by passing in the scopes the user should authorize for your application. Note that the `scopes` option takes in **a space-separated list** of scopes.

          Because OAuth sign-in often includes redirects, you should register an `onAuthStateChange` callback immediately after you create the Supabase client. This callback will listen for the presence of `provider_token` and `provider_refresh_token` properties on the `session` object and store them in local storage. The client library will emit these values **only once** immediately after the user signs in. You can then access them by looking them up in local storage, or send them to your backend servers for further processing.

          Finally, make sure you remove them from local storage on the `SIGNED_OUT` event. If the OAuth provider supports token revocation, make sure you call those APIs either from the frontend or schedule them to be called on the backend.
        code: |
          ```js
          // Register this immediately after calling createClient!
          // Because signInWithOAuth causes a redirect, you need to fetch the
          // provider tokens from the callback.
          supabase.auth.onAuthStateChange((event, session) => {
            if (session && session.provider_token) {
              window.localStorage.setItem('oauth_provider_token', session.provider_token)
            }

            if (session && session.provider_refresh_token) {
              window.localStorage.setItem('oauth_provider_refresh_token', session.provider_refresh_token)
            }

            if (event === 'SIGNED_OUT') {
              window.localStorage.removeItem('oauth_provider_token')
              window.localStorage.removeItem('oauth_provider_refresh_token')
            }
          })

          // Call this on your Sign in with GitHub button to initiate OAuth
          // with GitHub with the requested elevated scopes.
          await supabase.auth.signInWithOAuth({
            provider: 'github',
            options: {
              scopes: 'repo gist notifications'
            }
          })
          ```

  - id: sign-in-with-id-token
    title: 'signInWithIdToken'
    $ref: '@supabase/auth-js.GoTrueClient.signInWithIdToken'
    examples:
      - id: sign-in-with-id-token
        name: 'Sign In using ID Token'
        code: |
          ```js
          const { data, error } = await supabase.auth.signInWithIdToken({
            provider: 'google',
            token: 'your-id-token'
          })
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  ...
                },
                "user_metadata": {
                  ...
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "provider": "google",
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {
                    ...
                  },
                  "user_metadata": {
                    ...
                  },
                  "identities": [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "provider": "google",
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                }
              }
            },
            "error": null
          }
          ```
  - id: sign-in-with-sso
    title: 'signInWithSSO()'
    $ref: '@supabase/auth-js.GoTrueClient.signInWithSSO'
    notes: |
      - Before you can call this method you need to [establish a connection](/docs/guides/auth/sso/auth-sso-saml#managing-saml-20-connections) to an identity provider. Use the [CLI commands](/docs/reference/cli/supabase-sso) to do this.
      - If you've associated an email domain to the identity provider, you can use the `domain` property to start a sign-in flow.
      - In case you need to use a different way to start the authentication flow with an identity provider, you can use the `providerId` property. For example:
          - Mapping specific user email addresses with an identity provider.
          - Using different hints to identity the identity provider to be used by the user, like a company-specific page, IP address or other tracking information.
    examples:
      - id: sign-in-with-domain
        name: Sign in with email domain
        isSpotlight: true
        code: |
          ```js
            // You can extract the user's email domain and use it to trigger the
            // authentication flow with the correct identity provider.

            const { data, error } = await supabase.auth.signInWithSSO({
              domain: 'company.com'
            })

            if (data?.url) {
              // redirect the user to the identity provider's authentication flow
              window.location.href = data.url
            }
          ```
      - id: sign-in-with-provider-uuid
        name: Sign in with provider UUID
        isSpotlight: true
        code: |
          ```js
            // Useful when you need to map a user's sign in request according
            // to different rules that can't use email domains.

            const { data, error } = await supabase.auth.signInWithSSO({
              providerId: '21648a9d-8d5a-4555-a9d1-d6375dc14e92'
            })

            if (data?.url) {
              // redirect the user to the identity provider's authentication flow
              window.location.href = data.url
            }
          ```
  - id: sign-out
    title: 'signOut()'
    $ref: '@supabase/auth-js.GoTrueClient.signOut'
    notes: |
      - In order to use the `signOut()` method, the user needs to be signed in first.
      - By default, `signOut()` uses the global scope, which signs out all other sessions that the user is logged into as well.
      - Since Supabase Auth uses JWTs for authentication, the access token JWT will be valid until it's expired. When the user signs out, Supabase revokes the refresh token and deletes the JWT from the client-side. This does not revoke the JWT and it will still be valid until it expires.
    examples:
      - id: sign-out
        name: Sign out
        isSpotlight: true
        code: |
          ```js
          const { error } = await supabase.auth.signOut()
          ```
  - id: verify-otp
    title: 'verifyOtp()'
    $ref: '@supabase/auth-js.GoTrueClient.verifyOtp'
    notes: |
      - The `verifyOtp` method takes in different verification types. If a phone number is used, the type can either be `sms` or `phone_change`. If an email address is used, the type can be one of the following: `email`, `recovery`, `invite` or `email_change` (`signup` and `magiclink` types are deprecated).
      - The verification type used should be determined based on the corresponding auth method called before `verifyOtp` to sign up / sign-in a user.
      - The `TokenHash` is contained in the [email templates](/docs/guides/auth/auth-email-templates) and can be used to sign in. You may wish to use the hash with Magic Links for the PKCE flow for Server Side Auth. See [this guide](/docs/guides/auth/server-side/email-based-auth-with-pkce-flow-for-ssr) for more details.
    examples:
      - id: verify-signup-one-time-password(otp)
        name: Verify Signup One-Time Password (OTP)
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.verifyOtp({ email, token, type: 'email'})
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "recovery_sent_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "<EMAIL>",
                  "email_confirmed_at": "2024-01-01T00:00:00Z",
                  "phone": "",
                  "confirmed_at": "2024-01-01T00:00:00Z",
                  "recovery_sent_at": "2024-01-01T00:00:00Z",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {
                    "provider": "email",
                    "providers": [
                      "email"
                    ]
                  },
                  "user_metadata": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "identities": [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "is_anonymous": false
                }
              }
            },
            "error": null
          }
          ```
      - id: verify-sms-one-time-password(otp)
        name: Verify Sms One-Time Password (OTP)
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.verifyOtp({ phone, token, type: 'sms'})
          ```
      - id: verify-email-auth(tokenhash)
        name: Verify Email Auth (Token Hash)
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.verifyOtp({ token_hash: tokenHash, type: 'email'})
          ```
  - id: get-session
    title: 'getSession()'
    $ref: '@supabase/auth-js.GoTrueClient.getSession'
    notes: |
      - This method retrieves the current local session (i.e local storage).
      - The session contains a signed JWT and unencoded session data.
      - Since the unencoded session data is retrieved from the local storage medium, **do not** rely on it as a source of trusted data on the server. It could be tampered with by the sender. If you need verified, trustworthy user data, call [`getUser`](/docs/reference/javascript/auth-getuser) instead.
      - If the session has an expired access token, this method will use the refresh token to get a new session.
    examples:
      - id: get-the-session-data
        name: Get the session data
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.getSession()
          ```
        response: |
          ```json
          {
            "data": {
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "<EMAIL>",
                  "email_confirmed_at": "2024-01-01T00:00:00Z",
                  "phone": "",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {
                    "provider": "email",
                    "providers": [
                      "email"
                    ]
                  },
                  "user_metadata": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "identities": [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "is_anonymous": false
                }
              }
            },
            "error": null
          }
          ```
  - id: start-auto-refresh
    title: 'startAutoRefresh()'
    $ref: '@supabase/auth-js.GoTrueClient.startAutoRefresh'
    notes: |
      - Only useful in non-browser environments such as React Native or Electron.
      - The Supabase Auth library automatically starts and stops proactively refreshing the session when a tab is focused or not.
      - On non-browser platforms, such as mobile or desktop apps built with web technologies, the library is not able to effectively determine whether the application is _focused_ or not.
      - To give this hint to the application, you should be calling this method when the app is in focus and calling `supabase.auth.stopAutoRefresh()` when it's out of focus.
    examples:
      - id: start-stop-auto-refresh-react-native
        name: Start and stop auto refresh in React Native
        isSpotlight: true
        code: |
          ```js
          import { AppState } from 'react-native'

          // make sure you register this only once!
          AppState.addEventListener('change', (state) => {
            if (state === 'active') {
              supabase.auth.startAutoRefresh()
            } else {
              supabase.auth.stopAutoRefresh()
            }
          })
          ```
  - id: stop-auto-refresh
    title: 'stopAutoRefresh()'
    $ref: '@supabase/auth-js.GoTrueClient.stopAutoRefresh'
    notes: |
      - Only useful in non-browser environments such as React Native or Electron.
      - The Supabase Auth library automatically starts and stops proactively refreshing the session when a tab is focused or not.
      - On non-browser platforms, such as mobile or desktop apps built with web technologies, the library is not able to effectively determine whether the application is _focused_ or not.
      - When your application goes in the background or out of focus, call this method to stop the proactive refreshing of the session.
    examples:
      - id: start-stop-auto-refresh-react-native
        name: Start and stop auto refresh in React Native
        isSpotlight: true
        code: |
          ```js
          import { AppState } from 'react-native'

          // make sure you register this only once!
          AppState.addEventListener('change', (state) => {
            if (state === 'active') {
              supabase.auth.startAutoRefresh()
            } else {
              supabase.auth.stopAutoRefresh()
            }
          })
          ```
  - id: get-user
    title: 'getUser()'
    $ref: '@supabase/auth-js.GoTrueClient.getUser'
    notes: |
      - This method fetches the user object from the database instead of local session.
      - This method is useful for checking if the user is authorized because it validates the user's access token JWT on the server.
      - Should always be used when checking for user authorization on the server. On the client, you can instead use `getSession().session.user` for faster results. `getSession` is insecure on the server.
    examples:
      - id: get-the-logged-in-user-with-the-current-existing-session
        name: Get the logged in user with the current existing session
        isSpotlight: true
        code: |
          ```js
          const { data: { user } } = await supabase.auth.getUser()
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              }
            },
            "error": null
          }
          ```
      - id: get-the-logged-in-user-with-a-custom-access-token-jwt
        name: Get the logged in user with a custom access token jwt
        isSpotlight: false
        code: |
          ```js
          const { data: { user } } = await supabase.auth.getUser(jwt)
          ```
  - id: update-user
    title: 'updateUser()'
    $ref: '@supabase/auth-js.GoTrueClient.updateUser'
    notes: |
      - In order to use the `updateUser()` method, the user needs to be signed in first.
      - By default, email updates sends a confirmation link to both the user's current and new email.
      To only send a confirmation link to the user's new email, disable **Secure email change** in your project's [email auth provider settings](/dashboard/project/_/auth/providers).

    examples:
      - id: update-the-email-for-an-authenticated-user
        name: Update the email for an authenticated user
        description: Sends a "Confirm Email Change" email to the new email address.
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.updateUser({
            email: '<EMAIL>'
          })
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "new_email": "<EMAIL>",
                "email_change_sent_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              }
            },
            "error": null
          }
          ```
      - id: update-the-phone-for-an-authenticated-user
        name: Update the phone number for an authenticated user
        description: Sends a one-time password (OTP) to the new phone number.
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.updateUser({
            phone: '*********'
          })
          ```
      - id: update-the-password-for-an-authenticated-user
        name: Update the password for an authenticated user
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.updateUser({
            password: 'new password'
          })
          ```
      - id: update-the-users-metadata
        name: Update the user's metadata
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.updateUser({
            data: { hello: 'world' }
          })
          ```
      - id: update-password-with-reauthentication
        name: Update the user's password with a nonce
        description: |
          If **Secure password change** is enabled in your [project's email provider settings](/dashboard/project/_/auth/providers), updating the user's password would require a nonce if the user **hasn't recently signed in**. The nonce is sent to the user's email or phone number. A user is deemed recently signed in if the session was created in the last 24 hours.
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.updateUser({
            password: 'new password',
            nonce: '123456'
          })
          ```
  - id: get-user-identities
    title: 'getUserIdentities()'
    $ref: '@supabase/auth-js.GoTrueClient.getUserIdentities'
    notes: |
      - The user needs to be signed in to call `getUserIdentities()`.
    examples:
      - id: get-user-identities
        name: Returns a list of identities linked to the user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.getUserIdentities()
          ```
        response: |
          ```json
          {
            "data": {
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "2024-01-01T00:00:00Z",
                  "user_id": "2024-01-01T00:00:00Z",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ]
            },
            "error": null
          }
          ```
  - id: link-identity
    title: 'linkIdentity()'
    $ref: '@supabase/auth-js.GoTrueClient.linkIdentity'
    notes: |
      - The **Enable Manual Linking** option must be enabled from your [project's authentication settings](/dashboard/project/_/settings/auth).
      - The user needs to be signed in to call `linkIdentity()`.
      - If the candidate identity is already linked to the existing user or another user, `linkIdentity()` will fail.
      - If `linkIdentity` is run in the browser, the user is automatically redirected to the returned URL. On the server, you should handle the redirect.
    examples:
      - id: link-identity
        name: Link an identity to a user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.linkIdentity({
            provider: 'github'
          })
          ```
        response: |
          ```json
          {
            data: {
              provider: 'github',
              url: <PROVIDER_URL_TO_REDIRECT_TO>
            },
            error: null
          }
          ```
  - id: unlink-identity
    title: 'unlinkIdentity()'
    $ref: '@supabase/auth-js.GoTrueClient.unlinkIdentity'
    notes: |
      - The **Enable Manual Linking** option must be enabled from your [project's authentication settings](/dashboard/project/_/settings/auth).
      - The user needs to be signed in to call `unlinkIdentity()`.
      - The user must have at least 2 identities in order to unlink an identity.
      - The identity to be unlinked must belong to the user.
    examples:
      - id: unlink-identity
        name: Unlink an identity
        isSpotlight: true
        code: |
          ```js
          // retrieve all identites linked to a user
          const identities = await supabase.auth.getUserIdentities()

          // find the google identity
          const googleIdentity = identities.find(
            identity => identity.provider === 'google'
          )

          // unlink the google identity
          const { error } = await supabase.auth.unlinkIdentity(googleIdentity)
          ```
  - id: send-password-reauthentication
    title: 'reauthenticate()'
    $ref: '@supabase/auth-js.GoTrueClient.reauthenticate'
    notes: |
      - This method is used together with `updateUser()` when a user's password needs to be updated.
      - If you require your user to reauthenticate before updating their password, you need to enable the **Secure password change** option in your [project's email provider settings](/dashboard/project/_/auth/providers).
      - A user is only require to reauthenticate before updating their password if **Secure password change** is enabled and the user **hasn't recently signed in**. A user is deemed recently signed in if the session was created in the last 24 hours.
      - This method will send a nonce to the user's email. If the user doesn't have a confirmed email address, the method will send the nonce to the user's confirmed phone number instead.
    examples:
      - id: send-reauthentication-nonce
        name: Send reauthentication nonce
        description: Sends a reauthentication nonce to the user's email or phone number.
        isSpotlight: true
        code: |
          ```js
          const { error } = await supabase.auth.reauthenticate()
          ```
  - id: resend-email-or-phone-otps
    title: 'resend()'
    $ref: '@supabase/auth-js.GoTrueClient.resend'
    notes: |
      - Resends a signup confirmation, email change or phone change email to the user.
      - Passwordless sign-ins can be resent by calling the `signInWithOtp()` method again.
      - Password recovery emails can be resent by calling the `resetPasswordForEmail()` method again.
      - This method will only resend an email or phone OTP to the user if there was an initial signup, email change or phone change request being made.
      - You can specify a redirect url when you resend an email link using the `emailRedirectTo` option.
    examples:
      - id: resend-email-signup-confirmation
        name: Resend an email signup confirmation
        description: Resends the email signup confirmation to the user
        isSpotlight: true
        code: |
          ```js
          const { error } = await supabase.auth.resend({
            type: 'signup',
            email: '<EMAIL>',
            options: {
              emailRedirectTo: 'https://example.com/welcome'
            }
          })
          ```
      - id: resend-phone-signup-confirmation
        name: Resend a phone signup confirmation
        description: Resends the phone signup confirmation email to the user
        code: |
          ```js
          const { error } = await supabase.auth.resend({
            type: 'sms',
            phone: '*********0'
          })
          ```
      - id: resend-email-change-email
        name: Resend email change email
        description: Resends the email change email to the user
        code: |
          ```js
          const { error } = await supabase.auth.resend({
            type: 'email_change',
            email: '<EMAIL>'
          })
          ```
      - id: resend-phone-change
        name: Resend phone change OTP
        description: Resends the phone change OTP to the user
        code: |
          ```js
          const { error } = await supabase.auth.resend({
            type: 'phone_change',
            phone: '*********0'
          })
          ```
  - id: set-session
    title: 'setSession()'
    $ref: '@supabase/auth-js.GoTrueClient.setSession'
    notes: |
      - This method sets the session using an `access_token` and `refresh_token`.
      - If successful, a `SIGNED_IN` event is emitted.
    examples:
      - id: set-the-session
        name: Set the session
        description: Sets the session data from an access_token and refresh_token, then returns an auth response or error.
        isSpotlight: true
        code: |
          ```js
            const { data, error } = await supabase.auth.setSession({
              access_token,
              refresh_token
            })
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "<EMAIL>",
                  "email_confirmed_at": "2024-01-01T00:00:00Z",
                  "phone": "",
                  "confirmed_at": "2024-01-01T00:00:00Z",
                  "last_sign_in_at": "11111111-1111-1111-1111-111111111111",
                  "app_metadata": {
                    "provider": "email",
                    "providers": [
                      "email"
                    ]
                  },
                  "user_metadata": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "identities": [
                    {
                      "identity_id": "2024-01-01T00:00:00Z",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "is_anonymous": false
                },
                "token_type": "bearer",
                "expires_in": 3500,
                "expires_at": **********
              }
            },
            "error": null
          }
          ```
  - id: refresh-session
    title: 'refreshSession()'
    $ref: '@supabase/auth-js.GoTrueClient.refreshSession'
    notes: |
      - This method will refresh and return a new session whether the current one is expired or not.
    examples:
      - id: refresh-session-using-the-current-session
        name: Refresh session using the current session
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.refreshSession()
          const { session, user } = data
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              },
              "session": {
                "access_token": "<ACCESS_TOKEN>",
                "token_type": "bearer",
                "expires_in": 3600,
                "expires_at": **********,
                "refresh_token": "<REFRESH_TOKEN>",
                "user": {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "aud": "authenticated",
                  "role": "authenticated",
                  "email": "<EMAIL>",
                  "email_confirmed_at": "2024-01-01T00:00:00Z",
                  "phone": "",
                  "confirmed_at": "2024-01-01T00:00:00Z",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "app_metadata": {
                    "provider": "email",
                    "providers": [
                      "email"
                    ]
                  },
                  "user_metadata": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "identities": [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "is_anonymous": false
                }
              }
            },
            "error": null
          }
          ```
      - id: refresh-session-using-a-passed-in-session
        name: Refresh session using a refresh token
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.refreshSession({ refresh_token })
          const { session, user } = data
          ```

  - id: exchange-code-for-session
    title: 'exchangeCodeForSession()'
    $ref: '@supabase/auth-js.GoTrueClient.exchangeCodeForSession'
    notes: |
      - Used when `flowType` is set to `pkce` in client options.
    examples:
      - id: exchange-auth-code
        name: Exchange Auth Code
        isSpotlight: true
        code: |
          ```js
          supabase.auth.exchangeCodeForSession('34e770dd-9ff9-416c-87fa-43b31d7ef225')
          ```
        response: |
          ```json
          {
            "data": {
              session: {
                access_token: '<ACCESS_TOKEN>',
                token_type: 'bearer',
                expires_in: 3600,
                expires_at: **********,
                refresh_token: '<REFRESH_TOKEN>',
                user: {
                  id: '11111111-1111-1111-1111-111111111111',
                  aud: 'authenticated',
                  role: 'authenticated',
                  email: '<EMAIL>'
                  email_confirmed_at: '2024-01-01T00:00:00Z',
                  phone: '',
                  confirmation_sent_at: '2024-01-01T00:00:00Z',
                  confirmed_at: '2024-01-01T00:00:00Z',
                  last_sign_in_at: '2024-01-01T00:00:00Z',
                  app_metadata: {
                    "provider": "email",
                    "providers": [
                      "email",
                      "<OTHER_PROVIDER>"
                    ]
                  },
                  user_metadata: {
                    email: '<EMAIL>',
                    email_verified: true,
                    full_name: 'User Name',
                    iss: '<ISS>',
                    name: 'User Name',
                    phone_verified: false,
                    provider_id: '<PROVIDER_ID>',
                    sub: '<SUB>'
                  },
                  identities: [
                    {
                      "identity_id": "22222222-2222-2222-2222-222222222222",
                      "id": "11111111-1111-1111-1111-111111111111",
                      "user_id": "11111111-1111-1111-1111-111111111111",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": false,
                        "phone_verified": false,
                        "sub": "11111111-1111-1111-1111-111111111111"
                      },
                      "provider": "email",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    },
                    {
                      "identity_id": "33333333-3333-3333-3333-333333333333",
                      "id": "<ID>",
                      "user_id": "<USER_ID>",
                      "identity_data": {
                        "email": "<EMAIL>",
                        "email_verified": true,
                        "full_name": "User Name",
                        "iss": "<ISS>",
                        "name": "User Name",
                        "phone_verified": false,
                        "provider_id": "<PROVIDER_ID>",
                        "sub": "<SUB>"
                      },
                      "provider": "<PROVIDER>",
                      "last_sign_in_at": "2024-01-01T00:00:00Z",
                      "created_at": "2024-01-01T00:00:00Z",
                      "updated_at": "2024-01-01T00:00:00Z",
                      "email": "<EMAIL>"
                    }
                  ],
                  created_at: '2024-01-01T00:00:00Z',
                  updated_at: '2024-01-01T00:00:00Z',
                  is_anonymous: false
                },
                provider_token: '<PROVIDER_TOKEN>',
                provider_refresh_token: '<PROVIDER_REFRESH_TOKEN>'
              },
              user: {
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                email_confirmed_at: '2024-01-01T00:00:00Z',
                phone: '',
                confirmation_sent_at: '2024-01-01T00:00:00Z',
                confirmed_at: '2024-01-01T00:00:00Z',
                last_sign_in_at: '2024-01-01T00:00:00Z',
                app_metadata: {
                  provider: 'email',
                  providers: [
                    "email",
                    "<OTHER_PROVIDER>"
                  ]
                },
                user_metadata: {
                  email: '<EMAIL>',
                  email_verified: true,
                  full_name: 'User Name',
                  iss: '<ISS>',
                  name: 'User Name',
                  phone_verified: false,
                  provider_id: '<PROVIDER_ID>',
                  sub: '<SUB>'
                },
                identities: [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  },
                  {
                    "identity_id": "33333333-3333-3333-3333-333333333333",
                    "id": "<ID>",
                    "user_id": "<USER_ID>",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": true,
                      "full_name": "User Name",
                      "iss": "<ISS>",
                      "name": "User Name",
                      "phone_verified": false,
                      "provider_id": "<PROVIDER_ID>",
                      "sub": "<SUB>"
                    },
                    "provider": "<PROVIDER>",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z',
                is_anonymous: false
              },
              redirectType: null
            },
            "error": null
          }
          ```
  - id: auth-mfa-api
    title: 'Overview'
    notes: |
      This section contains methods commonly used for Multi-Factor Authentication (MFA) and are invoked behind the `supabase.auth.mfa` namespace.

      Currently, there is support for time-based one-time password (TOTP) and phone verification code as the 2nd factor. Recovery codes are not supported but users can enroll multiple factors, with an upper limit of 10.

      Having a 2nd factor for recovery frees the user of the burden of having to store their recovery codes somewhere. It also reduces the attack surface since multiple recovery codes are usually generated compared to just having 1 backup factor.
  - id: mfa-enroll
    title: 'mfa.enroll()'
    $ref: '@supabase/auth-js.GoTrueMFAApi.enroll'
    notes: |
      - Use `totp` or `phone` as the `factorType` and use the returned `id` to create a challenge.
      - To create a challenge, see [`mfa.challenge()`](/docs/reference/javascript/auth-mfa-challenge).
      - To verify a challenge, see [`mfa.verify()`](/docs/reference/javascript/auth-mfa-verify).
      - To create and verify a TOTP challenge in a single step, see [`mfa.challengeAndVerify()`](/docs/reference/javascript/auth-mfa-challengeandverify).
      - To generate a QR code for the `totp` secret in Next.js, you can do the following:
      ```html
      <Image src={data.totp.qr_code} alt={data.totp.uri} layout="fill"></Image>
      ```
      - The `challenge` and `verify` steps are separated when using Phone factors as the user will need time to receive and input the code obtained from the SMS in challenge.
    examples:
      - id: enroll-totp-factor
        name: Enroll a time-based, one-time password (TOTP) factor
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.enroll({
            factorType: 'totp',
            friendlyName: 'your_friendly_name'
          })

          // Use the id to create a challenge.
          // The challenge can be verified by entering the code generated from the authenticator app.
          // The code will be generated upon scanning the qr_code or entering the secret into the authenticator app.
          const { id, type, totp: { qr_code, secret, uri }, friendly_name } = data
          const challenge = await supabase.auth.mfa.challenge({ factorId: id });
          ```
        response: |
          ```json
          {
            data: {
              id: '<ID>',
              type: 'totp'
              totp: {
                qr_code: '<QR_CODE_AS_SVG_DATA>',
                secret: '<SECRET>',
                uri: '<URI>',
              }
              friendly_name?: 'Important app'
            },
            error: null
          }
          ```
      - id: enroll-phone-factor
        name: Enroll a Phone Factor
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.enroll({
            factorType: 'phone',
            friendlyName: 'your_friendly_name',
            phone: '+12345678',
          })

          // Use the id to create a challenge and send an SMS with a code to the user.
          const { id, type, friendly_name, phone } = data

          const challenge = await supabase.auth.mfa.challenge({ factorId: id });
          ```
        response: |
          ```json
          {
            data: {
              id: '<ID>',
              type: 'phone',
              friendly_name?: 'Important app',
              phone: '+5787123456'
            },
            error: null
          }
          ```
  - id: mfa-challenge
    title: 'mfa.challenge()'
    $ref: '@supabase/auth-js.GoTrueMFAApi.challenge'
    notes: |
      - An [enrolled factor](/docs/reference/javascript/auth-mfa-enroll) is required before creating a challenge.
      - To verify a challenge, see [`mfa.verify()`](/docs/reference/javascript/auth-mfa-verify).
      - A phone factor sends a code to the user upon challenge. The channel defaults to `sms` unless otherwise specified.
    examples:
      - id: create-mfa-challenge
        name: Create a challenge for a factor
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.challenge({
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225'
          })
          ```
        response: |
          ```json
          {
            data: {
              id: '<ID>',
              type: 'totp',
              expires_at: **********
            },
            error: null
          }
          ```
      - id: create-mfa-phone-challenge
        name: Create a challenge for a phone factor
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.challenge({
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
          })
          ```
        response: |
          ```json
          {
            data: {
              id: '<ID>',
              type: 'phone',
              expires_at: **********
            },
            error: null
          }
          ```
      - id: create-mfa-phone-challenge-whatsapp
        name: Create a challenge for a phone factor (WhatsApp)
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.challenge({
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
            channel: 'whatsapp',
          })
          ```
        response: |
          ```json
          {
            data: {
              id: '<ID>',
              expires_at: **********
            },
            error: null
          }
          ```
  - id: mfa-verify
    title: 'mfa.verify()'
    $ref: '@supabase/auth-js.GoTrueMFAApi.verify'
    notes: |
      - To verify a challenge, please [create a challenge](/docs/reference/javascript/auth-mfa-challenge) first.
    examples:
      - id: verify-challenge
        name: Verify a challenge for a factor
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.verify({
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
            challengeId: '4034ae6f-a8ce-4fb5-8ee5-69a5863a7c15',
            code: '123456'
          })
          ```
        response: |
          ```json
          {
            data: {
              access_token: '<ACCESS_TOKEN>',
              token_type: 'Bearer',
              expires_in: 3600,
              refresh_token: '<REFRESH_TOKEN>',
              user: {
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                email_confirmed_at: '2024-01-01T00:00:00Z',
                phone: '',
                confirmation_sent_at: '2024-01-01T00:00:00Z',
                confirmed_at: '2024-01-01T00:00:00Z',
                last_sign_in_at: '2024-01-01T00:00:00Z',
                app_metadata: {
                  provider: 'email',
                  providers: [
                    "email",
                  ]
                },
                user_metadata: {},
                identities: [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": true,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  },
                ],
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z',
                is_anonymous: false,
                factors: [
                  "id": '<ID>',
                  "friendly_name": 'Important Auth App',
                  "factor_type": 'totp',
                  "status": 'verified',
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z"
                ]
              }
            }
            error: null
          }
          ```
  - id: mfa-challenge-and-verify
    title: 'mfa.challengeAndVerify()'
    $ref: '@supabase/auth-js.GoTrueMFAApi.challengeAndVerify'
    notes: |
      - Intended for use with only TOTP factors.
      - An [enrolled factor](/docs/reference/javascript/auth-mfa-enroll) is required before invoking `challengeAndVerify()`.
      - Executes [`mfa.challenge()`](/docs/reference/javascript/auth-mfa-challenge) and [`mfa.verify()`](/docs/reference/javascript/auth-mfa-verify) in a single step.
    examples:
      - id: challenge-and-verify
        name: Create and verify a challenge for a factor
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.challengeAndVerify({
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
            code: '123456'
          })
          ```
        response: |
          ```json
          {
            data: {
              access_token: '<ACCESS_TOKEN>',
              token_type: 'Bearer',
              expires_in: 3600,
              refresh_token: '<REFRESH_TOKEN>',
              user: {
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                email_confirmed_at: '2024-01-01T00:00:00Z',
                phone: '',
                confirmation_sent_at: '2024-01-01T00:00:00Z',
                confirmed_at: '2024-01-01T00:00:00Z',
                last_sign_in_at: '2024-01-01T00:00:00Z',
                app_metadata: {
                  provider: 'email',
                  providers: [
                    "email",
                  ]
                },
                user_metadata: {},
                identities: [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": true,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  },
                ],
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z',
                is_anonymous: false,
                factors: [
                  "id": '<ID>',
                  "friendly_name": 'Important Auth App',
                  "factor_type": 'totp',
                  "status": 'verified',
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z"
                ]
              }
            }
            error: null
          }
          ```
  - id: mfa-unenroll
    title: 'mfa.unenroll()'
    $ref: '@supabase/auth-js.GoTrueMFAApi.unenroll'
    examples:
      - id: unenroll-a-factor
        name: Unenroll a factor
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.unenroll({
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
          })
          ```
        response: |
          ```json
          {
            data: {
              id: '<FACTOR_ID>'
            },
            error: null
          }
          ```
  - id: mfa-get-authenticator-assurance-level
    title: 'mfa.getAuthenticatorAssuranceLevel()'
    $ref: '@supabase/auth-js.GoTrueMFAApi.getAuthenticatorAssuranceLevel'
    notes: |
      - Authenticator Assurance Level (AAL) is the measure of the strength of an authentication mechanism.
      - In Supabase, having an AAL of `aal1` refers to having the 1st factor of authentication such as an email and password or OAuth sign-in while `aal2` refers to the 2nd factor of authentication such as a time-based, one-time-password (TOTP) or Phone factor.
      - If the user has a verified factor, the `nextLevel` field will return `aal2`, else, it will return `aal1`.
    examples:
      - id: get-aal
        name: Get the AAL details of a session
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel()
          const { currentLevel, nextLevel, currentAuthenticationMethods } = data
          ```
        response: |
          ```json
          {
            data: {
              currentLevel: 'aal1',
              nextLevel: 'aal2',
              currentAuthenticationMethods: [
                {
                  method: 'password',
                  timestamp: **********
                }
              ]
            }
            error: null
          }
          ```
  - id: admin-api
    title: 'Overview'
    notes: |
      - Any method under the `supabase.auth.admin` namespace requires a `service_role` key.
      - These methods are considered admin methods and should be called on a trusted server. Never expose your `service_role` key in the browser.
    examples:
      - id: create-auth-admin-client
        name: Create server-side auth client
        isSpotlight: true
        code: |
          ```js
          import { createClient } from '@supabase/supabase-js'

          const supabase = createClient(supabase_url, service_role_key, {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            }
          })

          // Access auth admin api
          const adminAuthClient = supabase.auth.admin
          ```

  - id: get-user-by-id
    title: 'getUserById()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.getUserById'
    notes: |
      - Fetches the user object from the database based on the user's id.
      - The `getUserById()` method requires the user's id which maps to the `auth.users.id` column.
    examples:
      - id: fetch-the-user-object-using-the-access-token-jwt
        name: Fetch the user object using the access_token jwt
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.getUserById(1)
          ```
        response: |
          ```json
          {
            data: {
              user: {
                id: '1',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                email_confirmed_at: '2024-01-01T00:00:00Z',
                phone: '',
                confirmation_sent_at: '2024-01-01T00:00:00Z',
                confirmed_at: '2024-01-01T00:00:00Z',
                last_sign_in_at: '2024-01-01T00:00:00Z',
                app_metadata: {},
                user_metadata: {},
                identities: [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "1",
                    "user_id": "1",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": true,
                      "phone_verified": false,
                      "sub": "1"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  },
                ],
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z',
                is_anonymous: false,
              }
            }
            error: null
          }
          ```

  - id: list-users
    title: 'listUsers()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.listUsers'
    notes: |
      - Defaults to return 50 users per page.
    examples:
      - id: get-a-full-list-of-users
        name: Get a page of users
        isSpotlight: true
        code: |
          ```js
          const { data: { users }, error } = await supabase.auth.admin.listUsers()
          ```
      - id: get-paginated-list-of-users
        name: Paginated list of users
        isSpotlight: false
        code: |
          ```js
          const { data: { users }, error } = await supabase.auth.admin.listUsers({
            page: 1,
            perPage: 1000
          })
          ```
  - id: create-user
    title: 'createUser()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.createUser'
    notes: |
      - To confirm the user's email address or phone number, set `email_confirm` or `phone_confirm` to true. Both arguments default to false.
      - `createUser()` will not send a confirmation email to the user. You can use [`inviteUserByEmail()`](/docs/reference/javascript/auth-admin-inviteuserbyemail) if you want to send them an email invite instead.
      - If you are sure that the created user's email or phone number is legitimate and verified, you can set the `email_confirm` or `phone_confirm` param to `true`.
    examples:
      - id: create-a-new-user-with-custom-user-metadata
        name: With custom user metadata
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.createUser({
            email: '<EMAIL>',
            password: 'password',
            user_metadata: { name: 'Yoda' }
          })
          ```
        response: |
          ```json
          {
            data: {
              user: {
                id: '1',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                email_confirmed_at: '2024-01-01T00:00:00Z',
                phone: '',
                confirmation_sent_at: '2024-01-01T00:00:00Z',
                confirmed_at: '2024-01-01T00:00:00Z',
                last_sign_in_at: '2024-01-01T00:00:00Z',
                app_metadata: {},
                user_metadata: {},
                identities: [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "1",
                    "user_id": "1",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": true,
                      "phone_verified": false,
                      "sub": "1"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  },
                ],
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-01-01T00:00:00Z',
                is_anonymous: false,
              }
            }
            error: null
          }
          ```
      - id: auto-confirm-the-users-email
        name: Auto-confirm the user's email
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.createUser({
            email: '<EMAIL>',
            email_confirm: true
          })
          ```
      - id: auto-confirm-the-users-phone-number
        name: Auto-confirm the user's phone number
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.createUser({
            phone: '*********0',
            phone_confirm: true
          })
          ```
  - id: delete-user
    title: 'deleteUser()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.deleteUser'
    notes: |
      - The `deleteUser()` method requires the user's ID, which maps to the `auth.users.id` column.
    examples:
      - id: removes-a-user
        name: Removes a user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.deleteUser(
            '715ed5db-f090-4b8c-a067-640ecee36aa0'
          )
          ```
        response: |
          ```json
          {
            "data": {
              "user": {}
            },
            "error": null
          }
          ```
  - id: invite-user-by-email
    title: 'inviteUserByEmail()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.inviteUserByEmail'
    notes: |
      - Sends an invite link to the user's email address.
      - The `inviteUserByEmail()` method is typically used by administrators to invite users to join the application.
      - Note that PKCE is not supported when using `inviteUserByEmail`. This is because the browser initiating the invite is often different from the browser accepting the invite which makes it difficult to provide the security guarantees required of the PKCE flow.
    examples:
      - id: invite-a-user
        name: Invite a user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.inviteUserByEmail('<EMAIL>')
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "invited_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmation_sent_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {},
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              }
            },
            "error": null
          }
          ```

  - id: reset-password-for-email
    title: 'resetPasswordForEmail()'
    $ref: '@supabase/auth-js.GoTrueClient.resetPasswordForEmail'
    notes: |
      - The password reset flow consist of 2 broad steps: (i) Allow the user to login via the password reset link; (ii) Update the user's password.
      - The `resetPasswordForEmail()` only sends a password reset link to the user's email.
      To update the user's password, see [`updateUser()`](/docs/reference/javascript/auth-updateuser).
      - A `PASSWORD_RECOVERY` event will be emitted when the password recovery link is clicked.
      You can use [`onAuthStateChange()`](/docs/reference/javascript/auth-onauthstatechange) to listen and invoke a callback function on these events.
      - When the user clicks the reset link in the email they are redirected back to your application.
      You can configure the URL that the user is redirected to with the `redirectTo` parameter.
      See [redirect URLs and wildcards](/docs/guides/auth#redirect-urls-and-wildcards) to add additional redirect URLs to your project.
      - After the user has been redirected successfully, prompt them for a new password and call `updateUser()`:
      ```js
      const { data, error } = await supabase.auth.updateUser({
        password: new_password
      })
      ```
    examples:
      - id: reset-password
        name: Reset password
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: 'https://example.com/update-password',
          })
          ```
        response: |
          ```json
          {
            data: {}
            error: null
          }
          ```
      - id: reset-password-react
        name: Reset password (React)
        isSpotlight: true
        code: |
          ```js
          /**
           * Step 1: Send the user an email to get a password reset token.
           * This email contains a link which sends the user back to your application.
           */
          const { data, error } = await supabase.auth
            .resetPasswordForEmail('<EMAIL>')

          /**
           * Step 2: Once the user is redirected back to your application,
           * ask the user to reset their password.
           */
           useEffect(() => {
             supabase.auth.onAuthStateChange(async (event, session) => {
               if (event == "PASSWORD_RECOVERY") {
                 const newPassword = prompt("What would you like your new password to be?");
                 const { data, error } = await supabase.auth
                   .updateUser({ password: newPassword })

                 if (data) alert("Password updated successfully!")
                 if (error) alert("There was an error updating your password.")
               }
             })
           }, [])
          ```

  - id: generate-link
    title: 'generateLink()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.generateLink'
    notes: |
      - The following types can be passed into `generateLink()`: `signup`, `magiclink`, `invite`, `recovery`, `email_change_current`, `email_change_new`, `phone_change`.
      - `generateLink()` only generates the email link for `email_change_email` if the **Secure email change** is enabled in your project's [email auth provider settings](/dashboard/project/_/auth/providers).
      - `generateLink()` handles the creation of the user for `signup`, `invite` and `magiclink`.
    overwriteParams:
      - name: params
        type: GenerateLinkParams
        subContent:
          - name: type
            type: 'signup | invite | magiclink | recovery | email_change_current | email_change_new'
          - name: email
            type: string
          - name: password
            type: string
            isOptional: true
            description: Only required if type is `signup`.
          - name: newEmail
            type: string
            isOptional: true
            description: Only required if type is `email_change_current` or `email_change_new`.
          - name: options
            type: object
            isOptional: true
            subContent:
              - name: data
                type: object
                description: >
                  Custom JSON object containing user metadata, to be stored in the `raw_user_meta_data` column.
                  Only accepted if type is `signup`, `invite`, or `magiclink`.
              - name: redirectTo
                type: string
                description: >
                  A redirect URL which will be appended to the generated email link.
    examples:
      - id: generate-a-signup-link
        name: Generate a signup link
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.generateLink({
            type: 'signup',
            email: '<EMAIL>',
            password: 'secret'
          })
          ```
        response: |
          ```json
          {
            "data": {
              "properties": {
                "action_link": "<LINK_TO_SEND_TO_USER>",
                "email_otp": "999999",
                "hashed_token": "<HASHED_TOKEN",
                "redirect_to": "<REDIRECT_URL>",
                "verification_type": "signup"
              },
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "phone": "",
                "confirmation_sent_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {},
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              }
            },
            "error": null
          }
          ```
      - id: generate-an-invite-link
        name: Generate an invite link
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.generateLink({
            type: 'invite',
            email: '<EMAIL>'
          })
          ```
      - id: generate-a-magic-link
        name: Generate a magic link
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.generateLink({
            type: 'magiclink',
            email: '<EMAIL>'
          })
          ```
      - id: generate-a-recovery-link
        name: Generate a recovery link
        isSpotlight: false
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.generateLink({
            type: 'recovery',
            email: '<EMAIL>'
          })
          ```
      - id: generate-links-to-change-current-email-address
        name: Generate links to change current email address
        isSpotlight: false
        code: |
          ```js
          // generate an email change link to be sent to the current email address
          const { data, error } = await supabase.auth.admin.generateLink({
            type: 'email_change_current',
            email: '<EMAIL>',
            newEmail: '<EMAIL>'
          })

          // generate an email change link to be sent to the new email address
          const { data, error } = await supabase.auth.admin.generateLink({
            type: 'email_change_new',
            email: '<EMAIL>',
            newEmail: '<EMAIL>'
          })
          ```

  - id: update-user-by-id
    title: 'updateUserById()'
    $ref: '@supabase/auth-js.GoTrueAdminApi.updateUserById'
    examples:
      - id: updates-a-users-email
        name: Updates a user's email
        isSpotlight: false
        code: |
          ```js
          const { data: user, error } = await supabase.auth.admin.updateUserById(
            '11111111-1111-1111-1111-111111111111',
            { email: '<EMAIL>' }
          )
          ```
        response: |
          ```json
          {
            "data": {
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "recovery_sent_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              }
            },
            "error": null
          }
          ```
      - id: updates-a-users-password
        name: Updates a user's password
        isSpotlight: false
        code: |
          ```js
          const { data: user, error } = await supabase.auth.admin.updateUserById(
            '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
            { password: 'new_password' }
          )
          ```
      - id: updates-a-users-metadata
        name: Updates a user's metadata
        isSpotlight: true
        code: |
          ```js
          const { data: user, error } = await supabase.auth.admin.updateUserById(
            '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
            { user_metadata: { hello: 'world' } }
          )
          ```
      - id: updates-a-users-app-metadata
        name: Updates a user's app_metadata
        isSpotlight: false
        code: |
          ```js
          const { data: user, error } = await supabase.auth.admin.updateUserById(
            '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
            { app_metadata: { plan: 'trial' } }
          )
          ```
      - id: confirms-a-users-email-address
        name: Confirms a user's email address
        isSpotlight: false
        code: |
          ```js
          const { data: user, error } = await supabase.auth.admin.updateUserById(
            '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
            { email_confirm: true }
          )
          ```
      - id: confirms-a-users-phone-number
        name: Confirms a user's phone number
        isSpotlight: false
        code: |
          ```js
          const { data: user, error } = await supabase.auth.admin.updateUserById(
            '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
            { phone_confirm: true }
          )
          ```
  - id: mfa-list-factors-admin
    title: 'mfa.listFactors()'
    $ref: '@supabase/auth-js.GoTrueAdminMFAApi.listFactors'
    examples:
      - id: list-factors
        name: List all factors for a user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.mfa.listFactors()
          ```
        response: |
          ```json
          {
            data: {
              factors: Factor[
                {
                  id: '<ID>',
                  friendly_name: 'Auth App Factor',
                  factor_type: 'totp',
                  status: 'verified',
                  created_at: '2024-01-01T00:00:00Z',
                  updated_at: '2024-01-01T00:00:00Z'
                }
              ]
            },
            error: null
          }
          ```
  - id: mfa-delete-factor
    title: 'mfa.deleteFactor()'
    $ref: '@supabase/auth-js.GoTrueAdminMFAApi.deleteFactor'
    examples:
      - id: delete-factor
        name: Delete a factor for a user
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.auth.admin.mfa.deleteFactor({
            id: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
            userId: 'a89baba7-b1b7-440f-b4bb-91026967f66b',
          })
          ```
        response: |
          ```json
          {
            data: {
              id: '34e770dd-9ff9-416c-87fa-43b31d7ef225'
            },
            error: null
          }
          ```
  - id: select
    title: 'Fetch data: select()'
    $ref: '@supabase/postgrest-js.PostgrestQueryBuilder.select'
    notes: |
      - By default, Supabase projects return a maximum of 1,000 rows. This setting can be changed in your project's [API settings](/dashboard/project/_/settings/api). It's recommended that you keep it low to limit the payload size of accidental or malicious requests. You can use `range()` queries to paginate through your data.
      - `select()` can be combined with [Filters](/docs/reference/javascript/using-filters)
      - `select()` can be combined with [Modifiers](/docs/reference/javascript/using-modifiers)
      - `apikey` is a reserved keyword if you're using the [Supabase Platform](/docs/guides/platform) and [should be avoided as a column name](https://github.com/supabase/supabase/issues/5465).
    examples:
      - id: getting-your-data
        name: Getting your data
        code: |
          ```js
          const { data, error } = await supabase
            .from('characters')
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Harry'),
              (2, 'Frodo'),
              (3, 'Katniss');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Harry"
              },
              {
                "id": 2,
                "name": "Frodo"
              },
              {
                "id": 3,
                "name": "Katniss"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
      - id: selecting-specific-columns
        name: Selecting specific columns
        code: |
          ```js
          const { data, error } = await supabase
            .from('characters')
            .select('name')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Frodo'),
              (2, 'Harry'),
              (3, 'Katniss');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Frodo"
              },
              {
                "name": "Harry"
              },
              {
                "name": "Katniss"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
      - id: query-referenced-tables
        name: Query referenced tables
        description: |
          If your database has foreign key relationships, you can query related tables too.
        code: |
          ```js
          const { data, error } = await supabase
            .from('orchestal_sections')
            .select(`
              name,
              instruments (
                name
              )
            `)
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  }
                ]
              },
              {
                "name": "woodwinds",
                "instruments": [
                  {
                    "name": "flute"
                  }
                ]
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
      - id: query-referenced-tables-with-spaces-in-their-names
        name: Query referenced tables with spaces in their names
        description: |
          If your table name contains spaces, you must use double quotes in the `select` statement to reference the table.
        code: |
          ```js
          const { data, error } = await supabase
            .from('orchestal sections')
            .select(`
              name,
              "musical instruments" (
                name
              )
            `)
          ```
        data:
          sql: |
            ```sql
            create table
              "orchestral sections" (id int8 primary key, name text);
            create table
              "musical instruments" (
                id int8 primary key,
                section_id int8 not null references "orchestral sections",
                name text
              );

            insert into
              "orchestral sections" (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              "musical instruments" (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "musical instruments": [
                  {
                    "name": "violin"
                  }
                ]
              },
              {
                "name": "woodwinds",
                "musical instruments": [
                  {
                    "name": "flute"
                  }
                ]
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
      - id: query-referenced-tables-through-a-join-table
        name: Query referenced tables through a join table
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .select(`
              name,
              teams (
                name
              )
            `)
            ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text
              );
            create table
              teams (
                id int8 primary key,
                name text
              );
            -- join table
            create table
              users_teams (
                user_id int8 not null references users,
                team_id int8 not null references teams,
                -- both foreign keys must be part of a composite primary key
                primary key (user_id, team_id)
              );

            insert into
              users (id, name)
            values
              (1, 'Kiran'),
              (2, 'Evan');
            insert into
              teams (id, name)
            values
              (1, 'Green'),
              (2, 'Blue');
            insert into
              users_teams (user_id, team_id)
            values
              (1, 1),
              (1, 2),
              (2, 2);
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "name": "Kiran",
                  "teams": [
                    {
                      "name": "Green"
                    },
                    {
                      "name": "Blue"
                    }
                  ]
                },
                {
                  "name": "Evan",
                  "teams": [
                    {
                      "name": "Blue"
                    }
                  ]
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        description: |
          If you're in a situation where your tables are **NOT** directly
          related, but instead are joined by a _join table_, you can still use
          the `select()` method to query the related data. The join table needs
          to have the foreign keys as part of its composite primary key.
        hideCodeBlock: true
      - id: query-the-same-referenced-table-multiple-times
        name: Query the same referenced table multiple times
        code: |
          ```ts
          const { data, error } = await supabase
            .from('messages')
            .select(`
              content,
              from:sender_id(name),
              to:receiver_id(name)
            `)

          // To infer types, use the name of the table (in this case `users`) and
          // the name of the foreign key constraint.
          const { data, error } = await supabase
            .from('messages')
            .select(`
              content,
              from:users!messages_sender_id_fkey(name),
              to:users!messages_receiver_id_fkey(name)
            `)
          ```
        data:
          sql: |
            ```sql
             create table
             users (id int8 primary key, name text);

             create table
               messages (
                 sender_id int8 not null references users,
                 receiver_id int8 not null references users,
                 content text
               );

             insert into
               users (id, name)
             values
               (1, 'Kiran'),
               (2, 'Evan');

             insert into
               messages (sender_id, receiver_id, content)
             values
               (1, 2, '👋');
             ```
        response: |
          ```json
          {
            "data": [
              {
                "content": "👋",
                "from": {
                  "name": "Kiran"
                },
                "to": {
                  "name": "Evan"
                }
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          If you need to query the same referenced table twice, use the name of the
          joined column to identify which join to use. You can also give each
          column an alias.
        hideCodeBlock: true
      - id: query-nested-foreign-tables-through-a-join-table
        name: Query nested foreign tables through a join table
        code: |
          ```ts
            const { data, error } = await supabase
              .from('games')
              .select(`
                game_id:id,
                away_team:teams!games_away_team_fkey (
                  users (
                    id,
                    name
                  )
                )
              `)
            ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text
              );
            create table
              teams (
                id int8 primary key,
                name text
              );
            -- join table
            create table
              users_teams (
                user_id int8 not null references users,
                team_id int8 not null references teams,

                primary key (user_id, team_id)
              );
            create table
              games (
                id int8 primary key,
                home_team int8 not null references teams,
                away_team int8 not null references teams,
                name text
              );

            insert into users (id, name)
            values
              (1, 'Kiran'),
              (2, 'Evan');
            insert into
              teams (id, name)
            values
              (1, 'Green'),
              (2, 'Blue');
            insert into
              users_teams (user_id, team_id)
            values
              (1, 1),
              (1, 2),
              (2, 2);
            insert into
              games (id, home_team, away_team, name)
            values
              (1, 1, 2, 'Green vs Blue'),
              (2, 2, 1, 'Blue vs Green');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "game_id": 1,
                  "away_team": {
                    "users": [
                      {
                        "id": 1,
                        "name": "Kiran"
                      },
                      {
                        "id": 2,
                        "name": "Evan"
                      }
                    ]
                  }
                },
                {
                  "game_id": 2,
                  "away_team": {
                    "users": [
                      {
                        "id": 1,
                        "name": "Kiran"
                      }
                    ]
                  }
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        description: |
          You can use the result of a joined table to gather data in
          another foreign table. With multiple references to the same foreign
          table you must specify the column on which to conduct the join.
        hideCodeBlock: true
      - id: filtering-through-referenced-tables
        name: Filtering through referenced tables
        code: |
          ```ts
          const { data, error } = await supabase
            .from('instruments')
            .select('name, orchestral_sections(*)')
            .eq('orchestral_sections.name', 'percussion')
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "flute",
                "orchestral_sections": null
              },
              {
                "name": "violin",
                "orchestral_sections": null
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          If the filter on a referenced table's column is not satisfied, the referenced
          table returns `[]` or `null` but the parent table is not filtered out.
          If you want to filter out the parent table rows, use the `!inner` hint
        hideCodeBlock: true
      - id: querying-referenced-table-with-count
        name: Querying referenced table with count
        code: |
          ```ts
          const { data, error } = await supabase
            .from('orchestral_sections')
            .select(`*, instruments(count)`)
          ```
        data:
          sql: |
            ```sql
            create table orchestral_sections (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text
            );

            create table characters (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text,
              "section_id" "uuid" references public.orchestral_sections on delete cascade
            );

            with section as (
              insert into orchestral_sections (name)
              values ('strings') returning id
            )
            insert into instruments (name, section_id) values
            ('violin', (select id from section)),
            ('viola', (select id from section)),
            ('cello', (select id from section)),
            ('double bass', (select id from section));
            ```
        response: |
          ```json
          [
            {
              "id": "693694e7-d993-4360-a6d7-6294e325d9b6",
              "name": "strings",
              "instruments": [
                {
                  "count": 4
                }
              ]
            }
          ]
          ```
        description: |
          You can get the number of rows in a related table by using the
          **count** property.
        hideCodeBlock: true
      - id: querying-with-count-option
        name: Querying with count option
        code: |
          ```ts
          const { count, error } = await supabase
            .from('characters')
            .select('*', { count: 'exact', head: true })
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "count": 3,
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          You can get the number of rows by using the
          [count](/docs/reference/javascript/select#parameters) option.
        hideCodeBlock: true
      - id: querying-json-data
        name: Querying JSON data
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .select(`
              id, name,
              address->city
            `)
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Frodo', '{"city":"Hobbiton"}');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Frodo",
                "city": "Hobbiton"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          You can select and filter data inside of
          [JSON](/docs/guides/database/json) columns. Postgres offers some
          [operators](/docs/guides/database/json#query-the-jsonb-data) for
          querying JSON data.
        hideCodeBlock: true
      - id: querying-referenced-table-with-inner-join
        name: Querying referenced table with inner join
        code: |
          ```ts
          const { data, error } = await supabase
            .from('instruments')
            .select('name, orchestral_sections!inner(name)')
            .eq('orchestral_sections.name', 'woodwinds')
            .limit(1)
          ```
        data:
          sql: |
            ```sql
            create table orchestral_sections (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text
            );

            create table instruments (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text,
              "section_id" "uuid" references public.orchestral_sections on delete cascade
            );

            with section as (
              insert into orchestral_sections (name)
              values ('woodwinds') returning id
            )
            insert into instruments (name, section_id) values
            ('flute', (select id from section)),
            ('clarinet', (select id from section)),
            ('bassoon', (select id from section)),
            ('piccolo', (select id from section));
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "flute",
                "orchestral_sections": {"name": "woodwinds"}
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          If you don't want to return the referenced table contents, you can leave the parenthesis empty.
          Like `.select('name, orchestral_sections!inner()')`.
        hideCodeBlock: true
      - id: switching-schemas-per-query
        name: Switching schemas per query
        code: |
          ```ts
          const { data, error } = await supabase
            .schema('myschema')
            .from('mytable')
            .select()
          ```
        data:
          sql: |
            ```sql
            create schema myschema;

            create table myschema.mytable (
              id uuid primary key default gen_random_uuid(),
              data text
            );

            insert into myschema.mytable (data) values ('mydata');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": "4162e008-27b0-4c0f-82dc-ccaeee9a624d",
                "data": "mydata"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          In addition to setting the schema during initialization, you can also switch schemas on a per-query basis.
          Make sure you've set up your [database privileges and API settings](/docs/guides/api/using-custom-schemas).
        hideCodeBlock: true

  - id: insert
    title: 'Create data: insert()'
    $ref: '@supabase/postgrest-js.PostgrestQueryBuilder.insert'
    examples:
      - id: create-a-record
        name: Create a record
        code: |
          ```ts
          const { error } = await supabase
            .from('countries')
            .insert({ id: 1, name: 'Mordor' })
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);
            ```
        response: |
          ```json
          {
            "status": 201,
            "statusText": "Created"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: create-a-record-and-return-it
        name: Create a record and return it
        code: |
          ```ts
          const { data, error } = await supabase
            .from('countries')
            .insert({ id: 1, name: 'Mordor' })
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mordor"
              }
            ],
            "status": 201,
            "statusText": "Created"
          }
          ```
        hideCodeBlock: true
      - id: bulk-create
        name: Bulk create
        code: |
          ```ts
          const { error } = await supabase
            .from('countries')
            .insert([
              { id: 1, name: 'Mordor' },
              { id: 1, name: 'The Shire' },
            ])
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);
            ```
        response: |
          ```json
          {
            "error": {
              "code": "23505",
              "details": "Key (id)=(1) already exists.",
              "hint": null,
              "message": "duplicate key value violates unique constraint \"countries_pkey\""
            },
            "status": 409,
            "statusText": "Conflict"
          }
          ```
        description: |
          A bulk create operation is handled in a single transaction.
          If any of the inserts fail, none of the rows are inserted.
        hideCodeBlock: true

  - id: update
    title: 'Modify data: update()'
    $ref: '@supabase/postgrest-js.PostgrestQueryBuilder.update'
    notes: |
      - `update()` should always be combined with [Filters](/docs/reference/javascript/using-filters) to target the item(s) you wish to update.
    examples:
      - id: updating-your-data
        name: Updating your data
        code: |
          ```ts
          const { error } = await supabase
            .from('instruments')
            .update({ name: 'piano' })
            .eq('id', 1)
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "status": 204,
            "statusText": "No Content"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: update-a-record-and-return-it
        name: Update a record and return it
        code: |
          ```ts
          const { data, error } = await supabase
            .from('instruments')
            .update({ name: 'piano' })
            .eq('id', 1)
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "piano"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
      - id: updating-json-data
        name: Updating JSON data
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .update({
              address: {
                street: 'Melrose Place',
                postcode: 90210
              }
            })
            .eq('address->postcode', 90210)
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210 }');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Michael",
                "address": {
                  "street": "Melrose Place",
                  "postcode": 90210
                }
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres offers some
          [operators](/docs/guides/database/json#query-the-jsonb-data) for
          working with JSON data. Currently, it is only possible to update the entire JSON document.
        hideCodeBlock: true
  - id: upsert
    title: 'Upsert data: upsert()'
    $ref: '@supabase/postgrest-js.PostgrestQueryBuilder.upsert'
    notes: |
      - Primary keys must be included in `values` to use upsert.
    examples:
      - id: upsert-your-data
        name: Upsert your data
        code: |
          ```ts
          const { data, error } = await supabase
            .from('instruments')
            .upsert({ id: 1, name: 'piano' })
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "piano"
              }
            ],
            "status": 201,
            "statusText": "Created"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: bulk-upsert-your-data
        name: Bulk Upsert your data
        code: |
          ```ts
          const { data, error } = await supabase
            .from('instruments')
            .upsert([
              { id: 1, name: 'piano' },
              { id: 2, name: 'harp' },
            ])
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "piano"
              },
              {
                "id": 2,
                "name": "harp"
              }
            ],
            "status": 201,
            "statusText": "Created"
          }
          ```
        hideCodeBlock: true
      - id: upserting-into-tables-with-constraints
        name: Upserting into tables with constraints
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .upsert({ id: 42, handle: 'saoirse', display_name: 'Saoirse' }, { onConflict: 'handle' })
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 generated by default as identity primary key,
                handle text not null unique,
                display_name text
              );

            insert into
              users (id, handle, display_name)
            values
              (1, 'saoirse', null);
            ```
        response: |
          ```json
          {
            "error": {
              "code": "23505",
              "details": "Key (handle)=(saoirse) already exists.",
              "hint": null,
              "message": "duplicate key value violates unique constraint \"users_handle_key\""
            },
            "status": 409,
            "statusText": "Conflict"
          }
          ```
        description: |
          In the following query, `upsert()` implicitly uses the `id`
          (primary key) column to determine conflicts. If there is no existing
          row with the same `id`, `upsert()` inserts a new row, which
          will fail in this case as there is already a row with `handle` `"saoirse"`.
          Using the `onConflict` option, you can instruct `upsert()` to use
          another column with a unique constraint to determine conflicts.
        hideCodeBlock: true

  - id: delete
    title: 'Delete data: delete()'
    $ref: '@supabase/postgrest-js.PostgrestQueryBuilder.delete'
    notes: |
      - `delete()` should always be combined with [filters](/docs/reference/javascript/using-filters) to target the item(s) you wish to delete.
      - If you use `delete()` with filters and you have
        [RLS](/docs/learn/auth-deep-dive/auth-row-level-security) enabled, only
        rows visible through `SELECT` policies are deleted. Note that by default
        no rows are visible, so you need at least one `SELECT`/`ALL` policy that
        makes the rows visible.
      - When using `delete().in()`, specify an array of values to target multiple rows with a single query. This is particularly useful for batch deleting entries that share common criteria, such as deleting users by their IDs. Ensure that the array you provide accurately represents all records you intend to delete to avoid unintended data removal.

    examples:
      - id: delete-records
        name: Delete a single record
        code: |
          ```ts
          const response = await supabase
            .from('countries')
            .delete()
            .eq('id', 1)
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Mordor');
            ```
        response: |
          ```json
          {
            "status": 204,
            "statusText": "No Content"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true

      - id: delete-records-and-return-it
        name: Delete a record and return it
        code: |
          ```ts
          const { data, error } = await supabase
            .from('countries')
            .delete()
            .eq('id', 1)
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Mordor');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mordor"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true

      - id: delete-multiple-records
        name: Delete multiple records
        code: |
          ```ts
          const response = await supabase
            .from('countries')
            .delete()
            .in('id', [1, 2, 3])
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Rohan'), (2, 'The Shire'), (3, 'Mordor');
            ```
        response: |
          ```json
          {
            "status": 204,
            "statusText": "No Content"
          }
          ```
        hideCodeBlock: true

  - id: rpc
    title: 'Postgres functions: rpc()'
    description: |
      You can call Postgres functions as _Remote Procedure Calls_, logic in your database that you can execute from anywhere.
      Functions are useful when the logic rarely changes—like for password resets and updates.

      ```sql
      create or replace function hello_world() returns text as $$
        select 'Hello world';
      $$ language sql;
      ```

      To call Postgres functions on [Read Replicas](/docs/guides/platform/read-replicas), use the `get: true` option.
    $ref: '@supabase/postgrest-js.PostgrestClient.rpc'
    examples:
      - id: call-a-postgres-function-without-arguments
        name: Call a Postgres function without arguments
        code: |
          ```ts
          const { data, error } = await supabase.rpc('hello_world')
          ```
        data:
          sql: |
            ```sql
            create function hello_world() returns text as $$
              select 'Hello world';
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": "Hello world",
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: call-a-postgres-function-with-arguments
        name: Call a Postgres function with arguments
        code: |
          ```ts
          const { data, error } = await supabase.rpc('echo', { say: '👋' })
          ```
        data:
          sql: |
            ```sql
            create function echo(say text) returns text as $$
              select say;
            $$ language sql;
            ```
        response: |
          ```json
            {
              "data": "👋",
              "status": 200,
              "statusText": "OK"
            }
            ```
        hideCodeBlock: true
      - id: bulk-processing
        name: Bulk processing
        code: |
          ```ts
          const { data, error } = await supabase.rpc('add_one_each', { arr: [1, 2, 3] })
          ```
        data:
          sql: |
            ```sql
            create function add_one_each(arr int[]) returns int[] as $$
              select array_agg(n + 1) from unnest(arr) as n;
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": [
              2,
              3,
              4
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          You can process large payloads by passing in an array as an argument.
        hideCodeBlock: true
      - id: call-a-postgres-function-with-filters
        name: Call a Postgres function with filters
        code: |
          ```ts
          const { data, error } = await supabase
            .rpc('list_stored_countries')
            .eq('id', 1)
            .single()
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Rohan'),
              (2, 'The Shire');

            create function list_stored_countries() returns setof countries as $$
              select * from countries;
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": {
              "id": 1,
              "name": "Rohan"
            },
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres functions that return tables can also be combined with [Filters](/docs/reference/javascript/using-filters) and [Modifiers](/docs/reference/javascript/using-modifiers).
        hideCodeBlock: true
      - id: call-a-read-only-postgres-function
        name: Call a read-only Postgres function
        code: |
          ```ts
          const { data, error } = await supabase.rpc('hello_world', { get: true })
          ```
        data:
          sql: |
            ```sql
            create function hello_world() returns text as $$
              select 'Hello world';
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": "Hello world",
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true

  - id: using-filters
    title: Using Filters
    description: |
      Filters allow you to only return rows that match certain conditions.

      Filters can be used on `select()`, `update()`, `upsert()`, and `delete()` queries.

      If a Postgres function returns a table response, you can also apply filters.
    examples:
      - id: applying-filters
        name: Applying Filters
        description: |
          Filters must be applied after any of `select()`, `update()`, `upsert()`,
          `delete()`, and `rpc()` and before
          [modifiers](/docs/reference/javascript/using-modifiers).
        code: |
          ```ts
          const { data, error } = await supabase
            .from('instruments')
            .select('name, section_id')
            .eq('name', 'violin')    // Correct

          const { data, error } = await supabase
            .from('instruments')
            .eq('name', 'violin')    // Incorrect
            .select('name, section_id')
          ```
      - id: chaining-filters
        name: Chaining
        description: |
          Filters can be chained together to produce advanced queries. For example,
          to query cities with population between 1,000 and 10,000:

          ```ts
          const { data, error } = await supabase
            .from('cities')
            .select('name, country_id')
            .gte('population', 1000)
            .lt('population', 10000)
          ```
        code: |
          ```ts
          const { data, error } = await supabase
            .from('cities')
            .select('name, country_id')
            .gte('population', 1000)
            .lt('population', 10000)
          ```
      - id: conditional-chaining
        name: Conditional Chaining
        description: |
          Filters can be built up one step at a time and then executed. For example:

          ```ts
          const filterByName = null
          const filterPopLow = 1000
          const filterPopHigh = 10000

          let query = supabase
            .from('cities')
            .select('name, country_id')

          if (filterByName)  { query = query.eq('name', filterByName) }
          if (filterPopLow)  { query = query.gte('population', filterPopLow) }
          if (filterPopHigh) { query = query.lt('population', filterPopHigh) }

          const { data, error } = await query
          ```
        code: |
          ```ts
          const filterByName = null
          const filterPopLow = 1000
          const filterPopHigh = 10000

          let query = supabase
            .from('cities')
            .select('name, country_id')

          if (filterByName)  { query = query.eq('name', filterByName) }
          if (filterPopLow)  { query = query.gte('population', filterPopLow) }
          if (filterPopHigh) { query = query.lt('population', filterPopHigh) }

          const { data, error } = await query
          ```
      - id: filter-by-value-within-json-column
        name: Filter by values within a JSON column
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .select()
            .eq('address->postcode', 90210)
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210 }'),
              (2, 'Jane', null);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Michael",
                "address": {
                  "postcode": 90210
                }
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
      - id: filter-referenced-tables
        name: Filter referenced tables
        code: |
          ```ts
          const { data, error } = await supabase
            .from('orchestral_sections')
            .select(`
              name,
              instruments!inner (
                name
              )
            `)
            .eq('instruments.name', 'flute')
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
          response: |
            ```json
            {
              "data": [
                {
                  "name": "woodwinds",
                  "characters": [
                    {
                      "name": "flute"
                    }
                  ]
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        description: |
          You can filter on referenced tables in your `select()` query using dot
          notation.
  ######## TODO CHECK FILTER YAML ERRORS ########
  - id: eq
    title: eq()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.eq'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .eq('name', 'Leia')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Leia"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: neq
    title: neq()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.neq'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .neq('name', 'Leia')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Luke"
              },
              {
                "id": 3,
                "name": "Han"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: gt
    title: gt()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.gt'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .gt('id', 2)
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 3,
                "name": "Han"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
        description: |
          When using [reserved words](https://www.postgresql.org/docs/current/sql-keywords-appendix.html) for column names you need
          to add double quotes e.g. `.gt('"order"', 2)`
  - id: gte
    title: gte()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.gte'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .gte('id', 2)
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Leia"
              },
              {
                "id": 3,
                "name": "Han"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: lt
    title: lt()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.lt'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .lt('id', 2)
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Luke"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: lte
    title: lte()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.lte'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .lte('id', 2)
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Luke"
              },
              {
                "id": 2,
                "name": "Leia"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: like
    title: like()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.like'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .like('name', '%Lu%')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Luke"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: ilike
    title: ilike()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.ilike'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .ilike('name', '%lu%')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Luke"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: is
    title: is()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.is'
    examples:
      - id: checking-nullness
        name: Checking for nullness, true or false
        code: |
          ```ts
          const { data, error } = await supabase
            .from('countries')
            .select()
            .is('name', null)
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'null'),
              (2, null);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "null"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Using the `eq()` filter doesn't work when filtering for `null`.

          Instead, you need to use `is()`.

        hideCodeBlock: true
        isSpotlight: true
  - id: in
    title: in()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.in'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .in('name', ['Leia', 'Han'])
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Leia"
              },
              {
                "id": 3,
                "name": "Han"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: contains
    title: contains()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.contains'
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('issues')
            .select()
            .contains('tags', ['is:open', 'priority:low'])
          ```
        data:
          sql: |
            ```sql
            create table
              issues (
                id int8 primary key,
                title text,
                tags text[]
              );

            insert into
              issues (id, title, tags)
            values
              (1, 'Cache invalidation is not working', array['is:open', 'severity:high', 'priority:low']),
              (2, 'Use better names', array['is:open', 'severity:low', 'priority:medium']);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "title": "Cache invalidation is not working"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .contains('during', '[2000-01-01 13:00, 2000-01-01 13:30)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
      - id: on-jsonb-columns
        name: On `jsonb` columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .select('name')
            .contains('address', { postcode: 90210 })
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210, "street": "Melrose Place" }'),
              (2, 'Jane', '{}');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Michael"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true

  - id: contained-by
    title: containedBy()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.containedBy'
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('classes')
            .select('name')
            .containedBy('days', ['monday', 'tuesday', 'wednesday', 'friday'])
          ```
        data:
          sql: |
            ```sql
            create table
              classes (
                id int8 primary key,
                name text,
                days text[]
              );

            insert into
              classes (id, name, days)
            values
              (1, 'Chemistry', array['monday', 'friday']),
              (2, 'History', array['monday', 'wednesday', 'thursday']);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Chemistry"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .containedBy('during', '[2000-01-01 00:00, 2000-01-01 23:59)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
      - id: on-jsonb-columns
        name: On `jsonb` columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('users')
            .select('name')
            .containedBy('address', {})
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210, "street": "Melrose Place" }'),
              (2, 'Jane', '{}');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "name": "Jane"
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        hideCodeBlock: true

  - id: range-gt
    title: rangeGt()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.rangeGt'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .rangeGt('during', '[2000-01-02 08:00, 2000-01-02 09:00)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "id": 2,
                  "room_name": "Topaz",
                  "during": "[\"2000-01-02 09:00:00\",\"2000-01-02 10:00:00\")"
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-gte
    title: rangeGte()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.rangeGte'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .rangeGte('during', '[2000-01-02 08:30, 2000-01-02 09:30)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "id": 2,
                  "room_name": "Topaz",
                  "during": "[\"2000-01-02 09:00:00\",\"2000-01-02 10:00:00\")"
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-lt
    title: rangeLt()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.rangeLt'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .rangeLt('during', '[2000-01-01 15:00, 2000-01-01 16:00)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true
  - id: range-lte
    title: rangeLte()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.rangeLte'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .rangeLte('during', '[2000-01-01 14:00, 2000-01-01 16:00)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "id": 1,
                  "room_name": "Emerald",
                  "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-adjacent
    title: rangeAdjacent()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.rangeAdjacent'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .rangeAdjacent('during', '[2000-01-01 12:00, 2000-01-01 13:00)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: overlaps
    title: overlaps()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.overlaps'
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('issues')
            .select('title')
            .overlaps('tags', ['is:closed', 'severity:high'])
          ```
        data:
          sql: |
            ```sql
            create table
              issues (
                id int8 primary key,
                title text,
                tags text[]
              );

            insert into
              issues (id, title, tags)
            values
              (1, 'Cache invalidation is not working', array['is:open', 'severity:high', 'priority:low']),
              (2, 'Use better names', array['is:open', 'severity:low', 'priority:medium']);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "title": "Cache invalidation is not working"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```ts
          const { data, error } = await supabase
            .from('reservations')
            .select()
            .overlaps('during', '[2000-01-01 12:45, 2000-01-01 13:15)')
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
  # TODO: schema & result
  - id: text-search
    title: textSearch()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.textSearch'
    notes: |
      - For more information, see [Postgres full text search](/docs/guides/database/full-text-search).
    examples:
      - id: text-search
        name: Text search
        code: |
          ```ts
          const result = await supabase
            .from("texts")
            .select("content")
            .textSearch("content", `'eggs' & 'ham'`, {
              config: "english",
            });
          ```
        data:
          sql: |
            ```sql
            create table texts (
              id      bigint
                      primary key
                      generated always as identity,
              content text
            );

            insert into texts (content) values
                ('Four score and seven years ago'),
                ('The road goes ever on and on'),
                ('Green eggs and ham')
            ;
            ```
        response: |
          ```json
          {
            "data": [
              {
                "content": "Green eggs and ham"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
      - id: basic-normalization
        name: Basic normalization
        description: Uses PostgreSQL's `plainto_tsquery` function.
        code: |
          ```ts
          const { data, error } = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('catchphrase', `'fat' & 'cat'`, {
              type: 'plain',
              config: 'english'
            })
          ```
      - id: full-normalization
        name: Full normalization
        description: Uses PostgreSQL's `phraseto_tsquery` function.
        code: |
          ```ts
          const { data, error } = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('catchphrase', `'fat' & 'cat'`, {
              type: 'phrase',
              config: 'english'
            })
          ```
      - id: web-search
        name: Websearch
        description: |
          Uses PostgreSQL's `websearch_to_tsquery` function.
          This function will never raise syntax errors, which makes it possible to use raw user-supplied input for search, and can be used
          with advanced operators.

          - `unquoted text`: text not inside quote marks will be converted to terms separated by & operators, as if processed by plainto_tsquery.
          - `"quoted text"`: text inside quote marks will be converted to terms separated by `<->` operators, as if processed by phraseto_tsquery.
          - `OR`: the word “or” will be converted to the | operator.
          - `-`: a dash will be converted to the ! operator.

        code: |
          ```ts
          const { data, error } = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('catchphrase', `'fat or cat'`, {
              type: 'websearch',
              config: 'english'
            })
          ```

  - id: match
    title: match()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.match'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select('name')
            .match({ id: 2, name: 'Leia' })
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Leia"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: not
    title: not()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.not'
    notes: |
      not() expects you to use the raw PostgREST syntax for the filter values.

      ```ts
      .not('id', 'in', '(5,6,7)')  // Use `()` for `in` filter
      .not('arraycol', 'cs', '{"a","b"}')  // Use `cs` for `contains()`, `{}` for array values
      ```
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('countries')
            .select()
            .not('name', 'is', null)
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'null'),
              (2, null);
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "id": 1,
                  "name": "null"
                }
              ],
              "status": 200,
              "statusText": "OK"
            }
            ```
        hideCodeBlock: true
        isSpotlight: true
  - id: or
    title: or()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.or'
    notes: |
      or() expects you to use the raw PostgREST syntax for the filter names and values.

      ```ts
      .or('id.in.(5,6,7), arraycol.cs.{"a","b"}')  // Use `()` for `in` filter, `{}` for array values and `cs` for `contains()`.
      .or('id.in.(5,6,7), arraycol.cd.{"a","b"}')  // Use `cd` for `containedBy()`
      ```
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select('name')
            .or('id.eq.2,name.eq.Han')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Leia"
              },
              {
                "name": "Han"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: use-or-with-and
        name: Use `or` with `and`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select('name')
            .or('id.gt.3,and(id.eq.1,name.eq.Luke)')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        reponse: |
          ```json
          {
            "data": [
              {
                "name": "Luke"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
      - id: use-or-on-referenced-tables
        name: Use `or` on referenced tables
        code: |
          ```ts
          const { data, error } = await supabase
            .from('orchestral_sections')
            .select(`
              name,
              instruments!inner (
                name
              )
            `)
            .or('section_id.eq.1,name.eq.guzheng', { referencedTable: 'instruments' })
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  }
                ]
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true

  - id: filter
    title: filter()
    $ref: '@supabase/postgrest-js.PostgrestFilterBuilder.filter'
    notes: |
      filter() expects you to use the raw PostgREST syntax for the filter values.

      ```ts
      .filter('id', 'in', '(5,6,7)')  // Use `()` for `in` filter
      .filter('arraycol', 'cs', '{"a","b"}')  // Use `cs` for `contains()`, `{}` for array values
      ```
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .filter('name', 'in', '("Han","Yoda")')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 3,
                "name": "Han"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-referenced-table
        name: On a referenced table
        code: |
          ```ts
          const { data, error } = await supabase
            .from('orchestral_sections')
            .select(`
              name,
              instruments!inner (
                name
              )
            `)
            .filter('instruments.name', 'eq', 'flute')
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
               instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "woodwinds",
                "instruments":
                  {
                    "name": "flute"
                  }
                ]
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
  - id: using-modifiers
    title: Using Modifiers
    description: |
      Filters work on the row level—they allow you to return rows that
      only match certain conditions without changing the shape of the rows.
      Modifiers are everything that don't fit that definition—allowing you to
      change the format of the response (e.g., returning a CSV string).

      Modifiers must be specified after filters. Some modifiers only apply for
      queries that return rows (e.g., `select()` or `rpc()` on a function that
      returns a table response).

  - id: db-modifiers-select
    title: select()
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.select'
    examples:
      - id: with-upsert
        name: With `upsert()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .upsert({ id: 1, name: 'Han Solo' })
            .select()
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Han Solo"
              }
            ],
            "status": 201,
            "statusText": "Created"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: order
    title: order()
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.order'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select('id, name')
            .order('id', { ascending: false })
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 3,
                "name": "Han"
              },
              {
                "id": 2,
                "name": "Leia"
              },
              {
                "id": 1,
                "name": "Luke"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-referenced-table
        name: On a referenced table
        code: |
          ```ts
            const { data, error } = await supabase
              .from('orchestral_sections')
              .select(`
                name,
                instruments (
                  name
                )
              `)
              .order('name', { referencedTable: 'instruments', ascending: false })
            ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'harp'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  },
                  {
                    "name": "harp"
                  }
                ]
              },
              {
                "name": "woodwinds",
                "characters": []
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Ordering with `referencedTable` doesn't affect the ordering of the
          parent table.
        hideCodeBlock: true
      - id: order-parent-table-by-a-referenced-table
        name: Order parent table by a referenced table
        code: |
          ```ts
            const { data, error } = await supabase
              .from('instruments')
              .select(`
                name,
                section:orchestral_sections (
                  name
                )
              `)
              .order('section(name)', { ascending: true })
            ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "violin",
                "orchestral_sections": {"name": "strings"}
              },
              {
                "name": "flute",
                "orchestral_sections": {"name": "woodwinds"}
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Ordering with `referenced_table(col)` affects the ordering of the
          parent table.
        hideCodeBlock: true

  - id: limit
    title: limit()
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.limit'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select('name')
            .limit(1)
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Luke"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-referenced-table
        name: On a referenced table
        code: |
          ```ts
          const { data, error } = await supabase
            .from('orchestral_sections')
            .select(`
              name,
              instruments (
                name
              )
            `)
            .limit(1, { referencedTable: 'instruments' })
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'harp'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  }
                ]
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
  - id: range
    title: range()
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.range'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('countries')
            .select('name')
            .range(0, 1)
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Luke"
              },
              {
                "name": "Leia"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: abort-signal
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.abortSignal'
    title: abortSignal()
    notes: |
      You can use this to set a timeout for the request.
    examples:
      - id: aborting-requests-in-flight
        name: Aborting requests in-flight
        code: |
          ```ts
          const ac = new AbortController()
          ac.abort()
          const { data, error } = await supabase
            .from('very_big_table')
            .select()
            .abortSignal(ac.signal)
          ```
        response: |
          ```json
            {
              "error": {
                "message": "FetchError: The user aborted a request.",
                "details": "",
                "hint": "",
                "code": ""
              },
              "status": 400,
              "statusText": "Bad Request"
            }
            ```
        description: |
          You can use an [`AbortController`](https://developer.mozilla.org/en-US/docs/Web/API/AbortController) to abort requests.
          Note that `status` and `statusText` don't mean anything for aborted requests as the request wasn't fulfilled.
        hideCodeBlock: true
        isSpotlight: true
      - id: set-a-timeout
        name: Set a timeout
        code: |
          ```ts
          const { data, error } = await supabase
            .from('very_big_table')
            .select()
            .abortSignal(AbortSignal.timeout(1000 /* ms */))
          ```
        response: |
          ```json
            {
              "error": {
                "message": "FetchError: The user aborted a request.",
                "details": "",
                "hint": "",
                "code": ""
              },
              "status": 400,
              "statusText": "Bad Request"
            }
            ```
        hideCodeBlock: true
  - id: single
    title: single()
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.single'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select('name')
            .limit(1)
            .single()
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": {
              "name": "Luke"
            },
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: maybe-single
    title: maybeSingle()
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.maybeSingle'
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .eq('name', 'Katniss')
            .maybeSingle()
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "status": 200,
            "statusText": "OK"
          }
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: csv
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.csv'
    title: csv()
    examples:
      - id: return-data-as-csv
        name: Return data as CSV
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .csv()
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```json
          {
            "data": "id,name\n1,Luke\n2,Leia\n3,Han",
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          By default, the data is returned in JSON format, but can also be returned as Comma Separated Values.
        hideCodeBlock: true
        isSpotlight: true

  - id: returns
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.returns'
    notes: |
      - Deprecated: use overrideTypes method instead
    title: returns()
    examples:
      - id: override-type-of-successful-response
        name: Override type of successful response
        code: |
          ```ts
          const { data } = await supabase
            .from('countries')
            .select()
            .returns<Array<MyType>>()
          ```
        response: |
          ```
          let x: typeof data // MyType[]
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: override-type-of-object-response
        name: Override type of object response
        code: |
          ```ts
          const { data } = await supabase
            .from('countries')
            .select()
            .maybeSingle()
            .returns<MyType>()
          ```
        response: |
          ```
          let x: typeof data // MyType | null
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: overrideTypes
    $ref: '@supabase/postgrest-js.PostgrestBuilder.overrideTypes'
    title: overrideTypes()
    examples:
      - id: complete-override-type-of-successful-response
        name: Complete Override type of successful response
        code: |
          ```ts
          const { data } = await supabase
            .from('countries')
            .select()
            .overrideTypes<Array<MyType>, { merge: false }>()
          ```
        response: |
          ```ts
          let x: typeof data // MyType[]
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: complete-override-type-of-object-response
        name: Complete Override type of object response
        code: |
          ```ts
          const { data } = await supabase
            .from('countries')
            .select()
            .maybeSingle()
            .overrideTypes<MyType, { merge: false }>()
          ```
        response: |
          ```ts
          let x: typeof data // MyType | null
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: partial-override-type-of-successful-response
        name: Partial Override type of successful response
        code: |
          ```ts
          const { data } = await supabase
            .from('countries')
            .select()
            .overrideTypes<Array<{ status: "A" | "B" }>>()
          ```
        response: |
          ```ts
          let x: typeof data // Array<CountryRowProperties & { status: "A" | "B" }>
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: partial-override-type-of-object-response
        name: Partial Override type of object response
        code: |
          ```ts
          const { data } = await supabase
            .from('countries')
            .select()
            .maybeSingle()
            .overrideTypes<{ status: "A" | "B" }>()
          ```
        response: |
          ```ts
          let x: typeof data // CountryRowProperties & { status: "A" | "B" } | null
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: explain
    $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.explain'
    title: Using Explain
    description: |
      For debugging slow queries, you can get the [Postgres `EXPLAIN` execution plan](https://www.postgresql.org/docs/current/sql-explain.html) of a query
      using the `explain()` method. This works on any query, even for `rpc()` or writes.

      Explain is not enabled by default as it can reveal sensitive information about your database.
      It's best to only enable this for testing environments but if you wish to enable it for production you can provide additional protection by using a `pre-request` function.

      Follow the [Performance Debugging Guide](/docs/guides/database/debugging-performance) to enable the functionality on your project.
    examples:
      - id: get-execution-plan
        name: Get the execution plan
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .explain()
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```
          Aggregate  (cost=33.34..33.36 rows=1 width=112)
            ->  Limit  (cost=0.00..18.33 rows=1000 width=40)
                  ->  Seq Scan on characters  (cost=0.00..22.00 rows=1200 width=40)
          ```
        description: |
          By default, the data is returned in TEXT format, but can also be returned as JSON by using the `format` parameter.
        hideCodeBlock: true
        isSpotlight: true

      - id: get-execution-plan-with-analyze-and-verbose
        name: Get the execution plan with analyze and verbose
        code: |
          ```ts
          const { data, error } = await supabase
            .from('characters')
            .select()
            .explain({analyze:true,verbose:true})
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Luke'),
              (2, 'Leia'),
              (3, 'Han');
            ```
        response: |
          ```
          Aggregate  (cost=33.34..33.36 rows=1 width=112) (actual time=0.041..0.041 rows=1 loops=1)
            Output: NULL::bigint, count(ROW(characters.id, characters.name)), COALESCE(json_agg(ROW(characters.id, characters.name)), '[]'::json), NULLIF(current_setting('response.headers'::text, true), ''::text), NULLIF(current_setting('response.status'::text, true), ''::text)
            ->  Limit  (cost=0.00..18.33 rows=1000 width=40) (actual time=0.005..0.006 rows=3 loops=1)
                  Output: characters.id, characters.name
                  ->  Seq Scan on public.characters  (cost=0.00..22.00 rows=1200 width=40) (actual time=0.004..0.005 rows=3 loops=1)
                        Output: characters.id, characters.name
          Query Identifier: -4730654291623321173
          Planning Time: 0.407 ms
          Execution Time: 0.119 ms
          ```
        description: |
          By default, the data is returned in TEXT format, but can also be returned as JSON by using the `format` parameter.
        hideCodeBlock: true
        isSpotlight: false

  # NOTE: Not available on currently deployed PostgREST
  # db.geojson():
  #   $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.geojson'
  #   title: geojson()

  # NOTE: Not available on currently deployed PostgREST
  # db.explain():
  #   $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.explain'
  #   title: explain()

  # NOTE: Not available on currently deployed PostgREST
  # db.rollback():
  #   $ref: '@supabase/postgrest-js.PostgrestTransformBuilder.rollback'
  #   title: rollback()
  #   examples:
  #     - id: With `delete()`
  #       description: |
  #         <details>
  #           <summary>Schema</summary>

  #           ```sql
  #           create table
  #             countries (id int8 primary key, name text);

  #           insert into
  #             countries (id, name)
  #           values
  #             (1, 'Mordor');
  #           ```
  #         </details>

  #         ```ts
  #         const { error } = await supabase
  #           .from('countries')
  #           .delete()
  #           .eq('id', 1)
  #           .rollback()
  #         ```

  #         <details>
  #           <summary>Result</summary>

  #           ```json
  #           {
  #             "status": 204,
  #             "statusText": "No Content"
  #           }
  #           ```
  #         </details>

  #         ```ts
  #         const { data, error } = await supabase
  #           .from('countries')
  #           .select()
  #         ```

  #         <details>
  #           <summary>Result</summary>

  #           ```json
  #           {
  #             "data": [
  #               {
  #                 "id": 1,
  #                 "name": "Mordor"
  #               }
  #             ],
  #             "status": 200,
  #             "statusText": "OK"
  #           }
  #           ```
  #         </details>
  #       hideCodeBlock: true
  #       isSpotlight: true
  #       js: |
  #         ```ts
  #         const { error } = await supabase
  #           .from('countries')
  #           .delete()
  #           .eq('id', 1)
  #         ```

  - id: invoke
    title: invoke()
    description: |
      Invoke a Supabase Edge Function.
    $ref: '@supabase/functions-js.FunctionsClient.invoke'
    notes: |
      - Requires an Authorization header.
      - Invoke params generally match the [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) spec.
      - When you pass in a body to your function, we automatically attach the Content-Type header for `Blob`, `ArrayBuffer`, `File`, `FormData` and `String`. If it doesn't match any of these types we assume the payload is `json`, serialize it and attach the `Content-Type` header as `application/json`. You can override this behavior by passing in a `Content-Type` header of your own.
      - Responses are automatically parsed as `json`, `blob` and `form-data` depending on the `Content-Type` header sent by your function. Responses are parsed as `text` by default.
    examples:
      - id: basic-invocation
        name: Basic invocation
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.functions.invoke('hello', {
            body: { foo: 'bar' }
          })
          ```
      - id: error-handling
        name: Error handling
        description: |
          A `FunctionsHttpError` error is returned if your function throws an error, `FunctionsRelayError` if the Supabase Relay has an error processing your function and `FunctionsFetchError` if there is a network error in calling your function.
        isSpotlight: true
        code: |
          ```js
          import { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from "@supabase/supabase-js";

          const { data, error } = await supabase.functions.invoke('hello', {
            headers: {
              "my-custom-header": 'my-custom-header-value'
            },
            body: { foo: 'bar' }
          })

          if (error instanceof FunctionsHttpError) {
            const errorMessage = await error.context.json()
            console.log('Function returned an error', errorMessage)
          } else if (error instanceof FunctionsRelayError) {
            console.log('Relay error:', error.message)
          } else if (error instanceof FunctionsFetchError) {
            console.log('Fetch error:', error.message)
          }
          ```
      - id: passing-custom-headers
        name: Passing custom headers
        description: |
          You can pass custom headers to your function. Note: supabase-js automatically passes the `Authorization` header with the signed in user's JWT.
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.functions.invoke('hello', {
            headers: {
              "my-custom-header": 'my-custom-header-value'
            },
            body: { foo: 'bar' }
          })
          ```
      - id: calling-with-delete-verb
        name: Calling with DELETE HTTP verb
        description: |
          You can also set the HTTP verb to `DELETE` when calling your Edge Function.
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.functions.invoke('hello', {
            headers: {
              "my-custom-header": 'my-custom-header-value'
            },
            body: { foo: 'bar' },
            method: 'DELETE'
          })
          ```
      - id: regional-invocation
        name: Invoking a Function in the UsEast1 region
        description: |
          Here are the available regions:
          - `FunctionRegion.Any`
          - `FunctionRegion.ApNortheast1`
          - `FunctionRegion.ApNortheast2`
          - `FunctionRegion.ApSouth1`
          - `FunctionRegion.ApSoutheast1`
          - `FunctionRegion.ApSoutheast2`
          - `FunctionRegion.CaCentral1`
          - `FunctionRegion.EuCentral1`
          - `FunctionRegion.EuWest1`
          - `FunctionRegion.EuWest2`
          - `FunctionRegion.EuWest3`
          - `FunctionRegion.SaEast1`
          - `FunctionRegion.UsEast1`
          - `FunctionRegion.UsWest1`
          - `FunctionRegion.UsWest2`
        isSpotlight: true
        code: |
          ```js
          import { createClient, FunctionRegion } from '@supabase/supabase-js'

          const { data, error } = await supabase.functions.invoke('hello', {
            body: { foo: 'bar' },
            region: FunctionRegion.UsEast1
          })
          ```

      - id: calling-with-get-verb
        name: Calling with GET HTTP verb
        description: |
          You can also set the HTTP verb to `GET` when calling your Edge Function.
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase.functions.invoke('hello', {
            headers: {
              "my-custom-header": 'my-custom-header-value'
            },
            method: 'GET'
          })
          ```

  - id: subscribe
    title: on().subscribe()
    $ref: '@supabase/realtime-js.RealtimeChannel.on'
    notes: |
      - By default, Broadcast and Presence are enabled for all projects.
      - By default, listening to database changes is disabled for new projects due to database performance and security concerns. You can turn it on by managing Realtime's [replication](/docs/guides/api#realtime-api-overview).
      - You can receive the "previous" data for updates and deletes by setting the table's `REPLICA IDENTITY` to `FULL` (e.g., `ALTER TABLE your_table REPLICA IDENTITY FULL;`).
      - Row level security is not applied to delete statements. When RLS is enabled and replica identity is set to full, only the primary key is sent to clients.
    examples:
      - id: listen-to-broadcast
        name: Listen to broadcast messages
        isSpotlight: true
        code: |
          ```js
          const channel = supabase.channel("room1")

          channel.on("broadcast", { event: "cursor-pos" }, (payload) => {
            console.log("Cursor position received!", payload);
          }).subscribe((status) => {
            if (status === "SUBSCRIBED") {
              channel.send({
                type: "broadcast",
                event: "cursor-pos",
                payload: { x: Math.random(), y: Math.random() },
              });
            }
          });
          ```
      - id: listen-to-presence-sync
        name: Listen to presence sync
        isSpotlight: true
        code: |
          ```js
          const channel = supabase.channel('room1')
          channel
            .on('presence', { event: 'sync' }, () => {
              console.log('Synced presence state: ', channel.presenceState())
            })
            .subscribe(async (status) => {
              if (status === 'SUBSCRIBED') {
                await channel.track({ online_at: new Date().toISOString() })
              }
            })
          ```
      - id: listen-to-presence-join
        name: Listen to presence join
        isSpotlight: true
        code: |
          ```js
          const channel = supabase.channel('room1')
          channel
            .on('presence', { event: 'join' }, ({ newPresences }) => {
              console.log('Newly joined presences: ', newPresences)
            })
            .subscribe(async (status) => {
              if (status === 'SUBSCRIBED') {
                await channel.track({ online_at: new Date().toISOString() })
              }
            })
          ```
      - id: listen-to-presence-leave
        name: Listen to presence leave
        isSpotlight: true
        code: |
          ```js
          const channel = supabase.channel('room1')
          channel
            .on('presence', { event: 'leave' }, ({ leftPresences }) => {
              console.log('Newly left presences: ', leftPresences)
            })
            .subscribe(async (status) => {
              if (status === 'SUBSCRIBED') {
                await channel.track({ online_at: new Date().toISOString() })
                await channel.untrack()
              }
            })
          ```
      - id: listen-to-all-database-changes
        name: Listen to all database changes
        isSpotlight: true
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: '*', schema: '*' }, payload => {
              console.log('Change received!', payload)
            })
            .subscribe()
          ```
      - id: listen-to-a-specific-table
        name: Listen to a specific table
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: '*', schema: 'public', table: 'countries' }, payload => {
              console.log('Change received!', payload)
            })
            .subscribe()
          ```
      - id: listen-to-inserts
        name: Listen to inserts
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'countries' }, payload => {
              console.log('Change received!', payload)
            })
            .subscribe()
          ```
      - id: listen-to-updates
        name: Listen to updates
        description: |
          By default, Supabase will send only the updated record. If you want to receive the previous values as well you can
          enable full replication for the table you are listening to:

          ```sql
          alter table "your_table" replica identity full;
          ```
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'countries' }, payload => {
              console.log('Change received!', payload)
            })
            .subscribe()
          ```
      - id: listen-to-deletes
        name: Listen to deletes
        description: |
          By default, Supabase does not send deleted records. If you want to receive the deleted record you can
          enable full replication for the table you are listening too:

          ```sql
          alter table "your_table" replica identity full;
          ```
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: 'DELETE', schema: 'public', table: 'countries' }, payload => {
              console.log('Change received!', payload)
            })
            .subscribe()
          ```
      - id: listen-to-multiple-events
        name: Listen to multiple events
        description: You can chain listeners if you want to listen to multiple events for each table.
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'countries' }, handleRecordInserted)
            .on('postgres_changes', { event: 'DELETE', schema: 'public', table: 'countries' }, handleRecordDeleted)
            .subscribe()
          ```
      - id: listening-to-row-level-changes
        name: Listen to row level changes
        description: You can listen to individual rows using the format `{table}:{col}=eq.{val}` - where `{col}` is the column name, and `{val}` is the value which you want to match.
        notes: |
          - ``eq`` filter works with all database types as under the hood, it's casting both the filter value and the database value to the correct type and then comparing them.
        code: |
          ```js
          supabase
            .channel('room1')
            .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'countries', filter: 'id=eq.200' }, handleRecordUpdated)
            .subscribe()
          ```
  - id: broadcast-message
    title: broadcastMessage()
    description: |
      Broadcast a message to all connected clients to a channel.
    notes: |
      - When using REST you don't need to subscribe to the channel
      - REST calls are only available from 2.37.0 onwards
    $ref: '@supabase/realtime-js.RealtimeChannel.send'
    examples:
      - id: send-a-message
        name: Send a message via websocket
        isSpotlight: true
        code: |
          ```js
          supabase
            .channel('room1')
            .subscribe((status) => {
              if (status === 'SUBSCRIBED') {
                channel.send({
                  type: 'broadcast',
                  event: 'cursor-pos',
                  payload: { x: Math.random(), y: Math.random() },
                })
              }
            })
          ```
        response: |
          ```
          ok | timed out | error
          ```
      - id: send-a-message-via-rest
        name: Send a message via REST
        isSpotlight: true
        code: |
          ```js
          supabase
            .channel('room1')
            .send({
              type: 'broadcast',
              event: 'cursor-pos',
              payload: { x: Math.random(), y: Math.random()
              },
            })
          ```
  - id: get-channels
    title: getChannels()
    $ref: '@supabase/supabase-js.index.SupabaseClient.getChannels'
    examples:
      - id: get-all-channels
        name: Get all channels
        isSpotlight: true
        code: |
          ```js
          const channels = supabase.getChannels()
          ```

  - id: remove-channel
    title: removeChannel()
    $ref: '@supabase/supabase-js.index.SupabaseClient.removeChannel'
    notes: |
      - Removing a channel is a great way to maintain the performance of your project's Realtime service as well as your database if you're listening to Postgres changes. Supabase will automatically handle cleanup 30 seconds after a client is disconnected, but unused channels may cause degradation as more clients are simultaneously subscribed.
    examples:
      - id: removes-a-channel
        name: Removes a channel
        isSpotlight: true
        code: |
          ```js
          supabase.removeChannel(myChannel)
          ```

  - id: remove-all-channels
    title: removeAllChannels()
    $ref: '@supabase/supabase-js.index.SupabaseClient.removeAllChannels'
    notes: |
      - Removing channels is a great way to maintain the performance of your project's Realtime service as well as your database if you're listening to Postgres changes. Supabase will automatically handle cleanup 30 seconds after a client is disconnected, but unused channels may cause degradation as more clients are simultaneously subscribed.
    examples:
      - id: remove-all-channels
        name: Remove all channels
        isSpotlight: true
        code: |
          ```js
          supabase.removeAllChannels()
          ```

  - id: list-buckets
    title: listBuckets()
    $ref: '@supabase/storage-js.packages/StorageBucketApi.default.listBuckets'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: list-buckets
        name: List buckets
        isSpotlight: true
        code: |
          ```ts
          const { data, error } = await supabase
            .storage
            .listBuckets()
          ```
        response: |
          ```json
          {
            "data": [
              {
                "id": "avatars",
                "name": "avatars",
                "owner": "",
                "public": false,
                "file_size_limit": 1024,
                "allowed_mime_types": [
                  "image/png"
                ],
                "created_at": "2024-05-22T22:26:05.100Z",
                "updated_at": "2024-05-22T22:26:05.100Z"
              }
            ],
            "error": null
          }
          ```

  - id: get-bucket
    title: getBucket()
    $ref: '@supabase/storage-js.packages/StorageBucketApi.default.getBucket'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: get-bucket
        name: Get bucket
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .getBucket('avatars')
          ```
        response: |
          ```json
          {
            "data": {
              "id": "avatars",
              "name": "avatars",
              "owner": "",
              "public": false,
              "file_size_limit": 1024,
              "allowed_mime_types": [
                "image/png"
              ],
              "created_at": "2024-05-22T22:26:05.100Z",
              "updated_at": "2024-05-22T22:26:05.100Z"
            },
            "error": null
          }
          ```

  - id: create-bucket
    title: createBucket()
    $ref: '@supabase/storage-js.packages/StorageBucketApi.default.createBucket'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `insert`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: create-bucket
        name: Create bucket
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .createBucket('avatars', {
              public: false,
              allowedMimeTypes: ['image/png'],
              fileSizeLimit: 1024
            })
          ```
        response: |
          ```json
          {
            "data": {
              "name": "avatars"
            },
            "error": null
          }
          ```

  - id: empty-bucket
    title: emptyBucket()
    $ref: '@supabase/storage-js.packages/StorageBucketApi.default.emptyBucket'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select`
        - `objects` table permissions: `select` and `delete`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: empty-bucket
        name: Empty bucket
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .emptyBucket('avatars')
          ```
        response: |
          ```json
          {
            "data": {
              "message": "Successfully emptied"
            },
            "error": null
          }
          ```
  - id: update-bucket
    title: updateBucket()
    $ref: '@supabase/storage-js.packages/StorageBucketApi.default.updateBucket'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select` and `update`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: update-bucket
        name: Update bucket
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .updateBucket('avatars', {
              public: false,
              allowedMimeTypes: ['image/png'],
              fileSizeLimit: 1024
            })
          ```
        response: |
          ```json
          {
            "data": {
              "message": "Successfully updated"
            },
            "error": null
          }
          ```

  - id: delete-bucket
    title: deleteBucket()
    $ref: '@supabase/storage-js.packages/StorageBucketApi.default.deleteBucket'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select` and `delete`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: delete-bucket
        name: Delete bucket
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .deleteBucket('avatars')
          ```
        response: |
          ```json
          {
            "data": {
              "message": "Successfully deleted"
            },
            "error": null
          }
          ```

  - id: from-upload
    title: from.upload()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.upload'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: only `insert` when you are uploading new files and `select`, `insert` and `update` when you are upserting files
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
      - For React Native, using either `Blob`, `File` or `FormData` does not work as intended. Upload file using `ArrayBuffer` from base64 file data instead, see example below.
    examples:
      - id: upload-file
        name: Upload file
        isSpotlight: true
        code: |
          ```js
          const avatarFile = event.target.files[0]
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .upload('public/avatar1.png', avatarFile, {
              cacheControl: '3600',
              upsert: false
            })
          ```
        response: |
          ```json
          {
            "data": {
              "path": "public/avatar1.png",
              "fullPath": "avatars/public/avatar1.png"
            },
            "error": null
          }
      - id: upload-file-using-arraybuffer-from-base64-file-data
        name: Upload file using `ArrayBuffer` from base64 file data
        code: |
          ```js
          import { decode } from 'base64-arraybuffer'

          const { data, error } = await supabase
            .storage
            .from('avatars')
            .upload('public/avatar1.png', decode('base64FileData'), {
              contentType: 'image/png'
            })
          ```

  - id: from-update
    title: from.update()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.update'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `update` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
      - For React Native, using either `Blob`, `File` or `FormData` does not work as intended. Update file using `ArrayBuffer` from base64 file data instead, see example below.
    examples:
      - id: update-file
        name: Update file
        isSpotlight: true
        code: |
          ```js
          const avatarFile = event.target.files[0]
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .update('public/avatar1.png', avatarFile, {
              cacheControl: '3600',
              upsert: true
            })
          ```
        response: |
          ```json
          {
            "data": {
              "path": "public/avatar1.png",
              "fullPath": "avatars/public/avatar1.png"
            },
            "error": null
          }
          ```
      - id: update-file-using-arraybuffer-from-base64-file-data
        name: Update file using `ArrayBuffer` from base64 file data
        code: |
          ```js
          import {decode} from 'base64-arraybuffer'

          const { data, error } = await supabase
            .storage
            .from('avatars')
            .update('public/avatar1.png', decode('base64FileData'), {
              contentType: 'image/png'
            })
          ```

  - id: from-move
    title: from.move()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.move'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `update` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: move-file
        name: Move file
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .move('public/avatar1.png', 'private/avatar2.png')
          ```
        response: |
          ```json
          {
            "data": {
              "message": "Successfully moved"
            },
            "error": null
          }
          ```

  - id: from-copy
    title: from.copy()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.copy'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `insert` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: copy-file
        name: Copy file
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .copy('public/avatar1.png', 'private/avatar2.png')
          ```
        response: |
          ```json
          {
            "data": {
              "path": "avatars/private/avatar2.png"
            },
            "error": null
          }
          ```

  - id: from-create-signed-url
    title: from.createSignedUrl()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.createSignedUrl'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: create-signed-url
        name: Create Signed URL
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .createSignedUrl('folder/avatar1.png', 60)
          ```
        response: |
          ```json
          {
            "data": {
              "signedUrl": "https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar1.png?token=<TOKEN>"
            },
            "error": null
          }
          ```
      - id: create-signed-url-with-transformations
        name: Create a signed URL for an asset with transformations
        isSpotlight: true
        code: |
          ```js
          const { data } = await supabase
            .storage
            .from('avatars')
            .createSignedUrl('folder/avatar1.png', 60, {
              transform: {
                width: 100,
                height: 100,
              }
            })
          ```
      - id: create-signed-url-with-download
        name: Create a signed URL which triggers the download of the asset
        isSpotlight: true
        code: |
          ```js
          const { data } = await supabase
            .storage
            .from('avatars')
            .createSignedUrl('folder/avatar1.png', 60, {
              download: true,
            })
          ```

  - id: from-create-signed-urls
    title: from.createSignedUrls()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.createSignedUrls'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: create-signed-urls
        name: Create Signed URLs
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .createSignedUrls(['folder/avatar1.png', 'folder/avatar2.png'], 60)
          ```
        response: |
          ```json
          {
            "data": [
              {
                "error": null,
                "path": "folder/avatar1.png",
                "signedURL": "/object/sign/avatars/folder/avatar1.png?token=<TOKEN>",
                "signedUrl": "https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar1.png?token=<TOKEN>"
              },
              {
                "error": null,
                "path": "folder/avatar2.png",
                "signedURL": "/object/sign/avatars/folder/avatar2.png?token=<TOKEN>",
                "signedUrl": "https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar2.png?token=<TOKEN>"
              }
            ],
            "error": null
          }
          ```

  - id: from-create-signed-upload-url
    title: from.createSignedUploadUrl()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.createSignedUploadUrl'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `insert`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: create-signed-upload-url
        name: Create Signed Upload URL
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .createSignedUploadUrl('folder/cat.jpg')
          ```
        response: |
          ```json
          {
            "data": {
              "signedUrl": "https://example.supabase.co/storage/v1/object/upload/sign/avatars/folder/cat.jpg?token=<TOKEN>",
              "path": "folder/cat.jpg",
              "token": "<TOKEN>"
            },
            "error": null
          }
          ```

  - id: from-upload-to-signed-url
    title: from.uploadToSignedUrl()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.uploadToSignedUrl'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: upload-to-signed-url
        name: Upload to a signed URL
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .uploadToSignedUrl('folder/cat.jpg', 'token-from-createSignedUploadUrl', file)
          ```
        response: |
          ```json
          {
            "data": {
              "path": "folder/cat.jpg",
              "fullPath": "avatars/folder/cat.jpg"
            },
            "error": null
          }
          ```

  - id: from-get-public-url
    title: from.getPublicUrl()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.getPublicUrl'
    notes: |
      - The bucket needs to be set to public, either via [updateBucket()](/docs/reference/javascript/storage-updatebucket) or by going to Storage on [supabase.com/dashboard](https://supabase.com/dashboard), clicking the overflow menu on a bucket and choosing "Make public"
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: returns-the-url-for-an-asset-in-a-public-bucket
        name: Returns the URL for an asset in a public bucket
        isSpotlight: true
        code: |
          ```js
          const { data } = supabase
            .storage
            .from('public-bucket')
            .getPublicUrl('folder/avatar1.png')
          ```
        response: |
          ```json
          {
            "data": {
              "publicUrl": "https://example.supabase.co/storage/v1/object/public/public-bucket/folder/avatar1.png"
            }
          }
          ```
      - id: transform-asset-in-public-bucket
        name: Returns the URL for an asset in a public bucket with transformations
        isSpotlight: true
        code: |
          ```js
          const { data } = supabase
            .storage
            .from('public-bucket')
            .getPublicUrl('folder/avatar1.png', {
              transform: {
                width: 100,
                height: 100,
              }
            })
          ```
      - id: download-asset-in-public-bucket
        name: Returns the URL which triggers the download of an asset in a public bucket
        isSpotlight: true
        code: |
          ```js
          const { data } = supabase
            .storage
            .from('public-bucket')
            .getPublicUrl('folder/avatar1.png', {
              download: true,
            })
          ```

  - id: from-download
    title: from.download()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.download'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: download-file
        name: Download file
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .download('folder/avatar1.png')
          ```
        response: |
          ```json
          {
            "data": <BLOB>,
            "error": null
          }
          ```
      - id: download-file-with-transformations
        name: Download file with transformations
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .download('folder/avatar1.png', {
              transform: {
                width: 100,
                height: 100,
                quality: 80
              }
            })
          ```

  - id: from-remove
    title: from.remove()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.remove'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `delete` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: delete-file
        name: Delete file
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .remove(['folder/avatar1.png'])
          ```
        response: |
          ```json
          {
            "data": [],
            "error": null
          }
          ```

  - id: from-list
    title: from.list()
    $ref: '@supabase/storage-js.packages/StorageFileApi.default.list'
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: list-files-in-a-bucket
        name: List files in a bucket
        isSpotlight: true
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .list('folder', {
              limit: 100,
              offset: 0,
              sortBy: { column: 'name', order: 'asc' },
            })
          ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "avatar1.png",
                "id": "e668cf7f-821b-4a2f-9dce-7dfa5dd1cfd2",
                "updated_at": "2024-05-22T23:06:05.580Z",
                "created_at": "2024-05-22T23:04:34.443Z",
                "last_accessed_at": "2024-05-22T23:04:34.443Z",
                "metadata": {
                  "eTag": "\"c5e8c553235d9af30ef4f6e280790b92\"",
                  "size": 32175,
                  "mimetype": "image/png",
                  "cacheControl": "max-age=3600",
                  "lastModified": "2024-05-22T23:06:05.574Z",
                  "contentLength": 32175,
                  "httpStatusCode": 200
                }
              }
            ],
            "error": null
          }
          ```
      - id: search-files-in-a-bucket
        name: Search files in a bucket
        code: |
          ```js
          const { data, error } = await supabase
            .storage
            .from('avatars')
            .list('folder', {
              limit: 100,
              offset: 0,
              sortBy: { column: 'name', order: 'asc' },
              search: 'jon'
            })
          ```
