[{"id": "introduction", "title": "Introduction", "slug": "introduction", "summary": "Introduction", "type": "markdown"}, {"id": "usage", "type": "category", "title": "Usage", "items": [{"id": "create-a-bucket", "title": "Create a bucket", "slug": "create-a-bucket", "summary": "Create a bucket", "type": "self-hosted-operation"}, {"id": "gets-all-buckets", "title": "Gets all buckets", "slug": "gets-all-buckets", "summary": "Gets all buckets", "type": "self-hosted-operation"}, {"id": "empty-a-bucket", "title": "Empty a bucket", "slug": "empty-a-bucket", "summary": "Empty a bucket", "type": "self-hosted-operation"}, {"id": "get-details-of-a-bucket", "title": "Get details of a bucket", "slug": "get-details-of-a-bucket", "summary": "Get details of a bucket", "type": "self-hosted-operation"}, {"id": "update-properties-of-a-bucket", "title": "Update properties of a bucket", "slug": "update-properties-of-a-bucket", "summary": "Update properties of a bucket", "type": "self-hosted-operation"}, {"id": "delete-a-bucket", "title": "Delete a bucket", "slug": "delete-a-bucket", "summary": "Delete a bucket", "type": "self-hosted-operation"}, {"id": "delete-an-object", "title": "Delete an object", "slug": "delete-an-object", "summary": "Delete an object", "type": "self-hosted-operation"}, {"id": "get-object", "title": "Get object", "slug": "get-object", "summary": "Get object", "type": "self-hosted-operation"}, {"id": "update-the-object-at-an-existing-key", "title": "Update the object at an existing key", "slug": "update-the-object-at-an-existing-key", "summary": "Update the object at an existing key", "type": "self-hosted-operation"}, {"id": "upload-a-new-object", "title": "Upload a new object", "slug": "upload-a-new-object", "summary": "Upload a new object", "type": "self-hosted-operation"}, {"id": "delete-multiple-objects", "title": "Delete multiple objects", "slug": "delete-multiple-objects", "summary": "Delete multiple objects", "type": "self-hosted-operation"}, {"id": "retrieve-an-object", "title": "Retrieve an object", "slug": "retrieve-an-object", "summary": "Retrieve an object", "type": "self-hosted-operation"}, {"id": "generate-a-presigned-url-to-retrieve-an-object", "title": "Generate a presigned url to retrieve an object", "slug": "generate-a-presigned-url-to-retrieve-an-object", "summary": "Generate a presigned url to retrieve an object", "type": "self-hosted-operation"}, {"id": "retrieve-an-object-via-a-presigned-url", "title": "Retrieve an object via a presigned URL", "slug": "retrieve-an-object-via-a-presigned-url", "summary": "Retrieve an object via a presigned URL", "type": "self-hosted-operation"}, {"id": "generate-presigned-urls-to-retrieve-objects", "title": "Generate presigned urls to retrieve objects", "slug": "generate-presigned-urls-to-retrieve-objects", "summary": "Generate presigned urls to retrieve objects", "type": "self-hosted-operation"}, {"id": "moves-an-object", "title": "Moves an object", "slug": "moves-an-object", "summary": "Moves an object", "type": "self-hosted-operation"}, {"id": "search-for-objects-under-a-prefix", "title": "Search for objects under a prefix", "slug": "search-for-objects-under-a-prefix", "summary": "Search for objects under a prefix", "type": "self-hosted-operation"}, {"id": "retrieve-object-info", "title": "Retrieve object info", "slug": "retrieve-object-info", "summary": "Retrieve object info", "type": "self-hosted-operation"}, {"id": "copies-an-object", "title": "Copies an object", "slug": "copies-an-object", "summary": "Copies an object", "type": "self-hosted-operation"}, {"id": "retrieve-an-object-from-a-public-bucket", "title": "Retrieve an object from a public bucket", "slug": "retrieve-an-object-from-a-public-bucket", "summary": "Retrieve an object from a public bucket", "type": "self-hosted-operation"}, {"id": "get-object-info", "title": "Get object info", "slug": "get-object-info", "summary": "Get object info", "type": "self-hosted-operation"}]}]