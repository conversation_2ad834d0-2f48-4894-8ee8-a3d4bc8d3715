configspec: '001'

# This section outlines the general information for the tool.
info:
  id: 'realtime' # {string} A unique ID for this tool.
  version: 'next' # {string} The current version number of the tool.
  title: 'Realtime' # {string} A readable name.
  source: 'https://github.com/supabase/realtime' # {string} Where developers can find the source code.
  bugs: 'https://github.com/supabase/realtime/issues' # {string} Where developers can file bugs.
  spec: 'https://github.com/supabase/supabase/blob/master/spec/realtime_v0_config.yml' # {string} Where developers can find this spec (to link directly in the docs).
  description: |
    You can use Environment Variables to configure your Realtime Server.
  tags:
    - id: general
      title: General Settings
      description: General server settings.
    - id: database
      title: Database Settings
      description: Connecting to your database.

# This section is an array of public functions which a user might need to execute.
parameters:
  - id: 'PORT' # {string} A unique identifier for this param.
    title: 'PORT' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '4000'
    description: |
      Port which you can connect your client/listeners

  - id: 'DB_HOST' # {string} A unique identifier for this param.
    title: 'DB_HOST' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'localhost'
    description: |
      Database host URL

  - id: 'DB_NAME' # {string} A unique identifier for this param.
    title: 'DB_NAME' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'postgres'
    description: |
      Database name

  - id: 'DB_USER' # {string} A unique identifier for this param.
    title: 'DB_USER' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'postgres'
    description: |
      Database user

  - id: 'DB_PASSWORD' # {string} A unique identifier for this param.
    title: 'DB_PASSWORD' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'postgres'
    description: |
      Database password

  - id: 'DB_PORT' # {string} A unique identifier for this param.
    title: 'DB_PORT' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '5432'
    description: |
      Database port

  - id: 'DB_SSL' # {string} A unique identifier for this param.
    title: 'DB_SSL' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'true'
    description: |
      Database SSL connection

  - id: 'DB_IP_VERSION' # {string} A unique identifier for this param.
    title: 'DB_IP_VERSION' # {string} Any name.
    tags: ['database'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'IPv4'
    description: |
      Connect to database via either IPv4 or IPv6. Disregarded if database host is an IP address (e.g. '127.0.0.1') and recommended if database host is a name (e.g. 'db.abcd.supabase.co') to prevent potential non-existent domain (NXDOMAIN) errors.

  - id: 'REPLICATION_MODE' # {string} A unique identifier for this param.
    title: 'REPLICATION_MODE' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'RLS'
    description: |
      Connect to database via either IPv4 or IPv6. Disregarded if database host is an IP address (e.g. '127.0.0.1') and recommended if database host is a name (e.g. 'db.abcd.supabase.co') to prevent potential non-existent domain (NXDOMAIN) errors.

  - id: 'SLOT_NAME' # {string} A unique identifier for this param.
    title: 'SLOT_NAME' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'realtime_rls'
    description: |
      A unique name for Postgres to track the Write-Ahead Logging (WAL). If the Realtime server dies then this slot can keep the changes since the last committed position.

  - id: 'TEMPORARY_SLOT' # {string} A unique identifier for this param.
    title: 'TEMPORARY_SLOT' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'false'
    description: |
      Start logical replication slot as either temporary or permanent

  - id: 'REALTIME_IP_VERSION' # {string} A unique identifier for this param.
    title: 'REALTIME_IP_VERSION' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'IPV6'
    description: |
      Bind realtime via either IPv4 or IPv6

  - id: 'PUBLICATIONS' # {string} A unique identifier for this param.
    title: 'PUBLICATIONS' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '[\"supabase_realtime\"]'
    description: |
      JSON encoded array of publication names. Realtime RLS currently accepts one publication.

  - id: 'SECURE_CHANNELS' # {string} A unique identifier for this param.
    title: 'SECURE_CHANNELS' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'true'
    description: |
      Enable/Disable channels authorization via JWT verification

  - id: 'JWT_SECRET' # {string} A unique identifier for this param.
    title: 'JWT_SECRET' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '""'
    description: |
      HS algorithm octet key (e.g. "95x0oR8jq9unl9pOIx")

  - id: 'JWT_CLAIM_VALIDATORS' # {string} A unique identifier for this param.
    title: 'JWT_CLAIM_VALIDATORS' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '{}'
    description: |
      Expected claim key/value pairs compared to JWT claims via equality checks in order to validate JWT. e.g. '{"iss": "Issuer", "nbf": 1610078130}'.

  - id: 'EXPOSE_METRICS' # {string} A unique identifier for this param.
    title: 'EXPOSE_METRICS' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'false'
    description: |
      Expose Prometheus metrics at '/metrics' endpoint.

  - id: 'DB_RECONNECT_BACKOFF_MIN' # {string} A unique identifier for this param.
    title: 'DB_RECONNECT_BACKOFF_MIN' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '100'
    description: |
      Specify the minimum amount of time to wait before reconnecting to database

  - id: 'DB_RECONNECT_BACKOFF_MAX' # {string} A unique identifier for this param.
    title: 'DB_RECONNECT_BACKOFF_MAX' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '120000'
    description: |
      Specify the maximum amount of time to wait before reconnecting to database

  - id: 'REPLICATION_POLL_INTERVAL' # {string} A unique identifier for this param.
    title: 'REPLICATION_POLL_INTERVAL' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '300'
    description: |
      Specify how often Realtime RLS should poll the replication slot for changes

  - id: 'SUBSCRIPTION_SYNC_INTERVAL' # {string} A unique identifier for this param.
    title: 'SUBSCRIPTION_SYNC_INTERVAL' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '60000'
    description: |
      Specify how often Realtime RLS should confirm connected subscribers and the tables they're listening to

  - id: 'MAX_CHANGES' # {string} A unique identifier for this param.
    title: 'MAX_CHANGES' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '100'
    description: |
      Soft limit for the number of database changes to fetch per replication poll

  - id: 'MAX_RECORD_BYTES' # {string} A unique identifier for this param.
    title: 'MAX_RECORD_BYTES' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '1048576' # bytes or 1MB
    description: |
      Controls the maximum size of a WAL record
