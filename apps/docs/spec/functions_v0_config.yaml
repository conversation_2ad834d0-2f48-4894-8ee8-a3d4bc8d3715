configspec: '001'

# This section outlines the general information for the tool.
info:
  id: 'functions' # {string} A unique ID for this tool.
  version: 'next' # {string} The current version number of the tool.
  title: 'Functions' # {string} A readable name.
  source: 'https://github.com/supabase/edge-runtime' # {string} Where developers can find the source code.
  bugs: 'https://github.com/supabase/edge-runtime/issues' # {string} Where developers can file bugs.
  spec: 'https://github.com/supabase/supabase/blob/master/spec/realtime_v0_config.yml' # {string} Where developers can find this spec (to link directly in the docs).
  description: |
    You can use environment variables to configure your Functions Server.
  tags:
    - id: general
      title: General Settings
      description: General server settings.

# This section is an array of public functions which a user might need to execute.
parameters:
  - id: 'LOGFLARE_SINGLE_TENANT' # {string} A unique identifier for this param.
    title: 'LOGFLARE_SINGLE_TENANT' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'true'
    type: 'boolean'
    description: |
      This is will seed a singular user into the database, and will disable browser authentication. All browser usage will default to this user. Inviting team users and other team-related functionality is currently not supported for self-hosted. Logflare self-hosted is currently intended for single-user experience only.
  - id: 'LOGFLARE_API_KEY' # {string} A unique identifier for this param.
    title: 'LOGFLARE_API_KEY' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: ''
    type: 'string'
    description: |
      Allows you to pass in an API key that will used for authentication. This is intended for programmatic usage where an external program sets the API key. It is advised to use the UI to configure the access tokens instead. If this value is not provided, the default API key will be automatically generated.
