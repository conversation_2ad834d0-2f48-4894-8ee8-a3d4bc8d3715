configspec: '001'

# This section outlines the general information for the tool.
info:
  id: 'storage' # {string} A unique ID for this tool.
  version: 'next' # {string} The current version number of the tool.
  title: 'Storage' # {string} A readable name.
  source: 'https://github.com/supabase/storage-api' # {string} Where developers can find the source code.
  bugs: 'https://github.com/supabase/storage-api/issues' # {string} Where developers can file bugs.
  spec: 'https://github.com/supabase/supabase/blob/master/spec/storage_v1_config.yml' # {string} Where developers can find this spec (to link directly in the docs).
  description: |
    A sample `.env` file is located in the [storage repository](https://github.com/supabase/storage-api/blob/master/.env.sample).

    Use this file to configure your environment variables for your Storage server.
  tags:
    - id: general
      title: General
      description: General Settings
    - id: multitenant
      title: Multi-tenant
      description: Configuration items for multi-tenant servers.

# This section is an array of public functions which a user might need to execute.
parameters:
  - id: 'ANON_KEY' # {string} A unique identifier for this param.
    title: 'ANON_KEY' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      A long-lived JWT with anonymous Postgres privileges.

  - id: 'SERVICE_KEY' # {string} A unique identifier for this param.
    title: 'SERVICE_KEY' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      A long-lived JWT with Postgres privileges to bypass Row Level Security.

  - id: 'TENANT_ID' # {string} A unique identifier for this param.
    title: 'TENANT_ID' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The ID of a Storage tenant.

  - id: 'REGION' # {string} A unique identifier for this param.
    title: 'REGION' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      Region of your S3 bucket.

  - id: 'GLOBAL_S3_BUCKET' # {string} A unique identifier for this param.
    title: 'GLOBAL_S3_BUCKET' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      Name of your S3 bucket.

  - id: 'POSTGREST_URL' # {string} A unique identifier for this param.
    title: 'POSTGREST_URL' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The URL of your PostgREST server.

  - id: 'PGRST_JWT_SECRET' # {string} A unique identifier for this param.
    title: 'PGRST_JWT_SECRET' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      A JWT Secret for the PostgREST database.

  - id: 'DATABASE_URL' # {string} A unique identifier for this param.
    title: 'DATABASE_URL' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The URL of your Postgres database.

  - id: 'PGOPTIONS' # {string} A unique identifier for this param.
    title: 'PGOPTIONS' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      Additional configuration parameters for Postgres startup.

  - id: 'FILE_SIZE_LIMIT' # {string} A unique identifier for this param.
    title: 'FILE_SIZE_LIMIT' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The maximum file size allowed.

  - id: 'STORAGE_BACKEND' # {string} A unique identifier for this param.
    title: 'STORAGE_BACKEND' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The storage provider.

  - id: 'FILE_STORAGE_BACKEND_PATH' # {string} A unique identifier for this param.
    title: 'FILE_STORAGE_BACKEND_PATH' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The location storage when the "STORAGE_BACKEND" is set to "file".

  - id: 'IS_MULTITENANT' # {string} A unique identifier for this param.
    title: 'IS_MULTITENANT' # {string} Any name.
    tags: ['multitenant'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      Operate across multiple tenants.

  - id: 'MULTITENANT_DATABASE_URL' # {string} A unique identifier for this param.
    title: 'MULTITENANT_DATABASE_URL' # {string} Any name.
    tags: ['multitenant'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The URL of the multitenant Postgres database.

  - id: 'X_FORWARDED_HOST_REGEXP' # {string} A unique identifier for this param.
    title: 'X_FORWARDED_HOST_REGEXP' # {string} Any name.
    tags: ['multitenant'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      TBD.

  - id: 'POSTGREST_URL_SUFFIX' # {string} A unique identifier for this param.
    title: 'POSTGREST_URL_SUFFIX' # {string} Any name.
    tags: ['multitenant'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      The suffix for the PostgREST instance.

  - id: 'ADMIN_API_KEYS' # {string} A unique identifier for this param.
    title: 'ADMIN_API_KEYS' # {string} Any name.
    tags: ['multitenant'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      Secure API key for administrative endpoints.

  - id: 'ENCRYPTION_KEY' # {string} A unique identifier for this param.
    title: 'ENCRYPTION_KEY' # {string} Any name.
    tags: ['multitenant'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    # default: '5432'
    description: |
      An key for encryting/decrypting secrets.
