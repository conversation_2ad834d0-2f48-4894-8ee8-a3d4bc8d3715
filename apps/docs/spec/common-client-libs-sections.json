[{"title": "Introduction", "id": "introduction", "slug": "introduction", "type": "markdown"}, {"title": "Installing", "id": "installing", "slug": "installing", "type": "markdown", "excludes": ["reference_javascript_v1", "reference_kotlin_v1", "reference_swift_v1"]}, {"title": "Initializing", "id": "initializing", "slug": "initializing", "type": "function"}, {"title": "TypeScript support", "id": "typescript-support", "slug": "typescript-support", "type": "markdown", "excludes": ["reference_javascript_v1", "reference_dart_v1", "reference_dart_v2", "reference_python_v2", "reference_csharp_v0", "reference_csharp_v1", "reference_swift_v1", "reference_swift_v2", "reference_kotlin_v1", "reference_kotlin_v2", "reference_kotlin_v3"]}, {"title": "Upgrade guide", "id": "upgrade-guide", "slug": "upgrade-guide", "type": "markdown", "excludes": ["reference_javascript_v2", "reference_dart_v1", "reference_python_v2", "reference_csharp_v0", "reference_csharp_v1", "reference_swift_v1", "reference_swift_v2", "reference_kotlin_v1", "reference_kotlin_v2", "reference_kotlin_v3"]}, {"type": "category", "title": "Database", "items": [{"id": "select", "title": "Fetch data", "slug": "select", "product": "database", "type": "function"}, {"id": "insert", "title": "Insert data", "slug": "insert", "product": "database", "type": "function"}, {"id": "update", "title": "Update data", "slug": "update", "product": "database", "type": "function"}, {"id": "upsert", "title": "Upsert data", "slug": "upsert", "product": "database", "type": "function"}, {"id": "delete", "title": "Delete data", "slug": "delete", "product": "database", "type": "function"}, {"id": "rpc", "title": "Call a Postgres function", "slug": "rpc", "product": "database", "type": "function"}, {"id": "using-filters", "isFunc": false, "title": "Using filters", "slug": "using-filters", "product": "database", "type": "function", "items": [{"id": "eq", "title": "Column is equal to a value", "slug": "eq", "product": "database", "parent": "filters", "type": "function"}, {"id": "neq", "title": "Column is not equal to a value", "slug": "neq", "product": "database", "parent": "filters", "type": "function"}, {"id": "gt", "title": "Column is greater than a value", "slug": "gt", "product": "database", "parent": "filters", "type": "function"}, {"id": "gte", "title": "Column is greater than or equal to a value", "slug": "gte", "product": "database", "parent": "filters", "type": "function"}, {"id": "lt", "title": "Column is less than a value", "slug": "lt", "product": "database", "parent": "filters", "type": "function"}, {"id": "lte", "title": "Column is less than or equal to a value", "slug": "lte", "product": "database", "parent": "filters", "type": "function"}, {"id": "like", "title": "Column matches a pattern", "slug": "like", "product": "database", "parent": "filters", "type": "function"}, {"id": "ilike", "title": "Column matches a case-insensitive pattern", "slug": "ilike", "product": "database", "parent": "filters", "type": "function"}, {"id": "is", "title": "Column is a value", "slug": "is", "product": "database", "parent": "filters", "type": "function"}, {"id": "in", "title": "Column is in an array", "slug": "in", "product": "database", "parent": "filters", "type": "function"}, {"id": "contains", "title": "Column contains every element in a value", "slug": "contains", "product": "database", "parent": "filters", "type": "function"}, {"id": "contained-by", "title": "Contained by value", "slug": "<PERSON>by", "product": "database", "parent": "filters", "type": "function"}, {"id": "range-gt", "title": "Greater than a range", "slug": "rangegt", "product": "database", "parent": "filters", "type": "function"}, {"id": "range-gte", "title": "Greater than or equal to a range", "slug": "rangegte", "product": "database", "parent": "filters", "type": "function"}, {"id": "range-lt", "title": "Less than a range", "slug": "rangelt", "product": "database", "parent": "filters", "type": "function"}, {"id": "range-lte", "title": "Less than or equal to a range", "slug": "rangelte", "product": "database", "parent": "filters", "type": "function"}, {"id": "range-adjacent", "title": "Mutually exclusive to a range", "slug": "<PERSON><PERSON><PERSON><PERSON>", "product": "database", "parent": "filters", "type": "function"}, {"id": "overlaps", "title": "With a common element", "slug": "overlaps", "product": "database", "parent": "filters", "type": "function"}, {"id": "text-search", "title": "Match a string", "slug": "textsearch", "product": "database", "parent": "filters", "type": "function"}, {"id": "match", "title": "Match an associated value", "slug": "match", "product": "database", "parent": "filters", "type": "function"}, {"id": "not", "title": "Don't match the filter", "slug": "not", "product": "database", "parent": "filters", "type": "function"}, {"id": "or", "title": "Match at least one filter", "slug": "or", "product": "database", "parent": "filters", "type": "function"}, {"id": "filter", "title": "Match the filter", "slug": "filter", "product": "database", "parent": "filters", "type": "function"}]}, {"id": "using-modifiers", "isFunc": false, "title": "Using modifiers", "slug": "using-modifiers", "product": "database", "parent": "modifiers", "type": "function", "items": [{"id": "db-modifiers-select", "title": "Return data after inserting", "slug": "db-modifiers-select", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "order", "title": "Order the results", "slug": "order", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "limit", "title": "Limit the number of rows returned", "slug": "limit", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "range", "title": "Limit the query to a range", "slug": "range", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "abort-signal", "title": "Set an abort signal", "slug": "db-abortsign<PERSON>", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "single", "title": "Retrieve one row of data", "slug": "single", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "maybe-single", "title": "Retrieve zero or one row of data", "slug": "<PERSON><PERSON><PERSON>", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "csv", "title": "Retrieve as a CSV", "slug": "db-csv", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "returns", "title": "Override type of successful response", "slug": "db-returns", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "overrideTypes", "title": "Partially override or replace type of successful response", "slug": "db-overrideTypes", "product": "database", "parent": "modifiers", "type": "function"}, {"id": "explain", "title": "Using explain", "slug": "explain", "product": "database", "parent": "modifiers", "type": "function"}]}]}, {"type": "category", "title": "<PERSON><PERSON>", "items": [{"id": "auth-api", "title": "Overview", "slug": "auth-api", "type": "function"}, {"id": "sign-up", "title": "Create a new user", "slug": "auth-signup", "product": "auth", "type": "function"}, {"id": "on-auth-state-change", "title": "Listen to auth events", "slug": "auth-onauthstatechange", "product": "auth", "type": "function"}, {"id": "sign-in", "title": "Sign in a user", "slug": "auth-signin", "product": "auth", "type": "function"}, {"id": "sign-in-with-provider", "title": "Sign in a user through OAuth", "slug": "auth-signin<PERSON><PERSON><PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "sign-in-anonymously", "title": "Create an anonymous user", "slug": "auth-signinanonymously", "product": "auth", "type": "function"}, {"id": "sign-in-with-password", "title": "Sign in a user", "slug": "auth-signinwithpassword", "product": "auth", "type": "function"}, {"id": "sign-in-with-id-token", "title": "Sign in with ID Token", "slug": "auth-sign<PERSON><PERSON><PERSON><PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "sign-in-with-otp", "title": "Sign in a user through OTP", "slug": "auth-signin<PERSON><PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "sign-in-with-whatsapp-otp", "title": "Sign in a user through WhatsApp", "slug": "auth-signin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "sign-in-with-apple", "title": "Sign in with Apple", "slug": "sign-in-with-apple", "product": "auth", "type": "function"}, {"id": "sign-in-with-o<PERSON>h", "title": "Sign in a user through OAuth", "slug": "auth-signin<PERSON><PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "sign-in-with-sso", "title": "Sign in a user through SSO", "slug": "auth-signin<PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "sign-out", "title": "Sign out a user", "slug": "auth-signout", "product": "auth", "type": "function"}, {"id": "reset-password-for-email", "title": "Send a password reset request", "slug": "auth-resetpasswordforemail", "product": "auth", "type": "function"}, {"id": "auth-update", "title": "Update a user", "slug": "auth-update", "product": "auth", "type": "function"}, {"id": "set-auth", "title": "Update the access token", "slug": "auth-<PERSON><PERSON><PERSON>", "product": "auth", "type": "function"}, {"id": "user", "title": "Retrieve a user", "slug": "auth-user", "product": "auth", "type": "function"}, {"id": "session", "title": "Retrieve a session", "slug": "auth-session", "product": "auth", "type": "function"}, {"id": "verify-otp", "title": "Verify and log in through OTP", "slug": "auth-verifyotp", "product": "auth", "type": "function"}, {"id": "current-session", "title": "Retrieve a session", "slug": "auth-currentsession", "product": "auth", "type": "function"}, {"id": "current-user", "title": "Retrieve a user", "slug": "auth-currentuser", "product": "auth", "type": "function"}, {"id": "get-session", "title": "Retrieve a session", "slug": "auth-getsession", "product": "auth", "type": "function"}, {"id": "refresh-session", "title": "Retrieve a new session", "slug": "auth-refreshsession", "product": "auth", "type": "function"}, {"id": "get-user", "title": "Retrieve a user", "slug": "auth-getuser", "product": "auth", "type": "function"}, {"id": "update-user", "title": "Update a user", "slug": "auth-updateuser", "product": "auth", "type": "function"}, {"id": "get-user-identities", "title": "Retrieve identities linked to a user", "slug": "auth-getuseridentities", "product": "auth", "type": "function"}, {"id": "link-identity", "title": "Link an identity to a user", "slug": "auth-linkidentity", "product": "auth", "type": "function"}, {"id": "unlink-identity", "title": "Unlink an identity from a user", "slug": "auth-unlinkidentity", "product": "auth", "type": "function"}, {"id": "send-password-reauthentication", "title": "Send a password reauthentication nonce", "slug": "auth-reauthentication", "product": "auth", "type": "function"}, {"id": "resend-email-or-phone-otps", "title": "Resend an OTP", "slug": "auth-resend", "product": "auth", "type": "function"}, {"id": "set-session", "title": "Set the session data", "slug": "auth-setsession", "product": "auth", "type": "function"}, {"id": "exchange-code-for-session", "title": "Exchange an auth code for a session", "slug": "auth-exchangecodeforsession", "product": "auth", "type": "function"}, {"id": "start-auto-refresh", "title": "Start auto-refresh session (non-browser)", "slug": "auth-startautorefresh", "product": "auth", "type": "function"}, {"id": "stop-auto-refresh", "title": "Stop auto-refresh session (non-browser)", "slug": "auth-stopautorefresh", "product": "auth", "type": "function"}, {"id": "auth-mfa-api", "title": "Auth MFA", "slug": "auth-mfa-api", "type": "function", "items": [{"id": "mfa-enroll", "title": "Enroll a factor", "slug": "auth-mfa-enroll", "product": "auth", "type": "function"}, {"id": "mfa-challenge", "title": "Create a challenge", "slug": "auth-mfa-challenge", "product": "auth", "type": "function"}, {"id": "mfa-verify", "title": "Verify a challenge", "slug": "auth-mfa-verify", "product": "auth", "type": "function"}, {"id": "mfa-challenge-and-verify", "title": "Create and verify a challenge", "slug": "auth-mfa-challengeandverify", "product": "auth", "type": "function"}, {"id": "mfa-unenroll", "title": "Unenroll a factor", "slug": "auth-mfa-unenroll", "product": "auth", "type": "function"}, {"id": "mfa-get-authenticator-assurance-level", "title": "Get Authenticator Assurance Level", "slug": "auth-mfa-getauthenticatorassurancelevel", "product": "auth", "type": "function"}]}, {"id": "admin-api", "title": "<PERSON><PERSON>", "slug": "admin-api", "type": "function", "items": [{"id": "get-user-by-id", "title": "Retrieve a user", "slug": "auth-admin-get<PERSON><PERSON><PERSON>", "product": "auth-admin", "type": "function"}, {"id": "list-users", "title": "List all users", "slug": "auth-admin-listusers", "product": "auth-admin", "type": "function"}, {"id": "create-user", "title": "Create a user", "slug": "auth-admin-createuser", "product": "auth-admin", "type": "function"}, {"id": "delete-user", "title": "Delete a user", "slug": "auth-admin-deleteuser", "product": "auth-admin", "type": "function"}, {"id": "invite-user-by-email", "title": "Send an email invite link", "slug": "auth-admin-<PERSON><PERSON><PERSON><PERSON><PERSON>", "product": "auth-admin", "type": "function"}, {"id": "generate-link", "title": "Generate an email link", "slug": "auth-admin-generatelink", "product": "auth-admin", "type": "function"}, {"id": "send-mobile-otp", "title": "Send a one-time passcode", "slug": "auth-api-sendmobileotp", "product": "auth-server", "type": "function"}, {"id": "update-user-by-id", "title": "Update a user", "slug": "auth-admin-update<PERSON><PERSON><PERSON>", "product": "auth-admin", "type": "function"}, {"id": "mfa-list-factors", "title": "List all factors for a user", "slug": "auth-admin-mfa-listfactors", "product": "auth-admin", "type": "function"}, {"id": "mfa-delete-factor", "title": "Delete a factor for a user", "slug": "auth-admin-mfa-deletefactor", "product": "auth-admin", "type": "function"}]}]}, {"type": "category", "title": "Edge Functions", "items": [{"id": "invoke", "title": "Invokes a Supabase Edge Function.", "slug": "functions-invoke", "product": "functions", "type": "function"}]}, {"type": "category", "title": "Realtime", "items": [{"id": "stream", "title": "Listen to database changes", "slug": "stream", "product": "realtime", "type": "function"}, {"id": "subscribe", "title": "Subscribe to channel", "slug": "subscribe", "product": "realtime", "type": "function"}, {"id": "remove-subscription", "title": "Remove a subscription", "slug": "removesubscription", "product": "realtime", "type": "function"}, {"id": "remove-all-subscriptions", "title": "Remove all subscriptions", "slug": "removeallsubscriptions", "product": "realtime", "type": "function"}, {"id": "get-subscriptions", "title": "Retrieve subscriptions", "slug": "getsubscriptions", "product": "realtime", "type": "function"}, {"id": "remove-channel", "title": "Unsubscribe from a channel", "slug": "removechannel", "product": "realtime", "type": "function"}, {"id": "remove-all-channels", "title": "Unsubscribe from all channels", "slug": "removeallchannels", "product": "realtime", "type": "function"}, {"id": "get-channels", "title": "Retrieve all channels", "slug": "getchannels", "product": "realtime", "type": "function"}, {"id": "broadcast-message", "title": "Broadcast a message", "slug": "broadcastmessage", "product": "realtime", "type": "function"}]}, {"type": "category", "title": "Storage", "items": [{"id": "create-bucket", "title": "Create a bucket", "slug": "storage-createbucket", "product": "storage", "type": "function"}, {"id": "get-bucket", "title": "Retrieve a bucket", "slug": "storage-getbucket", "product": "storage", "type": "function"}, {"id": "list-buckets", "title": "List all buckets", "slug": "storage-listbuckets", "product": "storage", "type": "function"}, {"id": "update-bucket", "title": "Update a bucket", "slug": "storage-updatebucket", "product": "storage", "type": "function"}, {"id": "delete-bucket", "title": "Delete a bucket", "slug": "storage-deletebucket", "product": "storage", "type": "function"}, {"id": "empty-bucket", "title": "Empty a bucket", "slug": "storage-emptybucket", "product": "storage", "type": "function"}, {"id": "from-upload", "title": "Upload a file", "slug": "storage-from-upload", "product": "storage", "type": "function"}, {"id": "from-download", "title": "Download a file", "slug": "storage-from-download", "product": "storage", "type": "function"}, {"id": "from-list", "title": "List all files in a bucket", "slug": "storage-from-list", "product": "storage", "type": "function"}, {"id": "from-update", "title": "Replace an existing file", "slug": "storage-from-update", "product": "storage", "type": "function"}, {"id": "from-move", "title": "Move an existing file", "slug": "storage-from-move", "product": "storage", "type": "function"}, {"id": "from-copy", "title": "Copy an existing file", "slug": "storage-from-copy", "product": "storage", "type": "function"}, {"id": "from-remove", "title": "Delete files in a bucket", "slug": "storage-from-remove", "product": "storage", "type": "function"}, {"id": "from-create-signed-url", "title": "Create a signed URL", "slug": "storage-from-createsignedurl", "product": "storage", "type": "function"}, {"id": "from-create-signed-urls", "title": "<PERSON><PERSON> signed URLs", "slug": "storage-from-createsignedurls", "product": "storage", "type": "function"}, {"id": "from-create-signed-upload-url", "title": "Create signed upload URL", "slug": "storage-from-createsigneduploadurl", "product": "storage", "type": "function"}, {"id": "from-upload-to-signed-url", "title": "Upload to a signed URL", "slug": "storage-from-uploadtosignedurl", "product": "storage", "type": "function"}, {"id": "from-get-public-url", "title": "Retrieve public URL", "slug": "storage-from-getpublicurl", "product": "storage", "type": "function"}]}, {"type": "category", "title": "Misc", "excludes": ["reference_dart_v1", "reference_dart_v2", "reference_javascript_v1", "reference_kotlin_v1", "reference_kotlin_v2", "reference_python_v2", "reference_swift_v1", "reference_swift_v2", "reference_kotlin_v3"], "items": [{"title": "Release Notes", "id": "release-notes", "slug": "release-notes", "isFunc": false, "type": "markdown"}]}]