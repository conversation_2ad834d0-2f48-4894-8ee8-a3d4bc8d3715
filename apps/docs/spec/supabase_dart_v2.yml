openref: 0.1

info:
  id: reference/dart
  title: Supabase Dart Client
  description: |

    Supabase Dart.
  slugPrefix: '/'
  specUrl: https://github.com/supabase/supabase/edit/master/apps/docs/spec/supabase_dart_v2.yml
  libraries:
    - name: 'Dart'
      id: 'dart'
      version: '0.0.1'

functions:
  - id: initializing
    title: 'Initializing'
    description: |
      You can initialize Supabase with the static `initialize()` method of the `Supabase` class.

      The Supabase client is your entrypoint to the rest of the Supabase functionality
      and is the easiest way to interact with everything we offer within the Supabase ecosystem.
    params:
      - name: url
        isOptional: false
        type: string
        description: The unique Supabase URL which is supplied when you create a new project in your project dashboard.
      - name: anonKey
        isOptional: false
        type: string
        description: The unique Supabase Key which is supplied when you create a new project in your project dashboard.
      - name: headers
        isOptional: true
        type: Map<String, String>
        description: Custom header to be passed to the Supabase client.
      - name: httpClient
        isOptional: true
        type: Client
        description: Custom http client to be used by the Supabase client.
      - name: authOptions
        isOptional: true
        type: FlutterAuthClientOptions
        description: Options to change the Auth behaviors.
        subContent:
          - name: authFlowType
            isOptional: true
            type: AuthFlowType
            description: Whether to use the `pkce` flow or the `implicit` flow. Defaults to `pkce`.
          - name: localStorage
            isOptional: true
            type: LocalStorage
            description: Parameter to override the local storage to store auth tokens.
          - name: autoRefreshToken
            isOptional: true
            type: bool
            description: Whether to automatically refresh the token when it expires. Defaults to `true`.
      - name: postgrestOptions
        isOptional: true
        type: PostgrestClientOptions
        description: Options to change the Postgrest behaviors.
        subContent:
          - name: schema
            isOptional: true
            type: String
            description: Schema to query with the Supabase client. Defaults to `public`.
      - name: realtimeClientOptions
        isOptional: true
        type: RealtimeClientOptions
        description: Options to change the Realtime behaviors.
        subContent:
          - name: logLevel
            isOptional: true
            type: RealtimeLogLevel
            description: Level of realtime server logs to to be logged.
      - name: storageOptions
        isOptional: true
        type: StorageClientOptions
        description: Options to change the Storage behaviors.
        subContent:
          - name: retryAttempts
            isOptional: true
            type: int
            description: The number of times to retry a failed upload request. Defaults to `0`.
    examples:
      - id: flutter-initialize
        name: For Flutter
        code: |
          ```dart
          Future<void> main() async {
            await Supabase.initialize(
              url: 'https://xyzcompany.supabase.co',
              anonKey: 'public-anon-key',
            );

            runApp(MyApp());
          }

          // Get a reference your Supabase client
          final supabase = Supabase.instance.client;
          ```
      - id: for-other-dart-projects
        name: For other Dart projects
        code: |
          ```dart
          final supabase = SupabaseClient(
            'https://xyzcompany.supabase.co',
            'public-anon-key',
          );
          ```

  - id: sign-up
    title: 'signUp()'
    description: |
      Creates a new user.
    notes: |
      - By default, the user needs to verify their email address before logging in. To turn this off, disable **Confirm email** in [your project](https://supabase.com/dashboard/project/_/auth/providers).
      - **Confirm email** determines if users need to confirm their email address after signing up.
        - If **Confirm email** is enabled, a `user` is returned but `session` is null.
        - If **Confirm email** is disabled, both a `user` and a `session` are returned.
      - When the user confirms their email address, they are redirected to the [`SITE_URL`](https://supabase.com/docs/guides/auth/redirect-urls) by default. You can modify your `SITE_URL` or add additional redirect URLs in [your project](https://supabase.com/dashboard/project/_/auth/url-configuration).
      - If signUp() is called for an existing confirmed user:
        - When both **Confirm email** and **Confirm phone** (even when phone provider is disabled) are enabled in [your project](/dashboard/project/_/auth/providers), an obfuscated/fake user object is returned.
        - When either **Confirm email** or **Confirm phone** (even when phone provider is disabled) is disabled, the error message, `User already registered` is returned.
    params:
      - name: email
        isOptional: true
        type: String
        description: User's email address to be used for email authentication.
      - name: phone
        isOptional: true
        type: String
        description: User's phone number to be used for phone authentication.
      - name: password
        isOptional: false
        type: String
        description: Password to be used for authentication.
      - name: emailRedirectTo
        isOptional: true
        type: String
        description: The URL to redirect the user to after they confirm their email address.
      - name: data
        isOptional: true
        type: Map<String, dynamic>
        description: The user's metadata to be stored in the user's object.
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification.
      - name: channel
        isOptional: true
        type: OtpChannel
        description: Messaging channel to use (e.g. whatsapp or sms). Defaults to `OtpChannel.sms`.
    examples:
      - id: sign-up
        name: Sign up with an email and password
        isSpotlight: true
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.signUp(
            email: '<EMAIL>',
            password: 'example-password',
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
        response: |
          ```json
          // Some fields may be null if "confirm email" is enabled.
          AuthResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                emailConfirmedAt: '2024-01-01T00:00:00Z',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {
                  'provider': 'email',
                  'providers': ['email']
                },
                userMetadata: {},
                identities: [
                  UserIdentity(
                    identityId: '********-2222-2222-2222-********2222',
                    id: '11111111-1111-1111-1111-111111111111',
                    userId: '11111111-1111-1111-1111-111111111111',
                    identityData: {
                      'email': '<EMAIL>',
                      'email_verified': false,
                      'phone_verified': false,
                      'sub': '11111111-1111-1111-1111-111111111111'
                    },
                    provider: 'email',
                    lastSignInAt: '2024-01-01T00:00:00Z',
                    createdAt: '2024-01-01T00:00:00Z',
                    updatedAt: '2024-01-01T00:00:00Z',
                  )
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              ),
            ),
          );
          ```
      - id: sign-up-phone
        name: Sign up with a phone number and password (SMS)
        isSpotlight: true
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.signUp(
            phone: '123456789',
            password: 'example-password',
            channel: OtpChannel.sms,
          );
          ```
      - id: sign-up-with-metadata
        name: Sign up with additional metadata
        isSpotlight: true
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.signUp(
            email: '<EMAIL>',
            password: 'example-password',
            data: {'username': 'my_user_name'},
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
      - id: sign-up-with-redirect-url
        name: Sign up with redirect URL
        isSpotlight: true
        description: |
          - See [redirect URLs and wildcards](/docs/guides/auth#redirect-urls-and-wildcards) to add additional redirect URLs to your project.
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.signUp(
            email: '<EMAIL>',
            password: 'example-password',
            emailRedirectTo: 'com.supabase.myapp://callback',
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
  - id: sign-in-anonymously
    title: 'signInAnonymously()'
    description: |
      Creates an anonymous user.
    notes: |
      - Returns an anonymous user
      - It is recommended to set up captcha for anonymous sign-ins to prevent abuse. You can pass in the captcha token in the `options` param.
    params:
      - name: data
        isOptional: true
        type: Map<String, dynamic>
        description: The user's metadata to be stored in the user's object.
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification.
    examples:
      - id: sign-in-anonymously
        name: Create an anonymous user
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.signInAnonymously();
          ```
        response: |
          ```json
          AuthResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {},
              userMetadata: {},
              identities: [],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
              isAnonymous: true,
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {},
                userMetadata: {},
                identities: [],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
                isAnonymous: true,
              ),
            ),
          );
          ```
      - id: sign-in-anonymously-with-user-metadata
        name: Create an anonymous user with custom user metadata
        isSpotlight: false
        code: |
          ```dart
          await supabase.auth.signInAnonymously(
            data: {'hello': 'world'},
          );
          ```
  - id: sign-in-with-password
    title: 'signInWithPassword()'
    description: |
      Log in an existing user using email or phone number with password.
    notes: |
      - Requires either an email and password or a phone number and password.
    params:
      - name: email
        isOptional: true
        type: String
        description: User's email address to be used for email authentication.
      - name: phone
        isOptional: true
        type: String
        description: User's phone number to be used for phone authentication.
      - name: password
        isOptional: false
        type: String
        description: Password to be used for authentication.
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification.
    examples:
      - id: sign-in-with-email-and-password
        name: Sign in with email and password
        isSpotlight: true
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.signInWithPassword(
            email: '<EMAIL>',
            password: 'example-password',
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
        response: |
          ```json
          AuthResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                emailConfirmedAt: '2024-01-01T00:00:00Z',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {
                  'provider': 'email',
                  'providers': ['email']
                },
                userMetadata: {},
                identities: [
                  UserIdentity(
                    identityId: '********-2222-2222-2222-********2222',
                    id: '11111111-1111-1111-1111-111111111111',
                    userId: '11111111-1111-1111-1111-111111111111',
                    identityData: {
                      'email': '<EMAIL>',
                      'email_verified': false,
                      'phone_verified': false,
                      'sub': '11111111-1111-1111-1111-111111111111'
                    },
                    provider: 'email',
                    lastSignInAt: '2024-01-01T00:00:00Z',
                    createdAt: '2024-01-01T00:00:00Z',
                    updatedAt: '2024-01-01T00:00:00Z',
                  )
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              ),
            ),
          );
          ```
      - id: sign-in-with-phone-and-password
        name: Sign in with phone and password
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.signInWithPassword(
            phone: '+***********',
            password: 'example-password',
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
  - id: sign-in-with-otp
    title: 'signInWithOtp()'
    notes: |
      - Requires either an email or phone number.
      - This method is used for passwordless sign-ins where an OTP is sent to the user's email or phone number.
      - If you're using an email, you can configure whether you want the user to receive a magiclink or an OTP.
      - If you're using phone, you can configure whether you want the user to receive an OTP.
      - The magic link's destination URL is determined by the [`SITE_URL`](https://supabase.com/docs/guides/auth/redirect-urls). You can modify the `SITE_URL` or add additional redirect urls in [your project](https://supabase.com/dashboard/project/_/auth/url-configuration).
    params:
      - name: email
        isOptional: true
        type: String
        description: Email address to send the magic link or OTP to.
      - name: phone
        isOptional: true
        type: String
        description: Phone number to send the OTP to.
      - name: emailRedirectTo
        isOptional: true
        type: String
        description: The URL to redirect the user to after they click on the magic link.
      - name: shouldCreateUser
        isOptional: true
        type: bool
        description: If set to false, this method will not create a new user. Defaults to true.
      - name: data
        isOptional: true
        type: Map<String, dynamic>
        description: The user's metadata to be stored in the user's object.
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification.
      - name: channel
        isOptional: true
        type: OtpChannel
        description: Messaging channel to use (e.g. whatsapp or sms). Defaults to `OtpChannel.sms`.
    examples:
      - id: sign-in-with-email
        name: Sign in with email.
        isSpotlight: true
        description: |
          The user will be sent an email which contains either a magiclink or an OTP or both. By default, a given user can only request an OTP once every 60 seconds.
          You can pass `emailRedirectTo` with a dynamic link to bring the users back to your app after they click on the magic link.
        code: |
          ```dart
          await supabase.auth.signInWithOtp(
            email: '<EMAIL>',
            emailRedirectTo: kIsWeb ? null : 'io.supabase.flutter://signin-callback/',
          );
          ```
        response: |
          ```json
          AuthResponse(
            user: null,
            session: null,
          );
          ```
      - id: sign-in-with-sms-otp
        name: Sign in with SMS OTP.
        description: The user will be sent a SMS which contains an OTP. By default, a given user can only request an OTP once every 60 seconds.
        code: |
          ```dart
          await supabase.auth.signInWithOtp(
            phone: '+***********',
          );
          ```
      - id: sign-in-with-whatsapp-otp
        name: Sign in with WhatsApp OTP
        isSpotlight: false
        description: The user will be sent a WhatsApp message which contains an OTP. By default, a given user can only request an OTP once every 60 seconds. Note that a user will need to have a valid WhatsApp account that is linked to Twilio in order to use this feature.
        code: |
          ```dart
          await supabase.auth.signInWithOtp(
            phone: '+***********',
            channel: OtpChannel.whatsapp,
          );
          ```
  - id: sign-in-with-id-token
    title: 'signInWithIdToken()'
    description: |
      Allows you to perform native Google and Apple sign in by combining it with [google_sign_in](https://pub.dev/packages/google_sign_in) or [sign_in_with_apple](https://pub.dev/packages/sign_in_with_apple) packages.
    params:
      - name: provider
        isOptional: false
        type: OAuthProvider
        description: The provider to perform the sign in with. Currently, `OAuthProvider.google` and `OAuthProvider.apple` are supported.
      - name: idToken
        isOptional: false
        type: String
        description: The identity token obtained from the third-party provider.
      - name: accessToken
        isOptional: true
        type: String
        description: Access token obtained from the third-party provider. Required for Google sign in.
      - name: nonce
        isOptional: true
        type: String
        description: Raw nonce value used to perform the third-party sign in. Required for Apple sign-in.
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification.
    examples:
      - id: sign-in-with-google
        name: Native Google sign in
        isSpotlight: true
        description: |
          You can perform native Google sign in on Android and iOS using [google_sign_in](https://pub.dev/packages/google_sign_in).
          For platform specific settings, follow the instructions in the package README.

          First, create client IDs for your app. You need to create a web client ID as well to perform Google sign-in with Supabase.

          - [Steps to obtain web client ID](https://developers.google.com/identity/sign-in/android/start-integrating#configure_a_project)
          - [Steps to obtain Android client ID](https://developers.google.com/identity/sign-in/android/start-integrating#configure_a_project)
          - [Steps to obtain iOS client ID](https://developers.google.com/identity/sign-in/ios/start-integrating#get_an_oauth_client_id)

          Add the web client ID to the `Authentication -> Providers -> Google -> Client IDs` section in your Supabase dashboard
        code: |
          ```dart
          import 'package:google_sign_in/google_sign_in.dart';
          import 'package:supabase_flutter/supabase_flutter.dart';

          const webClientId = '<web client ID that you registered on Google Cloud, for example my-web.apps.googleusercontent.com>';

          const iosClientId = '<iOS client ID that you registered on Google Cloud, for example my-ios.apps.googleusercontent.com';

          final GoogleSignIn googleSignIn = GoogleSignIn(
            clientId: iosClientId,
            serverClientId: webClientId,
          );
          final googleUser = await googleSignIn.signIn();
          final googleAuth = await googleUser!.authentication;
          final accessToken = googleAuth.accessToken;
          final idToken = googleAuth.idToken;

          if (accessToken == null) {
            throw 'No Access Token found.';
          }
          if (idToken == null) {
            throw 'No ID Token found.';
          }

          final response = await supabase.auth.signInWithIdToken(
            provider: OAuthProvider.google,
            idToken: idToken,
            accessToken: accessToken,
          );
          ```
        response: |
          ```json
          AuthResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                ...
              },
              userMetadata: {
                ...
              },
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  provider: 'google',
                  ...
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                emailConfirmedAt: '2024-01-01T00:00:00Z',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {
                  ...
                },
                userMetadata: {
                  ...
                },
                identities: [
                  UserIdentity(
                    identityId: '********-2222-2222-2222-********2222',
                    provider: 'google',
                    ...
                  )
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              ),
            ),
          );
          ```
      - id: sign-in-with-apple
        name: Native Apple Sign in
        description: You need to [register your app ID with Apple](https://developer.apple.com/help/account/manage-identifiers/register-an-app-id/) with the `Sign In with Apple` capability selected, and add the bundle ID to your Supabase dashboard in `Authentication -> Providers -> Apple` before performing native Apple sign in.
        code: |
          ```dart
          import 'package:sign_in_with_apple/sign_in_with_apple.dart';
          import 'package:supabase_flutter/supabase_flutter.dart';
          import 'package:crypto/crypto.dart';

          /// Performs Apple sign in on iOS or macOS
          final rawNonce = supabase.auth.generateRawNonce();
          final hashedNonce = sha256.convert(utf8.encode(rawNonce)).toString();

          final credential = await SignInWithApple.getAppleIDCredential(
            scopes: [
              AppleIDAuthorizationScopes.email,
              AppleIDAuthorizationScopes.fullName,
            ],
            nonce: hashedNonce,
          );

          final idToken = credential.identityToken;
          if (idToken == null) {
            throw const AuthException(
              'Could not find ID Token from generated credential.',
            );
          }

          final response = await supabase.auth.signInWithIdToken(
            provider: OAuthProvider.apple,
            idToken: idToken,
            nonce: rawNonce,
          );
          ```
  - id: sign-in-with-oauth
    title: 'signInWithOAuth()'
    description: |
      Signs the user in using third-party OAuth providers.
    notes: |
      - This method is used for signing in using a third-party provider.
      - Supabase supports many different [third-party providers](https://supabase.com/docs/guides/auth#providers).
    params:
      - name: provider
        isOptional: false
        type: OAuthProvider
        description: The OAuth provider to use for signing in.
      - name: redirectTo
        isOptional: true
        type: String
        description: The URL to redirect the user to after they sign in with the third-party provider.
      - name: scopes
        isOptional: true
        type: String
        description: A list of scopes to request from the third-party provider.
      - name: authScreenLaunchMode
        isOptional: true
        type: LaunchMode
        description: The launch mode for the auth screen. Defaults to `LaunchMode.platformDefault`.
      - name: queryParams
        isOptional: true
        type: Map<String, String>
        description: Additional query parameters to be passed to the OAuth flow.
    examples:
      - id: sign-in-using-a-third-party-provider
        name: Sign in using a third-party provider
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.signInWithOAuth(
            OAuthProvider.github,
            redirectTo: kIsWeb ? null : 'my.scheme://my-host', // Optionally set the redirect link to bring back the user via deeplink.
            authScreenLaunchMode:
                kIsWeb ? LaunchMode.platformDefault : LaunchMode.externalApplication, // Launch the auth screen in a new webview on mobile.
          );
          ```
      - id: sign-in-using-a-third-party-provider-with-redirect
        name: With `redirectTo`
        description: |
          Specify the redirect link to bring back the user via deeplink.
          Note that `redirectTo` should be null for Flutter Web.
        code: |
          ```dart
          await supabase.auth.signInWithOAuth(
            OAuthProvider.github,
            redirectTo: kIsWeb ? null : 'io.supabase.flutter://reset-callback/',
          );
          ```
      - id: sign-in-with-scopes
        name: With scopes
        description: |
          If you need additional data from an OAuth provider, you can include a space-separated list of scopes in your request to get back an OAuth provider token.
          You may also need to specify the scopes in the provider's OAuth app settings, depending on the provider.
        code: |
          ```dart
          await supabase.auth.signInWithOAuth(
            OAuthProvider.github,
            scopes: 'repo gist notifications'
          );
          ...
          // after user comes back from signin flow

          final Session? session = supabase.auth.currentSession;
          final String? oAuthToken = session?.providerToken;
          ```
  - id: sign-in-with-sso
    title: 'signInWithSSO()'
    notes: |
      - Before you can call this method you need to [establish a connection](/docs/guides/auth/sso/auth-sso-saml#managing-saml-20-connections) to an identity provider. Use the [CLI commands](/docs/reference/cli/supabase-sso) to do this.
      - If you've associated an email domain to the identity provider, you can use the `domain` property to start a sign-in flow.
      - In case you need to use a different way to start the authentication flow with an identity provider, you can use the `providerId` property. For example:
          - Mapping specific user email addresses with an identity provider.
          - Using different hints to identify the correct identity provider, like a company-specific page, IP address or other tracking information.
    params:
      - name: providerId
        isOptional: true
        type: String
        description: The ID of the SSO provider to use for signing in.
      - name: domain
        isOptional: true
        type: String
        description: The email domain to use for signing in.
      - name: redirectTo
        isOptional: true
        type: String
        description: The URL to redirect the user to after they sign in with the third-party provider.
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification.
      - name: launchMode
        isOptional: true
        type: LaunchMode
        description: The launch mode for the auth screen. Defaults to `LaunchMode.platformDefault`.
    examples:
      - id: sign-in-with-domain
        name: Sign in with email domain
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.signInWithSSO(
            domain: 'company.com',
          );
          ```
      - id: sign-in-with-provider-uuid
        name: Sign in with provider UUID
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.signInWithSSO(
            providerId: '21648a9d-8d5a-4555-a9d1-d6375dc14e92',
          );
          ```
  - id: sign-out
    title: 'signOut()'
    description: |
      Signs out the current user, if there is a logged in user.
    notes: |
      - In order to use the `signOut()` method, the user needs to be signed in first.
    params:
      - name: scope
        isOptional: true
        type: SignOutScope
        description: Whether to sign out from all devices or just the current device. Defaults to `SignOutScope.local`.
    examples:
      - id: sign-out
        name: Sign out
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.signOut();
          ```
  - id: verify-otp
    title: 'verifyOtp()'
    notes: |
      - The `verifyOtp` method takes in different verification types. If a phone number is used, the type can either be `sms` or `phone_change`. If an email address is used, the type can be one of the following: `signup`, `magiclink`, `recovery`, `invite` or `email_change`.
      - The verification type used should be determined based on the corresponding auth method called before `verifyOtp` to sign up or sign in a user.
    params:
      - name: token
        isOptional: false
        type: String
        description: The token that user was sent to their email or mobile phone
      - name: type
        isOptional: false
        type: OtpType
        description: Type of the OTP to verify
      - name: email
        isOptional: true
        type: String
        description: Email address that the OTP was sent to
      - name: phone
        isOptional: true
        type: String
        description: Phone number that the OTP was sent to
      - name: redirectTo
        isOptional: true
        type: String
        description: URI to redirect the user to after the OTP is verified
      - name: captchaToken
        isOptional: true
        type: String
        description: The captcha token to be used for captcha verification
      - name: tokenHash
        isOptional: true
        type: String
        description: Token used in an email link
    examples:
      - id: verify-signup-one-time-password(otp)
        name: Verify Signup One-Time Password (OTP)
        isSpotlight: false
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.verifyOTP(
            type: OtpType.signup,
            token: token,
            phone: '+***********',
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
        response: |
          ```json
          AuthResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                emailConfirmedAt: '2024-01-01T00:00:00Z',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {
                  'provider': 'email',
                  'providers': ['email']
                },
                userMetadata: {},
                identities: [
                  UserIdentity(
                    identityId: '********-2222-2222-2222-********2222',
                    id: '11111111-1111-1111-1111-111111111111',
                    userId: '11111111-1111-1111-1111-111111111111',
                    identityData: {
                      'email': '<EMAIL>',
                      'email_verified': false,
                      'phone_verified': false,
                      'sub': '11111111-1111-1111-1111-111111111111'
                    },
                    provider: 'email',
                    lastSignInAt: '2024-01-01T00:00:00Z',
                    createdAt: '2024-01-01T00:00:00Z',
                    updatedAt: '2024-01-01T00:00:00Z',
                  )
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              ),
            ),
          );
          ```
      - id: verify-sms-one-time-password(otp)
        name: Verify SMS One-Time Password (OTP)
        isSpotlight: true
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.verifyOTP(
            type: OtpType.sms,
            token: '111111',
            phone: '+***********',
          );
          final Session? session = res.session;
          final User? user = res.user;
          ```
  - id: get-session
    title: 'currentSession'
    description: |
      Returns the session data, if there is an active session.
    examples:
      - id: get-the-session-data
        name: Get the session data
        isSpotlight: true
        code: |
          ```dart
          final Session? session = supabase.auth.currentSession;
          ```
        response: |
          ```json
          Session(
            accessToken: '<ACCESS_TOKEN>',
            tokenType: 'bearer',
            expiresIn: 3600,
            refreshToken: '<REFRESH_TOKEN>',
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
          );
          ```
  - id: get-user
    title: 'currentUser'
    description: |
      Returns the user data, if there is a logged in user.
    examples:
      - name: Get the logged in user
        isSpotlight: true
        code: |
          ```dart
          final User? user = supabase.auth.currentUser;
          ```
        response: |
          ```json
          User(
            id: '11111111-1111-1111-1111-111111111111',
            aud: 'authenticated',
            role: 'authenticated',
            email: '<EMAIL>',
            emailConfirmedAt: '2024-01-01T00:00:00Z',
            phone: '',
            lastSignInAt: '2024-01-01T00:00:00Z',
            appMetadata: {
              'provider': 'email',
              'providers': ['email']
            },
            userMetadata: {},
            identities: [
              UserIdentity(
                identityId: '********-2222-2222-2222-********2222',
                id: '11111111-1111-1111-1111-111111111111',
                userId: '11111111-1111-1111-1111-111111111111',
                identityData: {
                  'email': '<EMAIL>',
                  'email_verified': false,
                  'phone_verified': false,
                  'sub': '11111111-1111-1111-1111-111111111111'
                },
                provider: 'email',
                lastSignInAt: '2024-01-01T00:00:00Z',
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              )
            ],
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          );
          ```
  - id: update-user
    title: 'updateUser()'
    description: |
      Updates user data for a logged in user.
    notes: |
      - In order to use the `updateUser()` method, the user needs to be signed in first.
      - By default, email updates sends a confirmation link to both the user's current and new email.
      To only send a confirmation link to the user's new email, disable **Secure email change** in your project's [email auth provider settings](https://supabase.com/dashboard/project/_/auth/providers).
    params:
      - name: attributes
        isOptional: false
        type: UserAttributes
        description: Attributes to update for the user.
        subContent:
          - name: email
            isOptional: true
            type: String
            description: The new email address for the user.
          - name: phone
            isOptional: true
            type: String
            description: The new phone number for the user.
          - name: password
            isOptional: true
            type: String
            description: The new password for the user.
          - name: data
            isOptional: true
            type: Map<String, dynamic>
            description: The user's metadata to be stored in the user's object.
          - name: nonce
            isOptional: true
            type: String
            description: The nonce sent for reauthentication if the user's password is to be updated.
      - name: emailRedirectTo
        isOptional: true
        type: String
        description: The URI to redirect the user to after the email is updated.
    examples:
      - id: update-the-email-for-an-authenticated-user
        name: Update the email for an authenticated user
        description: Sends a "Confirm Email Change" email to the new email address.
        isSpotlight: true
        code: |
          ```dart
          final UserResponse res = await supabase.auth.updateUser(
            UserAttributes(
              email: '<EMAIL>',
            ),
          );
          final User? updatedUser = res.user;
          ```
        response: |
          ```json
          UserResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
          );
          ```
      - id: update-the-password-for-an-authenticated-user
        name: Update the password for an authenticated user
        isSpotlight: false
        code: |
          ```dart
          final UserResponse res = await supabase.auth.updateUser(
            UserAttributes(
              password: 'new password',
            ),
          );
          final User? updatedUser = res.user;
          ```
      - id: update-the-users-metadata
        name: Update the user's metadata
        isSpotlight: true
        code: |
          ```dart
          final UserResponse res = await supabase.auth.updateUser(
            UserAttributes(
              data: { 'hello': 'world' },
            ),
          );
          final User? updatedUser = res.user;
          ```
      - id: update-password-with-reauthentication
        name: Update the user's password with a nonce
        description: If "Secure password change" is enabled, updating the user's password requires a nonce. The nonce is sent to the user's email or phone number.
        isSpotlight: true
        code: |
          ```dart
          supabase.auth.updateUser(UserAttributes(
            email: '<EMAIL>',
            nonce: '123456',
          ));
          ```
  - id: get-user-identities
    title: 'getUserIdentities()'
    description: |
      Gets all the identities linked to a user.
    notes: |
      - The user needs to be signed in to call `getUserIdentities()`.
    examples:
      - id: get-user-identities
        name: Returns a list of identities linked to the user
        isSpotlight: true
        code: |
          ```dart
          final identities = await supabase.auth.getUserIdentities();
          ```
        response: |
          ```json
          [
            UserIdentity(
              identityId: '********-2222-2222-2222-********2222',
              id: '2024-01-01T00:00:00Z',
              userId: '2024-01-01T00:00:00Z',
              identityData: {
                'email': '<EMAIL>',
                'email_verified': false,
                'phone_verified': false,
                'sub': '11111111-1111-1111-1111-111111111111'
              },
              provider: 'email',
              lastSignInAt: '2024-01-01T00:00:00Z',
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            )
          ];
          ```
  - id: link-identity
    title: 'linkIdentity()'
    description: |
      Links an oauth identity to an existing user. This method supports the PKCE flow.
    notes: |
      - The **Enable Manual Linking** option must be enabled from your [project's authentication settings](/dashboard/project/_/settings/auth).
      - The user needs to be signed in to call `linkIdentity()`.
      - If the candidate identity is already linked to the existing user or another user, `linkIdentity()` will fail.
    params:
      - name: provider
        isOptional: false
        type: OAuthProvider
        description: The provider to link the identity to.
      - name: redirectTo
        isOptional: true
        type: String
        description: The URL to redirect the user to after they sign in with the third-party provider.
      - name: scopes
        isOptional: true
        type: String
        description: A list of scopes to request from the third-party provider.
      - name: authScreenLaunchMode
        isOptional: true
        type: LaunchMode
        description: The launch mode for the auth screen. Defaults to `LaunchMode.platformDefault`.
      - name: queryParams
        isOptional: true
        type: Map<String, String>
        description: Additional query parameters to be passed to the OAuth flow.
    examples:
      - id: link-identity
        name: Link an identity to a user
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.linkIdentity(OAuthProvider.google);
          ```
  - id: unlink-identity
    title: 'unlinkIdentity()'
    description: |
      Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.
    params:
      - name: identity
        isOptional: false
        type: UserIdentity
        description: The user identity to unlink.
    notes: |
      - The **Enable Manual Linking** option must be enabled from your [project's authentication settings](/dashboard/project/_/settings/auth).
      - The user needs to be signed in to call `unlinkIdentity()`.
      - The user must have at least 2 identities in order to unlink an identity.
      - The identity to be unlinked must belong to the user.
    examples:
      - id: unlink-identity
        name: Unlink an identity
        isSpotlight: true
        code: |
          ```dart
          // retrieve all identites linked to a user
          final identities = await supabase.auth.getUserIdentities();

          // find the google identity
          final googleIdentity = identities.firstWhere(
            (element) => element.provider == 'google',
          );

          // unlink the google identity
          await supabase.auth.unlinkIdentity(googleIdentity);
          ```
  - id: send-password-reauthentication
    title: 'reauthenticate()'
    notes: |
      - This method is used together with `updateUser()` when a user's password needs to be updated.
      - This method sends a nonce to the user's email. If the user doesn't have a confirmed email address, the method sends the nonce to the user's confirmed phone number instead.
    examples:
      - id: send-reauthentication-nonce
        name: Send reauthentication nonce
        description: Sends a reauthentication nonce to the user's email or phone number.
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.reauthenticate();
          ```
  - id: resend-email-or-phone-otps
    title: 'resend()'
    notes: |
      - Resends a signup confirmation, email change, or phone change email to the user.
      - Passwordless sign-ins can be resent by calling the `signInWithOtp()` method again.
      - Password recovery emails can be resent by calling the `resetPasswordForEmail()` method again.
      - This method only resend an email or phone OTP to the user if an initial signup, email change, or phone change request was made.
    examples:
      - id: resend-email-signup-confirmation
        name: Resend an email signup confirmation
        description: Resends the email signup confirmation to the user
        isSpotlight: true
        code: |
          ```dart
          final ResendResponse res = await supabase.auth.resend(
            type: OtpType.signup,
            email: '<EMAIL>',
          );
          ```
  - id: on-auth-state-change
    title: 'onAuthStateChange()'
    description: |
      Receive a notification every time an auth event happens.
    notes: |
      - Types of auth events: `AuthChangeEvent.passwordRecovery`, `AuthChangeEvent.signedIn`, `AuthChangeEvent.signedOut`, `AuthChangeEvent.tokenRefreshed`, `AuthChangeEvent.userUpdated`and `AuthChangeEvent.userDeleted`
    examples:
      - id: listen-to-auth-changes
        name: Listen to auth changes
        isSpotlight: true
        code: |
          ```dart
          final authSubscription = supabase.auth.onAuthStateChange.listen((data) {
            final AuthChangeEvent event = data.event;
            final Session? session = data.session;

            print('event: $event, session: $session');

            switch (event) {
              case AuthChangeEvent.initialSession:
              // handle initial session
              case AuthChangeEvent.signedIn:
              // handle signed in
              case AuthChangeEvent.signedOut:
              // handle signed out
              case AuthChangeEvent.passwordRecovery:
              // handle password recovery
              case AuthChangeEvent.tokenRefreshed:
              // handle token refreshed
              case AuthChangeEvent.userUpdated:
              // handle user updated
              case AuthChangeEvent.userDeleted:
              // handle user deleted
              case AuthChangeEvent.mfaChallengeVerified:
              // handle mfa challenge verified
            }
          });
          ```
      - id: listen-to-a-specific-event
        name: Listen to a specific event
        code: |
          ```dart
          final authSubscription = supabase.auth.onAuthStateChange.listen((data) {
            final AuthChangeEvent event = data.event;
            if (event == AuthChangeEvent.signedIn) {
              // handle signIn
            }
          });
          ```
      - id: unsubscribe-from-auth-subscription
        name: Unsubscribe from auth subscription
        code: |
          ```dart
          final authSubscription = supabase.auth.onAuthStateChange.listen((data) {});

          authSubscription.cancel();
          ```
  - id: auth-reset-password-for-email
    title: 'resetPasswordForEmail()'
    description: |
      Sends a reset request to an email address.
    notes: |
      Sends a password reset request to an email address. When the user clicks the reset link in the email they are redirected back to your application. Prompt the user for a new password and call auth.updateUser():

      ```dart
      await supabase.auth.resetPasswordForEmail(
        '<EMAIL>',
        redirectTo: kIsWeb ? null : 'io.supabase.flutter://reset-callback/',
      );
      ```
    examples:
      - id: reset-password
        name: Reset password for Flutter
        isSpotlight: true
        code: |
          `redirectTo` is used to open the app via deeplink when user opens the password reset email.
          ```dart
          await supabase.auth.resetPasswordForEmail(
            '<EMAIL>',
            redirectTo: kIsWeb ? null : 'io.supabase.flutter://reset-callback/',
          );
          ```
  - id: set-session
    title: 'setSession()'
    notes: |
      - `setSession()` takes in a refresh token and uses it to get a new session.
      - The refresh token can only be used once to obtain a new session.
      - [Refresh token rotation](/docs/guides/cli/config#auth.enable_refresh_token_rotation) is enabled by default on all projects to guard against replay attacks.
      - You can configure the [`REFRESH_TOKEN_REUSE_INTERVAL`](/docs/guides/cli/config#auth.refresh_token_reuse_interval) which provides a short window in which the same refresh token can be used multiple times in the event of concurrency or offline issues.
    params:
      - name: refreshToken
        isOptional: false
        type: String
        description: Refresh token to use to get a new session.
    examples:
      - id: refresh-the-session
        name: Refresh the session
        description: Sets the session data from refresh_token and returns current session or an error if the refresh_token is invalid.
        isSpotlight: true
        code: |
          ```dart
          final refreshToken = supabase.currentSession?.refreshToken ?? '';
          final AuthResponse response = await supabase.auth.setSession(refreshToken);

          final session = res.session;
          ```
        response: |
          ```json
          AuthResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                emailConfirmedAt: '2024-01-01T00:00:00Z',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {
                  'provider': 'email',
                  'providers': ['email']
                },
                userMetadata: {},
                identities: [
                  UserIdentity(
                    identityId: '********-2222-2222-2222-********2222',
                    id: '11111111-1111-1111-1111-111111111111',
                    userId: '11111111-1111-1111-1111-111111111111',
                    identityData: {
                      'email': '<EMAIL>',
                      'email_verified': false,
                      'phone_verified': false,
                      'sub': '11111111-1111-1111-1111-111111111111'
                    },
                    provider: 'email',
                    lastSignInAt: '2024-01-01T00:00:00Z',
                    createdAt: '2024-01-01T00:00:00Z',
                    updatedAt: '2024-01-01T00:00:00Z',
                  )
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              ),
            ),
          );
          ```
  - id: refresh-session
    title: 'refreshSession()'
    notes: |
      - This method will refresh and return a new session whether the current one is expired or not.
    examples:
      - id: refresh-session-using-the-current-session
        name: Refresh session using the current session
        isSpotlight: true
        code: |
          ```dart
          final AuthResponse res = await supabase.auth.refreshSession();
          final session = res.session;
          ```
        response: |
          ```json
          AuthResponse(
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
            session: Session(
              accessToken: '<ACCESS_TOKEN>',
              tokenType: 'bearer',
              expiresIn: 3600,
              refreshToken: '<REFRESH_TOKEN>',
              user: const User(
                id: '11111111-1111-1111-1111-111111111111',
                aud: 'authenticated',
                role: 'authenticated',
                email: '<EMAIL>',
                emailConfirmedAt: '2024-01-01T00:00:00Z',
                phone: '',
                lastSignInAt: '2024-01-01T00:00:00Z',
                appMetadata: {
                  'provider': 'email',
                  'providers': ['email']
                },
                userMetadata: {},
                identities: [
                  UserIdentity(
                    identityId: '********-2222-2222-2222-********2222',
                    id: '11111111-1111-1111-1111-111111111111',
                    userId: '11111111-1111-1111-1111-111111111111',
                    identityData: {
                      'email': '<EMAIL>',
                      'email_verified': false,
                      'phone_verified': false,
                      'sub': '11111111-1111-1111-1111-111111111111'
                    },
                    provider: 'email',
                    lastSignInAt: '2024-01-01T00:00:00Z',
                    createdAt: '2024-01-01T00:00:00Z',
                    updatedAt: '2024-01-01T00:00:00Z',
                  )
                ],
                createdAt: '2024-01-01T00:00:00Z',
                updatedAt: '2024-01-01T00:00:00Z',
              ),
            ),
          );
          ```
  - id: auth-mfa-api
    title: 'Overview'
    notes: |
      This section contains methods commonly used for Multi-Factor Authentication (MFA) and are invoked behind the `supabase.auth.mfa` namespace.

      Currently, we only support time-based one-time password (TOTP) as the 2nd factor. We don't support recovery codes but we allow users to enroll more than 1 TOTP factor, with an upper limit of 10.

      Having a 2nd TOTP factor for recovery means the user doesn't have to store their recovery codes. It also reduces the attack surface since the recovery factor is usually time-limited and not a single static code.

      Learn more about implementing MFA on your application on our guide [here](https://supabase.com/docs/guides/auth/auth-mfa#overview).
  - id: mfa-enroll
    title: 'mfa.enroll()'
    notes: |
      Starts the enrollment process for a new Multi-Factor Authentication (MFA) factor. This method creates a new `unverified` factor.
      To verify a factor, present the QR code or secret to the user and ask them to add it to their authenticator app.
      The user has to enter the code from their authenticator app to verify it.
      - Currently, `totp` is the only supported `factorType`. The returned `id` should be used to create a challenge.
      - To create a challenge, see [`mfa.challenge()`](/docs/reference/dart/auth-mfa-challenge).
      - To verify a challenge, see [`mfa.verify()`](/docs/reference/dart/auth-mfa-verify).
      - To create and verify a challenge in a single step, see [`mfa.challengeAndVerify()`](/docs/reference/dart/auth-mfa-challengeandverify).
    params:
      - name: factorType
        isOptional: true
        type: String
        description: Type of factor being enrolled.
      - name: issuer
        isOptional: true
        type: String
        description: Domain which the user is enrolled with.
      - name: friendlyName
        isOptional: true
        type: String
        description: Human readable name assigned to the factor.
    examples:
      - id: enroll-totp-factor
        name: Enroll a time-based, one-time password (TOTP) factor
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.mfa.enroll(factorType: FactorType.totp);

          final qrCodeUrl = res.totp.qrCode;
          ```
        response: |
          ```json
          AuthMFAEnrollResponse(
            id: '<ID>',
            type: FactorType.totp,
            totp: TOTPEnrollment(
              qrCode: '<QR_CODE_AS_SVG_DATA>',
              secret: '<SECRET>',
              uri: '<URI>',
            ),
          );
          ```
  - id: mfa-challenge
    title: 'mfa.challenge()'
    notes: |
      Prepares a challenge used to verify that a user has access to a MFA factor.
      - An [enrolled factor](/docs/reference/dart/auth-mfa-enroll) is required before creating a challenge.
      - To verify a challenge, see [`mfa.verify()`](/docs/reference/dart/auth-mfa-verify).
    params:
      - name: factorId
        isOptional: false
        type: String
        description: System assigned identifier for authenticator device as returned by enroll
    examples:
      - id: create-mfa-challenge
        name: Create a challenge for a factor
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.mfa.challenge(
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
          );
          ```
        response: |
          ```json
          AuthMFAChallengeResponse(
            id: '<ID>',
            expiresAt: DateTime.fromMillisecondsSinceEpoch(1700000000),
          );
          ```
  - id: mfa-verify
    title: 'mfa.verify()'
    notes: |
      Verifies a code against a challenge. The verification code is provided by the user by entering a code seen in their authenticator app.
      - To verify a challenge, please [create a challenge](/docs/reference/dart/auth-mfa-challenge) first.
    params:
      - name: factorId
        isOptional: false
        type: String
        description: System assigned identifier for authenticator device as returned by enroll
      - name: challengeId
        isOptional: false
        type: String
        description: The ID of the challenge to verify
      - name: code
        isOptional: false
        type: String
        description: The verification code on the user's authenticator app
    examples:
      - id: verify-challenge
        name: Verify a challenge for a factor
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.mfa.verify(
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
            challengeId: '4034ae6f-a8ce-4fb5-8ee5-69a5863a7c15',
            code: '123456',
          );
          ```
        response: |
          ```json
          AuthMFAVerifyResponse(
            accessToken: '<ACCESS_TOKEN>',
            tokenType: 'Bearer',
            expiresIn: Duration(seconds: 3600),
            refreshToken: '<REFRESH_TOKEN>',
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
              isAnonymous: false,
              factors: [
                Factor(
                  id: '<ID>',
                  friendlyName: 'Important Auth App',
                  factorType: FactorType.totp,
                  status: 'verified',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z'
                )
              ]
            )
          );
          ```
  - id: mfa-challenge-and-verify
    title: 'mfa.challengeAndVerify()'
    notes: |
      Helper method which creates a challenge and immediately uses the given code to verify against it thereafter. The verification code is provided by the user by entering a code seen in their authenticator app.
      - An [enrolled factor](/docs/reference/dart/auth-mfa-enroll) is required before invoking `challengeAndVerify()`.
      - Executes [`mfa.challenge()`](/docs/reference/dart/auth-mfa-challenge) and [`mfa.verify()`](/docs/reference/dart/auth-mfa-verify) in a single step.
    params:
      - name: factorId
        isOptional: false
        type: String
        description: System assigned identifier for authenticator device as returned by enroll
      - name: code
        isOptional: false
        type: String
        description: The verification code on the user's authenticator app
    examples:
      - id: challenge-and-verify
        name: Create and verify a challenge for a factor
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.mfa.challengeAndVerify(
            factorId: '34e770dd-9ff9-416c-87fa-43b31d7ef225',
            code: '123456',
          );
          ```
        response: |
          ```json
          AuthMFAVerifyResponse(
            accessToken: '<ACCESS_TOKEN>',
            tokenType: 'Bearer',
            expiresIn: Duration(seconds: 3600),
            refreshToken: '<REFRESH_TOKEN>',
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                ),
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
              isAnonymous: false,
              factors: [
                Factor(
                  id: '<ID>',
                  friendlyName: 'Important Auth App',
                  factorType: FactorType.totp,
                  status: 'verified',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z'
                )
              ]
            )
          );
          ```
  - id: mfa-unenroll
    title: 'mfa.unenroll()'
    notes: |
      Unenroll removes a MFA factor.
      A user has to have an `aal2` authenticator level in order to unenroll a `verified` factor.
    params:
      - name: factorId
        isOptional: false
        type: String
        description: System assigned identifier for authenticator device as returned by enroll
    examples:
      - id: unenroll-a-factor
        name: Unenroll a factor
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.mfa.unenroll(
            '34e770dd-9ff9-416c-87fa-43b31d7ef225',
          );
          ```
        response: |
          ```json
          AuthMFAUnenrollResponse(
            id: '<FACTOR_ID>',
          );
          ```
  - id: mfa-get-authenticator-assurance-level
    title: 'mfa.getAuthenticatorAssuranceLevel()'
    notes: |
      Returns the Authenticator Assurance Level (AAL) for the active session.
      - Authenticator Assurance Level (AAL) is the measure of the strength of an authentication mechanism.
      - In Supabase, having an AAL of `aal1` means the user has signed in with their first factor, such as email, password, or OAuth sign-in. An AAL of `aal2` means the user has also signed in with their second factor, such as a time-based, one-time-password (TOTP).
      - If the user has a verified factor, the `nextLevel` field returns `aal2`. Otherwise, it returns `aal1`.
    examples:
      - id: get-aal
        name: Get the AAL details of a session
        isSpotlight: true
        code: |
          ```dart
          final res = supabase.auth.mfa.getAuthenticatorAssuranceLevel();
          final currentLevel = res.currentLevel;
          final nextLevel = res.nextLevel;
          final currentAuthenticationMethods = res.currentAuthenticationMethods;
          ```
        response: |
          ```json
          AuthMFAGetAuthenticatorAssuranceLevelResponse(
            currentLevel: AuthenticatorAssuranceLevels.aal1,
            nextLevel: AuthenticatorAssuranceLevels.aal2,
            currentAuthenticationMethods: [
              AMREntry(
                method: AMRMethod.password,
                timestamp: DateTime.fromMillisecondsSinceEpoch(1700000000000),
              )
            ]
          }
          ```
  - id: admin-api
    title: 'Overview'
    notes: |
      - Any method under the `supabase.auth.admin` namespace requires a `service_role` key.
      - These methods are considered admin methods and should be called on a trusted server. Never expose your `service_role` key in the Flutter app.
    examples:
      - id: create-auth-admin-client
        name: Create server-side auth client
        isSpotlight: true
        code: |
          ```dart
          final supabase = SupabaseClient(supabaseUrl, serviceRoleKey);
          ```
  - id: get-user-by-id
    title: 'getUserById()'
    notes: |
      Get user by id.
      - Fetches the user object from the database based on the user's id.
      - The `getUserById()` method requires the user's id which maps to the `auth.users.id` column.
    params:
      - name: uid
        isOptional: false
        type: String
        description: User ID of the user to fetch.
    examples:
      - id: fetch-the-user-object-using-the-access-token-jwt
        name: Fetch the user object using the access_token jwt
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.admin.getUserById(userId);
          final user = res.user;
          ```
        response: |
          ```json
          UserResponse(
            user: const User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
          );
          ```
  - id: list-users
    title: 'listUsers()'
    notes: |
      Get a list of users.
      - Defaults to return 50 users per page.
    params:
      - name: page
        isOptional: true
        type: int
        description: What page of users to return.
      - name: page
        isOptional: true
        type: int
        description: How many users to be returned per page. Defaults to 50.
    examples:
      - id: get-a-full-list-of-users
        name: Get a page of users
        isSpotlight: true
        code: |
          ```dart
          // Returns the first 50 users.
          final List<User> users = await supabase.auth.admin.listUsers();
          ```
        response: |
          ```json
          [
            User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            )
          ]
          ```
      - id: get-paginated-list-of-users
        name: Paginated list of users
        isSpotlight: false
        code: |
          ```dart
          // Returns the 101th - 200th users.
          final List<User> res = await supabase.auth.admin.listUsers(
            page: 2,
            perPage: 100,
          );
          ```
  - id: create-user
    title: 'createUser()'
    notes: |
      Creates a new user.
      - To confirm the user's email address or phone number, set `email_confirm` or `phone_confirm` to true. Both arguments default to false.
      - `createUser()` will not send a confirmation email to the user. You can use [`inviteUserByEmail()`](/docs/reference/dart/auth-admin-inviteuserbyemail) if you want to send them an email invite instead.
      - If you are sure that the created user's email or phone number is legitimate and verified, you can set the `email_confirm` or `phone_confirm` param to `true`.
    params:
      - name: attributes
        isOptional: false
        type: AdminUserAttributes
        description: Attributes to create the user with.
        subContent:
          - name: email
            isOptional: false
            type: String
            description: The email address of the user.
          - name: password
            isOptional: false
            type: String
            description: The password of the user.
          - name: phone
            isOptional: true
            type: String
            description: The phone number of the user.
          - name: userMetadata
            isOptional: true
            type: Map<String, dynamic>
            description: A custom data object to store the user's metadata. This maps to the `auth.users.user_metadata` column.
          - name: appMetadata
            isOptional: true
            type: Map<String, dynamic>
            description: A custom data object to store the user's application specific metadata. This maps to the `auth.users.app_metadata` column.
          - name: emailConfirm
            isOptional: true
            type: bool
            description: Whether to confirm the user's email address.
          - name: phoneConfirm
            isOptional: true
            type: bool
            description: Whether to confirm the user's phone number.
          - name: banDuration
            isOptional: true
            type: String
            description: Determines how long a user is banned for. Some possible durations include '300ms', '2h45m'.
    examples:
      - id: create-a-new-user-with-custom-user-metadata
        name: With custom user metadata
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.admin.createUser(AdminUserAttributes(
            email: '<EMAIL>',
            password: 'password',
            userMetadata: {'name': 'Yoda'},
          ));
          ```
        response: |
          ```json
          UserResponse(
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {'name': 'Yoda'},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
          );
          ```
      - id: auto-confirm-the-users-email
        name: Auto-confirm the user's email
        code: |
          ```dart
          final res = await supabase.auth.admin.createUser(AdminUserAttributes(
            email: '<EMAIL>',
            emailConfirm: true,
          ));
          ```
      - id: auto-confirm-the-users-phone-number
        name: Auto-confirm the user's phone number
        code: |
          ```dart
          final res = await supabase.auth.admin.createUser(AdminUserAttributes(
            phone: '**********',
            phoneConfirm: true,
          ));
          ```
  - id: delete-user
    title: 'deleteUser()'
    notes: |
      Delete a user.
      - The `deleteUser()` method requires the user's ID, which maps to the `auth.users.id` column.
    params:
      - name: id
        isOptional: false
        type: String
        description: ID of the user to be deleted.
    examples:
      - id: removes-a-user
        name: Removes a user
        isSpotlight: true
        code: |
          ```dart
          await supabase.auth.admin
              .deleteUser('715ed5db-f090-4b8c-a067-640ecee36aa0');
          ```
  - id: invite-user-by-email
    title: 'inviteUserByEmail()'
    notes: |
      Sends an invite link to the user's email address.
    params:
      - name: email
        isOptional: false
        type: String
        description: Email address of the user to invite.
      - name: redirectTo
        isOptional: true
        type: String
        description: URI to redirect the user to after they open the invite link.
      - name: data
        isOptional: true
        type: Map<String, dynamic>
        description: A custom data object to store the user's metadata. This maps to the `auth.users.user_metadata` column.
    examples:
      - id: invite-a-user
        name: Invite a user
        isSpotlight: true
        code: |
          ```dart
          final UserResponse res = await supabase.auth.admin
              .inviteUserByEmail('<EMAIL>');
          final User? user = res.user;
          ```
        response: |
          ```json
          UserResponse(
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            ),
          );
          ```
  - id: generate-link
    title: 'generateLink()'
    notes: |
      Generates email links and OTPs to be sent via a custom email provider.
      - The following types can be passed into `generateLink()`: `signup`, `magiclink`, `invite`, `recovery`, `emailChangeCurrent`, `emailChangeNew`, `phoneChange`.
      - `generateLink()` only generates the email link for `email_change_email` if the "Secure email change" setting is enabled under the "Email" provider in your Supabase project.
      - `generateLink()` handles the creation of the user for `signup`, `invite` and `magiclink`.
    params:
      - name: type
        isOptional: false
        type: GenerateLinkType
        description: The type of invite link to generate.
      - name: email
        isOptional: false
        type: String
        description: Email address of the user to invite.
      - name: password
        isOptional: true
        type: String
        description: Password for the user. Required for `signup` type.
      - name: redirectTo
        isOptional: true
        type: String
        description: URI to redirect the user to after they open the invite link.
      - name: data
        isOptional: true
        type: Map<String, dynamic>
        description: A custom data object to store the user's metadata. This maps to the `auth.users.user_metadata` column.
    examples:
      - id: generate-a-signup-link
        name: Generate a signup link
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.auth.admin.generateLink(
            type: GenerateLinkType.signup,
            email: '<EMAIL>',
            password: 'secret',
          );
          final actionLink = res.properties.actionLink;
          ```
        response: |
          ```json
          GenerateLinkResponse(
            properties: GenerateLinkProperties(
              actionLink: '<LINK_TO_SEND_TO_USER>',
              emailOtp: '999999',
              hashedToken: '<HASHED_TOKEN',
              redirectTo: '<REDIRECT_URL>',
              verificationType: GenerateLinkType.signup
            ),
            user: User(
              id: '11111111-1111-1111-1111-111111111111',
              aud: 'authenticated',
              role: 'authenticated',
              email: '<EMAIL>',
              emailConfirmedAt: '2024-01-01T00:00:00Z',
              phone: '',
              lastSignInAt: '2024-01-01T00:00:00Z',
              appMetadata: {
                'provider': 'email',
                'providers': ['email']
              },
              userMetadata: {},
              identities: [
                UserIdentity(
                  identityId: '********-2222-2222-2222-********2222',
                  id: '11111111-1111-1111-1111-111111111111',
                  userId: '11111111-1111-1111-1111-111111111111',
                  identityData: {
                    'email': '<EMAIL>',
                    'email_verified': false,
                    'phone_verified': false,
                    'sub': '11111111-1111-1111-1111-111111111111'
                  },
                  provider: 'email',
                  lastSignInAt: '2024-01-01T00:00:00Z',
                  createdAt: '2024-01-01T00:00:00Z',
                  updatedAt: '2024-01-01T00:00:00Z',
                )
              ],
              createdAt: '2024-01-01T00:00:00Z',
              updatedAt: '2024-01-01T00:00:00Z',
            )
          )
          ```
  - id: update-user-by-id
    title: 'updateUserById()'
    params:
      - name: uid
        isOptional: false
        type: GenerateLinkType
        description: User ID of the user to update.
      - name: attributes
        isOptional: false
        type: AdminUserAttributes
        description: Attributes to update for the user.
        subContent:
          - name: email
            isOptional: false
            type: String
            description: The email address of the user.
          - name: password
            isOptional: false
            type: String
            description: The password of the user.
          - name: phone
            isOptional: true
            type: String
            description: The phone number of the user.
          - name: userMetadata
            isOptional: true
            type: Map<String, dynamic>
            description: A custom data object to store the user's metadata. This maps to the `auth.users.user_metadata` column.
          - name: appMetadata
            isOptional: true
            type: Map<String, dynamic>
            description: A custom data object to store the user's application specific metadata. This maps to the `auth.users.app_metadata` column.
          - name: emailConfirm
            isOptional: true
            type: bool
            description: Whether to confirm the user's email address.
          - name: phoneConfirm
            isOptional: true
            type: bool
            description: Whether to confirm the user's phone number.
          - name: banDuration
            isOptional: true
            type: String
            description: Determines how long a user is banned for. Some possible durations include '300ms', '2h45m'.
    examples:
      - id: updates-a-users-email
        name: Updates a user's email
        isSpotlight: false
        code: |
          ```dart
          await supabase.auth.admin.updateUserById(
            '6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4',
            attributes: AdminUserAttributes(
              email: '<EMAIL>',
            ),
          );
          ```
  - id: invoke
    title: 'invoke()'
    description: |
      Invokes a Supabase Function. See the [guide](/docs/guides/functions) for details on writing Functions.
    notes: |
      - Requires an Authorization header.
      - Invoke params generally match the [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) spec.
    params:
      - name: functionName
        isOptional: false
        type: String
        description: The name of the function to invoke.
      - name: headers
        isOptional: true
        type: Map<String, String>
        description: Custom headers to send with the request.
      - name: body
        isOptional: true
        type: Map<String, String>
        description: The body of the request.
      - name: method
        isOptional: true
        type: HttpMethod
        description: HTTP method of the request. Defaults to POST.
    examples:
      - id: basic-invocation
        name: Basic invocation.
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.functions.invoke('hello', body: {'foo': 'baa'});
          final data = res.data;
          ```
      - id: specifying-response-type
        name: Specifying response type.
        description: |
          By default, `invoke()` will parse the response as JSON. You can parse the response in the following formats: `json`, `blob`, `text`, and `arrayBuffer`.
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.functions.invoke(
            'hello',
            body: {'foo': 'baa'},
            responseType: ResponseType.text,
          );
          final data = res.data;
          ```
      - id: parsing-custom-headers
        name: Parsing custom headers.
        description: |
          Any `headers` will be passed through to the function. A common pattern is to pass a logged-in user's JWT token as an Authorization header.
        isSpotlight: true
        code: |
          ```dart
          final res = await supabase.functions.invoke(
            'hello',
            body: {'foo': 'baa'},
            headers: {
              'Authorization': 'Bearer ${supabase.auth.currentSession?.accessToken}'
            },
          );
          ```
  - id: select
    description: |
      Perform a SELECT query on the table or view.
    title: 'Fetch data: select()'
    notes: |
      - By default, Supabase projects will return a maximum of 1,000 rows. This setting can be changed in Project API Settings. It's recommended that you keep it low to limit the payload size of accidental or malicious requests. You can use `range()` queries to paginate through your data.
      - `select()` can be combined with [Filters](/docs/reference/dart/using-filters)
      - `select()` can be combined with [Modifiers](/docs/reference/dart/using-modifiers)
      - `apikey` is a reserved keyword if you're using the [Supabase Platform](/docs/guides/platform) and [should be avoided as a column name](https://github.com/supabase/supabase/issues/5465).
    params:
      - name: columns
        isOptional: true
        type: String
        description: The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`
    examples:
      - id: getting-your-data
        name: Getting your data
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'violin'
            },
            {
              'id': 2,
              'name': 'viola'
            },
            {
              'id': 3,
              'name': 'cello'
            },
          ]
          ```
      - id: selecting-specific-columns
        name: Selecting specific columns
        description: You can select specific fields from your tables.
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('''
              name
            ''');
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'name': 'violin'
            },
            {
              'name': 'viola'
            },
            {
              'name': 'cello'
            },
          ]
          ```
      - id: query-referenced-tables
        name: Query referenced tables
        description: If your database has relationships, you can query related tables too.
        code: |
          ```dart
          final data = await supabase
            .from('orchestral_sections')
            .select('''
              name,
              instruments ( name )
            ''');
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          [
            {
              'name': 'strings',
              'instruments': [
                {
                  'name': 'violin'
                },
              ]
            },
            {
              'name': 'woodwinds',
              'instruments': [
                {
                  'name': 'flute'
                },
              ]
            },
          ]
          ```
      - id: query-referenced-tables-through-a-join-table
        name: Query referenced tables through a join table
        code: |
          ```dart
          final data = await supabase
            .from('users')
            .select('''
              name,
              teams (
                name
              )
            ''');
            ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text
              );
            create table
              teams (
                id int8 primary key,
                name text
              );
            -- join table
            create table
              users_teams (
                user_id int8 not null references users,
                team_id int8 not null references teams,
                -- both foreign keys must be part of a composite primary key
                primary key (user_id, team_id)
              );

            insert into
              users (id, name)
            values
              (1, 'Kiran'),
              (2, 'Evan');
            insert into
              teams (id, name)
            values
              (1, 'Green'),
              (2, 'Blue');
            insert into
              users_teams (user_id, team_id)
            values
              (1, 1),
              (1, 2),
              (2, 2);
            ```
        response: |
          ```json
          [
            {
              'name': 'Kiran',
              'teams': [
                {
                  'name': 'Green'
                },
                {
                  'name': 'Blue'
                },
              ]
            },
            {
              'name': 'Evan',
              'teams': [
                {
                  'name': 'Blue'
                }
              ]
            }
          ]
          ```
        description: |
          If you're in a situation where your tables are **NOT** directly
          related, but instead are joined by a _join table_, you can still use
          the `select()` method to query the related data. The join table needs
          to have the foreign keys as part of its composite primary key.
        hideCodeBlock: true
      - id: query-the-same-referenced-table-multiple-times
        name: Query the same referenced table multiple times
        description: |
          Sometimes you will need to query the same referenced table twice. In
          this case, you can use the name of the joined column to identify which
          join you intend to use. For convenience, you can also give an alias
          for each column. This example queries a `messages` table that is
          joined to a `users` table via `sender_id` and `receiver_id`. Since you
          want the `name` for both sender and receiver, you can alias the
          columns as `to` and `from`.
        code: |
          ```dart
          final data = await supabase
            .from('messages')
            .select('''
              content,
              from:sender_id(name),
              to:receiver_id(name)
            ''');
          ```
        data:
          sql: |
            ```sql
             create table
             users (id int8 primary key, name text);

             create table
               messages (
                 sender_id int8 not null references users,
                 receiver_id int8 not null references users,
                 content text
               );

             insert into
               users (id, name)
             values
               (1, 'Kiran'),
               (2, 'Evan');

             insert into
               messages (sender_id, receiver_id, content)
             values
               (1, 2, '👋');
             ```
        response: |
          ```json
          [
            {
              'content': '👋',
              'from': {
                'name': 'Kiran'
              },
              'to': {
                'name': 'Evan'
              }
            }
          ]
          ```
      - id: filtering-through-referenced-tables
        name: Filtering through referenced tables
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('name, orchestral_sections(*)')
            .eq('orchestral_sections.name', 'percussion');
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          [
            {
              'name': 'flute',
              'orchestral_sections': null
            },
            {
              'name': 'violin',
              'orchestral_sections': null
            }
          ]
          ```
        description: |
          If the filter on a referenced table's column is not satisfied, the referenced
          table returns `[]` or `null` but the parent table is not filtered out.
          If you want to filter out the parent table rows, use the `!inner` hint
        hideCodeBlock: true
      - id: querying-with-count-option
        name: Querying with count option
        description: |
          You can get the number of rows by using the count option.
          Allowed values for count option are [CountOption.exact](https://postgrest.org/en/stable/api.html#exact-count), [CountOptionplanned](https://postgrest.org/en/stable/api.html#planned-count) and [CountOption.estimated](https://postgrest.org/en/stable/api.html#estimated-count).
        code: |
          ```dart
          final res = await supabase
              .from('instruments')
              .select('name')
              .count(CountOption.exact);

          final data = res.data;
          final count = res.count;
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          PostgrestResponse(
            data: [
              {
                'id': 1,
                'name': 'violin'
              },
              {
                'id': 2,
                'name': 'viola'
              },
              {
                'id': 3,
                'name': 'cello'
              }
            ],
            count: 3
          );
          ```
      - id: querying-json-data
        name: Querying JSON data
        description: |
          If you have data inside of a JSONB column, you can apply select
          and query filters to the data values. Postgres offers a
          [number of operators](https://www.postgresql.org/docs/current/functions-json.html)
          for querying JSON data. Also see
          [PostgREST docs](http://postgrest.org/en/v7.0.0/api.html#json-columns) for more details.
        code: |
          ```dart
          final data = await supabase
            .from('users')
            .select('''
              id, name,
              address->city
            ''');
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Frodo', '{"city":"Hobbiton"}');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'Frodo',
              'city': 'Hobbiton'
            }
          ]
          ```
      - id: querying-referenced-table-with-inner-join
        name: Querying referenced table with inner join
        code: |
          ```dart
          final data = await supabase
            .from('orchestral_sections')
            .select('name, instruments!inner(name)')
            .eq('orchestral_sections.name', 'strings')
            .limit(1);
          ```
        data:
          sql: |
            ```sql
            create table orchestral_sections (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text
            );

            create table instruments (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text,
              "section_id" "uuid" references public.orchestral_sections on delete cascade
            );

            with section as (
              insert into orchestral_sections (name)
              values ('strings') returning id
            )
            insert into instruments (name, section_id) values
            ('violin', (select id from section)),
            ('viola', (select id from section)),
            ('cello', (select id from section)),
            ('double bass', (select id from section));
            ```
        response: |
          ```json
          [
            {
              'name': 'violin',
              'orchestral_sections': {'name': 'strings'}
            }
          ]
          ```
        description: |
          If you don't want to return the referenced table contents, you can leave the parenthesis empty.
          Like `.select('name, books!inner()')`.
        hideCodeBlock: true
      - id: switching-schemas-per-query
        name: Switching schemas per query
        code: |
          ```dart
          final data = await supabase
            .schema('myschema')
            .from('mytable')
            .select();
          ```
        data:
          sql: |
            ```sql
            create schema myschema;

            create table myschema.mytable (
              id uuid primary key default gen_random_uuid(),
              data text
            );

            insert into myschema.mytable (data) values ('mydata');
            ```
        response: |
          ```json
          [
            {
              'id': '4162e008-27b0-4c0f-82dc-ccaeee9a624d',
              'data': 'mydata'
            }
          ]
          ```
        description: |
          In addition to setting the schema during initialization, you can also switch schemas on a per-query basis.
          Make sure you've set up your [database privileges and API settings](/docs/guides/api/using-custom-schemas).
        hideCodeBlock: true
  - id: insert
    description: |
      Perform an INSERT into the table or view.
    title: 'Create data: insert()'
    params:
      - name: values
        isOptional: false
        type: Map<String, dynamic> or List<Map<String, dynamic>>
        description: The values to insert. Pass an object to insert a single row or an array to insert multiple rows.
    examples:
      - id: create-a-record
        name: Create a record
        isSpotlight: true
        code: |
          ```dart
          await supabase
              .from('cities')
              .insert({'name': 'The Shire', 'country_id': 554});
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);
            ```
        hideCodeBlock: true
      - id: fetch-inserted-data
        name: Fetch inserted record
        code: |
          ```dart
          final List<Map<String, dynamic>> data =
                  await supabase.from('cities').insert([
                {'name': 'The Shire', 'country_id': 554},
                {'name': 'Rohan', 'country_id': 555},
              ]).select();
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'Denmark'
            }
          ]
          ```
      - id: bulk-create
        name: Bulk create
        code: |
          ```dart
          await supabase.from('cities').insert([
            {'name': 'The Shire', 'country_id': 554},
            {'name': 'Rohan', 'country_id': 555},
          ]);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);
            ```
        response: |
          ```json
          PostgrestException(
            'code': '23505',
            'details': 'Key (id)=(1) already exists.',
            'hint': null,
            'message': 'duplicate key value violates unique constraint "countries_pkey"'
          );
          ```

  - id: update
    description: |
      Perform an UPDATE on the table or view.
    title: 'Modify data: update()'
    notes: |
      - `update()` should always be combined with [Filters](/docs/reference/dart/using-filters) to target the item(s) you wish to update.
    params:
      - name: values
        isOptional: false
        type: Map<String, dynamic>
        description: The values to update with.
    examples:
      - id: updating-your-data
        name: Update your data
        isSpotlight: true
        code: |
          ```dart
          await supabase
            .from('instruments')
            .update({ 'name': 'piano' })
            .eq('id', 1);
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
      - id: update-a-record-and-return-it
        name: Update a record and return it
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .update({ 'name': 'piano' })
            .eq('id', 1)
            .select();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'piano'
            }
          ]
          ```
        hideCodeBlock: true
      - id: updating-json-data
        name: Update JSON data
        code: |
          ```dart
          await supabase
            .from('users')
            .update({
              'address': {
                'street': 'Melrose Place',
                'postcode': 90210
              }
            })
            .eq('address->postcode', 90210);
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210 }');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'Michael',
              'address': {
                'street': 'Melrose Place',
                'postcode': 90210
              }
            }
          ]
          ```
        description: |
          Postgres offers some
          [operators](/docs/guides/database/json#query-the-jsonb-data) for
          working with JSON data. Currently, it is only possible to update the entire JSON document.

  - id: upsert
    description: |
      Perform an UPSERT on the table or view. Depending on the column(s) passed to `onConflict`, `.upsert()` allows you to perform the equivalent of `.insert()` if a row with the corresponding `onConflict` columns doesn't exist, or if it does exist, perform an alternative action depending on `ignoreDuplicates`.
    title: 'Upsert data: upsert()'
    notes: |
      - Primary keys must be included in `values` to use upsert.
    params:
      - name: values
        isOptional: false
        type: Map<String, dynamic> or List<Map<String, dynamic>>
        description: The values to upsert with. Pass a Map to upsert a single row or an List to upsert multiple rows.
      - name: onConflict
        isOptional: true
        type: String
        description: Comma-separated UNIQUE column(s) to specify how duplicate rows are determined. Two rows are duplicates if all the `onConflict` columns are equal.
      - name: ignoreDuplicates
        isOptional: true
        type: bool
        description: If `true`, duplicate rows are ignored. If `false`, duplicate rows are merged with existing rows.
      - name: defaultToNull
        isOptional: true
        type: bool
        description: Make missing fields default to `null`. Otherwise, use the default value for the column. This only applies when inserting new rows, not when merging with existing rows where ignoreDuplicates is set to false. This also only applies when doing bulk upserts.
    examples:
      - id: upsert-your-data
        name: Upsert your data
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .upsert({ 'id': 1, 'name': 'piano' })
            .select();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'piano'
            }
          ]
          ```
      - id: bulk-upsert-your-data
        name: Bulk Upsert your data
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .upsert([
              { 'id': 1, 'name': 'piano' },
              { 'id': 2, 'name': 'harp' },
            ])
            .select();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'piano'
            },
            {
              'id': 2,
              'name': 'harp'
            }
          ]
          ```
        hideCodeBlock: true
      - id: upserting-into-tables-with-constraints
        name: Upserting into tables with constraints
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('users')
            .upsert({ 'id': 42, 'handle': 'saoirse', 'display_name': 'Saoirse' }, { onConflict: 'handle' })
            .select();
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 generated by default as identity primary key,
                handle text not null unique,
                display_name text
              );

            insert into
              users (id, handle, display_name)
            values
              (1, 'saoirse', null);
            ```
        response: |
          ```json
          PostgrestException(
            'code': '23505',
            'details': 'Key (handle)=(saoirse) already exists.',
            'hint': null,
            'message': "duplicate key value violates unique constraint "users_handle_key"'
          )
          ```
        description: |
          In the above query, `upsert()` implicitly uses the `id`
          (primary key) column to determine conflicts. If there is no existing
          row with the same `id`, `upsert()` inserts a new row, which
          will fail in this case as there is already a row with `handle` `"saoirse"`.
          Using the `onConflict` option, you can instruct `upsert()` to use
          another column with a unique constraint to determine conflicts.

  - id: delete
    description: |
      Perform a DELETE on the table or view.
    title: 'Delete data: delete()'
    notes: |
      - `delete()` should always be combined with [Filters](/docs/reference/dart/using-filters) to target the item(s) you wish to delete.
      - If you use `delete()` with filters and you have RLS enabled, only rows visible through `SELECT` policies are deleted. Note that by default no rows are visible, so you need at least one `SELECT`/`ALL` policy that makes the rows visible.
    examples:
      - id: delete-records
        name: Delete records
        isSpotlight: true
        code: |
          ```dart
          await supabase
            .from('countries')
            .delete()
            .eq('id', 1);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Mordor');
            ```
      - id: delete-multiple-records
        name: Delete multiple records
        code: |
          ```dart
          await supabase
            .from('countries')
            .delete()
            .inFilter('id', [1, 2, 3])
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Mordor'), (2, 'Gondor'), (3, 'Rohan');
            ```
      - id: fetch-delete-records
        name: Fetch deleted records
        code: |
          ```dart
          final List<Map<String,dynamic>> data = await supabase
            .from('cities')
            .delete()
            .match({ 'id': 666 })
            .select();
          ```

  - id: rpc
    title: 'Stored Procedures: rpc()'
    description: |
      Perform a function call.

      You can call Postgres functions as Remote Procedure Calls, logic in your database that you can execute from anywhere.
      Functions are useful when the logic rarely changes—like for password resets and updates.
    params:
      - name: fn
        isOptional: false
        type: String
        description: The function name to call.
      - name: params
        isOptional: true
        type: Map<String, dynamic>
        description: The arguments to pass to the function call.
    examples:
      - id: call-a-postgres-function-without-arguments
        name: Call a Postgres function without arguments
        code: |
          ```dart
          final data = await supabase
            .rpc('hello_world');
          ```
        data:
          sql: |
            ```sql
            create function hello_world() returns text as $$
              select 'Hello world';
            $$ language sql;
            ```
        response: |
          ```json
          'Hello world'
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: call-a-postgres-function-with-arguments
        name: Call a Postgres function with arguments
        code: |
          ```dart
          final data = await supabase
            .rpc('echo_city', params: { 'say': '👋' });
          ```
        data:
          sql: |
            ```sql
            create function echo(say text) returns text as $$
              select say;
            $$ language sql;
            ```
        response: |
          ```json
          '👋'
          ```
        hideCodeBlock: true
      - id: bulk-processing
        name: Bulk processing
        code: |
          ```dart
          final data = await supabase
            .rpc('add_one_each', params: { arr: [1, 2, 3] });
          ```
        data:
          sql: |
            ```sql
            create function add_one_each(arr int[]) returns int[] as $$
              select array_agg(n + 1) from unnest(arr) as n;
            $$ language sql;
            ```
        response: |
          ```json
          [
            2,
            3,
            4,
          ]
          ```
        description: |
          You can process large payloads by passing in an array as an argument.
        hideCodeBlock: true
      - id: call-a-postgres-function-with-filters
        name: Call a Postgres function with filters
        code: |
          ```dart
          final data = await supabase
            .rpc('list_stored_countries')
            .eq('id', 1)
            .single();
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Mordor'),
              (2, 'Gondor');

            create function list_stored_countries() returns setof countries as $$
              select * from countries;
            $$ language sql;
            ```
        response: |
          ```json
          {
            'id': 1,
            'name': 'Mordor'
          }
          ```
        description: |
          Postgres functions that return tables can also be combined with [Filters](/docs/reference/dart/using-filters) and [Modifiers](/docs/reference/dart/using-modifiers).
        hideCodeBlock: true

  - id: subscribe
    description: |
      Subscribe to realtime changes in your database.
    title: 'on().subscribe()'
    notes: |
      - Realtime is disabled by default for new tables. You can turn it on by [managing replication](/docs/guides/realtime/postgres-changes#replication-setup).
      - If you want to receive the "previous" data for updates and deletes, you will need to set `REPLICA IDENTITY` to `FULL`, like this: `ALTER TABLE your_table REPLICA IDENTITY FULL;`
    examples:
      - id: listen-to-database-changes
        name: Listen to database changes
        isSpotlight: true
        code: |
          ```dart
          supabase
              .channel('public:countries')
              .onPostgresChanges(
                  event: PostgresChangeEvent.all,
                  schema: 'public',
                  table: 'countries',
                  callback: (payload) {
                    print('Change received: ${payload.toString()}');
                  })
              .subscribe();
          ```
      - id: listen-to-inserts
        name: Listen to inserts
        code: |
          ```dart
          supabase
              .channel('public:countries')
              .onPostgresChanges(
                  event: PostgresChangeEvent.insert,
                  schema: 'public',
                  table: 'countries',
                  callback: (payload) {
                    print('Change received: ${payload.toString()}');
                  })
              .subscribe();
          ```
      - id: listen-to-updates
        name: Listen to updates
        description: |
          By default, Supabase will send only the updated record. If you want to receive the previous values as well you can
          enable full replication for the table you are listening too:

          ```sql
          alter table "your_table" replica identity full;
          ```
        code: |
          ```dart
          supabase
              .channel('public:countries')
              .onPostgresChanges(
                  event: PostgresChangeEvent.update,
                  schema: 'public',
                  table: 'countries',
                  callback: (payload) {
                    print('Change received: ${payload.toString()}');
                  })
              .subscribe();
          ```
      - id: listen-to-deletes
        name: Listen to deletes
        description: |
          By default, Supabase does not send deleted records. If you want to receive the deleted record you can
          enable full replication for the table you are listening too:

          ```sql
          alter table "your_table" replica identity full;
          ```
        code: |
          ```dart
          supabase
              .channel('public:countries')
              .onPostgresChanges(
                  event: PostgresChangeEvent.delete,
                  schema: 'public',
                  table: 'countries',
                  callback: (payload) {
                    print('Change received: ${payload.toString()}');
                  })
              .subscribe();
          ```
      - id: listen-to-multiple-events
        name: Listen to multiple events
        description: You can chain listeners if you want to listen to multiple events for each table.
        code: |
          ```dart
          supabase
              .channel('public:countries')
              .onPostgresChanges(
                  event: PostgresChangeEvent.insert,
                  schema: 'public',
                  table: 'countries',
                  callback: (payload) {
                    print('Insert event received: ${payload.toString()}');
                  })
              .onPostgresChanges(
                  event: PostgresChangeEvent.delete,
                  schema: 'public',
                  table: 'countries',
                  callback: (payload) {
                    print('Delete event received: ${payload.toString()}');
                  })
              .subscribe();
          ```
      - id: listening-to-row-level-changes
        name: Listen to row level changes
        description: You can listen to individual rows by using the `filter` parameter passing a `PostgresChangeFilter` object with `PostgresChangeFilterType.eq` as `type`.
        code: |
          ```dart
          supabase
              .channel('public:countries:id=eq.200')
              .onPostgresChanges(
                  event: PostgresChangeEvent.delete,
                  schema: 'public',
                  table: 'countries',
                  filter: PostgresChangeFilter(
                    type: PostgresChangeFilterType.eq,
                    column: 'id',
                    value: 200,
                  ),
                  callback: (payload) {
                    print('Change received: ${payload.toString()}');
                  })
              .subscribe();
          ```
      - id: listen-to-broadcast
        name: Listen to broadcast messages
        code: |
          ```dart
          supabase
              .channel('room1')
              .onBroadcast(
                  event: 'cursor-pos',
                  callback: (payload) {
                    print('Cursor position received!: $payload');
                  })
              .subscribe();
          ```
      - id: listen-to-presence-events
        name: Listen to presence events
        code: |
          ```dart
          final channel = supabase.channel('room1');

          channel.onPresenceSync((payload) {
            print('Synced presence state: ${channel.presenceState()}');
          }).onPresenceJoin((payload) {
            print('Newly joined presences $payload');
          }).onPresenceLeave((payload) {
            print('Newly left presences: $payload');
          }).subscribe((status, error) async {
            if (status == RealtimeSubscribeStatus.subscribed) {
              await channel.track({'online_at': DateTime.now().toIso8601String()});
            }
          });
          ```
  - id: remove-channel
    description: |
      Unsubscribes and removes Realtime channel from Realtime client.
    title: 'removeChannel()'
    notes: |
      - Removing a channel is a great way to maintain the performance of your project's Realtime service as well as your database if you're listening to Postgres changes. Supabase will automatically handle cleanup 30 seconds after a client is disconnected, but unused channels may cause degradation as more clients are simultaneously subscribed.
    examples:
      - id: removes-a-channel
        name: Remove a channel
        isSpotlight: true
        code: |
          ```dart
          final status = await supabase.removeChannel(channel);
          ```

  - id: remove-all-channels
    description: |
      Unsubscribes and removes all Realtime channels from Realtime client.
    title: 'removeAllChannels()'
    notes: |
      - Removing channels is a great way to maintain the performance of your project's Realtime service as well as your database if you're listening to Postgres changes. Supabase will automatically handle cleanup 30 seconds after a client is disconnected, but unused channels may cause degradation as more clients are simultaneously subscribed.
    examples:
      - id: remove-all-channels
        name: Remove all channels
        isSpotlight: true
        code: |
          ```dart
          final statuses = await supabase.removeAllChannels();
          ```

  - id: get-channels
    description: |
      Returns all Realtime channels.
    title: 'getChannels()'
    examples:
      - id: get-all-channels
        name: Get all channels
        isSpotlight: true
        code: |
          ```dart
          final channels = supabase.getChannels();
          ```

  - id: stream
    description: |
      Returns real-time data from your table as a `Stream`.
    title: 'stream()'
    notes: |
      - Realtime is disabled by default for new tables. You can turn it on by [managing replication](/docs/guides/realtime/postgres-changes#replication-setup).
      - `stream()` will emit the initial data as well as any further change on the database as `Stream<List<Map<String, dynamic>>>` by combining Postgrest and Realtime.
      - Takes a list of primary key column names that will be used to update and delete the proper records within the SDK.
      - The following filters are available
        - `.eq('column', value)` listens to rows where the column equals the value
        - `.neq('column', value)` listens to rows where the column does not equal the value
        - `.gt('column', value)` listens to rows where the column is greater than the value
        - `.gte('column', value)` listens to rows where the column is greater than or equal to the value
        - `.lt('column', value)` listens to rows where the column is less than the value
        - `.lte('column', value)` listens to rows where the column is less than or equal to the value
        - `.inFilter('column', [val1, val2, val3])` listens to rows where the column is one of the values
    examples:
      - id: listen-to-table
        name: Listen to a table
        isSpotlight: true
        code: |
          ```dart
          supabase.from('countries')
            .stream(primaryKey: ['id'])
            .listen((List<Map<String, dynamic>> data) {
            // Do something awesome with the data
          });
          ```
      - id: with-filter-order-limit
        name: With filter, order and limit
        code: |
          ```dart
          supabase.from('countries')
            .stream(primaryKey: ['id'])
            .eq('id', 120)
            .order('name')
            .limit(10);
          ```
      - id: with-in-filter
        name: With an IN filter
        code: |
          ```dart
          supabase.from('countries')
            .stream(primaryKey: ['id'])
            .inFilter('id', [1, 2, 3])
            .order('name')
            .limit(10);
          ```
      - id: using-stream-with-stream-builder
        name: Using `stream()` with `StreamBuilder`
        description: |
          When using `stream()` with a `StreamBuilder` within your Flutter application, make sure to store your stream in a variable to prevent refetching upon rebuilding.
        code: |
          ```dart
          final supabase = Supabase.instance.client;

          class MyWidget extends StatefulWidget {
            const MyWidget({Key? key}) : super(key: key);

            @override
            State<MyWidget> createState() => _MyWidgetState();
          }

          class _MyWidgetState extends State<MyWidget> {
            // Persist the stream in a local variable to prevent refetching upon rebuilds
            final _stream = supabase.from('countries').stream(primaryKey: ['id']);

            @override
            Widget build(BuildContext context) {
              return StreamBuilder(
                stream: _stream,
                builder: (context, snapshot) {
                  // Return your widget with the data from the snapshot
                },
              );
            }
          }
          ```

  - id: list-buckets
    description: |
      Retrieves the details of all Storage buckets within an existing product.
    title: 'listBuckets()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: `select`
        - `objects` permissions: none
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: list-buckets
        name: List buckets
        isSpotlight: true
        code: |
          ```dart
          final List<Bucket> buckets = await supabase
            .storage
            .listBuckets();
          ```
        response: |
          ```json
          [
            Bucket(
              id: 'avatars',
              name: 'avatars',
              owner: '',
              public: false,
              file_size_limit: 1024,
              allowed_mime_types: [
                'image/png'
              ],
              created_at: '2024-05-22T22:26:05.100Z',
              updated_at: '2024-05-22T22:26:05.100Z'
            ),
          ]
          ```

  - id: get-bucket
    description: |
      Retrieves the details of an existing Storage bucket.
    title: 'getBucket()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: `select`
        - `objects` permissions: none
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: String
        description: The unique identifier of the bucket you would like to retrieve.
    examples:
      - id: get-bucket
        name: Get bucket
        isSpotlight: true
        code: |
          ```dart
          final Bucket bucket = await supabase
            .storage
            .getBucket('avatars');
          ```
        response: |
          ```json
          Bucket(
            id: 'avatars',
            name: 'avatars',
            owner: '',
            public: false,
            file_size_limit: 1024,
            allowed_mime_types: [
              'image/png'
            ],
            created_at: '2024-05-22T22:26:05.100Z',
            updated_at: '2024-05-22T22:26:05.100Z'
          )
          ```
  - id: create-bucket
    description: |
      Creates a new Storage bucket
    title: 'createBucket()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: `insert`
        - `objects` permissions: none
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: String
        description: A unique identifier for the bucket you are creating.
      - name: bucketOptions
        isOptional: true
        type: BucketOptions
        description: A parameter to optionally make the bucket public.
        subContent:
          - name: public
            isOptional: false
            type: bool
            description: The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.
          - name: fileSizeLimit
            isOptional: true
            type: String
            description: Specifies the max file size in bytes that can be uploaded to this bucket. The global file size limit takes precedence over this value. The default value is null, which doesn't set a per bucket file size limit.
          - name: allowedMimeTypes
            isOptional: true
            type: List<String>
            description: Specifies the allowed mime types that this bucket can accept during upload. The default value is null, which allows files with all mime types to be uploaded. Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.
    examples:
      - id: create-bucket
        name: Create bucket
        isSpotlight: true
        code: |
          ```dart
          final String bucketId = await supabase
            .storage
            .createBucket('avatars');
          ```
        response: |
          ```json
          'avatars'
          ```

  - id: empty-bucket
    description: |
      Removes all objects inside a single bucket.
    title: 'emptyBucket()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: `select`
        - `objects` permissions: `select` and `delete`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: String
        description: A unique identifier for the bucket you are emptying.
    examples:
      - id: empty-bucket
        name: Empty bucket
        isSpotlight: true
        code: |
          ```dart
          final String res = await supabase
            .storage
            .emptyBucket('avatars');
          ```
        response: |
          ```json
          'Successfully emptied'
          ```
  - id: update-bucket
    description: |
      Updates a new Storage bucket
    title: 'updateBucket()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: `update`
        - `objects` permissions: none
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: String
        description: A unique identifier for the bucket you are updating.
      - name: bucketOptions
        isOptional: false
        type: BucketOptions
        description: A parameter to optionally make the bucket public.
        subContent:
          - name: public
            isOptional: false
            type: bool
            description: The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.
          - name: fileSizeLimit
            isOptional: true
            type: String
            description: Specifies the max file size in bytes that can be uploaded to this bucket. The global file size limit takes precedence over this value. The default value is null, which doesn't set a per bucket file size limit.
          - name: allowedMimeTypes
            isOptional: true
            type: List<String>
            description: Specifies the allowed mime types that this bucket can accept during upload. The default value is null, which allows files with all mime types to be uploaded. Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.
    examples:
      - id: update-bucket
        name: Update bucket
        isSpotlight: true
        code: |
          ```dart
          final String res = await supabase
            .storage
            .updateBucket('avatars', const BucketOptions(public: false));
          ```
        response: |
          ```json
          'Successfully updated'
          ```

  - id: delete-bucket
    description: |
      Deletes an existing bucket. A bucket can't be deleted with existing objects inside it. You must first `empty()` the bucket.
    title: 'deleteBucket()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: `select` and `delete`
        - `objects` permissions: none
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: String
        description: A unique identifier for the bucket you are deleting.
    examples:
      - id: delete-bucket
        name: Delete bucket
        isSpotlight: true
        code: |
          ```dart
          final String res = await supabase
            .storage
            .deleteBucket('avatars');
          ```
        response: |
          ```json
          'Successfully deleted'
          ```

  - id: from-upload
    description: |
      Uploads a file to an existing bucket.
    title: 'from.upload()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `insert`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: String
        description: The relative file path. Should be of the format folder/subfolder/filename.png. The bucket must already exist before attempting to update.
      - name: file
        isOptional: false
        type: File or Uint8List
        description: File object to be stored in the bucket.
      - name: fileOptions
        isOptional: true
        type: FileOptions
        subContent:
          - name: cacheControl
            isOptional: true
            type: String
            description: The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control` header as `max-age=<seconds>`. Defaults to 3600 seconds.
          - name: upsert
            isOptional: true
            type: bool
            description: When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false.
          - name: contentType
            isOptional: true
            type: String
            description: The `Content-Type` header value. Gets parsed with MediaType.parse(mime). Throws a FormatError if the media type is invalid.
      - name: retryAttempts
        isOptional: true
        type: int
        description: Sets the retryAttempts parameter set across the storage client. Defaults to 10.
      - name: retryController
        isOptional: true
        type: StorageRetryController
        description: Pass a RetryController instance and call `cancel()` to cancel the retry attempts.
    examples:
      - id: upload-file
        name: Upload file
        isSpotlight: true
        code: |
          ```dart
          final avatarFile = File('path/to/file');
          final String fullPath = await supabase.storage.from('avatars').upload(
                'public/avatar1.png',
                avatarFile,
                fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
              );
          ```
        response: |
          ```json
          'avatars/public/avatar1.png'
          ```
      - id: upload-file-on-web
        name: Upload file on web
        code: |
          ```dart
          final Uint8List avatarFile = file.bytes;
          final String fullPath = await supabase.storage.from('avatars').uploadBinary(
                'public/avatar1.png',
                avatarFile,
                fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
              );
          ```
        response: |
          ```json
          'avatars/public/avatar1.png'
          ```

  - id: from-update
    description: |
      Replaces an existing file at the specified path with a new one.
    title: 'from.update()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `update` and `select`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: String
        description: The relative file path. Should be of the format folder/subfolder/filename.png. The bucket must already exist before attempting to update.
      - name: file
        isOptional: false
        type: File or Uint8List
        description: File object to be stored in the bucket.
      - name: fileOptions
        isOptional: true
        type: FileOptions
        subContent:
          - name: cacheControl
            isOptional: true
            type: String
            description: The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control` header as `max-age=<seconds>`. Defaults to 3600 seconds.
          - name: upsert
            isOptional: true
            type: bool
            description: When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false.
          - name: contentType
            isOptional: true
            type: String
            description: The `Content-Type` header value. Gets parsed with MediaType.parse(mime). Throws a FormatError if the media type is invalid.
      - name: retryAttempts
        isOptional: true
        type: int
        description: Sets the retryAttempts parameter set across the storage client. Defaults to 10.
      - name: retryController
        isOptional: true
        type: StorageRetryController
        description: Pass a RetryController instance and call `cancel()` to cancel the retry attempts.
    examples:
      - id: update-file
        name: Update file
        isSpotlight: true
        code: |
          ```dart
          final avatarFile = File('path/to/local/file');
          final String path = await supabase.storage.from('avatars').update(
                'public/avatar1.png',
                avatarFile,
                fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
              );
          ```
        response: |
          ```json
          'avatars/public/avatar1.png'
          ```
      - id: update-file-on-web
        name: Update file on web
        code: |
          ```dart
          final Uint8List avatarFile = file.bytes;
          final String path = await supabase.storage.from('avatars').updateBinary(
                'public/avatar1.png',
                avatarFile,
                fileOptions: const FileOptions(cacheControl: '3600', upsert: false),
              );
          ```
        response: |
          ```json
          'avatars/public/avatar1.png'
          ```

  - id: from-move
    description: |
      Moves an existing file, optionally renaming it at the same time.
    title: 'from.move()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `update` and `select`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: fromPath
        isOptional: false
        type: String
        description: The original file path, including the current file name. For example folder/image.png.
      - name: toPath
        isOptional: false
        type: String
        description: The new file path, including the new file name. For example folder/image-new.png.
    examples:
      - id: move-file
        name: Move file
        isSpotlight: true
        code: |
          ```dart
          final String result = await supabase
            .storage
            .from('avatars')
            .move('public/avatar1.png', 'private/avatar2.png');
          ```
        response: |
          ```json
          'Successfully moved'
          ```

  - id: from-create-signed-url
    description: |
      Create signed url to download file without requiring permissions. This URL can be valid for a set number of seconds.
    title: 'from.createSignedUrl()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `select`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: String
        description: The file path, including the file name. For example folder/image.png.
      - name: expiresIn
        isOptional: false
        type: int
        description: The number of seconds until the signed URL expires. For example, 60 for a URL which is valid for one minute.
      - name: transform
        isOptional: true
        type: TransformOptions
        description: Transform the asset before serving it to the client.
        subContent:
          - name: width
            isOptional: true
            type: int
            description: The width of the image in pixels.
          - name: height
            isOptional: true
            type: int
            description: The height of the image in pixels.
          - name: resize
            isOptional: true
            type: ResizeMode
            description: Specifies how image cropping should be handled when performing image transformations. Defaults to `ResizeMode.cover`.
          - name: quality
            isOptional: true
            type: int
            description: Set the quality of the returned image. A number from 20 to 100, with 100 being the highest quality. Defaults to 80
          - name: format
            isOptional: true
            type: RequestImageFormat
            description: Specify the format of the image requested. When using 'origin' we force the format to be the same as the original image. When this option is not passed in, images are optimized to modern image formats like Webp.
    examples:
      - id: create-signed-url
        name: Create Signed URL
        isSpotlight: true
        code: |
          ```dart
          final String signedUrl = await supabase
            .storage
            .from('avatars')
            .createSignedUrl('avatar1.png', 60);
          ```
      - id: create-signed-url-with-transform
        name: With transform
        code: |
          ```dart
          final String signedUrl = await supabase
            .storage
            .from('avatars')
            .createSignedUrl(
              'avatar1.png',
              60,
              transform: TransformOptions(
                width: 200,
                height: 200,
              ),
            );
          ```
        response: |
          ```json
          'https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar1.png?token=<TOKEN>''
          ```

  - id: from-get-public-url
    description: |
      Retrieve URLs for assets in public buckets
    title: 'from.getPublicUrl()'
    notes: |
      - The bucket needs to be set to public, either via [updateBucket()](/docs/reference/dart/storage-updatebucket) or by going to Storage on [supabase.com/dashboard](https://supabase.com/dashboard), clicking the overflow menu on a bucket and choosing "Make public"
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: none
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: String
        description: The path and name of the file to generate the public URL for. For example folder/image.png.
      - name: transform
        isOptional: true
        type: TransformOptions
        description: Transform the asset before serving it to the client.
        subContent:
          - name: width
            isOptional: true
            type: int
            description: The width of the image in pixels.
          - name: height
            isOptional: true
            type: int
            description: The height of the image in pixels.
          - name: resize
            isOptional: true
            type: ResizeMode
            description: Specifies how image cropping should be handled when performing image transformations. Defaults to `ResizeMode.cover`.
          - name: quality
            isOptional: true
            type: int
            description: Set the quality of the returned image. A number from 20 to 100, with 100 being the highest quality. Defaults to 80
          - name: format
            isOptional: true
            type: RequestImageFormat
            description: Specify the format of the image requested. When using 'origin' we force the format to be the same as the original image. When this option is not passed in, images are optimized to modern image formats like Webp.
    examples:
      - id: returns-the-url-for-an-asset-in-a-public-bucket
        name: Returns the URL for an asset in a public bucket
        isSpotlight: true
        code: |
          ```dart
          final String publicUrl = supabase
            .storage
            .from('public-bucket')
            .getPublicUrl('avatar1.png');
          ```
        response: |
          ```json
          'https://example.supabase.co/storage/v1/object/public/public-bucket/folder/avatar1.png'
          ```
      - id: returns-the-url-for-an-asset-in-a-public-bucket-with-transform
        name: With transform
        isSpotlight: true
        code: |
          ```dart
          final String publicUrl = await supabase
            .storage
            .from('public-bucket')
            .getPublicUrl(
              'avatar1.png',
              transform: TransformOptions(
                width: 200,
                height: 200,
              ),
            );
          ```
        response: |
          ```json
          'https://example.supabase.co/storage/v1/object/public/public-bucket/folder/avatar1.png'
          ```

  - id: from-download
    description: |
      Downloads a file.
    title: 'from.download()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `select`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: String
        description: The full path and file name of the file to be downloaded. For example folder/image.png.
      - name: transform
        isOptional: true
        type: TransformOptions
        description: Transform the asset before serving it to the client.
        subContent:
          - name: width
            isOptional: true
            type: int
            description: The width of the image in pixels.
          - name: height
            isOptional: true
            type: int
            description: The height of the image in pixels.
          - name: resize
            isOptional: true
            type: ResizeMode
            description: Specifies how image cropping should be handled when performing image transformations. Defaults to `ResizeMode.cover`.
          - name: quality
            isOptional: true
            type: int
            description: Set the quality of the returned image. A number from 20 to 100, with 100 being the highest quality. Defaults to 80
          - name: format
            isOptional: true
            type: RequestImageFormat
            description: Specify the format of the image requested. When using 'origin' we force the format to be the same as the original image. When this option is not passed in, images are optimized to modern image formats like Webp.
    examples:
      - id: download-file
        name: Download file
        isSpotlight: true
        code: |
          ```dart
          final Uint8List file = await supabase
            .storage
            .from('avatars')
            .download('avatar1.png');
          ```
        response: |
          ```json
          <Blob>
          ```
      - id: download-file-with-transform
        name: With transform
        isSpotlight: true
        code: |
          ```dart
          final Uint8List file = await supabase
            .storage
            .from('avatars')
            .download(
              'avatar1.png',
              transform: TransformOptions(
                width: 200,
                height: 200,
              ),
            );
          ```
        response: |
          ```json
          <Blob>
          ```

  - id: from-remove
    description: |
      Deletes files within the same bucket
    title: 'from.remove()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `delete` and `select`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: paths
        isOptional: false
        type: List<String>
        description: A list of files to delete, including the path and file name. For example ['folder/image.png'].
    examples:
      - id: delete-file
        name: Delete file
        isSpotlight: true
        code: |
          ```dart
          final List<FileObject> objects = await supabase
            .storage
            .from('avatars')
            .remove(['avatar1.png']);
          ```
        response: |
          ```json
          [
            FileObject(
            name: 'avatar1.png',
            id: 'e668cf7f-821b-4a2f-9dce-7dfa5dd1cfd2',
            updated_at: '2024-05-22T23:06:05.580Z',
            created_at: '2024-05-22T23:04:34.443Z',
            last_accessed_at: '2024-05-22T23:04:34.443Z',
            metadata: {
              eTag: 'c5e8c553235d9af30ef4f6e280790b92',
              size: 32175,
              mimetype: 'image/png',
              cacheControl: 'max-age=3600',
              lastModified: '2024-05-22T23:06:05.574Z',
              contentLength: 32175,
              httpStatusCode: 200
            ),
          ]
          ```

  - id: from-list
    description: |
      Lists all the files within a bucket.
    title: 'from.list()'
    notes: |
      - Policy permissions required:
        - `buckets` permissions: none
        - `objects` permissions: `select`
      - Refer to the [Storage guide](https://supabase.com/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: String
        description: The folder path.
      - name: searchOptions
        isOptional: true
        type: SearchOptions
        description: Options for the search operations such as limit and offset.
        subContent:
          - name: limit
            isOptional: true
            type: int
            description: The number of files you want to be returned.
          - name: offset
            isOptional: true
            type: int
            description: The starting position.
          - name: sortBy
            isOptional: true
            type: SortBy
            description: The column to sort by. Can be any column inside a FileObject.
            subContent:
              - name: column
                isOptional: true
                type: String
              - name: order
                isOptional: true
                type: String
          - name: search
            isOptional: true
            type: String
            description: The search string to filter files by.
    examples:
      - id: list-files-in-a-bucket
        name: List files in a bucket
        isSpotlight: true
        code: |
          ```dart
          final List<FileObject> objects = await supabase
            .storage
            .from('avatars')
            .list();
          ```
        response: |
          ```json
          [
            FileObject(
            name: 'avatar1.png',
            id: 'e668cf7f-821b-4a2f-9dce-7dfa5dd1cfd2',
            updated_at: '2024-05-22T23:06:05.580Z',
            created_at: '2024-05-22T23:04:34.443Z',
            last_accessed_at: '2024-05-22T23:04:34.443Z',
            metadata: {
              eTag: 'c5e8c553235d9af30ef4f6e280790b92',
              size: 32175,
              mimetype: 'image/png',
              cacheControl: 'max-age=3600',
              lastModified: '2024-05-22T23:06:05.574Z',
              contentLength: 32175,
              httpStatusCode: 200
            ),
          ]
          ```
  - id: using-modifiers
    title: Using Modifiers
    description: |
      Filters work on the row level. That is, they allow you to return rows that
      only match certain conditions without changing the shape of the rows.
      Modifiers are everything that don't fit that definition—allowing you to
      change the format of the response (e.g., returning a CSV string).

      Modifiers must be specified after filters. Some modifiers only apply for
      queries that return rows (e.g., `select()` or `rpc()` on a function that
      returns a table response).

  - id: db-modifiers-select
    title: select()
    examples:
      - id: with-upsert
        name: With `upsert()`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .upsert({ 'id': 1, 'name': 'piano' })
            .select();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          [
            {
              "id": 1,
              "name": "piano"
            }
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: order
    title: order()
    description: |
      Orders the result with the specified column.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to order by.
      - name: ascending
        isOptional: true
        type: bool
        description: Whether to order in ascending order. Default is `false`.
      - name: nullsFirst
        isOptional: true
        type: bool
        description: Whether to order nulls first. Default is `false`.
      - name: referencedTable
        isOptional: true
        type: String
        description: Specify the referenced table when ordering by a column in an embedded resource.
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('id, name')
            .order('id', ascending: false);
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'id': 3,
              'name': 'cello'
            },
            {
              'id': 2,
              'name': 'viola'
            },
            {
              'id': 1,
              'name': 'viola'
            }
          ]
          ```
      - id: on-a-referenced-table
        name: On a referenced table
        code: |
          ```dart
          final data = await supabase
              .from('orchestral_sections')
              .select('''
                name,
                instruments (
                  name
                )
              ''')
              .order('name', referencedTable: 'instruments', ascending: false);
            ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'harp'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          [
            {
              'name': 'strings',
              'instruments': [
                {
                  'name': 'violin'
                },
                {
                  'name': 'harp'
                }
              ]
            },
            {
              'name': 'woodwinds',
              'instruments': []
            }
          ]
          ```
      - id: order-parent-table-by-a-referenced-table
        name: Order parent table by a referenced table
        code: |
          ```dart
          final data = await supabase
              .from('instruments')
              .select('''
                name,
                section:orchestral_sections (
                  name
                )
              ''')
              .order('section(name)', ascending: true)
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'violin'),
              (2, 2, 'flute');
            ```
        response: |
          ```json
          [
            {
              "name": "violin",
              "orchestral_sections": { "name": "strings" }
            },
            {
              "name": "flute",
              "orchestral_sections": { "name": "woodwinds" }
            }
          ]
          ```
  - id: limit
    title: limit()
    description: |
      Limits the result with the specified count.
    params:
      - name: count
        isOptional: false
        type: int
        description: The maximum number of rows to return.
      - name: referencedTable
        isOptional: true
        type: int
        description: Set this to limit rows of referenced tables instead of the parent table.
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('name')
            .limit(1);
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'name': 'violin'
            }
          ]
          ```
      - id: on-a-referenced-table
        name: On a referenced table
        code: |
          ```dart
          final data = await supabase
            .from('orchestral_sections')
            .select('''
              name,
              instruments (
                name
              )
            ''')
            .limit(1, referencedTable: 'instruments');
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'violin'),
              (2, 1, 'viola');
            ```
        response: |
          ```json
          [
            {
              'name': 'strings',
              'instruments': [
                {
                  'name': 'violin'
                }
              ]
            }
          ]
          ```
  - id: range
    title: range()
    description: |
      Limits the result to rows within the specified range, inclusive.
    params:
      - name: from
        isOptional: false
        type: int
        description: The starting index from which to limit the result.
      - name: to
        isOptional: false
        type: int
        description: The last index to which to limit the result.
      - name: referencedTable
        isOptional: true
        type: String
        description: Set this to limit rows of referenced tables instead of the parent table.
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('name')
            .range(0, 1);
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'name': 'violin'
            },
            {
              'name': 'viola'
            }
          ]
          ```
  - id: single
    title: single()
    description: |
      Retrieves only one row from the result. Result must be one row (e.g. using limit), otherwise this will result in an error.
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('name')
            .limit(1)
            .single();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          {
            'name': 'violin'
          }
          ```
  - id: maybe-single
    title: maybeSingle()
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select()
            .eq('name', 'guzheng')
            .maybeSingle();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          null
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: csv
    title: csv()
    examples:
      - id: return-data-as-csv
        name: Return data as CSV
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select()
            .csv();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          'id,name\n1,violin\n2,viola\n3,cello'
          ```
        description: |
          By default, the data is returned in JSON format, but can also be returned as Comma Separated Values.
        hideCodeBlock: true
        isSpotlight: true
  - id: explain
    title: Using Explain
    description: |
      For debugging slow queries, you can get the [Postgres `EXPLAIN` execution plan](https://www.postgresql.org/docs/current/sql-explain.html) of a query
      using the `explain()` method. This works on any query, even for `rpc()` or writes.

      Explain is not enabled by default as it can reveal sensitive information about your database.
      It's best to only enable this for testing environments but if you wish to enable it for production you can provide additional protection by using a `pre-request` function.

      Follow the [Performance Debugging Guide](/docs/guides/database/debugging-performance) to enable the functionality on your project.
    params:
      - name: analyze
        isOptional: true
        type: bool
        description: If `true`, the query will be executed and the actual run time will be returned.
      - name: verbose
        isOptional: true
        type: bool
        description: If `true`, the query identifier will be returned and `data` will include the output columns of the query.
      - name: settings
        isOptional: true
        type: bool
        description: If `true`, include information on configuration parameters that affect query planning.
      - name: buffers
        isOptional: true
        type: bool
        description: If `true`, include information on buffer usage.
      - name: wal
        isOptional: true
        type: bool
        description: If `true`, include information on WAL record generation.
    examples:
      - id: get-execution-plan
        name: Get the execution plan
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select()
            .explain();
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```
          Aggregate  (cost=33.34..33.36 rows=1 width=112)
            ->  Limit  (cost=0.00..18.33 rows=1000 width=40)
                  ->  Seq Scan on instruments  (cost=0.00..22.00 rows=1200 width=40)
          ```
        description: |
          By default, the data is returned in TEXT format, but can also be returned as JSON by using the `format` parameter.
        hideCodeBlock: true
        isSpotlight: true

      - id: get-execution-plan-with-analyze-and-verbose
        name: Get the execution plan with analyze and verbose
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select()
            .explain(analyze:true, verbose:true);
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```
          Aggregate  (cost=33.34..33.36 rows=1 width=112) (actual time=0.041..0.041 rows=1 loops=1)
            Output: NULL::bigint, count(ROW(instruments.id, instruments.name)), COALESCE(json_agg(ROW(instruments.id, instruments.name)), '[]'::json), NULLIF(current_setting('response.headers'::text, true), ''::text), NULLIF(current_setting('response.status'::text, true), ''::text)
            ->  Limit  (cost=0.00..18.33 rows=1000 width=40) (actual time=0.005..0.006 rows=3 loops=1)
                  Output: instruments.id, instruments.name
                  ->  Seq Scan on public.instruments  (cost=0.00..22.00 rows=1200 width=40) (actual time=0.004..0.005 rows=3 loops=1)
                        Output: instruments.id, instruments.name
          Query Identifier: -4730654291623321173
          Planning Time: 0.407 ms
          Execution Time: 0.119 ms
          ```
        description: |
          By default, the data is returned in TEXT format, but can also be returned as JSON by using the `format` parameter.
        hideCodeBlock: true
        isSpotlight: false
  - id: using-filters
    title: Using Filters
    description: |
      Filters allow you to only return rows that match certain conditions.

      Filters can be used on `select()`, `update()`, `upsert()`, and `delete()` queries.

      If a Database function returns a table response, you can also apply filters.
    examples:
      - id: applying-filters
        name: Applying Filters
        description: |
          Filters must be applied after any of `select()`, `update()`, `upsert()`,
          `delete()`, and `rpc()` and before
          [modifiers](/docs/reference/dart/using-modifiers).
        code: |
          ```dart
          final data = await supabase
            .from('cities')
            .select('name, country_id')
            .eq('name', 'The Shire');  // Correct

          final data = await supabase
            .from('cities')
            .eq('name', 'The Shire')  // Incorrect
            .select('name, country_id');
          ```
      - id: chaining-filters
        name: Chaining Filters
        description: |
          Filters can be chained together to produce advanced queries as shown in the example code.
        code: |
          ```dart
          final data = await supabase
            .from('cities')
            .select('name, country_id')
            .gte('population', 1000)
            .lt('population', 10000)
          ```
      - id: conditional-chaining
        name: Conditional Chaining
        description: |
          Filters can be built up one step at a time and then executed as shown in the example code.
        code: |
          ```dart
          final filterByName = null;
          final filterPopLow = 1000;
          final filterPopHigh = 10000;

          var query = supabase
            .from('cities')
            .select('name, country_id');

          if (filterByName != null) query = query.eq('name', filterByName);
          if (filterPopLow != null) query = query.gte('population', filterPopLow);
          if (filterPopHigh != null) query = query.lt('population', filterPopHigh);

          final data = await query;
          ```
      - id: filter-by-value-within-json-column
        name: Filter by values within a JSON column
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210 }'),
              (2, 'Jane', null);
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'Michael',
              'address': {
                'postcode': 90210
              }
            },
          ]
          ```
        code: |
          ```dart
          final data = await supabase
            .from('users')
            .select()
            .eq('address->postcode', 90210);
          ```
      - id: filter-referenced-tables
        name: Filter Referenced Tables
        code: |
          ```dart
          final data = await supabase
            .from('orchestral_sections')
            .select('''
              name,
              instruments!inner (
                name
              )
            ''')
            .eq('instruments.name', 'flute');
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          [
            {
              'name': 'strings',
              'instruments': [
                {
                  'name': 'flute'
                }
              ]
            },
          ]
          ```
        description: |
          You can filter on referenced tables in your `select()` query using dot
          notation.
  - id: or
    title: or()
    description: |
      Finds all rows satisfying at least one of the filters.
    params:
      - name: filters
        isOptional: false
        type: String
        description: The filters to use, following PostgREST syntax
      - name: referencedTable
        isOptional: true
        type: String
        description: Set this to filter on referenced tables instead of the parent table
    notes: |
      - `.or()` expects you to use the raw [PostgREST syntax](https://postgrest.org/en/stable/api.html#horizontal-filtering-rows) for the filter names and values.

        ```dart
        .or('id.in.(6,7),arraycol.cs.{"a","b"}')  // Use Postgres list () and 'in' instead of `inFilter`. Array {} and 'cs' for contains.
        .or('id.in.(${mylist.join(',')}),arraycol.cs.{${mylistArray.join(',')}}')	// You can insert a Dart list for list or array column.
        .or('id.in.(${mylist.join(',')}),rangecol.cs.(${mylistRange.join(',')}]')	// You can insert a Dart list for list or range column.
        ```
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('name')
            .or('id.eq.2,name.eq.cello');
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'name': 'viola'
            },
            {
              'name': 'cello'
            }
          ]
          ```
      - id: use-or-with-and
        name: Use `or` with `and`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('name')
            .or('id.gt.3,and(id.eq.1,name.eq.violin)');
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        reponse: |
          ```json
          [
            {
              'name': 'violin'
            }
          ]
          ```
      - id: use-or-on-referenced-tables
        name: Use `or` on referenced tables
        code: |
          ```dart
          final data = await supabase
            .from('orchestral_sections')
            .select('''
              name,
              instruments!inner (
                name
              )
            ''')
            .or('section_id.eq.1,name.eq.guzheng', referencedTable: 'instruments' );
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, book_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          [
            {
              'name': 'strings',
              'instruments': [
                {
                  'name': 'violin'
                }
              ]
            }
          ]
          ```
        hideCodeBlock: true

  - id: not
    title: not()
    description: |
      Finds all rows which doesn't satisfy the filter.
    notes: |
      - `.not()` expects you to use the raw [PostgREST syntax](https://postgrest.org/en/stable/api.html#horizontal-filtering-rows) for the filter names and values.

        ```dart
        .not('name','eq','violin')
        .not('arraycol','cs','{"a","b"}') // Use Postgres array {} for array column and 'cs' for contains.
        .not('rangecol','cs','(1,2]') // Use Postgres range syntax for range column.
        .not('id','in','(6,7)')  // Use Postgres list () and 'in' instead of `inFilter`.
        .not('id','in','(${mylist.join(',')})')  // You can insert a Dart list array.
        ```
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: operator
        isOptional: false
        type: String
        description: The operator to be negated to filter with, following PostgREST syntax.
      - name: value
        isOptional: true
        type: Object
        description: The value to filter with, following PostgREST syntax.
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: false
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .select()
            .not('name', 'is', null)
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'null'),
              (2, null);
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'null'
            }
          ]
          ```
      - id: with-update
        name: With `update()`
        code: |
          ```dart
          final data = await supabase
            .from('cities')
            .update({ 'name': 'Mordor' })
            .not('name', 'eq', 'Rohan');
          ```
      - id: with-delete
        name: With `delete()`
        code: |
          ```dart
          final data = await supabase
            .from('cities')
            .delete()
            .not('name', 'eq', 'Mordor');
          ```
      - id: with-rpc
        name: With `rpc()`
        code: |
          ```dart
          // Only valid if the Stored Procedure returns a table type.
          final data = await supabase
            .rpc('echo_all_cities')
            .not('name', 'eq', 'Mordor');
          ```

  - id: match
    title: match()
    description: |
      Finds all rows whose columns match the specified `query` object.
    params:
      - name: query
        isOptional: false
        type: Map<String, dynamic>
        description: The object to filter with, with column names as keys mapped to their filter values
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select()
            .match({ 'id': 2, 'name': 'viola' });
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'name': 'viola'
            }
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: eq
    title: eq()
    description: |
      Match only rows where `column` is equal to `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select()
            .eq('name', 'viola');
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'name': 'viola'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: neq
    title: neq()
    description: |
      Finds all rows whose value on the stated `column` doesn't match the specified `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .select('id, name')
            .neq('name', 'viola');
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'violin'),
              (2, 'viola'),
              (3, 'cello');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'violin'
            },
            {
              'id': 3,
              'name': 'cello'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: gt
    title: gt()
    description: |
      Finds all rows whose value on the stated `column` is greater than the specified `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .select()
            .gt('id', 2);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Rohan'),
              (2, 'The Shire'),
              (3, 'Mordor');
            ```
        response: |
          ```json
          [
            {
              'id': 3,
              'name': 'Mordor'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: gte
    title: gte()
    description: |
      Finds all rows whose value on the stated `column` is greater than or equal to the specified `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .select()
            .gte('id', 2);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Rohan'),
              (2, 'The Shire'),
              (3, 'Mordor');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'name': 'The Shire'
            },
            {
              'id': 3,
              'name': 'Mordor'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: lt
    title: lt()
    description: |
      Finds all rows whose value on the stated `column` is less than the specified `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .select()
            .lt('id', 2);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Rohan'),
              (2, 'The Shire'),
              (3, 'Mordor');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'Rohan'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: lte
    title: lte()
    description: |
      Finds all rows whose value on the stated `column` is less than or equal to the specified `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .select()
            .lte('id', 2);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Rohan'),
              (2, 'The Shire'),
              (3, 'Mordor');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'Rohan'
            },
            {
              'id': 2,
              'name': 'The Shire'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: like
    title: like()
    description: |
      Finds all rows whose value in the stated `column` matches the supplied `pattern` (case sensitive).
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: pattern
        isOptional: false
        type: String
        description: The pattern to match with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('planets')
            .select()
            .like('name', '%Ea%');
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'name': 'Earth'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: ilike
    title: ilike()
    description: |
      Finds all rows whose value in the stated `column` matches the supplied `pattern` (case insensitive).
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: pattern
        isOptional: false
        type: String
        description: The pattern to match with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('planets')
            .select()
            .ilike('name', '%ea%');
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'name': 'Earth'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: is
    title: isFilter()
    description: |
      A check for exact equality (null, true, false), finds all rows whose value on the stated `column` exactly match the specified `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: value
        isOptional: false
        type: Object?
        description: The value to filter with.
    examples:
      - id: checking-nullness
        name: Checking for nullness, true or false
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .select()
            .isFilter('name', null);
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'null'),
              (2, null);
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'name': 'null'
            },
          ]
          ```
        description: |
          Using the `eq()` filter doesn't work when filtering for `null`.

          Instead, you need to use `isFilter()`.

          `isFilter()` is equivalent to `is()` in SDKs for other languages. It's named `isFilter()` in Dart to avoid a conflict with the `is` keyword in Dart.
        hideCodeBlock: true
        isSpotlight: true

  - id: in
    title: inFilter()
    description: |
      Finds all rows whose value on the stated `column` is found on the specified `values`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: values
        isOptional: false
        type: List
        description: The List to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('characters')
            .select()
            .inFilter('name', ['Luke', 'Leia']);
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Han'),
              (2, 'Luke'),
              (3, 'Leia');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'name': 'Luke'
            },
            {
              'id': 3,
              'name': 'Leia'
            },
          ]
          ```
        description: |
          `inFilter()` is equivalent to `in()` in SDKs for other languages. It's named `inFilter()` in Dart to avoid a conflict with the `in` keyword in Dart.
        hideCodeBlock: true
        isSpotlight: true

  - id: contains
    title: contains()
    description: Only relevant for jsonb, array, and range columns. Match only rows where `column` contains every element appearing in `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The jsonb, array, or range column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The jsonb, array, or range value to filter with.
    examples:
      - id: on-array-columns
        name: On array columns
        description: |
          Only relevant for jsonb, array, and range columns. Match only rows where `column` contains every element appearing in `value`.
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('issues')
            .select()
            .contains('tags', ['is:open', 'priority:low']);
          ```
        data:
          sql: |
            ```sql
            create table
              issues (
                id int8 primary key,
                title text,
                tags text[]
              );

            insert into
              issues (id, title, tags)
            values
              (1, 'Cache invalidation is not working', array['is:open', 'severity:high', 'priority:low']),
              (2, 'Use better names', array['is:open', 'severity:low', 'priority:medium']);
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'title': 'Cache invalidation is not working',
              'tags': ['is:open', 'severity:high', 'priority:low']
            },
          ]
          ```
        hideCodeBlock: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .contains('during', '[2000-01-01 13:00, 2000-01-01 13:30)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'room_name': 'Emerald',
              'during': '["2000-01-01 13:00:00","2000-01-01 15:00:00")'
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
      - id: on-jsonb-columns
        name: On `jsonb` columns
        code: |
          ```dart
          final data = await supabase
            .from('users')
            .select('name')
            .contains('address', { 'street': 'Melrose Place' });
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210, "street": "Melrose Place" }'),
              (2, 'Jane', '{"postcode": 90210}');
            ```
        response: |
          ```json
          [
            {
              'name': 'Michael'
            },
          ]
          ```
        hideCodeBlock: true

  - id: contained-by
    title: containedBy()
    description: |
      Only relevant for jsonb, array, and range columns. Match only rows where every element appearing in `column` is contained by `value`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The jsonb, array, or range column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The jsonb, array, or range value to filter with.
    examples:
      - id: on-array-columns
        name: On array columns
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('classes')
            .select('name')
            .containedBy('days', ['monday', 'tuesday', 'wednesday', 'friday']);
          ```
        data:
          sql: |
            ```sql
            create table
              classes (
                id int8 primary key,
                name text,
                days text[]
              );

            insert into
              classes (id, name, days)
            values
              (1, 'Chemistry', array['monday', 'friday']),
              (2, 'History', array['monday', 'wednesday', 'thursday']);
            ```
        response: |
          ```json
          [
            {
              'name': 'Chemistry'
            },
          ]
          ```
        hideCodeBlock: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .containedBy('during', '[2000-01-01 00:00, 2000-01-01 23:59)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'room_name': 'Emerald',
              'during': '["2000-01-01 13:00:00","2000-01-01 15:00:00")'
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
      - id: on-jsonb-columns
        name: On `jsonb` columns
        code: |
          ```dart
          final data = await supabase
            .from('users')
            .select('name')
            .containedBy('address', {'postcode': 90210});
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210, "street": "Melrose Place" }'),
              (2, 'Jane', '{"postcode": 90210}');
            ```
        response: |
          ```json
            [
              {
                'name': 'Jane'
              },
            ]
            ```
        hideCodeBlock: true

  - id: range-lt
    title: rangeLt()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is less than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The range column to filter on.
      - name: range
        isOptional: false
        type: String
        description: The range to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .rangeLt('during', '[2000-01-01 15:00, 2000-01-01 16:00)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'room_name': 'Emerald',
              'during': '["2000-01-01 13:00:00","2000-01-01 15:00:00")'
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-gt
    title: rangeGt()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is greater than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The range column to filter on.
      - name: range
        isOptional: false
        type: String
        description: The range to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .rangeGt('during', '[2000-01-02 08:00, 2000-01-02 09:00)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'room_name': 'Topaz',
              'during': '["2000-01-02 09:00:00","2000-01-02 10:00:00")'
            }
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-gte
    title: rangeGte()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is either contained in `range` or greater than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The range column to filter on.
      - name: range
        isOptional: false
        type: String
        description: The range to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .rangeGte('during', '[2000-01-02 08:30, 2000-01-02 09:30)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 2,
              'room_name': 'Topaz',
              'during': '["2000-01-02 09:00:00","2000-01-02 10:00:00")''
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-lte
    title: rangeLte()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is either contained in `range` or less than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: String
        description: The range column to filter on.
      - name: range
        isOptional: false
        type: String
        description: The range to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .rangeLte('during', '[2000-01-01 15:00, 2000-01-01 16:00)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'room_name': 'Emerald',
              'during': '["2000-01-01 13:00:00","2000-01-01 15:00:00")'
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-adjacent
    title: rangeAdjacent()
    description: |
      Only relevant for range columns. Match only rows where `column` is mutually exclusive to `range` and there can be no element between the two ranges.
    params:
      - name: column
        isOptional: false
        type: String
        description: The range column to filter on.
      - name: range
        isOptional: false
        type: String
        description: The range to filter with.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .rangeAdjacent('during', '[2000-01-01 12:00, 2000-01-01 13:00)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'room_name': 'Emerald',
              'during': '["2000-01-01 13:00:00","2000-01-01 15:00:00")'
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
        isSpotlight: true

  - id: overlaps
    title: overlaps()
    description: |
      Only relevant for array and range columns. Match only rows where `column` and `value` have an element in common.
    params:
      - name: column
        isOptional: false
        type: String
        description: The array or range column to filter on.
      - name: value
        isOptional: false
        type: Object
        description: The array or range value to filter with.
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```dart
          final data = await supabase
            .from('issues')
            .select('title')
            .overlaps('tags', ['is:closed', 'severity:high']);
          ```
        data:
          sql: |
            ```sql
            create table
              issues (
                id int8 primary key,
                title text,
                tags text[]
              );

            insert into
              issues (id, title, tags)
            values
              (1, 'Cache invalidation is not working', array['is:open', 'severity:high', 'priority:low']),
              (2, 'Use better names', array['is:open', 'severity:low', 'priority:medium']);
            ```
        response: |
          ```json
          [
            {
              'title': 'Cache invalidation is not working'
            },
          ]
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```dart
          final data = await supabase
            .from('reservations')
            .select()
            .overlaps('during', '[2000-01-01 12:45, 2000-01-01 13:15)');
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          [
            {
              'id': 1,
              'room_name': 'Emerald',
              'during': '["2000-01-01 13:00:00","2000-01-01 15:00:00")'
            },
          ]
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true

  - id: text-search
    title: textSearch()
    description: |
      Finds all rows whose tsvector value on the stated `column` matches to_tsquery(query).
    params:
      - name: column
        isOptional: false
        type: String
        description: The text or tsvector column to filter on.
      - name: query
        isOptional: false
        type: String
        description: The query text to match with.
      - name: config
        isOptional: true
        type: String
        description: The text search configuration to use.
      - name: type
        isOptional: true
        type: TextSearchType
        description: Change how the `query` text is interpreted.
    examples:
      - id: text-search
        name: Text search
        code: |
          ```dart
          final data = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('content', "'eggs' & 'ham'",
              config: 'english'
            );
          ```
        data:
          sql: |
            ```sql
            create table texts (
              id      bigint
                      primary key
                      generated always as identity,
              content text
            );

            insert into texts (content) values
                ('Four score and seven years ago'),
                ('The road goes ever on and on'),
                ('Green eggs and ham')
            ;
            ```
        response: |
          ```json
          [
            {
              'content': 'Green eggs and ham'
            }
          ]
          ```
      - id: basic-normalization
        name: Basic normalization
        description: Uses PostgreSQL's `plainto_tsquery` function.
        code: |
          ```dart
          final data = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('catchphrase', "'fat' & 'cat'",
              type: TextSearchType.plain,
              config: 'english'
            );
          ```
      - id: full-normalization
        name: Full normalization
        description: Uses PostgreSQL's `phraseto_tsquery` function.
        code: |
          ```dart
          final data = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('catchphrase', "'fat' & 'cat'",
              type: TextSearchType.phrase,
              config: 'english'
            );
          ```
      - id: web-search
        name: Websearch
        description: |
          Uses PostgreSQL's `websearch_to_tsquery` function.
          This function will never raise syntax errors, which makes it possible to use raw user-supplied input for search, and can be used
          with advanced operators.

          - `unquoted text`: text not inside quote marks will be converted to terms separated by & operators, as if processed by plainto_tsquery.
          - `"quoted text"`: text inside quote marks will be converted to terms separated by `<->` operators, as if processed by phraseto_tsquery.
          - `OR`: the word “or” will be converted to the | operator.
          - `-`: a dash will be converted to the ! operator.

        code: |
          ```dart
          final data = await supabase
            .from('quotes')
            .select('catchphrase')
            .textSearch('catchphrase', "'fat or cat'",
              type: TextSearchType.websearch,
              config: 'english'
            );
          ```

  - id: filter
    title: filter()
    description: |
      Match only rows which satisfy the filter. This is an escape hatch - you should use the specific filter methods wherever possible.
    params:
      - name: column
        isOptional: false
        type: String
        description: The column to filter on.
      - name: operator
        isOptional: false
        type: String
        description: The operator to filter with, following PostgREST syntax.
      - name: value
        isOptional: false
        type: Object
        description: The value to filter with, following PostgREST syntax.
    notes: |
      `.filter()` expects you to use the raw [PostgREST syntax](https://postgrest.org/en/stable/api.html#horizontal-filtering-rows) for the filter names and values, so it should only be used as an escape hatch in case other filters don't work.
      ```dart
      .filter('arraycol','cs','{"a","b"}') // Use Postgres array {} and 'cs' for contains.
      .filter('rangecol','cs','(1,2]') // Use Postgres range syntax for range column.
      .filter('id','in','(6,7)')  // Use Postgres list () and 'in' for in_ filter.
      .filter('id','cs','{${mylist.join(',')}}')  // You can insert a Dart array list.
      ```
    examples:
      - id: with-select
        name: With `select()`
        isSpotlight: true
        code: |
          ```dart
          final data = await supabase
            .from('characters')
            .select()
            .filter('name', 'in', '("Ron","Dumbledore")')
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Harry'),
              (2, 'Hermione'),
              (3, 'Ron');
            ```
        response: |
          ```json
          [
            {
              'id': 3,
              'name': 'Ron'
            }
          ]
          ```
      - id: with-update
        name: With `update()`
        code: |
          ```dart
          final data = await supabase
            .from('instruments')
            .update({ 'name': 'piano' })
            .filter('name', 'in', '("harpsichord","clavichord")');
          ```
      - id: with-delete
        name: With `delete()`
        code: |
          ```dart
          final data = await supabase
            .from('countries')
            .delete()
            .filter('name', 'in', '("Rohan","Mordor")');
          ```
      - id: with-rpc
        name: With `rpc()`
        code: |
          ```dart
          // Only valid if the Stored Procedure returns a table type.
          final data = await supabase
            .rpc('echo_all_countries')
            .filter('name', 'in', '("Rohan","Mordor")');
          ```
      - id: on-a-referenced-table
        name: On a referenced table
        code: |
          ```dart
          final data = await supabase
            .from('orchestral_sections')
            .select('''
              name,
              instruments!inner (
                name
              )
            ''')
            .filter('characters.name', 'eq', 'flute')
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          [
            {
              'name': 'woodwinds',
              'instruments': [
                {
                  'name': 'flute'
                }
              ]
            }
          ]
          ```
        hideCodeBlock: true
