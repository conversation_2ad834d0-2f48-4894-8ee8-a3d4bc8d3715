{"openapi": "3.0.0", "paths": {"/v1/branches/{branch_id}": {"get": {"operationId": "v1-get-a-branch-config", "summary": "Get database branch config", "description": "Fetches configurations of the specified database branch", "parameters": [{"name": "branch_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchDetailResponse"}}}}, "500": {"description": "Failed to retrieve database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}, "patch": {"operationId": "v1-update-a-branch-config", "summary": "Update database branch config", "description": "Updates the configuration of the specified database branch", "parameters": [{"name": "branch_id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBranchBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchResponse"}}}}, "500": {"description": "Failed to update database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-delete-a-branch", "summary": "Delete a database branch", "description": "Deletes the specified database branch", "parameters": [{"name": "branch_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchDeleteResponse"}}}}, "500": {"description": "Failed to delete database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}}, "/v1/branches/{branch_id}/push": {"post": {"operationId": "v1-push-a-branch", "summary": "Pushes a database branch", "description": "Pushes the specified database branch", "parameters": [{"name": "branch_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchUpdateResponse"}}}}, "500": {"description": "Failed to push database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}}, "/v1/branches/{branch_id}/merge": {"post": {"operationId": "v1-merge-a-branch", "summary": "Merges a database branch", "description": "Merges the specified database branch", "parameters": [{"name": "branch_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchUpdateResponse"}}}}, "500": {"description": "Failed to merge database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}}, "/v1/branches/{branch_id}/reset": {"post": {"operationId": "v1-reset-a-branch", "summary": "Resets a database branch", "description": "Resets the specified database branch", "parameters": [{"name": "branch_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchUpdateResponse"}}}}, "500": {"description": "Failed to reset database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}}, "/v1/projects": {"get": {"operationId": "v1-list-all-projects", "summary": "List all projects", "description": "Returns a list of all projects you've previously created.", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/V1ProjectWithDatabaseResponse"}}}}}}, "tags": ["Projects"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-create-a-project", "summary": "Create a project", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1CreateProjectBodyDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ProjectResponse"}}}}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/organizations": {"get": {"operationId": "v1-list-all-organizations", "summary": "List all organizations", "description": "Returns a list of organizations that you currently belong to.", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrganizationResponseV1"}}}}}, "500": {"description": "Unexpected error listing organizations"}}, "tags": ["Organizations"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-create-an-organization", "summary": "Create an organization", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationV1Dto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrganizationResponseV1"}}}}, "500": {"description": "Unexpected error creating an organization"}}, "tags": ["Organizations"], "security": [{"bearer": []}]}}, "/v1/oauth/authorize": {"get": {"operationId": "v1-authorize-user", "summary": "[Beta] Authorize user through oauth", "parameters": [{"name": "code_challenge_method", "required": false, "in": "query", "schema": {"enum": ["plain", "sha256", "S256"], "type": "string"}}, {"name": "code_challenge", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "state", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "response_type", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "redirect_uri", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "client_id", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"303": {"description": ""}}, "tags": ["OAuth"], "security": [{"oauth2": ["read"]}]}}, "/v1/oauth/token": {"post": {"operationId": "v1-exchange-oauth-token", "summary": "[Beta] Exchange auth code for user's access and refresh token", "parameters": [], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/OAuthTokenBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OAuthTokenResponse"}}}}}, "tags": ["OAuth"], "security": [{"oauth2": ["write"]}]}}, "/v1/oauth/revoke": {"post": {"operationId": "v1-revoke-token", "summary": "[Beta] Revoke oauth app authorization and it's corresponding tokens", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OAuthRevokeTokenBodyDto"}}}}, "responses": {"204": {"description": ""}}, "tags": ["OAuth"], "security": [{"oauth2": ["write"]}]}}, "/v1/snippets": {"get": {"operationId": "v1-list-all-snippets", "summary": "Lists SQL snippets for the logged in user", "parameters": [{"name": "cursor", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "limit", "required": false, "in": "query", "schema": {"type": "string", "minimum": 1, "maximum": 100}}, {"name": "sort_by", "required": false, "in": "query", "schema": {"enum": ["name", "inserted_at"], "type": "string"}}, {"name": "sort_order", "required": false, "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "project_ref", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnippetList"}}}}, "500": {"description": "Failed to list user's SQL snippets"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/snippets/{id}": {"get": {"operationId": "v1-get-a-snippet", "summary": "Gets a specific SQL snippet", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"format": "uuid", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SnippetResponse"}}}}, "500": {"description": "Failed to retrieve SQL snippet"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/api-keys": {"get": {"operationId": "v1-get-project-api-keys", "summary": "Get project api keys", "parameters": [{"name": "reveal", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}}, "403": {"description": ""}}, "tags": ["Secrets"], "security": [{"bearer": []}]}, "post": {"operationId": "createApiKey", "summary": "[Alpha] Creates a new API key for the project", "parameters": [{"name": "reveal", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApiKeyBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["Secrets"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/api-keys/{id}": {"patch": {"operationId": "updateApiKey", "summary": "[Alpha] Updates an API key for the project", "parameters": [{"name": "reveal", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateApiKeyBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["Secrets"], "security": [{"bearer": []}]}, "get": {"operationId": "getApi<PERSON>ey", "summary": "[Alpha] Get API key", "parameters": [{"name": "reveal", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["Secrets"], "security": [{"bearer": []}]}, "delete": {"operationId": "deleteApiKey", "summary": "[Alpha] Deletes an API key for the project", "parameters": [{"name": "reveal", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["Secrets"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/branches": {"get": {"operationId": "v1-list-all-branches", "summary": "List all database branches", "description": "Returns all database branches of the specified project.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BranchResponse"}}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve database branches"}}, "tags": ["Environments"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-create-a-branch", "summary": "Create a database branch", "description": "Creates a database branch from the specified project.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBranchBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BranchResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to create database branch"}}, "tags": ["Environments"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-disable-preview-branching", "summary": "Disables preview branching", "description": "Disables preview branching for the specified project", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to disable preview branching"}}, "tags": ["Environments"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/custom-hostname": {"get": {"operationId": "v1-get-hostname-config", "summary": "[Beta] Gets project's custom hostname config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomHostnameResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's custom hostname config"}}, "tags": ["Domains"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-Delete hostname config", "summary": "[Beta] Deletes a project's custom hostname configuration", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to delete project custom hostname configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/custom-hostname/initialize": {"post": {"operationId": "v1-update-hostname-config", "summary": "[Beta] Updates project's custom hostname configuration", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomHostnameBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomHostnameResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project custom hostname configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/custom-hostname/reverify": {"post": {"operationId": "v1-verify-dns-config", "summary": "[Beta] Attempts to verify the DNS configuration for project's custom hostname configuration", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomHostnameResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to verify project custom hostname configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/custom-hostname/activate": {"post": {"operationId": "v1-activate-custom-hostname", "summary": "[Beta] Activates a custom hostname for a project.", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomHostnameResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to activate project custom hostname configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/network-bans/retrieve": {"post": {"operationId": "v1-list-all-network-bans", "summary": "[Beta] Gets project's network bans", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NetworkBanResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's network bans"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/network-bans": {"delete": {"operationId": "v1-delete-network-bans", "summary": "[Beta] Remove network bans.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveNetworkBanRequest"}}}}, "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to remove network bans."}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/network-restrictions": {"get": {"operationId": "v1-get-network-restrictions", "summary": "[Beta] Gets project's network restrictions", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NetworkRestrictionsResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's network restrictions"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/network-restrictions/apply": {"post": {"operationId": "v1-update-network-restrictions", "summary": "[Beta] Updates project's network restrictions", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NetworkRestrictionsRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NetworkRestrictionsResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project network restrictions"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/pgsodium": {"get": {"operationId": "v1-get-pgsodium-config", "summary": "[Beta] Gets project's pgsodium config", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PgsodiumConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's pgsodium config"}}, "tags": ["Secrets"], "security": [{"bearer": []}]}, "put": {"operationId": "v1-update-pgsodium-config", "summary": "[Beta] Updates project's pgsodium config. Updating the root_key can cause all data encrypted with the older key to become inaccessible.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePgsodiumConfigBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PgsodiumConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project's pgsodium config"}}, "tags": ["Secrets"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/postgrest": {"get": {"operationId": "v1-get-postgrest-service-config", "summary": "Gets project's postgrest config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostgrestConfigWithJWTSecretResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's postgrest config"}}, "tags": ["Rest"], "security": [{"bearer": []}]}, "patch": {"operationId": "v1-update-postgrest-service-config", "summary": "Updates project's postgrest config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1UpdatePostgrestConfigBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1PostgrestConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project's postgrest config"}}, "tags": ["Rest"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}": {"get": {"operationId": "v1-get-project", "summary": "Gets a specific project that belongs to the authenticated user", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ProjectWithDatabaseResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project"}}, "tags": ["Projects"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-delete-a-project", "summary": "Deletes the given project", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ProjectRefResponse"}}}}, "403": {"description": ""}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/secrets": {"get": {"operationId": "v1-list-all-secrets", "summary": "List all secrets", "description": "Returns all secrets you've previously added to the specified project.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SecretResponse"}}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's secrets"}}, "tags": ["Secrets"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-bulk-create-secrets", "summary": "Bulk create secrets", "description": "Creates multiple secrets and adds them to the specified project.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateSecretBody"}}}}}, "responses": {"201": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to create project's secrets"}}, "tags": ["Secrets"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-bulk-delete-secrets", "summary": "Bulk delete secrets", "description": "Deletes all secrets with the given names from the specified project", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "403": {"description": ""}, "500": {"description": "Failed to delete secrets with given names"}}, "tags": ["Secrets"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/ssl-enforcement": {"get": {"operationId": "v1-get-ssl-enforcement-config", "summary": "[Beta] Get project's SSL enforcement configuration.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SslEnforcementResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's SSL enforcement config"}}, "tags": ["Database"], "security": [{"bearer": []}]}, "put": {"operationId": "v1-update-ssl-enforcement-config", "summary": "[Beta] Update project's SSL enforcement configuration.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SslEnforcementRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SslEnforcementResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project's SSL enforcement configuration."}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/types/typescript": {"get": {"operationId": "v1-generate-typescript-types", "summary": "Generate TypeScript types", "description": "Returns the TypeScript types of your schema for use with supabase-js.", "parameters": [{"name": "included_schemas", "required": false, "in": "query", "schema": {"default": "public", "type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TypescriptResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to generate TypeScript types"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/vanity-subdomain": {"get": {"operationId": "v1-get-vanity-subdomain-config", "summary": "[Beta] Gets current vanity subdomain config", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VanitySubdomainConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to get project vanity subdomain configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-deactivate-vanity-subdomain-config", "summary": "[Beta] Deletes a project's vanity subdomain configuration", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to delete project vanity subdomain configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/vanity-subdomain/check-availability": {"post": {"operationId": "v1-check-vanity-subdomain-availability", "summary": "[Beta] Checks vanity subdomain availability", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VanitySubdomainBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubdomainAvailabilityResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to check project vanity subdomain configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/vanity-subdomain/activate": {"post": {"operationId": "v1-activate-vanity-subdomain-config", "summary": "[Beta] Activates a vanity subdomain for a project.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VanitySubdomainBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivateVanitySubdomainResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to activate project vanity subdomain configuration"}}, "tags": ["Domains"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/upgrade": {"post": {"operationId": "v1-upgrade-postgres-version", "summary": "[Beta] Upgrades the project's Postgres version", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpgradeDatabaseBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectUpgradeInitiateResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to initiate project upgrade"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/upgrade/eligibility": {"get": {"operationId": "v1-get-postgres-upgrade-eligibility", "summary": "[Beta] Returns the project's eligibility for upgrades", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectUpgradeEligibilityResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to determine project upgrade eligibility"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/upgrade/status": {"get": {"operationId": "v1-get-postgres-upgrade-status", "summary": "[Beta] Gets the latest status of the project's upgrade", "parameters": [{"name": "tracking_id", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DatabaseUpgradeStatusResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project upgrade status"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/readonly": {"get": {"operationId": "v1-get-readonly-mode-status", "summary": "Returns project's readonly mode status", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadOnlyStatusResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to get project readonly mode status"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/readonly/temporary-disable": {"post": {"operationId": "v1-disable-readonly-mode-temporarily", "summary": "Disables project's readonly mode for the next 15 minutes", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"201": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to disable project's readonly mode"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/read-replicas/setup": {"post": {"operationId": "v1-setup-a-read-replica", "summary": "[Beta] Set up a read replica", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetUpReadReplicaBody"}}}}, "responses": {"201": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to set up read replica"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/read-replicas/remove": {"post": {"operationId": "v1-remove-a-read-replica", "summary": "[Beta] Remove a read replica", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveReadReplicaBody"}}}}, "responses": {"201": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to remove read replica"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/health": {"get": {"operationId": "v1-get-services-health", "summary": "Gets project's service health status", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}, {"name": "timeout_ms", "required": false, "in": "query", "schema": {"minimum": 0, "maximum": 10000, "type": "integer"}}, {"name": "services", "required": true, "in": "query", "schema": {"type": "array", "items": {"type": "string", "enum": ["auth", "db", "pooler", "realtime", "rest", "storage"]}}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/V1ServiceHealthResponse"}}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's service health status"}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth/signing-keys": {"post": {"operationId": "createSigningKeyForProject", "summary": "[Alpha] Create a new signing key for the project in standby status", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSigningKeyBody"}}}}, "responses": {"201": {"description": "[Alpha] Create a new signing key for the project in standby status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSigningKeyBody"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "get": {"operationId": "listSigningKeysForProject", "summary": "[Alpha] List all signing keys for the project", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigningKeysResponse"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth/signing-keys/{id}": {"get": {"operationId": "getSigningKeyForProject", "summary": "[Alpha] Get information about a signing key", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigningKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "delete": {"operationId": "deleteSigningKey", "summary": "[Alpha] Remove a signing key from a project, where the status is previously_used", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigningKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "patch": {"operationId": "patchSigningKey", "summary": "[Alpha] Update a signing key, mainly its status", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSigningKeyBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigningKeyResponse"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/storage": {"get": {"operationId": "v1-get-storage-config", "summary": "Gets project's storage config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StorageConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's storage config"}}, "tags": ["Storage"], "security": [{"bearer": []}]}, "patch": {"operationId": "v1-update-storage-config", "summary": "Updates project's storage config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStorageConfigBody"}}}}, "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to update project's storage config"}}, "tags": ["Storage"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/database/postgres": {"get": {"operationId": "v1-get-postgres-config", "summary": "Gets project's Postgres config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostgresConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's Postgres config"}}, "tags": ["Database"], "security": [{"bearer": []}]}, "put": {"operationId": "v1-update-postgres-config", "summary": "Updates project's Postgres config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePostgresConfigBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostgresConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project's Postgres config"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/database/pgbouncer": {"get": {"operationId": "v1-get-project-pgbouncer-config", "summary": "Get project's pgbouncer config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1PgbouncerConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's pgbouncer config"}}, "tags": ["Database"]}}, "/v1/projects/{ref}/config/database/pooler": {"get": {"operationId": "getSupavisorConfig", "summary": "Gets project's supavisor config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupavisorConfigResponse"}}}}}, "500": {"description": "Failed to retrieve project's supavisor config"}}, "tags": ["Database"], "security": [{"bearer": []}]}, "patch": {"operationId": "updateSupavisorConfig", "summary": "Updates project's supavisor config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSupavisorConfigBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSupavisorConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project's supavisor config"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth": {"get": {"operationId": "v1-get-auth-service-config", "summary": "Gets project's auth config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's auth config"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "patch": {"operationId": "v1-update-auth-service-config", "summary": "Updates a project's auth config", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAuthConfigBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthConfigResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update project's auth config"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth/third-party-auth": {"post": {"operationId": "createTPAForProject", "summary": "Creates a new third-party auth integration", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateThirdPartyAuthBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThirdPartyAuth"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "get": {"operationId": "listTPAForProject", "summary": "Lists all third-party auth integrations", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ThirdPartyAuth"}}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth/third-party-auth/{tpa_id}": {"delete": {"operationId": "deleteTPAForProject", "summary": "Removes a third-party auth integration", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}, {"name": "tpa_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThirdPartyAuth"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "get": {"operationId": "getTPAForProject", "summary": "Get a third-party integration", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}, {"name": "tpa_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThirdPartyAuth"}}}}, "403": {"description": ""}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/pause": {"post": {"operationId": "v1-pause-a-project", "summary": "Pauses the given project", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/restore": {"get": {"operationId": "v1-list-available-restore-versions", "summary": "Lists available restore versions for the given project", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetProjectAvailableRestoreVersionsResponse"}}}}, "403": {"description": ""}}, "tags": ["Projects"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-restore-a-project", "summary": "Restores the given project", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RestoreProjectBodyDto"}}}}, "responses": {"200": {"description": ""}, "403": {"description": ""}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/restore/cancel": {"post": {"operationId": "v1-cancel-a-project-restoration", "summary": "Cancels the given project restoration", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}}, "tags": ["Projects"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/billing/addons": {"get": {"operationId": "v1-list-project-addons", "summary": "Lists project addons", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListProjectAddonsResponseDto"}}}}, "403": {"description": ""}, "500": {"description": "Failed to list project addons"}}, "security": [{"bearer": []}]}, "patch": {"operationId": "v1-apply-project-addon", "summary": "Applies project addon", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplyProjectAddonBodyDto"}}}}, "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to apply project addon"}}, "security": [{"bearer": []}]}}, "/v1/projects/{ref}/billing/addons/{addon_variant}": {"delete": {"operationId": "v1-remove-project-addon", "summary": "Removes project addon", "parameters": [{"name": "addon_variant", "required": true, "in": "path", "schema": {"enum": ["ci_micro", "ci_small", "ci_medium", "ci_large", "ci_xlarge", "ci_2xlarge", "ci_4xlarge", "ci_8xlarge", "ci_12xlarge", "ci_16xlarge", "cd_default", "pitr_7", "pitr_14", "pitr_28", "ipv4_default"], "type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to remove project addon"}}, "security": [{"bearer": []}]}}, "/v1/projects/{ref}/analytics/endpoints/logs.all": {"get": {"operationId": "getLogs", "summary": "Gets project's logs", "parameters": [{"name": "iso_timestamp_end", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "iso_timestamp_start", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "sql", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1AnalyticsResponse"}}}}, "403": {"description": ""}}, "tags": ["Analytics"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/database/migrations": {"get": {"operationId": "v1-list-migrations", "summary": "[Beta] List applied migration versions", "description": "Only available to selected partner OAuth apps", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ListMigrationsResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to list database migrations"}}, "tags": ["Database"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-apply-a-migration", "summary": "[Beta] Apply a database migration", "description": "Only available to selected partner OAuth apps", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "403": {"description": ""}, "500": {"description": "Failed to apply database migration"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/database/query": {"post": {"operationId": "v1-run-a-query", "summary": "[Beta] Run sql query", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1RunQueryBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "403": {"description": ""}, "500": {"description": "Failed to run sql query"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/database/webhooks/enable": {"post": {"operationId": "v1-enable-database-webhook", "summary": "[Beta] Enables Database Webhooks on the project", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"201": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to enable Database Webhooks on the project"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/database/context": {"get": {"operationId": "getDatabaseMetadata", "summary": "Gets database metadata for the given project.", "description": "This is an **experimental** endpoint. It is subject to change or removal in future versions. Use it with caution, as it may not remain supported or stable.", "deprecated": true, "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetProjectDbMetadataResponseDto"}}}}, "403": {"description": ""}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/functions": {"get": {"operationId": "v1-list-all-functions", "summary": "List all functions", "description": "Returns all functions you've previously added to the specified project.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FunctionResponse"}}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve project's functions"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}, "post": {"operationId": "v1-create-a-function", "summary": "Create a function", "description": "This endpoint is deprecated - use the deploy endpoint. Creates a function and adds it to the specified project.", "deprecated": true, "parameters": [{"name": "import_map_path", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "entrypoint_path", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "import_map", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "verify_jwt", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "slug", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1CreateFunctionBody"}}, "application/vnd.denoland.eszip": {"schema": {"$ref": "#/components/schemas/V1CreateFunctionBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FunctionResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to create project's function"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}, "put": {"operationId": "v1-bulk-update-functions", "summary": "Bulk update functions", "description": "Bulk update functions. It will create a new function or replace existing. The operation is idempotent. NOTE: You will need to manually bump the version.", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BulkUpdateFunctionBody"}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateFunctionResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update functions"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/functions/deploy": {"post": {"operationId": "v1-deploy-a-function", "summary": "Deploy a function", "description": "A new endpoint to deploy functions. It will create if function does not exist.", "parameters": [{"name": "bundleOnly", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "slug", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/FunctionDeployBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeployFunctionResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to deploy function"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/functions/{function_slug}": {"get": {"operationId": "v1-get-a-function", "summary": "Retrieve a function", "description": "Retrieves a function with the specified slug and project.", "parameters": [{"name": "function_slug", "required": true, "in": "path", "schema": {"pattern": "^[A-Za-z0-9_-]+$", "type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FunctionSlugResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to retrieve function with given slug"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}, "patch": {"operationId": "v1-update-a-function", "summary": "Update a function", "description": "Updates a function with the specified slug and project.", "parameters": [{"name": "import_map_path", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "entrypoint_path", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "import_map", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "verify_jwt", "required": false, "in": "query", "schema": {"type": "boolean"}}, {"name": "name", "required": false, "in": "query", "schema": {"type": "string"}}, {"name": "function_slug", "required": true, "in": "path", "schema": {"pattern": "^[A-Za-z0-9_-]+$", "type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1UpdateFunctionBody"}}, "application/vnd.denoland.eszip": {"schema": {"$ref": "#/components/schemas/V1UpdateFunctionBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FunctionResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to update function with given slug"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-delete-a-function", "summary": "Delete a function", "description": "Deletes a function with the specified slug from the specified project.", "parameters": [{"name": "function_slug", "required": true, "in": "path", "schema": {"pattern": "^[A-Za-z0-9_-]+$", "type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to delete function with given slug"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/functions/{function_slug}/body": {"get": {"operationId": "v1-get-a-function-body", "summary": "Retrieve a function body", "description": "Retrieves a function body for the specified slug and project.", "parameters": [{"name": "function_slug", "required": true, "in": "path", "schema": {"pattern": "^[A-Za-z0-9_-]+$", "type": "string"}}, {"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "403": {"description": ""}, "500": {"description": "Failed to retrieve function body with given slug"}}, "tags": ["Edge Functions"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/storage/buckets": {"get": {"operationId": "v1-list-all-buckets", "summary": "Lists all buckets", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/V1StorageBucketResponse"}}}}}, "403": {"description": ""}, "500": {"description": "Failed to get list of buckets"}}, "tags": ["Storage"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth/sso/providers": {"post": {"operationId": "v1-create-a-sso-provider", "summary": "Creates a new SSO provider", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProviderBody"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProviderResponse"}}}}, "403": {"description": ""}, "404": {"description": "SAML 2.0 support is not enabled for this project"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "get": {"operationId": "v1-list-all-sso-provider", "summary": "Lists all SSO providers", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListProvidersResponse"}}}}, "403": {"description": ""}, "404": {"description": "SAML 2.0 support is not enabled for this project"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/config/auth/sso/providers/{provider_id}": {"get": {"operationId": "v1-get-a-sso-provider", "summary": "Gets a SSO provider by its UUID", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}, {"name": "provider_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetProviderResponse"}}}}, "403": {"description": ""}, "404": {"description": "Either SAML 2.0 was not enabled for this project, or the provider does not exist"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "put": {"operationId": "v1-update-a-sso-provider", "summary": "Updates a SSO provider by its UUID", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}, {"name": "provider_id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProviderBody"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProviderResponse"}}}}, "403": {"description": ""}, "404": {"description": "Either SAML 2.0 was not enabled for this project, or the provider does not exist"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}, "delete": {"operationId": "v1-delete-a-sso-provider", "summary": "Removes a SSO provider by its UUID", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}, {"name": "provider_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteProviderResponse"}}}}, "403": {"description": ""}, "404": {"description": "Either SAML 2.0 was not enabled for this project, or the provider does not exist"}}, "tags": ["<PERSON><PERSON>"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/database/backups": {"get": {"operationId": "v1-list-all-backups", "summary": "Lists all backups", "parameters": [{"name": "ref", "required": true, "in": "path", "description": "Project ref", "schema": {"minLength": 20, "maxLength": 20, "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1BackupsResponse"}}}}, "403": {"description": ""}, "500": {"description": "Failed to get backups"}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/projects/{ref}/database/backups/restore-pitr": {"post": {"operationId": "v1-restore-pitr-backup", "summary": "Restores a PITR backup for a database", "parameters": [{"name": "ref", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1RestorePitrBody"}}}}, "responses": {"201": {"description": ""}, "403": {"description": ""}}, "tags": ["Database"], "security": [{"bearer": []}]}}, "/v1/organizations/{slug}/members": {"get": {"operationId": "v1-list-organization-members", "summary": "List members of an organization", "parameters": [{"name": "slug", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/V1OrganizationMemberResponse"}}}}}, "403": {"description": ""}}, "tags": ["Organizations"], "security": [{"bearer": []}]}}, "/v1/organizations/{slug}": {"get": {"operationId": "v1-get-an-organization", "summary": "Gets information about the organization", "parameters": [{"name": "slug", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1OrganizationSlugResponse"}}}}, "403": {"description": ""}}, "tags": ["Organizations"], "security": [{"bearer": []}]}}}, "info": {"title": "Supabase API (v1)", "description": "Supabase API generated from the OpenAPI specification.<br>Visit [https://supabase.com/docs](https://supabase.com/docs) for a complete documentation.", "version": "1.0.0", "contact": {}}, "tags": [{"name": "<PERSON><PERSON>", "description": "Auth related endpoints"}, {"name": "Database", "description": "Database related endpoints"}, {"name": "Domains", "description": "Domains related endpoints"}, {"name": "Edge Functions", "description": "Edge related endpoints"}, {"name": "Environments", "description": "Environments related endpoints"}, {"name": "OAuth", "description": "OAuth related endpoints"}, {"name": "Organizations", "description": "Organizations related endpoints"}, {"name": "Projects", "description": "Projects related endpoints"}, {"name": "Rest", "description": "Rest related endpoints"}, {"name": "Secrets", "description": "Secrets related endpoints"}, {"name": "Storage", "description": "Storage related endpoints"}], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"BranchDetailResponse": {"type": "object", "properties": {"ref": {"type": "string"}, "postgres_version": {"type": "string"}, "postgres_engine": {"type": "string"}, "release_channel": {"type": "string"}, "status": {"type": "string", "enum": ["INACTIVE", "ACTIVE_HEALTHY", "ACTIVE_UNHEALTHY", "COMING_UP", "UNKNOWN", "GOING_DOWN", "INIT_FAILED", "REMOVED", "RESTORING", "UPGRADING", "PAUSING", "RESTORE_FAILED", "RESTARTING", "PAUSE_FAILED", "RESIZING"]}, "db_host": {"type": "string"}, "db_port": {"type": "number"}, "db_user": {"type": "string"}, "db_pass": {"type": "string"}, "jwt_secret": {"type": "string"}}, "required": ["ref", "postgres_version", "postgres_engine", "release_channel", "status", "db_host", "db_port"]}, "UpdateBranchBody": {"type": "object", "properties": {"branch_name": {"type": "string"}, "git_branch": {"type": "string"}, "reset_on_push": {"type": "boolean", "description": "This field is deprecated and will be ignored. Use v1-reset-a-branch endpoint directly instead.", "deprecated": true}, "persistent": {"type": "boolean"}, "status": {"type": "string", "enum": ["CREATING_PROJECT", "RUNNING_MIGRATIONS", "MIGRATIONS_PASSED", "MIGRATIONS_FAILED", "FUNCTIONS_DEPLOYED", "FUNCTIONS_FAILED"]}}}, "BranchResponse": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "project_ref": {"type": "string"}, "parent_project_ref": {"type": "string"}, "is_default": {"type": "boolean"}, "git_branch": {"type": "string"}, "pr_number": {"type": "integer", "format": "int32"}, "latest_check_run_id": {"type": "number", "description": "This field is deprecated and will not be populated.", "deprecated": true}, "persistent": {"type": "boolean"}, "status": {"type": "string", "enum": ["CREATING_PROJECT", "RUNNING_MIGRATIONS", "MIGRATIONS_PASSED", "MIGRATIONS_FAILED", "FUNCTIONS_DEPLOYED", "FUNCTIONS_FAILED"]}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name", "project_ref", "parent_project_ref", "is_default", "persistent", "status", "created_at", "updated_at"]}, "BranchDeleteResponse": {"type": "object", "properties": {"message": {"type": "string", "enum": ["ok"]}}, "required": ["message"]}, "BranchUpdateResponse": {"type": "object", "properties": {"workflow_run_id": {"type": "string"}, "message": {"type": "string", "enum": ["ok"]}}, "required": ["workflow_run_id", "message"]}, "V1DatabaseResponse": {"type": "object", "properties": {"host": {"type": "string", "description": "Database host"}, "version": {"type": "string", "description": "Database version"}, "postgres_engine": {"type": "string", "description": "Database engine"}, "release_channel": {"type": "string", "description": "Release channel"}}, "required": ["host", "version", "postgres_engine", "release_channel"]}, "V1ProjectWithDatabaseResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of your project"}, "organization_id": {"type": "string", "description": "Slug of your organization"}, "name": {"type": "string", "description": "Name of your project"}, "region": {"type": "string", "description": "Region of your project", "example": "us-east-1"}, "created_at": {"type": "string", "description": "Creation timestamp", "example": "2023-03-29T16:32:59Z"}, "status": {"type": "string", "enum": ["INACTIVE", "ACTIVE_HEALTHY", "ACTIVE_UNHEALTHY", "COMING_UP", "UNKNOWN", "GOING_DOWN", "INIT_FAILED", "REMOVED", "RESTORING", "UPGRADING", "PAUSING", "RESTORE_FAILED", "RESTARTING", "PAUSE_FAILED", "RESIZING"]}, "database": {"$ref": "#/components/schemas/V1DatabaseResponse"}}, "required": ["id", "organization_id", "name", "region", "created_at", "status", "database"]}, "V1CreateProjectBodyDto": {"type": "object", "properties": {"db_pass": {"type": "string", "description": "Database password"}, "name": {"type": "string", "description": "Name of your project"}, "organization_id": {"type": "string", "description": "Slug of your organization"}, "plan": {"type": "string", "enum": ["free", "pro"], "deprecated": true, "description": "Subscription Plan is now set on organization level and is ignored in this request"}, "region": {"type": "string", "description": "Region you want your server to reside in", "enum": ["us-east-1", "us-east-2", "us-west-1", "us-west-2", "ap-east-1", "ap-southeast-1", "ap-northeast-1", "ap-northeast-2", "ap-southeast-2", "eu-west-1", "eu-west-2", "eu-west-3", "eu-north-1", "eu-central-1", "eu-central-2", "ca-central-1", "ap-south-1", "sa-east-1"]}, "kps_enabled": {"type": "boolean", "deprecated": true, "description": "This field is deprecated and is ignored in this request"}, "desired_instance_size": {"type": "string", "enum": ["micro", "small", "medium", "large", "xlarge", "2xlarge", "4xlarge", "8xlarge", "12xlarge", "16xlarge"]}, "template_url": {"type": "string", "format": "uri", "description": "Template URL used to create the project from the CLI.", "example": "https://github.com/supabase/supabase/tree/master/examples/slack-clone/nextjs-slack-clone"}}, "required": ["db_pass", "name", "organization_id", "region"], "additionalProperties": false, "hideDefinitions": ["release_channel", "postgres_engine"]}, "V1ProjectResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of your project"}, "organization_id": {"type": "string", "description": "Slug of your organization"}, "name": {"type": "string", "description": "Name of your project"}, "region": {"type": "string", "description": "Region of your project", "example": "us-east-1"}, "created_at": {"type": "string", "description": "Creation timestamp", "example": "2023-03-29T16:32:59Z"}, "status": {"type": "string", "enum": ["INACTIVE", "ACTIVE_HEALTHY", "ACTIVE_UNHEALTHY", "COMING_UP", "UNKNOWN", "GOING_DOWN", "INIT_FAILED", "REMOVED", "RESTORING", "UPGRADING", "PAUSING", "RESTORE_FAILED", "RESTARTING", "PAUSE_FAILED", "RESIZING"]}}, "required": ["id", "organization_id", "name", "region", "created_at", "status"]}, "OrganizationResponseV1": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "CreateOrganizationV1Dto": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": false}, "OAuthTokenBody": {"type": "object", "properties": {"grant_type": {"type": "string", "enum": ["authorization_code", "refresh_token"]}, "client_id": {"type": "string", "format": "uuid"}, "client_secret": {"type": "string"}, "code": {"type": "string"}, "code_verifier": {"type": "string"}, "redirect_uri": {"type": "string"}, "refresh_token": {"type": "string"}}, "additionalProperties": false}, "OAuthTokenResponse": {"type": "object", "properties": {"access_token": {"type": "string"}, "refresh_token": {"type": "string"}, "expires_in": {"type": "integer"}, "token_type": {"type": "string", "enum": ["Bearer"]}}, "required": ["access_token", "refresh_token", "expires_in", "token_type"], "additionalProperties": false}, "OAuthRevokeTokenBodyDto": {"type": "object", "properties": {"client_id": {"type": "string", "format": "uuid"}, "client_secret": {"type": "string"}, "refresh_token": {"type": "string"}}, "required": ["client_id", "client_secret", "refresh_token"], "additionalProperties": false}, "SnippetProject": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "SnippetUser": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string"}}, "required": ["id", "username"]}, "SnippetMeta": {"type": "object", "properties": {"id": {"type": "string"}, "inserted_at": {"type": "string"}, "updated_at": {"type": "string"}, "type": {"type": "string", "enum": ["sql"]}, "visibility": {"type": "string", "enum": ["user", "project", "org", "public"]}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/SnippetProject"}, "owner": {"$ref": "#/components/schemas/SnippetUser"}, "updated_by": {"$ref": "#/components/schemas/SnippetUser"}}, "required": ["id", "inserted_at", "updated_at", "type", "visibility", "name", "description", "project", "owner", "updated_by"]}, "SnippetList": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/SnippetMeta"}}, "cursor": {"type": "string"}}, "required": ["data"]}, "SnippetContent": {"type": "object", "properties": {"favorite": {"type": "boolean"}, "schema_version": {"type": "string"}, "sql": {"type": "string"}}, "required": ["favorite", "schema_version", "sql"]}, "SnippetResponse": {"type": "object", "properties": {"id": {"type": "string"}, "inserted_at": {"type": "string"}, "updated_at": {"type": "string"}, "type": {"type": "string", "enum": ["sql"]}, "visibility": {"enum": ["user", "project", "org", "public"], "type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "project": {"$ref": "#/components/schemas/SnippetProject"}, "owner": {"$ref": "#/components/schemas/SnippetUser"}, "updated_by": {"$ref": "#/components/schemas/SnippetUser"}, "content": {"$ref": "#/components/schemas/SnippetContent"}}, "required": ["id", "inserted_at", "updated_at", "type", "visibility", "name", "description", "project", "owner", "updated_by", "content"]}, "ApiKeyResponse": {"type": "object", "properties": {"name": {"type": "string"}, "api_key": {"type": "string"}, "id": {"type": "string", "nullable": true}, "type": {"type": "string", "enum": ["publishable", "secret", "legacy"], "nullable": true}, "prefix": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "hash": {"type": "string", "nullable": true}, "secret_jwt_template": {"type": "object", "properties": {"role": {"type": "string", "minLength": 1}}, "required": ["role"], "nullable": true}, "inserted_at": {"type": "string", "format": "date-time", "nullable": true}, "updated_at": {"type": "string", "format": "date-time", "nullable": true}}, "required": ["name", "api_key"]}, "CreateApiKeyBody": {"type": "object", "properties": {"type": {"type": "string", "enum": ["publishable", "secret"]}, "description": {"type": "string", "nullable": true}, "secret_jwt_template": {"type": "object", "properties": {"role": {"type": "string", "minLength": 1}}, "required": ["role"], "nullable": true}}, "required": ["type"]}, "UpdateApiKeyBody": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "secret_jwt_template": {"type": "object", "properties": {"role": {"type": "string", "minLength": 1}}, "required": ["role"], "nullable": true}}}, "CreateBranchBody": {"type": "object", "properties": {"branch_name": {"type": "string", "minLength": 1}, "git_branch": {"type": "string"}, "persistent": {"type": "boolean"}, "region": {"type": "string"}, "desired_instance_size": {"type": "string", "enum": ["pico", "nano", "micro", "small", "medium", "large", "xlarge", "2xlarge", "4xlarge", "8xlarge", "12xlarge", "16xlarge"]}, "release_channel": {"type": "string", "enum": ["internal", "alpha", "beta", "ga", "withdrawn", "preview"], "description": "Release channel. If not provided, GA will be used."}, "postgres_engine": {"type": "string", "enum": ["15", "17-oriole"], "description": "Postgres engine version. If not provided, the latest version will be used."}, "secrets": {"type": "object", "additionalProperties": {"type": "string"}}}, "required": ["branch_name"]}, "ValidationRecord": {"type": "object", "properties": {"txt_name": {"type": "string"}, "txt_value": {"type": "string"}}, "required": ["txt_name", "txt_value"]}, "ValidationError": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "SslValidation": {"type": "object", "properties": {"status": {"type": "string"}, "validation_records": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationRecord"}}, "validation_errors": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}}, "required": ["status", "validation_records"]}, "OwnershipVerification": {"type": "object", "properties": {"type": {"type": "string"}, "name": {"type": "string"}, "value": {"type": "string"}}, "required": ["type", "name", "value"]}, "CustomHostnameDetails": {"type": "object", "properties": {"id": {"type": "string"}, "hostname": {"type": "string"}, "ssl": {"$ref": "#/components/schemas/SslValidation"}, "ownership_verification": {"$ref": "#/components/schemas/OwnershipVerification"}, "custom_origin_server": {"type": "string"}, "verification_errors": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}}, "required": ["id", "hostname", "ssl", "ownership_verification", "custom_origin_server", "status"]}, "CfResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "object"}}, "messages": {"type": "array", "items": {"type": "object"}}, "result": {"$ref": "#/components/schemas/CustomHostnameDetails"}}, "required": ["success", "errors", "messages", "result"]}, "UpdateCustomHostnameResponse": {"type": "object", "properties": {"status": {"enum": ["1_not_started", "2_initiated", "3_challenge_verified", "4_origin_setup_completed", "5_services_reconfigured"], "type": "string"}, "custom_hostname": {"type": "string"}, "data": {"$ref": "#/components/schemas/CfResponse"}}, "required": ["status", "custom_hostname", "data"]}, "UpdateCustomHostnameBody": {"type": "object", "properties": {"custom_hostname": {"type": "string"}}, "required": ["custom_hostname"]}, "NetworkBanResponse": {"type": "object", "properties": {"banned_ipv4_addresses": {"type": "array", "items": {"type": "string"}}}, "required": ["banned_ipv4_addresses"]}, "RemoveNetworkBanRequest": {"type": "object", "properties": {"ipv4_addresses": {"type": "array", "items": {"type": "string"}}}, "required": ["ipv4_addresses"]}, "NetworkRestrictionsResponse": {"type": "object", "properties": {"entitlement": {"type": "string", "enum": ["disallowed", "allowed"]}, "config": {"type": "object", "properties": {"dbAllowedCidrs": {"type": "array", "items": {"type": "string"}}, "dbAllowedCidrsV6": {"type": "array", "items": {"type": "string"}}}, "description": "At any given point in time, this is the config that the user has requested be applied to their project. The `status` field indicates if it has been applied to the project, or is pending. When an updated config is received, the applied config is moved to `old_config`."}, "old_config": {"type": "object", "properties": {"dbAllowedCidrs": {"type": "array", "items": {"type": "string"}}, "dbAllowedCidrsV6": {"type": "array", "items": {"type": "string"}}}, "description": "Populated when a new config has been received, but not registered as successfully applied to a project."}, "status": {"type": "string", "enum": ["stored", "applied"]}}, "required": ["entitlement", "config", "status"]}, "NetworkRestrictionsRequest": {"type": "object", "properties": {"dbAllowedCidrs": {"type": "array", "items": {"type": "string"}}, "dbAllowedCidrsV6": {"type": "array", "items": {"type": "string"}}}}, "PgsodiumConfigResponse": {"type": "object", "properties": {"root_key": {"type": "string"}}, "required": ["root_key"]}, "UpdatePgsodiumConfigBody": {"type": "object", "properties": {"root_key": {"type": "string"}}, "required": ["root_key"]}, "PostgrestConfigWithJWTSecretResponse": {"type": "object", "properties": {"db_schema": {"type": "string"}, "max_rows": {"type": "integer"}, "db_extra_search_path": {"type": "string"}, "db_pool": {"type": "integer", "nullable": true, "description": "If `null`, the value is automatically configured based on compute size."}, "jwt_secret": {"type": "string"}}, "required": ["db_schema", "max_rows", "db_extra_search_path", "db_pool"]}, "V1UpdatePostgrestConfigBody": {"type": "object", "properties": {"db_extra_search_path": {"type": "string"}, "db_schema": {"type": "string"}, "max_rows": {"type": "integer", "minimum": 0, "maximum": 1000000}, "db_pool": {"type": "integer", "minimum": 0, "maximum": 1000}}}, "V1PostgrestConfigResponse": {"type": "object", "properties": {"db_schema": {"type": "string"}, "max_rows": {"type": "integer"}, "db_extra_search_path": {"type": "string"}, "db_pool": {"type": "integer", "nullable": true, "description": "If `null`, the value is automatically configured based on compute size."}}, "required": ["db_schema", "max_rows", "db_extra_search_path", "db_pool"]}, "V1ProjectRefResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "ref": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "ref", "name"]}, "SecretResponse": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["name", "value"]}, "CreateSecretBody": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 256, "pattern": "^(?!SUPABASE_).*", "description": "Secret name must not start with the SUPABASE_ prefix.", "example": "string"}, "value": {"type": "string", "maxLength": 24576}}, "required": ["name", "value"]}, "SslEnforcementResponse": {"type": "object", "properties": {"currentConfig": {"type": "object", "properties": {"database": {"type": "boolean"}}, "required": ["database"]}, "appliedSuccessfully": {"type": "boolean"}}, "required": ["currentConfig", "appliedSuccessfully"]}, "SslEnforcementRequest": {"type": "object", "properties": {"requestedConfig": {"type": "object", "properties": {"database": {"type": "boolean"}}, "required": ["database"]}}, "required": ["requestedConfig"]}, "TypescriptResponse": {"type": "object", "properties": {"types": {"type": "string"}}, "required": ["types"]}, "VanitySubdomainConfigResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["not-used", "custom-domain-used", "active"]}, "custom_domain": {"type": "string"}}, "required": ["status"]}, "VanitySubdomainBody": {"type": "object", "properties": {"vanity_subdomain": {"type": "string"}}, "required": ["vanity_subdomain"]}, "SubdomainAvailabilityResponse": {"type": "object", "properties": {"available": {"type": "boolean"}}, "required": ["available"]}, "ActivateVanitySubdomainResponse": {"type": "object", "properties": {"custom_domain": {"type": "string"}}, "required": ["custom_domain"]}, "UpgradeDatabaseBody": {"type": "object", "properties": {"target_version": {"type": "string"}, "release_channel": {"type": "string", "enum": ["internal", "alpha", "beta", "ga", "withdrawn", "preview"]}}, "required": ["target_version"]}, "ProjectUpgradeInitiateResponse": {"type": "object", "properties": {"tracking_id": {"type": "string"}}, "required": ["tracking_id"]}, "ProjectUpgradeEligibilityResponse": {"type": "object", "properties": {"eligible": {"type": "boolean"}, "current_app_version": {"type": "string"}, "current_app_version_release_channel": {"type": "string", "enum": ["internal", "alpha", "beta", "ga", "withdrawn", "preview"]}, "latest_app_version": {"type": "string"}, "target_upgrade_versions": {"type": "array", "items": {"type": "object", "properties": {"postgres_version": {"type": "string", "enum": ["15", "17-oriole"]}, "release_channel": {"type": "string", "enum": ["internal", "alpha", "beta", "ga", "withdrawn", "preview"]}, "app_version": {"type": "string"}}, "required": ["postgres_version", "release_channel", "app_version"]}}, "potential_breaking_changes": {"type": "array", "items": {"type": "string"}}, "duration_estimate_hours": {"type": "number"}, "legacy_auth_custom_roles": {"type": "array", "items": {"type": "string"}}, "extension_dependent_objects": {"type": "array", "items": {"type": "string"}}}, "required": ["eligible", "current_app_version", "current_app_version_release_channel", "latest_app_version", "target_upgrade_versions", "potential_breaking_changes", "duration_estimate_hours", "legacy_auth_custom_roles", "extension_dependent_objects"]}, "DatabaseUpgradeStatusResponse": {"type": "object", "properties": {"databaseUpgradeStatus": {"type": "object", "properties": {"initiated_at": {"type": "string"}, "latest_status_at": {"type": "string"}, "target_version": {"type": "number"}, "error": {"type": "string", "enum": ["1_upgraded_instance_launch_failed", "2_volume_detachchment_from_upgraded_instance_failed", "3_volume_attachment_to_original_instance_failed", "4_data_upgrade_initiation_failed", "5_data_upgrade_completion_failed", "6_volume_detachchment_from_original_instance_failed", "7_volume_attachment_to_upgraded_instance_failed", "8_upgrade_completion_failed", "9_post_physical_backup_failed"]}, "progress": {"type": "string", "enum": ["0_requested", "1_started", "2_launched_upgraded_instance", "3_detached_volume_from_upgraded_instance", "4_attached_volume_to_original_instance", "5_initiated_data_upgrade", "6_completed_data_upgrade", "7_detached_volume_from_original_instance", "8_attached_volume_to_upgraded_instance", "9_completed_upgrade", "10_completed_post_physical_backup"]}, "status": {"type": "number"}}, "required": ["initiated_at", "latest_status_at", "target_version", "status"], "nullable": true}}, "required": ["databaseUpgradeStatus"]}, "ReadOnlyStatusResponse": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "override_enabled": {"type": "boolean"}, "override_active_until": {"type": "string"}}, "required": ["enabled", "override_enabled", "override_active_until"]}, "SetUpReadReplicaBody": {"type": "object", "properties": {"read_replica_region": {"type": "string", "enum": ["us-east-1", "us-east-2", "us-west-1", "us-west-2", "ap-east-1", "ap-southeast-1", "ap-northeast-1", "ap-northeast-2", "ap-southeast-2", "eu-west-1", "eu-west-2", "eu-west-3", "eu-north-1", "eu-central-1", "eu-central-2", "ca-central-1", "ap-south-1", "sa-east-1"], "description": "Region you want your read replica to reside in", "example": "us-east-1"}}, "required": ["read_replica_region"]}, "RemoveReadReplicaBody": {"type": "object", "properties": {"database_identifier": {"type": "string"}}, "required": ["database_identifier"]}, "AuthHealthResponse": {"type": "object", "properties": {"name": {"type": "string", "enum": ["GoTrue"]}}, "required": ["name"]}, "RealtimeHealthResponse": {"type": "object", "properties": {"connected_cluster": {"type": "integer"}}, "required": ["connected_cluster"]}, "V1ServiceHealthResponse": {"type": "object", "properties": {"info": {"oneOf": [{"$ref": "#/components/schemas/AuthHealthResponse"}, {"$ref": "#/components/schemas/RealtimeHealthResponse"}]}, "name": {"enum": ["auth", "db", "pooler", "realtime", "rest", "storage"], "type": "string"}, "healthy": {"type": "boolean"}, "status": {"enum": ["COMING_UP", "ACTIVE_HEALTHY", "UNHEALTHY"], "type": "string"}, "error": {"type": "string"}}, "required": ["name", "healthy", "status"]}, "CreateSigningKeyBody": {"type": "object", "properties": {"algorithm": {"type": "string", "enum": ["EdDSA", "ES256", "RS256", "HS256"]}, "status": {"type": "string", "enum": ["in_use", "standby"]}}, "required": ["algorithm"], "additionalProperties": false}, "SigningKeysResponse": {"type": "object", "properties": {"keys": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "algorithm": {"type": "string", "enum": ["EdDSA", "ES256", "RS256", "HS256"]}, "status": {"type": "string", "enum": ["in_use", "previously_used", "revoked", "standby"]}, "public_jwk": {"nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "required": ["id", "algorithm", "status", "created_at", "updated_at"], "additionalProperties": false}}}, "required": ["keys"], "additionalProperties": false}, "SigningKeyResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "algorithm": {"type": "string", "enum": ["EdDSA", "ES256", "RS256", "HS256"]}, "status": {"type": "string", "enum": ["in_use", "previously_used", "revoked", "standby"]}, "public_jwk": {"nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "required": ["id", "algorithm", "status", "created_at", "updated_at"], "additionalProperties": false}, "UpdateSigningKeyBody": {"type": "object", "properties": {"status": {"type": "string", "enum": ["in_use", "previously_used", "revoked", "standby"]}}, "required": ["status"], "additionalProperties": false}, "StorageFeatureImageTransformation": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"]}, "StorageFeatureS3Protocol": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"]}, "StorageFeatures": {"type": "object", "properties": {"imageTransformation": {"$ref": "#/components/schemas/StorageFeatureImageTransformation"}, "s3Protocol": {"$ref": "#/components/schemas/StorageFeatureS3Protocol"}}, "required": ["imageTransformation", "s3Protocol"]}, "StorageConfigResponse": {"type": "object", "properties": {"fileSizeLimit": {"type": "integer", "format": "int64"}, "features": {"$ref": "#/components/schemas/StorageFeatures"}}, "required": ["fileSizeLimit", "features"]}, "UpdateStorageConfigBody": {"type": "object", "properties": {"fileSizeLimit": {"type": "integer", "minimum": 0, "maximum": 53687091200, "format": "int64"}, "features": {"$ref": "#/components/schemas/StorageFeatures"}}}, "PostgresConfigResponse": {"type": "object", "properties": {"effective_cache_size": {"type": "string"}, "logical_decoding_work_mem": {"type": "string"}, "maintenance_work_mem": {"type": "string"}, "track_activity_query_size": {"type": "string"}, "max_connections": {"type": "integer", "minimum": 1, "maximum": 262143}, "max_locks_per_transaction": {"type": "integer", "minimum": 10, "maximum": 2147483640}, "max_parallel_maintenance_workers": {"type": "integer", "minimum": 0, "maximum": 1024}, "max_parallel_workers": {"type": "integer", "minimum": 0, "maximum": 1024}, "max_parallel_workers_per_gather": {"type": "integer", "minimum": 0, "maximum": 1024}, "max_replication_slots": {"type": "integer"}, "max_slot_wal_keep_size": {"type": "string"}, "max_standby_archive_delay": {"type": "string"}, "max_standby_streaming_delay": {"type": "string"}, "max_wal_size": {"type": "string"}, "max_wal_senders": {"type": "integer"}, "max_worker_processes": {"type": "integer", "minimum": 0, "maximum": 262143}, "shared_buffers": {"type": "string"}, "statement_timeout": {"type": "string"}, "track_commit_timestamp": {"type": "boolean"}, "wal_keep_size": {"type": "string"}, "wal_sender_timeout": {"type": "string"}, "work_mem": {"type": "string"}, "session_replication_role": {"enum": ["origin", "replica", "local"], "type": "string"}}}, "UpdatePostgresConfigBody": {"type": "object", "properties": {"effective_cache_size": {"type": "string"}, "logical_decoding_work_mem": {"type": "string"}, "maintenance_work_mem": {"type": "string"}, "track_activity_query_size": {"type": "string"}, "max_connections": {"type": "integer", "minimum": 1, "maximum": 262143}, "max_locks_per_transaction": {"type": "integer", "minimum": 10, "maximum": 2147483640}, "max_parallel_maintenance_workers": {"type": "integer", "minimum": 0, "maximum": 1024}, "max_parallel_workers": {"type": "integer", "minimum": 0, "maximum": 1024}, "max_parallel_workers_per_gather": {"type": "integer", "minimum": 0, "maximum": 1024}, "max_replication_slots": {"type": "integer"}, "max_slot_wal_keep_size": {"type": "string"}, "max_standby_archive_delay": {"type": "string"}, "max_standby_streaming_delay": {"type": "string"}, "max_wal_size": {"type": "string"}, "max_wal_senders": {"type": "integer"}, "max_worker_processes": {"type": "integer", "minimum": 0, "maximum": 262143}, "shared_buffers": {"type": "string"}, "statement_timeout": {"type": "string"}, "track_commit_timestamp": {"type": "boolean"}, "wal_keep_size": {"type": "string"}, "wal_sender_timeout": {"type": "string"}, "work_mem": {"type": "string"}, "restart_database": {"type": "boolean"}, "session_replication_role": {"enum": ["origin", "replica", "local"], "type": "string"}}}, "V1PgbouncerConfigResponse": {"type": "object", "properties": {"pool_mode": {"type": "string", "enum": ["transaction", "session", "statement"]}, "default_pool_size": {"type": "number"}, "ignore_startup_parameters": {"type": "string"}, "max_client_conn": {"type": "number"}, "connection_string": {"type": "string"}}}, "SupavisorConfigResponse": {"type": "object", "properties": {"database_type": {"type": "string", "enum": ["PRIMARY", "READ_REPLICA"]}, "db_port": {"type": "integer"}, "connectionString": {"type": "string", "deprecated": true, "description": "Use connection_string instead"}, "default_pool_size": {"type": "integer", "nullable": true}, "max_client_conn": {"type": "integer", "nullable": true}, "pool_mode": {"enum": ["transaction", "session"], "type": "string"}, "identifier": {"type": "string"}, "is_using_scram_auth": {"type": "boolean"}, "db_user": {"type": "string"}, "db_host": {"type": "string"}, "db_name": {"type": "string"}, "connection_string": {"type": "string"}}, "required": ["database_type", "db_port", "connectionString", "default_pool_size", "max_client_conn", "pool_mode", "identifier", "is_using_scram_auth", "db_user", "db_host", "db_name", "connection_string"]}, "UpdateSupavisorConfigBody": {"type": "object", "properties": {"default_pool_size": {"type": "integer", "nullable": true, "minimum": 0, "maximum": 1000}, "pool_mode": {"enum": ["transaction", "session"], "type": "string", "description": "Dedicated pooler mode for the project", "deprecated": true}}}, "UpdateSupavisorConfigResponse": {"type": "object", "properties": {"default_pool_size": {"type": "integer", "nullable": true}, "pool_mode": {"type": "string"}}, "required": ["default_pool_size", "pool_mode"]}, "AuthConfigResponse": {"type": "object", "properties": {"api_max_request_duration": {"type": "integer", "nullable": true}, "db_max_pool_size": {"type": "integer", "nullable": true}, "jwt_exp": {"type": "integer", "nullable": true}, "mailer_otp_exp": {"type": "integer"}, "mailer_otp_length": {"type": "integer", "nullable": true}, "mfa_max_enrolled_factors": {"type": "integer", "nullable": true}, "mfa_phone_otp_length": {"type": "integer"}, "mfa_phone_max_frequency": {"type": "integer", "nullable": true}, "password_min_length": {"type": "integer", "nullable": true}, "rate_limit_anonymous_users": {"type": "integer", "nullable": true}, "rate_limit_email_sent": {"type": "integer", "nullable": true}, "rate_limit_sms_sent": {"type": "integer", "nullable": true}, "rate_limit_token_refresh": {"type": "integer", "nullable": true}, "rate_limit_verify": {"type": "integer", "nullable": true}, "rate_limit_otp": {"type": "integer", "nullable": true}, "security_refresh_token_reuse_interval": {"type": "integer", "nullable": true}, "sessions_inactivity_timeout": {"type": "integer", "nullable": true}, "sessions_timebox": {"type": "integer", "nullable": true}, "sms_max_frequency": {"type": "integer", "nullable": true}, "sms_otp_exp": {"type": "integer", "nullable": true}, "sms_otp_length": {"type": "integer"}, "smtp_max_frequency": {"type": "integer", "nullable": true}, "disable_signup": {"type": "boolean", "nullable": true}, "external_anonymous_users_enabled": {"type": "boolean", "nullable": true}, "external_apple_additional_client_ids": {"type": "string", "nullable": true}, "external_apple_client_id": {"type": "string", "nullable": true}, "external_apple_enabled": {"type": "boolean", "nullable": true}, "external_apple_secret": {"type": "string", "nullable": true}, "external_azure_client_id": {"type": "string", "nullable": true}, "external_azure_enabled": {"type": "boolean", "nullable": true}, "external_azure_secret": {"type": "string", "nullable": true}, "external_azure_url": {"type": "string", "nullable": true}, "external_bitbucket_client_id": {"type": "string", "nullable": true}, "external_bitbucket_enabled": {"type": "boolean", "nullable": true}, "external_bitbucket_secret": {"type": "string", "nullable": true}, "external_discord_client_id": {"type": "string", "nullable": true}, "external_discord_enabled": {"type": "boolean", "nullable": true}, "external_discord_secret": {"type": "string", "nullable": true}, "external_email_enabled": {"type": "boolean", "nullable": true}, "external_facebook_client_id": {"type": "string", "nullable": true}, "external_facebook_enabled": {"type": "boolean", "nullable": true}, "external_facebook_secret": {"type": "string", "nullable": true}, "external_figma_client_id": {"type": "string", "nullable": true}, "external_figma_enabled": {"type": "boolean", "nullable": true}, "external_figma_secret": {"type": "string", "nullable": true}, "external_github_client_id": {"type": "string", "nullable": true}, "external_github_enabled": {"type": "boolean", "nullable": true}, "external_github_secret": {"type": "string", "nullable": true}, "external_gitlab_client_id": {"type": "string", "nullable": true}, "external_gitlab_enabled": {"type": "boolean", "nullable": true}, "external_gitlab_secret": {"type": "string", "nullable": true}, "external_gitlab_url": {"type": "string", "nullable": true}, "external_google_additional_client_ids": {"type": "string", "nullable": true}, "external_google_client_id": {"type": "string", "nullable": true}, "external_google_enabled": {"type": "boolean", "nullable": true}, "external_google_secret": {"type": "string", "nullable": true}, "external_google_skip_nonce_check": {"type": "boolean", "nullable": true}, "external_kakao_client_id": {"type": "string", "nullable": true}, "external_kakao_enabled": {"type": "boolean", "nullable": true}, "external_kakao_secret": {"type": "string", "nullable": true}, "external_keycloak_client_id": {"type": "string", "nullable": true}, "external_keycloak_enabled": {"type": "boolean", "nullable": true}, "external_keycloak_secret": {"type": "string", "nullable": true}, "external_keycloak_url": {"type": "string", "nullable": true}, "external_linkedin_oidc_client_id": {"type": "string", "nullable": true}, "external_linkedin_oidc_enabled": {"type": "boolean", "nullable": true}, "external_linkedin_oidc_secret": {"type": "string", "nullable": true}, "external_slack_oidc_client_id": {"type": "string", "nullable": true}, "external_slack_oidc_enabled": {"type": "boolean", "nullable": true}, "external_slack_oidc_secret": {"type": "string", "nullable": true}, "external_notion_client_id": {"type": "string", "nullable": true}, "external_notion_enabled": {"type": "boolean", "nullable": true}, "external_notion_secret": {"type": "string", "nullable": true}, "external_phone_enabled": {"type": "boolean", "nullable": true}, "external_slack_client_id": {"type": "string", "nullable": true}, "external_slack_enabled": {"type": "boolean", "nullable": true}, "external_slack_secret": {"type": "string", "nullable": true}, "external_spotify_client_id": {"type": "string", "nullable": true}, "external_spotify_enabled": {"type": "boolean", "nullable": true}, "external_spotify_secret": {"type": "string", "nullable": true}, "external_twitch_client_id": {"type": "string", "nullable": true}, "external_twitch_enabled": {"type": "boolean", "nullable": true}, "external_twitch_secret": {"type": "string", "nullable": true}, "external_twitter_client_id": {"type": "string", "nullable": true}, "external_twitter_enabled": {"type": "boolean", "nullable": true}, "external_twitter_secret": {"type": "string", "nullable": true}, "external_workos_client_id": {"type": "string", "nullable": true}, "external_workos_enabled": {"type": "boolean", "nullable": true}, "external_workos_secret": {"type": "string", "nullable": true}, "external_workos_url": {"type": "string", "nullable": true}, "external_zoom_client_id": {"type": "string", "nullable": true}, "external_zoom_enabled": {"type": "boolean", "nullable": true}, "external_zoom_secret": {"type": "string", "nullable": true}, "hook_custom_access_token_enabled": {"type": "boolean", "nullable": true}, "hook_custom_access_token_uri": {"type": "string", "nullable": true}, "hook_custom_access_token_secrets": {"type": "string", "nullable": true}, "hook_mfa_verification_attempt_enabled": {"type": "boolean", "nullable": true}, "hook_mfa_verification_attempt_uri": {"type": "string", "nullable": true}, "hook_mfa_verification_attempt_secrets": {"type": "string", "nullable": true}, "hook_password_verification_attempt_enabled": {"type": "boolean", "nullable": true}, "hook_password_verification_attempt_uri": {"type": "string", "nullable": true}, "hook_password_verification_attempt_secrets": {"type": "string", "nullable": true}, "hook_send_sms_enabled": {"type": "boolean", "nullable": true}, "hook_send_sms_uri": {"type": "string", "nullable": true}, "hook_send_sms_secrets": {"type": "string", "nullable": true}, "hook_send_email_enabled": {"type": "boolean", "nullable": true}, "hook_send_email_uri": {"type": "string", "nullable": true}, "hook_send_email_secrets": {"type": "string", "nullable": true}, "mailer_allow_unverified_email_sign_ins": {"type": "boolean", "nullable": true}, "mailer_autoconfirm": {"type": "boolean", "nullable": true}, "mailer_secure_email_change_enabled": {"type": "boolean", "nullable": true}, "mailer_subjects_confirmation": {"type": "string", "nullable": true}, "mailer_subjects_email_change": {"type": "string", "nullable": true}, "mailer_subjects_invite": {"type": "string", "nullable": true}, "mailer_subjects_magic_link": {"type": "string", "nullable": true}, "mailer_subjects_reauthentication": {"type": "string", "nullable": true}, "mailer_subjects_recovery": {"type": "string", "nullable": true}, "mailer_templates_confirmation_content": {"type": "string", "nullable": true}, "mailer_templates_email_change_content": {"type": "string", "nullable": true}, "mailer_templates_invite_content": {"type": "string", "nullable": true}, "mailer_templates_magic_link_content": {"type": "string", "nullable": true}, "mailer_templates_reauthentication_content": {"type": "string", "nullable": true}, "mailer_templates_recovery_content": {"type": "string", "nullable": true}, "mfa_totp_enroll_enabled": {"type": "boolean", "nullable": true}, "mfa_totp_verify_enabled": {"type": "boolean", "nullable": true}, "mfa_phone_enroll_enabled": {"type": "boolean", "nullable": true}, "mfa_phone_verify_enabled": {"type": "boolean", "nullable": true}, "mfa_web_authn_enroll_enabled": {"type": "boolean", "nullable": true}, "mfa_web_authn_verify_enabled": {"type": "boolean", "nullable": true}, "mfa_phone_template": {"type": "string", "nullable": true}, "password_hibp_enabled": {"type": "boolean", "nullable": true}, "password_required_characters": {"type": "string", "nullable": true}, "refresh_token_rotation_enabled": {"type": "boolean", "nullable": true}, "saml_enabled": {"type": "boolean", "nullable": true}, "saml_external_url": {"type": "string", "nullable": true}, "saml_allow_encrypted_assertions": {"type": "boolean", "nullable": true}, "security_captcha_enabled": {"type": "boolean", "nullable": true}, "security_captcha_provider": {"type": "string", "nullable": true}, "security_captcha_secret": {"type": "string", "nullable": true}, "security_manual_linking_enabled": {"type": "boolean", "nullable": true}, "security_update_password_require_reauthentication": {"type": "boolean", "nullable": true}, "sessions_single_per_user": {"type": "boolean", "nullable": true}, "sessions_tags": {"type": "string", "nullable": true}, "site_url": {"type": "string", "nullable": true}, "sms_autoconfirm": {"type": "boolean", "nullable": true}, "sms_messagebird_access_key": {"type": "string", "nullable": true}, "sms_messagebird_originator": {"type": "string", "nullable": true}, "sms_provider": {"type": "string", "nullable": true}, "sms_template": {"type": "string", "nullable": true}, "sms_test_otp": {"type": "string", "nullable": true}, "sms_test_otp_valid_until": {"type": "string", "nullable": true}, "sms_textlocal_api_key": {"type": "string", "nullable": true}, "sms_textlocal_sender": {"type": "string", "nullable": true}, "sms_twilio_account_sid": {"type": "string", "nullable": true}, "sms_twilio_auth_token": {"type": "string", "nullable": true}, "sms_twilio_content_sid": {"type": "string", "nullable": true}, "sms_twilio_message_service_sid": {"type": "string", "nullable": true}, "sms_twilio_verify_account_sid": {"type": "string", "nullable": true}, "sms_twilio_verify_auth_token": {"type": "string", "nullable": true}, "sms_twilio_verify_message_service_sid": {"type": "string", "nullable": true}, "sms_vonage_api_key": {"type": "string", "nullable": true}, "sms_vonage_api_secret": {"type": "string", "nullable": true}, "sms_vonage_from": {"type": "string", "nullable": true}, "smtp_admin_email": {"type": "string", "nullable": true}, "smtp_host": {"type": "string", "nullable": true}, "smtp_pass": {"type": "string", "nullable": true}, "smtp_port": {"type": "string", "nullable": true}, "smtp_sender_name": {"type": "string", "nullable": true}, "smtp_user": {"type": "string", "nullable": true}, "uri_allow_list": {"type": "string", "nullable": true}}, "required": ["api_max_request_duration", "db_max_pool_size", "jwt_exp", "mailer_otp_exp", "mailer_otp_length", "mfa_max_enrolled_factors", "mfa_phone_otp_length", "mfa_phone_max_frequency", "password_min_length", "rate_limit_anonymous_users", "rate_limit_email_sent", "rate_limit_sms_sent", "rate_limit_token_refresh", "rate_limit_verify", "rate_limit_otp", "security_refresh_token_reuse_interval", "sessions_inactivity_timeout", "sessions_timebox", "sms_max_frequency", "sms_otp_exp", "sms_otp_length", "smtp_max_frequency", "disable_signup", "external_anonymous_users_enabled", "external_apple_additional_client_ids", "external_apple_client_id", "external_apple_enabled", "external_apple_secret", "external_azure_client_id", "external_azure_enabled", "external_azure_secret", "external_azure_url", "external_bitbucket_client_id", "external_bitbucket_enabled", "external_bitbucket_secret", "external_discord_client_id", "external_discord_enabled", "external_discord_secret", "external_email_enabled", "external_facebook_client_id", "external_facebook_enabled", "external_facebook_secret", "external_figma_client_id", "external_figma_enabled", "external_figma_secret", "external_github_client_id", "external_github_enabled", "external_github_secret", "external_gitlab_client_id", "external_gitlab_enabled", "external_gitlab_secret", "external_gitlab_url", "external_google_additional_client_ids", "external_google_client_id", "external_google_enabled", "external_google_secret", "external_google_skip_nonce_check", "external_kakao_client_id", "external_kakao_enabled", "external_kakao_secret", "external_keycloak_client_id", "external_keycloak_enabled", "external_keycloak_secret", "external_keycloak_url", "external_linkedin_oidc_client_id", "external_linkedin_oidc_enabled", "external_linkedin_oidc_secret", "external_slack_oidc_client_id", "external_slack_oidc_enabled", "external_slack_oidc_secret", "external_notion_client_id", "external_notion_enabled", "external_notion_secret", "external_phone_enabled", "external_slack_client_id", "external_slack_enabled", "external_slack_secret", "external_spotify_client_id", "external_spotify_enabled", "external_spotify_secret", "external_twitch_client_id", "external_twitch_enabled", "external_twitch_secret", "external_twitter_client_id", "external_twitter_enabled", "external_twitter_secret", "external_workos_client_id", "external_workos_enabled", "external_workos_secret", "external_workos_url", "external_zoom_client_id", "external_zoom_enabled", "external_zoom_secret", "hook_custom_access_token_enabled", "hook_custom_access_token_uri", "hook_custom_access_token_secrets", "hook_mfa_verification_attempt_enabled", "hook_mfa_verification_attempt_uri", "hook_mfa_verification_attempt_secrets", "hook_password_verification_attempt_enabled", "hook_password_verification_attempt_uri", "hook_password_verification_attempt_secrets", "hook_send_sms_enabled", "hook_send_sms_uri", "hook_send_sms_secrets", "hook_send_email_enabled", "hook_send_email_uri", "hook_send_email_secrets", "mailer_allow_unverified_email_sign_ins", "mailer_autoconfirm", "mailer_secure_email_change_enabled", "mailer_subjects_confirmation", "mailer_subjects_email_change", "mailer_subjects_invite", "mailer_subjects_magic_link", "mailer_subjects_reauthentication", "mailer_subjects_recovery", "mailer_templates_confirmation_content", "mailer_templates_email_change_content", "mailer_templates_invite_content", "mailer_templates_magic_link_content", "mailer_templates_reauthentication_content", "mailer_templates_recovery_content", "mfa_totp_enroll_enabled", "mfa_totp_verify_enabled", "mfa_phone_enroll_enabled", "mfa_phone_verify_enabled", "mfa_web_authn_enroll_enabled", "mfa_web_authn_verify_enabled", "mfa_phone_template", "password_hibp_enabled", "password_required_characters", "refresh_token_rotation_enabled", "saml_enabled", "saml_external_url", "saml_allow_encrypted_assertions", "security_captcha_enabled", "security_captcha_provider", "security_captcha_secret", "security_manual_linking_enabled", "security_update_password_require_reauthentication", "sessions_single_per_user", "sessions_tags", "site_url", "sms_autoconfirm", "sms_messagebird_access_key", "sms_messagebird_originator", "sms_provider", "sms_template", "sms_test_otp", "sms_test_otp_valid_until", "sms_textlocal_api_key", "sms_textlocal_sender", "sms_twilio_account_sid", "sms_twilio_auth_token", "sms_twilio_content_sid", "sms_twilio_message_service_sid", "sms_twilio_verify_account_sid", "sms_twilio_verify_auth_token", "sms_twilio_verify_message_service_sid", "sms_vonage_api_key", "sms_vonage_api_secret", "sms_vonage_from", "smtp_admin_email", "smtp_host", "smtp_pass", "smtp_port", "smtp_sender_name", "smtp_user", "uri_allow_list"]}, "UpdateAuthConfigBody": {"type": "object", "properties": {"jwt_exp": {"type": "integer", "minimum": 0, "maximum": 604800}, "smtp_max_frequency": {"type": "integer", "minimum": 0, "maximum": 32767}, "mfa_max_enrolled_factors": {"type": "integer", "minimum": 0, "maximum": **********}, "sessions_timebox": {"type": "integer", "minimum": 0}, "sessions_inactivity_timeout": {"type": "integer", "minimum": 0}, "rate_limit_anonymous_users": {"type": "integer", "minimum": 1, "maximum": **********}, "rate_limit_email_sent": {"type": "integer", "minimum": 1, "maximum": **********}, "rate_limit_sms_sent": {"type": "integer", "minimum": 1, "maximum": **********}, "rate_limit_verify": {"type": "integer", "minimum": 1, "maximum": **********}, "rate_limit_token_refresh": {"type": "integer", "minimum": 1, "maximum": **********}, "rate_limit_otp": {"type": "integer", "minimum": 1, "maximum": **********}, "password_min_length": {"type": "integer", "minimum": 6, "maximum": 32767}, "security_refresh_token_reuse_interval": {"type": "integer", "minimum": 0, "maximum": **********}, "mailer_otp_exp": {"type": "integer", "minimum": 0, "maximum": **********}, "mailer_otp_length": {"type": "integer", "minimum": 6, "maximum": 10}, "sms_max_frequency": {"type": "integer", "minimum": 0, "maximum": 32767}, "sms_otp_exp": {"type": "integer", "minimum": 0, "maximum": **********}, "sms_otp_length": {"type": "integer", "minimum": 0, "maximum": 32767}, "db_max_pool_size": {"type": "integer"}, "api_max_request_duration": {"type": "integer"}, "mfa_phone_max_frequency": {"type": "integer", "minimum": 0, "maximum": 32767}, "mfa_phone_otp_length": {"type": "integer", "minimum": 0, "maximum": 32767}, "site_url": {"type": "string", "pattern": "/^[^,]+$/"}, "disable_signup": {"type": "boolean"}, "smtp_admin_email": {"type": "string"}, "smtp_host": {"type": "string"}, "smtp_port": {"type": "string"}, "smtp_user": {"type": "string"}, "smtp_pass": {"type": "string"}, "smtp_sender_name": {"type": "string"}, "mailer_allow_unverified_email_sign_ins": {"type": "boolean"}, "mailer_autoconfirm": {"type": "boolean"}, "mailer_subjects_invite": {"type": "string"}, "mailer_subjects_confirmation": {"type": "string"}, "mailer_subjects_recovery": {"type": "string"}, "mailer_subjects_email_change": {"type": "string"}, "mailer_subjects_magic_link": {"type": "string"}, "mailer_subjects_reauthentication": {"type": "string"}, "mailer_templates_invite_content": {"type": "string"}, "mailer_templates_confirmation_content": {"type": "string"}, "mailer_templates_recovery_content": {"type": "string"}, "mailer_templates_email_change_content": {"type": "string"}, "mailer_templates_magic_link_content": {"type": "string"}, "mailer_templates_reauthentication_content": {"type": "string"}, "uri_allow_list": {"type": "string"}, "external_anonymous_users_enabled": {"type": "boolean"}, "external_email_enabled": {"type": "boolean"}, "external_phone_enabled": {"type": "boolean"}, "saml_enabled": {"type": "boolean"}, "saml_external_url": {"type": "string", "pattern": "/^[^,]+$/"}, "security_captcha_enabled": {"type": "boolean"}, "security_captcha_provider": {"type": "string"}, "security_captcha_secret": {"type": "string"}, "sessions_single_per_user": {"type": "boolean"}, "sessions_tags": {"type": "string", "pattern": "/^\\s*([a-z0-9_-]+(\\s*,+\\s*)?)*\\s*$/i"}, "mailer_secure_email_change_enabled": {"type": "boolean"}, "refresh_token_rotation_enabled": {"type": "boolean"}, "password_hibp_enabled": {"type": "boolean"}, "password_required_characters": {"type": "string", "enum": ["abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ:**********", "abcdefghijklmnopqrstuvwxyz:ABCDEFGHIJKLMNOPQRSTUVWXYZ:**********", "abcdefghijklmnopqrstuvwxyz:ABCDEFGHIJKLMNOPQRSTUVWXYZ:**********:!@#$%^&*()_+-=[]{};'\\\\:\"|<>?,./`~", ""]}, "security_manual_linking_enabled": {"type": "boolean"}, "security_update_password_require_reauthentication": {"type": "boolean"}, "sms_autoconfirm": {"type": "boolean"}, "sms_provider": {"type": "string"}, "sms_messagebird_access_key": {"type": "string"}, "sms_messagebird_originator": {"type": "string"}, "sms_test_otp": {"type": "string", "pattern": "/^([0-9]{1,15}=[0-9]+,?)*$/"}, "sms_test_otp_valid_until": {"type": "string"}, "sms_textlocal_api_key": {"type": "string"}, "sms_textlocal_sender": {"type": "string"}, "sms_twilio_account_sid": {"type": "string"}, "sms_twilio_auth_token": {"type": "string"}, "sms_twilio_content_sid": {"type": "string"}, "sms_twilio_message_service_sid": {"type": "string"}, "sms_twilio_verify_account_sid": {"type": "string"}, "sms_twilio_verify_auth_token": {"type": "string"}, "sms_twilio_verify_message_service_sid": {"type": "string"}, "sms_vonage_api_key": {"type": "string"}, "sms_vonage_api_secret": {"type": "string"}, "sms_vonage_from": {"type": "string"}, "sms_template": {"type": "string"}, "hook_mfa_verification_attempt_enabled": {"type": "boolean"}, "hook_mfa_verification_attempt_uri": {"type": "string"}, "hook_mfa_verification_attempt_secrets": {"type": "string"}, "hook_password_verification_attempt_enabled": {"type": "boolean"}, "hook_password_verification_attempt_uri": {"type": "string"}, "hook_password_verification_attempt_secrets": {"type": "string"}, "hook_custom_access_token_enabled": {"type": "boolean"}, "hook_custom_access_token_uri": {"type": "string"}, "hook_custom_access_token_secrets": {"type": "string"}, "hook_send_sms_enabled": {"type": "boolean"}, "hook_send_sms_uri": {"type": "string"}, "hook_send_sms_secrets": {"type": "string"}, "hook_send_email_enabled": {"type": "boolean"}, "hook_send_email_uri": {"type": "string"}, "hook_send_email_secrets": {"type": "string"}, "external_apple_enabled": {"type": "boolean"}, "external_apple_client_id": {"type": "string"}, "external_apple_secret": {"type": "string"}, "external_apple_additional_client_ids": {"type": "string"}, "external_azure_enabled": {"type": "boolean"}, "external_azure_client_id": {"type": "string"}, "external_azure_secret": {"type": "string"}, "external_azure_url": {"type": "string"}, "external_bitbucket_enabled": {"type": "boolean"}, "external_bitbucket_client_id": {"type": "string"}, "external_bitbucket_secret": {"type": "string"}, "external_discord_enabled": {"type": "boolean"}, "external_discord_client_id": {"type": "string"}, "external_discord_secret": {"type": "string"}, "external_facebook_enabled": {"type": "boolean"}, "external_facebook_client_id": {"type": "string"}, "external_facebook_secret": {"type": "string"}, "external_figma_enabled": {"type": "boolean"}, "external_figma_client_id": {"type": "string"}, "external_figma_secret": {"type": "string"}, "external_github_enabled": {"type": "boolean"}, "external_github_client_id": {"type": "string"}, "external_github_secret": {"type": "string"}, "external_gitlab_enabled": {"type": "boolean"}, "external_gitlab_client_id": {"type": "string"}, "external_gitlab_secret": {"type": "string"}, "external_gitlab_url": {"type": "string"}, "external_google_enabled": {"type": "boolean"}, "external_google_client_id": {"type": "string"}, "external_google_secret": {"type": "string"}, "external_google_additional_client_ids": {"type": "string"}, "external_google_skip_nonce_check": {"type": "boolean"}, "external_kakao_enabled": {"type": "boolean"}, "external_kakao_client_id": {"type": "string"}, "external_kakao_secret": {"type": "string"}, "external_keycloak_enabled": {"type": "boolean"}, "external_keycloak_client_id": {"type": "string"}, "external_keycloak_secret": {"type": "string"}, "external_keycloak_url": {"type": "string"}, "external_linkedin_oidc_enabled": {"type": "boolean"}, "external_linkedin_oidc_client_id": {"type": "string"}, "external_linkedin_oidc_secret": {"type": "string"}, "external_slack_oidc_enabled": {"type": "boolean"}, "external_slack_oidc_client_id": {"type": "string"}, "external_slack_oidc_secret": {"type": "string"}, "external_notion_enabled": {"type": "boolean"}, "external_notion_client_id": {"type": "string"}, "external_notion_secret": {"type": "string"}, "external_slack_enabled": {"type": "boolean"}, "external_slack_client_id": {"type": "string"}, "external_slack_secret": {"type": "string"}, "external_spotify_enabled": {"type": "boolean"}, "external_spotify_client_id": {"type": "string"}, "external_spotify_secret": {"type": "string"}, "external_twitch_enabled": {"type": "boolean"}, "external_twitch_client_id": {"type": "string"}, "external_twitch_secret": {"type": "string"}, "external_twitter_enabled": {"type": "boolean"}, "external_twitter_client_id": {"type": "string"}, "external_twitter_secret": {"type": "string"}, "external_workos_enabled": {"type": "boolean"}, "external_workos_client_id": {"type": "string"}, "external_workos_secret": {"type": "string"}, "external_workos_url": {"type": "string"}, "external_zoom_enabled": {"type": "boolean"}, "external_zoom_client_id": {"type": "string"}, "external_zoom_secret": {"type": "string"}, "mfa_totp_enroll_enabled": {"type": "boolean"}, "mfa_totp_verify_enabled": {"type": "boolean"}, "mfa_web_authn_enroll_enabled": {"type": "boolean"}, "mfa_web_authn_verify_enabled": {"type": "boolean"}, "mfa_phone_enroll_enabled": {"type": "boolean"}, "mfa_phone_verify_enabled": {"type": "boolean"}, "mfa_phone_template": {"type": "string"}}}, "CreateThirdPartyAuthBody": {"type": "object", "properties": {"oidc_issuer_url": {"type": "string"}, "jwks_url": {"type": "string"}, "custom_jwks": {"type": "object"}}}, "ThirdPartyAuth": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "oidc_issuer_url": {"type": "string", "nullable": true}, "jwks_url": {"type": "string", "nullable": true}, "custom_jwks": {"type": "object", "nullable": true}, "resolved_jwks": {"type": "object", "nullable": true}, "inserted_at": {"type": "string"}, "updated_at": {"type": "string"}, "resolved_at": {"type": "string", "nullable": true}}, "required": ["id", "type", "inserted_at", "updated_at"]}, "ProjectAvailableRestoreVersion": {"type": "object", "properties": {"version": {"type": "string"}, "release_channel": {"type": "string", "enum": ["internal", "alpha", "beta", "ga", "withdrawn", "preview"]}, "postgres_engine": {"type": "string", "enum": ["13", "14", "15", "17", "17-oriole"]}}, "required": ["version", "release_channel", "postgres_engine"]}, "GetProjectAvailableRestoreVersionsResponse": {"type": "object", "properties": {"available_versions": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectAvailableRestoreVersion"}}}, "required": ["available_versions"]}, "RestoreProjectBodyDto": {"type": "object", "properties": {}, "hideDefinitions": ["release_channel", "postgres_engine"]}, "ListProjectAddonsResponseDto": {"type": "object", "properties": {"selected_addons": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["custom_domain", "compute_instance", "pitr", "ipv4", "auth_mfa_phone", "auth_mfa_web_authn", "log_drain"]}, "variant": {"type": "object", "properties": {"id": {"oneOf": [{"type": "string", "enum": ["ci_micro", "ci_small", "ci_medium", "ci_large", "ci_xlarge", "ci_2xlarge", "ci_4xlarge", "ci_8xlarge", "ci_12xlarge", "ci_16xlarge"]}, {"type": "string", "enum": ["cd_default"]}, {"type": "string", "enum": ["pitr_7", "pitr_14", "pitr_28"]}, {"type": "string", "enum": ["ipv4_default"]}, {"type": "string", "enum": ["auth_mfa_phone_default"]}, {"type": "string", "enum": ["auth_mfa_web_authn_default"]}, {"type": "string", "enum": ["log_drain_default"]}]}, "name": {"type": "string"}, "price": {"type": "object", "properties": {"description": {"type": "string"}, "type": {"type": "string", "enum": ["fixed", "usage"]}, "interval": {"type": "string", "enum": ["monthly", "hourly"]}, "amount": {"type": "number"}}, "required": ["description", "type", "interval", "amount"]}, "meta": {"type": "object", "additionalProperties": {"oneOf": [{"type": "number"}, {"type": "boolean"}, {"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}}, "required": ["id", "name", "price"]}}, "required": ["type", "variant"]}}, "available_addons": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["custom_domain", "compute_instance", "pitr", "ipv4", "auth_mfa_phone", "auth_mfa_web_authn", "log_drain"]}, "name": {"type": "string"}, "variants": {"type": "array", "items": {"type": "object", "properties": {"id": {"oneOf": [{"type": "string", "enum": ["ci_micro", "ci_small", "ci_medium", "ci_large", "ci_xlarge", "ci_2xlarge", "ci_4xlarge", "ci_8xlarge", "ci_12xlarge", "ci_16xlarge"]}, {"type": "string", "enum": ["cd_default"]}, {"type": "string", "enum": ["pitr_7", "pitr_14", "pitr_28"]}, {"type": "string", "enum": ["ipv4_default"]}, {"type": "string", "enum": ["auth_mfa_phone_default"]}, {"type": "string", "enum": ["auth_mfa_web_authn_default"]}, {"type": "string", "enum": ["log_drain_default"]}]}, "name": {"type": "string"}, "price": {"type": "object", "properties": {"description": {"type": "string"}, "type": {"type": "string", "enum": ["fixed", "usage"]}, "interval": {"type": "string", "enum": ["monthly", "hourly"]}, "amount": {"type": "number"}}, "required": ["description", "type", "interval", "amount"]}, "meta": {"type": "object", "additionalProperties": {"oneOf": [{"type": "number"}, {"type": "boolean"}, {"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}}, "required": ["id", "name", "price"]}}}, "required": ["type", "name", "variants"]}}}, "required": ["selected_addons", "available_addons"]}, "ApplyProjectAddonBodyDto": {"type": "object", "properties": {"addon_variant": {"oneOf": [{"type": "string", "enum": ["ci_micro", "ci_small", "ci_medium", "ci_large", "ci_xlarge", "ci_2xlarge", "ci_4xlarge", "ci_8xlarge", "ci_12xlarge", "ci_16xlarge"]}, {"type": "string", "enum": ["cd_default"]}, {"type": "string", "enum": ["pitr_7", "pitr_14", "pitr_28"]}, {"type": "string", "enum": ["ipv4_default"]}]}, "addon_type": {"type": "string", "enum": ["custom_domain", "compute_instance", "pitr", "ipv4", "auth_mfa_phone", "auth_mfa_web_authn", "log_drain"]}}, "required": ["addon_variant", "addon_type"]}, "V1AnalyticsResponse": {"type": "object", "properties": {"error": {"oneOf": [{"properties": {"code": {"type": "number"}, "errors": {"type": "array", "items": {"properties": {"domain": {"type": "string"}, "location": {"type": "string"}, "locationType": {"type": "string"}, "message": {"type": "string"}, "reason": {"type": "string"}}}}, "message": {"type": "string"}, "status": {"type": "string"}}}, {"type": "string"}]}, "result": {"type": "array", "items": {"type": "object"}}}}, "V1ListMigrationsResponse": {"type": "array", "items": {"type": "object", "properties": {"version": {"type": "string", "minLength": 1}, "name": {"type": "string"}}, "required": ["version"]}}, "V1RunQueryBody": {"type": "object", "properties": {"query": {"type": "string", "minLength": 1}}, "required": ["query"]}, "GetProjectDbMetadataResponseDto": {"type": "object", "properties": {"databases": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "schemas": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"], "additionalProperties": true}}}, "required": ["name", "schemas"], "additionalProperties": true}}}, "required": ["databases"]}, "FunctionResponse": {"type": "object", "properties": {"id": {"type": "string"}, "slug": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "REMOVED", "THROTTLED"]}, "version": {"type": "integer"}, "created_at": {"type": "integer"}, "updated_at": {"type": "integer"}, "verify_jwt": {"type": "boolean"}, "import_map": {"type": "boolean"}, "entrypoint_path": {"type": "string"}, "import_map_path": {"type": "string"}}, "required": ["id", "slug", "name", "status", "version", "created_at", "updated_at"]}, "V1CreateFunctionBody": {"type": "object", "properties": {"slug": {"type": "string", "pattern": "^[A-Za-z0-9_-]+$"}, "name": {"type": "string"}, "body": {"type": "string"}, "verify_jwt": {"type": "boolean"}}, "required": ["slug", "name", "body"]}, "BulkUpdateFunctionBody": {"type": "object", "properties": {"id": {"type": "string"}, "slug": {"type": "string", "pattern": "^[A-Za-z0-9_-]+$"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "REMOVED", "THROTTLED"]}, "version": {"type": "integer"}, "created_at": {"type": "integer"}, "verify_jwt": {"type": "boolean"}, "import_map": {"type": "boolean"}, "entrypoint_path": {"type": "string"}, "import_map_path": {"type": "string"}}, "required": ["id", "slug", "name", "status", "version"]}, "BulkUpdateFunctionResponse": {"type": "object", "properties": {"functions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "slug": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "REMOVED", "THROTTLED"]}, "version": {"type": "integer"}, "created_at": {"type": "integer"}, "updated_at": {"type": "integer"}, "verify_jwt": {"type": "boolean"}, "import_map": {"type": "boolean"}, "entrypoint_path": {"type": "string"}, "import_map_path": {"type": "string"}}, "required": ["id", "slug", "name", "status", "version", "created_at", "updated_at"]}}}, "required": ["functions"]}, "FunctionDeployBody": {"type": "object", "properties": {"file": {"type": "array", "items": {"type": "string", "format": "binary"}}, "metadata": {"type": "object", "properties": {"entrypoint_path": {"type": "string"}, "import_map_path": {"type": "string"}, "static_patterns": {"type": "array", "items": {"type": "string"}}, "verify_jwt": {"type": "boolean"}, "name": {"type": "string"}}, "required": ["entrypoint_path"]}}, "required": ["metadata"]}, "DeployFunctionResponse": {"type": "object", "properties": {"id": {"type": "string"}, "slug": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "REMOVED", "THROTTLED"]}, "version": {"type": "integer"}, "created_at": {"type": "integer"}, "updated_at": {"type": "integer"}, "verify_jwt": {"type": "boolean"}, "import_map": {"type": "boolean"}, "entrypoint_path": {"type": "string"}, "import_map_path": {"type": "string"}}, "required": ["id", "slug", "name", "status", "version"]}, "FunctionSlugResponse": {"type": "object", "properties": {"id": {"type": "string"}, "slug": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "REMOVED", "THROTTLED"]}, "version": {"type": "integer"}, "created_at": {"type": "integer"}, "updated_at": {"type": "integer"}, "verify_jwt": {"type": "boolean"}, "import_map": {"type": "boolean"}, "entrypoint_path": {"type": "string"}, "import_map_path": {"type": "string"}}, "required": ["id", "slug", "name", "status", "version", "created_at", "updated_at"]}, "V1UpdateFunctionBody": {"type": "object", "properties": {"name": {"type": "string"}, "body": {"type": "string"}, "verify_jwt": {"type": "boolean"}}}, "V1StorageBucketResponse": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "owner": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "public": {"type": "boolean"}}, "required": ["id", "name", "owner", "created_at", "updated_at", "public"]}, "AttributeValue": {"type": "object", "properties": {"default": {"oneOf": [{"type": "object"}, {"type": "number"}, {"type": "string"}, {"type": "boolean"}]}, "name": {"type": "string"}, "names": {"type": "array", "items": {"type": "string"}}, "array": {"type": "boolean"}}}, "AttributeMapping": {"type": "object", "properties": {"keys": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/AttributeValue"}}}, "required": ["keys"]}, "CreateProviderBody": {"type": "object", "properties": {"type": {"type": "string", "enum": ["saml"], "description": "What type of provider will be created"}, "metadata_xml": {"type": "string"}, "metadata_url": {"type": "string"}, "domains": {"type": "array", "items": {"type": "string"}}, "attribute_mapping": {"$ref": "#/components/schemas/AttributeMapping"}}, "required": ["type"]}, "SamlDescriptor": {"type": "object", "properties": {"id": {"type": "string"}, "entity_id": {"type": "string"}, "metadata_url": {"type": "string"}, "metadata_xml": {"type": "string"}, "attribute_mapping": {"$ref": "#/components/schemas/AttributeMapping"}}, "required": ["id", "entity_id"]}, "Domain": {"type": "object", "properties": {"id": {"type": "string"}, "domain": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id"]}, "CreateProviderResponse": {"type": "object", "properties": {"id": {"type": "string"}, "saml": {"$ref": "#/components/schemas/SamlDescriptor"}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id"]}, "Provider": {"type": "object", "properties": {"id": {"type": "string"}, "saml": {"$ref": "#/components/schemas/SamlDescriptor"}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id"]}, "ListProvidersResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Provider"}}}, "required": ["items"]}, "GetProviderResponse": {"type": "object", "properties": {"id": {"type": "string"}, "saml": {"$ref": "#/components/schemas/SamlDescriptor"}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id"]}, "UpdateProviderBody": {"type": "object", "properties": {"metadata_xml": {"type": "string"}, "metadata_url": {"type": "string"}, "domains": {"type": "array", "items": {"type": "string"}}, "attribute_mapping": {"$ref": "#/components/schemas/AttributeMapping"}}}, "UpdateProviderResponse": {"type": "object", "properties": {"id": {"type": "string"}, "saml": {"$ref": "#/components/schemas/SamlDescriptor"}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id"]}, "DeleteProviderResponse": {"type": "object", "properties": {"id": {"type": "string"}, "saml": {"$ref": "#/components/schemas/SamlDescriptor"}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id"]}, "V1Backup": {"type": "object", "properties": {"status": {"type": "string", "enum": ["COMPLETED", "FAILED", "PENDING", "REMOVED", "ARCHIVED", "CANCELLED"]}, "is_physical_backup": {"type": "boolean"}, "inserted_at": {"type": "string"}}, "required": ["status", "is_physical_backup", "inserted_at"]}, "V1PhysicalBackup": {"type": "object", "properties": {"earliest_physical_backup_date_unix": {"type": "integer", "format": "int64"}, "latest_physical_backup_date_unix": {"type": "integer", "format": "int64"}}}, "V1BackupsResponse": {"type": "object", "properties": {"region": {"type": "string"}, "walg_enabled": {"type": "boolean"}, "pitr_enabled": {"type": "boolean"}, "backups": {"type": "array", "items": {"$ref": "#/components/schemas/V1Backup"}}, "physical_backup_data": {"$ref": "#/components/schemas/V1PhysicalBackup"}}, "required": ["region", "walg_enabled", "pitr_enabled", "backups", "physical_backup_data"]}, "V1RestorePitrBody": {"type": "object", "properties": {"recovery_time_target_unix": {"type": "integer", "minimum": 0, "format": "int64"}}, "required": ["recovery_time_target_unix"]}, "V1OrganizationMemberResponse": {"type": "object", "properties": {"user_id": {"type": "string"}, "user_name": {"type": "string"}, "email": {"type": "string"}, "role_name": {"type": "string"}, "mfa_enabled": {"type": "boolean"}}, "required": ["user_id", "user_name", "role_name", "mfa_enabled"]}, "BillingPlanId": {"type": "string", "enum": ["free", "pro", "team", "enterprise"]}, "ReleaseChannel": {"type": "string", "enum": ["internal", "alpha", "beta", "ga", "withdrawn", "preview"]}, "V1OrganizationSlugResponse": {"type": "object", "properties": {"plan": {"$ref": "#/components/schemas/BillingPlanId"}, "opt_in_tags": {"type": "array", "items": {"type": "string", "enum": ["AI_SQL_GENERATOR_OPT_IN"]}}, "allowed_release_channels": {"type": "array", "items": {"$ref": "#/components/schemas/ReleaseChannel"}}, "id": {"type": "string"}, "name": {"type": "string"}}, "required": ["opt_in_tags", "allowed_release_channels", "id", "name"]}}}}