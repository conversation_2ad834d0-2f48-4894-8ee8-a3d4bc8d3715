clispec: '001'
info:
  id: cli
  version: 1.11.3
  title: Supabase CLI
  language: sh
  source: https://github.com/supabase/cli
  bugs: https://github.com/supabase/cli/issues
  spec: https://github.com/supabase/supabase.tools/cli_spec/lib.yaml
  description: |
    Supabase CLI provides you with tools to develop your application locally, and deploy your application to the Supabase platform.
  options: |-
    ```sh
          --debug            output debug logs to stderr
          --experimental     enable experimental features
      -h, --help             help for supabase
          --workdir string   path to a Supabase project directory
    ```
  tags:
    - id: local-dev
      title: Local Development
    - id: management-api
      title: Management APIs
    - id: other-commands
      title: Additional Commands
commands:
  - id: supabase-test
    title: supabase test
    summary: Run tests on local Supabase containers
    tags:
      - local-dev
    links: []
    subcommands:
      - supabase-test-db
    options: |-
      ```sh
        -h, --help   help for test
      ```
  - id: supabase-test-db
    title: supabase test db
    summary: Tests local database with pgTAP
    tags: []
    links: []
    usage: |-
      ```sh
      supabase test db [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for db
      ```
  - id: supabase-stop
    title: supabase stop
    summary: Stop all local Supabase containers
    tags:
      - local-dev
    links: []
    usage: |-
      ```sh
      supabase stop [flags]
      ```
    subcommands: []
    options: |-
      ```sh
            --backup   Backs up the current database before stopping.
        -h, --help     help for stop
      ```
  - id: supabase-status
    title: supabase status
    summary: Show status of local Supabase containers
    tags:
      - local-dev
    links: []
    usage: |-
      ```sh
      supabase status [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for status
      ```
  - id: supabase-start
    title: supabase start
    summary: Start containers for Supabase local development
    tags:
      - local-dev
    links: []
    usage: |-
      ```sh
      supabase start [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for start
      ```
  - id: supabase-secrets
    title: supabase secrets
    summary: Manage Supabase secrets
    tags:
      - management-api
    links: []
    subcommands:
      - supabase-secrets-list
      - supabase-secrets-set
      - supabase-secrets-unset
    options: |-
      ```sh
        -h, --help   help for secrets
      ```
  - id: supabase-secrets-unset
    title: supabase secrets unset
    summary: Unset a secret(s) on Supabase
    description: Unset a secret(s) from the linked Supabase project.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase secrets unset <NAME> ... [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for unset
      ```
  - id: supabase-secrets-set
    title: supabase secrets set
    summary: Set a secret(s) on Supabase
    description: Set a secret(s) to the linked Supabase project.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase secrets set [flags] <NAME=VALUE> ...
      ```
    subcommands: []
    options: |-
      ```sh
            --env-file string   Read secrets from a .env file.
        -h, --help              help for set
      ```
  - id: supabase-secrets-list
    title: supabase secrets list
    summary: List all secrets on Supabase
    description: List all secrets in the linked project.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase secrets list [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for list
      ```
  - id: supabase-projects
    title: supabase projects
    summary: Manage Supabase projects
    tags:
      - management-api
    links: []
    subcommands:
      - supabase-projects-create
      - supabase-projects-list
    options: |-
      ```sh
        -h, --help   help for projects
      ```
  - id: supabase-projects-list
    title: supabase projects list
    summary: List all Supabase projects
    description: List all Supabase projects the logged-in user can access.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase projects list [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for list
      ```
  - id: supabase-projects-create
    title: supabase projects create
    summary: Create a project on Supabase
    tags: []
    links: []
    usage: |-
      ```sh
      supabase projects create <project name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
            --db-password string   Database password of the project.
        -h, --help                 help for create
        -i, --interactive          Enables interactive mode.
            --org-id string        Organization ID to create the project in.
            --plan string          Select a plan that suits your needs. (default "free")
            --region string        Select a region close to you for the best performance.
      ```
  - id: supabase-orgs
    title: supabase orgs
    summary: Manage Supabase organizations
    tags:
      - management-api
    links: []
    subcommands:
      - supabase-orgs-list
    options: |-
      ```sh
        -h, --help   help for orgs
      ```
  - id: supabase-orgs-list
    title: supabase orgs list
    summary: List all organizations
    description: List all organizations the logged-in user belongs.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase orgs list [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for list
      ```
  - id: supabase-migration
    title: supabase migration
    summary: Manage database migration scripts
    tags:
      - local-dev
    links: []
    subcommands:
      - supabase-migration-list
      - supabase-migration-new
    options: |-
      ```sh
        -h, --help   help for migration
      ```
  - id: supabase-migration-new
    title: supabase migration new
    summary: Create an empty migration script
    tags: []
    links: []
    usage: |-
      ```sh
      supabase migration new <migration name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for new
      ```
  - id: supabase-migration-list
    title: supabase migration list
    summary: List local and remote migrations
    tags: []
    links: []
    usage: |-
      ```sh
      supabase migration list [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help              help for list
        -p, --password string   Password to your remote Postgres database.
      ```
  - id: supabase-login
    title: supabase login
    summary: Authenticate using an access token
    tags:
      - local-dev
    links: []
    usage: |-
      ```sh
      supabase login [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for login
      ```
  - id: supabase-link
    title: supabase link
    summary: Link to a Supabase project
    tags:
      - local-dev
    links: []
    usage: |-
      ```sh
      supabase link [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help                 help for link
        -p, --password string      Password to your remote Postgres database.
            --project-ref string   Project ref of the Supabase project.
      ```
  - id: supabase-init
    title: supabase init
    summary: Initialize a local project
    tags:
      - local-dev
    links: []
    usage: |-
      ```sh
      supabase init [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for init
      ```
  - id: supabase-gen
    title: supabase gen
    summary: Run code generation tools
    tags:
      - local-dev
    links: []
    subcommands:
      - supabase-gen-types
    options: |-
      ```sh
        -h, --help   help for gen
      ```
  - id: supabase-gen-types
    title: supabase gen types
    summary: Generate types from Postgres schema
    tags: []
    links: []
    subcommands:
      - supabase-gen-types-typescript
    options: |-
      ```sh
        -h, --help   help for types
      ```
  - id: supabase-gen-types-typescript
    title: supabase gen types typescript
    summary: Generate types for TypeScript
    description: |
      Generate types for TypeScript. Must specify one of --local, --linked, --project-id, or --db-url
    tags: []
    links: []
    usage: |-
      ```sh
      supabase gen types typescript [flags]
      ```
    subcommands: []
    options: |-
      ```sh
            --db-url string        Generate types from a database url.
        -h, --help                 help for typescript
            --linked               Generate types from the linked project.
            --local                Generate types from the local dev database.
            --project-id string    Generate types from a project ID.
            --schema stringArray   Schemas to generate types for.
      ```
  - id: supabase-functions
    title: supabase functions
    summary: Manage Supabase Edge functions
    tags:
      - management-api
    links: []
    subcommands:
      - supabase-functions-delete
      - supabase-functions-deploy
      - supabase-functions-new
      - supabase-functions-serve
    options: |-
      ```sh
        -h, --help   help for functions
      ```
  - id: supabase-functions-serve
    title: supabase functions serve
    summary: Serve Functions locally
    tags: []
    links: []
    usage: |-
      ```sh
      supabase functions serve [flags]
      ```
    subcommands: []
    options: |-
      ```sh
            --env-file string   Path to an env file to be populated to the Function environment.
        -h, --help              help for serve
            --no-verify-jwt     Disable JWT verification for the Function.
      ```
  - id: supabase-functions-new
    title: supabase functions new
    summary: Create a new Function locally
    tags: []
    links: []
    usage: |-
      ```sh
      supabase functions new <Function name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for new
      ```
  - id: supabase-functions-deploy
    title: supabase functions deploy
    summary: Deploy a Function to Supabase
    description: Deploy a Function to the linked Supabase project.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase functions deploy <Function name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help                 help for deploy
            --no-verify-jwt        Disable JWT verification for the Function.
            --project-ref string   Project ref of the Supabase project.
      ```
  - id: supabase-functions-delete
    title: supabase functions delete
    summary: Delete a Function from Supabase
    description: |
      Delete a Function from the linked Supabase project. This does NOT remove the Function locally.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase functions delete <Function name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help                 help for delete
            --project-ref string   Project ref of the Supabase project.
      ```
  - id: supabase-domains
    title: supabase domains
    summary: Manage custom domain names for Supabase projects
    tags:
      - management-api
    links: []
    subcommands:
      - supabase-domains-activate
      - supabase-domains-create
      - supabase-domains-delete
      - supabase-domains-get
      - supabase-domains-reverify
    options: |-
      ```sh
        -h, --help                 help for domains
            --include-raw-output   Include raw output (useful for debugging).
            --project-ref string   Project ref of the Supabase project.
      ```
  - id: supabase-domains-reverify
    title: supabase domains reverify
    summary: Re-verify the custom hostname config for your project.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase domains reverify [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for reverify
      ```
  - id: supabase-domains-get
    title: supabase domains get
    summary: Get the current custom hostname config.
    description: |
      Retrieve the custom hostname config for your project, as stored in the Supabase platform.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase domains get [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for get
      ```
  - id: supabase-domains-delete
    title: supabase domains delete
    summary: Deletes the custom hostname config for your project.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase domains delete [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for delete
      ```
  - id: supabase-domains-create
    title: supabase domains create
    summary: Create a custom hostname.
    description: |-
      Create a custom hostname for your Supabase project.

      Expects your custom hostname to have a CNAME record to your Supabase project's subdomain.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase domains create [flags]
      ```
    subcommands: []
    options: |-
      ```sh
            --custom-hostname string   The custom hostname to use for your Supabase project.
        -h, --help                     help for create
      ```
  - id: supabase-domains-activate
    title: supabase domains activate
    summary: Activate the custom hostname for a project.
    description: |-
      Activates the custom hostname configuration for a project.

      This reconfigures your Supabase project to respond to requests on your custom hostname.
      After the custom hostname is activated, your project's auth services will no longer function on the Supabase-provisioned subdomain.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase domains activate [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for activate
      ```
  - id: supabase-db
    title: supabase db
    summary: Manage local Postgres databases
    tags:
      - local-dev
    links: []
    subcommands:
      - supabase-db-branch
      - supabase-db-diff
      - supabase-db-lint
      - supabase-db-push
      - supabase-db-remote
      - supabase-db-reset
    options: |-
      ```sh
        -h, --help   help for db
      ```
  - id: supabase-db-reset
    title: supabase db reset
    summary: Resets the local database to current migrations
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db reset [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for reset
      ```
  - id: supabase-db-remote
    title: supabase db remote
    summary: Manage remote databases
    tags: []
    links: []
    subcommands:
      - supabase-db-remote-changes
      - supabase-db-remote-commit
    options: |-
      ```sh
        -h, --help              help for remote
        -p, --password string   Password to your remote Postgres database.
      ```
  - id: supabase-db-remote-commit
    title: supabase db remote commit
    summary: Commit remote changes as a new migration
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db remote commit [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for commit
      ```
  - id: supabase-db-remote-changes
    title: supabase db remote changes
    summary: Show changes on the remote database
    description: Show changes on the remote database since last migration.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db remote changes [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for changes
      ```
  - id: supabase-db-push
    title: supabase db push
    summary: Push new migrations to the remote database
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db push [flags]
      ```
    subcommands: []
    options: |-
      ```sh
            --dry-run           Print the migrations that would be applied, but don't actually apply them.
        -h, --help              help for push
        -p, --password string   Password to your remote Postgres database.
      ```
  - id: supabase-db-lint
    title: supabase db lint
    summary: Checks local database for typing error
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db lint [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help             help for lint
            --level string     Error level to emit. (default "warning")
        -s, --schema strings   List of schema to include. (default [public])
      ```
  - id: supabase-db-diff
    title: supabase db diff
    summary: Diffs the local database for schema changes
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db diff [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -f, --file string      Saves schema diff to a file.
        -h, --help             help for diff
            --linked           Diffs local schema against linked project.
        -s, --schema strings   List of schema to include. (default [public])
            --use-migra        Use migra to generate schema diff.
      ```
  - id: supabase-db-branch
    title: supabase db branch
    summary: Manage local database branches
    description: |
      Manage local database branches. Each branch is associated with a separate local database. Forking remote databases is NOT supported.
    tags: []
    links: []
    subcommands:
      - supabase-db-branch-create
      - supabase-db-branch-delete
      - supabase-db-branch-list
      - supabase-db-branch-switch
    options: |-
      ```sh
        -h, --help   help for branch
      ```
  - id: supabase-db-branch-switch
    title: supabase db branch switch
    summary: Switch the active branch
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db branch switch <branch name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for switch
      ```
  - id: supabase-db-branch-list
    title: supabase db branch list
    summary: List branches
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db branch list [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for list
      ```
  - id: supabase-db-branch-delete
    title: supabase db branch delete
    summary: Delete a branch
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db branch delete <branch name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for delete
      ```
  - id: supabase-db-branch-create
    title: supabase db branch create
    summary: Create a branch
    tags: []
    links: []
    usage: |-
      ```sh
      supabase db branch create <branch name> [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help   help for create
      ```
  - id: supabase-completion
    title: supabase completion
    summary: Generate the autocompletion script for the specified shell
    description: |
      Generate the autocompletion script for supabase for the specified shell.
      See each sub-command's help for details on how to use the generated script.
    tags:
      - other-commands
    links: []
    subcommands:
      - supabase-completion-bash
      - supabase-completion-fish
      - supabase-completion-powershell
      - supabase-completion-zsh
    options: |-
      ```sh
        -h, --help   help for completion
      ```
  - id: supabase-completion-zsh
    title: supabase completion zsh
    summary: Generate the autocompletion script for zsh
    description: |
      Generate the autocompletion script for the zsh shell.

      If shell completion is not already enabled in your environment you will need
      to enable it.  You can execute the following once:

          echo "autoload -U compinit; compinit" >> ~/.zshrc

      To load completions in your current shell session:

          source <(supabase completion zsh); compdef _supabase supabase

      To load completions for every new session, execute once:

      #### Linux:

          supabase completion zsh > "${fpath[1]}/_supabase"

      #### macOS:

          supabase completion zsh > $(brew --prefix)/share/zsh/site-functions/_supabase

      You will need to start a new shell for this setup to take effect.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase completion zsh [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help              help for zsh
            --no-descriptions   disable completion descriptions
      ```
  - id: supabase-completion-powershell
    title: supabase completion powershell
    summary: Generate the autocompletion script for powershell
    description: |
      Generate the autocompletion script for powershell.

      To load completions in your current shell session:

          supabase completion powershell | Out-String | Invoke-Expression

      To load completions for every new session, add the output of the above command
      to your powershell profile.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase completion powershell [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help              help for powershell
            --no-descriptions   disable completion descriptions
      ```
  - id: supabase-completion-fish
    title: supabase completion fish
    summary: Generate the autocompletion script for fish
    description: |
      Generate the autocompletion script for the fish shell.

      To load completions in your current shell session:

          supabase completion fish | source

      To load completions for every new session, execute once:

          supabase completion fish > ~/.config/fish/completions/supabase.fish

      You will need to start a new shell for this setup to take effect.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase completion fish [flags]
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help              help for fish
            --no-descriptions   disable completion descriptions
      ```
  - id: supabase-completion-bash
    title: supabase completion bash
    summary: Generate the autocompletion script for bash
    description: |
      Generate the autocompletion script for the bash shell.

      This script depends on the 'bash-completion' package.
      If it is not installed already, you can install it via your OS's package manager.

      To load completions in your current shell session:

          source <(supabase completion bash)

      To load completions for every new session, execute once:

      #### Linux:

          supabase completion bash > /etc/bash_completion.d/supabase

      #### macOS:

          supabase completion bash > $(brew --prefix)/etc/bash_completion.d/supabase

      You will need to start a new shell for this setup to take effect.
    tags: []
    links: []
    usage: |-
      ```sh
      supabase completion bash
      ```
    subcommands: []
    options: |-
      ```sh
        -h, --help              help for bash
            --no-descriptions   disable completion descriptions
      ```
