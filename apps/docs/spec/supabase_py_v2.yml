openref: 0.1

info:
  id: reference/supabase-py
  title: Supabase Python Client
  description: |

    Supabase Python

  definition: spec/enrichments/tsdoc_v2/combined.json
  specUrl: https://github.com/supabase/supabase/edit/master/apps/docs/spec/supabase_py_v2.yml
  slugPrefix: '/'
  libraries:
    - name: 'Python'
      id: 'py'
      version: '0.0.1'

functions:
  - id: initializing
    description: |
      You can initialize a new Supabase client using the `create_client()` method.

      The Supabase client is your entrypoint to the rest of the Supabase functionality
      and is the easiest way to interact with everything we offer within the Supabase ecosystem.

    params:
      - name: supabase_url
        isOptional: false
        type: string
        description: The unique Supabase URL which is supplied when you create a new project in your project dashboard.
      - name: supabase_key
        isOptional: false
        type: string
        description: The unique Supabase Key which is supplied when you create a new project in your project dashboard.
      - name: options
        isOptional: true
        type: ClientOptions
        description: Options to change the Auth behaviors.
        subContent:
          - name: schema
            isOptional: true
            type: string
            description: The Postgres schema which your tables belong to. Must be on the list of exposed schemas in Supabase. Defaults to 'public'.
          - name: headers
            isOptional: true
            type: dictionary
            description: Optional headers for initializing the client.
          - name: auto_refresh_token
            isOptional: true
            type: bool
            description: Whether to automatically refresh the token when it expires. Defaults to `true`.
          - name: persist_session
            isOptional: true
            type: bool
            description: Whether to persist a logged in session to storage.
          - name: storage
            isOptional: true
            type: SyncSupportedStorage
            description: A storage provider. Used to store the logged in session.
          - name: realtime
            isOptional: true
            type: string
            description: Options passed to the realtime-py instance.
          - name: postgrest_client_timeout
            isOptional: true
            type: number, float, Timeout
            description: Timeout passed to the SyncPostgrestClient instance.
          - name: storage_client_timeout
            isOptional: true
            type: number, float, Timeout
            description: Timeout passed to the SyncStorageClient instance.
          - name: flow_type
            isOptional: true
            type: AuthFlowType
            description: flow type to use for authentication.

    examples:
      - id: create-client
        name: create_client()
        code: |
          ```python
          import os
          from supabase import create_client, Client

          url: str = os.environ.get("SUPABASE_URL")
          key: str = os.environ.get("SUPABASE_KEY")
          supabase: Client = create_client(url, key)
          ```
      - id: with-timeout-option
        name: With timeout option
        code: |
          ```python
          import os
          from supabase import create_client, Client
          from supabase.client import ClientOptions

          url: str = os.environ.get("SUPABASE_URL")
          key: str = os.environ.get("SUPABASE_KEY")
          supabase: Client = create_client(
              url, 
              key,
              options=ClientOptions(
                  postgrest_client_timeout=10,
                  storage_client_timeout=10,
                  schema="public",
              )
          )
          ```

  - id: auth-api
    title: 'Overview'
    notes: |
      - The auth methods can be accessed via the `supabase.auth` namespace.
      - By default, the supabase client sets `persist_session` to true and attempts to store the session in memory.
      - Any email links and one-time passwords (OTPs) sent have a default expiry of 24 hours. We have the following [rate limits](/docs/guides/platform/going-into-prod#auth-rate-limits) in place to guard against brute force attacks.
      - The expiry of an access token can be set in the "JWT expiry limit" field in [your project's auth settings](/dashboard/project/_/settings/auth). A refresh token never expires and can only be used once.
  - id: sign-up
    title: 'sign_up()'
    params:
      - name: credentials
        isOptional: false
        type: SignUpWithPasswordCredentials
        subContent:
          - name: email
            isOptional: true
            type: string
            description: One of `email` or `phone` must be provided.
          - name: phone
            isOptional: true
            type: string
            description: One of `email` or `phone` must be provided.
          - name: password
            type: string
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: email_redirect_to
                isOptional: true
                type: string
                description: >
                  Only for email signups.
                  The redirect URL embedded in the email link.
                  Must be a configured redirect URL for your Supabase instance.
              - name: data
                isOptional: true
                type: object
                description: >
                  A custom data object to store additional user metadata.
              - name: captcha_token
                isOptional: true
                type: string
              - name: channel
                isOptional: true
                type: sms | whatsapp
                description: >
                  The channel to use for sending messages.
                  Only for phone signups.
    notes: |
      - By default, the user needs to verify their email address before logging in. To turn this off, disable **Confirm email** in [your project](https://supabase.com/dashboard/project/_/auth/providers).
      - **Confirm email** determines if users need to confirm their email address after signing up.
        - If **Confirm email** is enabled, a `user` is returned but `session` is null.
        - If **Confirm email** is disabled, both a `user` and a `session` are returned.
      - By default, when the user confirms their email address, they are redirected to the [`SITE_URL`](https://supabase.com/docs/guides/auth/redirect-urls). You can modify your `SITE_URL` or add additional redirect URLs in [your project](https://supabase.com/dashboard/project/_/auth/url-configuration).
      - If sign_up() is called for an existing confirmed user:
        - When both **Confirm email** and **Confirm phone** (even when phone provider is disabled) are enabled in [your project](/dashboard/project/_/auth/providers), an obfuscated/fake user object is returned.
        - When either **Confirm email** or **Confirm phone** (even when phone provider is disabled) is disabled, the error message, `User already registered` is returned.
      - To fetch the currently logged-in user, refer to [`get_user()`](/docs/reference/python/auth-getuser).
    examples:
      - id: signup
        name: Sign up with an email and password
        code: |
          ```python
          response = supabase.auth.sign_up(
              {
                  "email": "<EMAIL>", 
                  "password": "password",
              }
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email"
                ]
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": null,
              "recovery_sent_at": null,
              "email_change_sent_at": null,
              "new_email": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2024-06-17T00:19:25.760110Z",
              "confirmed_at": null,
              "email_confirmed_at": "2024-06-17T00:19:25.779181Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-06-17T00:19:25.785489Z",
              "role": "authenticated",
              "updated_at": "2024-06-17T00:19:25.794650Z",
              "identities": [
                {
                  "id": "11111111-1111-1111-1111-111111111111",
                  "user_id": "11111111-1111-1111-1111-111111111111",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "created_at": "2024-06-17T00:19:25.774522Z",
                  "last_sign_in_at": "2024-06-17T00:19:25.774498Z",
                  "updated_at": "2024-06-17T00:19:25.774522Z"
                }
              ],
              "factors": null
            },
            "session": {
              "provider_token": null,
              "provider_refresh_token": null,
              "access_token": "<ACCESS_TOKEN>",
              "refresh_token": "<REFRESH_TOKEN>",
              "expires_in": 3600,
              "expires_at": **********,
              "token_type": "bearer",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {},
                "aud": "authenticated",
                "confirmation_sent_at": null,
                "recovery_sent_at": null,
                "email_change_sent_at": null,
                "new_email": null,
                "invited_at": null,
                "action_link": null,
                "email": "<EMAIL>",
                "phone": "",
                "created_at": "2024-06-17T00:19:25.760110Z",
                "confirmed_at": null,
                "email_confirmed_at": "2024-06-17T00:19:25.779181Z",
                "phone_confirmed_at": null,
                "last_sign_in_at": "2024-06-17T00:19:25.785489Z",
                "role": "authenticated",
                "updated_at": "2024-06-17T00:19:25.794650Z",
                "identities": [
                  {
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "created_at": "2024-06-17T00:19:25.774522Z",
                    "last_sign_in_at": "2024-06-17T00:19:25.774498Z",
                    "updated_at": "2024-06-17T00:19:25.774522Z"
                  }
                ],
                "factors": null
              }
            }
          }
          ```
      - id: sign-up-phone
        name: Sign up with a phone number and password (SMS)
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.sign_up(
              {
                  "phone": "*********", 
                  "password": "password",
              }
          )
          ```
      - id: sign-up-phone-whatsapp
        name: Sign up with a phone number and password (whatsapp)
        isSpotlight: true
        description: |
          The user will be sent a WhatsApp message which contains a OTP. By default, a given user can only request a OTP once every 60 seconds. Note that a user will need to have a valid WhatsApp account that is linked to Twilio in order to use this feature.
        code: |
          ```python
          response = supabase.auth.sign_up(
              {
                  "phone": "*********",
                  "password": "password",
                  "options": {"channel": "whatsapp"},
              }
          )
          ```
      - id: sign-up-with-additional-user-metadata
        name: Sign up with additional user metadata
        code: |
          ```python
          response = supabase.auth.sign_up(
              {
                  "email": "<EMAIL>",
                  "password": "password",
                  "options": {"data": {"first_name": "John", "age": 27}},
              }
          )
          ```
      - id: sign-up-with-redirect
        name: Sign up with a redirect URL
        description: |
          - See [redirect URLs and wildcards](/docs/guides/auth/redirect-urls) to add additional redirect URLs to your project.
        code: |
          ```python
          response = supabase.auth.sign_up(
              {
                  "email": "<EMAIL>",
                  "password": "password",
                  "options": {
                      "email_redirect_to": "https://example.com/welcome",
                  },
              }
          )
          ```
  - id: sign-in-anonymously
    title: 'sign_in_anonymously()'
    params:
      - name: credentials
        isOptional: false
        type: SignInAnonymouslyCredentials
        subContent:
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: data
                isOptional: true
                type: object
                description: A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column. The `data` should be a JSON object that includes user-specific info, such as their first and last name.
              - name: captcha_token
                isOptional: true
                type: string
                description: Verification token received when the user completes the captcha on the site.

    notes: |
      - Returns an anonymous user
      - It is recommended to set up captcha for anonymous sign-ins to prevent abuse. You can pass in the captcha token in the `options` param.
    examples:
      - id: sign-in-anonymously
        name: Create an anonymous user
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.sign_in_anonymously(
              {"options": {"captcha_token": ""}}
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {},
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": null,
              "recovery_sent_at": null,
              "email_change_sent_at": null,
              "new_email": null,
              "invited_at": null,
              "action_link": null,
              "email": "",
              "phone": "",
              "created_at": "2024-06-25T19:57:25.870777Z",
              "confirmed_at": null,
              "email_confirmed_at": null,
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-06-25T19:57:25.878874Z",
              "role": "authenticated",
              "updated_at": "2024-06-25T19:57:25.880971Z",
              "identities": [],
              "factors": null,
              "is_anonymous": true
            },
            "session": {
              "provider_token": null,
              "provider_refresh_token": null,
              "access_token": "<ACCESS_TOKEN>",
              "refresh_token": "<REFRESH_TOKEN>",
              "expires_in": 3600,
              "expires_at": **********,
              "token_type": "bearer",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "app_metadata": {},
                "user_metadata": {},
                "aud": "authenticated",
                "confirmation_sent_at": null,
                "recovery_sent_at": null,
                "email_change_sent_at": null,
                "new_email": null,
                "invited_at": null,
                "action_link": null,
                "email": "",
                "phone": "",
                "created_at": "2024-06-25T19:57:25.870777Z",
                "confirmed_at": null,
                "email_confirmed_at": null,
                "phone_confirmed_at": null,
                "last_sign_in_at": "2024-06-25T19:57:25.878874Z",
                "role": "authenticated",
                "updated_at": "2024-06-25T19:57:25.880971Z",
                "identities": [],
                "factors": null,
                "is_anonymous": true
              }
            }
          }
          ```
      - id: sign-in-anonymously-with-user-metadata
        name: Create an anonymous user with custom user metadata
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.sign_in_anonymously(
              {"options": {"data": {}}}
          )
          ```
  - id: sign-in-with-password
    title: 'sign_in_with_password'
    params:
      - name: credentials
        type: SignInWithPasswordCredentials
        subContent:
          - name: email
            isOptional: true
            type: string
            description: One of `email` or `phone` must be provided.
          - name: phone
            isOptional: true
            type: string
            description: One of `email` or `phone` must be provided.
          - name: password
            type: string
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: captcha_token
                isOptional: true
                type: string
    notes: |
      Log in an existing user with an email and password or phone and password.
      - Requires either an email and password or a phone number and password.
    examples:
      - id: sign-in-with-email-and-password
        name: Sign in with email and password
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.sign_in_with_password(
              {
                  "email": "<EMAIL>", 
                  "password": "example-password",
              }
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email"
                ]
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
              "recovery_sent_at": null,
              "email_change_sent_at": null,
              "new_email": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2023-02-19T00:01:51.142802Z",
              "confirmed_at": "2023-02-19T00:01:51.351735Z",
              "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-06-25T19:50:31.744823Z",
              "role": "authenticated",
              "updated_at": "2024-06-25T19:50:31.757491Z",
              "identities": [],
              "factors": null,
              "is_anonymous": false
            },
            "session": {
              "provider_token": null,
              "provider_refresh_token": null,
              "access_token": "<ACCESS_TOKEN>",
              "refresh_token": "<REFRESH_TOKEN>",
              "expires_in": 3600,
              "expires_at": **********,
              "token_type": "bearer",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email"
                  ]
                },
                "user_metadata": {},
                "aud": "authenticated",
                "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
                "recovery_sent_at": null,
                "email_change_sent_at": null,
                "new_email": null,
                "invited_at": null,
                "action_link": null,
                "email": "<EMAIL>",
                "phone": "",
                "created_at": "2023-02-19T00:01:51.142802Z",
                "confirmed_at": "2023-02-19T00:01:51.351735Z",
                "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
                "phone_confirmed_at": null,
                "last_sign_in_at": "2024-06-25T19:50:31.744823Z",
                "role": "authenticated",
                "updated_at": "2024-06-25T19:50:31.757491Z",
                "identities": [],
                "factors": null,
                "is_anonymous": false
              }
            }
          }
          ```
      - id: sign-in-with-phone-and-password
        name: Sign in with phone and password
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.sign_in_with_password(
              {
                  "phone": "+***********", 
                  "password": "some-password",
              }
          )
          ```
  - id: sign-in-with-otp
    title: 'sign_in_with_otp'
    params:
      - name: credentials
        type: SignInWithPasswordCredentials
        subContent:
          - name: email
            isOptional: true
            type: string
            description: One of `email` or `phone` must be provided.
          - name: phone
            isOptional: true
            type: string
            description: One of `email` or `phone` must be provided.
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: email_redirect_to
                isOptional: true
                type: string
                description: >
                  Only for email signups.
                  The redirect URL embedded in the email link.
                  Must be a configured redirect URL for your Supabase instance.
              - name: should_create_user
                isOptional: true
                type: boolean
                description: >
                  Whether to create the user if they don't already exist.
                  Defaults to true.
              - name: data
                isOptional: true
                type: object
                description: >
                  A custom data object to store additional user metadata.
              - name: captcha_token
                isOptional: true
                type: string
              - name: channel
                isOptional: true
                type: sms | whatsapp
                description: >
                  The channel to use for sending messages.
                  Only for phone signups.
    notes: |
      - Requires either an email or phone number.
      - This method is used for passwordless sign-ins where a OTP is sent to the user's email or phone number.
      - If the user doesn't exist, `sign_in_with_otp()` will signup the user instead. To restrict this behavior, you can set `should_create_user` in `SignInWithPasswordlessCredentials.options` to `false`.
      - If you're using an email, you can configure whether you want the user to receive a magiclink or a OTP.
      - If you're using phone, you can configure whether you want the user to receive a OTP.
      - The magic link's destination URL is determined by the [`SITE_URL`](/docs/guides/auth/redirect-urls).
      - See [redirect URLs and wildcards](/docs/guides/auth/redirect-urls#use-wildcards-in-redirect-urls) to add additional redirect URLs to your project.
      - Magic links and OTPs share the same implementation. To send users a one-time code instead of a magic link, [modify the magic link email template](https://supabase.com/dashboard/project/_/auth/templates) to include `{{ .Token }}` instead of `{{ .ConfirmationURL }}`.
    examples:
      - id: sign-in-with-email
        name: Sign in with email
        isSpotlight: true
        description: The user will be sent an email which contains either a magiclink or a OTP or both. By default, a given user can only request a OTP once every 60 seconds.
        code: |
          ```python
          response = supabase.auth.sign_in_with_otp(
              {
                  "email": "<EMAIL>",
                  "options": {
                      "email_redirect_to": "https://example.com/welcome",
                  },
              }
          )
          ```
        response: |
          ```json
          {
            "message_id": null,
            "user": null,
            "session": null
          }
          ```
      - id: sign-in-with-sms-otp
        name: Sign in with SMS OTP
        isSpotlight: false
        description: The user will be sent a SMS which contains a OTP. By default, a given user can only request a OTP once every 60 seconds.
        code: |
          ```python
          response = supabase.auth.sign_in_with_otp(
              {"phone": "+***********"}
          )
          ```
      - id: sign-in-with-whatsapp-otp
        name: Sign in with WhatsApp OTP
        isSpotlight: false
        description: The user will be sent a WhatsApp message which contains a OTP. By default, a given user can only request a OTP once every 60 seconds. Note that a user will need to have a valid WhatsApp account that is linked to Twilio in order to use this feature.
        code: |
          ```python
          response = supabase.auth.sign_in_with_otp(
              {
                  "phone": "+***********",
                  "options": {
                      "channel": "whatsapp",
                  },
              }
          )
          ```
  - id: sign-in-with-oauth
    title: 'sign_in_with_oauth'
    params:
      - name: credentials
        isOptional: false
        type: SignInWithOAuthCredentials
        subContent:
          - name: provider
            isOptional: false
            type: boolean
            description: One of the providers supported by GoTrue.
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: scopes
                isOptional: true
                type: string
                description: A space-separated list of scopes granted to the OAuth application.
              - name: redirect_to
                isOptional: true
                type: string
                description: A URL to send the user to after they are confirmed.
              - name: query_params
                isOptional: true
                type: string
                description: An object of query params
    notes: |
      - This method is used for signing in using a third-party provider.
      - Supabase supports many different [third-party providers](/docs/guides/auth#configure-third-party-providers).
    examples:
      - id: sign-in-using-a-third-party-provider
        name: Sign in using a third-party provider
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.sign_in_with_oauth(
              {"provider": "github"}
          )
          ```
      - id: sign-in-using-a-third-party-provider-with-redirect
        name: Sign in using a third-party provider with redirect
        isSpotlight: false
        description: |
          - When the third-party provider successfully authenticates the user, the provider redirects the user to the URL specified in the `redirectTo` parameter. This parameter defaults to the [`SITE_URL`](/docs/guides/auth/redirect-urls). It does not redirect the user immediately after invoking this method.
          - See [redirect URLs and wildcards](docs/guides/auth/redirect-urls#use-wildcards-in-redirect-urls) to add additional redirect URLs to your project.
        code: |
          ```python
          response = supabase.auth.sign_in_with_oauth(
              {
                  "provider": "github",
                  "options": {
                      "redirect_to": "https://example.com/welcome",
                  }
              }
          )
          ```
      - id: sign-in-with-scopes
        name: Sign in with scopes
        isSpotlight: false
        description: |
          If you need additional data from an OAuth provider, you can include a space-separated list of scopes in your request to get back an OAuth provider token.
          You may also need to specify the scopes in the provider's OAuth app settings, depending on the provider. The list of scopes will be documented by the third-party provider you are using and specifying scopes will enable you to use the OAuth provider token to call additional APIs supported by the third-party provider to get more information.
        code: |
          ```python
          response = supabase.auth.sign_in_with_oauth(
              {
                  "provider": "github",
                  "options": {
                      "scopes": "repo gist notifications",
                  }
              }
          )
          ```

  - id: sign-in-with-id-token
    title: 'sign_in_with_id_token'
    params:
      - name: credentials
        type: SignInWithIdTokenCredentials
        subContent:
          - name: provider
            isOptional: false
            type: '"google" | "apple" | "azure" | "facebook" | "kakao" | string'
            description: |
              Provider name or OIDC `iss` value identifying which provider should be used to verify the provided token. Supported names: `google`, `apple`, `azure`, `facebook`, `kakao`, `keycloak` (deprecated).
          - name: token
            isOptional: false
            type: string
            description: |
              OIDC ID token issued by the specified provider. The `iss` claim in the ID token must match the supplied provider. Some ID tokens contain an `at_hash` which require that you provide an `access_token` value to be accepted properly. If the token contains a `nonce` claim you must supply the nonce used to obtain the ID token.
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: captcha_token
                isOptional: true
                type: string
          - name: nonce
            isOptional: true
            type: string
            description: |
              If the ID token contains a `nonce` claim, then the hash of this value is compared to the value in the ID token.
          - name: access_token
            isOptional: true
            type: string
            description: |
              If the ID token contains an `at_hash` claim, then the hash of this value is compared to the value in the ID token.
    notes: |
      Allows signing in with an OIDC ID token. The authentication provider used should be enabled and configured.
    examples:
      - id: sign-in-with-id-token
        name: 'Sign In using ID Token'
        code: |
          ```python
          response = supabase.auth.sign_in_with_id_token(
              {
                  "provider": "google", 
                  "token": "your-id-token",
              }
          )
          ```
        response: |
          ```json
          {
            "session": {
              "access_token": "<ACCESS_TOKEN>",
              "expires_at": **********,
              "expires_in": 60,
              "provider_refresh_token": null,
              "provider_token": null,
              "refresh_token": "<REFRESH_TOKEN>",
              "token_type": "bearer",
              "user": {
                "action_link": null,
                "app_metadata": {
                  ...
                },
                "aud": "authenticated",
                "confirmation_sent_at": null,
                "confirmed_at": "Wed, 17 Jul 2024 16:34:24 GMT",
                "created_at": "Wed, 17 Jul 2024 16:34:24 GMT",
                "email": "<EMAIL>",
                "email_change_sent_at": null,
                "email_confirmed_at": "Wed, 17 Jul 2024 16:34:24 GMT",
                "factors": null,
                "id": "11111111-1111-1111-1111-111111111111",
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "provider": "google",
                    "user_id": "11111111-1111-1111-1111-111111111111"
                  }
                ],
                "invited_at": null,
                "last_sign_in_at": "Wed, 17 Jul 2024 17:18:49 GMT",
                "new_email": null,
                "phone": "",
                "phone_confirmed_at": null,
                "recovery_sent_at": null,
                "role": "authenticated",
                "updated_at": "Wed, 17 Jul 2024 17:18:49 GMT",
                "user_metadata": {
                  ...
                }
              }
            },
            "user": {
              "action_link": null,
              "app_metadata": {
                ...
              },
              "aud": "authenticated",
              "confirmation_sent_at": null,
              "confirmed_at": "Wed, 17 Jul 2024 16:34:24 GMT",
              "created_at": "Wed, 17 Jul 2024 16:34:24 GMT",
              "email": "<EMAIL>",
              "email_change_sent_at": null,
              "email_confirmed_at": "Wed, 17 Jul 2024 16:34:24 GMT",
              "factors": null,
              "id": "11111111-1111-1111-1111-111111111111",
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "provider": "google",
                  "user_id": "11111111-1111-1111-1111-111111111111"
                }
              ],
              "invited_at": null,
              "last_sign_in_at": "Wed, 17 Jul 2024 17:18:49 GMT",
              "new_email": null,
              "phone": "",
              "phone_confirmed_at": null,
              "recovery_sent_at": null,
              "role": "authenticated",
              "updated_at": "Wed, 17 Jul 2024 17:18:49 GMT",
              "user_metadata": {
                ...
              }
            }
          }
          ```
  - id: sign-in-with-sso
    title: 'sign_in_with_sso()'
    overwriteParams:
      - name: params
        type: SignInWithSSOCredentials
        subContent:
          - name: provider_id
            isOptional: true
            type: string
            description: >
              UUID of the SSO provider.
              One of `provider_id` or `domain` is required.
          - name: domain
            isOptional: true
            type: string
            description: >
              Domain name of the organization to use SSO with.
              One of `provider_id` or `domain` is required.
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: redirect_to
                type: string
                description: >
                  The URL to redirect the user to after they have signed in.
                  Must be a configured redirect URL for your Supabase instance.
              - name: captcha_token
                type: string
    notes: |
      - Before you can call this method you need to [establish a connection](/docs/guides/auth/sso/auth-sso-saml#managing-saml-20-connections) to an identity provider. Use the [CLI commands](/docs/reference/cli/supabase-sso) to do this.
      - If you've associated an email domain to the identity provider, you can use the `domain` property to start a sign-in flow.
      - In case you need to use a different way to start the authentication flow with an identity provider, you can use the `provider_id` property. For example:
          - Mapping specific user email addresses with an identity provider.
          - Using different hints to identity the identity provider to be used by the user, like a company-specific page, IP address or other tracking information.
    examples:
      - id: sign-in-with-domain
        name: Sign in with email domain
        isSpotlight: true
        description: |
          You can extract the user's email domain and use it to trigger the authentication flow with the correct identity provider.
        code: |
          ```python
          response = supabase.auth.sign_in_with_sso(
              {"domain": "company.com"}
          )
          ```
        response: |
          ```json
          {
            "url": ""
          }
          ```
      - id: sign-in-with-provider-uuid
        name: Sign in with provider UUID
        isSpotlight: true
        description: |
          Useful when you need to map a user's sign in request according to different rules that can't use email domains.
        code: |
          ```python
          response = supabase.auth.sign_in_with_sso(
              {"provider_id": "21648a9d-8d5a-4555-a9d1-d6375dc14e92"}
          )
          ```
  - id: sign-out
    title: 'sign_out()'
    params:
      - name: options
        isOptional: true
        type: SignOutOptions
        subContent:
          - name: scope
            isOptional: true
            type: '"global" | "local" | "others"'
            description: |
              Determines which sessions should be logged out. Global means all sessions by this account. Local means only this session. Others means all other sessions except the current one. When using others, there is no sign-out event fired on the current session!
    notes: |
      - In order to use the `sign_out()` method, the user needs to be signed in first.
      - By default, `sign_out()` uses the global scope, which signs out all other sessions that the user is logged into as well.
      - Since Supabase Auth uses JWTs for authentication, the access token JWT will be valid until it's expired. When the user signs out, Supabase revokes the refresh token and deletes the JWT from the client-side. This does not revoke the JWT and it will still be valid until it expires.
    examples:
      - id: sign-out
        name: Sign out
        code: |
          ```python
          response = supabase.auth.sign_out()
          ```
  - id: verify-otp
    title: 'verify_otp'
    params:
      - name: params
        type: VerifyOtpParams
        subContent:
          - name: phone
            isOptional: true
            type: string
            description: One of `phone`, `email`, or `token_hash` must be provided.
          - name: email
            isOptional: true
            type: string
            description: One of `phone`, `email`, or `token_hash` must be provided.
          - name: token_hash
            isOptional: true
            type: string
            description: >
              The token hash from the user's email link.
              One of `phone`, `email`, or `token_hash` must be provided.
          - name: type
            type: sms | phone_change | signup | invite | magiclink | recovery | email_change | email
          - name: token
            isOptional: true
            type: string
            description: The OTP sent to the user. Required if using `phone` or `email`.
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: redirect_to
                isOptional: true
                type: string
                description: >
                  A URL to redirect the user to after they are confirmed.
                  Must be in your configured redirect URLs.
              - name: captcha_token
                isOptional: true
                type: string
                description: Deprecated.
    notes: |
      - The `verify_otp` method takes in different verification types. If a phone number is used, the type can either be `sms` or `phone_change`. If an email address is used, the type can be one of the following: `email`, `recovery`, `invite` or `email_change` (`signup` and `magiclink` types are deprecated).
      - The verification type used should be determined based on the corresponding auth method called before `verify_otp` to sign up / sign-in a user.
      - The `TokenHash` is contained in the [email templates](/docs/guides/auth/auth-email-templates) and can be used to sign in. You may wish to use the hash with Magic Links for the PKCE flow for Server Side Auth. See [this guide](/docs/guides/auth/server-side/email-based-auth-with-pkce-flow-for-ssr) for more details.
    examples:
      - id: verify-signup-one-time-password(otp)
        name: Verify Signup One-Time Password (OTP)
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.verify_otp(
              {
                  "email": "<EMAIL>", 
                  "token": "123456", 
                  "type": "email",
              }
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": []
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
              "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
              "email_change_sent_at": null,
              "new_email": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2023-02-19T00:01:51.142802Z",
              "confirmed_at": "2023-02-19T00:01:51.351735Z",
              "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-07-21T22:21:35.199451Z",
              "role": "authenticated",
              "updated_at": "2024-07-21T22:21:35.206985Z",
              "identities": [],
              "factors": null,
              "is_anonymous": false
            },
            "session": {
              "provider_token": null,
              "provider_refresh_token": null,
              "access_token": "<ACCESS_TOKEN>",
              "refresh_token": "<REFRESH_TOKEN>",
              "expires_in": 3600,
              "expires_at": **********,
              "token_type": "bearer",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "app_metadata": {
                  "provider": "email",
                  "providers": []
                },
                "user_metadata": {},
                "aud": "authenticated",
                "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
                "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
                "email_change_sent_at": null,
                "new_email": null,
                "invited_at": null,
                "action_link": null,
                "email": "<EMAIL>",
                "phone": "",
                "created_at": "2023-02-19T00:01:51.142802Z",
                "confirmed_at": "2023-02-19T00:01:51.351735Z",
                "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
                "phone_confirmed_at": null,
                "last_sign_in_at": "2024-07-21T22:21:35.199451Z",
                "role": "authenticated",
                "updated_at": "2024-07-21T22:21:35.206985Z",
                "identities": [],
                "factors": null,
                "is_anonymous": false
              }
            }
          }
          ```
      - id: verify-sms-one-time-password
        name: Verify SMS One-Time Password (OTP)
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.verify_otp(
              {
                  "phone": "+***********", 
                  "token": "123456", 
                  "type": "sms",
              }
          )
          ```
      - id: verify-email-auth(tokenhash)
        name: Verify Email Auth (Token Hash)
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.verify_otp(
              {
                  "email": "<EMAIL>", 
                  "token_hash": "<token-hash>", 
                  "type": "email",
              }
          )
          ```
  - id: get-session
    title: 'get_session'
    notes: |
      - This method retrieves the current local session (i.e in memory).
      - The session contains a signed JWT and unencoded session data.
      - Since the unencoded session data is retrieved from the local storage medium, **do not** rely on it as a source of trusted data on the server. It could be tampered with by the sender. If you need verified, trustworthy user data, call [`get_user`](/docs/reference/python/auth-getuser) instead.
      - If the session has an expired access token, this method will use the refresh token to get a new session.
    examples:
      - id: get-the-session-data
        name: Get the session data
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.get_session()
          ```
        response: |
          ```json
          {
            "provider_token": null,
            "provider_refresh_token": null,
            "access_token": "<ACCESS_TOKEN>",
            "refresh_token": "<REFRESH_TOKEN>",
            "expires_in": 3600,
            "expires_at": **********,
            "token_type": "bearer",
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": []
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
              "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
              "email_change_sent_at": null,
              "new_email": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2023-02-19T00:01:51.142802Z",
              "confirmed_at": "2023-02-19T00:01:51.351735Z",
              "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-07-21T22:36:45.194120Z",
              "role": "authenticated",
              "updated_at": "2024-07-21T22:36:45.196044Z",
              "identities": [],
              "factors": null,
              "is_anonymous": false
            }
          }
          ```
  - id: get-user
    title: 'get_user'
    params:
      - name: jwt
        isOptional: true
        type: string
        description: Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.
    notes: |
      - This method fetches the user object from the database instead of local session.
      - This method is useful for checking if the user is authorized because it validates the user's access token JWT on the server.
    examples:
      - id: get-the-logged-in-user-with-the-current-existing-session
        name: Get the logged in user with the current existing session
        isSpotlight: true
        code: |
          ```
          response = supabase.auth.get_user()
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": []
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
              "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
              "email_change_sent_at": null,
              "new_email": null,
              "new_phone": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2023-02-19T00:01:51.142802Z",
              "confirmed_at": "2023-02-19T00:01:51.351735Z",
              "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-07-24T22:24:57.642878Z",
              "role": "authenticated",
              "updated_at": "2024-07-24T22:24:57.650021Z",
              "identities": [],
              "is_anonymous": false,
              "factors": null
            }
          }
          ```
      - id: get-the-logged-in-user-with-a-custom-access-token-jwt
        name: Get the logged in user with a custom access token jwt
        isSpotlight: false
        code: |
          ```
          response = supabase.auth.get_user(jwt)
          ```
  - id: update-user
    title: 'update_user()'
    notes: |
      - In order to use the `update_user()` method, the user needs to be signed in first.
      - By default, email updates sends a confirmation link to both the user's current and new email.
      To only send a confirmation link to the user's new email, disable **Secure email change** in your project's [email auth provider settings](/dashboard/project/_/auth/providers).

    examples:
      - id: update-the-email-for-an-authenticated-user
        name: Update the email for an authenticated user
        description: Sends a "Confirm Email Change" email to the new email address.
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.update_user(
              {"email": "<EMAIL>"}
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "email_confirmed_at": "2024-01-01T00:00:00Z",
              "phone": "",
              "confirmed_at": "2024-01-01T00:00:00Z",
              "new_email": "<EMAIL>",
              "email_change_sent_at": "2024-01-01T00:00:00Z",
              "last_sign_in_at": "2024-01-01T00:00:00Z",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email"
                ]
              },
              "user_metadata": {
                "email": "<EMAIL>",
                "email_verified": false,
                "phone_verified": false,
                "sub": "11111111-1111-1111-1111-111111111111"
              },
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "11111111-1111-1111-1111-111111111111",
                  "user_id": "11111111-1111-1111-1111-111111111111",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            }
          }
          ```
      - id: update-the-phone-for-an-authenticated-user
        name: Update the phone number for an authenticated user
        description: Sends a one-time password (OTP) to the new phone number.
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.update_user(
              {"phone": "*********"}
          )
          ```
      - id: update-the-password-for-an-authenticated-user
        name: Update the password for an authenticated user
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.update_user(
              {"password": "new password"}
          )
          ```
      - id: update-the-users-metadata
        name: Update the user's metadata
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.update_user(
              {
                  "data": {"hello": "world"},
              }
          )
          ```
      - id: update-password-with-reauthentication
        name: Update the user's password with a nonce
        description: |
          If **Secure password change** is enabled in your [project's email provider settings](/dashboard/project/_/auth/providers), updating the user's password would require a nonce if the user **hasn't recently signed in**. The nonce is sent to the user's email or phone number. A user is deemed recently signed in if the session was created in the last 24 hours.
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.update_user(
              {
                  "password": "new password",
                  "nonce": "123456",
              }
          )
          ```
  - id: get-user-identities
    title: 'get_user_identities()'
    notes: |
      Gets all the identities linked to a user.
      - The user needs to be signed in to call `get_user_identities()`.
    examples:
      - id: get-user-identities
        name: Returns a list of identities linked to the user
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.get_user_identities()
          ```
        response: |
          ```json
          {
            "identities": [
              {
                "identity_id": "22222222-2222-2222-2222-222222222222",
                "id": "2024-01-01T00:00:00Z",
                "user_id": "2024-01-01T00:00:00Z",
                "identity_data": {
                  "email": "<EMAIL>",
                  "email_verified": false,
                  "phone_verified": false,
                  "sub": "11111111-1111-1111-1111-111111111111"
                },
                "provider": "email",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "email": "<EMAIL>"
              }
            ]
          }
          ```
  - id: link-identity
    title: 'link_identity()'
    params:
      - name: credentials
        isOptional: false
        type: SignInWithOAuthCredentials
        subContent:
          - name: provider
            isOptional: false
            type: boolean
            description: One of the providers supported by GoTrue.
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: scopes
                isOptional: true
                type: string
                description: A space-separated list of scopes granted to the OAuth application.
              - name: redirect_to
                isOptional: true
                type: string
                description: A URL to send the user to after they are confirmed.
              - name: query_params
                isOptional: true
                type: string
                description: An object of query params
    notes: |
      - The **Enable Manual Linking** option must be enabled from your [project's authentication settings](/dashboard/project/_/settings/auth).
      - The user needs to be signed in to call `link_identity()`.
      - If the candidate identity is already linked to the existing user or another user, `link_identity()` will fail.
      - If `link_identity` is run on the server, you should handle the redirect.
    examples:
      - id: link-identity
        name: Link an identity to a user
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.link_identity(
              {provider: "github"}
          )
          ```
        response: |
          ```json
          {
            "provider": "github",
            "url": "<PROVIDER_URL_TO_REDIRECT_TO>"
          }
          ```
  - id: unlink-identity
    title: 'unlink_identity()'
    params:
      - name: identity
        isOptional: false
        type: UserIdentity
        subContent:
          - name: id
            isOptional: false
            type: string
          - name: identity_id
            isOptional: false
            type: string
          - name: provider
            isOptional: false
            type: string
          - name: user_id
            isOptional: false
            type: string
          - name: created_at
            isOptional: true
            type: string
          - name: identity_data
            isOptional: true
            type: Dict[string, Any]
          - name: last_sign_in_at
            isOptional: true
            type: string
          - name: updated_at
            isOptional: true
            type: string
    notes: |
      - The **Enable Manual Linking** option must be enabled from your [project's authentication settings](/dashboard/project/_/settings/auth).
      - The user needs to be signed in to call `unlink_identity()`.
      - The user must have at least 2 identities in order to unlink an identity.
      - The identity to be unlinked must belong to the user.
    examples:
      - id: unlink-identity
        name: Unlink an identity
        isSpotlight: true
        code: |
          ```python
          # retrieve all identites linked to a user
          response = supabase.auth.get_user_identities()

          # find the google identity
          google_identity = list(
              filter(lambda identity: identity.provider == "google", res.identities)
          ).pop()

          # unlink the google identity
          response = supabase.auth.unlink_identity(google_identity)
          ```
  - id: send-password-reauthentication
    title: 'reauthenticate()'
    notes: |
      - This method is used together with `updateUser()` when a user's password needs to be updated.
      - If you require your user to reauthenticate before updating their password, you need to enable the **Secure password change** option in your [project's email provider settings](/dashboard/project/_/auth/providers).
      - A user is only require to reauthenticate before updating their password if **Secure password change** is enabled and the user **hasn't recently signed in**. A user is deemed recently signed in if the session was created in the last 24 hours.
      - This method will send a nonce to the user's email. If the user doesn't have a confirmed email address, the method will send the nonce to the user's confirmed phone number instead.
    examples:
      - id: send-reauthentication-nonce
        name: Send reauthentication nonce
        description: Sends a reauthentication nonce to the user's email or phone number.
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.reauthenticate()
          ```
  - id: resend-email-or-phone-otps
    title: 'resend()'
    params:
      - name: credentials
        isOptional: false
        type: ResendCredentials
        subContent:
          - name: email
            isOptional: true
            type: string
            description: One of email or phone must be provided.
          - name: phone
            isOptional: true
            type: string
            description: One of email or phone must be provided.
          - name: type
            isOptional: false
            type: signup | email_change | sms | phone_change
          - name: options
            isOptional: true
            type: object
            subContent:
              - name: captcha_token
                isOptional: true
                type: string
                description: Verification token received when the user completes the captcha on the site.
              - name: email_redirect_to
                isOptional: true
                type: string
                description: A URL to send the user to after they have signed-in.
    notes: |
      - Resends a signup confirmation, email change or phone change email to the user.
      - Passwordless sign-ins can be resent by calling the `sign_in_with_otp()` method again.
      - Password recovery emails can be resent by calling the `reset_password_for_email()` method again.
      - This method will only resend an email or phone OTP to the user if there was an initial signup, email change or phone change request being made.
      - You can specify a redirect url when you resend an email link using the `email_redirect_to` option.
    examples:
      - id: resend-email-signup-confirmation
        name: Resend an email signup confirmation
        description: Resends the email signup confirmation to the user
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.resend(
              {
                  "type": "signup",
                  "email": "<EMAIL>",
                  "options": {
                      "email_redirect_to": "https://example.com/welcome",
                  },
              }
          )
          ```
      - id: resend-phone-signup-confirmation
        name: Resend a phone signup confirmation
        description: Resends the phone signup confirmation email to the user
        code: |
          ```python
          response = supabase.auth.resend(
              {
                  "type": "sms",
                  "phone": "**********",
              }
          )
          ```
      - id: resend-email-change-email
        name: Resend email change email
        description: Resends the email change email to the user
        code: |
          ```python
          response = supabase.auth.resend(
              {
                  "type": "email_change",
                  "email": "<EMAIL>",
              }
          )
          ```
      - id: resend-phone-change
        name: Resend phone change OTP
        description: Resends the phone change OTP to the user
        code: |
          ```python
          response = supabase.auth.resend(
              {
                  "type": "phone_change",
                  "phone": "**********",
              }
          )
          ```
  - id: set-session
    title: 'set_session()'
    params:
      - name: access_token
        isOptional: false
        type: string
      - name: refresh_token
        isOptional: false
        type: string
    description: |
      Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.
      If the refresh token or access token in the current session is invalid, an error will be thrown.
    notes: |
      - This method sets the session using an `access_token` and `refresh_token`.
      - If successful, a `SIGNED_IN` event is emitted.
    examples:
      - id: set-session
        name: Refresh the session
        description: Sets the session data from refresh_token and returns current session or raise an error if the refresh_token is invalid.
        code: |
          ```python
          response = supabase.auth.set_session(access_token, refresh_token)
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": []
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
              "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
              "email_change_sent_at": null,
              "new_email": null,
              "new_phone": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2023-02-19T00:01:51.142802Z",
              "confirmed_at": "2023-02-19T00:01:51.351735Z",
              "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-07-24T22:24:57.642878Z",
              "role": "authenticated",
              "updated_at": "2024-07-24T22:24:57.650021Z",
              "identities": [],
              "is_anonymous": false,
              "factors": null
            },
            "session": {
              "provider_token": null,
              "provider_refresh_token": null,
              "access_token": "<ACCESS_TOKEN>",
              "refresh_token": "<REFRESH_TOKEN>",
              "expires_in": 89,
              "expires_at": **********,
              "token_type": "bearer",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "app_metadata": {
                  "provider": "email",
                  "providers": []
                },
                "user_metadata": {},
                "aud": "authenticated",
                "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
                "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
                "email_change_sent_at": null,
                "new_email": null,
                "new_phone": null,
                "invited_at": null,
                "action_link": null,
                "email": "<EMAIL>",
                "phone": "",
                "created_at": "2023-02-19T00:01:51.142802Z",
                "confirmed_at": "2023-02-19T00:01:51.351735Z",
                "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
                "phone_confirmed_at": null,
                "last_sign_in_at": "2024-07-24T22:24:57.642878Z",
                "role": "authenticated",
                "updated_at": "2024-07-24T22:24:57.650021Z",
                "identities": [],
                "is_anonymous": false,
                "factors": null
              }
            }
          }
          ```
  - id: refresh-session
    title: 'refresh_session()'
    params:
      - name: refresh_token
        isOptional: true
        type: string
    description: |
      Returns a new session, regardless of expiry status.
      Takes in an optional refresh token. If not passed in, then refresh_session() will attempt to retrieve it from get_session().
      If the current session's refresh token is invalid, an error will be thrown.
    notes: |
      - This method will refresh the session whether the current one is expired or not.
    examples:
      - id: refresh-session
        name: Refresh session using the current session
        code: |
          ```
          response = supabase.auth.refresh_session()
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "app_metadata": {
                "provider": "email",
                "providers": []
              },
              "user_metadata": {},
              "aud": "authenticated",
              "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
              "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
              "email_change_sent_at": null,
              "new_email": null,
              "new_phone": null,
              "invited_at": null,
              "action_link": null,
              "email": "<EMAIL>",
              "phone": "",
              "created_at": "2023-02-19T00:01:51.142802Z",
              "confirmed_at": "2023-02-19T00:01:51.351735Z",
              "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
              "phone_confirmed_at": null,
              "last_sign_in_at": "2024-07-24T22:24:57.642878Z",
              "role": "authenticated",
              "updated_at": "2024-07-24T22:24:57.650021Z",
              "identities": [],
              "is_anonymous": false,
              "factors": null
            },
            "session": {
              "provider_token": null,
              "provider_refresh_token": null,
              "access_token": "<ACCESS_TOKEN>",
              "refresh_token": "<REFRESH_TOKEN>",
              "expires_in": 89,
              "expires_at": **********,
              "token_type": "bearer",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "app_metadata": {
                  "provider": "email",
                  "providers": []
                },
                "user_metadata": {},
                "aud": "authenticated",
                "confirmation_sent_at": "2023-02-19T00:01:51.147035Z",
                "recovery_sent_at": "2024-07-21T22:20:00.366959Z",
                "email_change_sent_at": null,
                "new_email": null,
                "new_phone": null,
                "invited_at": null,
                "action_link": null,
                "email": "<EMAIL>",
                "phone": "",
                "created_at": "2023-02-19T00:01:51.142802Z",
                "confirmed_at": "2023-02-19T00:01:51.351735Z",
                "email_confirmed_at": "2023-02-19T00:01:51.351735Z",
                "phone_confirmed_at": null,
                "last_sign_in_at": "2024-07-24T22:24:57.642878Z",
                "role": "authenticated",
                "updated_at": "2024-07-24T22:24:57.650021Z",
                "identities": [],
                "is_anonymous": false,
                "factors": null
              }
            }
          }
          ```

  - id: exchange-code-for-session
    title: 'exchange_code_for_session()'
    params:
      - name: auth_code
        isOptional: false
        type: string
    notes: |
      Log in an existing user by exchanging an Auth Code issued during the PKCE flow.

      - Used when `flow_type` is set to `pkce` in client options.
    examples:
      - id: exchange-auth-code
        name: Exchange Auth Code
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.exchange_code_for_session(
              {"auth_code": "34e770dd-9ff9-416c-87fa-43b31d7ef225"}
          )
          ```
        response: |
          ```json
          {
            "session": {
              "access_token": "<ACCESS_TOKEN>",
              "token_type": "bearer",
              "expires_in": 3600,
              "expires_at": **********,
              "refresh_token": "<REFRESH_TOKEN>",
              "user": {
                "id": "11111111-1111-1111-1111-111111111111",
                "aud": "authenticated",
                "role": "authenticated",
                "email": "<EMAIL>",
                "email_confirmed_at": "2024-01-01T00:00:00Z",
                "phone": "",
                "confirmation_sent_at": "2024-01-01T00:00:00Z",
                "confirmed_at": "2024-01-01T00:00:00Z",
                "last_sign_in_at": "2024-01-01T00:00:00Z",
                "app_metadata": {
                  "provider": "email",
                  "providers": [
                    "email",
                    "<OTHER_PROVIDER>"
                  ]
                },
                "user_metadata": {
                  "email": "<EMAIL>",
                  "email_verified": true,
                  "full_name": "User Name",
                  "iss": "<ISS>",
                  "name": "User Name",
                  "phone_verified": false,
                  "provider_id": "<PROVIDER_ID>",
                  "sub": "<SUB>"
                },
                "identities": [
                  {
                    "identity_id": "22222222-2222-2222-2222-222222222222",
                    "id": "11111111-1111-1111-1111-111111111111",
                    "user_id": "11111111-1111-1111-1111-111111111111",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": false,
                      "phone_verified": false,
                      "sub": "11111111-1111-1111-1111-111111111111"
                    },
                    "provider": "email",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  },
                  {
                    "identity_id": "33333333-3333-3333-3333-333333333333",
                    "id": "<ID>",
                    "user_id": "<USER_ID>",
                    "identity_data": {
                      "email": "<EMAIL>",
                      "email_verified": true,
                      "full_name": "User Name",
                      "iss": "<ISS>",
                      "name": "User Name",
                      "phone_verified": false,
                      "provider_id": "<PROVIDER_ID>",
                      "sub": "<SUB>"
                    },
                    "provider": "<PROVIDER>",
                    "last_sign_in_at": "2024-01-01T00:00:00Z",
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z",
                    "email": "<EMAIL>"
                  }
                ],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "is_anonymous": false
              },
              "provider_token": "<PROVIDER_TOKEN>",
              "provider_refresh_token": "<PROVIDER_REFRESH_TOKEN>"
            },
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "email_confirmed_at": "2024-01-01T00:00:00Z",
              "phone": "",
              "confirmation_sent_at": "2024-01-01T00:00:00Z",
              "confirmed_at": "2024-01-01T00:00:00Z",
              "last_sign_in_at": "2024-01-01T00:00:00Z",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email",
                  "<OTHER_PROVIDER>"
                ]
              },
              "user_metadata": {
                "email": "<EMAIL>",
                "email_verified": true,
                "full_name": "User Name",
                "iss": "<ISS>",
                "name": "User Name",
                "phone_verified": false,
                "provider_id": "<PROVIDER_ID>",
                "sub": "<SUB>"
              },
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "11111111-1111-1111-1111-111111111111",
                  "user_id": "11111111-1111-1111-1111-111111111111",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                },
                {
                  "identity_id": "33333333-3333-3333-3333-333333333333",
                  "id": "<ID>",
                  "user_id": "<USER_ID>",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": true,
                    "full_name": "User Name",
                    "iss": "<ISS>",
                    "name": "User Name",
                    "phone_verified": false,
                    "provider_id": "<PROVIDER_ID>",
                    "sub": "<SUB>"
                  },
                  "provider": "<PROVIDER>",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            },
            "redirect_type": null
          }
          ```
  - id: auth-mfa-api
    title: 'Overview'
    notes: |
      This section contains methods commonly used for Multi-Factor Authentication (MFA) and are invoked behind the `supabase.auth.mfa` namespace.

      Currently, we only support time-based one-time password (TOTP) as the 2nd factor. We don't support recovery codes but we allow users to enroll more than 1 TOTP factor, with an upper limit of 10.

      Having a 2nd TOTP factor for recovery frees the user of the burden of having to store their recovery codes somewhere. It also reduces the attack surface since multiple recovery codes are usually generated compared to just having 1 backup TOTP factor.
  - id: mfa-enroll
    title: 'mfa.enroll()'
    notes: |
      - Currently, `totp` is the only supported `factor_type`. The returned `id` should be used to create a challenge.
      - To create a challenge, see [`mfa.challenge()`](/docs/reference/python/auth-mfa-challenge).
      - To verify a challenge, see [`mfa.verify()`](/docs/reference/python/auth-mfa-verify).
      - To create and verify a challenge in a single step, see [`mfa.challenge_and_verify()`](/docs/reference/python/auth-mfa-challengeandverify).
    examples:
      - id: enroll-totp-factor
        name: Enroll a time-based, one-time password (TOTP) factor
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.mfa.enroll(
              {
                  "factor_type": "totp",
                  "friendly_name": "your_friendly_name",
              }
          )
          ```
  - id: mfa-challenge
    title: 'mfa.challenge()'
    notes: |
      - An [enrolled factor](/docs/reference/python/auth-mfa-enroll) is required before creating a challenge.
      - To verify a challenge, see [`mfa.verify()`](/docs/reference/python/auth-mfa-verify).
    examples:
      - id: create-mfa-challenge
        name: Create a challenge for a factor
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.mfa.challenge(
              {"factor_id": "34e770dd-9ff9-416c-87fa-43b31d7ef225"}
          )
          ```
  - id: mfa-verify
    title: 'mfa.verify()'
    notes: |
      - To verify a challenge, please [create a challenge](/docs/reference/python/auth-mfa-challenge) first.
    examples:
      - id: verify-challenge
        name: Verify a challenge for a factor
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.mfa.verify(
              {
                  "factor_id": "34e770dd-9ff9-416c-87fa-43b31d7ef225",
                  "challenge_id": "4034ae6f-a8ce-4fb5-8ee5-69a5863a7c15",
                  "code": "123456",
              }
          )
          ```
  - id: mfa-challenge-and-verify
    title: 'mfa.challenge_and_verify()'
    notes: |
      - An [enrolled factor](/docs/reference/python/auth-mfa-enroll) is required before invoking `challengeAndVerify()`.
      - Executes [`mfa.challenge()`](/docs/reference/python/auth-mfa-challenge) and [`mfa.verify()`](/docs/reference/python/auth-mfa-verify) in a single step.
    examples:
      - id: challenge-and-verify
        name: Create and verify a challenge for a factor
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.mfa.challenge_and_verify(
              {
                  "factor_id": "34e770dd-9ff9-416c-87fa-43b31d7ef225",
                  "code": "123456",
              }
          )
          ```
  - id: mfa-unenroll
    title: 'mfa.unenroll()'
    examples:
      - id: unenroll-a-factor
        name: Unenroll a factor
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.mfa.unenroll(
              {"factor_id": "34e770dd-9ff9-416c-87fa-43b31d7ef225"}
          )
          ```
  - id: mfa-get-authenticator-assurance-level
    title: 'mfa.get_authenticator_assurance_level()'
    notes: |
      - Authenticator Assurance Level (AAL) is the measure of the strength of an authentication mechanism.
      - In Supabase, having an AAL of `aal1` refers to having the 1st factor of authentication such as an email and password or OAuth sign-in while `aal2` refers to the 2nd factor of authentication such as a time-based, one-time-password (TOTP).
      - If the user has a verified factor, the `next_level` field will return `aal2`, else, it will return `aal1`.
    examples:
      - id: get-aal
        name: Get the AAL details of a session
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.mfa.get_authenticator_assurance_level()
          ```
  - id: admin-api
    title: 'Overview'
    notes: |
      - Any method under the `supabase.auth.admin` namespace requires a `service_role` key.
      - These methods are considered admin methods and should be called on a trusted server. Never expose your `service_role` key in the browser.
    examples:
      - id: create-auth-admin-client
        name: Create server-side auth client
        isSpotlight: true
        code: |
          ```python
          from supabase import create_client
          from supabase.lib.client_options import ClientOptions

          supabase = create_client(
              supabase_url,
              service_role_key,
              options=ClientOptions(
                  auto_refresh_token=False,
                  persist_session=False,
              )
          )

          # Access auth admin api
          admin_auth_client = supabase.auth.admin
          ```

  - id: get-user-by-id
    title: 'get_user_by_id()'
    params:
      - name: uid
        isOptional: false
        type: string
        description: |
          The user's unique identifier

          This function should only be called on a server. Never expose your `service_role` key in the browser.
    notes: |
      - Fetches the user object from the database based on the user's id.
      - The `get_user_by_id()` method requires the user's id which maps to the `auth.users.id` column.
    examples:
      - id: fetch-the-user-object-using-the-access-token-jwt
        name: Fetch the user object using the access_token jwt
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.get_user_by_id(1)
          ```
        response: |
          ```json
          {
            "user": {
              "id": "1",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "email_confirmed_at": "2024-01-01T00:00:00Z",
              "phone": "",
              "confirmation_sent_at": "2024-01-01T00:00:00Z",
              "confirmed_at": "2024-01-01T00:00:00Z",
              "last_sign_in_at": "2024-01-01T00:00:00Z",
              "app_metadata": {},
              "user_metadata": {},
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "1",
                  "user_id": "1",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": true,
                    "phone_verified": false,
                    "sub": "1"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            }
          }
          ```
  - id: list-users
    title: 'list_users()'
    params:
      - name: params
        isOptional: true
        type: PageParams
        description: |
          An object which supports page and per_page as numbers, to alter the paginated results.
        subContent:
          - name: page
            isOptional: true
            type: number
            description: The page number
          - name: per_page
            isOptional: true
            type: number
            description: Number of items returned per page
    notes: |
      - Defaults to return 50 users per page.
    examples:
      - id: get-a-full-list-of-users
        name: Get a page of users
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.list_users()
          ```
      - id: get-paginated-list-of-users
        name: Paginated list of users
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.list_users(
              page=1,
              per_page=1000,
          )
          ```
  - id: create-user
    title: 'create_user()'
    params:
      - name: attributes
        isOptional: false
        type: AdminUserAttributes
        subContent:
          - name: app_metadata
            isOptional: true
            type: object
            description: |
              A custom data object to store the user's application specific metadata. This maps to the `auth.users.app_metadata` column.
          - name: ban_duration
            isOptional: true
            type: string
            description: Determines how long a user is banned for.
          - name: email
            isOptional: true
            type: string
            description: The user's email.
          - name: email_confirm
            isOptional: true
            type: boolean
            description: Confirms the user's email address if set to true.
          - name: nonce
            isOptional: true
            type: string
            description: The nonce sent for reauthentication if the user's password is to be updated.
          - name: password
            isOptional: true
            type: string
            description: The user's password.
          - name: phone
            isOptional: true
            type: string
            description: The user's phone.
          - name: phone_confirm
            isOptional: true
            type: boolean
            description: Confirms the user's phone number if set to true.
          - name: role
            isOptional: true
            type: string
            description: The `role` claim set in the user's access token JWT.
          - name: user_metadata
            isOptional: true
            type: object
            description: |
              A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.
    notes: |
      - To confirm the user's email address or phone number, set `email_confirm` or `phone_confirm` to true. Both arguments default to false.
      - `create_user()` will not send a confirmation email to the user. You can use [`invite_user_by_email()`](/docs/reference/python/auth-admin-inviteuserbyemail) if you want to send them an email invite instead.
      - If you are sure that the created user's email or phone number is legitimate and verified, you can set the `email_confirm` or `phone_confirm` param to `true`.
    examples:
      - id: create-a-new-user-with-custom-user-metadata
        name: With custom user metadata
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.create_user(
              {
                  "email": "<EMAIL>",
                  "password": "password",
                  "user_metadata": {"name": "Yoda"},
              }
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "1",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "email_confirmed_at": "2024-01-01T00:00:00Z",
              "phone": "",
              "confirmation_sent_at": "2024-01-01T00:00:00Z",
              "confirmed_at": "2024-01-01T00:00:00Z",
              "last_sign_in_at": "2024-01-01T00:00:00Z",
              "app_metadata": {},
              "user_metadata": {},
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "1",
                  "user_id": "1",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": true,
                    "phone_verified": false,
                    "sub": "1"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            }
          }
          ```
      - id: auto-confirm-the-users-email
        name: Auto-confirm the user's email
        code: |
          ```python
          response = supabase.auth.admin.create_user(
              {
                  "email": "<EMAIL>",
                  "email_confirm": True,
              }
          )
          ```
      - id: auto-confirm-the-users-phone-number
        name: Auto-confirm the user's phone number
        code: |
          ```python
          response = supabase.auth.admin.create_user(
              {
                  "phone": "**********",
                  "phone_confirm": True,
              }
          )
          ```
  - id: delete-user
    title: 'delete_user()'
    params:
      - name: id
        isOptional: false
        type: string
        description: The user id you want to remove.
      - name: should_soft_delete
        isOptional: true
        type: boolean
        description: |
          If true, then the user will be soft-deleted (setting `deleted_at` to the current timestamp and disabling their account while preserving their data) from the auth schema. Defaults to false for backward compatibility.

          This function should only be called on a server. Never expose your `service_role` key in the browser.
    description: Delete a user. Requires a `service_role` key.
    notes: |
      - The `delete_user()` method requires the user's ID, which maps to the `auth.users.id` column.
    examples:
      - id: removes-a-user
        name: Removes a user
        isSpotlight: true
        code: |
          ```python
          supabase.auth.admin.delete_user(
              "715ed5db-f090-4b8c-a067-640ecee36aa0"
          )
          ```
  - id: invite-user-by-email
    title: 'invite_user_by_email()'
    params:
      - name: email
        isOptional: false
        type: string
        description: The email address of the user.
      - name: options
        isOptional: true
        type: InviteUserByEmailOptions
        subContent:
          - name: data
            isOptional: true
            type: object
            description: |
              A custom data object to store additional metadata about the user. This maps to the `auth.users.user_metadata` column.
          - name: redirect_to
            isOptional: true
            type: string
            description: |
              The URL which will be appended to the email link sent to the user's email address. Once clicked the user will end up on this URL.
    description: Sends an invite link to an email address.
    notes: |
      - Sends an invite link to the user's email address.
      - The `invite_user_by_email()` method is typically used by administrators to invite users to join the application.
      - Note that PKCE is not supported when using `invite_user_by_email`. This is because the browser initiating the invite is often different from the browser accepting the invite which makes it difficult to provide the security guarantees required of the PKCE flow.
    examples:
      - id: invite-a-user
        name: Invite a user
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.invite_user_by_email("<EMAIL>")
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "invited_at": "2024-01-01T00:00:00Z",
              "phone": "",
              "confirmation_sent_at": "2024-01-01T00:00:00Z",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email"
                ]
              },
              "user_metadata": {},
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "11111111-1111-1111-1111-111111111111",
                  "user_id": "11111111-1111-1111-1111-111111111111",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            }
          }
          ```
  - id: generate-link
    title: 'generate_link()'
    params:
      - name: params
        type: GenerateLinkParams
        subContent:
          - name: type
            type: 'signup | invite | magiclink | recovery | email_change_current | email_change_new'
          - name: email
            type: string
          - name: password
            type: string
            isOptional: true
            description: Only required if type is `signup`.
          - name: new_email
            type: string
            isOptional: true
            description: Only required if type is `email_change_current` or `email_change_new`.
          - name: options
            type: object
            isOptional: true
            subContent:
              - name: data
                type: object
                description: >
                  Custom JSON object containing user metadata, to be stored in the `raw_user_meta_data` column.
                  Only accepted if type is `signup`, `invite`, or `magiclink`.
              - name: redirect_to
                type: string
                description: >
                  A redirect URL which will be appended to the generated email link.
    notes: |
      - The following types can be passed into `generate_link()`: `signup`, `magiclink`, `invite`, `recovery`, `email_change_current`, `email_change_new`, `phone_change`.
      - `generate_link()` only generates the email link for `email_change_email` if the **Secure email change** is enabled in your project's [email auth provider settings](/dashboard/project/_/auth/providers).
      - `generate_link()` handles the creation of the user for `signup`, `invite` and `magiclink`.
    examples:
      - id: generate-a-signup-link
        name: Generate a signup link
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.generate_link(
              {
                  "type": "signup",
                  "email": "<EMAIL>",
                  "password": "secret",
              }
          )
          ```
        response: |
          ```json
          {
            "properties": {
              "action_link": "<LINK_TO_SEND_TO_USER>",
              "email_otp": "999999",
              "hashed_token": "<HASHED_TOKEN",
              "redirect_to": "<REDIRECT_URL>",
              "verification_type": "signup"
            },
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "phone": "",
              "confirmation_sent_at": "2024-01-01T00:00:00Z",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email"
                ]
              },
              "user_metadata": {},
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "11111111-1111-1111-1111-111111111111",
                  "user_id": "11111111-1111-1111-1111-111111111111",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            }
          }
          ```
      - id: generate-an-invite-link
        name: Generate an invite link
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.generate_link(
              {
                  "type": "invite",
                  "email": "<EMAIL>",
              }
          )
          ```
      - id: generate-a-magic-link
        name: Generate a magic link
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.generate_link(
              {
                  "type": "magiclink",
                  "email": "<EMAIL>",
              }
          )
          ```
      - id: generate-a-recovery-link
        name: Generate a recovery link
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.generate_link(
              {
                  "type": "recovery",
                  "email": "<EMAIL>",
              }
          )
          ```
      - id: generate-links-to-change-current-email-address
        name: Generate links to change current email address
        isSpotlight: false
        code: |
          ```python
          # Generate an email change link to be sent to the current email address
          response = supabase.auth.admin.generate_link(
              {
                  "type": "email_change_current",
                  "email": "<EMAIL>",
                  "new_email": "<EMAIL>",
              }
          )

          # Generate an email change link to be sent to the new email address
          response = supabase.auth.admin.generate_link(
              {
                  "type": "email_change_new",
                  "email": "<EMAIL>",
                  "new_email": "<EMAIL>",
              }
          )
          ```
  - id: update-user-by-id
    title: 'update_user_by_id()'
    params:
      - name: uid
        isOptional: false
        type: string
      - name: attributes
        isOptional: false
        type: AdminUserAttributes
        description: |
          The data you want to update.

          This function should only be called on a server. Never expose your `service_role` key in the browser.
        subContent:
          - name: app_metadata
            isOptional: true
            type: object
            description: |
              A custom data object to store the user's application specific metadata. This maps to the `auth.users.app_metadata` column.
          - name: ban_duration
            isOptional: true
            type: string
            description: Determines how long a user is banned for.
          - name: email
            isOptional: true
            type: string
            description: The user's email.
          - name: email_confirm
            isOptional: true
            type: boolean
            description: Confirms the user's email address if set to true.
          - name: nonce
            isOptional: true
            type: string
            description: The nonce sent for reauthentication if the user's password is to be updated.
          - name: password
            isOptional: true
            type: string
            description: The user's password.
          - name: phone
            isOptional: true
            type: string
            description: The user's phone.
          - name: phone_confirm
            isOptional: true
            type: boolean
            description: Confirms the user's phone number if set to true.
          - name: role
            isOptional: true
            type: string
            description: The `role` claim set in the user's access token JWT.
          - name: user_metadata
            isOptional: true
            type: string
            description: A custom data object to store the user's metadata. This maps to the `auth.users.raw_user_meta_data` column.
    examples:
      - id: updates-a-users-email
        name: Updates a user's email
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.update_user_by_id(
              "11111111-1111-1111-1111-111111111111",
              {
                  "email": "<EMAIL>",
              }
          )
          ```
        response: |
          ```json
          {
            "user": {
              "id": "11111111-1111-1111-1111-111111111111",
              "aud": "authenticated",
              "role": "authenticated",
              "email": "<EMAIL>",
              "email_confirmed_at": "2024-01-01T00:00:00Z",
              "phone": "",
              "confirmed_at": "2024-01-01T00:00:00Z",
              "recovery_sent_at": "2024-01-01T00:00:00Z",
              "last_sign_in_at": "2024-01-01T00:00:00Z",
              "app_metadata": {
                "provider": "email",
                "providers": [
                  "email"
                ]
              },
              "user_metadata": {
                "email": "<EMAIL>",
                "email_verified": false,
                "phone_verified": false,
                "sub": "11111111-1111-1111-1111-111111111111"
              },
              "identities": [
                {
                  "identity_id": "22222222-2222-2222-2222-222222222222",
                  "id": "11111111-1111-1111-1111-111111111111",
                  "user_id": "11111111-1111-1111-1111-111111111111",
                  "identity_data": {
                    "email": "<EMAIL>",
                    "email_verified": false,
                    "phone_verified": false,
                    "sub": "11111111-1111-1111-1111-111111111111"
                  },
                  "provider": "email",
                  "last_sign_in_at": "2024-01-01T00:00:00Z",
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "email": "<EMAIL>"
                }
              ],
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "is_anonymous": false
            }
          }
          ```
      - id: updates-a-users-password
        name: Updates a user's password
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.update_user_by_id(
              "6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4",
              {
                  "password": "new_password",
              }
          )
          ```
      - id: updates-a-users-metadata
        name: Updates a user's metadata
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.update_user_by_id(
              "6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4",
              {
                  "user_metadata": {"hello": "world"},
              }
          )
          ```
      - id: updates-a-users-app-metadata
        name: Updates a user's app_metadata
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.update_user_by_id(
              "6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4",
              {
                  "app_metadata": {"plan": "trial"},
              }
          )
          ```
      - id: confirms-a-users-email-address
        name: Confirms a user's email address
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.update_user_by_id(
              "6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4",
              {
                  "email_confirm": True,
              }
          )
          ```
      - id: confirms-a-users-phone-number
        name: Confirms a user's phone number
        isSpotlight: false
        code: |
          ```python
          response = supabase.auth.admin.update_user_by_id(
              "6aa5d0d4-2a9f-4483-b6c8-0cf4c6c98ac4",
              {
                  "phone_confirm": True,
              }
          )
          ```
  - id: mfa-delete-factor
    title: 'mfa.delete_factor()'
    params:
      - name: params
        isOptional: false
        type: AuthMFAAdminDeleteFactorParams
        subContent:
          - name: id
            isOptional: false
            type: string
            description: ID of the MFA factor to delete.
          - name: user_id
            isOptional: false
            type: string
            description: ID of the user whose factor is being deleted.
    notes: |
      Deletes a factor on a user. This will log the user out of all active sessions if the deleted factor was verified.
    examples:
      - id: delete-factor
        name: Delete a factor for a user
        isSpotlight: true
        code: |
          ```python
          response = supabase.auth.admin.mfa.delete_factor(
              {
                  "id": "34e770dd-9ff9-416c-87fa-43b31d7ef225",
                  "user_id": "a89baba7-b1b7-440f-b4bb-91026967f66b"
              }
          )
          ```
        response: |
          ```json
          {
            "id": "34e770dd-9ff9-416c-87fa-43b31d7ef225"
          }
          ```

  - id: reset-password-for-email
    title: 'reset_password_for_email()'
    params:
      - name: email
        isOptional: false
        type: string
        description: The email address of the user.
      - name: options
        isOptional: true
        type: object
        subContent:
          - name: redirect_to
            isOptional: true
            type: string
            description: >
              The URL to send the user to after they click the password reset link.
              Must be in your configured redirect URLs.
          - name: captcha_token
            isOptional: true
            type: string
            description: Verification token received when the user completes the captcha on the site.
    notes: |
      - The password reset flow consist of 2 broad steps: (i) Allow the user to login via the password reset link; (ii) Update the user's password.
      - The `reset_password_for_email()` only sends a password reset link to the user's email.
      To update the user's password, see [`update_user()`](/docs/reference/python/auth-updateuser).
      - When the user clicks the reset link in the email they are redirected back to your application.
      You can configure the URL that the user is redirected to with the `redirectTo` parameter.
      See [redirect URLs and wildcards](docs/guides/auth/redirect-urls#use-wildcards-in-redirect-urls) to add additional redirect URLs to your project.
      - After the user has been redirected successfully, prompt them for a new password and call `update_user()`:
      ```python
      response = supabase.auth.update_user(
          {"password": new_password}
      )
      ```
    examples:
      - id: reset-password
        name: Reset password
        isSpotlight: true
        code: |
          ```python
          supabase.auth.reset_password_for_email(
              email,
              {
                  "redirect_to": "https://example.com/update-password",
              }
          )
          ```

  - id: select
    title: 'Fetch data: select()'
    notes: |
      - By default, Supabase projects return a maximum of 1,000 rows. This setting can be changed in your project's [API settings](/dashboard/project/_/settings/api). It's recommended that you keep it low to limit the payload size of accidental or malicious requests. You can use `range()` queries to paginate through your data.
      - `select()` can be combined with [Filters](/docs/reference/python/using-filters)
      - `select()` can be combined with [Modifiers](/docs/reference/python/using-modifiers)
      - `apikey` is a reserved keyword if you're using the [Supabase Platform](/docs/guides/platform) and [should be avoided as a column name](https://github.com/supabase/supabase/issues/5465).
    params:
      - name: columns
        isOptional: true
        type: string
        description: The columns to retrieve, defaults to `*`.
      - name: count
        isOptional: true
        type: CountMethod
        description: The property to use to get the count of rows returned.
    examples:
      - id: getting-your-data
        name: Getting your data
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              },
              {
                "id": 2,
                "name": "Earth"
              },
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
      - id: selecting-specific-columns
        name: Selecting specific columns
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("name")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Mercury"
              },
              {
                "name": "Earth"
              },
              {
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
      - id: query-referenced-tables
        name: Query referenced tables
        description: |
          If your database has foreign key relationships, you can query related tables too.
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments(name)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  }
                ]
              },
              {
                "name": "woodwinds",
                "instruments": [
                  {
                    "name": "flute"
                  }
                ]
              }
            ],
            "count": null
          }
          ```
      - id: query-referenced-tables-through-a-join-table
        name: Query referenced tables through a join table
        code: |
          ```python
          response = (
              supabase.table("users")
              .select("name, teams(name)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text
              );
            create table
              teams (
                id int8 primary key,
                name text
              );
            -- join table
            create table
              users_teams (
                user_id int8 not null references users,
                team_id int8 not null references teams,
                -- both foreign keys must be part of a composite primary key
                primary key (user_id, team_id)
              );

            insert into
              users (id, name)
            values
              (1, 'Kiran'),
              (2, 'Evan');
            insert into
              teams (id, name)
            values
              (1, 'Green'),
              (2, 'Blue');
            insert into
              users_teams (user_id, team_id)
            values
              (1, 1),
              (1, 2),
              (2, 2);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Kiran",
                "teams": [
                  {
                    "name": "Green"
                  },
                  {
                    "name": "Blue"
                  }
                ]
              },
              {
                "name": "Evan",
                "teams": [
                  {
                    "name": "Blue"
                  }
                ]
              }
            ],
            "count": null
          }
          ```
        description: |
          If you're in a situation where your tables are **NOT** directly
          related, but instead are joined by a _join table_, you can still use
          the `select()` method to query the related data. The join table needs
          to have the foreign keys as part of its composite primary key.
        hideCodeBlock: true
      - id: query-the-same-referenced-table-multiple-times
        name: Query the same referenced table multiple times
        code: |
          ```python
          response = (
              supabase.table("messages")
              .select("content,from:sender_id(name),to:receiver_id(name)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
             create table
             users (id int8 primary key, name text);

             create table
               messages (
                 sender_id int8 not null references users,
                 receiver_id int8 not null references users,
                 content text
               );

             insert into
               users (id, name)
             values
               (1, 'Kiran'),
               (2, 'Evan');

             insert into
               messages (sender_id, receiver_id, content)
             values
               (1, 2, '👋');
             ```
        response: |
          ```json
          {
            "data": [
              {
                "content": "👋",
                "from": {
                  "name": "Kiran"
                },
                "to": {
                  "name": "Evan"
                }
              }
            ],
            "count": null
          }
          ```
        description: |
          If you need to query the same referenced table twice, use the name of the
          joined column to identify which join to use. You can also give each
          column an alias.
        hideCodeBlock: true
      - id: filtering-through-referenced-tables
        name: Filtering through referenced tables
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments(*)")
              .eq("instruments.name", "guqin")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": null
              },
              {
                "name": "woodwinds",
                "instruments": null
              }
            ],
            "count": null
          }
          ```
        description: |
          If the filter on a referenced table's column is not satisfied, the referenced
          table returns `[]` or `null` but the parent table is not filtered out.
          If you want to filter out the parent table rows, use the `!inner` hint
        hideCodeBlock: true
      - id: querying-referenced-table-with-count
        name: Querying referenced table with count
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("*, instruments(count)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table orchestral_sections (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text
            );

            create table instruments (
              "id" "uuid" primary key default "extensions"."uuid_generate_v4"() not null,
              "name" text,
              "section_id" "uuid" references public.orchestral_sections on delete cascade
            );

            with section as (
              insert into orchestral_sections (name)
              values ('strings') returning id
            )
            insert into instruments (name, section_id) values
            ('violin', (select id from section)),
            ('viola', (select id from section)),
            ('cello', (select id from section)),
            ('double bass', (select id from section));
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": "c31e7151-5a6f-453c-915b-caf31ec9a0a0",
                "name": "strings",
                "instruments": [
                  {
                    "count": 4
                  }
                ]
              }
            ],
            "count": null
          }
          ```
        description: |
          You can get the number of rows in a related table by using the
          **count** property.
        hideCodeBlock: true
      - id: querying-with-count-option
        name: Querying with count option
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*", count="exact")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              },
              {
                "id": 2,
                "name": "Earth"
              },
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": 3
          }
          ```
        description: |
          You can get the number of rows by using the
          *count* parameter in the select query.
        hideCodeBlock: true
      - id: querying-json-data
        name: Querying JSON data
        code: |
          ```python
          response = (
              supabase.table("users")
              .select("id, name, address->city")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Avdotya', '{"city":"Saint Petersburg"}');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Avdotya",
                "city": "Saint Petersburg"
              }
            ],
            "count": null
          }
          ```
        description: |
          You can select and filter data inside of
          [JSON](/docs/guides/database/json) columns. Postgres offers some
          [operators](/docs/guides/database/json#query-the-jsonb-data) for
          querying JSON data.
        hideCodeBlock: true
      - id: querying-referenced-table-with-inner-join
        name: Querying referenced table with inner join
        code: |
          ```python
          response = (
              supabase.table("instruments")
              .select("name, orchestral_sections!inner(name)")
              .eq("orchestral_sections.name", "woodwinds")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "flute",
                "orchestral_sections": {
                  "name": "woodwinds"
                }
              }
            ],
            "count": null
          }
          ```
        description: |
          If you don't want to return the referenced table contents, you can leave the parenthesis empty.
          Like `.select('name, orchestral_sections!inner()')`.
        hideCodeBlock: true
      - id: switching-schemas-per-query
        name: Switching schemas per query
        code: |
          ```python
          response = (
              supabase.schema("myschema")
              .table("mytable")
              .select("*")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create schema myschema;

            create table myschema.mytable (
              id uuid primary key default gen_random_uuid(),
              data text
            );

            insert into myschema.mytable (data) values ('mydata');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": "b1a8c0b5-bdf0-46f1-95a5-f0fb9cb2f410",
                "data": "mydata"
              }
            ],
            "count": null
          }
          ```
        description: |
          In addition to setting the schema during initialization, you can also switch schemas on a per-query basis.
          Make sure you've set up your [database privileges and API settings](/docs/guides/api/using-custom-schemas).
        hideCodeBlock: true

  - id: insert
    title: 'Create data: insert()'
    params:
      - name: json
        isOptional: false
        type: dict, list
        description: The values to insert. Pass an dict to insert a single row or an list to insert multiple rows.
      - name: count
        isOptional: true
        type: CountMethod
        description: The property to use to get the count of rows returned.
      - name: returning
        isOptional: true
        type: ReturnMethod
        description: Either 'minimal' or 'representation'. Defaults to 'representation'.
      - name: default_to_null
        isOptional: true
        type: bool
        description: Make missing fields default to `null`. Otherwise, use the default value for the column. Only applies for bulk inserts.
    examples:
      - id: create-a-record
        name: Create a record
        code: |
          ```python
          response = (
              supabase.table("planets")
              .insert({"id": 1, "name": "Pluto"})
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Pluto"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: bulk-create
        name: Bulk create
        code: |
          ```python
          try:
              response = (
                  supabase.table("characters")
                  .insert([
                      {"id": 1, "name": "Frodo"},
                      {"id": 2, "name": "Sam"},
                  ])
                  .execute()
              )
              return response
          except Exception as exception:
              return exception
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);
            ```
        response: |
          ```json
          {
            "_raw_error": {
              "code": "23505",
              "details": "Key (id)=(1) already exists.",
              "hint": null,
              "message": "duplicate key value violates unique constraint \"characters_pkey\""
            },
            "message": "duplicate key value violates unique constraint \"characters_pkey\"",
            "code": "23505",
            "hint": null,
            "details": "Key (id)=(1) already exists."
          }
          ```
        description: |
          A bulk create operation is handled in a single transaction.
          If any of the inserts fail, none of the rows are inserted.
        hideCodeBlock: true
  - id: update
    title: 'Modify data: update()'
    notes: |
      - `update()` should always be combined with [Filters](/docs/reference/python/using-filters) to target the item(s) you wish to update.
    params:
      - name: json
        isOptional: false
        type: dict, list
        description: The values to insert. Pass an dict to insert a single row or an list to insert multiple rows.
      - name: count
        isOptional: true
        type: CountMethod
        description: The property to use to get the count of rows returned.
    examples:
      - id: updating-your-data
        name: Updating your data
        code: |
          ```python
          response = (
              supabase.table("instruments")
              .update({"name": "piano"})
              .eq("id", 1)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "piano"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: updating-json-data
        name: Updating JSON data
        code: |
          ```python
          response = (
              supabase.table("users")
              .update({"address": {"street": "Melrose Place", "postcode": 90210}})
              .eq("address->postcode", 90210)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210 }');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Michael",
                "address": {
                  "street": "Melrose Place",
                  "postcode": 90210
                }
              }
            ],
            "count": null
          }
          ```
        description: |
          Postgres offers some [operators](/docs/guides/database/json#query-the-jsonb-data) for working with JSON data. Currently, it is only possible to update the entire JSON document.
        hideCodeBlock: true
  - id: upsert
    title: 'Upsert data: upsert()'
    notes: |
      - Primary keys must be included in the `values` dict to use upsert.
    params:
      - name: json
        isOptional: false
        type: dict, list
        description: The values to insert. Pass an dict to insert a single row or an list to insert multiple rows.
      - name: count
        isOptional: true
        type: CountMethod
        description: The property to use to get the count of rows returned.
      - name: returning
        isOptional: true
        type: ReturnMethod
        description: Either 'minimal' or 'representation'. Defaults to 'representation'.
      - name: ignore_duplicates
        isOptional: true
        type: bool
        description: Whether duplicate rows should be ignored.
      - name: on_conflict
        isOptional: true
        type: string
        description: Specified columns to be made to work with UNIQUE constraint.
      - name: default_to_null
        isOptional: true
        type: bool
        description: Make missing fields default to `null`. Otherwise, use the default value for the column. Only applies for bulk inserts.
    examples:
      - id: upsert-your-data
        name: Upsert your data
        code: |
          ```python
          response = (
              supabase.table("instruments")
              .upsert({"id": 1, "name": "piano"})
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "piano"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: bulk-upsert-your-data
        name: Bulk Upsert your data
        code: |
          ```python
          response = (
              supabase.table("instruments")
              .upsert([{"id": 1, "name": "piano"}, {"id": 2, "name": "guitar"}])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              instruments (id int8 primary key, name text);

            insert into
              instruments (id, name)
            values
              (1, 'harpsichord');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "piano"
              },
              {
                "id": 2,
                "name": "guitar"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
      - id: upserting-into-tables-with-constraints
        name: Upserting into tables with constraints
        code: |
          ```python
          response = (
              supabase.table("users")
              .upsert(
                  {"id": 42, "handle": "saoirse", "display_name": "Saoirse"},
                  on_conflict="handle",
              )
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 generated by default as identity primary key,
                handle text not null unique,
                display_name text
              );

            insert into
              users (id, handle, display_name)
            values
              (1, 'saoirse', null);
            ```
        response: |
          ```json
          {
            "_raw_error": {
              "code": "23505",
              "details": "Key (handle)=(saoirse) already exists.",
              "hint": null,
              "message": "duplicate key value violates unique constraint \"users_handle_key\""
            },
            "message": "duplicate key value violates unique constraint \"users_handle_key\"",
            "code": "23505",
            "hint": null,
            "details": "Key (handle)=(saoirse) already exists."
          }
          ```
        description: |
          In the following query, `upsert()` implicitly uses the `id`(primary key) column to determine conflicts. If there is no existing row with the same `id`, `upsert()` inserts a new row, which will fail in this case as there is already a row with `handle` `"saoirse"`.
          Using the `on_conflict` option, you can instruct `upsert()` to use another column with a unique constraint to determine conflicts.
        hideCodeBlock: true

  - id: delete
    title: 'Delete data: delete()'
    notes: |
      - `delete()` should always be combined with [filters](/docs/reference/python/using-filters) to target the item(s) you wish to delete.
      - If you use `delete()` with filters and you have
        [RLS](/docs/learn/auth-deep-dive/auth-row-level-security) enabled, only
        rows visible through `SELECT` policies are deleted. Note that by default
        no rows are visible, so you need at least one `SELECT`/`ALL` policy that
        makes the rows visible.
      - When using `delete().in_()`, specify an array of values to target multiple rows with a single query. This is particularly useful for batch deleting entries that share common criteria, such as deleting users by their IDs. Ensure that the array you provide accurately represents all records you intend to delete to avoid unintended data removal.
    params:
      - name: count
        isOptional: true
        type: CountMethod
        description: The property to use to get the count of rows returned.
      - name: returning
        isOptional: true
        type: ReturnMethod
        description: Either 'minimal' or 'representation'. Defaults to 'representation'.
    examples:
      - id: delete-records
        name: Delete records
        code: |
          ```python
          response = (
              supabase.table("countries")
              .delete()
              .eq("id", 1)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              countries (id int8 primary key, name text);

            insert into
              countries (id, name)
            values
              (1, 'Mordor');
            ```
        response: |
          ```
          {
            "data": [
              {
                "id": 1,
                "name": "Mordor"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: delete-multiple-records
        name: Delete multiple records
        code: |
          ```python
          response = (
              supabase.table("countries")
              .delete()
              .in_("id", [1, 2, 3])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              characters (id int8 primary key, name text);

            insert into
              characters (id, name)
            values
              (1, 'Harry'), (2, 'Hermione'), (3, 'Ron');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Harry"
              },
              {
                "id": 2,
                "name": "Hermione"
              },
              {
                "id": 3,
                "name": "Ron"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: false
        isSpotlight: false

  - id: rpc
    title: 'Postgres functions: rpc()'
    description: |
      You can call Postgres functions as _Remote Procedure Calls_, logic in your database that you can execute from anywhere.
      Functions are useful when the logic rarely changes—like for password resets and updates.

      ```sql
      create or replace function hello_world() returns text as $$
        select 'Hello world';
      $$ language sql;
      ```
    params:
      - name: fn
        isOptional: false
        type: callable
        description: The stored procedure call to be executed.
      - name: params
        isOptional: true
        type: dict of any
        description: Parameters passed into the stored procedure call.
      - name: get
        isOptional: true
        type: dict of any
        description: When set to `true`, `data` will not be returned. Useful if you only need the count.
      - name: head
        isOptional: true
        type: dict of any
        description: When set to `true`, the function will be called with read-only access mode.
      - name: count
        isOptional: true
        type: CountMethod
        description: |
          Count algorithm to use to count rows returned by the function. Only applicable for [set-returning functions](https://www.postgresql.org/docs/current/functions-srf.html). `"exact"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the hood. `"planned"`: Approximated but fast count algorithm. Uses the Postgres statistics under the hood. `"estimated"`: Uses exact count for low numbers and planned count for high numbers.
    examples:
      - id: call-a-postgres-function-without-arguments
        name: Call a Postgres function without arguments
        code: |
          ```python
          response = (
              supabase.rpc("hello_world")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create function hello_world() returns text as $$
              select 'Hello world';
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": "Hello world",
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: call-a-postgres-function-with-arguments
        name: Call a Postgres function with arguments
        code: |
          ```python
          response = (
              supabase.rpc("echo", { "say": "👋" })
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create function echo(say text) returns text as $$
              select say;
            $$ language sql;
            ```
        response: |
          ```json
            {
              "data": "👋",
              "count": null
            }
            ```
        hideCodeBlock: true
      - id: bulk-processing
        name: Bulk processing
        code: |
          ```python
          response = (
              supabase.rpc("add_one_each", {"arr": [1, 2, 3]})
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create function add_one_each(arr int[]) returns int[] as $$
              select array_agg(n + 1) from unnest(arr) as n;
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": [
              2,
              3,
              4
            ],
            "count": null
          }
          ```
        description: |
          You can process large payloads by passing in an array as an argument.
        hideCodeBlock: true
      - id: call-a-postgres-function-with-filters
        name: Call a Postgres function with filters
        code: |
          ```python
          response = (
              supabase.rpc("list_stored_planets")
              .eq("id", 1)
              .single()
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Earth'),
              (2, 'Mars');

            create function list_stored_planets() returns setof planets as $$
              select * from planets;
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": {
              "id": 1,
              "name": "Earth"
            },
            "count": null
          }
          ```
        description: |
          Postgres functions that return tables can also be combined with [Filters](/docs/reference/javascript/using-filters) and [Modifiers](/docs/reference/javascript/using-modifiers).
        hideCodeBlock: true
      - id: call-a-read-only-postgres-function
        name: Call a read-only Postgres function
        code: |
          ```python
          response = (
              supabase.rpc("hello_world", get=True)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create function hello_world() returns text as $$
              select 'Hello world';
            $$ language sql;
            ```
        response: |
          ```json
          {
            "data": "Hello world",
            "count": null
          }
          ```
        hideCodeBlock: true

  - id: using-filters
    title: Using Filters
    description: |
      Filters allow you to only return rows that match certain conditions.

      Filters can be used on `select()`, `update()`, `upsert()`, and `delete()` queries.

      If a Postgres function returns a table response, you can also apply filters.
    examples:
      - id: applying-filters
        name: Applying Filters
        description: |
          Filters must be applied after any of `select()`, `update()`, `upsert()`,
          `delete()`, and `rpc()` and before
          [modifiers](/docs/reference/python/using-modifiers).
        code: |
          ```python
          # Correct
          response = (
              supabase.table("instruments")
              .select("name, section_id")
              .eq("name", "flute")
              .execute()
          )

          # Incorrect
          response = (
              supabase.table("instruments")
              .eq("name", "flute")
              .select("name, section_id")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
      - id: chaining-filters
        name: Chaining
        description: |
          Filters can be chained together to produce advanced queries. For example,
          to query instruments with octave ranges between 3 and 7 (non-inclusive).
        code: |
          ```python
          response = (
              supabase.table("instruments")
              .select("name, section_id")
              .gte("octave_range", 3)
              .lt("octave_range", 7)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                secttion_id int8 not null references orchestral_sections,
                name text,
                octave_range int8
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'percussion'),
              (2, 'strings');
            insert into
              instruments (id, section_id, name, octave_range)
            values
              (1, 2, 'violin', 4),
              (2, 1, 'piano', 7);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "violin",
                "section_id": 2
              }
            ],
            "count": null
          }
          ```
      - id: conditional-chaining
        name: Conditional chaining
        description: |
          Filters can be built up one step at a time and then executed.
        code: |
          ```python
          filterByName = None
          filterOctaveLow = 3
          filterOctaveHigh = 7

          query = supabase.table("instruments").select("name, section_id")

          if filterByName:
              query = query.eq("name", filterByName)

          if filterAgeLow:
              query = query.gte("octave_range", filterOctaveLow)

          if filterAgeHigh:
              query = query.lt("octave_range", filterOctaveHigh)

          response = query.execute()
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text,
                octave_range int8
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'percussion'),
              (2, 'strings');
            insert into
              instruments (id, section_id, name, octave_range)
            values
              (1, 2, 'violin', 4),
              (2, 1, 'piano', 7);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "violin",
                "section_id": 2
              }
            ],
            "count": null
          }
          ```
      - id: filter-by-value-within-json-column
        name: Filter by values within JSON column
        code: |
          ```python
          response = (
              supabase.table("users")
              .select("*")
              .eq("address->postcode", 90210)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210 }'),
              (2, 'Jane', null);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Michael",
                "address": {
                  "postcode": 90210
                }
              }
            ],
            "count": null
          }
          ```
      - id: filter-foreign-tables
        name: Filter Foreign Tables
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments!inner(name)")
              .eq("instruments.name", "flute")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "woodwinds",
                "instruments": [
                  {
                    "name": "flute"
                  }
                ]
              }
            ],
            "count": null
          }
          ```
        description: |
          You can filter on foreign tables in your `select()` query using dot
          notation.

  - id: eq
    title: eq()
    description: |
      Match only rows where `column` is equal to `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: value
        isOptional: false
        type: any
        description: The value to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .eq("name", "Earth")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: neq
    title: neq()
    description: |
      Match only rows where `column` is not equal to `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: value
        isOptional: false
        type: any
        description: The value to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .neq("name", "Earth")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              },
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: gt
    title: gt()
    description: |
      Match only rows where `column` is greather than `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: value
        isOptional: false
        type: any
        description: The value to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .gt("id", 2)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
        description: |
          When using [reserved words](https://www.postgresql.org/docs/current/sql-keywords-appendix.html) for column names you need
          to add double quotes e.g. `.gt('"order"', 2)`
  - id: gte
    title: gte()
    description: |
      Match only rows where `column` is greater than or equal to `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: value
        isOptional: false
        type: any
        description: The value to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .gte("id", 2)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Earth"
              },
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: lt
    title: lt()
    description: |
      Match only rows where `column` is less than `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: value
        isOptional: false
        type: any
        description: The value to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .lt("id", 2)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
            planets (id int8 primary key, name text);

            insert into
            planets (id, name)
            values
            (1, 'Mercury'),
            (2, 'Earth'),
            (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: lte
    title: lte()
    description: |
      Match only rows where `column` is less than or equal to `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: value
        isOptional: false
        type: any
        description: The value to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .lte("id", 2)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
            planets (id int8 primary key, name text);

            insert into
            planets (id, name)
            values
            (1, 'Mercury'),
            (2, 'Earth'),
            (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              },
              {
                "id": 2,
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: like
    title: like()
    description: |
      Match only rows where `column` matches `pattern` case-sensitively.
    params:
      - name: column
        isOptional: false
        type: string
        description: The name of the column to apply a filter on
      - name: pattern
        isOptional: false
        type: string
        description: The pattern to match by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .like("name", "%Ea%")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
            planets (id int8 primary key, name text);

            insert into
            planets (id, name)
            values
            (1, 'Mercury'),
            (2, 'Earth'),
            (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: ilike
    title: ilike()
    description: |
      Match only rows where `column` matches `pattern` case-insensitively.
    params:
      - name: column
        isOptional: false
        type: string
        description: The name of the column to apply a filter on
      - name: pattern
        isOptional: false
        type: string
        description: The pattern to match by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .ilike("name", "%ea%")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
            planets (id int8 primary key, name text);

            insert into
            planets (id, name)
            values
            (1, 'Mercury'),
            (2, 'Earth'),
            (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: is
    title: is_()
    description: |
      Match only rows where `column` IS `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The name of the column to apply a filter on
      - name: value
        isOptional: false
        type: null | boolean
        description: The value to match by
    examples:
      - id: checking-nullness
        name: Checking for nullness, True or False
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .is_("name", "null")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Earth'),
              (2, null);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": null
              }
            ],
            "count": null
          }
          ```
        description: |
          Using the `eq()` filter doesn't work when filtering for `null`. Instead, you need to use `is_()`.

          To query for null values in python use the string 'null' instead of the python `None` value.

          Note that `is` is a reserved word in Python, so the underscore is added to avoid a syntax error.
        hideCodeBlock: true
        isSpotlight: true
  - id: in
    title: in_()
    description: |
      Match only rows where `column` is included in the `values` array.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: values
        isOptional: false
        type: array
        description: The values to filter by
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .in_("name", ["Earth", "Mars"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
            planets (id int8 primary key, name text);

            insert into
            planets (id, name)
            values
            (1, 'Mercury'),
            (2, 'Earth'),
            (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Earth"
              },
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: contains
    title: contains()
    description: |
      Only relevant for jsonb, array, and range columns. Match only rows where `column` contains every element appearing in `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: values
        isOptional: false
        type: object
        description: The jsonb, array, or range value to filter with
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```python
          response = (
              supabase.table("issues")
              .select("*")
              .contains("tags", ["is:open", "priority:low"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                tags text[]
              );
            insert into
              issues (id, title, tags)
            values
              (1, 'Cache invalidation is not working', array['is:open', 'severity:high', 'priority:low']),
              (2, 'Use better names', array['is:open', 'severity:low', 'priority:medium']);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "title": "Cache invalidation is not working",
                "tags": [
                  "is:open",
                  "severity:high",
                  "priority:low"
                ]
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .contains("during", "[2000-01-01 13:00, 2000-01-01 13:30)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "count": null
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
      - id: on-jsonb-columns
        name: On `jsonb` columns
        code: |
          ```python
          response = (
              supabase.table("users")
              .select("*")
              .contains("address", {"postcode": 90210})
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210, "street": "Melrose Place" }'),
              (2, 'Jane', '{}');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Michael",
                "address": {
                  "street": "Melrose Place",
                  "postcode": 90210
                }
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
  - id: contained-by
    title: contained_by()
    description: |
      Only relevant for jsonb, array, and range columns. Match only rows where every element appearing in `column` is contained by `value`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The jsonb, array, or range column to filter on
      - name: value
        isOptional: false
        type: object
        description: The jsonb, array, or range value to filter with
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```python
          response = (
              supabase.table("classes")
              .select("name")
              .contained_by("days", ["monday", "tuesday", "wednesday", "friday"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              classes (
                  id int8 primary key,
                  name text,
                  days text[]
              );

            insert into
              classes (id, name, days)
            values
              (1, 'Chemistry', array['monday', 'friday']),
              (2, 'History', array['monday', 'wednesday', 'thursday']);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Chemistry"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .contained_by("during", "[2000-01-01 00:00, 2000-01-01 23:59)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "count": null
          }
          ```
        description: |
          Postgres supports a number of [range
          types](https://www.postgresql.org/docs/current/rangetypes.html). You
          can filter on range columns using the string representation of range
          values.
        hideCodeBlock: true
      - id: on-jsonb-columns
        name: On `jsonb` columns
        code: |
          ```python
          response = (
              supabase.table("users")
              .select("name")
              .contained_by("address", {})
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              users (
                id int8 primary key,
                name text,
                address jsonb
              );

            insert into
              users (id, name, address)
            values
              (1, 'Michael', '{ "postcode": 90210, "street": "Melrose Place" }'),
              (2, 'Jane', '{}');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Jane"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
  - id: range-gt
    title: range_gt()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is greater than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The range column to filter on
      - name: range
        isOptional: false
        type: array
        description: The range to filter with
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .range_gt("during", ["2000-01-02 08:00", "2000-01-02 09:00"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "id": 2,
                  "room_name": "Topaz",
                  "during": "[\"2000-01-02 09:00:00\",\"2000-01-02 10:00:00\")"
                }
              ],
              "count": null
            }
            ```
        description: |
          Postgres supports a number of [range types](https://www.postgresql.org/docs/current/rangetypes.html). You can filter on range columns using the string representation of range values.
        hideCodeBlock: true
        isSpotlight: true
  - id: range-gte
    title: range_gte()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is either contained in `range` or greater than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The range column to filter on
      - name: range
        isOptional: false
        type: string
        description: The range to filter with
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .range_gte("during", ["2000-01-02 08:30", "2000-01-02 09:30"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "room_name": "Topaz",
                "during": "[\"2000-01-02 09:00:00\",\"2000-01-02 10:00:00\")"
              }
            ],
            "count": null
          }
          ```
        description: |
          Postgres supports a number of [range types](https://www.postgresql.org/docs/current/rangetypes.html). You can filter on range columns using the string representation of range values.
        hideCodeBlock: true
        isSpotlight: true
  - id: range-lt
    title: range_lt()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is less than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The range column to filter on
      - name: range
        isOptional: false
        type: array
        description: The range to filter with
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .range_lt("during", ["2000-01-01 15:00", "2000-01-01 16:00"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "count": null
          }
          ```
        description: |
          Postgres supports a number of [range types](https://www.postgresql.org/docs/current/rangetypes.html). You can filter on range columns using the string representation of range values.
        hideCodeBlock: true
        isSpotlight: true
  - id: range-lte
    title: range_lte()
    description: |
      Only relevant for range columns. Match only rows where every element in `column` is less than any element in `range`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The range column to filter on
      - name: range
        isOptional: false
        type: array
        description: The range to filter with
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .range_lte("during", ["2000-01-01 14:00", "2000-01-01 16:00"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
            {
              "data": [
                {
                  "id": 1,
                  "room_name": "Emerald",
                  "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
                }
              ],
              "count": null
            }
          ```
        description: |
          Postgres supports a number of [range types](https://www.postgresql.org/docs/current/rangetypes.html). You can filter on range columns using the string representation of range values.
        hideCodeBlock: true
        isSpotlight: true

  - id: range-adjacent
    title: range_adjacent()
    description: |
      Only relevant for range columns. Match only rows where `column` is mutually exclusive to `range` and there can be no element between the two ranges.
    params:
      - name: column
        isOptional: false
        type: string
        description: The range column to filter on
      - name: range
        isOptional: false
        type: array
        description: The range to filter with
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .range_adjacent("during", ["2000-01-01 12:00", "2000-01-01 13:00"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "count": null
          }
          ```
        description: |
          Postgres supports a number of [range types](https://www.postgresql.org/docs/current/rangetypes.html). You can filter on range columns using the string representation of range values.
        hideCodeBlock: true
        isSpotlight: true

  - id: overlaps
    title: overlaps()
    description: |
      Only relevant for array and range columns. Match only rows where `column` and `value` have an element in common.
    params:
      - name: column
        isOptional: false
        type: string
        description: The array or range column to filter on
      - name: value
        isOptional: false
        type: Iterable[Any]
        description: The array or range value to filter with
    examples:
      - id: on-array-columns
        name: On array columns
        code: |
          ```python
          response = (
              supabase.table("issues")
              .select("title")
              .overlaps("tags", ["is:closed", "severity:high"])
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              issues (
                id int8 primary key,
                title text,
                tags text[]
              );

            insert into
              issues (id, title, tags)
            values
              (1, 'Cache invalidation is not working', array['is:open', 'severity:high', 'priority:low']),
              (2, 'Use better names', array['is:open', 'severity:low', 'priority:medium']);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "title": "Cache invalidation is not working"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-range-columns
        name: On range columns
        code: |
          ```python
          response = (
              supabase.table("reservations")
              .select("*")
              .overlaps("during", "[2000-01-01 12:45, 2000-01-01 13:15)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              reservations (
                id int8 primary key,
                room_name text,
                during tsrange
              );

            insert into
              reservations (id, room_name, during)
            values
              (1, 'Emerald', '[2000-01-01 13:00, 2000-01-01 15:00)'),
              (2, 'Topaz', '[2000-01-02 09:00, 2000-01-02 10:00)');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "room_name": "Emerald",
                "during": "[\"2000-01-01 13:00:00\",\"2000-01-01 15:00:00\")"
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Postgres supports a number of [range types](https://www.postgresql.org/docs/current/rangetypes.html). You can filter on range columns using the string representation of range values.
        hideCodeBlock: true

  - id: text-search
    title: text_search()
    description: |
      Only relevant for text and tsvector columns. Match only rows where `column` matches the query string in `query`.

    params:
      - name: column
        isOptional: false
        type: string
        description: The text or tsvector column to filter on
      - name: query
        isOptional: false
        type: string
        description: The query text to match with
      - name: options
        isOptional: true
        type: object
        description: Named parameters
        subContent:
          - name: type
            isOptional: true
            type: '"plain" | "phrase" | "websearch"'
            description: Change how the `query` text is interpreted
          - name: config
            isOptional: true
            type: string
            description: The text search configuration to use
    notes: |
      - For more information, see [Postgres full text search](/docs/guides/database/full-text-search).
    examples:
      - id: text-search
        name: Text search
        code: |
          ```python
          response = (
              supabase.table("texts")
              .select("content")
              .text_search(
                  "content", 
                  "'eggs' & 'ham'", 
                  options={"config": "english"},
              )
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table texts (
              id      bigint
                      primary key
                      generated always as identity,
              content text
            );

            insert into texts (content) values
                ('Four score and seven years ago'),
                ('The road goes ever on and on'),
                ('Green eggs and ham')
            ;
            ```
        response: |
          ```json
          {
            "data": [
              {
                "content": "Green eggs and ham"
              }
            ],
            "count": null
          }
          ```
      - id: basic-normalization
        name: Basic normalization
        description: Uses PostgreSQL's `plainto_tsquery` function.
        code: |
          ```python
          response = (
              supabase.table("quotes")
              .select("catchphrase")
              .text_search(
                  "catchphrase",
                  "'fat' & 'cat'",
                  options={"type": "plain", "config": "english"},
              )
              .execute()
          )
          ```
      - id: full-normalization
        name: Full normalization
        description: Uses PostgreSQL's `phraseto_tsquery` function.
        code: |
          ```python
          response = (
              supabase.table("quotes")
              .select("catchphrase")
              .text_search(
                  "catchphrase",
                  "'fat' & 'cat'",
                  options={"type": "phrase", "config": "english"},
              )
              .execute()
          )
          ```
      - id: web-search
        name: Websearch
        description: |
          Uses PostgreSQL's `websearch_to_tsquery` function.
          This function will never raise syntax errors, which makes it possible to use raw user-supplied input for search, and can be used
          with advanced operators.

          - `unquoted text`: text not inside quote marks will be converted to terms separated by & operators, as if processed by plainto_tsquery.
          - `"quoted text"`: text inside quote marks will be converted to terms separated by `<->` operators, as if processed by phraseto_tsquery.
          - `OR`: the word “or” will be converted to the | operator.
          - `-`: a dash will be converted to the ! operator.

        code: |
          ```python
          response = (
              supabase.table("quotes")
              .select("catchphrase")
              .text_search(
                  "catchphrase",
                  "'fat or cat'",
                  options={"type": "websearch", "config": "english"},
              )
              .execute()
          )
          ```

  - id: match
    title: match()
    description: |
      Match only rows where each column in `query` keys is equal to its associated value. Shorthand for multiple `.eq()`s.
    params:
      - name: query
        isOptional: false
        type: dict
        description: The object to filter with, with column names as keys mapped to their filter values
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .match({"id": 2, "name": "Earth"})
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 2,
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: not
    title: not_()
    description: |
      Match only rows which doesn't satisfy the filter. `not_` expects you to use the raw PostgREST syntax for the filter values.
    notes: |
      ```python
      .not_.in_('id', '(5,6,7)')  # Use `()` for `in` filter
      .not_.contains('arraycol', '{"a","b"}')  # Use `{}` for array values
      ```
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .not_.is_("name", "null")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, null);
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: or
    title: or_()
    params:
      - name: filters
        isOptional: false
        type: string
        description: The filters to use, following PostgREST syntax
      - name: reference_table
        isOptional: true
        type: string
        description: Set this to filter on referenced tables instead of the parent table
    notes: |
      or_() expects you to use the raw PostgREST syntax for the filter names and values.

      ```python
      .or_('id.in.(5,6,7), arraycol.cs.{"a","b"}')  # Use `()` for `in` filter, `{}` for array values and `cs` for `contains()`.
      .or_('id.in.(5,6,7), arraycol.cd.{"a","b"}')  # Use `cd` for `containedBy()`
      ```
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("name")
              .or_("id.eq.2,name.eq.Mars")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Earth"
              },
              {
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: use-or-with-and
        name: Use `or` with `and`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("name")
              .or_("id.gt.3,and(id.eq.1,name.eq.Mercury)")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        reponse: |
          ```json
          {
            "data": [
              {
                "name": "Mercury"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
      - id: use-or-on-referenced-tables
        name: Use `or` on referenced tables
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments!inner(name)")
              .or_("book_id.eq.1,name.eq.guqin", reference_table="instruments")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  }
                ]
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true

  - id: filter
    title: filter()
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to filter on
      - name: operator
        isOptional: true
        type: string
        description: The operator to filter with, following PostgREST syntax
      - name: value
        isOptional: true
        type: any
        description: The value to filter with, following PostgREST syntax
    notes: |
      filter() expects you to use the raw PostgREST syntax for the filter values.

      ```python
      .filter('id', 'in', '(5,6,7)')  # Use `()` for `in` filter
      .filter('arraycol', 'cs', '{"a","b"}')  # Use `cs` for `contains()`, `{}` for array values
      ```
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .filter("name", "in", '("Mars","Tatooine")')
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 3,
                "name": "Mars"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-foreign-table
        name: On a foreign table
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments!inner(name)")
              .filter("instruments.name", "eq", "flute")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 2, 'flute'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "woodwinds",
                "instruments": [
                  {
                    "name": "flute"
                  }
                ]
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
  - id: using-modifiers
    title: Using Modifiers
    description: |
      Filters work on the row level—they allow you to return rows that
      only match certain conditions without changing the shape of the rows.
      Modifiers are everything that don't fit that definition—allowing you to
      change the format of the response (e.g., returning a CSV string).

      Modifiers must be specified after filters. Some modifiers only apply for
      queries that return rows (e.g., `select()` or `rpc()` on a function that
      returns a table response).

  - id: order
    title: order()
    description: Order the query result by `column`.
    params:
      - name: column
        isOptional: false
        type: string
        description: The column to order by
      - name: desc
        isOptional: true
        type: bool
        description: Whether the rows should be ordered in descending order or not.
      - name: foreign_table
        isOptional: true
        type: string
        description: Foreign table name whose results are to be ordered.
      - name: nullsfirst
        isOptional: true
        type: bool
        description: Order by showing nulls first
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .order("name", desc=True)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "id": 1,
                "name": "Mercury"
              },
              {
                "id": 3,
                "name": "Mars"
              },
              {
                "id": 2,
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-foreign-table
        name: On a foreign table
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments(name)")
              .order("name", desc=True, foreign_table="instruments")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'harp'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  },
                  {
                    "name": "harp"
                  }
                ]
              },
              {
                "name": "woodwinds",
                "instruments": []
              }
            ],
            "count": null
          }
          ```
        description: |
          Ordering on foreign tables doesn't affect the ordering of
          the parent table.
        hideCodeBlock: true
      - id: order-parent-table-by-a-referenced-table
        name: Order parent table by a referenced table
        code: |
          ```python
          response = (
              supabase.table("instruments")
              .select("name, section:orchestral_sections(name)")
              .order("section(name)", desc=False)
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings'),
              (2, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'violin'),
              (2, 2, 'flute');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "violin",
                "section": { "name": "strings" }
              },
              {
                "name": "flute",
                "section": { "name": "woodwinds" }
              }
            ],
            "status": 200,
            "statusText": "OK"
          }
          ```
        description: |
          Ordering with `referenced_table(col)` affects the ordering of the
          parent table.
        hideCodeBlock: true
  - id: limit
    title: limit()
    params:
      - name: size
        isOptional: false
        type: number
        description: The maximum number of rows to return
      - name: foreign_table
        isOptional: true
        type: string
        description: Set this to limit rows of foreign tables instead of the parent table.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("name")
              .limit(1)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Mercury"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-foreign-table
        name: On a foreign table
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments(name)")
              .limit(1, foreign_table="instruments")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'strings');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'harp'),
              (2, 1, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "harp"
                  }
                ]
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
  - id: range
    title: range()
    params:
      - name: start
        isOptional: false
        type: number
        description: The starting index from which to limit the result.
      - name: end
        isOptional: false
        type: number
        description: The last index to which to limit the result.
      - name: foreign_table
        isOptional: true
        type: string
        description: Set this to limit rows of foreign tables instead of the parent table.
    notes: |
      Limit the query result by starting at an offset (`start`) and ending at the offset (`end`). Only records within this range are returned. This respects the query order and if there is no order clause the range could behave unexpectedly.

      The `start` and `end` values are 0-based and inclusive: `range(1, 3)` will include the second, third and fourth rows of the query.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("name")
              .range(0, 1)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "Mercury"
              },
              {
                "name": "Earth"
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
      - id: on-a-foreign-table
        name: On a foreign table
        code: |
          ```python
          response = (
              supabase.table("orchestral_sections")
              .select("name, instruments(name)")
              .range(0, 1, foreign_table="instruments")
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              orchestral_sections (id int8 primary key, name text);
            create table
              instruments (
                id int8 primary key,
                section_id int8 not null references orchestral_sections,
                name text
              );

            insert into
              orchestral_sections (id, name)
            values
              (1, 'brass'),
              (2, 'strings'),
              (3, 'woodwinds');
            insert into
              instruments (id, section_id, name)
            values
              (1, 1, 'horn'),
              (2, 1, 'tuba'),
              (3, 2, 'violin');
            ```
        response: |
          ```json
          {
            "data": [
              {
                "name": "brass",
                "instruments": [
                  {
                    "name": "horn"
                  },
                  {
                    "name": "tuba"
                  }
                ]
              },
              {
                "name": "strings",
                "instruments": [
                  {
                    "name": "violin"
                  }
                ]
              },
              {
                "name": "woodwinds",
                "instruments": []
              }
            ],
            "count": null
          }
          ```
        hideCodeBlock: true
  - id: single
    title: single()
    notes: Return `data` as a single object instead of an array of objects.
    examples:
      - id: with-select()
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("name")
              .limit(1)
              .single()
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": {
              "name": "Mercury"
            },
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true
  - id: maybe-single
    title: maybe_single()
    notes: Return `data` as a single object instead of an array of objects.
    examples:
      - id: with-select
        name: With `select()`
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .eq("name", "Earth")
              .maybe_single()
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": {
              "id": 2,
              "name": "Earth"
            },
            "count": null
          }
          ```
        hideCodeBlock: true
        isSpotlight: true

  - id: csv
    title: csv()
    notes: Return `data` as a string in CSV format.
    examples:
      - id: return-data-as-csv
        name: Return data as CSV
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .csv()
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```json
          {
            "data": "id,name\n1,Mercury\n2,Earth\n3,Mars",
            "count": null
          }
          ```
        description: |
          By default, the data is returned in JSON format, but can also be returned as Comma Separated Values.
        hideCodeBlock: true
        isSpotlight: true

  - id: explain
    title: Using Explain
    params:
      - name: wal
        isOptional: true
        type: boolean
        description: If `true`, include information on WAL record generation.
      - name: verbose
        isOptional: true
        type: boolean
        description: If `true`, the query identifier will be returned and `data` will include the output columns of the query.
      - name: settings
        isOptional: true
        type: boolean
        description: If `true`, include information on configuration parameters that affect query planning.
      - name: format
        isOptional: true
        type: boolean
        description: The format of the output, can be `"text"` (default) or `"json"`.
      - name: format
        isOptional: true
        type: '"text" | "json"'
        description: The format of the output, can be `"text"` (default) or `"json"`.
      - name: buffers
        isOptional: true
        type: boolean
        description: If `true`, include information on buffer usage.
      - name: analyze
        isOptional: true
        type: boolean
        description: If `true`, the query will be executed and the actual run time will be returned.
    description: |
      For debugging slow queries, you can get the [Postgres `EXPLAIN` execution plan](https://www.postgresql.org/docs/current/sql-explain.html) of a query
      using the `explain()` method. This works on any query, even for `rpc()` or writes.

      Explain is not enabled by default as it can reveal sensitive information about your database.
      It's best to only enable this for testing environments but if you wish to enable it for production you can provide additional protection by using a `pre-request` function.

      Follow the [Performance Debugging Guide](/docs/guides/database/debugging-performance) to enable the functionality on your project.
    examples:
      - id: get-execution-plan
        name: Get the execution plan
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .explain()
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```
          Aggregate  (cost=33.34..33.36 rows=1 width=112)
            ->  Limit  (cost=0.00..18.33 rows=1000 width=40)
                  ->  Seq Scan on planets  (cost=0.00..22.00 rows=1200 width=40)
          ```
        description: |
          By default, the data is returned in TEXT format, but can also be returned as JSON by using the `format` parameter.
        hideCodeBlock: true
        isSpotlight: true

      - id: get-execution-plan-with-analyze-and-verbose
        name: Get the execution plan with analyze and verbose
        code: |
          ```python
          response = (
              supabase.table("planets")
              .select("*")
              .explain(analyze=True, verbose=True)
              .execute()
          )
          ```
        data:
          sql: |
            ```sql
            create table
              planets (id int8 primary key, name text);

            insert into
              planets (id, name)
            values
              (1, 'Mercury'),
              (2, 'Earth'),
              (3, 'Mars');
            ```
        response: |
          ```
          Aggregate  (cost=33.34..33.36 rows=1 width=112) (actual time=0.041..0.041 rows=1 loops=1)
            Output: NULL::bigint, count(ROW(planets.id, planets.name)), COALESCE(json_agg(ROW(planets.id, planets.name)), '[]'::json), NULLIF(current_setting('response.headers'::text, true), ''::text), NULLIF(current_setting('response.status'::text, true), ''::text)
            ->  Limit  (cost=0.00..18.33 rows=1000 width=40) (actual time=0.005..0.006 rows=3 loops=1)
                  Output: planets.id, planets.name
                  ->  Seq Scan on public.planets  (cost=0.00..22.00 rows=1200 width=40) (actual time=0.004..0.005 rows=3 loops=1)
                        Output: planets.id, planets.name
          Query Identifier: -4730654291623321173
          Planning Time: 0.407 ms
          Execution Time: 0.119 ms
          ```
        description: |
          By default, the data is returned in TEXT format, but can also be returned as JSON by using the `format` parameter.
        hideCodeBlock: true
        isSpotlight: false

  - id: invoke
    title: 'invoke()'
    description: |
      Invoke a Supabase Function.
    notes: |
      - Requires an Authorization header.
      - When you pass in a body to your function, we automatically attach the Content-Type header for `Blob`, `ArrayBuffer`, `File`, `FormData` and `String`. If it doesn't match any of these types we assume the payload is `json`, serialise it and attach the `Content-Type` header as `application/json`. You can override this behaviour by passing in a `Content-Type` header of your own.
    examples:
      - id: invoke-function
        name: Basic invocation
        description:
        code: |
          ```python
          response = supabase.functions.invoke(
              "hello-world", 
              invoke_options={
                  "body": {"name": "Functions"},
              },
          )
          ```
      - id: error-handling
        name: Error handling
        description: |
          Returns one of the following errors:

          - `FunctionsHttpError`: if your function throws an error
          - `FunctionsRelayError`: if the Supabase Relay encounters an error processing your function
        isSpotlight: true
        code: |
          ```python
          from supafunc.errors import FunctionsRelayError, FunctionsHttpError

          try:
              response = supabase.functions.invoke(
                  "hello-world",
                  invoke_options={
                      "body": {"foo": "bar"},
                      "headers": {"my-custom-header": "my-custom-header-value"},
                  },
              )
          except FunctionsHttpError as exception:
              err = exception.to_dict()
              print(f'Function returned an error {err.get("message")}')
          except FunctionsRelayError as exception:
              err = exception.to_dict()
              print(f'Relay error: {err.get("message")}')
          ```
      - id: passing-custom-headers
        name: Passing custom headers
        description: |
          The library accepts custom headers via the `headers` option.

          Note: `supabase-py` automatically populates the `Authorization` header if there is a signed in user.
        isSpotlight: true
        code: |
          ```python
          response = supabase.functions.invoke(
              "hello-world",
              invoke_options={
                  "headers": {
                      "my-custom-header": "my-custom-header-value",
                  },
                  "body": {"foo": "bar"},
              },
          )
          ```

  - id: subscribe
    title: on().subscribe()
    notes: |
      - By default, Broadcast and Presence are enabled for all projects.
      - By default, listening to database changes is disabled for new projects due to database performance and security concerns. You can turn it on by managing Realtime's [replication](/docs/guides/api#realtime-api-overview).
      - You can receive the "previous" data for updates and deletes by setting the table's `REPLICA IDENTITY` to `FULL` (e.g., `ALTER TABLE your_table REPLICA IDENTITY FULL;`).
      - Row level security is not applied to delete statements. When RLS is enabled and replica identity is set to full, only the primary key is sent to clients.
    examples:
      - id: listen-to-broadcast
        name: Listen to broadcast messages
        isSpotlight: true
        code: |
          ```python
          channel = supabase.channel("room1")

          def on_subscribe(status, err):
              if status == RealtimeSubscribeStates.SUBSCRIBED:
                  channel.send_broadcast(
                      "cursor-pos", 
                      {"x": random.random(), "y": random.random()}
                  )

          def handle_broadcast(payload):
              print("Cursor position received!", payload)

          channel.on_broadcast(event="cursor-pos", callback=handle_broadcast).subscribe(on_subscribe)
          ```
      - id: listen-to-presence-sync
        name: Listen to presence sync
        isSpotlight: true
        code: |
          ```python
          channel = supabase.channel("room1")

          def on_subscribe(status, err):
              if status == RealtimeSubscribeStates.SUBSCRIBED:
                  channel.track({"online_at": datetime.datetime.now().isoformat()})

          def handle_presence_sync():
              print("Synced presence state: ", channel.presence.state)

          channel.on_presence_sync(callback=handle_presence_sync).subscribe(on_subscribe)
          ```
      - id: listen-to-presence-join
        name: Listen to presence join
        isSpotlight: true
        code: |
          ```python
          channel = supabase.channel("room1")

          def handle_presence_join(key, current_presence, new_presence):
              print("Newly joined presences: ", new_presence)

          def on_subscribe(status, err):
              if status == RealtimeSubscribeStates.SUBSCRIBED:
                  channel.track({"online_at": datetime.datetime.now().isoformat()})

          channel.on_presence_join(callback=handle_presence_join).subscribe(on_subscribe)
          ```
      - id: listen-to-presence-leave
        name: Listen to presence leave
        isSpotlight: true
        code: |
          ```python
          channel = supabase.channel("room1")

          def handle_presence_leave(key, current_presence, left_presence):
              print("Newly left presences: ", left_presence)

          def on_subscribe(status, err):
              if status == RealtimeSubscribeStates.SUBSCRIBED:
                  channel.track({"online_at": datetime.datetime.now().isoformat()})
                  channel.untrack()

          channel.on_presence_leave(callback=handle_presence_leave).subscribe(on_subscribe)
          ```
      - id: listen-to-all-database-changes
        name: Listen to all database changes
        isSpotlight: true
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("*", schema="*", callback=handle_record_updated)
              .subscribe()
          )
          ```
      - id: listen-to-a-specific-table
        name: Listen to a specific table
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("*", schema="public", table="countries", callback=handle_record_updated)
              .subscribe()
          )
          ```
      - id: listen-to-inserts
        name: Listen to inserts
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("INSERT", schema="public", table="countries", callback=handle_record_inserted)
              .subscribe()
          )
          ```
      - id: listen-to-updates
        name: Listen to updates
        description: |
          By default, Supabase will send only the updated record. If you want to receive the previous values as well you can
          enable full replication for the table you are listening to:

          ```sql
          alter table "your_table" replica identity full;
          ```
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("UPDATE", schema="public", table="countries", callback=handle_record_updated)
              .subscribe()
          )
          ```
      - id: listen-to-deletes
        name: Listen to deletes
        description: |
          By default, Supabase does not send deleted records. If you want to receive the deleted record you can
          enable full replication for the table you are listening too:

          ```sql
          alter table "your_table" replica identity full;
          ```
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("DELETE", schema="public", table="countries", callback=handle_record_deleted)
              .subscribe()
          )
          ```
      - id: listen-to-multiple-events
        name: Listen to multiple events
        description: You can chain listeners if you want to listen to multiple events for each table.
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("INSERT", schema="public", table="countries", callback=handle_record_inserted)
              .on_postgres_changes("DELETE", schema="public", table="countries", callback=handle_record_deleted)
              .subscribe()
          )
          ```
      - id: listening-to-row-level-changes
        name: Listen to row level changes
        description: You can listen to individual rows using the format `{table}:{col}=eq.{val}` - where `{col}` is the column name, and `{val}` is the value which you want to match.
        notes: |
          - ``eq`` filter works with all database types as under the hood, it's casting both the filter value and the database value to the correct type and then comparing them.
        code: |
          ```python
          response = (
              supabase.channel("room1")
              .on_postgres_changes("UPDATE", schema="public", table="countries", filter="id=eq.200", callback=handle_record_updated)
              .subscribe()
          )
          ```
  - id: broadcast-message
    title: broadcastMessage()
    description: |
      Broadcast a message to all connected clients to a channel.
    examples:
      - id: send-a-message
        name: Send a message via websocket
        isSpotlight: true
        code: |
          ```python
          channel = supabase.channel("room1")

          def on_subscribe(status, err):
              if status == RealtimeSubscribeStates.SUBSCRIBED:
                  channel.send_broadcast('cursor-pos', {"x": random.random(), "y": random.random()})

          channel.subscribe(on_subscribe)
          ```
        response: |
          ```
          ok | timed out | error
          ```
  - id: get-channels
    title: getChannels()
    examples:
      - id: get-all-channels
        name: Get all channels
        isSpotlight: true
        code: |
          ```python
          channels = supabase.get_channels()
          ```

  - id: remove-channel
    title: removeChannel()
    notes: |
      - Removing a channel is a great way to maintain the performance of your project's Realtime service as well as your database if you're listening to Postgres changes. Supabase will automatically handle cleanup 30 seconds after a client is disconnected, but unused channels may cause degradation as more clients are simultaneously subscribed.
    examples:
      - id: removes-a-channel
        name: Removes a channel
        isSpotlight: true
        code: |
          ```python
          supabase.remove_channel(myChannel)
          ```

  - id: remove-all-channels
    title: removeAllChannels()
    notes: |
      - Removing channels is a great way to maintain the performance of your project's Realtime service as well as your database if you're listening to Postgres changes. Supabase will automatically handle cleanup 30 seconds after a client is disconnected, but unused channels may cause degradation as more clients are simultaneously subscribed.
    examples:
      - id: remove-all-channels
        name: Remove all channels
        isSpotlight: true
        code: |
          ```python
          supabase.remove_all_channels()
          ```

  - id: list-buckets
    title: 'list_buckets()'
    description: |
      Retrieves the details of all Storage buckets within an existing project.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    examples:
      - id: list-buckets
        name: List buckets
        code: |
          ```python
          response = supabase.storage.list_buckets()
          ```
        response: |
          ```json
          [
            {
              "id": "avatars",
              "name": "avatars",
              "owner": "",
              "public": false,
              "file_size_limit": 1024,
              "allowed_mime_types": [
                "image/png"
              ],
              "created_at": "2024-05-22T22:26:05.100Z",
              "updated_at": "2024-05-22T22:26:05.100Z"
            }
          ]
          ```

  - id: get-bucket
    title: 'get_bucket()'
    description: |
      Retrieves the details of an existing Storage bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: string
        description: The unique identifier of the bucket you would like to retrieve.
    examples:
      - id: get-bucket
        name: Get bucket
        code: |
          ```python
          response = supabase.storage.get_bucket("avatars")
          ```
        response: |
          ```json
          {
            "id": "avatars",
            "name": "avatars",
            "owner": "",
            "public": false,
            "file_size_limit": 1024,
            "allowed_mime_types": [
              "image/png"
            ],
            "created_at": "2024-05-22T22:26:05.100Z",
            "updated_at": "2024-05-22T22:26:05.100Z"
          }
          ```

  - id: create-bucket
    title: 'create_bucket()'
    description: |
      Creates a new Storage bucket
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `insert`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: string
        description: A unique identifier for the bucket you are creating.
      - name: options
        isOptional: false
        type: CreateOrUpdateBucketOptions
        subContent:
          - name: public
            isOptional: true
            type: bool
            description: The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private
          - name: allowed_mime_types
            isOptional: true
            type: list[string]
            description: Specifies the allowed mime types that this bucket can accept during upload. The default value is null, which allows files with all mime types to be uploaded. Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.
          - name: file_size_limit
            isOptional: true
            type: number
            description: Specifies the max file size in bytes that can be uploaded to this bucket. The global file size limit takes precedence over this value. The default value is null, which doesn't set a per bucket file size limit.
    examples:
      - id: create-bucket
        name: Create bucket
        code: |
          ```python
          response = (
              supabase.storage
              .create_bucket(
                  "avatars",
                  options={
                      "public": False,
                      "allowed_mime_types": ["image/png"],
                      "file_size_limit": 1024,
                  }
              )
          )
          ```
        response: |
          ```json
          {
            "name": "avatars"
          }
          ```

  - id: empty-bucket
    title: 'empty_bucket()'
    description: |
      Removes all objects inside a single bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select`
        - `objects` table permissions: `select` and `delete`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: string
        description: The unique identifier of the bucket you would like to empty.
    examples:
      - id: empty-bucket
        name: Empty bucket
        code: |
          ```python
          response = supabase.storage.empty_bucket("avatars")
          ```
        response: |
          ```json
          {
            "message": "Successfully emptied"
          }
          ```
  - id: update-bucket
    title: 'update_bucket()'
    description: |
      Updates a Storage bucket
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select` and `update`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: string
        description: A unique identifier for the bucket you are creating.
      - name: options
        isOptional: false
        type: CreateOrUpdateBucketOptions
        subContent:
          - name: public
            isOptional: true
            type: bool
            description: The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private
          - name: allowed_mime_types
            isOptional: true
            type: list[string]
            description: Specifies the allowed mime types that this bucket can accept during upload. The default value is null, which allows files with all mime types to be uploaded. Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.
          - name: file_size_limit
            isOptional: true
            type: number
            description: Specifies the max file size in bytes that can be uploaded to this bucket. The global file size limit takes precedence over this value. The default value is null, which doesn't set a per bucket file size limit.
    examples:
      - id: update-bucket
        name: Update bucket
        code: |
          ```python
          response = (
              supabase.storage
              .update_bucket(
                  "avatars",
                  options={
                      "public": False,
                      "allowed_mime_types": ["image/png"],
                      "file_size_limit": 1024,
                  }
              )
          )
          ```
        response: |
          ```json
          {
            "message": "Successfully updated"
          }
          ```

  - id: delete-bucket
    title: 'delete_bucket()'
    description: |
      Deletes an existing bucket. A bucket can't be deleted with existing objects inside it. You must first `empty()` the bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: `select` and `delete`
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: id
        isOptional: false
        type: string
        description: The unique identifier of the bucket you would like to delete.
    examples:
      - id: delete-bucket
        name: Delete bucket
        code: |
          ```python
          response = supabase.storage.delete_bucket("avatars")
          ```
        response: |
          ```json
          {
            "message": "Successfully deleted"
          }
          ```

  - id: from-upload
    title: 'from_.upload()'
    description: Uploads a file to an existing bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: only `insert` when you are uploading new files and `select`, `insert` and `update` when you are upserting files
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
      - Please specify the appropriate content [MIME type](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types) if you are uploading images or audio. If no `file_options` are specified, the MIME type defaults to `text/html`.
    params:
      - name: path
        isOptional: false
        type: string
        description: The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.
      - name: file
        isOptional: false
        type: BufferedReader | bytes | FileIO | string | Path
        description: The body of the file to be stored in the bucket.
      - name: file_options
        isOptional: false
        type: FileOptions
        subContent:
          - name: cache-control
            isOptional: true
            type: string
            description: |
              The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds.
          - name: content-type
            isOptional: true
            type: string
            description: |
              The `Content-Type` header value. Should be specified if using a `file` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.
          - name: upsert
            isOptional: true
            type: string
            description: When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false.
    examples:
      - id: upload-file
        name: Upload file using filepath
        code: |
          ```python
          with open("./public/avatar1.png", "rb") as f:
              response = (
                  supabase.storage
                  .from_("avatars")
                  .upload(
                      file=f,
                      path="public/avatar1.png",
                      file_options={"cache-control": "3600", "upsert": "false"}
                  )
              )
          ```
        response: |
          ```json
          {
            "path": "public/avatar1.png",
            "full_path": "avatars/public/avatar1.png"
          }
          ```
  - id: from-update
    title: from_.update()
    description: Replaces an existing file at the specified path with a new one.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `update` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: string
        description: The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.
      - name: file
        isOptional: false
        type: BufferedReader | bytes | FileIO | string | Path
        description: The body of the file to be stored in the bucket.
      - name: file_options
        isOptional: false
        type: FileOptions
        subContent:
          - name: cache-control
            isOptional: true
            type: string
            description: |
              The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds.
          - name: content-type
            isOptional: true
            type: string
            description: |
              The `Content-Type` header value. Should be specified if using a `file` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.
          - name: upsert
            isOptional: true
            type: string
            description: When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false.
    examples:
      - id: update-file
        name: Update file
        code: |
          ```python
          with open("./public/avatar1.png", "rb") as f:
              response = (
                  supabase.storage
                  .from_("avatars")
                  .update(
                      file=f,
                      path="public/avatar1.png",
                      file_options={"cache-control": "3600", "upsert": "true"}
                  )
              )
          ```
        response: |
          ```json
          {
            "path": "public/avatar1.png",
            "full_path": "avatars/public/avatar1.png"
          }
          ```
  - id: from-move
    title: 'from_.move()'
    description: Moves an existing file to a new path in the same bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `update` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: from_path
        isOptional: false
        type: string
        description: The original file path, including the current file name. For example `folder/image.png`.
      - name: to_path
        isOptional: false
        type: string
        description: The new file path, including the new file name. For example `folder/image-new.png`.
    examples:
      - id: move-file
        name: Move file
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .move(
                  "public/avatar1.png", 
                  "private/avatar2.png"
              )
          )
          ```
        response: |
          ```json
          {
            "message": "Successfully moved"
          }
          ```
  - id: from-copy
    title: 'from_.copy()'
    description: Copies an existing file to a new path in the same bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `update` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: from_path
        isOptional: false
        type: string
        description: The original file path, including the current file name. For example `folder/image.png`.
      - name: to_path
        isOptional: false
        type: string
        description: The new file path, including the new file name. For example `folder/image-new.png`.
    examples:
      - id: copy-file
        name: Copy file
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .copy(
                  "public/avatar1.png", 
                  "private/avatar2.png"
              )
          )
          ```
        response: |
          ```json
          {
            "path": "avatars/private/avatar2.png"
          }
          ```

  - id: from-create-signed-urls
    title: 'from_.create_signed_urls()'
    description: Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: paths
        isOptional: false
        type: list[string]
        description: |
          The file paths to be downloaded, including the current file names. For example `["folder/image.png", "folder2/image2.png"]`.
      - name: expires_in
        isOptional: false
        type: number
        description: The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.
      - name: options
        isOptional: true
        type: CreateSignedURLsOptions
        subContent:
          - name: download
            isOptional: true
            type: string | bool
            description: |
              Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.
    examples:
      - id: create-signed-urls
        name: Create Signed URLs
        isSpotlight: true
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .create_signed_urls(
                  ["folder/avatar1.png", "folder/avatar2.png"], 
                  60
              )
          )
          ```
        response: |
          ```json
          [
            {
              "error": null,
              "path": "folder/avatar1.png",
              "signedURL": "/object/sign/avatars/folder/avatar1.png?token=<TOKEN>",
              "signedUrl": "https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar1.png?token=<TOKEN>"
            },
            {
              "error": null,
              "path": "folder/avatar2.png",
              "signedURL": "/object/sign/avatars/folder/avatar2.png?token=<TOKEN>",
              "signedUrl": "https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar2.png?token=<TOKEN>"
            }
          ]
          ```
  - id: from-create-signed-url
    title: 'from_.create_signed_url()'
    description: Creates a signed URL for a file. Use a signed URL to share a file for a fixed amount of time.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: string
        description: |
          The file path, including the file name. For example `"folder/image.png"`.
      - name: expires_in
        isOptional: false
        type: number
        description: The number of seconds until the signed URL expires. For example, `60` for URLs which are valid for one minute.
      - name: options
        isOptional: true
        type: URLOptions
        subContent:
          - name: download
            isOptional: true
            type: string | bool
            description: |
              Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.
          - name: transform
            isOptional: true
            type: TransformOptions
            description: Transform the asset before serving it to the client.
            subContent:
              - name: format
                isOptional: true
                type: origin | avif
                description: Specify the format of the image requested.
              - name: height
                isOptional: true
                type: number
                description: The height of the image in pixels.
              - name: quality
                isOptional: true
                type: number
                description: Set the quality of the returned image. A number from 20 to 100, with 100 being the highest quality. Defaults to 80.
              - name: resize
                isOptional: true
                type: cover | contain | fill
                description: |
                  The resize mode can be cover, contain or fill. Defaults to cover. Cover resizes the image to maintain it's aspect ratio while filling the entire width and height. Contain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height. Fill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit.
              - name: width
                isOptional: true
                type: number
                description: The width of the image in pixels.
    examples:
      - id: create-signed-url
        name: Create Signed URL
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .create_signed_url(
                  "folder/avatar1.png", 
                  60
              )
          )
          ```
        response: |
          ```json
          {
            "signedUrl": "https://example.supabase.co/storage/v1/object/sign/avatars/folder/avatar1.png?token=<TOKEN>"
          }
          ```
      - id: create-signed-url-with-transformations
        name: Create a signed URL for an asset with transformations
        isSpotlight: true
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .create_signed_url(
                  "folder/avatar1.png", 
                  60, 
                  {"transform": {"width": 100, "height": 100}}
              )
          )
          ```
      - id: create-signed-url-with-download
        name: Create a signed URL which triggers the download of the asset
        isSpotlight: true
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .create_signed_url(
                  "folder/avatar1.png", 
                  60, 
                  {"download": True}
              )
          )
          ```

  - id: from-create-signed-upload-url
    title: 'from_.create_signed_upload_url()'
    description: Creates a signed upload URL. Signed upload URLs can be used to upload files to the bucket without further authentication. They are valid for 2 hours.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `insert`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: string
        description: |
          The file path, including the current file name. For example `"folder/image.png"`.
    examples:
      - id: create-signed-url
        name: Create Signed URL
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .create_signed_upload_url("folder/avatar1.png")
          )
          ```
        response: |
          ```json
          {
            "signedUrl": "https://example.supabase.co/storage/v1/object/upload/sign/avatars/folder/cat.jpg?token=<TOKEN>",
              "path": "folder/cat.jpg",
              "token": "<TOKEN>"
          }
          ```
  - id: from-upload-to-signed-url
    title: 'from_.upload_to_signed_url()'
    description: Upload a file with a token generated from `create_signed_upload_url`.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: string
        description: |
          The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.
      - name: token
        isOptional: false
        type: string
        description: The token generated from `create_signed_upload_url`
      - name: file
        isOptional: false
        type: BufferedReader | bytes | FileIO | string | Path
        description: The body of the file to be stored in the bucket.
      - name: options
        isOptional: false
        type: FileOptions
        subContent:
          - name: cache-control
            isOptional: true
            type: string
            description: |
              The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds.
          - name: content-type
            isOptional: true
            type: string
            description: |
              The `Content-Type` header value. Should be specified if using a `file` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.
          - name: upsert
            isOptional: true
            type: string
            description: When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false.
    examples:
      - id: create-signed-url
        name: Create Signed URL
        code: |
          ```python
          with open("./public/avatar1.png", "rb") as f:
              response = (
                  supabase.storage
                  .from_("avatars")
                  .upload_to_signed_url(
                      path="folder/cat.jpg",
                      token="token-from-create_signed_upload_url",
                      file=f,
                  )
              )
          ```
        response: |
          ```json
          {
            "path": "folder/cat.jpg",
            "full_path": "avatars/folder/cat.jpg"
          }
          ```

  - id: from-get-public-url
    title: 'from_.get_public_url()'
    description: |
      A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset. This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.
    notes: |
      - The bucket needs to be set to public, either via [update_bucket()](/docs/reference/python/storage-updatebucket) or by going to Storage on [supabase.com/dashboard](https://supabase.com/dashboard), clicking the overflow menu on a bucket and choosing "Make public"
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: none
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: string
        description: |
          The path and name of the file to generate the public URL for. For example `folder/image.png`.
      - name: options
        isOptional: true
        type: URLOptions
        subContent:
          - name: download
            isOptional: true
            type: string | bool
            description: |
              Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.
          - name: transform
            isOptional: true
            type: TransformOptions
            description: Transform the asset before serving it to the client.
            subContent:
              - name: format
                isOptional: true
                type: origin | avif
                description: Specify the format of the image requested.
              - name: height
                isOptional: true
                type: number
                description: The height of the image in pixels.
              - name: quality
                isOptional: true
                type: number
                description: Set the quality of the returned image. A number from 20 to 100, with 100 being the highest quality. Defaults to 80.
              - name: resize
                isOptional: true
                type: cover | contain | fill
                description: |
                  The resize mode can be cover, contain or fill. Defaults to cover. Cover resizes the image to maintain it's aspect ratio while filling the entire width and height. Contain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height. Fill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit.
              - name: width
                isOptional: true
                type: number
                description: The width of the image in pixels.
    examples:
      - id: get-public-url
        name: Returns the URL for an asset in a public bucket
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .get_public_url("folder/avatar1.jpg")
          )
          ```
        response: |
          ```json
          {
            "publicURL": "https://example.supabase.co/storage/v1/object/public/public-bucket/folder/avatar1.png"
          }
          ```
      - id: transform-asset-in-public-bucket
        name: Returns the URL for an asset in a public bucket with transformations
        isSpotlight: true
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .get_public_url(
                  "folder/avatar1.jpg", 
                  {"transform": {"width": 100, "height": 100}}
              )
          )
          ```
      - id: download-asset-in-public-bucket
        name: Returns the URL which triggers the download of an asset in a public bucket
        isSpotlight: true
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .get_public_url(
                  "folder/avatar1.jpg", 
                  {"download": True}
              )
          )
          ```

  - id: from-download
    title: 'from_.download()'
    description: Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `get_public_url` instead.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: false
        type: string
        description: The full path and file name of the file to be downloaded. For example `folder/image.png`.
      - name: options
        isOptional: false
        type: DownloadOptions
        subContent:
          - name: transform
            isOptional: true
            type: TransformOptions
            description: Transform the asset before serving it to the client.
            subContent:
              - name: format
                isOptional: true
                type: origin | avif
                description: Specify the format of the image requested.
              - name: height
                isOptional: true
                type: number
                description: The height of the image in pixels.
              - name: quality
                isOptional: true
                type: number
                description: Set the quality of the returned image. A number from 20 to 100, with 100 being the highest quality. Defaults to 80.
              - name: resize
                isOptional: true
                type: cover | contain | fill
                description: |
                  The resize mode can be cover, contain or fill. Defaults to cover. Cover resizes the image to maintain it's aspect ratio while filling the entire width and height. Contain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height. Fill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit.
              - name: width
                isOptional: true
                type: number
                description: The width of the image in pixels.
    examples:
      - id: download-file
        name: Download file
        code: |
          ```python
          with open("./myfolder/avatar1.png", "wb+") as f:
              response = (
                  supabase.storage
                  .from_("avatars")
                  .download("folder/avatar1.png")
              )
              f.write(response)
          ```
      - id: download-file-with-transformations
        name: Download file with transformations
        isSpotlight: true
        code: |
          ```python
          with open("./myfolder/avatar1.png", "wb+") as f:
              response = (
                  supabase.storage
                  .from_("avatars")
                  .download(
                      "folder/avatar1.png",
                      {"transform": {"width": 100, "height": 100, "quality": 80}},
                  )
              )
              f.write(response)
          ```

  - id: from-remove
    title: 'from_.remove()'
    description: Deletes files within the same bucket
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `delete` and `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: paths
        isOptional: false
        type: list[string]
        description: |
          An array of files to delete, including the path and file name. For example `["folder/image.png"]`.
    examples:
      - id: delete-file
        name: Delete file
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .remove(["folder/avatar1.png"])
          )
          ```
        response: |
          ```json
          [
            {
              "name": "folder/avatar1.png",
              "bucket_id": "avatars",
              "owner": "",
              "owner_id": "",
              "version": "151d16d2-0319-4e5b-add4-53820e8a0863",
              "id": "e281a32a-6998-4ed1-b8e0-7d3d7ae9ae3c",
              "updated_at": "2024-10-25T15:52:13.993Z",
              "created_at": "2024-10-23T21:33:10.046Z",
              "last_accessed_at": "2024-10-23T21:33:10.046Z",
              "metadata": {
                "eTag": "\"ca390059ef9fdb91e2ad5447201d2e91\"",
                "size": 343017,
                "mimetype": "image/png",
                "cacheControl": "max-age=360",
                "lastModified": "2024-10-25T15:52:13.974Z",
                "contentLength": 343017,
                "httpStatusCode": 200
              },
              "user_metadata": {}
            }
          ]
          ```

  - id: from-list
    title: 'from_.list()'
    description: Lists all the files within a bucket.
    notes: |
      - RLS policy permissions required:
        - `buckets` table permissions: none
        - `objects` table permissions: `select`
      - Refer to the [Storage guide](/docs/guides/storage/security/access-control) on how access control works
    params:
      - name: path
        isOptional: true
        type: string
        description: |
          The folder path.
      - name: options
        isOptional: true
        type: SearchOptions
        subContent:
          - name: limit
            isOptional: true
            type: number
            description: The number of files you want to be returned.
          - name: offset
            isOptional: true
            type: number
            description: The starting position.
          - name: search
            isOptional: true
            type: number
            description: The search string to filter files by.
          - name: sortBy
            isOptional: true
            type: number
            description: The column to sort by. Can be any column inside a FileObject.
            subContent:
              - name: column
                isOptional: true
                type: string
              - name: order
                isOptional: true
                type: asc | desc
    examples:
      - id: list-files
        name: List files in a bucket
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .list(
                  "folder",
                  {
                      "limit": 100,
                      "offset": 0,
                      "sortBy": {"column": "name", "order": "desc"},
                  }
              )
          )
          ```
        response: |
          ```json
          [
            {
              "name": "avatar1.png",
              "id": "e668cf7f-821b-4a2f-9dce-7dfa5dd1cfd2",
              "updated_at": "2024-05-22T23:06:05.580Z",
              "created_at": "2024-05-22T23:04:34.443Z",
              "last_accessed_at": "2024-05-22T23:04:34.443Z",
              "metadata": {
                "eTag": "\"c5e8c553235d9af30ef4f6e280790b92\"",
                "size": 32175,
                "mimetype": "image/png",
                "cacheControl": "max-age=3600",
                "lastModified": "2024-05-22T23:06:05.574Z",
                "contentLength": 32175,
                "httpStatusCode": 200
              }
            }
          ]
          ```
      - id: search-files-in-a-bucket
        name: Search files in a bucket
        code: |
          ```python
          response = (
              supabase.storage
              .from_("avatars")
              .list(
                  "folder",
                  {
                      "limit": 100,
                      "offset": 0,
                      "sortBy": {"column": "name", "order": "desc"},
                      "search": "jon",
                  }
              )
          )
          ```
