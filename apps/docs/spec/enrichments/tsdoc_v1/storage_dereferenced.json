{"id": 0, "name": "@supabase/storage-js", "kind": 0, "flags": {}, "originalName": "", "children": [{"id": 312, "name": "\"StorageClient\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/StorageClient.ts", "children": [{"id": 313, "name": "StorageClient", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 314, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 315, "name": "new StorageClient", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 316, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 317, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 318, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 319, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 320, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "StorageClient.ts", "line": 5, "character": 35}]}}, "defaultValue": "{}"}, {"id": 321, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}], "type": {"type": "reference", "id": 313, "name": "StorageClient"}, "overwrites": {"type": "reference", "id": 114, "name": "StorageBucketApi.__constructor"}}], "sources": [{"fileName": "StorageClient.ts", "line": 4, "character": 53}], "overwrites": {"type": "reference", "id": 114, "name": "StorageBucketApi.__constructor"}}, {"id": 330, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 9, "character": 17}], "type": {"type": "reference", "id": 15, "name": "<PERSON>tch"}, "inheritedFrom": {"type": "reference", "id": 113, "name": "StorageBucketApi.fetch"}}, {"id": 326, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 8, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 327, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 328, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 329, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 8, "character": 20}]}}, "inheritedFrom": {"type": "reference", "id": 109, "name": "StorageBucketApi.headers"}}, {"id": 325, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 7, "character": 15}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 108, "name": "StorageBucketApi.url"}}, {"id": 342, "name": "createBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 343, "name": "createBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Creates a new Storage bucket", "returns": "newly created bucket id\n"}, "parameters": [{"id": 344, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A unique identifier for the bucket you are creating."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 345, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 346, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 347, "name": "public", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 51, "character": 21}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Variables", "kind": 32, "children": [347]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 51, "character": 12}]}}, "defaultValue": "{ public: false }"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 348, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 349, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 52, "character": 19}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 350, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 52, "character": 41}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [349, 350]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 52, "character": 13}]}}], "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 133, "name": "StorageBucketApi.createBucket"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 49, "character": 20}], "inheritedFrom": {"type": "reference", "id": 133, "name": "StorageBucketApi.createBucket"}}, {"id": 370, "name": "deleteBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 371, "name": "deleteBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\nYou must first `empty()` the bucket."}, "parameters": [{"id": 372, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The unique identifier of the bucket you would like to delete.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 373, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 374, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 375, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 376, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [376]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 377, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [374, 377]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 13}]}}], "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 161, "name": "StorageBucketApi.deleteBucket"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 115, "character": 20}], "inheritedFrom": {"type": "reference", "id": 161, "name": "StorageBucketApi.deleteBucket"}}, {"id": 362, "name": "emptyBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 363, "name": "emptyBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Removes all objects inside a single bucket."}, "parameters": [{"id": 364, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The unique identifier of the bucket you would like to empty.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 365, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 366, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 367, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 368, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [368]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 369, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [366, 369]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 13}]}}], "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 153, "name": "StorageBucketApi.emptyBucket"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 93, "character": 19}], "inheritedFrom": {"type": "reference", "id": 153, "name": "StorageBucketApi.emptyBucket"}}, {"id": 322, "name": "from", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 323, "name": "from", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Perform file operation in a bucket."}, "parameters": [{"id": 324, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The bucket id to operate on.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 170, "name": "StorageFileApi"}}], "sources": [{"fileName": "StorageClient.ts", "line": 14, "character": 6}]}, {"id": 336, "name": "getBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 337, "name": "getBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves the details of an existing Storage bucket."}, "parameters": [{"id": 338, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The unique identifier of the bucket you would like to retrieve.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 339, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 340, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 45}], "type": {"type": "union", "types": [{"type": "reference", "id": 74, "name": "Bucket"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 341, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 67}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [340, 341]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 39}]}}], "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 127, "name": "StorageBucketApi.getBucket"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 17}], "inheritedFrom": {"type": "reference", "id": 127, "name": "StorageBucketApi.getBucket"}}, {"id": 331, "name": "listBuckets", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 332, "name": "listBuckets", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves the details of all Storage buckets within an existing project."}, "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 333, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 334, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 37}], "type": {"type": "union", "types": [{"type": "array", "elementType": {"type": "reference", "id": 74, "name": "Bucket"}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 335, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 61}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [334, 335]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 31}]}}], "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 122, "name": "StorageBucketApi.listBuckets"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 19}], "inheritedFrom": {"type": "reference", "id": 122, "name": "StorageBucketApi.listBuckets"}}, {"id": 351, "name": "updateBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 352, "name": "updateBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Updates a new Storage bucket"}, "parameters": [{"id": 353, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A unique identifier for the bucket you are updating.\n"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 354, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 355, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 356, "name": "public", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 73, "character": 21}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Variables", "kind": 32, "children": [356]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 73, "character": 12}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 357, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 358, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 359, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 360, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [360]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 361, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [358, 361]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 13}]}}], "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 142, "name": "StorageBucketApi.updateBucket"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 71, "character": 20}], "inheritedFrom": {"type": "reference", "id": 142, "name": "StorageBucketApi.updateBucket"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [314]}, {"title": "Properties", "kind": 1024, "children": [330, 326, 325]}, {"title": "Methods", "kind": 2048, "children": [342, 370, 362, 322, 336, 331, 351]}], "sources": [{"fileName": "StorageClient.ts", "line": 4, "character": 26}], "extendedTypes": [{"type": "reference", "id": 107, "name": "StorageBucketApi"}]}], "groups": [{"title": "Classes", "kind": 128, "children": [313]}], "sources": [{"fileName": "StorageClient.ts", "line": 1, "character": 0}]}, {"id": 378, "name": "\"index\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/index.ts", "children": [{"id": 381, "name": "Bucket", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 74}, {"id": 382, "name": "FileObject", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 81}, {"id": 384, "name": "FileOptions", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 95}, {"id": 386, "name": "<PERSON><PERSON><PERSON>", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 104}, {"id": 385, "name": "SearchOptions", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 99}, {"id": 383, "name": "SortBy", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 92}, {"id": 379, "name": "StorageClient", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 313}, {"id": 380, "name": "SupabaseStorageClient", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 313}], "groups": [{"title": "References", "kind": 16777216, "children": [381, 382, 384, 386, 385, 383, 379, 380]}], "sources": [{"fileName": "index.ts", "line": 1, "character": 0}]}, {"id": 106, "name": "\"lib/StorageBucketApi\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/StorageBucketApi.ts", "children": [{"id": 107, "name": "StorageBucketApi", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 114, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 115, "name": "new StorageBucketApi", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 116, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 117, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 118, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 119, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 120, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 11, "character": 35}]}}, "defaultValue": "{}"}, {"id": 121, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}], "type": {"type": "reference", "id": 107, "name": "StorageBucketApi"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 9, "character": 24}]}, {"id": 113, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 9, "character": 17}], "type": {"type": "reference", "id": 15, "name": "<PERSON>tch"}}, {"id": 109, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 8, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 110, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 111, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 112, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 8, "character": 20}]}}}, {"id": 108, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 7, "character": 15}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 133, "name": "createBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 134, "name": "createBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Creates a new Storage bucket", "returns": "newly created bucket id\n"}, "parameters": [{"id": 135, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A unique identifier for the bucket you are creating."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 136, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 137, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 138, "name": "public", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 51, "character": 21}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Variables", "kind": 32, "children": [138]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 51, "character": 12}]}}, "defaultValue": "{ public: false }"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 139, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 140, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 52, "character": 19}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 141, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 52, "character": 41}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [140, 141]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 52, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 49, "character": 20}]}, {"id": 161, "name": "deleteBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 162, "name": "deleteBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\nYou must first `empty()` the bucket."}, "parameters": [{"id": 163, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The unique identifier of the bucket you would like to delete.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 164, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 165, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 166, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 167, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [167]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 168, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [165, 168]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 117, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 115, "character": 20}]}, {"id": 153, "name": "emptyBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 154, "name": "emptyBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Removes all objects inside a single bucket."}, "parameters": [{"id": 155, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The unique identifier of the bucket you would like to empty.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 156, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 157, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 158, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 159, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [159]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 160, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [157, 160]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 95, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 93, "character": 19}]}, {"id": 127, "name": "getBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 128, "name": "getBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves the details of an existing Storage bucket."}, "parameters": [{"id": 129, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The unique identifier of the bucket you would like to retrieve.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 130, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 131, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 45}], "type": {"type": "union", "types": [{"type": "reference", "id": 74, "name": "Bucket"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 132, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 67}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [131, 132]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 39}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 34, "character": 17}]}, {"id": 122, "name": "listBuckets", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 123, "name": "listBuckets", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves the details of all Storage buckets within an existing project."}, "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 124, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 125, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 37}], "type": {"type": "union", "types": [{"type": "array", "elementType": {"type": "reference", "id": 74, "name": "Bucket"}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 126, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 61}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [125, 126]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 31}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 20, "character": 19}]}, {"id": 142, "name": "updateBucket", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 143, "name": "updateBucket", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Updates a new Storage bucket"}, "parameters": [{"id": 144, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A unique identifier for the bucket you are updating.\n"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 145, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 146, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 147, "name": "public", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 73, "character": 21}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Variables", "kind": 32, "children": [147]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 73, "character": 12}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 148, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 149, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 150, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 151, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [151]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 152, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [149, 152]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 74, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 71, "character": 20}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [114]}, {"title": "Properties", "kind": 1024, "children": [113, 109, 108]}, {"title": "Methods", "kind": 2048, "children": [133, 161, 153, 127, 122, 142]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 6, "character": 29}], "extendedBy": [{"type": "reference", "id": 313, "name": "StorageClient"}]}], "groups": [{"title": "Classes", "kind": 128, "children": [107]}], "sources": [{"fileName": "lib/StorageBucketApi.ts", "line": 1, "character": 0}]}, {"id": 169, "name": "\"lib/StorageFileApi\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/StorageFileApi.ts", "children": [{"id": 170, "name": "StorageFileApi", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 178, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 179, "name": "new StorageFileApi", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 180, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 181, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 182, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 183, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 184, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 28, "character": 12}]}}, "defaultValue": "{}"}, {"id": 185, "name": "bucketId", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 186, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}], "type": {"type": "reference", "id": 170, "name": "StorageFileApi"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 24, "character": 24}]}, {"id": 176, "name": "bucketId", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 23, "character": 20}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 177, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 24, "character": 17}], "type": {"type": "reference", "id": 15, "name": "<PERSON>tch"}}, {"id": 172, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 22, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 173, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 174, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 175, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 22, "character": 20}]}}}, {"id": 171, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 21, "character": 15}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 286, "name": "_get<PERSON><PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 287, "name": "_get<PERSON><PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 288, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 387, "character": 15}]}, {"id": 289, "name": "_removeEmptyFolders", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 290, "name": "_removeEmptyFolders", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 291, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 391, "character": 21}]}, {"id": 227, "name": "copy", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 228, "name": "copy", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Copies an existing file."}, "parameters": [{"id": 229, "name": "fromPath", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The original file path, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 230, "name": "to<PERSON><PERSON>", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The new file path, including the new file name. For example `folder/image-copy.png`.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 231, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 232, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 195, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 233, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 234, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 195, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [234]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 195, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 235, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 195, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [232, 235]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 195, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 192, "character": 12}]}, {"id": 236, "name": "createSignedUrl", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 237, "name": "createSignedUrl", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "<PERSON><PERSON> signed URL to download file without requiring permissions. This URL can be valid for a set number of seconds."}, "parameters": [{"id": 238, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The file path to be downloaded, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 239, "name": "expiresIn", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n"}, "type": {"type": "intrinsic", "name": "number"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 240, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 241, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 219, "character": 8}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 242, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 243, "name": "signedURL", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 219, "character": 21}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [243]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 219, "character": 9}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 244, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 220, "character": 9}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 245, "name": "signedURL", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 221, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [241, 244, 245]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 218, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 215, "character": 23}]}, {"id": 246, "name": "createSignedUrls", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 247, "name": "createSignedUrls", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Create signed URLs to download files without requiring permissions. These URLs can be valid for a set number of seconds."}, "parameters": [{"id": 248, "name": "paths", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`."}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 249, "name": "expiresIn", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n"}, "type": {"type": "intrinsic", "name": "number"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 250, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 251, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 249, "character": 8}], "type": {"type": "union", "types": [{"type": "array", "elementType": {"type": "reflection", "declaration": {"id": 252, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 253, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 249, "character": 17}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 254, "name": "path", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 249, "character": 38}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 255, "name": "signedURL", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 249, "character": 64}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [253, 254, 255]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 249, "character": 9}]}}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 256, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 250, "character": 9}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [251, 256]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 248, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 245, "character": 24}]}, {"id": 257, "name": "download", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 258, "name": "download", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Downloads a file."}, "parameters": [{"id": 259, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The file path to be downloaded, including the path and file name. For example `folder/image.png`.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 260, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 261, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 276, "character": 46}], "type": {"type": "union", "types": [{"type": "reference", "name": "Blob"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 262, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 276, "character": 66}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [261, 262]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 276, "character": 40}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 276, "character": 16}]}, {"id": 263, "name": "getPublicUrl", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 264, "name": "getPublicUrl", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieve URLs for assets in public buckets"}, "parameters": [{"id": 265, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The file path to be downloaded, including the path and file name. For example `folder/image.png`.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reflection", "declaration": {"id": 266, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 267, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 298, "character": 8}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 268, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 269, "name": "publicURL", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 298, "character": 21}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [269]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 298, "character": 9}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 270, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 299, "character": 9}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 271, "name": "publicURL", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 300, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [267, 270, 271]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 297, "character": 4}]}}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 295, "character": 14}]}, {"id": 278, "name": "list", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 279, "name": "list", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Lists all the files within a bucket."}, "parameters": [{"id": 280, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "The folder path."}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 281, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "Search options, including `limit`, `offset`, `sortBy`, and `search`."}, "type": {"type": "reference", "id": 99, "name": "SearchOptions", "dereferenced": {"id": 99, "name": "SearchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 100, "name": "limit", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The number of files you want to be returned."}, "sources": [{"fileName": "lib/types.ts", "line": 35, "character": 7}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 101, "name": "offset", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The starting position."}, "sources": [{"fileName": "lib/types.ts", "line": 38, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 103, "name": "search", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The search string to filter files by."}, "sources": [{"fileName": "lib/types.ts", "line": 44, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 102, "name": "sortBy", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The column to sort by. Can be any column inside a FileObject."}, "sources": [{"fileName": "lib/types.ts", "line": 41, "character": 8}], "type": {"type": "reference", "id": 92, "name": "SortBy"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [100, 101, 103, 102]}], "sources": [{"fileName": "lib/types.ts", "line": 33, "character": 30}]}}}, {"id": 282, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "Fetch parameters, currently only supports `signal`, which is an AbortController's signal\n"}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 283, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 284, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 371, "character": 19}], "type": {"type": "union", "types": [{"type": "array", "elementType": {"type": "reference", "id": 81, "name": "FileObject"}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 285, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 371, "character": 47}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [284, 285]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 371, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 367, "character": 12}]}, {"id": 218, "name": "move", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 219, "name": "move", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Moves an existing file."}, "parameters": [{"id": 220, "name": "fromPath", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The original file path, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 221, "name": "to<PERSON><PERSON>", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The new file path, including the new file name. For example `folder/image-new.png`.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 222, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 223, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 172, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 224, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 225, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 172, "character": 30}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [225]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 172, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 226, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 172, "character": 54}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [223, 226]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 172, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 169, "character": 12}]}, {"id": 272, "name": "remove", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 273, "name": "remove", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Deletes files within the same bucket"}, "parameters": [{"id": 274, "name": "paths", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "An array of files to be deleted, including the path and file name. For example [`folder/image.png`].\n"}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 275, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 276, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 317, "character": 47}], "type": {"type": "union", "types": [{"type": "array", "elementType": {"type": "reference", "id": 81, "name": "FileObject"}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 277, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 317, "character": 75}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [276, 277]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 317, "character": 41}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 317, "character": 14}]}, {"id": 208, "name": "update", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 209, "name": "update", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Replaces an existing file at the specified path with a new one."}, "parameters": [{"id": 210, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 211, "name": "fileBody", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The body of the file to be stored in the bucket."}, "type": {"type": "union", "types": [{"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew"}, {"type": "reference", "name": "Blob"}, {"type": "reference", "name": "<PERSON><PERSON><PERSON>"}, {"type": "reference", "name": "File"}, {"type": "reference", "name": "FormData"}, {"type": "reference", "name": "ReadableStream"}, {"type": "reference", "typeArguments": [{"type": "reference", "name": "Uint8Array"}], "name": "ReadableStream"}, {"type": "reference", "name": "URLSearchParams"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 212, "name": "fileOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "HTTP headers.\n`cacheControl`: string, the `Cache-Control: max-age=<seconds>` seconds value.\n`contentType`: string, the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.\n`upsert`: boolean, whether to perform an upsert.\n"}, "type": {"type": "reference", "id": 95, "name": "FileOptions", "dereferenced": {"id": 95, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 96, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 28, "character": 14}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 97, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 29, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 98, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 30, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [96, 97, 98]}], "sources": [{"fileName": "lib/types.ts", "line": 27, "character": 28}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 213, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 214, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 159, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 215, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 216, "name": "Key", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 159, "character": 26}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [216]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 159, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 217, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 159, "character": 50}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [214, 217]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 159, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 145, "character": 14}]}, {"id": 198, "name": "upload", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 199, "name": "upload", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Uploads a file to an existing bucket."}, "parameters": [{"id": 200, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 201, "name": "fileBody", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The body of the file to be stored in the bucket."}, "type": {"type": "union", "types": [{"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew"}, {"type": "reference", "name": "Blob"}, {"type": "reference", "name": "<PERSON><PERSON><PERSON>"}, {"type": "reference", "name": "File"}, {"type": "reference", "name": "FormData"}, {"type": "reference", "name": "ReadableStream"}, {"type": "reference", "typeArguments": [{"type": "reference", "name": "Uint8Array"}], "name": "ReadableStream"}, {"type": "reference", "name": "URLSearchParams"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 202, "name": "fileOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "HTTP headers.\n`cacheControl`: string, the `Cache-Control: max-age=<seconds>` seconds value.\n`contentType`: string, the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.\n`upsert`: boolean, whether to perform an upsert.\n"}, "type": {"type": "reference", "id": 95, "name": "FileOptions", "dereferenced": {"id": 95, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 96, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 28, "character": 14}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 97, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 29, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 98, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 30, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [96, 97, 98]}], "sources": [{"fileName": "lib/types.ts", "line": 27, "character": 28}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 203, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 204, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 131, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 205, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 206, "name": "Key", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 131, "character": 26}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [206]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 131, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 207, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 131, "character": 50}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [204, 207]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 131, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 117, "character": 14}]}, {"id": 187, "name": "uploadOrUpdate", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 188, "name": "uploadOrUpdate", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one."}, "parameters": [{"id": 189, "name": "method", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "HTTP method."}, "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PUT"}]}}, {"id": 190, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 191, "name": "fileBody", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The body of the file to be stored in the bucket."}, "type": {"type": "union", "types": [{"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew"}, {"type": "reference", "name": "Blob"}, {"type": "reference", "name": "<PERSON><PERSON><PERSON>"}, {"type": "reference", "name": "File"}, {"type": "reference", "name": "FormData"}, {"type": "reference", "name": "ReadableStream"}, {"type": "reference", "typeArguments": [{"type": "reference", "name": "Uint8Array"}], "name": "ReadableStream"}, {"type": "reference", "name": "URLSearchParams"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 192, "name": "fileOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "HTTP headers.\n`cacheControl`: string, the `Cache-Control: max-age=<seconds>` seconds value.\n`contentType`: string, the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`.\n`upsert`: boolean, whether to perform an upsert.\n"}, "type": {"type": "reference", "id": 95, "name": "FileOptions", "dereferenced": {"id": 95, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 96, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 28, "character": 14}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 97, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 29, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 98, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 30, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [96, 97, 98]}], "sources": [{"fileName": "lib/types.ts", "line": 27, "character": 28}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 193, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 194, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 64, "character": 19}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 195, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 196, "name": "Key", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 64, "character": 26}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [196]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 64, "character": 20}]}}, {"type": "intrinsic", "name": "null"}]}}, {"id": 197, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 64, "character": 50}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [194, 197]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 64, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 49, "character": 30}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [178]}, {"title": "Properties", "kind": 1024, "children": [176, 177, 172, 171]}, {"title": "Methods", "kind": 2048, "children": [286, 289, 227, 236, 246, 257, 263, 278, 218, 272, 208, 198, 187]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 20, "character": 27}]}, {"id": 298, "name": "DEFAULT_FILE_OPTIONS", "kind": 2097152, "kindString": "Object literal", "flags": {"isConst": true}, "children": [{"id": 299, "name": "cacheControl", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 15, "character": 14}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"3600\""}, {"id": 300, "name": "contentType", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 16, "character": 13}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"text/plain;charset=UTF-8\""}, {"id": 301, "name": "upsert", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 17, "character": 8}], "type": {"type": "intrinsic", "name": "false"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [299, 300, 301]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 14, "character": 26}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 292, "name": "DEFAULT_SEARCH_OPTIONS", "kind": 2097152, "kindString": "Object literal", "flags": {"isConst": true}, "children": [{"id": 293, "name": "limit", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 6, "character": 7}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "100"}, {"id": 294, "name": "offset", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 7, "character": 8}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "0"}, {"id": 295, "name": "sortBy", "kind": 2097152, "kindString": "Object literal", "flags": {}, "children": [{"id": 296, "name": "column", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 9, "character": 10}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"name\""}, {"id": 297, "name": "order", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 10, "character": 9}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"asc\""}], "groups": [{"title": "Variables", "kind": 32, "children": [296, 297]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 8, "character": 8}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Variables", "kind": 32, "children": [293, 294]}, {"title": "Object literals", "kind": 2097152, "children": [295]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 5, "character": 28}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Classes", "kind": 128, "children": [170]}, {"title": "Object literals", "kind": 2097152, "children": [298, 292]}], "sources": [{"fileName": "lib/StorageFileApi.ts", "line": 1, "character": 0}]}, {"id": 3, "name": "\"lib/constants\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/constants.ts", "children": [{"id": 4, "name": "DEFAULT_HEADERS", "kind": 2097152, "kindString": "Object literal", "flags": {"isExported": true, "isConst": true}, "children": [{"id": 5, "name": "X-Client-Info", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 2, "character": 48}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "`storage-js/${version}`"}], "groups": [{"title": "Variables", "kind": 32, "children": [5]}], "sources": [{"fileName": "lib/constants.ts", "line": 2, "character": 28}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Object literals", "kind": 2097152, "children": [4]}], "sources": [{"fileName": "lib/constants.ts", "line": 1, "character": 0}]}, {"id": 6, "name": "\"lib/fetch\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/fetch.ts", "children": [{"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}, {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}, {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}, {"id": 16, "name": "RequestMethodType", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 14, "character": 29}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PUT"}, {"type": "stringLiteral", "value": "DELETE"}]}}, {"id": 17, "name": "_getErrorMessage", "kind": 64, "kindString": "Function", "flags": {"isConst": true}, "signatures": [{"id": 18, "name": "_getErrorMessage", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 19, "name": "err", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 16, "character": 22}]}, {"id": 24, "name": "_getRequestParams", "kind": 64, "kindString": "Function", "flags": {"isConst": true}, "signatures": [{"id": 25, "name": "_getRequestParams", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 26, "name": "method", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 16, "name": "RequestMethodType", "dereferenced": {"id": 16, "name": "RequestMethodType", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 14, "character": 29}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PUT"}, {"type": "stringLiteral", "value": "DELETE"}]}}}}, {"id": 27, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 7, "name": "FetchOptions", "dereferenced": {"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}}}, {"id": 28, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}, {"id": 29, "name": "body", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "object"}]}}], "type": {"type": "reflection", "declaration": {"id": 30, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": [{"id": 31, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 32, "name": "k", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "sources": [{"fileName": "lib/fetch.ts", "line": 31, "character": 23}]}, {"id": 33, "name": "_handleRequest", "kind": 64, "kindString": "Function", "flags": {}, "signatures": [{"id": 34, "name": "_handleRequest", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 35, "name": "fetcher", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}, {"id": 36, "name": "method", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 16, "name": "RequestMethodType", "dereferenced": {"id": 16, "name": "RequestMethodType", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 14, "character": 29}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PUT"}, {"type": "stringLiteral", "value": "DELETE"}]}}}}, {"id": 37, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 38, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 7, "name": "FetchOptions", "dereferenced": {"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}}}, {"id": 39, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}, {"id": 40, "name": "body", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "object"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "any"}], "name": "Promise"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 48, "character": 29}]}, {"id": 41, "name": "get", "kind": 64, "kindString": "Function", "flags": {"isExported": true}, "signatures": [{"id": 42, "name": "get", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 43, "name": "fetcher", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}, {"id": 44, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 45, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 7, "name": "FetchOptions", "dereferenced": {"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}}}, {"id": 46, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "any"}], "name": "Promise"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 68, "character": 25}]}, {"id": 20, "name": "handleError", "kind": 64, "kindString": "Function", "flags": {"isConst": true}, "signatures": [{"id": 21, "name": "handleError", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 22, "name": "error", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 23, "name": "reject", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "any"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 19, "character": 17}]}, {"id": 47, "name": "post", "kind": 64, "kindString": "Function", "flags": {"isExported": true}, "signatures": [{"id": 48, "name": "post", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 49, "name": "fetcher", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}, {"id": 50, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 51, "name": "body", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "object"}}, {"id": 52, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 7, "name": "FetchOptions", "dereferenced": {"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}}}, {"id": 53, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "any"}], "name": "Promise"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 77, "character": 26}]}, {"id": 54, "name": "put", "kind": 64, "kindString": "Function", "flags": {"isExported": true}, "signatures": [{"id": 55, "name": "put", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 56, "name": "fetcher", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}, {"id": 57, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 58, "name": "body", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "object"}}, {"id": 59, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 7, "name": "FetchOptions", "dereferenced": {"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}}}, {"id": 60, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "any"}], "name": "Promise"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 87, "character": 25}]}, {"id": 61, "name": "remove", "kind": 64, "kindString": "Function", "flags": {"isExported": true}, "signatures": [{"id": 62, "name": "remove", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 63, "name": "fetcher", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 15, "name": "<PERSON>tch", "dereferenced": {"id": 15, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}, {"id": 64, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 65, "name": "body", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "object"}}, {"id": 66, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 7, "name": "FetchOptions", "dereferenced": {"id": 7, "name": "FetchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 8, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 4, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 10, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 11, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 12, "name": "noResolveJson", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 7, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 12]}], "sources": [{"fileName": "lib/fetch.ts", "line": 3, "character": 29}]}}}, {"id": 67, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 13, "name": "FetchParameters", "dereferenced": {"id": 13, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 14, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/fetch.ts", "line": 11, "character": 8}], "type": {"type": "reference", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [14]}], "sources": [{"fileName": "lib/fetch.ts", "line": 10, "character": 32}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "any"}], "name": "Promise"}}], "sources": [{"fileName": "lib/fetch.ts", "line": 97, "character": 28}]}], "groups": [{"title": "Interfaces", "kind": 256, "children": [7, 13]}, {"title": "Type aliases", "kind": 4194304, "children": [15, 16]}, {"title": "Functions", "kind": 64, "children": [17, 24, 33, 41, 20, 47, 54, 61]}], "sources": [{"fileName": "lib/fetch.ts", "line": 1, "character": 0}]}, {"id": 68, "name": "\"lib/helpers\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/helpers.ts", "children": [{"id": 69, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "lib/helpers.ts", "line": 1, "character": 10}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}, {"id": 70, "name": "resolveFetch", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "signatures": [{"id": 71, "name": "resolveFetch", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 72, "name": "customFetch", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 69, "name": "<PERSON>tch", "dereferenced": {"id": 69, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "lib/helpers.ts", "line": 1, "character": 10}], "type": {"type": "query", "queryType": {"type": "reference", "name": "fetch"}}}}}], "type": {"type": "reference", "id": 69, "name": "<PERSON>tch"}}], "sources": [{"fileName": "lib/helpers.ts", "line": 3, "character": 25}]}], "groups": [{"title": "Type aliases", "kind": 4194304, "children": [69]}, {"title": "Functions", "kind": 64, "children": [70]}], "sources": [{"fileName": "lib/helpers.ts", "line": 1, "character": 0}]}, {"id": 302, "name": "\"lib/index\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/index.ts", "children": [{"id": 305, "name": "Bucket", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 74}, {"id": 311, "name": "DEFAULT_HEADERS", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 4}, {"id": 306, "name": "FileObject", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 81}, {"id": 308, "name": "FileOptions", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 95}, {"id": 310, "name": "<PERSON><PERSON><PERSON>", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 104}, {"id": 309, "name": "SearchOptions", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 99}, {"id": 307, "name": "SortBy", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 92}, {"id": 303, "name": "StorageBucketApi", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 107}, {"id": 304, "name": "StorageFileApi", "kind": 16777216, "kindString": "Reference", "flags": {"isExported": true}, "target": 170}], "groups": [{"title": "References", "kind": 16777216, "children": [305, 311, 306, 308, 310, 309, 307, 303, 304]}], "sources": [{"fileName": "lib/index.ts", "line": 1, "character": 0}]}, {"id": 73, "name": "\"lib/types\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/types.ts", "children": [{"id": 74, "name": "Bucket", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 78, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 5, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 75, "name": "id", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 2, "character": 4}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 76, "name": "name", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 3, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 77, "name": "owner", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 4, "character": 7}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 80, "name": "public", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 7, "character": 8}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 79, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 6, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [78, 75, 76, 77, 80, 79]}], "sources": [{"fileName": "lib/types.ts", "line": 1, "character": 23}]}, {"id": 81, "name": "FileObject", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 83, "name": "bucket_id", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 12, "character": 11}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 91, "name": "buckets", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 19, "character": 9}], "type": {"type": "reference", "id": 74, "name": "Bucket"}}, {"id": 87, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 16, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 85, "name": "id", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 14, "character": 4}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 88, "name": "last_accessed_at", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 17, "character": 18}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 89, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 18, "character": 10}], "type": {"type": "reflection", "declaration": {"id": 90, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 18, "character": 11}]}}}, {"id": 82, "name": "name", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 11, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 84, "name": "owner", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 13, "character": 7}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 86, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 15, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [83, 91, 87, 85, 88, 89, 82, 84, 86]}], "sources": [{"fileName": "lib/types.ts", "line": 10, "character": 27}]}, {"id": 95, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 96, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 28, "character": 14}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 97, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 29, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 98, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 30, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [96, 97, 98]}], "sources": [{"fileName": "lib/types.ts", "line": 27, "character": 28}]}, {"id": 104, "name": "<PERSON><PERSON><PERSON>", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 105, "name": "name", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 49, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [105]}], "sources": [{"fileName": "lib/types.ts", "line": 48, "character": 25}]}, {"id": 99, "name": "SearchOptions", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 100, "name": "limit", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The number of files you want to be returned."}, "sources": [{"fileName": "lib/types.ts", "line": 35, "character": 7}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 101, "name": "offset", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The starting position."}, "sources": [{"fileName": "lib/types.ts", "line": 38, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 103, "name": "search", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The search string to filter files by."}, "sources": [{"fileName": "lib/types.ts", "line": 44, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 102, "name": "sortBy", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "comment": {"shortText": "The column to sort by. Can be any column inside a FileObject."}, "sources": [{"fileName": "lib/types.ts", "line": 41, "character": 8}], "type": {"type": "reference", "id": 92, "name": "SortBy"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [100, 101, 103, 102]}], "sources": [{"fileName": "lib/types.ts", "line": 33, "character": 30}]}, {"id": 92, "name": "SortBy", "kind": 256, "kindString": "Interface", "flags": {"isExported": true}, "children": [{"id": 93, "name": "column", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 23, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 94, "name": "order", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 24, "character": 7}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [93, 94]}], "sources": [{"fileName": "lib/types.ts", "line": 22, "character": 23}]}], "groups": [{"title": "Interfaces", "kind": 256, "children": [74, 81, 95, 104, 99, 92]}], "sources": [{"fileName": "lib/types.ts", "line": 1, "character": 0}]}, {"id": 1, "name": "\"lib/version\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/storage-js/src/lib/version.ts", "children": [{"id": 2, "name": "version", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isConst": true}, "sources": [{"fileName": "lib/version.ts", "line": 2, "character": 20}], "type": {"type": "stringLiteral", "value": "0.0.0"}, "defaultValue": "\"0.0.0\""}], "groups": [{"title": "Variables", "kind": 32, "children": [2]}], "sources": [{"fileName": "lib/version.ts", "line": 1, "character": 0}]}], "groups": [{"title": "<PERSON><PERSON><PERSON>", "kind": 1, "children": [312, 378, 106, 169, 3, 6, 68, 302, 73, 1]}]}