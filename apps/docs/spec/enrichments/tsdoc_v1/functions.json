{"id": 0, "name": "@supabase/functions-js", "kind": 1, "kindString": "Project", "flags": {}, "originalName": "", "children": [{"id": 1, "name": "FunctionsClient", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 2, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 9, "character": 2}], "signatures": [{"id": 3, "name": "new FunctionsClient", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 4, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 5, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 6, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 8, "name": "customFetch", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/index.ts", "line": 16, "character": 6}], "type": {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 10, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 11, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}}, {"id": 12, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 7, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/index.ts", "line": 15, "character": 6}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 7]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 1, "name": "FunctionsClient"}}]}, {"id": 15, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/index.ts", "line": 7, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 16, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 17, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 18, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}}, {"id": 19, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 14, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/index.ts", "line": 6, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 13, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/index.ts", "line": 5, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 23, "name": "invoke", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 40, "character": 8}], "signatures": [{"id": 24, "name": "invoke", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Invokes a function"}, "typeParameter": [{"id": 25, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}, "default": {"type": "intrinsic", "name": "any"}}], "parameters": [{"id": 26, "name": "functionName", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "the name of the function to invoke"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 27, "name": "invokeOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"shortText": "object with the following properties\n`headers`: object representing the headers to send with the request\n`body`: the body of the request\n`responseType`: how the response should be parsed. The default is `json`\n"}, "type": {"type": "reference", "name": "FunctionInvokeOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 28, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 29, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 43, "character": 15}], "type": {"type": "reference", "id": 25, "name": "T"}}, {"id": 30, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 43, "character": 24}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [29, 30]}]}}, {"type": "reflection", "declaration": {"id": 31, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 32, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 43, "character": 42}], "type": {"type": "literal", "value": null}}, {"id": 33, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 43, "character": 54}], "type": {"type": "reference", "qualifiedName": "Error", "package": "typescript", "name": "Error"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [32, 33]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 20, "name": "setAuth", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 28, "character": 2}], "signatures": [{"id": 21, "name": "setAuth", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates the authorization header", "tags": [{"tag": "params", "text": "token - the new jwt token sent in the authorisation header\n"}]}, "parameters": [{"id": 22, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [2]}, {"title": "Properties", "kind": 1024, "children": [15, 14, 13]}, {"title": "Methods", "kind": 2048, "children": [23, 20]}], "sources": [{"fileName": "src/index.ts", "line": 4, "character": 13}]}], "groups": [{"title": "Classes", "kind": 128, "children": [1]}], "sources": [{"fileName": "src/index.ts", "line": 1, "character": 0}]}