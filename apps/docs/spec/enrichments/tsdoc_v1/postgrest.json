{"id": 0, "name": "@supabase/postgrest-js", "kind": 0, "flags": {}, "originalName": "", "children": [{"id": 502, "name": "\"PostgrestClient\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/PostgrestClient.ts", "children": [{"id": 503, "name": "PostgrestClient", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 512, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "comment": {"shortText": "Creates a PostgREST client."}, "signatures": [{"id": 513, "name": "new PostgrestClient", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "comment": {"shortText": "Creates a PostgREST client."}, "parameters": [{"id": 514, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "URL of the PostgREST endpoint."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 515, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 516, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 522, "name": "fetch", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 26, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reference", "id": 375, "name": "fetch"}]}}, {"id": 517, "name": "headers", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Custom headers."}, "sources": [{"fileName": "PostgrestClient.ts", "line": 24, "character": 13}], "type": {"type": "reflection", "declaration": {"id": 518, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 519, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 520, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}}, {"id": 521, "name": "schema", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Postgres schema to switch to.\n"}, "sources": [{"fileName": "PostgrestClient.ts", "line": 25, "character": 12}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 523, "name": "throwOnError", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 27, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [522, 517, 521, 523]}], "sources": [{"fileName": "PostgrestClient.ts", "line": 22, "character": 16}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 503, "name": "PostgrestClient"}}], "sources": [{"fileName": "PostgrestClient.ts", "line": 12, "character": 30}]}, {"id": 510, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 11, "character": 7}], "type": {"type": "reference", "id": 59, "name": "<PERSON>tch"}}, {"id": 505, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 9, "character": 9}], "type": {"type": "reflection", "declaration": {"id": 506, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 507, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 508, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "PostgrestClient.ts", "line": 9, "character": 10}]}}}, {"id": 509, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 10, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 511, "name": "shouldThrowOnError", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 12, "character": 20}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}, {"id": 504, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "PostgrestClient.ts", "line": 8, "character": 5}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 524, "name": "auth", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 525, "name": "auth", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Authenticates the request with JWT."}, "parameters": [{"id": 526, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The JWT token to use.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "PostgrestClient.ts", "line": 47, "character": 6}]}, {"id": 527, "name": "from", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 528, "name": "from", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Perform a table operation."}, "typeParameter": [{"id": 529, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 530, "name": "table", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The table name to operate on.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 363, "typeArguments": [{"type": "typeParameter", "name": "T", "default": {"type": "intrinsic", "name": "any"}}], "name": "PostgrestQueryBuilder"}}], "sources": [{"fileName": "PostgrestClient.ts", "line": 57, "character": 6}]}, {"id": 531, "name": "rpc", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 532, "name": "rpc", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Perform a function call."}, "typeParameter": [{"id": 533, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 534, "name": "fn", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The function name to call."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 535, "name": "params", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "The parameters to pass to the function call."}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "object"}]}}, {"id": 536, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 537, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 539, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Count algorithm to use to count rows in a table.\n"}, "sources": [{"fileName": "PostgrestClient.ts", "line": 80, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}, "defaultValue": "null"}, {"id": 538, "name": "head", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "When set to true, no data will be returned."}, "sources": [{"fileName": "PostgrestClient.ts", "line": 79, "character": 10}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [539, 538]}], "sources": [{"fileName": "PostgrestClient.ts", "line": 77, "character": 20}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T", "default": {"type": "intrinsic", "name": "any"}}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "PostgrestClient.ts", "line": 75, "character": 5}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [512]}, {"title": "Properties", "kind": 1024, "children": [510, 505, 509, 511, 504]}, {"title": "Methods", "kind": 2048, "children": [524, 527, 531]}], "sources": [{"fileName": "PostgrestClient.ts", "line": 7, "character": 36}]}], "groups": [{"title": "Classes", "kind": 128, "children": [503]}], "sources": [{"fileName": "PostgrestClient.ts", "line": 1, "character": 0}]}, {"id": 540, "name": "\"index\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/index.ts", "sources": [{"fileName": "index.ts", "line": 1, "character": 0}]}, {"id": 149, "name": "\"lib/PostgrestFilterBuilder\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/PostgrestFilterBuilder.ts", "children": [{"id": 150, "name": "PostgrestFilterBuilder", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "typeParameter": [{"id": 151, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "children": [{"id": 343, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 344, "name": "new PostgrestFilterBuilder", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 345, "name": "builder", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 27, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestBuilder"}}], "type": {"type": "reference", "id": 150, "name": "PostgrestFilterBuilder"}, "inheritedFrom": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}], "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 31}], "inheritedFrom": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}, {"id": 237, "name": "adj", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `rangeAdjacent()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 328, "character": 5}], "type": {"type": "reference", "id": 233, "name": "rangeAdja<PERSON>"}, "defaultValue": "this.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": 342, "name": "allowEmpty", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 22}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 40, "name": "PostgrestBuilder.allowEmpty"}}, {"id": 338, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 56, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}, "inheritedFrom": {"type": "reference", "id": 36, "name": "PostgrestBuilder.body"}}, {"id": 212, "name": "cd", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `containedBy()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 253, "character": 4}], "type": {"type": "reference", "id": 208, "name": "containedBy"}, "defaultValue": "this.containedBy"}, {"id": 207, "name": "cs", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `contains()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 229, "character": 4}], "type": {"type": "reference", "id": 203, "name": "contains"}, "defaultValue": "this.contains"}, {"id": 341, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 59, "character": 17}], "type": {"type": "reference", "id": 59, "name": "<PERSON>tch"}, "inheritedFrom": {"type": "reference", "id": 39, "name": "PostgrestBuilder.fetch"}}, {"id": 333, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 334, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 335, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 336, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 21}]}}, "inheritedFrom": {"type": "reference", "id": 31, "name": "PostgrestBuilder.headers"}}, {"id": 331, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 52, "character": 18}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "HEAD"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PATCH"}, {"type": "stringLiteral", "value": "DELETE"}]}, "inheritedFrom": {"type": "reference", "id": 29, "name": "PostgrestBuilder.method"}}, {"id": 227, "name": "nxl", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `rangeGte()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 298, "character": 5}], "type": {"type": "reference", "id": 223, "name": "rangeGte"}, "defaultValue": "this.rangeGte"}, {"id": 232, "name": "nxr", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `rangeLte()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 313, "character": 5}], "type": {"type": "reference", "id": 228, "name": "rangeLte"}, "defaultValue": "this.rangeLte"}, {"id": 242, "name": "ov", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `overlaps()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 349, "character": 4}], "type": {"type": "reference", "id": 238, "name": "overlaps"}, "defaultValue": "this.overlaps"}, {"id": 337, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 55, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}, "inheritedFrom": {"type": "reference", "id": 35, "name": "PostgrestBuilder.schema"}}, {"id": 339, "name": "shouldThrowOnError", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 57, "character": 30}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 37, "name": "PostgrestBuilder.shouldThrowOnError"}}, {"id": 340, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 58, "character": 18}], "type": {"type": "reference", "name": "AbortSignal"}, "inheritedFrom": {"type": "reference", "id": 38, "name": "PostgrestBuilder.signal"}}, {"id": 217, "name": "sl", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `rangeLt()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 268, "character": 4}], "type": {"type": "reference", "id": 213, "name": "rangeLt"}, "defaultValue": "this.rangeLt"}, {"id": 222, "name": "sr", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `rangeGt()` instead."}]}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 283, "character": 4}], "type": {"type": "reference", "id": 218, "name": "rangeGt"}, "defaultValue": "this.rangeGt"}, {"id": 332, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 53, "character": 15}], "type": {"type": "reference", "name": "URL"}, "inheritedFrom": {"type": "reference", "id": 30, "name": "PostgrestBuilder.url"}}, {"id": 311, "name": "abortSignal", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 312, "name": "abortSignal", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Sets the AbortSignal for the fetch request."}, "parameters": [{"id": 313, "name": "signal", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "AbortSignal"}}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 99, "name": "PostgrestTransformBuilder.abortSignal"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 91, "character": 13}], "inheritedFrom": {"type": "reference", "id": 99, "name": "PostgrestTransformBuilder.abortSignal"}}, {"id": 208, "name": "containedBy", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 209, "name": "containedBy", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose json, array, or range value on the stated `column` is\ncontained by the specified `value`."}, "parameters": [{"id": 210, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 211, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "array", "elementType": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}, {"type": "intrinsic", "name": "object"}]}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 238, "character": 13}]}, {"id": 203, "name": "contains", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 204, "name": "contains", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose json, array, or range value on the stated `column`\ncontains the values specified in `value`."}, "parameters": [{"id": 205, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 206, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "array", "elementType": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}, {"type": "intrinsic", "name": "object"}]}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 213, "character": 10}]}, {"id": 318, "name": "csv", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 319, "name": "csv", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Set the response type to CSV."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 68, "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "PostgrestSingleResponse"}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 106, "name": "PostgrestTransformBuilder.csv"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 119, "character": 5}], "inheritedFrom": {"type": "reference", "id": 106, "name": "PostgrestTransformBuilder.csv"}}, {"id": 163, "name": "eq", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 164, "name": "eq", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` exactly matches the\nspecified `value`."}, "parameters": [{"id": 165, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 166, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 85, "character": 4}]}, {"id": 322, "name": "explain", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 323, "name": "explain", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Obtains the EXPLAIN plan for this request."}, "parameters": [{"id": 324, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__0", "type": {"type": "reflection", "declaration": {"id": 325, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 326, "name": "analyze", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, the query will be executed and the actual run time will be displayed."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 142, "character": 11}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 329, "name": "buffers", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, include information on buffer usage."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 145, "character": 11}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 328, "name": "settings", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, include information on configuration parameters that affect query planning."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 144, "character": 12}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 327, "name": "verbose", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, the query identifier will be displayed and the result will include the output columns of the query."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 143, "character": 11}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 330, "name": "wal", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, include information on WAL record generation\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 146, "character": 7}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [326, 329, 328, 327, 330]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 141, "character": 10}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "name": "Record"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 110, "name": "PostgrestTransformBuilder.explain"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 141, "character": 9}], "inheritedFrom": {"type": "reference", "id": 110, "name": "PostgrestTransformBuilder.explain"}}, {"id": 279, "name": "filter", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 280, "name": "filter", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose `column` satisfies the filter."}, "parameters": [{"id": 281, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 282, "name": "operator", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The operator to filter with."}, "type": {"type": "reference", "id": 361, "name": "FilterOperator"}}, {"id": 283, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 452, "character": 8}]}, {"id": 251, "name": "fts", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 252, "name": "fts", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose tsvector value on the stated `column` matches\nto_tsquery(`query`).", "tags": [{"tag": "deprecated", "text": "Use `textSearch()` instead.\n"}]}, "parameters": [{"id": 253, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 254, "name": "query", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The Postgres tsquery string to filter with."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 255, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 256, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 257, "name": "config", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The text search configuration to use.\n"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 391, "character": 46}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [257]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 391, "character": 37}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 391, "character": 5}]}, {"id": 320, "name": "g<PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 321, "name": "g<PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Set the response type to GeoJSON."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 68, "typeArguments": [{"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "name": "Record"}], "name": "PostgrestSingleResponse"}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 108, "name": "PostgrestTransformBuilder.geojson"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 127, "character": 9}], "inheritedFrom": {"type": "reference", "id": 108, "name": "PostgrestTransformBuilder.geojson"}}, {"id": 171, "name": "gt", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 172, "name": "gt", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` is greater than the\nspecified `value`."}, "parameters": [{"id": 173, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 174, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 109, "character": 4}]}, {"id": 175, "name": "gte", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 176, "name": "gte", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` is greater than or\nequal to the specified `value`."}, "parameters": [{"id": 177, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 178, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 121, "character": 5}]}, {"id": 191, "name": "ilike", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 192, "name": "ilike", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value in the stated `column` matches the supplied\n`pattern` (case insensitive)."}, "parameters": [{"id": 193, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 194, "name": "pattern", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The pattern to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 169, "character": 7}]}, {"id": 199, "name": "in", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 200, "name": "in", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` is found on the\nspecified `values`."}, "parameters": [{"id": 201, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 202, "name": "values", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The values to filter with.\n"}, "type": {"type": "array", "elementType": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 193, "character": 4}]}, {"id": 195, "name": "is", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 196, "name": "is", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "A check for exact equality (null, true, false), finds all rows whose\nvalue on the stated `column` exactly match the specified `value`."}, "parameters": [{"id": 197, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 198, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "boolean"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 181, "character": 4}]}, {"id": 187, "name": "like", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 188, "name": "like", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value in the stated `column` matches the supplied\n`pattern` (case sensitive)."}, "parameters": [{"id": 189, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 190, "name": "pattern", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The pattern to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 157, "character": 6}]}, {"id": 298, "name": "limit", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 299, "name": "limit", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Limits the result with the specified `count`."}, "parameters": [{"id": 300, "name": "count", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The maximum no. of rows to limit to."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 301, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 302, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 303, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (for foreign columns).\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 66, "character": 37}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [303]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 66, "character": 22}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 86, "name": "PostgrestTransformBuilder.limit"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 66, "character": 7}], "inheritedFrom": {"type": "reference", "id": 86, "name": "PostgrestTransformBuilder.limit"}}, {"id": 179, "name": "lt", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 180, "name": "lt", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` is less than the\nspecified `value`."}, "parameters": [{"id": 181, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 182, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 133, "character": 4}]}, {"id": 183, "name": "lte", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 184, "name": "lte", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` is less than or equal\nto the specified `value`."}, "parameters": [{"id": 185, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 186, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 145, "character": 5}]}, {"id": 284, "name": "match", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 285, "name": "match", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose columns match the specified `query` object."}, "parameters": [{"id": 286, "name": "query", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The object to filter with, with column names as keys mapped\n              to their filter values.\n"}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "name": "Record"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 463, "character": 7}]}, {"id": 316, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 317, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves at most one row from the result. Result must be at most one row\n(e.g. using `eq` on a UNIQUE column), otherwise this will result in an\nerror."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 70, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestMaybeSingleResponse"}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 104, "name": "PostgrestTransformBuilder.maybeSingle"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 110, "character": 13}], "inheritedFrom": {"type": "reference", "id": 104, "name": "PostgrestTransformBuilder.maybeSingle"}}, {"id": 167, "name": "neq", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 168, "name": "neq", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose value on the stated `column` doesn't match the\nspecified `value`."}, "parameters": [{"id": 169, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 170, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 97, "character": 5}]}, {"id": 152, "name": "not", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 153, "name": "not", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows which doesn't satisfy the filter."}, "parameters": [{"id": 154, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 155, "name": "operator", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The operator to filter with."}, "type": {"type": "reference", "id": 361, "name": "FilterOperator"}}, {"id": 156, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 61, "character": 5}]}, {"id": 157, "name": "or", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 158, "name": "or", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows satisfying at least one of the filters."}, "parameters": [{"id": 159, "name": "filters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The filters to use, separated by commas."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 160, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 161, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 162, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (if `column` is a foreign column).\n"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 72, "character": 36}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [162]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 72, "character": 21}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 72, "character": 4}]}, {"id": 290, "name": "order", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 291, "name": "order", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Orders the result with the specified `column`."}, "parameters": [{"id": 292, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to order on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 293, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 294, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 295, "name": "ascending", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, the result will be in ascending order."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 43, "character": 15}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "true"}, {"id": 297, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (if `column` is a foreign column).\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 45, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 296, "name": "nullsFirst", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, `null`s appear first."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 44, "character": 16}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [295, 297, 296]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 41, "character": 20}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 78, "name": "PostgrestTransformBuilder.order"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 40, "character": 7}], "inheritedFrom": {"type": "reference", "id": 78, "name": "PostgrestTransformBuilder.order"}}, {"id": 238, "name": "overlaps", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 239, "name": "overlaps", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose array or range value on the stated `column` overlaps\n(has a value in common) with the specified `value`."}, "parameters": [{"id": 240, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 241, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The value to filter with.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "array", "elementType": {"type": "indexedAccess", "indexType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}, "objectType": {"type": "typeParameter", "name": "T"}}}]}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 337, "character": 10}]}, {"id": 265, "name": "phfts", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 266, "name": "phfts", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose tsvector value on the stated `column` matches\nphraseto_tsquery(`query`).", "tags": [{"tag": "deprecated", "text": "Use `textSearch()` with `type: 'phrase'` instead.\n"}]}, "parameters": [{"id": 267, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 268, "name": "query", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The Postgres tsquery string to filter with."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 269, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 270, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 271, "name": "config", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The text search configuration to use.\n"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 423, "character": 48}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [271]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 423, "character": 39}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 423, "character": 7}]}, {"id": 258, "name": "plfts", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 259, "name": "plfts", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose tsvector value on the stated `column` matches\nplainto_tsquery(`query`).", "tags": [{"tag": "deprecated", "text": "Use `textSearch()` with `type: 'plain'` instead.\n"}]}, "parameters": [{"id": 260, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 261, "name": "query", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The Postgres tsquery string to filter with."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 262, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 263, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 264, "name": "config", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The text search configuration to use.\n"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 407, "character": 48}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [264]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 407, "character": 39}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 407, "character": 7}]}, {"id": 304, "name": "range", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 305, "name": "range", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Limits the result to rows within the specified range, inclusive."}, "parameters": [{"id": 306, "name": "from", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The starting index from which to limit the result, inclusive."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 307, "name": "to", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The last index to which to limit the result, inclusive."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 308, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 309, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 310, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (for foreign columns).\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 79, "character": 48}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [310]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 79, "character": 33}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 92, "name": "PostgrestTransformBuilder.range"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 79, "character": 7}], "inheritedFrom": {"type": "reference", "id": 92, "name": "PostgrestTransformBuilder.range"}}, {"id": 233, "name": "rangeAdja<PERSON>", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 234, "name": "rangeAdja<PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose range value on the stated `column` is adjacent to\nthe specified `range`."}, "parameters": [{"id": 235, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 236, "name": "range", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The range to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 322, "character": 15}]}, {"id": 218, "name": "rangeGt", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 219, "name": "rangeGt", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose range value on the stated `column` is strictly to\nthe right of the specified `range`."}, "parameters": [{"id": 220, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 221, "name": "range", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The range to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 277, "character": 9}]}, {"id": 223, "name": "rangeGte", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 224, "name": "rangeGte", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose range value on the stated `column` does not extend\nto the left of the specified `range`."}, "parameters": [{"id": 225, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 226, "name": "range", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The range to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 292, "character": 10}]}, {"id": 213, "name": "rangeLt", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 214, "name": "rangeLt", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose range value on the stated `column` is strictly to the\nleft of the specified `range`."}, "parameters": [{"id": 215, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 216, "name": "range", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The range to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 262, "character": 9}]}, {"id": 228, "name": "rangeLte", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 229, "name": "rangeLte", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose range value on the stated `column` does not extend\nto the right of the specified `range`."}, "parameters": [{"id": 230, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 231, "name": "range", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The range to filter with.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 307, "character": 10}]}, {"id": 287, "name": "select", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 288, "name": "select", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs vertical filtering with SELECT."}, "parameters": [{"id": 289, "name": "columns", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The columns to retrieve, separated by commas.\n"}, "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"*\""}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 75, "name": "PostgrestTransformBuilder.select"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 13, "character": 8}], "inheritedFrom": {"type": "reference", "id": 75, "name": "PostgrestTransformBuilder.select"}}, {"id": 314, "name": "single", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 315, "name": "single", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves only one row from the result. Result must be one row (e.g. using\n`limit`), otherwise this will result in an error."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 68, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestSingleResponse"}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 102, "name": "PostgrestTransformBuilder.single"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 100, "character": 8}], "inheritedFrom": {"type": "reference", "id": 102, "name": "PostgrestTransformBuilder.single"}}, {"id": 243, "name": "textSearch", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 244, "name": "textSearch", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose text or tsvector value on the stated `column` matches\nthe tsquery in `query`."}, "parameters": [{"id": 245, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 246, "name": "query", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The Postgres tsquery string to filter with."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 247, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 248, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 249, "name": "config", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The text search configuration to use."}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 364, "character": 12}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 250, "name": "type", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The type of tsquery conversion to use on `query`.\n"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 365, "character": 10}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "plain"}, {"type": "stringLiteral", "value": "phrase"}, {"type": "stringLiteral", "value": "websearch"}]}, "defaultValue": "null"}], "groups": [{"title": "Variables", "kind": 32, "children": [249, 250]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 362, "character": 18}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 360, "character": 12}]}, {"id": 349, "name": "then", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 350, "name": "then", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "typeParameter": [{"id": 351, "name": "TResult1", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}, {"id": 352, "name": "TResult2", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 353, "name": "onfulfilled", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 354, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 355, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 356, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 93, "character": 9}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 357, "name": "onrejected", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 358, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 359, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 360, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 96, "character": 18}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}]}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}], "sources": [{"fileName": "lib/types.ts", "line": 91, "character": 6}], "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}, {"id": 346, "name": "throwOnError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 347, "name": "throwOnError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "If there's an error with the query, throwOnError will reject the promise by\nthrowing the error instead of returning it as part of a successful response.", "text": "{@link https://github.com/supabase/supabase-js/issues/92}\n"}, "parameters": [{"id": 348, "name": "throwOnError", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}], "sources": [{"fileName": "lib/types.ts", "line": 83, "character": 14}], "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}, {"id": 272, "name": "wfts", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 273, "name": "wfts", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Finds all rows whose tsvector value on the stated `column` matches\nwebsearch_to_tsquery(`query`).", "tags": [{"tag": "deprecated", "text": "Use `textSearch()` with `type: 'websearch'` instead.\n"}]}, "parameters": [{"id": 274, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to filter on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 275, "name": "query", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The Postgres tsquery string to filter with."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 276, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 277, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 278, "name": "config", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The text search configuration to use.\n"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 439, "character": 47}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [278]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 439, "character": 38}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 439, "character": 6}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [343]}, {"title": "Properties", "kind": 1024, "children": [237, 342, 338, 212, 207, 341, 333, 331, 227, 232, 242, 337, 339, 340, 217, 222, 332]}, {"title": "Methods", "kind": 2048, "children": [311, 208, 203, 318, 163, 322, 279, 251, 320, 171, 175, 191, 199, 195, 187, 298, 179, 183, 284, 316, 167, 152, 157, 290, 238, 265, 258, 304, 233, 218, 223, 213, 228, 287, 314, 243, 349, 346, 272]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 53, "character": 43}], "extendedTypes": [{"type": "reference", "id": 73, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestTransformBuilder"}], "implementedTypes": [{"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}]}, {"id": 361, "name": "FilterOperator", "kind": 4194304, "kindString": "Type alias", "flags": {}, "comment": {"shortText": "Filters"}, "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 7, "character": 19}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "eq"}, {"type": "stringLiteral", "value": "neq"}, {"type": "stringLiteral", "value": "gt"}, {"type": "stringLiteral", "value": "gte"}, {"type": "stringLiteral", "value": "lt"}, {"type": "stringLiteral", "value": "lte"}, {"type": "stringLiteral", "value": "like"}, {"type": "stringLiteral", "value": "ilike"}, {"type": "stringLiteral", "value": "is"}, {"type": "stringLiteral", "value": "in"}, {"type": "stringLiteral", "value": "cs"}, {"type": "stringLiteral", "value": "cd"}, {"type": "stringLiteral", "value": "sl"}, {"type": "stringLiteral", "value": "sr"}, {"type": "stringLiteral", "value": "nxl"}, {"type": "stringLiteral", "value": "nxr"}, {"type": "stringLiteral", "value": "adj"}, {"type": "stringLiteral", "value": "ov"}, {"type": "stringLiteral", "value": "fts"}, {"type": "stringLiteral", "value": "plfts"}, {"type": "stringLiteral", "value": "phfts"}, {"type": "stringLiteral", "value": "wfts"}, {"type": "stringLiteral", "value": "not.eq"}, {"type": "stringLiteral", "value": "not.neq"}, {"type": "stringLiteral", "value": "not.gt"}, {"type": "stringLiteral", "value": "not.gte"}, {"type": "stringLiteral", "value": "not.lt"}, {"type": "stringLiteral", "value": "not.lte"}, {"type": "stringLiteral", "value": "not.like"}, {"type": "stringLiteral", "value": "not.ilike"}, {"type": "stringLiteral", "value": "not.is"}, {"type": "stringLiteral", "value": "not.in"}, {"type": "stringLiteral", "value": "not.cs"}, {"type": "stringLiteral", "value": "not.cd"}, {"type": "stringLiteral", "value": "not.sl"}, {"type": "stringLiteral", "value": "not.sr"}, {"type": "stringLiteral", "value": "not.nxl"}, {"type": "stringLiteral", "value": "not.nxr"}, {"type": "stringLiteral", "value": "not.adj"}, {"type": "stringLiteral", "value": "not.ov"}, {"type": "stringLiteral", "value": "not.fts"}, {"type": "stringLiteral", "value": "not.plfts"}, {"type": "stringLiteral", "value": "not.phfts"}, {"type": "stringLiteral", "value": "not.wfts"}]}}], "groups": [{"title": "Classes", "kind": 128, "children": [150]}, {"title": "Type aliases", "kind": 4194304, "children": [361]}], "sources": [{"fileName": "lib/PostgrestFilterBuilder.ts", "line": 1, "character": 0}]}, {"id": 362, "name": "\"lib/PostgrestQueryBuilder\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/PostgrestQueryBuilder.ts", "children": [{"id": 363, "name": "PostgrestQueryBuilder", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "typeParameter": [{"id": 364, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "children": [{"id": 365, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 366, "name": "new PostgrestQueryBuilder", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 367, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 368, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 369, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 375, "name": "fetch", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 10, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reference", "id": 375, "name": "fetch"}]}}, {"id": 370, "name": "headers", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 8, "character": 13}], "type": {"type": "reflection", "declaration": {"id": 371, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 372, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 373, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}}, {"id": 374, "name": "schema", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 9, "character": 12}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 376, "name": "shouldThrowOnError", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 11, "character": 24}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [375, 370, 374, 376]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 6, "character": 16}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 363, "name": "PostgrestQueryBuilder"}, "overwrites": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 4, "character": 75}], "overwrites": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}, {"id": 432, "name": "allowEmpty", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 22}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 40, "name": "PostgrestBuilder.allowEmpty"}}, {"id": 428, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 56, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}, "inheritedFrom": {"type": "reference", "id": 36, "name": "PostgrestBuilder.body"}}, {"id": 431, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 59, "character": 17}], "type": {"type": "reference", "id": 59, "name": "<PERSON>tch"}, "inheritedFrom": {"type": "reference", "id": 39, "name": "PostgrestBuilder.fetch"}}, {"id": 423, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 424, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 425, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 426, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 21}]}}, "inheritedFrom": {"type": "reference", "id": 31, "name": "PostgrestBuilder.headers"}}, {"id": 421, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 52, "character": 18}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "HEAD"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PATCH"}, {"type": "stringLiteral", "value": "DELETE"}]}, "inheritedFrom": {"type": "reference", "id": 29, "name": "PostgrestBuilder.method"}}, {"id": 427, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 55, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}, "inheritedFrom": {"type": "reference", "id": 35, "name": "PostgrestBuilder.schema"}}, {"id": 429, "name": "shouldThrowOnError", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 57, "character": 30}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 37, "name": "PostgrestBuilder.shouldThrowOnError"}}, {"id": 430, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 58, "character": 18}], "type": {"type": "reference", "name": "AbortSignal"}, "inheritedFrom": {"type": "reference", "id": 38, "name": "PostgrestBuilder.signal"}}, {"id": 422, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 53, "character": 15}], "type": {"type": "reference", "name": "URL"}, "inheritedFrom": {"type": "reference", "id": 30, "name": "PostgrestBuilder.url"}}, {"id": 415, "name": "delete", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 416, "name": "delete", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs a DELETE on the table."}, "parameters": [{"id": 417, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__0", "type": {"type": "reflection", "declaration": {"id": 418, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 420, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Count algorithm to use to count rows in a table.\n"}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 214, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}, "defaultValue": "null"}, {"id": 419, "name": "returning", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, return the deleted row(s) in the response."}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 213, "character": 13}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "minimal"}, {"type": "stringLiteral", "value": "representation"}]}, "defaultValue": "\"representation\""}], "groups": [{"title": "Variables", "kind": 32, "children": [420, 419]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 212, "character": 9}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 212, "character": 8}]}, {"id": 384, "name": "insert", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 385, "name": "insert", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs an INSERT into the table."}, "parameters": [{"id": 386, "name": "values", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The values to insert."}, "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}}, {"id": 387, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 388, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 390, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 78, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}}, {"id": 389, "name": "returning", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 77, "character": 15}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "minimal"}, {"type": "stringLiteral", "value": "representation"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [390, 389]}]}}]}}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}, {"id": 391, "name": "insert", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `upsert()` instead.\n"}]}, "parameters": [{"id": 392, "name": "values", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}}, {"id": 393, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 394, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 398, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 90, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}}, {"id": 396, "name": "onConflict", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 88, "character": 16}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 397, "name": "returning", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 89, "character": 15}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "minimal"}, {"type": "stringLiteral", "value": "representation"}]}}, {"id": 395, "name": "upsert", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 87, "character": 12}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [398, 396, 397, 395]}]}}]}}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 74, "character": 8}, {"fileName": "lib/PostgrestQueryBuilder.ts", "line": 84, "character": 8}, {"fileName": "lib/PostgrestQueryBuilder.ts", "line": 93, "character": 8}]}, {"id": 377, "name": "select", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 378, "name": "select", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs vertical filtering with SELECT."}, "parameters": [{"id": 379, "name": "columns", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The columns to retrieve, separated by commas."}, "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"*\""}, {"id": 380, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 381, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 383, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Count algorithm to use to count rows in a table.\n"}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 36, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}, "defaultValue": "null"}, {"id": 382, "name": "head", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "When set to true, select will void data."}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 35, "character": 10}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [383, 382]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 33, "character": 18}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 32, "character": 8}]}, {"id": 436, "name": "then", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 437, "name": "then", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "typeParameter": [{"id": 438, "name": "TResult1", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}, {"id": 439, "name": "TResult2", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 440, "name": "onfulfilled", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 441, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 442, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 443, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 93, "character": 9}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 444, "name": "onrejected", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 445, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 446, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 447, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 96, "character": 18}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}]}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}], "sources": [{"fileName": "lib/types.ts", "line": 91, "character": 6}], "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}, {"id": 433, "name": "throwOnError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 434, "name": "throwOnError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "If there's an error with the query, throwOnError will reject the promise by\nthrowing the error instead of returning it as part of a successful response.", "text": "{@link https://github.com/supabase/supabase-js/issues/92}\n"}, "parameters": [{"id": 435, "name": "throwOnError", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}], "sources": [{"fileName": "lib/types.ts", "line": 83, "character": 14}], "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}, {"id": 408, "name": "update", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 409, "name": "update", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs an UPDATE on the table."}, "parameters": [{"id": 410, "name": "values", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The values to update."}, "type": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}, {"id": 411, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 412, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 414, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Count algorithm to use to count rows in a table.\n"}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 187, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}, "defaultValue": "null"}, {"id": 413, "name": "returning", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "By default the updated record is returned. Set this to 'minimal' if you don't need this value."}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 186, "character": 15}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "minimal"}, {"type": "stringLiteral", "value": "representation"}]}, "defaultValue": "\"representation\""}], "groups": [{"title": "Variables", "kind": 32, "children": [414, 413]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 184, "character": 23}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 183, "character": 8}]}, {"id": 399, "name": "upsert", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 400, "name": "upsert", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs an UPSERT into the table."}, "parameters": [{"id": 401, "name": "values", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The values to insert."}, "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}}, {"id": 402, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 403, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 406, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Count algorithm to use to count rows in a table."}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 147, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}, "defaultValue": "null"}, {"id": 407, "name": "ignoreDuplicates", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "Specifies if duplicate rows should be ignored and not inserted.\n"}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 148, "character": 22}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 404, "name": "onConflict", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "By specifying the `on_conflict` query parameter, you can make UPSERT work on a column(s) that has a UNIQUE constraint."}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 145, "character": 16}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 405, "name": "returning", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "By default the new record is returned. Set this to 'minimal' if you don't need this value."}, "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 146, "character": 15}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "minimal"}, {"type": "stringLiteral", "value": "representation"}]}, "defaultValue": "\"representation\""}], "groups": [{"title": "Variables", "kind": 32, "children": [406, 407, 404, 405]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 143, "character": 38}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 142, "character": 8}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [365]}, {"title": "Properties", "kind": 1024, "children": [432, 428, 431, 423, 421, 427, 429, 430, 422]}, {"title": "Methods", "kind": 2048, "children": [415, 384, 377, 436, 433, 408, 399]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 4, "character": 42}], "extendedTypes": [{"type": "reference", "id": 27, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestBuilder"}], "implementedTypes": [{"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}]}], "groups": [{"title": "Classes", "kind": 128, "children": [363]}], "sources": [{"fileName": "lib/PostgrestQueryBuilder.ts", "line": 1, "character": 0}]}, {"id": 448, "name": "\"lib/PostgrestRpcBuilder\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/PostgrestRpcBuilder.ts", "children": [{"id": 449, "name": "PostgrestRpcBuilder", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "typeParameter": [{"id": 450, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "children": [{"id": 451, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 452, "name": "new PostgrestRpcBuilder", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 453, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 454, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 455, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 461, "name": "fetch", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 10, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reference", "id": 375, "name": "fetch"}]}}, {"id": 456, "name": "headers", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 8, "character": 13}], "type": {"type": "reflection", "declaration": {"id": 457, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 458, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 459, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}}, {"id": 460, "name": "schema", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 9, "character": 12}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 462, "name": "shouldThrowOnError", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 11, "character": 24}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [461, 456, 460, 462]}], "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 6, "character": 16}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 449, "name": "PostgrestRpcBuilder"}, "overwrites": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}], "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 4, "character": 73}], "overwrites": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}, {"id": 481, "name": "allowEmpty", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 22}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 40, "name": "PostgrestBuilder.allowEmpty"}}, {"id": 477, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 56, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}, "inheritedFrom": {"type": "reference", "id": 36, "name": "PostgrestBuilder.body"}}, {"id": 480, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 59, "character": 17}], "type": {"type": "reference", "id": 59, "name": "<PERSON>tch"}, "inheritedFrom": {"type": "reference", "id": 39, "name": "PostgrestBuilder.fetch"}}, {"id": 472, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 473, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 474, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 475, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 21}]}}, "inheritedFrom": {"type": "reference", "id": 31, "name": "PostgrestBuilder.headers"}}, {"id": 470, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 52, "character": 18}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "HEAD"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PATCH"}, {"type": "stringLiteral", "value": "DELETE"}]}, "inheritedFrom": {"type": "reference", "id": 29, "name": "PostgrestBuilder.method"}}, {"id": 476, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 55, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}, "inheritedFrom": {"type": "reference", "id": 35, "name": "PostgrestBuilder.schema"}}, {"id": 478, "name": "shouldThrowOnError", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 57, "character": 30}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 37, "name": "PostgrestBuilder.shouldThrowOnError"}}, {"id": 479, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 58, "character": 18}], "type": {"type": "reference", "name": "AbortSignal"}, "inheritedFrom": {"type": "reference", "id": 38, "name": "PostgrestBuilder.signal"}}, {"id": 471, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 53, "character": 15}], "type": {"type": "reference", "name": "URL"}, "inheritedFrom": {"type": "reference", "id": 30, "name": "PostgrestBuilder.url"}}, {"id": 463, "name": "rpc", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 464, "name": "rpc", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Perform a function call."}, "parameters": [{"id": 465, "name": "params", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "object"}]}}, {"id": 466, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 467, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 469, "name": "count", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 32, "character": 11}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "stringLiteral", "value": "exact"}, {"type": "stringLiteral", "value": "planned"}, {"type": "stringLiteral", "value": "estimated"}]}, "defaultValue": "null"}, {"id": 468, "name": "head", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 31, "character": 10}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [469, 468]}], "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 29, "character": 20}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 150, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestFilterBuilder"}}], "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 28, "character": 5}]}, {"id": 485, "name": "then", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 486, "name": "then", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "typeParameter": [{"id": 487, "name": "TResult1", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}, {"id": 488, "name": "TResult2", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 489, "name": "onfulfilled", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 490, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 491, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 492, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 93, "character": 9}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 493, "name": "onrejected", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 494, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 495, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 496, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 96, "character": 18}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}]}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}], "sources": [{"fileName": "lib/types.ts", "line": 91, "character": 6}], "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}, {"id": 482, "name": "throwOnError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 483, "name": "throwOnError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "If there's an error with the query, throwOnError will reject the promise by\nthrowing the error instead of returning it as part of a successful response.", "text": "{@link https://github.com/supabase/supabase-js/issues/92}\n"}, "parameters": [{"id": 484, "name": "throwOnError", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}], "sources": [{"fileName": "lib/types.ts", "line": 83, "character": 14}], "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [451]}, {"title": "Properties", "kind": 1024, "children": [481, 477, 480, 472, 470, 476, 478, 479, 471]}, {"title": "Methods", "kind": 2048, "children": [463, 485, 482]}], "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 4, "character": 40}], "extendedTypes": [{"type": "reference", "id": 27, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestBuilder"}], "implementedTypes": [{"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}]}], "groups": [{"title": "Classes", "kind": 128, "children": [449]}], "sources": [{"fileName": "lib/PostgrestRpcBuilder.ts", "line": 1, "character": 0}]}, {"id": 72, "name": "\"lib/PostgrestTransformBuilder\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/PostgrestTransformBuilder.ts", "children": [{"id": 73, "name": "PostgrestTransformBuilder", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "comment": {"shortText": "Post-filters (transforms)"}, "typeParameter": [{"id": 74, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "children": [{"id": 131, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 132, "name": "new PostgrestTransformBuilder", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 133, "name": "builder", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 27, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestBuilder"}}], "type": {"type": "reference", "id": 73, "name": "PostgrestTransformBuilder"}, "inheritedFrom": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}], "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 31}], "inheritedFrom": {"type": "reference", "id": 41, "name": "PostgrestBuilder.__constructor"}}, {"id": 130, "name": "allowEmpty", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 22}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 40, "name": "PostgrestBuilder.allowEmpty"}}, {"id": 126, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 56, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}, "inheritedFrom": {"type": "reference", "id": 36, "name": "PostgrestBuilder.body"}}, {"id": 129, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 59, "character": 17}], "type": {"type": "reference", "id": 59, "name": "<PERSON>tch"}, "inheritedFrom": {"type": "reference", "id": 39, "name": "PostgrestBuilder.fetch"}}, {"id": 121, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 122, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 123, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 124, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 21}]}}, "inheritedFrom": {"type": "reference", "id": 31, "name": "PostgrestBuilder.headers"}}, {"id": 119, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 52, "character": 18}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "HEAD"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PATCH"}, {"type": "stringLiteral", "value": "DELETE"}]}, "inheritedFrom": {"type": "reference", "id": 29, "name": "PostgrestBuilder.method"}}, {"id": 125, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 55, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}, "inheritedFrom": {"type": "reference", "id": 35, "name": "PostgrestBuilder.schema"}}, {"id": 127, "name": "shouldThrowOnError", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 57, "character": 30}], "type": {"type": "intrinsic", "name": "boolean"}, "inheritedFrom": {"type": "reference", "id": 37, "name": "PostgrestBuilder.shouldThrowOnError"}}, {"id": 128, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 58, "character": 18}], "type": {"type": "reference", "name": "AbortSignal"}, "inheritedFrom": {"type": "reference", "id": 38, "name": "PostgrestBuilder.signal"}}, {"id": 120, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 53, "character": 15}], "type": {"type": "reference", "name": "URL"}, "inheritedFrom": {"type": "reference", "id": 30, "name": "PostgrestBuilder.url"}}, {"id": 99, "name": "abortSignal", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 100, "name": "abortSignal", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Sets the AbortSignal for the fetch request."}, "parameters": [{"id": 101, "name": "signal", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "AbortSignal"}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 91, "character": 13}]}, {"id": 106, "name": "csv", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 107, "name": "csv", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Set the response type to CSV."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 68, "typeArguments": [{"type": "intrinsic", "name": "string"}], "name": "PostgrestSingleResponse"}], "name": "PromiseLike"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 119, "character": 5}]}, {"id": 110, "name": "explain", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 111, "name": "explain", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Obtains the EXPLAIN plan for this request."}, "parameters": [{"id": 112, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__0", "type": {"type": "reflection", "declaration": {"id": 113, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 114, "name": "analyze", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, the query will be executed and the actual run time will be displayed."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 142, "character": 11}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 117, "name": "buffers", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, include information on buffer usage."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 145, "character": 11}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 116, "name": "settings", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, include information on configuration parameters that affect query planning."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 144, "character": 12}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 115, "name": "verbose", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, the query identifier will be displayed and the result will include the output columns of the query."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 143, "character": 11}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 118, "name": "wal", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, include information on WAL record generation\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 146, "character": 7}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [114, 117, 116, 115, 118]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 141, "character": 10}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "name": "Record"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 141, "character": 9}]}, {"id": 108, "name": "g<PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 109, "name": "g<PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Set the response type to GeoJSON."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 68, "typeArguments": [{"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "name": "Record"}], "name": "PostgrestSingleResponse"}], "name": "PromiseLike"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 127, "character": 9}]}, {"id": 86, "name": "limit", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 87, "name": "limit", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Limits the result with the specified `count`."}, "parameters": [{"id": 88, "name": "count", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The maximum no. of rows to limit to."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 89, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 90, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 91, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (for foreign columns).\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 66, "character": 37}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [91]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 66, "character": 22}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 66, "character": 7}]}, {"id": 104, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 105, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves at most one row from the result. Result must be at most one row\n(e.g. using `eq` on a UNIQUE column), otherwise this will result in an\nerror."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 70, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestMaybeSingleResponse"}], "name": "PromiseLike"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 110, "character": 13}]}, {"id": 78, "name": "order", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 79, "name": "order", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Orders the result with the specified `column`."}, "parameters": [{"id": 80, "name": "column", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The column to order on."}, "type": {"type": "typeOperator", "operator": "keyof", "target": {"type": "typeParameter", "name": "T"}}}, {"id": 81, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__1", "type": {"type": "reflection", "declaration": {"id": 82, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 83, "name": "ascending", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, the result will be in ascending order."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 43, "character": 15}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "true"}, {"id": 85, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (if `column` is a foreign column).\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 45, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 84, "name": "nullsFirst", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "If `true`, `null`s appear first."}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 44, "character": 16}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}], "groups": [{"title": "Variables", "kind": 32, "children": [83, 85, 84]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 41, "character": 20}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 40, "character": 7}]}, {"id": 92, "name": "range", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 93, "name": "range", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Limits the result to rows within the specified range, inclusive."}, "parameters": [{"id": 94, "name": "from", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The starting index from which to limit the result, inclusive."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 95, "name": "to", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The last index to which to limit the result, inclusive."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 96, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__2", "type": {"type": "reflection", "declaration": {"id": 97, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 98, "name": "foreignTable", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "comment": {"text": "The foreign table to use (for foreign columns).\n"}, "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 79, "character": 48}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [98]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 79, "character": 33}]}}, "defaultValue": "{}"}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 79, "character": 7}]}, {"id": 75, "name": "select", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 76, "name": "select", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Performs vertical filtering with SELECT."}, "parameters": [{"id": 77, "name": "columns", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "The columns to retrieve, separated by commas.\n"}, "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"*\""}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 13, "character": 8}]}, {"id": 102, "name": "single", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 103, "name": "single", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Retrieves only one row from the result. Result must be one row (e.g. using\n`limit`), otherwise this will result in an error."}, "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 68, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestSingleResponse"}], "name": "PromiseLike"}}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 100, "character": 8}]}, {"id": 137, "name": "then", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 138, "name": "then", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "typeParameter": [{"id": 139, "name": "TResult1", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}, {"id": 140, "name": "TResult2", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 141, "name": "onfulfilled", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 142, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 143, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 144, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 93, "character": 9}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 145, "name": "onrejected", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 146, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 147, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 148, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 96, "character": 18}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}]}], "name": "PromiseLike"}, "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}], "sources": [{"fileName": "lib/types.ts", "line": 91, "character": 6}], "inheritedFrom": {"type": "reference", "id": 47, "name": "PostgrestBuilder.then"}}, {"id": 134, "name": "throwOnError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 135, "name": "throwOnError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "If there's an error with the query, throwOnError will reject the promise by\nthrowing the error instead of returning it as part of a successful response.", "text": "{@link https://github.com/supabase/supabase-js/issues/92}\n"}, "parameters": [{"id": 136, "name": "throwOnError", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "type": {"type": "intrinsic", "name": "this"}, "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}], "sources": [{"fileName": "lib/types.ts", "line": 83, "character": 14}], "inheritedFrom": {"type": "reference", "id": 44, "name": "PostgrestBuilder.throwOnError"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [131]}, {"title": "Properties", "kind": 1024, "children": [130, 126, 129, 121, 119, 125, 127, 128, 120]}, {"title": "Methods", "kind": 2048, "children": [99, 106, 110, 108, 86, 104, 78, 92, 75, 102, 137, 134]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 7, "character": 46}], "extendedTypes": [{"type": "reference", "id": 27, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestBuilder"}], "extendedBy": [{"type": "reference", "id": 150, "name": "PostgrestFilterBuilder"}], "implementedTypes": [{"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}]}], "groups": [{"title": "Classes", "kind": 128, "children": [73]}], "sources": [{"fileName": "lib/PostgrestTransformBuilder.ts", "line": 1, "character": 0}]}, {"id": 499, "name": "\"lib/constants\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/constants.ts", "children": [{"id": 500, "name": "DEFAULT_HEADERS", "kind": 2097152, "kindString": "Object literal", "flags": {"isExported": true, "isConst": true}, "children": [{"id": 501, "name": "X-Client-Info", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 2, "character": 48}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "`postgrest-js/${version}`"}], "groups": [{"title": "Variables", "kind": 32, "children": [501]}], "sources": [{"fileName": "lib/constants.ts", "line": 2, "character": 28}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Object literals", "kind": 2097152, "children": [500]}], "sources": [{"fileName": "lib/constants.ts", "line": 1, "character": 0}]}, {"id": 1, "name": "\"lib/types\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/types.ts", "children": [{"id": 27, "name": "PostgrestBuilder", "kind": 128, "kindString": "Class", "flags": {"isExported": true, "isAbstract": true}, "typeParameter": [{"id": 28, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "children": [{"id": 41, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 42, "name": "new PostgrestBuilder", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 43, "name": "builder", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 27, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestBuilder"}}], "type": {"type": "reference", "id": 27, "name": "PostgrestBuilder"}}], "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 31}]}, {"id": 40, "name": "allowEmpty", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 60, "character": 22}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 36, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 56, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}, {"type": "array", "elementType": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "Partial"}}]}}, {"id": 39, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 59, "character": 17}], "type": {"type": "reference", "id": 59, "name": "<PERSON>tch"}}, {"id": 31, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 32, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 33, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 34, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "lib/types.ts", "line": 54, "character": 21}]}}}, {"id": 29, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 52, "character": 18}], "type": {"type": "union", "types": [{"type": "stringLiteral", "value": "GET"}, {"type": "stringLiteral", "value": "HEAD"}, {"type": "stringLiteral", "value": "POST"}, {"type": "stringLiteral", "value": "PATCH"}, {"type": "stringLiteral", "value": "DELETE"}]}}, {"id": 35, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 55, "character": 18}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 37, "name": "shouldThrowOnError", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 57, "character": 30}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 38, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/types.ts", "line": 58, "character": 18}], "type": {"type": "reference", "name": "AbortSignal"}}, {"id": 30, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 53, "character": 15}], "type": {"type": "reference", "name": "URL"}}, {"id": 47, "name": "then", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 48, "name": "then", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "typeParameter": [{"id": 49, "name": "TResult1", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}, {"id": 50, "name": "TResult2", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "parameters": [{"id": 51, "name": "onfulfilled", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 52, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 53, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 54, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 93, "character": 9}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 55, "name": "onrejected", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 56, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "signatures": [{"id": 57, "name": "__call", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 58, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "union", "types": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}, {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}], "name": "PromiseLike"}]}}], "sources": [{"fileName": "lib/types.ts", "line": 96, "character": 18}]}}, {"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "typeParameter", "name": "TResult1", "default": {"type": "reference", "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}}, {"type": "typeParameter", "name": "TResult2", "default": {"type": "intrinsic", "name": "never"}}]}], "name": "PromiseLike"}}], "sources": [{"fileName": "lib/types.ts", "line": 91, "character": 6}]}, {"id": 44, "name": "throwOnError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 45, "name": "throwOnError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "If there's an error with the query, throwOnError will reject the promise by\nthrowing the error instead of returning it as part of a successful response.", "text": "{@link https://github.com/supabase/supabase-js/issues/92}\n"}, "parameters": [{"id": 46, "name": "throwOnError", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "type": {"type": "intrinsic", "name": "this"}}], "sources": [{"fileName": "lib/types.ts", "line": 83, "character": 14}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [41]}, {"title": "Properties", "kind": 1024, "children": [40, 36, 39, 31, 29, 35, 37, 38, 30]}, {"title": "Methods", "kind": 2048, "children": [47, 44]}], "sources": [{"fileName": "lib/types.ts", "line": 51, "character": 38}], "extendedBy": [{"type": "reference", "id": 73, "name": "PostgrestTransformBuilder"}, {"type": "reference", "id": 363, "name": "PostgrestQueryBuilder"}, {"type": "reference", "id": 449, "name": "PostgrestRpcBuilder"}], "implementedTypes": [{"type": "reference", "typeArguments": [{"type": "reference", "id": 66, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponse"}], "name": "PromiseLike"}]}, {"id": 2, "name": "PostgrestResponseBase", "kind": 256, "kindString": "Interface", "flags": {}, "comment": {"shortText": "Response format", "text": "{@link https://github.com/supabase/supabase-js/issues/32}\n"}, "children": [{"id": 3, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 21, "character": 8}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 4, "name": "statusText", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 22, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [3, 4]}], "sources": [{"fileName": "lib/types.ts", "line": 20, "character": 31}], "extendedBy": [{"type": "reference", "id": 5, "name": "PostgrestResponseSuccess"}, {"type": "reference", "id": 13, "name": "PostgrestResponseFailure"}, {"type": "reference", "id": 20, "name": "PostgrestSingleResponseSuccess"}]}, {"id": 13, "name": "PostgrestResponseFailure", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 16, "name": "body", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 35, "character": 6}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 17, "name": "count", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 36, "character": 7}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 15, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 33, "character": 6}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 14, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 32, "character": 7}], "type": {"type": "reference", "id": 60, "name": "PostgrestError"}}, {"id": 18, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 21, "character": 8}], "type": {"type": "intrinsic", "name": "number"}, "inheritedFrom": {"type": "reference", "id": 3, "name": "PostgrestResponseBase.status"}}, {"id": 19, "name": "statusText", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 22, "character": 12}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 4, "name": "PostgrestResponseBase.statusText"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [16, 17, 15, 14, 18, 19]}], "sources": [{"fileName": "lib/types.ts", "line": 31, "character": 34}], "extendedTypes": [{"type": "reference", "id": 2, "name": "PostgrestResponseBase"}]}, {"id": 5, "name": "PostgrestResponseSuccess", "kind": 256, "kindString": "Interface", "flags": {}, "typeParameter": [{"id": 6, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "children": [{"id": 9, "name": "body", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 28, "character": 6}], "type": {"type": "array", "elementType": {"type": "typeParameter", "name": "T"}}}, {"id": 10, "name": "count", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 29, "character": 7}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "number"}, {"type": "intrinsic", "name": "null"}]}}, {"id": 8, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 27, "character": 6}], "type": {"type": "array", "elementType": {"type": "typeParameter", "name": "T"}}}, {"id": 7, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 26, "character": 7}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 11, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 21, "character": 8}], "type": {"type": "intrinsic", "name": "number"}, "inheritedFrom": {"type": "reference", "id": 3, "name": "PostgrestResponseBase.status"}}, {"id": 12, "name": "statusText", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 22, "character": 12}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 4, "name": "PostgrestResponseBase.statusText"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [9, 10, 8, 7, 11, 12]}], "sources": [{"fileName": "lib/types.ts", "line": 25, "character": 34}], "extendedTypes": [{"type": "reference", "id": 2, "name": "PostgrestResponseBase"}]}, {"id": 20, "name": "PostgrestSingleResponseSuccess", "kind": 256, "kindString": "Interface", "flags": {}, "typeParameter": [{"id": 21, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "children": [{"id": 24, "name": "body", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 44, "character": 6}], "type": {"type": "typeParameter", "name": "T"}}, {"id": 23, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 42, "character": 6}], "type": {"type": "typeParameter", "name": "T"}}, {"id": 22, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 41, "character": 7}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 25, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 21, "character": 8}], "type": {"type": "intrinsic", "name": "number"}, "inheritedFrom": {"type": "reference", "id": 3, "name": "PostgrestResponseBase.status"}}, {"id": 26, "name": "statusText", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "lib/types.ts", "line": 22, "character": 12}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 4, "name": "PostgrestResponseBase.statusText"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [24, 23, 22, 25, 26]}], "sources": [{"fileName": "lib/types.ts", "line": 40, "character": 40}], "extendedTypes": [{"type": "reference", "id": 2, "name": "PostgrestResponseBase"}]}, {"id": 59, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 1, "character": 17}], "type": {"type": "query", "queryType": {"type": "reference", "id": 375, "name": "fetch"}}}, {"id": 60, "name": "PostgrestError", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "comment": {"shortText": "Error format", "text": "{@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n"}, "sources": [{"fileName": "lib/types.ts", "line": 8, "character": 26}], "type": {"type": "reflection", "declaration": {"id": 61, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 65, "name": "code", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 12, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 63, "name": "details", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 10, "character": 9}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 64, "name": "hint", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 11, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 62, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/types.ts", "line": 9, "character": 9}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [65, 63, 64, 62]}], "sources": [{"fileName": "lib/types.ts", "line": 8, "character": 28}]}}}, {"id": 70, "name": "PostgrestMaybeSingleResponse", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "typeParameter": [{"id": 71, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "sources": [{"fileName": "lib/types.ts", "line": 49, "character": 40}], "type": {"type": "reference", "id": 68, "typeArguments": [{"type": "union", "types": [{"type": "typeParameter", "name": "T"}, {"type": "intrinsic", "name": "null"}]}], "name": "PostgrestSingleResponse"}}, {"id": 66, "name": "PostgrestResponse", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "typeParameter": [{"id": 67, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "sources": [{"fileName": "lib/types.ts", "line": 38, "character": 29}], "type": {"type": "union", "types": [{"type": "reference", "id": 5, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestResponseSuccess"}, {"type": "reference", "id": 13, "name": "PostgrestResponseFailure"}]}}, {"id": 68, "name": "PostgrestSingleResponse", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "typeParameter": [{"id": 69, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {"isExported": true}}], "sources": [{"fileName": "lib/types.ts", "line": 46, "character": 35}], "type": {"type": "union", "types": [{"type": "reference", "id": 20, "typeArguments": [{"type": "typeParameter", "name": "T"}], "name": "PostgrestSingleResponseSuccess"}, {"type": "reference", "id": 13, "name": "PostgrestResponseFailure"}]}}], "groups": [{"title": "Classes", "kind": 128, "children": [27]}, {"title": "Interfaces", "kind": 256, "children": [2, 13, 5, 20]}, {"title": "Type aliases", "kind": 4194304, "children": [59, 60, 70, 66, 68]}], "sources": [{"fileName": "lib/types.ts", "line": 1, "character": 0}]}, {"id": 497, "name": "\"lib/version\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/postgrest-js/src/lib/version.ts", "children": [{"id": 498, "name": "version", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isConst": true}, "sources": [{"fileName": "lib/version.ts", "line": 2, "character": 20}], "type": {"type": "stringLiteral", "value": "0.0.0-automated"}, "defaultValue": "\"0.0.0-automated\""}], "groups": [{"title": "Variables", "kind": 32, "children": [498]}], "sources": [{"fileName": "lib/version.ts", "line": 1, "character": 0}]}], "groups": [{"title": "<PERSON><PERSON><PERSON>", "kind": 1, "children": [502, 540, 149, 362, 448, 72, 499, 1, 497]}]}