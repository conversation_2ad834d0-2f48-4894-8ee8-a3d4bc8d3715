{"id": 0, "name": "@supabase/realtime-js", "kind": 0, "flags": {}, "originalName": "", "children": [{"id": 227, "name": "\"RealtimeClient\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/RealtimeClient.ts", "children": [{"id": 228, "name": "RealtimeClient", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 260, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "comment": {"shortText": "Initializes the Socket."}, "signatures": [{"id": 261, "name": "new RealtimeClient", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "comment": {"shortText": "Initializes the Socket."}, "parameters": [{"id": 262, "name": "endPoint", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 263, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "reference", "id": 340, "name": "Options"}}], "type": {"type": "reference", "id": 228, "name": "RealtimeClient"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 73, "character": 3}]}, {"id": 229, "name": "accessToken", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 43, "character": 13}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}, "defaultValue": "null"}, {"id": 230, "name": "channels", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 44, "character": 10}], "type": {"type": "array", "elementType": {"type": "reference", "id": 140, "name": "RealtimeSubscription"}}, "defaultValue": "[]"}, {"id": 252, "name": "conn", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 60, "character": 6}], "type": {"type": "union", "types": [{"type": "reference", "name": "WebSocket"}, {"type": "intrinsic", "name": "null"}]}, "defaultValue": "null"}, {"id": 250, "name": "decode", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 58, "character": 8}], "type": {"type": "reference", "name": "Function"}}, {"id": 249, "name": "encode", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 57, "character": 8}], "type": {"type": "reference", "name": "Function"}}, {"id": 231, "name": "endPoint", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 45, "character": 10}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"\""}, {"id": 232, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 46, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 233, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 234, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 235, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}, "defaultValue": "DEFAULT_HEADERS"}, {"id": 242, "name": "heartbeatIntervalMs", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 50, "character": 21}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "30000"}, {"id": 244, "name": "heartbeatTimer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 52, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "typeArguments": [{"type": "query", "queryType": {"type": "reference", "name": "setInterval"}}], "name": "ReturnType"}, {"type": "intrinsic", "name": "undefined"}]}, "defaultValue": "undefined"}, {"id": 248, "name": "logger", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 56, "character": 8}], "type": {"type": "reference", "name": "Function"}, "defaultValue": "noop"}, {"id": 243, "name": "longpollerTimeout", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 51, "character": 19}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "20000"}, {"id": 236, "name": "params", "kind": 1024, "kindString": "Property", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 47, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 237, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 238, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 239, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 245, "name": "pendingHeartbeatRef", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 53, "character": 21}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}, "defaultValue": "null"}, {"id": 251, "name": "reconnectAfterMs", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 59, "character": 18}], "type": {"type": "reference", "name": "Function"}}, {"id": 247, "name": "reconnectTimer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 55, "character": 16}], "type": {"type": "reference", "id": 35, "name": "Timer"}}, {"id": 246, "name": "ref", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 54, "character": 5}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "0"}, {"id": 253, "name": "send<PERSON><PERSON><PERSON>", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 61, "character": 12}], "type": {"type": "array", "elementType": {"type": "reference", "name": "Function"}}, "defaultValue": "[]"}, {"id": 254, "name": "serializer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 62, "character": 12}], "type": {"type": "reference", "id": 49, "name": "Serializer"}, "defaultValue": "new Serializer()"}, {"id": 240, "name": "timeout", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 48, "character": 9}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "DEFAULT_TIMEOUT"}, {"id": 241, "name": "transport", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 49, "character": 11}], "type": {"type": "intrinsic", "name": "any"}, "defaultValue": "w3cwebsocket"}, {"id": 329, "name": "_appendParams", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 330, "name": "_appendParams", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 331, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 332, "name": "params", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 333, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 334, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 335, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 405, "character": 11}]}}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 403, "character": 23}]}, {"id": 336, "name": "_flushSend<PERSON><PERSON>er", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 337, "name": "_flushSend<PERSON><PERSON>er", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 416, "character": 26}]}, {"id": 321, "name": "_onConnClose", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 322, "name": "_onConnClose", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 323, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 383, "character": 22}]}, {"id": 324, "name": "_onConnError", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 325, "name": "_onConnError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 326, "name": "error", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "ErrorEvent"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 391, "character": 22}]}, {"id": 319, "name": "_onConnOpen", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 320, "name": "_onConnOpen", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 371, "character": 21}]}, {"id": 338, "name": "_sendHeartbeat", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 339, "name": "_sendHeartbeat", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 423, "character": 24}]}, {"id": 327, "name": "_trigger<PERSON>hanError", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 328, "name": "_trigger<PERSON>hanError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 397, "character": 27}]}, {"id": 297, "name": "channel", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 298, "name": "channel", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 299, "name": "topic", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 300, "name": "chanParams", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 364, "name": "ChannelParams"}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 140, "name": "RealtimeSubscription"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 264, "character": 9}]}, {"id": 264, "name": "connect", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 265, "name": "connect", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Connects the socket, unless already connected."}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 125, "character": 9}]}, {"id": 290, "name": "connectionState", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 291, "name": "connectionState", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Returns the current state of the socket."}, "type": {"type": "reference", "id": 24, "name": "CONNECTION_STATE"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 233, "character": 17}]}, {"id": 266, "name": "disconnect", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 267, "name": "disconnect", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Disconnects the socket."}, "parameters": [{"id": 268, "name": "code", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "A numeric status code to send on disconnect."}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 269, "name": "reason", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "comment": {"text": "A custom reason for the disconnect.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 270, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 272, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 151, "character": 40}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 271, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 151, "character": 20}], "type": {"type": "union", "types": [{"type": "reference", "name": "Error"}, {"type": "intrinsic", "name": "null"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [272, 271]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 151, "character": 13}]}}], "name": "Promise"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 148, "character": 12}]}, {"id": 309, "name": "endPointURL", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 310, "name": "endPointURL", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Returns the URL of the websocket."}, "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 320, "character": 13}]}, {"id": 292, "name": "isConnected", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 293, "name": "isConnected", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Returns `true` is the connection is open."}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 249, "character": 13}]}, {"id": 316, "name": "leaveOpenTopic", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 317, "name": "leaveOpenTopic", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Unsubscribe from channels with the specified topic."}, "parameters": [{"id": 318, "name": "topic", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 361, "character": 16}]}, {"id": 273, "name": "log", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 274, "name": "log", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Logs the message.", "text": "For customized logging, `this.logger` can be overridden.\n"}, "parameters": [{"id": 275, "name": "kind", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 276, "name": "msg", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 277, "name": "data", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 178, "character": 5}]}, {"id": 311, "name": "makeRef", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 312, "name": "makeRef", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Return the next message ref, accounting for overflows"}, "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 330, "character": 9}]}, {"id": 281, "name": "onClose", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 282, "name": "onClose", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Registers a callback for connection state change events.", "tags": [{"tag": "example", "text": "\n   socket.onOpen(() => console.log(\"Socket closed.\"))\n"}]}, "parameters": [{"id": 283, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A function to be called when the event occurs.\n"}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 202, "character": 9}]}, {"id": 304, "name": "onConnMessage", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 305, "name": "onConnMessage", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 306, "name": "rawMessage", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 307, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 308, "name": "data", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 290, "character": 34}], "type": {"type": "intrinsic", "name": "any"}}], "groups": [{"title": "Variables", "kind": 32, "children": [308]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 290, "character": 27}]}}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 290, "character": 15}]}, {"id": 284, "name": "onError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 285, "name": "onError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Registers a callback for connection state change events.", "tags": [{"tag": "example", "text": "\n   socket.onOpen((error) => console.log(\"An error occurred\"))\n"}]}, "parameters": [{"id": 286, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A function to be called when the event occurs.\n"}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 214, "character": 9}]}, {"id": 287, "name": "onMessage", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 288, "name": "onMessage", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Calls a function any time a message is received.", "tags": [{"tag": "example", "text": "\n   socket.onMessage((message) => console.log(message))\n"}]}, "parameters": [{"id": 289, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A function to be called when the event occurs.\n"}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 226, "character": 11}]}, {"id": 278, "name": "onOpen", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 279, "name": "onOpen", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Registers a callback for connection state change event.", "tags": [{"tag": "example", "text": "\n   socket.onOpen(() => console.log(\"Socket opened.\"))\n"}]}, "parameters": [{"id": 280, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A function to be called when the event occurs.\n"}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 190, "character": 8}]}, {"id": 301, "name": "push", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 302, "name": "push", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Push out a message if the socket is connected.", "text": "If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n"}, "parameters": [{"id": 303, "name": "data", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 358, "name": "Message"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 275, "character": 6}]}, {"id": 294, "name": "remove", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 295, "name": "remove", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Removes a subscription from the socket."}, "parameters": [{"id": 296, "name": "channel", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "An open subscription.\n"}, "type": {"type": "reference", "id": 140, "name": "RealtimeSubscription"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 258, "character": 8}]}, {"id": 313, "name": "setAuth", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 314, "name": "setAuth", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Sets the JWT access token used for channel subscription authorization and Realtime RLS."}, "parameters": [{"id": 315, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"text": "A JWT string.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 346, "character": 9}]}, {"id": 255, "name": "stateChangeCallbacks", "kind": 2097152, "kindString": "Object literal", "flags": {"isExported": true}, "children": [{"id": 257, "name": "close", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 70, "character": 9}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "never"}}, "defaultValue": "[]"}, {"id": 258, "name": "error", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 71, "character": 9}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "never"}}, "defaultValue": "[]"}, {"id": 259, "name": "message", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 72, "character": 11}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "never"}}, "defaultValue": "[]"}, {"id": 256, "name": "open", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 69, "character": 8}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "never"}}, "defaultValue": "[]"}], "groups": [{"title": "Variables", "kind": 32, "children": [257, 258, 259, 256]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 63, "character": 22}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [260]}, {"title": "Properties", "kind": 1024, "children": [229, 230, 252, 250, 249, 231, 232, 242, 244, 248, 243, 236, 245, 251, 247, 246, 253, 254, 240, 241]}, {"title": "Methods", "kind": 2048, "children": [329, 336, 321, 324, 319, 338, 327, 297, 264, 290, 266, 309, 292, 316, 273, 311, 281, 304, 284, 287, 278, 301, 294, 313]}, {"title": "Object literals", "kind": 2097152, "children": [255]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 42, "character": 35}]}, {"id": 364, "name": "ChannelParams", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "RealtimeClient.ts", "line": 35, "character": 18}], "type": {"type": "reflection", "declaration": {"id": 365, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": [{"id": 367, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 368, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}], "children": [{"id": 366, "name": "selfBroadcast", "kind": 32, "kindString": "Variable", "flags": {"isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 36, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [366]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 35, "character": 20}]}}}, {"id": 358, "name": "Message", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "RealtimeClient.ts", "line": 28, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 359, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 361, "name": "event", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "RealtimeClient.ts", "line": 30, "character": 7}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 362, "name": "payload", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "RealtimeClient.ts", "line": 31, "character": 9}], "type": {"type": "intrinsic", "name": "any"}}, {"id": 363, "name": "ref", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "RealtimeClient.ts", "line": 32, "character": 5}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 360, "name": "topic", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "RealtimeClient.ts", "line": 29, "character": 7}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [361, 362, 363, 360]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 28, "character": 14}]}}}, {"id": 340, "name": "Options", "kind": 4194304, "kindString": "Type alias", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 16, "character": 19}], "type": {"type": "reflection", "declaration": {"id": 341, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 348, "name": "decode", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 23, "character": 8}], "type": {"type": "reference", "name": "Function"}}, {"id": 347, "name": "encode", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 22, "character": 8}], "type": {"type": "reference", "name": "Function"}}, {"id": 350, "name": "headers", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 25, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 351, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 352, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 353, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 344, "name": "heartbeatIntervalMs", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 19, "character": 21}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 346, "name": "logger", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 21, "character": 8}], "type": {"type": "reference", "name": "Function"}}, {"id": 345, "name": "longpollerTimeout", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 20, "character": 19}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 354, "name": "params", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 26, "character": 8}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reflection", "declaration": {"id": 355, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 356, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 357, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}]}}, {"id": 349, "name": "reconnectAfterMs", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 24, "character": 18}], "type": {"type": "reference", "name": "Function"}}, {"id": 343, "name": "timeout", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 18, "character": 9}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 342, "name": "transport", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "RealtimeClient.ts", "line": 17, "character": 11}], "type": {"type": "reference", "name": "WebSocket"}}], "groups": [{"title": "Variables", "kind": 32, "children": [348, 347, 350, 344, 346, 345, 354, 349, 343, 342]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 16, "character": 21}]}}}, {"id": 369, "name": "noop", "kind": 64, "kindString": "Function", "flags": {"isConst": true}, "signatures": [{"id": 370, "name": "noop", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeClient.ts", "line": 40, "character": 10}]}], "groups": [{"title": "Classes", "kind": 128, "children": [228]}, {"title": "Type aliases", "kind": 4194304, "children": [364, 358, 340]}, {"title": "Functions", "kind": 64, "children": [369]}], "sources": [{"fileName": "RealtimeClient.ts", "line": 1, "character": 0}]}, {"id": 139, "name": "\"RealtimeSubscription\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/RealtimeSubscription.ts", "children": [{"id": 140, "name": "RealtimeSubscription", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 148, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 155, "name": "new RealtimeSubscription", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 156, "name": "topic", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 157, "name": "params", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 158, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 159, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 160, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "unknown"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 17, "character": 18}]}}, "defaultValue": "{}"}, {"id": 161, "name": "socket", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 228, "name": "RealtimeClient"}}], "type": {"type": "reference", "id": 140, "name": "RealtimeSubscription"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 13, "character": 25}]}, {"id": 141, "name": "bindings", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 7, "character": 10}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "any"}}, "defaultValue": "[]"}, {"id": 145, "name": "joinPush", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 11, "character": 10}], "type": {"type": "reference", "id": 74, "name": "<PERSON><PERSON>"}}, {"id": 144, "name": "joinedOnce", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 10, "character": 12}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 150, "name": "params", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 17, "character": 17}], "type": {"type": "reflection", "declaration": {"id": 151, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 152, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 153, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "unknown"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 17, "character": 18}]}}}, {"id": 147, "name": "pushBuffer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 13, "character": 12}], "type": {"type": "array", "elementType": {"type": "reference", "id": 74, "name": "<PERSON><PERSON>"}}, "defaultValue": "[]"}, {"id": 146, "name": "rejoinTimer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 12, "character": 13}], "type": {"type": "reference", "id": 35, "name": "Timer"}}, {"id": 154, "name": "socket", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 18, "character": 17}], "type": {"type": "reference", "id": 228, "name": "RealtimeClient"}}, {"id": 143, "name": "state", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 9, "character": 7}], "type": {"type": "reference", "id": 9, "name": "CHANNEL_STATES"}, "defaultValue": "CHANNEL_STATES.closed"}, {"id": 142, "name": "timeout", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 8, "character": 9}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 149, "name": "topic", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "sources": [{"fileName": "RealtimeSubscription.ts", "line": 16, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 180, "name": "canPush", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 181, "name": "canPush", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 97, "character": 9}]}, {"id": 217, "name": "isClosed", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 218, "name": "isClosed", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 202, "character": 10}]}, {"id": 219, "name": "isErrored", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 220, "name": "isErrored", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 205, "character": 11}]}, {"id": 221, "name": "isJoined", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 222, "name": "isJoined", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 208, "character": 10}]}, {"id": 223, "name": "isJoining", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 224, "name": "isJoining", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 211, "character": 11}]}, {"id": 225, "name": "isLeaving", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 226, "name": "isLeaving", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 214, "character": 11}]}, {"id": 201, "name": "isMember", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 202, "name": "isMember", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 203, "name": "topic", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "boolean"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 158, "character": 10}]}, {"id": 204, "name": "joinRef", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 205, "name": "joinRef", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 162, "character": 9}]}, {"id": 177, "name": "off", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 178, "name": "off", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 179, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 93, "character": 5}]}, {"id": 173, "name": "on", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 174, "name": "on", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 175, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 176, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 89, "character": 4}]}, {"id": 167, "name": "onClose", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 168, "name": "onClose", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 169, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 81, "character": 9}]}, {"id": 170, "name": "onError", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 171, "name": "onError", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 172, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 85, "character": 9}]}, {"id": 196, "name": "onMessage", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 197, "name": "onMessage", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Overridable message hook", "text": "Receives all events for specialized message handling before dispatching to the channel callbacks.\nMust return the payload, modified or unmodified.\n"}, "parameters": [{"id": 198, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 199, "name": "payload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 200, "name": "ref", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "type": {"type": "intrinsic", "name": "any"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 154, "character": 11}]}, {"id": 182, "name": "push", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 183, "name": "push", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 184, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 15, "name": "CHANNEL_EVENTS"}}, {"id": 185, "name": "payload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 186, "name": "timeout", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "this.timeout"}], "type": {"type": "reference", "id": 74, "name": "<PERSON><PERSON>"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 101, "character": 6}]}, {"id": 206, "name": "rejoin", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 207, "name": "rejoin", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 208, "name": "timeout", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "this.timeout"}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 166, "character": 8}]}, {"id": 162, "name": "rejoinUntilConnected", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 163, "name": "rejoinUntilConnected", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 64, "character": 22}]}, {"id": 214, "name": "replyEventName", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 215, "name": "replyEventName", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 216, "name": "ref", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 198, "character": 16}]}, {"id": 164, "name": "subscribe", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 165, "name": "subscribe", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 166, "name": "timeout", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "this.timeout"}], "type": {"type": "reference", "id": 74, "name": "<PERSON><PERSON>"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 71, "character": 11}]}, {"id": 209, "name": "trigger", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 210, "name": "trigger", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 211, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 212, "name": "payload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 213, "name": "ref", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true, "isOptional": true}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 175, "character": 9}]}, {"id": 193, "name": "unsubscribe", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 194, "name": "unsubscribe", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Leaves the channel", "text": "Unsubscribes from server events, and instructs channel to terminate on server.\nTriggers onClose() hooks.\n\nTo receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\nchannel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n"}, "parameters": [{"id": 195, "name": "timeout", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "this.timeout"}], "type": {"type": "reference", "id": 74, "name": "<PERSON><PERSON>"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 129, "character": 13}]}, {"id": 187, "name": "updateJoinPayload", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 188, "name": "updateJoinPayload", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 189, "name": "payload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 190, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 191, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 192, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "unknown"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 116, "character": 28}]}}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 116, "character": 19}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [148]}, {"title": "Properties", "kind": 1024, "children": [141, 145, 144, 150, 147, 146, 154, 143, 142, 149]}, {"title": "Methods", "kind": 2048, "children": [180, 217, 219, 221, 223, 225, 201, 204, 177, 173, 167, 170, 196, 182, 206, 162, 214, 164, 209, 193, 187]}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 6, "character": 41}]}], "groups": [{"title": "Classes", "kind": 128, "children": [140]}], "sources": [{"fileName": "RealtimeSubscription.ts", "line": 1, "character": 0}]}, {"id": 445, "name": "\"index\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/index.ts", "sources": [{"fileName": "index.ts", "line": 1, "character": 0}]}, {"id": 3, "name": "\"lib/constants\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/lib/constants.ts", "children": [{"id": 15, "name": "CHANNEL_EVENTS", "kind": 4, "kindString": "Enumeration", "flags": {"isExported": true}, "children": [{"id": 21, "name": "access_token", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 32, "character": 14}], "defaultValue": "\"access_token\""}, {"id": 16, "name": "close", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 27, "character": 7}], "defaultValue": "\"phx_close\""}, {"id": 17, "name": "error", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 28, "character": 7}], "defaultValue": "\"phx_error\""}, {"id": 18, "name": "join", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 29, "character": 6}], "defaultValue": "\"phx_join\""}, {"id": 20, "name": "leave", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 31, "character": 7}], "defaultValue": "\"phx_leave\""}, {"id": 19, "name": "reply", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 30, "character": 7}], "defaultValue": "\"phx_reply\""}], "groups": [{"title": "Enumeration members", "kind": 16, "children": [21, 16, 17, 18, 20, 19]}], "sources": [{"fileName": "lib/constants.ts", "line": 26, "character": 26}]}, {"id": 9, "name": "CHANNEL_STATES", "kind": 4, "kindString": "Enumeration", "flags": {"isExported": true}, "children": [{"id": 10, "name": "closed", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 19, "character": 8}], "defaultValue": "\"closed\""}, {"id": 11, "name": "errored", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 20, "character": 9}], "defaultValue": "\"errored\""}, {"id": 12, "name": "joined", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 21, "character": 8}], "defaultValue": "\"joined\""}, {"id": 13, "name": "joining", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 22, "character": 9}], "defaultValue": "\"joining\""}, {"id": 14, "name": "leaving", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 23, "character": 9}], "defaultValue": "\"leaving\""}], "groups": [{"title": "Enumeration members", "kind": 16, "children": [10, 11, 12, 13, 14]}], "sources": [{"fileName": "lib/constants.ts", "line": 18, "character": 26}]}, {"id": 24, "name": "CONNECTION_STATE", "kind": 4, "kindString": "Enumeration", "flags": {"isExported": true}, "children": [{"id": 28, "name": "Closed", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 43, "character": 8}], "defaultValue": "\"closed\""}, {"id": 27, "name": "Closing", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 42, "character": 9}], "defaultValue": "\"closing\""}, {"id": 25, "name": "Connecting", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 40, "character": 12}], "defaultValue": "\"connecting\""}, {"id": 26, "name": "Open", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 41, "character": 6}], "defaultValue": "\"open\""}], "groups": [{"title": "Enumeration members", "kind": 16, "children": [28, 27, 25, 26]}], "sources": [{"fileName": "lib/constants.ts", "line": 39, "character": 28}]}, {"id": 4, "name": "SOCKET_STATES", "kind": 4, "kindString": "Enumeration", "flags": {"isExported": true}, "children": [{"id": 8, "name": "closed", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 15, "character": 8}], "defaultValue": "3"}, {"id": 7, "name": "closing", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 14, "character": 9}], "defaultValue": "2"}, {"id": 5, "name": "connecting", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 12, "character": 12}], "defaultValue": "0"}, {"id": 6, "name": "open", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 13, "character": 6}], "defaultValue": "1"}], "groups": [{"title": "Enumeration members", "kind": 16, "children": [8, 7, 5, 6]}], "sources": [{"fileName": "lib/constants.ts", "line": 11, "character": 25}]}, {"id": 22, "name": "TRANSPORTS", "kind": 4, "kindString": "Enumeration", "flags": {"isExported": true}, "children": [{"id": 23, "name": "websocket", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 36, "character": 11}], "defaultValue": "\"websocket\""}], "groups": [{"title": "Enumeration members", "kind": 16, "children": [23]}], "sources": [{"fileName": "lib/constants.ts", "line": 35, "character": 22}]}, {"id": 32, "name": "DEFAULT_TIMEOUT", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isConst": true}, "sources": [{"fileName": "lib/constants.ts", "line": 7, "character": 28}], "type": {"type": "unknown", "name": "10000"}, "defaultValue": "10000"}, {"id": 31, "name": "VSN", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isConst": true}, "sources": [{"fileName": "lib/constants.ts", "line": 5, "character": 16}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"1.0.0\""}, {"id": 33, "name": "WS_CLOSE_NORMAL", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isConst": true}, "sources": [{"fileName": "lib/constants.ts", "line": 9, "character": 28}], "type": {"type": "unknown", "name": "1000"}, "defaultValue": "1000"}, {"id": 29, "name": "DEFAULT_HEADERS", "kind": 2097152, "kindString": "Object literal", "flags": {"isExported": true, "isConst": true}, "children": [{"id": 30, "name": "X-Client-Info", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/constants.ts", "line": 3, "character": 48}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "`realtime-js/${version}`"}], "groups": [{"title": "Variables", "kind": 32, "children": [30]}], "sources": [{"fileName": "lib/constants.ts", "line": 3, "character": 28}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Enumerations", "kind": 4, "children": [15, 9, 24, 4, 22]}, {"title": "Variables", "kind": 32, "children": [32, 31, 33]}, {"title": "Object literals", "kind": 2097152, "children": [29]}], "sources": [{"fileName": "lib/constants.ts", "line": 1, "character": 0}]}, {"id": 73, "name": "\"lib/push\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/lib/push.ts", "children": [{"id": 74, "name": "<PERSON><PERSON>", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 87, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "comment": {"shortText": "Initializes the Push"}, "signatures": [{"id": 95, "name": "new Push", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "comment": {"shortText": "Initializes the Push"}, "parameters": [{"id": 96, "name": "channel", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The Channel"}, "type": {"type": "reference", "id": 140, "name": "RealtimeSubscription"}}, {"id": 97, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The event, for example `\"phx_join\"`"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 98, "name": "payload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The payload, for example `{user_id: 123}`"}, "type": {"type": "reflection", "declaration": {"id": 99, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 100, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 101, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "unknown"}}], "sources": [{"fileName": "lib/push.ts", "line": 29, "character": 19}]}}, "defaultValue": "{}"}, {"id": 102, "name": "timeout", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The push timeout in milliseconds\n"}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "DEFAULT_TIMEOUT"}], "type": {"type": "reference", "id": 74, "name": "<PERSON><PERSON>"}}], "sources": [{"fileName": "lib/push.ts", "line": 16, "character": 32}]}, {"id": 88, "name": "channel", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "comment": {"shortText": "The Channel"}, "sources": [{"fileName": "lib/push.ts", "line": 27, "character": 18}], "type": {"type": "reference", "id": 140, "name": "RealtimeSubscription"}}, {"id": 89, "name": "event", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "comment": {"shortText": "The event, for example `\"phx_join\"`"}, "sources": [{"fileName": "lib/push.ts", "line": 28, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 90, "name": "payload", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "comment": {"shortText": "The payload, for example `{user_id: 123}`"}, "sources": [{"fileName": "lib/push.ts", "line": 29, "character": 18}], "type": {"type": "reflection", "declaration": {"id": 91, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 92, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 93, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "unknown"}}], "sources": [{"fileName": "lib/push.ts", "line": 29, "character": 19}]}}}, {"id": 82, "name": "rec<PERSON>ooks", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 12, "character": 10}], "type": {"type": "array", "elementType": {"type": "reflection", "declaration": {"id": 83, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 85, "name": "callback", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 14, "character": 12}], "type": {"type": "reference", "name": "Function"}}, {"id": 84, "name": "status", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 13, "character": 10}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [85, 84]}], "sources": [{"fileName": "lib/push.ts", "line": 12, "character": 11}]}}}, "defaultValue": "[]"}, {"id": 78, "name": "receivedResp", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 8, "character": 14}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 79, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 81, "name": "response", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 10, "character": 12}], "type": {"type": "reference", "name": "Function"}}, {"id": 80, "name": "status", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 9, "character": 10}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [81, 80]}], "sources": [{"fileName": "lib/push.ts", "line": 8, "character": 15}]}}, {"type": "intrinsic", "name": "null"}]}, "defaultValue": "null"}, {"id": 77, "name": "ref", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 7, "character": 5}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "\"\""}, {"id": 86, "name": "refEvent", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 16, "character": 10}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "null"}]}, "defaultValue": "null"}, {"id": 75, "name": "sent", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 5, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 94, "name": "timeout", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "comment": {"shortText": "The push timeout in milliseconds\n"}, "sources": [{"fileName": "lib/push.ts", "line": 30, "character": 18}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 76, "name": "timeoutTimer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 6, "character": 14}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "number"}, {"type": "intrinsic", "name": "undefined"}]}, "defaultValue": "undefined"}, {"id": 126, "name": "_cancelRefEvent", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 127, "name": "_cancelRefEvent", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 100, "character": 25}]}, {"id": 128, "name": "_cancelTimeout", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 129, "name": "_cancelTimeout", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 108, "character": 24}]}, {"id": 136, "name": "_hasReceived", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 137, "name": "_hasReceived", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 138, "name": "status", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "intrinsic", "name": "false"}, {"type": "intrinsic", "name": "true"}]}}], "sources": [{"fileName": "lib/push.ts", "line": 125, "character": 22}]}, {"id": 130, "name": "_matchReceive", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 131, "name": "_matchReceive", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 132, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "originalName": "__0", "type": {"type": "reflection", "declaration": {"id": 133, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 135, "name": "response", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 115, "character": 12}], "type": {"type": "reference", "name": "Function"}}, {"id": 134, "name": "status", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/push.ts", "line": 114, "character": 10}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [135, 134]}], "sources": [{"fileName": "lib/push.ts", "line": 113, "character": 24}]}}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 113, "character": 23}]}, {"id": 124, "name": "destroy", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 125, "name": "destroy", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 95, "character": 9}]}, {"id": 114, "name": "receive", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 115, "name": "receive", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 116, "name": "status", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 117, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "unknown", "name": "this"}}], "sources": [{"fileName": "lib/push.ts", "line": 61, "character": 9}]}, {"id": 103, "name": "resend", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 104, "name": "resend", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 105, "name": "timeout", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "number"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 33, "character": 8}]}, {"id": 106, "name": "send", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 107, "name": "send", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 43, "character": 6}]}, {"id": 118, "name": "startTimeout", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 119, "name": "startTimeout", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 70, "character": 14}]}, {"id": 120, "name": "trigger", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 121, "name": "trigger", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 122, "name": "status", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 123, "name": "response", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 91, "character": 9}]}, {"id": 108, "name": "updatePayload", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 109, "name": "updatePayload", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 110, "name": "payload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reflection", "declaration": {"id": 111, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "indexSignature": [{"id": 112, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {"isExported": true}, "parameters": [{"id": 113, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "unknown"}}], "sources": [{"fileName": "lib/push.ts", "line": 57, "character": 24}]}}}], "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/push.ts", "line": 57, "character": 15}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [87]}, {"title": "Properties", "kind": 1024, "children": [88, 89, 90, 82, 78, 77, 86, 75, 94, 76]}, {"title": "Methods", "kind": 2048, "children": [126, 128, 136, 130, 124, 114, 103, 106, 118, 120, 108]}], "sources": [{"fileName": "lib/push.ts", "line": 4, "character": 25}]}], "groups": [{"title": "Classes", "kind": 128, "children": [74]}], "sources": [{"fileName": "lib/push.ts", "line": 1, "character": 0}]}, {"id": 48, "name": "\"lib/serializer\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/lib/serializer.ts", "children": [{"id": 49, "name": "Serializer", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "children": [{"id": 50, "name": "HEADER_LENGTH", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 5, "character": 15}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "1"}, {"id": 55, "name": "_binaryDecode", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 56, "name": "_binaryDecode", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 57, "name": "buffer", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}], "type": {"type": "reflection", "declaration": {"id": 58, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 61, "name": "event", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 33, "character": 9}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 62, "name": "payload", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 34, "character": 11}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 59, "name": "ref", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 31, "character": 7}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 60, "name": "topic", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 32, "character": 9}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [61, 62, 59, 60]}]}}}], "sources": [{"fileName": "lib/serializer.ts", "line": 19, "character": 23}]}, {"id": 63, "name": "_decodeBroadcast", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true, "isExported": true}, "signatures": [{"id": 64, "name": "_decodeBroadcast", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 65, "name": "buffer", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"id": 66, "name": "view", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "DataView"}}, {"id": 67, "name": "decoder", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "TextDecoder"}}], "type": {"type": "reflection", "declaration": {"id": 68, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 71, "name": "event", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 33, "character": 9}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 72, "name": "payload", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 34, "character": 11}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 69, "name": "ref", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 31, "character": 7}], "type": {"type": "intrinsic", "name": "null"}}, {"id": 70, "name": "topic", "kind": 32, "kindString": "Variable", "flags": {"isExported": true}, "sources": [{"fileName": "lib/serializer.ts", "line": 32, "character": 9}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Variables", "kind": 32, "children": [71, 72, 69, 70]}], "sources": [{"fileName": "lib/serializer.ts", "line": 30, "character": 4}]}}}], "sources": [{"fileName": "lib/serializer.ts", "line": 26, "character": 26}]}, {"id": 51, "name": "decode", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 52, "name": "decode", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 53, "name": "rawPayload", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "union", "types": [{"type": "reference", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 54, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "intrinsic", "name": "any"}}], "sources": [{"fileName": "lib/serializer.ts", "line": 7, "character": 8}]}], "groups": [{"title": "Properties", "kind": 1024, "children": [50]}, {"title": "Methods", "kind": 2048, "children": [55, 63, 51]}], "sources": [{"fileName": "lib/serializer.ts", "line": 4, "character": 31}]}], "groups": [{"title": "Classes", "kind": 128, "children": [49]}], "sources": [{"fileName": "lib/serializer.ts", "line": 1, "character": 0}]}, {"id": 34, "name": "\"lib/timer\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/lib/timer.ts", "children": [{"id": 35, "name": "Timer", "kind": 128, "kindString": "Class", "flags": {"isExported": true}, "comment": {"shortText": "Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.", "tags": [{"tag": "example", "text": "\n   let reconnectTimer = new Timer(() => this.connect(), function(tries){\n     return [1000, 5000, 10000][tries - 1] || 10000\n   })\n   reconnectTimer.scheduleTimeout() // fires after 1000\n   reconnectTimer.scheduleTimeout() // fires after 5000\n   reconnectTimer.reset()\n   reconnectTimer.scheduleTimeout() // fires after 1000\n"}]}, "children": [{"id": 38, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "signatures": [{"id": 41, "name": "new Timer", "kind": 16384, "kindString": "Constructor signature", "flags": {"isExported": true}, "parameters": [{"id": 42, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}, {"id": 43, "name": "timerCalc", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "name": "Function"}}], "type": {"type": "reference", "id": 35, "name": "Timer"}}], "sources": [{"fileName": "lib/timer.ts", "line": 15, "character": 19}]}, {"id": 39, "name": "callback", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "sources": [{"fileName": "lib/timer.ts", "line": 17, "character": 29}], "type": {"type": "reference", "name": "Function"}}, {"id": 36, "name": "timer", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/timer.ts", "line": 14, "character": 7}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "number"}, {"type": "intrinsic", "name": "undefined"}]}, "defaultValue": "undefined"}, {"id": 40, "name": "timerCalc", "kind": 1024, "kindString": "Property", "flags": {"isPublic": true, "isExported": true, "isConstructorProperty": true}, "sources": [{"fileName": "lib/timer.ts", "line": 17, "character": 57}], "type": {"type": "reference", "name": "Function"}}, {"id": 37, "name": "tries", "kind": 1024, "kindString": "Property", "flags": {"isExported": true}, "sources": [{"fileName": "lib/timer.ts", "line": 15, "character": 7}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "0"}, {"id": 44, "name": "reset", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 45, "name": "reset", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/timer.ts", "line": 22, "character": 7}]}, {"id": 46, "name": "scheduleTimeout", "kind": 2048, "kindString": "Method", "flags": {"isExported": true}, "signatures": [{"id": 47, "name": "scheduleTimeout", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "void"}}], "sources": [{"fileName": "lib/timer.ts", "line": 28, "character": 17}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [38]}, {"title": "Properties", "kind": 1024, "children": [39, 36, 40, 37]}, {"title": "Methods", "kind": 2048, "children": [44, 46]}], "sources": [{"fileName": "lib/timer.ts", "line": 13, "character": 26}]}], "groups": [{"title": "Classes", "kind": 128, "children": [35]}], "sources": [{"fileName": "lib/timer.ts", "line": 1, "character": 0}]}, {"id": 371, "name": "\"lib/transformers\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/lib/transformers.ts", "children": [{"id": 372, "name": "PostgresTypes", "kind": 4, "kindString": "Enumeration", "flags": {"isExported": true}, "comment": {"shortText": "Helpers to convert the change Payload into native JS types."}, "children": [{"id": 373, "name": "abstime", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 9, "character": 9}], "defaultValue": "\"abstime\""}, {"id": 374, "name": "bool", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 10, "character": 6}], "defaultValue": "\"bool\""}, {"id": 375, "name": "date", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 11, "character": 6}], "defaultValue": "\"date\""}, {"id": 376, "name": "daterange", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 12, "character": 11}], "defaultValue": "\"daterange\""}, {"id": 377, "name": "float4", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 13, "character": 8}], "defaultValue": "\"float4\""}, {"id": 378, "name": "float8", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 14, "character": 8}], "defaultValue": "\"float8\""}, {"id": 379, "name": "int2", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 15, "character": 6}], "defaultValue": "\"int2\""}, {"id": 380, "name": "int4", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 16, "character": 6}], "defaultValue": "\"int4\""}, {"id": 381, "name": "int4range", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 17, "character": 11}], "defaultValue": "\"int4range\""}, {"id": 382, "name": "int8", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 18, "character": 6}], "defaultValue": "\"int8\""}, {"id": 383, "name": "int8range", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 19, "character": 11}], "defaultValue": "\"int8range\""}, {"id": 384, "name": "json", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 20, "character": 6}], "defaultValue": "\"json\""}, {"id": 385, "name": "jsonb", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 21, "character": 7}], "defaultValue": "\"jsonb\""}, {"id": 386, "name": "money", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 22, "character": 7}], "defaultValue": "\"money\""}, {"id": 387, "name": "numeric", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 23, "character": 9}], "defaultValue": "\"numeric\""}, {"id": 388, "name": "oid", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 24, "character": 5}], "defaultValue": "\"oid\""}, {"id": 389, "name": "reltime", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 25, "character": 9}], "defaultValue": "\"reltime\""}, {"id": 390, "name": "text", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 26, "character": 6}], "defaultValue": "\"text\""}, {"id": 391, "name": "time", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 27, "character": 6}], "defaultValue": "\"time\""}, {"id": 392, "name": "timestamp", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 28, "character": 11}], "defaultValue": "\"timestamp\""}, {"id": 393, "name": "timestamptz", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 29, "character": 13}], "defaultValue": "\"timestamptz\""}, {"id": 394, "name": "timetz", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 30, "character": 8}], "defaultValue": "\"timetz\""}, {"id": 395, "name": "tsrange", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 31, "character": 9}], "defaultValue": "\"tsrange\""}, {"id": 396, "name": "tstzrange", "kind": 16, "kindString": "Enumeration member", "flags": {"isExported": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 32, "character": 11}], "defaultValue": "\"tstzrange\""}], "groups": [{"title": "Enumeration members", "kind": 16, "children": [373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396]}], "sources": [{"fileName": "lib/transformers.ts", "line": 8, "character": 25}]}, {"id": 403, "name": "BaseValue", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "lib/transformers.ts", "line": 42, "character": 14}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "null"}, {"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "number"}, {"type": "intrinsic", "name": "boolean"}]}}, {"id": 397, "name": "Columns", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "lib/transformers.ts", "line": 35, "character": 12}], "type": {"type": "array", "elementType": {"type": "reflection", "declaration": {"id": 398, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 401, "name": "flags", "kind": 32, "kindString": "Variable", "flags": {"isOptional": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 38, "character": 7}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 399, "name": "name", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/transformers.ts", "line": 36, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 400, "name": "type", "kind": 32, "kindString": "Variable", "flags": {}, "sources": [{"fileName": "lib/transformers.ts", "line": 37, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 402, "name": "type_modifier", "kind": 32, "kindString": "Variable", "flags": {"isOptional": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 39, "character": 15}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "number"}]}}], "groups": [{"title": "Variables", "kind": 32, "children": [401, 399, 400, 402]}], "sources": [{"fileName": "lib/transformers.ts", "line": 35, "character": 14}]}}}}, {"id": 405, "name": "Record", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "lib/transformers.ts", "line": 45, "character": 11}], "type": {"type": "reflection", "declaration": {"id": 406, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": [{"id": 407, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 408, "name": "key", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 45, "character": 13}]}}}, {"id": 404, "name": "RecordValue", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "lib/transformers.ts", "line": 43, "character": 16}], "type": {"type": "union", "types": [{"type": "reference", "id": 403, "name": "BaseValue"}, {"type": "array", "elementType": {"type": "reference", "id": 403, "name": "BaseValue"}}]}}, {"id": 422, "name": "convertCell", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "comment": {"shortText": "If the value of the cell is `null`, returns null.\nOtherwise converts the string value to the correct type.", "tags": [{"tag": "example", "text": "convertCell('bool', 't')\n//=> true"}, {"tag": "example", "text": "convertCell('int8', '10')\n//=> 10"}, {"tag": "example", "text": "convertCell('_int4', '{1,2,3,4}')\n//=> [1,2,3,4]\n"}]}, "signatures": [{"id": 423, "name": "convertCell", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "If the value of the cell is `null`, returns null.\nOtherwise converts the string value to the correct type.", "tags": [{"tag": "example", "text": "convertCell('bool', 't')\n//=> true"}, {"tag": "example", "text": "convertCell('int8', '10')\n//=> 10"}, {"tag": "example", "text": "convertCell('_int4', '{1,2,3,4}')\n//=> [1,2,3,4]\n"}]}, "parameters": [{"id": 424, "name": "type", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "A postgres column type"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 425, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 118, "character": 24}]}, {"id": 409, "name": "convertChangeData", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "comment": {"shortText": "Takes an array of columns and an object of string values then converts each string value\nto its mapped type.", "tags": [{"tag": "example", "text": "convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: '<PERSON>', age:'33'}, {})\n//=>{ first_name: '<PERSON>', age: 33 }\n"}]}, "signatures": [{"id": 410, "name": "convertChangeData", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Takes an array of columns and an object of string values then converts each string value\nto its mapped type.", "tags": [{"tag": "example", "text": "convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: '<PERSON>', age:'33'}, {})\n//=>{ first_name: '<PERSON>', age: 33 }\n"}]}, "parameters": [{"id": 411, "name": "columns", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 397, "name": "Columns"}}, {"id": 412, "name": "record", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {}, "type": {"type": "reference", "id": 405, "name": "Record"}}, {"id": 413, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The map of various options that can be applied to the mapper"}, "type": {"type": "reflection", "declaration": {"id": 414, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {"isExported": true}, "children": [{"id": 415, "name": "skipTypes", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isOptional": true}, "sources": [{"fileName": "lib/transformers.ts", "line": 64, "character": 22}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}], "groups": [{"title": "Variables", "kind": 32, "children": [415]}], "sources": [{"fileName": "lib/transformers.ts", "line": 64, "character": 10}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 405, "name": "Record"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 61, "character": 30}]}, {"id": 416, "name": "convertColumn", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "comment": {"shortText": "Converts the value of an individual column.", "returns": "Useless information\n", "tags": [{"tag": "example", "text": "convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: '<PERSON>', age: '33'}, [])\n//=> 33"}, {"tag": "example", "text": "convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: '<PERSON>', age: '33'}, ['int4'])\n//=> \"33\"\n"}]}, "signatures": [{"id": 417, "name": "convertColumn", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Converts the value of an individual column.", "returns": "Useless information\n", "tags": [{"tag": "example", "text": "convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: '<PERSON>', age: '33'}, [])\n//=> 33"}, {"tag": "example", "text": "convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: '<PERSON>', age: '33'}, ['int4'])\n//=> \"33\"\n"}]}, "parameters": [{"id": 418, "name": "columnName", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The column that you want to convert"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 419, "name": "columns", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 397, "name": "Columns"}}, {"id": 420, "name": "record", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "The map of string values"}, "type": {"type": "reference", "id": 405, "name": "Record"}}, {"id": 421, "name": "skipTypes", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "comment": {"shortText": "An array of types that should not be converted"}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 88, "character": 26}]}, {"id": 426, "name": "noop", "kind": 64, "kindString": "Function", "flags": {"isConst": true}, "signatures": [{"id": 427, "name": "noop", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 428, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 162, "character": 10}]}, {"id": 438, "name": "toArray", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "comment": {"shortText": "Converts a Postgres Array into a native JS array", "tags": [{"tag": "example", "text": "toArray('{}', 'int4')\n//=> []"}, {"tag": "example", "text": "toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n//=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']"}, {"tag": "example", "text": "toArray([1,2,3,4], 'int4')\n//=> [1,2,3,4]\n"}]}, "signatures": [{"id": 439, "name": "toArray", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Converts a Postgres Array into a native JS array", "tags": [{"tag": "example", "text": "toArray('{}', 'int4')\n//=> []"}, {"tag": "example", "text": "toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n//=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']"}, {"tag": "example", "text": "toArray([1,2,3,4], 'int4')\n//=> [1,2,3,4]\n"}]}, "parameters": [{"id": 440, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}, {"id": 441, "name": "type", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 206, "character": 20}]}, {"id": 429, "name": "toBoolean", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "signatures": [{"id": 430, "name": "toBoolean", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 431, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 165, "character": 22}]}, {"id": 435, "name": "to<PERSON><PERSON>", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "signatures": [{"id": 436, "name": "to<PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 437, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 184, "character": 19}]}, {"id": 432, "name": "toNumber", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "signatures": [{"id": 433, "name": "toNumber", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "parameters": [{"id": 434, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 175, "character": 21}]}, {"id": 442, "name": "toTimestampString", "kind": 64, "kindString": "Function", "flags": {"isExported": true, "isConst": true}, "comment": {"shortText": "Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\nSee https://github.com/supabase/supabase/issues/18", "tags": [{"tag": "example", "text": "toTimestampString('2019-09-10 00:00:00')\n//=> '2019-09-10T00:00:00'\n"}]}, "signatures": [{"id": 443, "name": "toTimestampString", "kind": 4096, "kindString": "Call signature", "flags": {"isExported": true}, "comment": {"shortText": "Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\nSee https://github.com/supabase/supabase/issues/18", "tags": [{"tag": "example", "text": "toTimestampString('2019-09-10 00:00:00')\n//=> '2019-09-10T00:00:00'\n"}]}, "parameters": [{"id": 444, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {"isExported": true}, "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "type": {"type": "reference", "id": 404, "name": "RecordValue"}}], "sources": [{"fileName": "lib/transformers.ts", "line": 241, "character": 30}]}], "groups": [{"title": "Enumerations", "kind": 4, "children": [372]}, {"title": "Type aliases", "kind": 4194304, "children": [403, 397, 405, 404]}, {"title": "Functions", "kind": 64, "children": [422, 409, 416, 426, 438, 429, 435, 432, 442]}], "sources": [{"fileName": "lib/transformers.ts", "line": 1, "character": 0}]}, {"id": 1, "name": "\"lib/version\"", "kind": 1, "kindString": "<PERSON><PERSON><PERSON>", "flags": {"isExported": true}, "originalName": "/Users/<USER>/Projects/Supabase/realtime-js/src/lib/version.ts", "children": [{"id": 2, "name": "version", "kind": 32, "kindString": "Variable", "flags": {"isExported": true, "isConst": true}, "sources": [{"fileName": "lib/version.ts", "line": 1, "character": 20}], "type": {"type": "stringLiteral", "value": "0.0.0-automated"}, "defaultValue": "\"0.0.0-automated\""}], "groups": [{"title": "Variables", "kind": 32, "children": [2]}], "sources": [{"fileName": "lib/version.ts", "line": 1, "character": 0}]}], "groups": [{"title": "<PERSON><PERSON><PERSON>", "kind": 1, "children": [227, 139, 445, 3, 73, 48, 34, 371, 1]}]}