{"id": 0, "name": "@supabase/gotrue-js", "kind": 1, "kindString": "Project", "flags": {}, "originalName": "", "children": [{"id": 1, "name": "GoTrueApi", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 2, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 25, "character": 2}], "signatures": [{"id": 3, "name": "new GoTrueApi", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 4, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 5, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 11, "name": "cookieOptions", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 35, "character": 4}], "type": {"type": "reference", "id": 557, "name": "CookieOptions"}}, {"id": 12, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 36, "character": 4}], "type": {"type": "reflection", "declaration": {"id": 13, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 14, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 15, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 16, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 7, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 32, "character": 4}], "type": {"type": "reflection", "declaration": {"id": 8, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 9, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 10, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 6, "name": "url", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 31, "character": 4}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [11, 12, 7, 6]}]}}}], "type": {"type": "reference", "id": 1, "name": "default"}}]}, {"id": 22, "name": "cookieOptions", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 22, "character": 12}], "type": {"type": "reference", "id": 557, "name": "CookieOptions"}}, {"id": 23, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 23, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 24, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 25, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 26, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 27, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 18, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 19, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 19, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 20, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 21, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 17, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 18, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 28, "name": "_createRequestHeaders", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 49, "character": 10}], "signatures": [{"id": 29, "name": "_createRequestHeaders", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Create a temporary object with all configured headers and\nadds the Authorization token to be used on request methods"}, "parameters": [{"id": 30, "name": "jwt", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A valid, logged-in JWT.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reflection", "declaration": {"id": 31, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}}]}, {"id": 32, "name": "cookieName", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 55, "character": 10}], "signatures": [{"id": 33, "name": "cookieName", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}]}, {"id": 196, "name": "createUser", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 659, "character": 8}], "signatures": [{"id": 197, "name": "createUser", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a new user.", "text": "This function should only be called on a server. Never expose your `service_role` key in the browser.\n"}, "parameters": [{"id": 198, "name": "attributes", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The data you want to create the user with.\n"}, "type": {"type": "reference", "id": 539, "name": "AdminUserAttributes", "dereferenced": {"id": 539, "name": "AdminUserAttributes", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 541, "name": "app_metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for app_metadata that.", "text": "Only a service role can modify.\n\nCan be any JSON that includes app-specific info, such as identity providers, roles, and other\naccess control information.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 137, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 548, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata that a user can modify. Can be any JSON."}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 2}], "type": {"type": "intrinsic", "name": "object"}, "inheritedFrom": {"type": "reference", "id": 538, "name": "UserAttributes.data"}}, {"id": 544, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's email."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 534, "name": "UserAttributes.email"}}, {"id": 547, "name": "email_change_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "An email change token."}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 537, "name": "UserAttributes.email_change_token"}}, {"id": 542, "name": "email_confirm", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Sets if a user has confirmed their email address.", "text": "Only a service role can modify.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 144, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 546, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's password."}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 536, "name": "UserAttributes.password"}}, {"id": 545, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's phone."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 535, "name": "UserAttributes.phone"}}, {"id": 543, "name": "phone_confirm", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Sets if a user has confirmed their phone number.", "text": "Only a service role can modify.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 151, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 540, "name": "user_metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata.", "text": "Can be any JSON.\n\nOnly a service role can modify.\n\nNote: When using the GoTrueAdminApi and wanting to modify a user's user_metadata,\nthis attribute is used instead of UserAttributes data.\n\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [541, 548, 544, 547, 542, 546, 545, 543, 540]}], "sources": [{"fileName": "src/lib/types.ts", "line": 115, "character": 17}], "extendedTypes": [{"type": "reference", "id": 533, "name": "UserAttributes"}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 199, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 201, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 662, "character": 18}], "type": {"type": "literal", "value": null}}, {"id": 202, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 662, "character": 30}], "type": {"type": "reference", "id": 483, "name": "ApiError"}}, {"id": 200, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 662, "character": 6}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [201, 202, 200]}]}}, {"type": "reflection", "declaration": {"id": 203, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 205, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 662, "character": 64}], "type": {"type": "reference", "id": 505, "name": "User"}}, {"id": 206, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 662, "character": 76}], "type": {"type": "literal", "value": null}}, {"id": 204, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 662, "character": 52}], "type": {"type": "reference", "id": 505, "name": "User"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [205, 206, 204]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 173, "name": "deleteAuthCookie", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 549, "character": 2}], "signatures": [{"id": 174, "name": "deleteAuthCookie", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Deletes the Auth Cookies and redirects to the"}, "parameters": [{"id": 175, "name": "req", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The request object."}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 176, "name": "res", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The response object."}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 177, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "Optionally specify a `redirectTo` URL in the options.\n"}, "originalName": "__namedParameters", "type": {"type": "reflection", "declaration": {"id": 178, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 179, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 549, "character": 63}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [179]}]}}}], "type": {"type": "intrinsic", "name": "any"}}]}, {"id": 241, "name": "deleteUser", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 799, "character": 8}], "signatures": [{"id": 242, "name": "deleteUser", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Delete a user. Requires a `service_role` key.", "text": "This function should only be called on a server. Never expose your `service_role` key in the browser.\n"}, "parameters": [{"id": 243, "name": "uid", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The user uid you want to remove.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 244, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 246, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 801, "character": 34}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 247, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 801, "character": 53}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 245, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 801, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [246, 247, 245]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 184, "name": "generateLink", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 616, "character": 8}], "signatures": [{"id": 185, "name": "generateLink", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Generates links to be sent via email or other."}, "parameters": [{"id": 186, "name": "type", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The link type (\"signup\" or \"magiclink\" or \"recovery\" or \"invite\")."}, "type": {"type": "union", "types": [{"type": "literal", "value": "signup"}, {"type": "literal", "value": "invite"}, {"type": "literal", "value": "magiclink"}, {"type": "literal", "value": "recovery"}, {"type": "literal", "value": "email_change_current"}, {"type": "literal", "value": "email_change_new"}]}}, {"id": 187, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The user's email."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 188, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 189, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 191, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 627, "character": 6}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 190, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 626, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 192, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 628, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [191, 190, 192]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 193, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 194, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 630, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 195, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 630, "character": 44}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [194, 195]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 180, "name": "getAuthCookieString", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 568, "character": 2}], "signatures": [{"id": 181, "name": "getAuthCookieString", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Helper method to generate the Auth Cookie string for you in case you can't use `setAuthCookie`.", "returns": "The Cookie string that needs to be set as the value for the `Set-Cookie` header.\n"}, "parameters": [{"id": 182, "name": "req", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The request object."}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 183, "name": "res", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The response object."}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}]}, {"id": 34, "name": "getUrl<PERSON>or<PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 65, "character": 2}], "signatures": [{"id": 35, "name": "getUrl<PERSON>or<PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Generates the relevant login URL for a third-party provider."}, "parameters": [{"id": 36, "name": "provider", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "One of the providers supported by GoTrue."}, "type": {"type": "reference", "id": 481, "name": "Provider", "dereferenced": {"id": 481, "name": "Provider", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 1, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "apple"}, {"type": "literal", "value": "azure"}, {"type": "literal", "value": "bitbucket"}, {"type": "literal", "value": "discord"}, {"type": "literal", "value": "facebook"}, {"type": "literal", "value": "github"}, {"type": "literal", "value": "gitlab"}, {"type": "literal", "value": "google"}, {"type": "literal", "value": "keycloak"}, {"type": "literal", "value": "linkedin"}, {"type": "literal", "value": "notion"}, {"type": "literal", "value": "slack"}, {"type": "literal", "value": "spotify"}, {"type": "literal", "value": "twitch"}, {"type": "literal", "value": "twitter"}, {"type": "literal", "value": "workos"}]}}}}, {"id": 37, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 38, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 41, "name": "queryParams", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 70, "character": 6}], "type": {"type": "reflection", "declaration": {"id": 42, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 43, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 44, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 39, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 68, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 40, "name": "scopes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 69, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [41, 39, 40]}]}}}], "type": {"type": "intrinsic", "name": "string"}}]}, {"id": 248, "name": "getUser", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 826, "character": 8}], "signatures": [{"id": 249, "name": "getUser", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Gets the current user details.", "text": "This method is called by the GoTrueClient `update` where\nthe jwt is set to this.currentSession.access_token\nand therefore, acts like getting the currently authenticated user\n"}, "parameters": [{"id": 250, "name": "jwt", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A valid, logged-in JWT. Typically, the access_token for the currentSession\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 251, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 253, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 828, "character": 34}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 254, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 828, "character": 53}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 252, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 828, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [253, 254, 252]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 224, "name": "getUserByCookie", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 714, "character": 8}], "signatures": [{"id": 225, "name": "getUserByCookie", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Get user by reading the cookie from the request.\nWorks for Next.js & Express (requires cookie-parser middleware)."}, "parameters": [{"id": 226, "name": "req", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 227, "name": "res", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 228, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 231, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 720, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 232, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 721, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 229, "name": "token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 718, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 230, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 719, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [231, 232, 229, 230]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 215, "name": "getUserById", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 697, "character": 8}], "signatures": [{"id": 216, "name": "getUserById", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Get user by id."}, "parameters": [{"id": 217, "name": "uid", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The user's unique identifier\n\nThis function should only be called on a server. Never expose your `service_role` key in the browser.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 218, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 219, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 699, "character": 15}], "type": {"type": "literal", "value": null}}, {"id": 220, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 699, "character": 27}], "type": {"type": "reference", "id": 483, "name": "ApiError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [219, 220]}]}}, {"type": "reflection", "declaration": {"id": 221, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 222, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 699, "character": 49}], "type": {"type": "reference", "id": 505, "name": "User"}}, {"id": 223, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 699, "character": 61}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [222, 223]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 142, "name": "inviteUserByEmail", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 419, "character": 8}], "signatures": [{"id": 143, "name": "inviteUserByEmail", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Sends an invite link to an email address."}, "parameters": [{"id": 144, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The email address of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 145, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 146, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 148, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 423, "character": 6}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 147, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 422, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [148, 147]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 149, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 150, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 425, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 151, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 425, "character": 34}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [150, 151]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 207, "name": "listUsers", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 679, "character": 8}], "signatures": [{"id": 208, "name": "listUsers", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Get a list of users.", "text": "This function should only be called on a server. Never expose your `service_role` key in the browser.\n"}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 209, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 210, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 679, "character": 31}], "type": {"type": "literal", "value": null}}, {"id": 211, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 679, "character": 43}], "type": {"type": "reference", "id": 483, "name": "ApiError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [210, 211]}]}}, {"type": "reflection", "declaration": {"id": 212, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 213, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 679, "character": 65}], "type": {"type": "array", "elementType": {"type": "reference", "id": 505, "name": "User"}}}, {"id": 214, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 679, "character": 79}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [213, 214]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 163, "name": "refreshAccessToken", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 479, "character": 8}], "signatures": [{"id": 164, "name": "refreshAccessToken", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Generates a new JWT."}, "parameters": [{"id": 165, "name": "refreshToken", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A valid refresh token that was returned on login.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 166, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 167, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 481, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 168, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 481, "character": 37}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [167, 168]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 152, "name": "resetPasswordForEmail", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 450, "character": 8}], "signatures": [{"id": 153, "name": "resetPasswordForEmail", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Sends a reset request to an email address."}, "parameters": [{"id": 154, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The email address of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 155, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 156, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 158, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 454, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 157, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 453, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [158, 157]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 159, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 160, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 456, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reflection", "declaration": {"id": 161, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}]}}, {"id": 162, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 456, "character": 32}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [160, 162]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 95, "name": "sendMagicLinkEmail", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 272, "character": 8}], "signatures": [{"id": 96, "name": "sendMagicLinkEmail", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Sends a magic login link to an email address."}, "parameters": [{"id": 97, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The email address of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 98, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 99, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 102, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 277, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 101, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 276, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 100, "name": "shouldCreateUser", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 275, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [102, 101, 100]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 103, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 104, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 279, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reflection", "declaration": {"id": 105, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}]}}, {"id": 106, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 279, "character": 32}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [104, 106]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 107, "name": "sendMobileOTP", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 310, "character": 8}], "signatures": [{"id": 108, "name": "sendMobileOTP", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Sends a mobile OTP via SMS. Will register the account if it doesn't already exist"}, "parameters": [{"id": 109, "name": "phone", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The user's phone number WITH international prefix"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 110, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 111, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 113, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 314, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 112, "name": "shouldCreateUser", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 313, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [113, 112]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 114, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 115, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 316, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reflection", "declaration": {"id": 116, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}]}}, {"id": 117, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 316, "character": 32}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [115, 117]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 169, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 503, "character": 2}], "signatures": [{"id": 170, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Set/delete the auth cookie based on the AuthChangeEvent.\nWorks for Next.js & Express (requires cookie-parser middleware)."}, "parameters": [{"id": 171, "name": "req", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The request object."}, "type": {"type": "intrinsic", "name": "any"}}, {"id": 172, "name": "res", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The response object.\n"}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 57, "name": "signInWithEmail", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 139, "character": 8}], "signatures": [{"id": 58, "name": "signInWithEmail", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Logs in an existing user using their email address."}, "parameters": [{"id": 59, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The email address of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 60, "name": "password", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The password of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 61, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 62, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 64, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 144, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 63, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 143, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [64, 63]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 65, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 66, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 146, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 67, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 146, "character": 37}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [66, 67]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 89, "name": "signInWithOpenIDConnect", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 241, "character": 8}], "signatures": [{"id": 90, "name": "signInWithOpenIDConnect", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Logs in an OpenID Connect user using their id_token."}, "parameters": [{"id": 91, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 583, "name": "OpenIDConnectCredentials", "dereferenced": {"id": 583, "name": "OpenIDConnectCredentials", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 587, "name": "client_id", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 211, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 584, "name": "id_token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 208, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 588, "name": "issuer", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 212, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 585, "name": "nonce", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 209, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 586, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 210, "character": 2}], "type": {"type": "reference", "id": 481, "name": "Provider"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [587, 584, 588, 585, 586]}], "sources": [{"fileName": "src/lib/types.ts", "line": 207, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 92, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 93, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 247, "character": 42}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 94, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 247, "character": 64}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [93, 94]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 79, "name": "signInWithPhone", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 209, "character": 8}], "signatures": [{"id": 80, "name": "signInWithPhone", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Logs in an existing user using their phone number and password."}, "parameters": [{"id": 81, "name": "phone", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The phone number of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 82, "name": "password", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The password of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 83, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 84, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 85, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 213, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [85]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 86, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 87, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 215, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 88, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 215, "character": 37}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [87, 88]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 118, "name": "signOut", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 340, "character": 8}], "signatures": [{"id": 119, "name": "signOut", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Removes a logged-in session."}, "parameters": [{"id": 120, "name": "jwt", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A valid, logged-in JWT.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 121, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 122, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 340, "character": 40}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [122]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 45, "name": "signUpWithEmail", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 98, "character": 8}], "signatures": [{"id": 46, "name": "signUpWithEmail", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a new user using their email address.", "returns": "A logged-in session if the server has \"autoconfirm\" ON"}, "parameters": [{"id": 47, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The email address of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 48, "name": "password", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The password of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 49, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 50, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 53, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 104, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 52, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 103, "character": 6}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 51, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 102, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [53, 52, 51]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 54, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 55, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 106, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 56, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 106, "character": 44}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [55, 56]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 68, "name": "signUpWithPhone", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 174, "character": 8}], "signatures": [{"id": 69, "name": "signUpWithPhone", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Signs up a new user using their phone number and a password."}, "parameters": [{"id": 70, "name": "phone", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The phone number of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 71, "name": "password", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The password of the user."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 72, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 73, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 75, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 179, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 74, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 178, "character": 6}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [75, 74]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 76, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 77, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 181, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 78, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 181, "character": 44}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [77, 78]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 255, "name": "updateUser", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 844, "character": 8}], "signatures": [{"id": 256, "name": "updateUser", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates the user data."}, "parameters": [{"id": 257, "name": "jwt", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A valid, logged-in JWT."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 258, "name": "attributes", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The data you want to update.\n"}, "type": {"type": "reference", "id": 533, "name": "UserAttributes", "dereferenced": {"id": 533, "name": "UserAttributes", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 538, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata that a user can modify. Can be any JSON."}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 534, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's email."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 537, "name": "email_change_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "An email change token."}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 536, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's password."}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 535, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's phone."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [538, 534, 537, 536, 535]}], "sources": [{"fileName": "src/lib/types.ts", "line": 88, "character": 17}], "extendedBy": [{"type": "reference", "id": 539, "name": "AdminUserAttributes"}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 259, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 261, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 847, "character": 34}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 262, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 847, "character": 53}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 260, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 847, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [261, 262, 260]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 233, "name": "updateUserById", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 777, "character": 8}], "signatures": [{"id": 234, "name": "updateUserById", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates the user data."}, "parameters": [{"id": 235, "name": "uid", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 236, "name": "attributes", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The data you want to update.\n\nThis function should only be called on a server. Never expose your `service_role` key in the browser.\n"}, "type": {"type": "reference", "id": 539, "name": "AdminUserAttributes", "dereferenced": {"id": 539, "name": "AdminUserAttributes", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 541, "name": "app_metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for app_metadata that.", "text": "Only a service role can modify.\n\nCan be any JSON that includes app-specific info, such as identity providers, roles, and other\naccess control information.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 137, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 548, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata that a user can modify. Can be any JSON."}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 2}], "type": {"type": "intrinsic", "name": "object"}, "inheritedFrom": {"type": "reference", "id": 538, "name": "UserAttributes.data"}}, {"id": 544, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's email."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 534, "name": "UserAttributes.email"}}, {"id": 547, "name": "email_change_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "An email change token."}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 537, "name": "UserAttributes.email_change_token"}}, {"id": 542, "name": "email_confirm", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Sets if a user has confirmed their email address.", "text": "Only a service role can modify.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 144, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 546, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's password."}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 536, "name": "UserAttributes.password"}}, {"id": 545, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's phone."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 535, "name": "UserAttributes.phone"}}, {"id": 543, "name": "phone_confirm", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Sets if a user has confirmed their phone number.", "text": "Only a service role can modify.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 151, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 540, "name": "user_metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata.", "text": "Can be any JSON.\n\nOnly a service role can modify.\n\nNote: When using the GoTrueAdminApi and wanting to modify a user's user_metadata,\nthis attribute is used instead of UserAttributes data.\n\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [541, 548, 544, 547, 542, 546, 545, 543, 540]}], "sources": [{"fileName": "src/lib/types.ts", "line": 115, "character": 17}], "extendedTypes": [{"type": "reference", "id": 533, "name": "UserAttributes"}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 237, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 239, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 780, "character": 34}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 240, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 780, "character": 53}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 238, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 780, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [239, 240, 238]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 123, "name": "verifyMobileOTP", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 360, "character": 8}], "signatures": [{"id": 124, "name": "verifyMobileOTP", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"tags": [{"tag": "deprecated", "text": "Use `verifyOTP` instead!"}]}, "parameters": [{"id": 125, "name": "phone", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The user's phone number WITH international prefix"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 126, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "token that user was sent to their mobile phone"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 127, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 128, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 129, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 364, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [129]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 130, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 131, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 366, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 132, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 366, "character": 44}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [131, 132]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 133, "name": "verifyOTP", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 391, "character": 8}], "signatures": [{"id": 134, "name": "verifyOTP", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Send User supplied Email / Mobile OTP to be verified"}, "parameters": [{"id": 135, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 570, "name": "VerifyOTPParams", "dereferenced": {"id": 570, "name": "VerifyOTPParams", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 191, "character": 12}], "type": {"type": "union", "types": [{"type": "reference", "id": 571, "name": "VerifyMobileOTPParams"}, {"type": "reference", "id": 576, "name": "VerifyEmailOTPParams"}]}}}}, {"id": 136, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 137, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 138, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 394, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [138]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 139, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 140, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 396, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 141, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueApi.ts", "line": 396, "character": 44}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [140, 141]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [2]}, {"title": "Properties", "kind": 1024, "children": [22, 23, 18, 17]}, {"title": "Methods", "kind": 2048, "children": [28, 32, 196, 173, 241, 184, 180, 34, 248, 224, 215, 142, 207, 163, 152, 95, 107, 169, 57, 89, 79, 118, 45, 68, 255, 233, 123, 133]}], "sources": [{"fileName": "src/GoTrueApi.ts", "line": 17, "character": 21}]}, {"id": 263, "name": "GoTrueClient", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 264, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 82, "character": 2}], "signatures": [{"id": 265, "name": "new GoTrueClient", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "comment": {"shortText": "Create a new client for use in the browser."}, "parameters": [{"id": 266, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 267, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 274, "name": "autoRefreshToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set to \"true\" if you want to automatically refresh the token before expiring."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 86, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 278, "name": "cookieOptions", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 90, "character": 4}], "type": {"type": "reference", "id": 557, "name": "CookieOptions"}}, {"id": 273, "name": "detectSessionInUrl", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 85, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 279, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 91, "character": 4}], "type": {"type": "reflection", "declaration": {"id": 280, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 281, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "A custom fetch implementation.\n"}, "parameters": [{"id": 282, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 283, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 269, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Any additional headers to send to the GoTrue server."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 84, "character": 4}], "type": {"type": "reflection", "declaration": {"id": 270, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 271, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 272, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 276, "name": "localStorage", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Provide your own local storage implementation to use instead of the browser's local storage."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 88, "character": 4}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Storage", "package": "typescript", "name": "Storage"}, {"type": "union", "types": [{"type": "literal", "value": "getItem"}, {"type": "literal", "value": "removeItem"}, {"type": "literal", "value": "setItem"}]}], "qualifiedName": "Pick", "package": "typescript", "name": "Pick"}], "name": "PromisifyMethods"}}, {"id": 277, "name": "multiTab", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set to \"false\" if you want to disable multi-tab/window events."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 89, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 275, "name": "persistSession", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set to \"true\" if you want to automatically save the user session into local storage."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 87, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 268, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The URL of the GoTrue server."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 83, "character": 4}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [274, 278, 273, 279, 269, 276, 277, 275, 268]}]}}}], "type": {"type": "reference", "id": 263, "name": "default"}}]}, {"id": 284, "name": "api", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "Namespace for the GoTrue API methods.\nThese can be used for example to get a user from a JWT in a server environment or reset a user's password."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 52, "character": 2}], "type": {"type": "reference", "id": 1, "name": "default"}}, {"id": 287, "name": "autoRefreshToken", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 62, "character": 12}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 286, "name": "currentSession", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "comment": {"shortText": "The session object for the currently logged in user or null."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 60, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 285, "name": "currentUser", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "comment": {"shortText": "The currently logged in user or null."}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 56, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 289, "name": "localStorage", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 64, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Storage", "package": "typescript", "name": "Storage"}, {"type": "union", "types": [{"type": "literal", "value": "getItem"}, {"type": "literal", "value": "removeItem"}, {"type": "literal", "value": "setItem"}]}], "qualifiedName": "Pick", "package": "typescript", "name": "Pick"}], "name": "PromisifyMethods"}}, {"id": 290, "name": "multiTab", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 65, "character": 12}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 293, "name": "networkRetries", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 68, "character": 12}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "0"}, {"id": 288, "name": "persistSession", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 63, "character": 12}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 292, "name": "refreshTokenTimer", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 67, "character": 12}], "type": {"type": "reference", "qualifiedName": "NodeJS.Timeout", "package": "@types/node", "name": "Timeout"}}, {"id": 291, "name": "stateChangeEmitters", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 66, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "id": 549, "name": "Subscription"}], "qualifiedName": "Map", "package": "typescript", "name": "Map"}, "defaultValue": "..."}, {"id": 454, "name": "_callRefreshToken", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 695, "character": 16}], "signatures": [{"id": 455, "name": "_callRefreshToken", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 456, "name": "refresh_token", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "intrinsic", "name": "string"}]}, "defaultValue": "..."}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 457, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 458, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 486, "name": "Session"}}, {"id": 459, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 708, "character": 21}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}], "groups": [{"title": "Properties", "kind": 1024, "children": [458, 459]}]}}, {"type": "reflection", "declaration": {"id": 460, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 461, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 710, "character": 15}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 462, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 710, "character": 27}], "type": {"type": "reference", "id": 483, "name": "ApiError"}, "defaultValue": "..."}], "groups": [{"title": "Properties", "kind": 1024, "children": [461, 462]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 383, "name": "_handleEmailSignIn", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 520, "character": 16}], "signatures": [{"id": 384, "name": "_handleEmailSignIn", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 385, "name": "email", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 386, "name": "password", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 387, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 388, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 390, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 525, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 389, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 524, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [390, 389]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 391, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 392, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 533, "character": 35}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 395, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 394, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 533, "character": 59}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 393, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 533, "character": 47}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}], "groups": [{"title": "Properties", "kind": 1024, "children": [392, 395, 394, 393]}]}}, {"type": "reflection", "declaration": {"id": 396, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 397, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 486, "name": "Session"}}, {"id": 400, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 540, "character": 53}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 399, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 540, "character": 38}], "type": {"type": "reference", "id": 486, "name": "Session"}, "defaultValue": "data"}, {"id": 398, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 540, "character": 21}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}, "defaultValue": "data.user"}], "groups": [{"title": "Properties", "kind": 1024, "children": [397, 400, 399, 398]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 443, "name": "_handleOpenIDConnectSignIn", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 595, "character": 16}], "signatures": [{"id": 444, "name": "_handleOpenIDConnectSignIn", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 445, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 583, "name": "OpenIDConnectCredentials", "dereferenced": {"id": 583, "name": "OpenIDConnectCredentials", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 587, "name": "client_id", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 211, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 584, "name": "id_token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 208, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 588, "name": "issuer", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 212, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 585, "name": "nonce", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 209, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 586, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 210, "character": 2}], "type": {"type": "reference", "id": 481, "name": "Provider"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [587, 584, 588, 585, 586]}], "sources": [{"fileName": "src/lib/types.ts", "line": 207, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 446, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 449, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 604, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 447, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 602, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 448, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 603, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [449, 447, 448]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 401, "name": "_handlePhoneSignIn", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 546, "character": 16}], "signatures": [{"id": 402, "name": "_handlePhoneSignIn", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 403, "name": "phone", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 404, "name": "password", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 405, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 406, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 407, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 550, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [407]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 408, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 409, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 555, "character": 35}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 412, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 411, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 555, "character": 59}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 410, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 555, "character": 47}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}], "groups": [{"title": "Properties", "kind": 1024, "children": [409, 412, 411, 410]}]}}, {"type": "reflection", "declaration": {"id": 413, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 414, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 486, "name": "Session"}}, {"id": 417, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 562, "character": 53}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 416, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 562, "character": 38}], "type": {"type": "reference", "id": 486, "name": "Session"}, "defaultValue": "data"}, {"id": 415, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 562, "character": 21}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}, "defaultValue": "data.user"}], "groups": [{"title": "Properties", "kind": 1024, "children": [414, 417, 416, 415]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 418, "name": "_handleProviderSignIn", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 568, "character": 10}], "signatures": [{"id": 419, "name": "_handleProviderSignIn", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 420, "name": "provider", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 481, "name": "Provider", "dereferenced": {"id": 481, "name": "Provider", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 1, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "apple"}, {"type": "literal", "value": "azure"}, {"type": "literal", "value": "bitbucket"}, {"type": "literal", "value": "discord"}, {"type": "literal", "value": "facebook"}, {"type": "literal", "value": "github"}, {"type": "literal", "value": "gitlab"}, {"type": "literal", "value": "google"}, {"type": "literal", "value": "keycloak"}, {"type": "literal", "value": "linkedin"}, {"type": "literal", "value": "notion"}, {"type": "literal", "value": "slack"}, {"type": "literal", "value": "spotify"}, {"type": "literal", "value": "twitch"}, {"type": "literal", "value": "twitter"}, {"type": "literal", "value": "workos"}]}}}}, {"id": 421, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 422, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 425, "name": "queryParams", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 573, "character": 6}], "type": {"type": "reflection", "declaration": {"id": 426, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 427, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 428, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 423, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 571, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 424, "name": "scopes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 572, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [425, 423, 424]}]}}, "defaultValue": "{}"}], "type": {"type": "union", "types": [{"type": "reflection", "declaration": {"id": 429, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 432, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 587, "character": 30}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 435, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 587, "character": 69}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 430, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "reference", "id": 481, "name": "Provider"}}, {"id": 433, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 587, "character": 42}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 431, "name": "url", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 434, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 587, "character": 57}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}], "groups": [{"title": "Properties", "kind": 1024, "children": [432, 435, 430, 433, 431, 434]}]}}, {"type": "reflection", "declaration": {"id": 436, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 437, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 591, "character": 15}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 440, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 591, "character": 54}], "type": {"type": "reference", "id": 483, "name": "ApiError"}, "defaultValue": "..."}, {"id": 441, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "undefined"}}, {"id": 439, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 591, "character": 39}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 442, "name": "url", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "undefined"}}, {"id": 438, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 591, "character": 27}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}], "groups": [{"title": "Properties", "kind": 1024, "children": [437, 440, 441, 439, 442, 438]}]}}]}}]}, {"id": 479, "name": "_handleVisibilityChange", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 800, "character": 10}], "signatures": [{"id": 480, "name": "_handleVisibilityChange", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "literal", "value": false}]}}]}, {"id": 477, "name": "_listenForMultiTabEvents", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 777, "character": 10}], "signatures": [{"id": 478, "name": "_listenForMultiTabEvents", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Listens for changes to LocalStorage and updates the current session."}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "literal", "value": false}]}}]}, {"id": 463, "name": "_notifyAllSubscribers", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 714, "character": 10}], "signatures": [{"id": 464, "name": "_notifyAllSubscribers", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 465, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 482, "name": "AuthChangeEvent", "dereferenced": {"id": 482, "name": "AuthChangeEvent", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 19, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "PASSWORD_RECOVERY"}, {"type": "literal", "value": "SIGNED_IN"}, {"type": "literal", "value": "SIGNED_OUT"}, {"type": "literal", "value": "TOKEN_REFRESHED"}, {"type": "literal", "value": "USER_UPDATED"}, {"type": "literal", "value": "USER_DELETED"}]}}}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 469, "name": "_persistSession", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 741, "character": 10}], "signatures": [{"id": 470, "name": "_persistSession", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 471, "name": "currentSession", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 486, "name": "Session", "dereferenced": {"id": 486, "name": "Session", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 488, "name": "access_token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 34, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 490, "name": "expires_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A timestamp of when the token will expire. Returned when a login is confirmed."}, "sources": [{"fileName": "src/lib/types.ts", "line": 42, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 489, "name": "expires_in", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds until the token expires (since it was issued). Returned when a login is confirmed."}, "sources": [{"fileName": "src/lib/types.ts", "line": 38, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 487, "name": "provider_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 33, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 491, "name": "refresh_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 43, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 492, "name": "token_type", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 44, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 493, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [488, 490, 489, 487, 491, 492, 493]}], "sources": [{"fileName": "src/lib/types.ts", "line": 32, "character": 17}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 452, "name": "_recoverAndRefresh", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 650, "character": 16}], "signatures": [{"id": 453, "name": "_recoverAndRefresh", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Recovers the session from LocalStorage and refreshes\nNote: this method is async to accommodate for AsyncStorage e.g. in React native."}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "literal", "value": null}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 450, "name": "_recoverSession", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 630, "character": 10}], "signatures": [{"id": 451, "name": "_recoverSession", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Attempts to get the session from LocalStorage\nNote: this should never be async (even for React Native), as we need it to return immediately in the constructor."}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "literal", "value": null}]}}]}, {"id": 472, "name": "_removeSession", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 746, "character": 16}], "signatures": [{"id": 473, "name": "_removeSession", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "void"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 466, "name": "_saveSession", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 722, "character": 10}], "signatures": [{"id": 467, "name": "_saveSession", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "set currentSession and currentUser\nprocess to _startAutoRefreshToken if possible"}, "parameters": [{"id": 468, "name": "session", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 486, "name": "Session", "dereferenced": {"id": 486, "name": "Session", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 488, "name": "access_token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 34, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 490, "name": "expires_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A timestamp of when the token will expire. Returned when a login is confirmed."}, "sources": [{"fileName": "src/lib/types.ts", "line": 42, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 489, "name": "expires_in", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds until the token expires (since it was issued). Returned when a login is confirmed."}, "sources": [{"fileName": "src/lib/types.ts", "line": 38, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 487, "name": "provider_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 33, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 491, "name": "refresh_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 43, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 492, "name": "token_type", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 44, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 493, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [488, 490, 489, 487, 491, 492, 493]}], "sources": [{"fileName": "src/lib/types.ts", "line": 32, "character": 17}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 474, "name": "_startAutoRefreshToken", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 757, "character": 10}], "signatures": [{"id": 475, "name": "_startAutoRefreshToken", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Clear and re-create refresh token timer"}, "parameters": [{"id": 476, "name": "value", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "time intervals in milliseconds\n"}, "type": {"type": "intrinsic", "name": "number"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 361, "name": "getSessionFromUrl", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 428, "character": 8}], "signatures": [{"id": 362, "name": "getSessionFromUrl", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Gets the session data from a URL string"}, "parameters": [{"id": 363, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 364, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 365, "name": "storeSession", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optionally store the session in the browser\n"}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 429, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [365]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 366, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 367, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 430, "character": 16}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 368, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 430, "character": 38}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [367, 368]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 373, "name": "onAuthStateChange", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 500, "character": 2}], "signatures": [{"id": 374, "name": "onAuthStateChange", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Receive a notification every time an auth event happens.", "returns": "A subscription object which can be used to unsubscribe itself.\n"}, "parameters": [{"id": 375, "name": "callback", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 376, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 377, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 378, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 482, "name": "AuthChangeEvent"}}, {"id": 379, "name": "session", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 380, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 381, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 501, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 549, "name": "Subscription"}]}}, {"id": 382, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 502, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [381, 382]}]}}}]}, {"id": 339, "name": "refreshSession", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 339, "character": 8}], "signatures": [{"id": 340, "name": "refreshSession", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Force refreshes the session including the user data in case it was updated in a different session."}, "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 341, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 342, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 340, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 344, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 342, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 343, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 341, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [342, 344, 343]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 337, "name": "session", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 332, "character": 2}], "signatures": [{"id": 338, "name": "session", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Returns the session data, if there is an active session."}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}]}, {"id": 358, "name": "setAuth", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 411, "character": 2}], "signatures": [{"id": 359, "name": "setAuth", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Overrides the JWT on the current client. The JWT will then be sent in all subsequent network requests."}, "parameters": [{"id": 360, "name": "access_token", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "a jwt access token\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 486, "name": "Session"}}]}, {"id": 352, "name": "setSession", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 387, "character": 8}], "signatures": [{"id": 353, "name": "setSession", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Sets the session data from refresh_token and returns current Session and Error"}, "parameters": [{"id": 354, "name": "refresh_token", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "a JWT token\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 355, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 357, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 389, "character": 40}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 356, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 389, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [357, 356]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 306, "name": "signIn", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 197, "character": 8}], "signatures": [{"id": 307, "name": "signIn", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Log in an existing user, or login via a third-party provider."}, "parameters": [{"id": 308, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 563, "name": "UserCredentials", "dereferenced": {"id": 563, "name": "UserCredentials", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 564, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 182, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 569, "name": "oidc", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 188, "character": 2}], "type": {"type": "reference", "id": 583, "name": "OpenIDConnectCredentials"}}, {"id": 566, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 184, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 565, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 183, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 568, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 187, "character": 2}], "type": {"type": "reference", "id": 481, "name": "Provider"}}, {"id": 567, "name": "refreshToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 185, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [564, 569, 566, 565, 568, 567]}], "sources": [{"fileName": "src/lib/types.ts", "line": 181, "character": 17}]}}}, {"id": 309, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 310, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 314, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 203, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 315, "name": "queryParams", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 204, "character": 6}], "type": {"type": "reflection", "declaration": {"id": 316, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 317, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 318, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 311, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 200, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 313, "name": "scopes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 202, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 312, "name": "shouldCreateUser", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 201, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [314, 315, 311, 313, 312]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 319, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 324, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 211, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 322, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 209, "character": 4}], "type": {"type": "reference", "id": 481, "name": "Provider"}}, {"id": 320, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 207, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 323, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 210, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 321, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 208, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [324, 322, 320, 323, 321]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 369, "name": "signOut", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 485, "character": 8}], "signatures": [{"id": 370, "name": "signOut", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Inside a browser context, `signOut()` will remove the logged in user from the browser session\nand log them out - removing all items from localstorage and then trigger a \"SIGNED_OUT\" event.", "text": "For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`. There is no way to revoke a user's session JWT before it automatically expires\n"}, "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 371, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 372, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 485, "character": 29}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [372]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 294, "name": "signUp", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 130, "character": 8}], "signatures": [{"id": 295, "name": "signUp", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a new user."}, "parameters": [{"id": 296, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 563, "name": "UserCredentials", "dereferenced": {"id": 563, "name": "UserCredentials", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 564, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 182, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 569, "name": "oidc", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 188, "character": 2}], "type": {"type": "reference", "id": 583, "name": "OpenIDConnectCredentials"}}, {"id": 566, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 184, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 565, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 183, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 568, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 187, "character": 2}], "type": {"type": "reference", "id": 481, "name": "Provider"}}, {"id": 567, "name": "refreshToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 185, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [564, 569, 566, 565, 568, 567]}], "sources": [{"fileName": "src/lib/types.ts", "line": 181, "character": 17}]}}}, {"id": 297, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 298, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 301, "name": "captchaToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 135, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 300, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 134, "character": 6}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 299, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 133, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [301, 300, 299]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 302, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 305, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 140, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 304, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 139, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 303, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 138, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [305, 304, 303]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 345, "name": "update", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 360, "character": 8}], "signatures": [{"id": 346, "name": "update", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates user data, if there is a logged in user."}, "parameters": [{"id": 347, "name": "attributes", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 533, "name": "UserAttributes", "dereferenced": {"id": 533, "name": "UserAttributes", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 538, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata that a user can modify. Can be any JSON."}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 534, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's email."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 537, "name": "email_change_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "An email change token."}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 536, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's password."}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 535, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's phone."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [538, 534, 537, 536, 535]}], "sources": [{"fileName": "src/lib/types.ts", "line": 88, "character": 17}], "extendedBy": [{"type": "reference", "id": 539, "name": "AdminUserAttributes"}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 348, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 349, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 362, "character": 15}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}, {"id": 351, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 362, "character": 53}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 350, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 362, "character": 34}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [349, 351, 350]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 335, "name": "user", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 325, "character": 2}], "signatures": [{"id": 336, "name": "user", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Inside a browser context, `user()` will return the user data, if there is a logged in user.", "text": "For server-side management, you can get a user through `auth.api.getUserByCookie()`\n"}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}]}, {"id": 325, "name": "verifyOTP", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 277, "character": 8}], "signatures": [{"id": 326, "name": "verifyOTP", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Log in a user given a User supplied OTP received via mobile."}, "parameters": [{"id": 327, "name": "params", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 570, "name": "VerifyOTPParams", "dereferenced": {"id": 570, "name": "VerifyOTPParams", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 191, "character": 12}], "type": {"type": "union", "types": [{"type": "reference", "id": 571, "name": "VerifyMobileOTPParams"}, {"type": "reference", "id": 576, "name": "VerifyEmailOTPParams"}]}}}}, {"id": 328, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 329, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 330, "name": "redirectTo", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 280, "character": 6}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [330]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 331, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 334, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 285, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 483, "name": "ApiError"}]}}, {"id": 333, "name": "session", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 284, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}, {"id": 332, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/GoTrueClient.ts", "line": 283, "character": 4}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [334, 333, 332]}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [264]}, {"title": "Properties", "kind": 1024, "children": [284, 287, 286, 285, 289, 290, 293, 288, 292, 291]}, {"title": "Methods", "kind": 2048, "children": [454, 383, 443, 401, 418, 479, 477, 463, 469, 452, 450, 472, 466, 474, 361, 373, 339, 337, 358, 352, 306, 369, 294, 345, 335, 325]}], "sources": [{"fileName": "src/GoTrueClient.ts", "line": 47, "character": 21}]}, {"id": 539, "name": "AdminUserAttributes", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 541, "name": "app_metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for app_metadata that.", "text": "Only a service role can modify.\n\nCan be any JSON that includes app-specific info, such as identity providers, roles, and other\naccess control information.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 137, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 548, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata that a user can modify. Can be any JSON."}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 2}], "type": {"type": "intrinsic", "name": "object"}, "inheritedFrom": {"type": "reference", "id": 538, "name": "UserAttributes.data"}}, {"id": 544, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's email."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 534, "name": "UserAttributes.email"}}, {"id": 547, "name": "email_change_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "An email change token."}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 537, "name": "UserAttributes.email_change_token"}}, {"id": 542, "name": "email_confirm", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Sets if a user has confirmed their email address.", "text": "Only a service role can modify.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 144, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 546, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's password."}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 536, "name": "UserAttributes.password"}}, {"id": 545, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's phone."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "inheritedFrom": {"type": "reference", "id": 535, "name": "UserAttributes.phone"}}, {"id": 543, "name": "phone_confirm", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Sets if a user has confirmed their phone number.", "text": "Only a service role can modify.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 151, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 540, "name": "user_metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata.", "text": "Can be any JSON.\n\nOnly a service role can modify.\n\nNote: When using the GoTrueAdminApi and wanting to modify a user's user_metadata,\nthis attribute is used instead of UserAttributes data.\n\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [541, 548, 544, 547, 542, 546, 545, 543, 540]}], "sources": [{"fileName": "src/lib/types.ts", "line": 115, "character": 17}], "extendedTypes": [{"type": "reference", "id": 533, "name": "UserAttributes"}]}, {"id": 483, "name": "ApiError", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 484, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 28, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 485, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 29, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [484, 485]}], "sources": [{"fileName": "src/lib/types.ts", "line": 27, "character": 17}]}, {"id": 557, "name": "CookieOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 560, "name": "domain", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 175, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 559, "name": "lifetime", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 173, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 558, "name": "name", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 171, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 561, "name": "path", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 176, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 562, "name": "sameSite", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 178, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [560, 559, 558, 561, 562]}], "sources": [{"fileName": "src/lib/types.ts", "line": 169, "character": 17}]}, {"id": 583, "name": "OpenIDConnectCredentials", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 587, "name": "client_id", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 211, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 584, "name": "id_token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 208, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 588, "name": "issuer", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 212, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 585, "name": "nonce", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 209, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 586, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 210, "character": 2}], "type": {"type": "reference", "id": 481, "name": "Provider"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [587, 584, 588, 585, 586]}], "sources": [{"fileName": "src/lib/types.ts", "line": 207, "character": 17}]}, {"id": 486, "name": "Session", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 488, "name": "access_token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 34, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 490, "name": "expires_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A timestamp of when the token will expire. Returned when a login is confirmed."}, "sources": [{"fileName": "src/lib/types.ts", "line": 42, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 489, "name": "expires_in", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds until the token expires (since it was issued). Returned when a login is confirmed."}, "sources": [{"fileName": "src/lib/types.ts", "line": 38, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 487, "name": "provider_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 33, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 491, "name": "refresh_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 43, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 492, "name": "token_type", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 44, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 493, "name": "user", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 505, "name": "User"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [488, 490, 489, 487, 491, 492, 493]}], "sources": [{"fileName": "src/lib/types.ts", "line": 32, "character": 17}]}, {"id": 549, "name": "Subscription", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 550, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "The subscriber UUID. This will be set by the client."}, "sources": [{"fileName": "src/lib/types.ts", "line": 158, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 551, "name": "callback", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 162, "character": 2}], "signatures": [{"id": 552, "name": "callback", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "The function to call every time there is an event. eg: (eventName) => {}"}, "parameters": [{"id": 553, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 482, "name": "AuthChangeEvent", "dereferenced": {"id": 482, "name": "AuthChangeEvent", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 19, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "PASSWORD_RECOVERY"}, {"type": "literal", "value": "SIGNED_IN"}, {"type": "literal", "value": "SIGNED_OUT"}, {"type": "literal", "value": "TOKEN_REFRESHED"}, {"type": "literal", "value": "USER_UPDATED"}, {"type": "literal", "value": "USER_DELETED"}]}}}}, {"id": 554, "name": "session", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "id": 486, "name": "Session"}]}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 555, "name": "unsubscribe", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 166, "character": 2}], "signatures": [{"id": 556, "name": "unsubscribe", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Call this to remove the listener."}, "type": {"type": "intrinsic", "name": "void"}}]}], "groups": [{"title": "Properties", "kind": 1024, "children": [550]}, {"title": "Methods", "kind": 2048, "children": [551, 555]}], "sources": [{"fileName": "src/lib/types.ts", "line": 154, "character": 17}]}, {"id": 505, "name": "User", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 522, "name": "action_link", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 75, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 507, "name": "app_metadata", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 62, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 508, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 509, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 63, "character": 4}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [509]}], "indexSignature": {"id": 510, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 511, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}}}}, {"id": 516, "name": "aud", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 69, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 517, "name": "confirmation_sent_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 70, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 526, "name": "confirmed_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 79, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 525, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 78, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 523, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 76, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 519, "name": "email_change_sent_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 72, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 527, "name": "email_confirmed_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 80, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 506, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 61, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 532, "name": "identities", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 85, "character": 2}], "type": {"type": "array", "elementType": {"type": "reference", "id": 494, "name": "UserIdentity"}}}, {"id": 521, "name": "invited_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 74, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 529, "name": "last_sign_in_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 82, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 520, "name": "new_email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 73, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 524, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 77, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 528, "name": "phone_confirmed_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 81, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 518, "name": "recovery_sent_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 71, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 530, "name": "role", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 83, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 531, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 84, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 512, "name": "user_metadata", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 66, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 513, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 514, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 515, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [522, 507, 516, 517, 526, 525, 523, 519, 527, 506, 532, 521, 529, 520, 524, 528, 518, 530, 531, 512]}], "sources": [{"fileName": "src/lib/types.ts", "line": 60, "character": 17}]}, {"id": 533, "name": "UserAttributes", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 538, "name": "data", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom data object for user_metadata that a user can modify. Can be any JSON."}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 2}], "type": {"type": "intrinsic", "name": "object"}}, {"id": 534, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's email."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 537, "name": "email_change_token", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "An email change token."}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 536, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's password."}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 535, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The user's phone."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [538, 534, 537, 536, 535]}], "sources": [{"fileName": "src/lib/types.ts", "line": 88, "character": 17}], "extendedBy": [{"type": "reference", "id": 539, "name": "AdminUserAttributes"}]}, {"id": 563, "name": "UserCredentials", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 564, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 182, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 569, "name": "oidc", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 188, "character": 2}], "type": {"type": "reference", "id": 583, "name": "OpenIDConnectCredentials"}}, {"id": 566, "name": "password", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 184, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 565, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 183, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 568, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 187, "character": 2}], "type": {"type": "reference", "id": 481, "name": "Provider"}}, {"id": 567, "name": "refreshToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 185, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [564, 569, 566, 565, 568, 567]}], "sources": [{"fileName": "src/lib/types.ts", "line": 181, "character": 17}]}, {"id": 494, "name": "UserIdentity", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 502, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 55, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 495, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 49, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 497, "name": "identity_data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 51, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 498, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 499, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 500, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}}}}, {"id": 503, "name": "last_sign_in_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 56, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 501, "name": "provider", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 54, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 504, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 57, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 496, "name": "user_id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 50, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [502, 495, 497, 503, 501, 504, 496]}], "sources": [{"fileName": "src/lib/types.ts", "line": 48, "character": 17}]}, {"id": 576, "name": "VerifyEmailOTPParams", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 577, "name": "email", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 199, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 578, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 200, "character": 2}], "type": {"type": "intrinsic", "name": "undefined"}}, {"id": 579, "name": "token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 201, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 580, "name": "type", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 202, "character": 2}], "type": {"type": "reference", "id": 582, "name": "EmailOTPType"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [577, 578, 579, 580]}], "sources": [{"fileName": "src/lib/types.ts", "line": 198, "character": 17}]}, {"id": 571, "name": "VerifyMobileOTPParams", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 572, "name": "email", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 193, "character": 2}], "type": {"type": "intrinsic", "name": "undefined"}}, {"id": 573, "name": "phone", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 194, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 574, "name": "token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 195, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 575, "name": "type", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 196, "character": 2}], "type": {"type": "reference", "id": 581, "name": "MobileOTPType"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [572, 573, 574, 575]}], "sources": [{"fileName": "src/lib/types.ts", "line": 192, "character": 17}]}, {"id": 482, "name": "AuthChangeEvent", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 19, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "PASSWORD_RECOVERY"}, {"type": "literal", "value": "SIGNED_IN"}, {"type": "literal", "value": "SIGNED_OUT"}, {"type": "literal", "value": "TOKEN_REFRESHED"}, {"type": "literal", "value": "USER_UPDATED"}, {"type": "literal", "value": "USER_DELETED"}]}}, {"id": 582, "name": "EmailOTPType", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 205, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "signup"}, {"type": "literal", "value": "invite"}, {"type": "literal", "value": "magiclink"}, {"type": "literal", "value": "recovery"}, {"type": "literal", "value": "email_change"}]}}, {"id": 581, "name": "MobileOTPType", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 204, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "sms"}, {"type": "literal", "value": "phone_change"}]}}, {"id": 481, "name": "Provider", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 1, "character": 12}], "type": {"type": "union", "types": [{"type": "literal", "value": "apple"}, {"type": "literal", "value": "azure"}, {"type": "literal", "value": "bitbucket"}, {"type": "literal", "value": "discord"}, {"type": "literal", "value": "facebook"}, {"type": "literal", "value": "github"}, {"type": "literal", "value": "gitlab"}, {"type": "literal", "value": "google"}, {"type": "literal", "value": "keycloak"}, {"type": "literal", "value": "linkedin"}, {"type": "literal", "value": "notion"}, {"type": "literal", "value": "slack"}, {"type": "literal", "value": "spotify"}, {"type": "literal", "value": "twitch"}, {"type": "literal", "value": "twitter"}, {"type": "literal", "value": "workos"}]}}, {"id": 589, "name": "SupportedStorage", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 224, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Storage", "package": "typescript", "name": "Storage"}, {"type": "union", "types": [{"type": "literal", "value": "getItem"}, {"type": "literal", "value": "setItem"}, {"type": "literal", "value": "removeItem"}]}], "qualifiedName": "Pick", "package": "typescript", "name": "Pick"}], "name": "PromisifyMethods"}}, {"id": 570, "name": "VerifyOTPParams", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 191, "character": 12}], "type": {"type": "union", "types": [{"type": "reference", "id": 571, "name": "VerifyMobileOTPParams"}, {"type": "reference", "id": 576, "name": "VerifyEmailOTPParams"}]}}], "groups": [{"title": "Classes", "kind": 128, "children": [1, 263]}, {"title": "Interfaces", "kind": 256, "children": [539, 483, 557, 583, 486, 549, 505, 533, 563, 494, 576, 571]}, {"title": "Type Aliases", "kind": 4194304, "children": [482, 582, 581, 481, 589, 570]}], "sources": [{"fileName": "src/index.ts", "line": 1, "character": 0}]}