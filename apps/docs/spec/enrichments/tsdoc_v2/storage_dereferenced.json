{"id": 0, "name": "@supabase/storage-js", "kind": 1, "flags": {}, "originalName": "", "children": [{"id": 1, "name": "index", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 498, "name": "StorageApiError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 499, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 17, "character": 2}], "signatures": [{"id": 500, "name": "new StorageApiError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 501, "name": "message", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 502, "name": "status", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "number"}}], "type": {"type": "reference", "id": 498, "name": "StorageApiError"}, "overwrites": {"type": "reference", "id": 495, "name": "StorageError.constructor"}}], "overwrites": {"type": "reference", "id": 494, "name": "StorageError.constructor"}}, {"id": 503, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 15, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 504, "name": "toJSON", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 23, "character": 2}], "signatures": [{"id": 505, "name": "toJSON", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 506, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 508, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 26, "character": 6}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "..."}, {"id": 507, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 25, "character": 6}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "..."}, {"id": 509, "name": "status", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 27, "character": 6}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "..."}], "groups": [{"title": "Properties", "kind": 1024, "children": [508, 507, 509]}]}}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [499]}, {"title": "Properties", "kind": 1024, "children": [503]}, {"title": "Methods", "kind": 2048, "children": [504]}], "sources": [{"fileName": "src/lib/errors.ts", "line": 14, "character": 13}], "extendedTypes": [{"type": "reference", "id": 493, "name": "StorageError"}]}, {"id": 333, "name": "StorageClient", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 334, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/StorageClient.ts", "line": 6, "character": 2}], "signatures": [{"id": 335, "name": "new StorageClient", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 336, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 337, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 338, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 339, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 340, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}, "defaultValue": "{}"}, {"id": 341, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 342, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 343, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 344, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}}, {"id": 345, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}], "type": {"type": "reference", "id": 333, "name": "StorageClient"}, "overwrites": {"type": "reference", "id": 5, "name": "default.constructor"}}], "overwrites": {"type": "reference", "id": 4, "name": "default.constructor"}}, {"id": 376, "name": "createBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 85, "character": 8}], "signatures": [{"id": 377, "name": "createBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a new Storage bucket", "returns": "newly created bucket id\n"}, "parameters": [{"id": 378, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A unique identifier for the bucket you are creating."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 379, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 380, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 383, "name": "allowedMimeTypes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the allowed mime types that this bucket can accept during upload.\nThe default value is null, which allows files with all mime types to be uploaded.\nEach mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 90, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}]}}, {"id": 382, "name": "fileSizeLimit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the max file size in bytes that can be uploaded to this bucket.\nThe global file size limit takes precedence over this value.\nThe default value is null, which doesn't set a per bucket file size limit."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 89, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 381, "name": "public", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 88, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [383, 382, 381]}]}}, "defaultValue": "..."}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 384, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 385, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 96, "character": 8}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 428, "name": "Bucket"}, {"type": "literal", "value": "name"}], "qualifiedName": "Pick", "package": "typescript", "name": "Pick"}}, {"id": 386, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 97, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [385, 386]}]}}, {"type": "reflection", "declaration": {"id": 387, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 388, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 100, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 389, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 101, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [388, 389]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 44, "name": "default.createBucket"}}], "inheritedFrom": {"type": "reference", "id": 43, "name": "default.createBucket"}}, {"id": 417, "name": "deleteBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 219, "character": 8}], "signatures": [{"id": 418, "name": "deleteBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\nYou must first `empty()` the bucket."}, "parameters": [{"id": 419, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique identifier of the bucket you would like to delete.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 420, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 421, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 223, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 422, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 423, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 223, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [423]}]}}}, {"id": 424, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 224, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [421, 424]}]}}, {"type": "reflection", "declaration": {"id": 425, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 426, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 227, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 427, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 228, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [426, 427]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 85, "name": "default.deleteBucket"}}], "inheritedFrom": {"type": "reference", "id": 84, "name": "default.deleteBucket"}}, {"id": 406, "name": "emptyBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 184, "character": 8}], "signatures": [{"id": 407, "name": "emptyBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Removes all objects inside a single bucket."}, "parameters": [{"id": 408, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique identifier of the bucket you would like to empty.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 409, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 410, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 188, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 411, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 412, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 188, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [412]}]}}}, {"id": 413, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 189, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [410, 413]}]}}, {"type": "reflection", "declaration": {"id": 414, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 415, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 192, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 416, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 193, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [415, 416]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 74, "name": "default.emptyBucket"}}], "inheritedFrom": {"type": "reference", "id": 73, "name": "default.emptyBucket"}}, {"id": 346, "name": "from", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/StorageClient.ts", "line": 15, "character": 2}], "signatures": [{"id": 347, "name": "from", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Perform file operation in a bucket."}, "parameters": [{"id": 348, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The bucket id to operate on.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 96, "name": "default"}}]}, {"id": 367, "name": "getBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 48, "character": 8}], "signatures": [{"id": 368, "name": "getBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Retrieves the details of an existing Storage bucket."}, "parameters": [{"id": 369, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique identifier of the bucket you would like to retrieve.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 370, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 371, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 52, "character": 8}], "type": {"type": "reference", "id": 428, "name": "Bucket"}}, {"id": 372, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 53, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [371, 372]}]}}, {"type": "reflection", "declaration": {"id": 373, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 374, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 56, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 375, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 57, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [374, 375]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 35, "name": "default.getBucket"}}], "inheritedFrom": {"type": "reference", "id": 34, "name": "default.getBucket"}}, {"id": 359, "name": "listBuckets", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 21, "character": 8}], "signatures": [{"id": 360, "name": "listBuckets", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Retrieves the details of all Storage buckets within an existing project."}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 361, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 362, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 23, "character": 8}], "type": {"type": "array", "elementType": {"type": "reference", "id": 428, "name": "Bucket"}}}, {"id": 363, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 24, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [362, 363]}]}}, {"type": "reflection", "declaration": {"id": 364, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 365, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 27, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 366, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 28, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [365, 366]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 27, "name": "default.listBuckets"}}], "inheritedFrom": {"type": "reference", "id": 26, "name": "default.listBuckets"}}, {"id": 390, "name": "updateBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 139, "character": 8}], "signatures": [{"id": 391, "name": "updateBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates a Storage bucket"}, "parameters": [{"id": 392, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A unique identifier for the bucket you are updating."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 393, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 394, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 397, "name": "allowedMimeTypes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the allowed mime types that this bucket can accept during upload.\nThe default value is null, which allows files with all mime types to be uploaded.\nEach mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n"}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 144, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}]}}, {"id": 396, "name": "fileSizeLimit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the max file size in bytes that can be uploaded to this bucket.\nThe global file size limit takes precedence over this value.\nThe default value is null, which doesn't set a per bucket file size limit."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 143, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 395, "name": "public", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 142, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [397, 396, 395]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 398, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 399, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 148, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 400, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 401, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 148, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [401]}]}}}, {"id": 402, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 149, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [399, 402]}]}}, {"type": "reflection", "declaration": {"id": 403, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 404, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 152, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 405, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 153, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [404, 405]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}, "inheritedFrom": {"type": "reference", "id": 58, "name": "default.updateBucket"}}], "inheritedFrom": {"type": "reference", "id": 57, "name": "default.updateBucket"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [334]}, {"title": "Methods", "kind": 2048, "children": [376, 417, 406, 346, 367, 359, 390]}], "sources": [{"fileName": "src/StorageClient.ts", "line": 5, "character": 13}], "extendedTypes": [{"type": "reference", "id": 3, "name": "default"}]}, {"id": 493, "name": "StorageError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 494, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 4, "character": 2}], "signatures": [{"id": 495, "name": "new StorageError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 496, "name": "message", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "id": 493, "name": "StorageError"}, "overwrites": {"type": "reference", "name": "Error.constructor"}}], "overwrites": {"type": "reference", "name": "Error.constructor"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [494]}], "sources": [{"fileName": "src/lib/errors.ts", "line": 1, "character": 13}], "extendedTypes": [{"type": "reference", "qualifiedName": "Error", "package": "typescript", "name": "Error"}], "extendedBy": [{"type": "reference", "id": 498, "name": "StorageApiError"}, {"type": "reference", "id": 511, "name": "StorageUnknownError"}]}, {"id": 511, "name": "StorageUnknownError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 512, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 35, "character": 2}], "signatures": [{"id": 513, "name": "new StorageUnknownError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 514, "name": "message", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 515, "name": "originalError", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "unknown"}}], "type": {"type": "reference", "id": 511, "name": "StorageUnknownError"}, "overwrites": {"type": "reference", "id": 495, "name": "StorageError.constructor"}}], "overwrites": {"type": "reference", "id": 494, "name": "StorageError.constructor"}}, {"id": 516, "name": "originalError", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 33, "character": 2}], "type": {"type": "intrinsic", "name": "unknown"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [512]}, {"title": "Properties", "kind": 1024, "children": [516]}], "sources": [{"fileName": "src/lib/errors.ts", "line": 32, "character": 13}], "extendedTypes": [{"type": "reference", "id": 493, "name": "StorageError"}]}, {"id": 428, "name": "Bucket", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 433, "name": "allowed_mime_types", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 6, "character": 2}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 434, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 7, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 432, "name": "file_size_limit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 5, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 429, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 2, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 430, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 3, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 431, "name": "owner", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 4, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 436, "name": "public", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 9, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 435, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 8, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [433, 434, 432, 429, 430, 431, 436, 435]}], "sources": [{"fileName": "src/lib/types.ts", "line": 1, "character": 17}]}, {"id": 471, "name": "DestinationOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 472, "name": "destinationBucket", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 75, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [472]}], "sources": [{"fileName": "src/lib/types.ts", "line": 74, "character": 17}]}, {"id": 478, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 479, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Pass in an AbortController's signal to cancel the request."}, "sources": [{"fileName": "src/lib/types.ts", "line": 104, "character": 2}], "type": {"type": "reference", "qualifiedName": "AbortSignal", "package": "typescript", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [479]}], "sources": [{"fileName": "src/lib/types.ts", "line": 100, "character": 17}]}, {"id": 437, "name": "FileObject", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 439, "name": "bucket_id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 14, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 446, "name": "buckets", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 21, "character": 2}], "type": {"type": "reference", "id": 428, "name": "Bucket", "dereferenced": {"id": 428, "name": "Bucket", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 433, "name": "allowed_mime_types", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 6, "character": 2}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 434, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 7, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 432, "name": "file_size_limit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 5, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 429, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 2, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 430, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 3, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 431, "name": "owner", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 4, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 436, "name": "public", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 9, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 435, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 8, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [433, 434, 432, 429, 430, 431, 436, 435]}], "sources": [{"fileName": "src/lib/types.ts", "line": 1, "character": 17}]}}}, {"id": 443, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 18, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 441, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 16, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 444, "name": "last_accessed_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 19, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 445, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 20, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 438, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 13, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 440, "name": "owner", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 15, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 442, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 17, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [439, 446, 443, 441, 444, 445, 438, 440, 442]}], "sources": [{"fileName": "src/lib/types.ts", "line": 12, "character": 17}]}, {"id": 447, "name": "FileObjectV2", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 451, "name": "bucket_id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 28, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 456, "name": "cache_control", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 33, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 457, "name": "content_type", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 34, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 453, "name": "created_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 30, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 458, "name": "etag", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 35, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 448, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 25, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 454, "name": "last_accessed_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 31, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 459, "name": "last_modified", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 36, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 460, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 37, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 450, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 27, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 455, "name": "size", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 32, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 452, "name": "updated_at", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 29, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 449, "name": "version", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 26, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [451, 456, 457, 453, 458, 448, 454, 459, 460, 450, 455, 452, 449]}], "sources": [{"fileName": "src/lib/types.ts", "line": 24, "character": 17}]}, {"id": 464, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 465, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds."}, "sources": [{"fileName": "src/lib/types.ts", "line": 49, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 466, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`."}, "sources": [{"fileName": "src/lib/types.ts", "line": 53, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 468, "name": "duplex", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The duplex option is a string parameter that enables or disables duplex streaming, allowing for both reading and writing data in the same stream. It can be passed as an option to the fetch() method."}, "sources": [{"fileName": "src/lib/types.ts", "line": 61, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 470, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optionally add extra headers"}, "sources": [{"fileName": "src/lib/types.ts", "line": 71, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 469, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The metadata option is an object that allows you to store additional information about the file. This information can be used to filter and search for files. The metadata object can contain any key-value pairs you want to store."}, "sources": [{"fileName": "src/lib/types.ts", "line": 66, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 467, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false."}, "sources": [{"fileName": "src/lib/types.ts", "line": 57, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [465, 466, 468, 470, 469, 467]}], "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 17}]}, {"id": 480, "name": "<PERSON><PERSON><PERSON>", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 481, "name": "name", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 109, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [481]}], "sources": [{"fileName": "src/lib/types.ts", "line": 108, "character": 17}]}, {"id": 473, "name": "SearchOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 474, "name": "limit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": " The number of files you want to be returned."}, "sources": [{"fileName": "src/lib/types.ts", "line": 82, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 475, "name": "offset", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The starting position."}, "sources": [{"fileName": "src/lib/types.ts", "line": 87, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 477, "name": "search", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The search string to filter files by."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 476, "name": "sortBy", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The column to sort by. Can be any column inside a FileObject."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "reference", "id": 461, "name": "SortBy", "dereferenced": {"id": 461, "name": "SortBy", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 462, "name": "column", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 41, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 463, "name": "order", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 42, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [462, 463]}], "sources": [{"fileName": "src/lib/types.ts", "line": 40, "character": 17}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [474, 475, 477, 476]}], "sources": [{"fileName": "src/lib/types.ts", "line": 78, "character": 17}]}, {"id": 461, "name": "SortBy", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 462, "name": "column", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 41, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 463, "name": "order", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 42, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [462, 463]}], "sources": [{"fileName": "src/lib/types.ts", "line": 40, "character": 17}]}, {"id": 482, "name": "TransformOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 487, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Specify the format of the image requested.", "text": "When using 'origin' we force the format to be the same as the original image.\nWhen this option is not passed in, images are optimized to modern image formats like Webp.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 140, "character": 2}], "type": {"type": "literal", "value": "origin"}}, {"id": 484, "name": "height", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The height of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 120, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 486, "name": "quality", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set the quality of the returned image.\nA number from 20 to 100, with 100 being the highest quality.\nDefaults to 80"}, "sources": [{"fileName": "src/lib/types.ts", "line": 133, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 485, "name": "resize", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The resize mode can be cover, contain or fill. Defaults to cover.\nCover resizes the image to maintain it's aspect ratio while filling the entire width and height.\nContain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height.\nFill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit."}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": "cover"}, {"type": "literal", "value": "contain"}, {"type": "literal", "value": "fill"}]}}, {"id": 483, "name": "width", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The width of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 116, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [487, 484, 486, 485, 483]}], "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 17}]}, {"id": 488, "name": "Came<PERSON>ze", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 147, "character": 12}], "typeParameter": [{"id": 489, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "type": {"type": "mapped", "parameter": "K", "parameterType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 489, "name": "T"}}, "templateType": {"type": "indexedAccess", "indexType": {"type": "reference", "name": "K"}, "objectType": {"type": "reference", "id": 489, "name": "T"}}, "nameType": {"type": "reference", "typeArguments": [{"type": "reference", "typeArguments": [{"type": "reference", "name": "K"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Extract", "package": "typescript", "name": "Extract"}], "name": "CamelCase"}}}, {"id": 490, "name": "isStorageError", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/errors.ts", "line": 10, "character": 16}], "signatures": [{"id": 491, "name": "isStorageError", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 492, "name": "error", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "unknown"}}], "type": {"type": "predicate", "name": "error", "asserts": false, "targetType": {"type": "reference", "id": 493, "name": "StorageError"}}}]}], "groups": [{"title": "Classes", "kind": 128, "children": [498, 333, 493, 511]}, {"title": "Interfaces", "kind": 256, "children": [428, 471, 478, 437, 447, 464, 480, 473, 461, 482]}, {"title": "Type Aliases", "kind": 4194304, "children": [488]}, {"title": "Functions", "kind": 64, "children": [490]}], "sources": [{"fileName": "src/index.ts", "line": 1, "character": 0}]}, {"id": 2, "name": "packages/StorageBucketApi", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 3, "name": "default", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 4, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 12, "character": 2}], "signatures": [{"id": 5, "name": "new default", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 6, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 7, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 8, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 9, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 10, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}, "defaultValue": "{}"}, {"id": 11, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 12, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 13, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 14, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}}, {"id": 15, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}], "type": {"type": "reference", "id": 3, "name": "default"}}]}, {"id": 43, "name": "createBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 85, "character": 8}], "signatures": [{"id": 44, "name": "createBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a new Storage bucket", "returns": "newly created bucket id\n"}, "parameters": [{"id": 45, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A unique identifier for the bucket you are creating."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 46, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 47, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 50, "name": "allowedMimeTypes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the allowed mime types that this bucket can accept during upload.\nThe default value is null, which allows files with all mime types to be uploaded.\nEach mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 90, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}]}}, {"id": 49, "name": "fileSizeLimit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the max file size in bytes that can be uploaded to this bucket.\nThe global file size limit takes precedence over this value.\nThe default value is null, which doesn't set a per bucket file size limit."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 89, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 48, "name": "public", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 88, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [50, 49, 48]}]}}, "defaultValue": "..."}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 51, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 52, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 96, "character": 8}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 428, "name": "Bucket"}, {"type": "literal", "value": "name"}], "qualifiedName": "Pick", "package": "typescript", "name": "Pick"}}, {"id": 53, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 97, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [52, 53]}]}}, {"type": "reflection", "declaration": {"id": 54, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 55, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 100, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 56, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 101, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [55, 56]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 84, "name": "deleteBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 219, "character": 8}], "signatures": [{"id": 85, "name": "deleteBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\nYou must first `empty()` the bucket."}, "parameters": [{"id": 86, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique identifier of the bucket you would like to delete.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 87, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 88, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 223, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 89, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 90, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 223, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [90]}]}}}, {"id": 91, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 224, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [88, 91]}]}}, {"type": "reflection", "declaration": {"id": 92, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 93, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 227, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 94, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 228, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [93, 94]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 73, "name": "emptyBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 184, "character": 8}], "signatures": [{"id": 74, "name": "emptyBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Removes all objects inside a single bucket."}, "parameters": [{"id": 75, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique identifier of the bucket you would like to empty.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 76, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 77, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 188, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 78, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 79, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 188, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [79]}]}}}, {"id": 80, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 189, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [77, 80]}]}}, {"type": "reflection", "declaration": {"id": 81, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 82, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 192, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 83, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 193, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [82, 83]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 34, "name": "getBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 48, "character": 8}], "signatures": [{"id": 35, "name": "getBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Retrieves the details of an existing Storage bucket."}, "parameters": [{"id": 36, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique identifier of the bucket you would like to retrieve.\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 37, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 38, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 52, "character": 8}], "type": {"type": "reference", "id": 428, "name": "Bucket"}}, {"id": 39, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 53, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [38, 39]}]}}, {"type": "reflection", "declaration": {"id": 40, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 41, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 56, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 42, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 57, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [41, 42]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 26, "name": "listBuckets", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 21, "character": 8}], "signatures": [{"id": 27, "name": "listBuckets", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Retrieves the details of all Storage buckets within an existing project."}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 28, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 29, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 23, "character": 8}], "type": {"type": "array", "elementType": {"type": "reference", "id": 428, "name": "Bucket"}}}, {"id": 30, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 24, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [29, 30]}]}}, {"type": "reflection", "declaration": {"id": 31, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 32, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 27, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 33, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 28, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [32, 33]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 57, "name": "updateBucket", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 139, "character": 8}], "signatures": [{"id": 58, "name": "updateBucket", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates a Storage bucket"}, "parameters": [{"id": 59, "name": "id", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "A unique identifier for the bucket you are updating."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 60, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 61, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 64, "name": "allowedMimeTypes", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the allowed mime types that this bucket can accept during upload.\nThe default value is null, which allows files with all mime types to be uploaded.\nEach mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n"}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 144, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}]}}, {"id": 63, "name": "fileSizeLimit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "specifies the max file size in bytes that can be uploaded to this bucket.\nThe global file size limit takes precedence over this value.\nThe default value is null, which doesn't set a per bucket file size limit."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 143, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "number"}]}}, {"id": 62, "name": "public", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations."}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 142, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [64, 63, 62]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 65, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 66, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 148, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 67, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 68, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 148, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [68]}]}}}, {"id": 69, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 149, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [66, 69]}]}}, {"type": "reflection", "declaration": {"id": 70, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 71, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 152, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 72, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 153, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [71, 72]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [4]}, {"title": "Methods", "kind": 2048, "children": [43, 84, 73, 34, 26, 57]}], "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 7, "character": 21}], "extendedBy": [{"type": "reference", "id": 333, "name": "StorageClient"}]}], "groups": [{"title": "Classes", "kind": 128, "children": [3]}], "sources": [{"fileName": "src/packages/StorageBucketApi.ts", "line": 1, "character": 0}]}, {"id": 95, "name": "packages/StorageFileApi", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 96, "name": "default", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 97, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 48, "character": 2}], "signatures": [{"id": 98, "name": "new default", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 99, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 100, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 101, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "indexSignature": {"id": 102, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 103, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}, "defaultValue": "{}"}, {"id": 104, "name": "bucketId", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 105, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 106, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 107, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 108, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}}, {"id": 109, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}], "type": {"type": "reference", "id": 96, "name": "default"}}]}, {"id": 211, "name": "copy", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 373, "character": 8}], "signatures": [{"id": 212, "name": "copy", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Copies an existing file to a new path in the same bucket."}, "parameters": [{"id": 213, "name": "fromPath", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The original file path, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 214, "name": "to<PERSON><PERSON>", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The new file path, including the new file name. For example `folder/image-copy.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 215, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"shortText": "The destination options.\n"}, "type": {"type": "reference", "id": 471, "name": "DestinationOptions", "dereferenced": {"id": 471, "name": "DestinationOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 472, "name": "destinationBucket", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 75, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [472]}], "sources": [{"fileName": "src/lib/types.ts", "line": 74, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 216, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 217, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 379, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 218, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 219, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 379, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [219]}]}}}, {"id": 220, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 380, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [217, 220]}]}}, {"type": "reflection", "declaration": {"id": 221, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 222, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 383, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 223, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 384, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [222, 223]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 167, "name": "createSignedUploadUrl", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 243, "character": 8}], "signatures": [{"id": 168, "name": "createSignedUploadUrl", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a signed upload URL.\nSigned upload URLs can be used to upload files to the bucket without further authentication.\nThey are valid for 2 hours."}, "parameters": [{"id": 169, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The file path, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 170, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 171, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 172, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "If set to true, allows the file to be overwritten if it already exists.\n"}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 245, "character": 16}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [172]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 173, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 174, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 248, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 175, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 178, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 248, "character": 50}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 176, "name": "signedUrl", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 248, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 177, "name": "token", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 248, "character": 35}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [178, 176, 177]}]}}}, {"id": 179, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 249, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [174, 179]}]}}, {"type": "reflection", "declaration": {"id": 180, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 181, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 252, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 182, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 253, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [181, 182]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 224, "name": "createSignedUrl", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 417, "character": 8}], "signatures": [{"id": 225, "name": "createSignedUrl", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a signed URL. Use a signed URL to share a file for a fixed amount of time."}, "parameters": [{"id": 226, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The file path, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 227, "name": "expiresIn", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 228, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 229, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 230, "name": "download", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename."}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 420, "character": 16}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "boolean"}]}}, {"id": 231, "name": "transform", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Transform the asset before serving it to the client.\n"}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 420, "character": 45}], "type": {"type": "reference", "id": 482, "name": "TransformOptions", "dereferenced": {"id": 482, "name": "TransformOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 487, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Specify the format of the image requested.", "text": "When using 'origin' we force the format to be the same as the original image.\nWhen this option is not passed in, images are optimized to modern image formats like Webp.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 140, "character": 2}], "type": {"type": "literal", "value": "origin"}}, {"id": 484, "name": "height", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The height of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 120, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 486, "name": "quality", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set the quality of the returned image.\nA number from 20 to 100, with 100 being the highest quality.\nDefaults to 80"}, "sources": [{"fileName": "src/lib/types.ts", "line": 133, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 485, "name": "resize", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The resize mode can be cover, contain or fill. Defaults to cover.\nCover resizes the image to maintain it's aspect ratio while filling the entire width and height.\nContain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height.\nFill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit."}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": "cover"}, {"type": "literal", "value": "contain"}, {"type": "literal", "value": "fill"}]}}, {"id": 483, "name": "width", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The width of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 116, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [487, 484, 486, 485, 483]}], "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 17}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [230, 231]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 232, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 233, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 423, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 234, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 235, "name": "signedUrl", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 423, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [235]}]}}}, {"id": 236, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 424, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [233, 236]}]}}, {"type": "reflection", "declaration": {"id": 237, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 238, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 427, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 239, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 428, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [238, 239]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 240, "name": "createSignedUrls", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 462, "character": 8}], "signatures": [{"id": 241, "name": "createSignedUrls", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time."}, "parameters": [{"id": 242, "name": "paths", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`."}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 243, "name": "expiresIn", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute."}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 244, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 245, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 246, "name": "download", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n"}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 465, "character": 16}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "boolean"}]}}], "groups": [{"title": "Properties", "kind": 1024, "children": [246]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 247, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 248, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 468, "character": 8}], "type": {"type": "array", "elementType": {"type": "reflection", "declaration": {"id": 249, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 250, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 468, "character": 16}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 251, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 468, "character": 38}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}}, {"id": 252, "name": "signedUrl", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 468, "character": 59}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [250, 251, 252]}]}}}}, {"id": 253, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 469, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [248, 253]}]}}, {"type": "reflection", "declaration": {"id": 254, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 255, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 472, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 256, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 473, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [255, 256]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 257, "name": "download", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 511, "character": 8}], "signatures": [{"id": 258, "name": "download", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead."}, "parameters": [{"id": 259, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The full path and file name of the file to be downloaded. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 260, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 261, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 262, "name": "transform", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Transform the asset before serving it to the client.\n"}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 513, "character": 16}], "type": {"type": "reference", "id": 482, "name": "TransformOptions", "dereferenced": {"id": 482, "name": "TransformOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 487, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Specify the format of the image requested.", "text": "When using 'origin' we force the format to be the same as the original image.\nWhen this option is not passed in, images are optimized to modern image formats like Webp.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 140, "character": 2}], "type": {"type": "literal", "value": "origin"}}, {"id": 484, "name": "height", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The height of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 120, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 486, "name": "quality", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set the quality of the returned image.\nA number from 20 to 100, with 100 being the highest quality.\nDefaults to 80"}, "sources": [{"fileName": "src/lib/types.ts", "line": 133, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 485, "name": "resize", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The resize mode can be cover, contain or fill. Defaults to cover.\nCover resizes the image to maintain it's aspect ratio while filling the entire width and height.\nContain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height.\nFill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit."}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": "cover"}, {"type": "literal", "value": "contain"}, {"type": "literal", "value": "fill"}]}}, {"id": 483, "name": "width", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The width of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 116, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [487, 484, 486, 485, 483]}], "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 17}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [262]}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 263, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 264, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 516, "character": 8}], "type": {"type": "reference", "qualifiedName": "Blob", "package": "typescript", "name": "Blob"}}, {"id": 265, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 517, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [264, 265]}]}}, {"type": "reflection", "declaration": {"id": 266, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 267, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 520, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 268, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 521, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [267, 268]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 278, "name": "exists", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 583, "character": 8}], "signatures": [{"id": 279, "name": "exists", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Checks the existence of a file."}, "parameters": [{"id": 280, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 281, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 282, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 587, "character": 8}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 283, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 588, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [282, 283]}]}}, {"type": "reflection", "declaration": {"id": 284, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 285, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 591, "character": 8}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 286, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 592, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [285, 286]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 287, "name": "getPublicUrl", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 624, "character": 2}], "signatures": [{"id": 288, "name": "getPublicUrl", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\nThis function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset."}, "parameters": [{"id": 289, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The path and name of the file to generate the public URL for. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 290, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 291, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 292, "name": "download", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename."}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 626, "character": 16}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "boolean"}]}}, {"id": 293, "name": "transform", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Transform the asset before serving it to the client.\n"}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 626, "character": 45}], "type": {"type": "reference", "id": 482, "name": "TransformOptions", "dereferenced": {"id": 482, "name": "TransformOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 487, "name": "format", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Specify the format of the image requested.", "text": "When using 'origin' we force the format to be the same as the original image.\nWhen this option is not passed in, images are optimized to modern image formats like Webp.\n"}, "sources": [{"fileName": "src/lib/types.ts", "line": 140, "character": 2}], "type": {"type": "literal", "value": "origin"}}, {"id": 484, "name": "height", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The height of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 120, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 486, "name": "quality", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Set the quality of the returned image.\nA number from 20 to 100, with 100 being the highest quality.\nDefaults to 80"}, "sources": [{"fileName": "src/lib/types.ts", "line": 133, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 485, "name": "resize", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The resize mode can be cover, contain or fill. Defaults to cover.\nCover resizes the image to maintain it's aspect ratio while filling the entire width and height.\nContain resizes the image to maintain it's aspect ratio while fitting the entire image within the width and height.\nFill resizes the image to fill the entire width and height. If the object's aspect ratio does not match the width and height, the image will be stretched to fit."}, "sources": [{"fileName": "src/lib/types.ts", "line": 127, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": "cover"}, {"type": "literal", "value": "contain"}, {"type": "literal", "value": "fill"}]}}, {"id": 483, "name": "width", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The width of the image in pixels."}, "sources": [{"fileName": "src/lib/types.ts", "line": 116, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [487, 484, 486, 485, 483]}], "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 17}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [292, 293]}]}}}], "type": {"type": "reflection", "declaration": {"id": 294, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 295, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 627, "character": 7}], "type": {"type": "reflection", "declaration": {"id": 296, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 297, "name": "publicUrl", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 627, "character": 15}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [297]}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [295]}]}}}]}, {"id": 269, "name": "info", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 550, "character": 8}], "signatures": [{"id": 270, "name": "info", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Retrieves the details of an existing file."}, "parameters": [{"id": 271, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 272, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 273, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 554, "character": 8}], "type": {"type": "reference", "id": 488, "typeArguments": [{"type": "reference", "id": 447, "name": "FileObjectV2"}], "name": "Came<PERSON>ze"}}, {"id": 274, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 555, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [273, 274]}]}}, {"type": "reflection", "declaration": {"id": 275, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 276, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 558, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 277, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 559, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [276, 277]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 307, "name": "list", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 758, "character": 8}], "signatures": [{"id": 308, "name": "list", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Lists all the files within a bucket."}, "parameters": [{"id": 309, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"shortText": "The folder path.\n"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 310, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 473, "name": "SearchOptions", "dereferenced": {"id": 473, "name": "SearchOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 474, "name": "limit", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": " The number of files you want to be returned."}, "sources": [{"fileName": "src/lib/types.ts", "line": 82, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 475, "name": "offset", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The starting position."}, "sources": [{"fileName": "src/lib/types.ts", "line": 87, "character": 2}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 477, "name": "search", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The search string to filter files by."}, "sources": [{"fileName": "src/lib/types.ts", "line": 97, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 476, "name": "sortBy", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The column to sort by. Can be any column inside a FileObject."}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "reference", "id": 461, "name": "SortBy", "dereferenced": {"id": 461, "name": "SortBy", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 462, "name": "column", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 41, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 463, "name": "order", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 42, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [462, 463]}], "sources": [{"fileName": "src/lib/types.ts", "line": 40, "character": 17}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [474, 475, 477, 476]}], "sources": [{"fileName": "src/lib/types.ts", "line": 78, "character": 17}]}}}, {"id": 311, "name": "parameters", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 478, "name": "FetchParameters", "dereferenced": {"id": 478, "name": "FetchParameters", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 479, "name": "signal", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Pass in an AbortController's signal to cancel the request."}, "sources": [{"fileName": "src/lib/types.ts", "line": 104, "character": 2}], "type": {"type": "reference", "qualifiedName": "AbortSignal", "package": "typescript", "name": "AbortSignal"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [479]}], "sources": [{"fileName": "src/lib/types.ts", "line": 100, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 312, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 313, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 764, "character": 8}], "type": {"type": "array", "elementType": {"type": "reference", "id": 437, "name": "FileObject"}}}, {"id": 314, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 765, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [313, 314]}]}}, {"type": "reflection", "declaration": {"id": 315, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 316, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 768, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 317, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 769, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [316, 317]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 198, "name": "move", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 330, "character": 8}], "signatures": [{"id": 199, "name": "move", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Moves an existing file to a new path in the same bucket."}, "parameters": [{"id": 200, "name": "fromPath", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The original file path, including the current file name. For example `folder/image.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 201, "name": "to<PERSON><PERSON>", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The new file path, including the new file name. For example `folder/image-new.png`."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 202, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "comment": {"shortText": "The destination options.\n"}, "type": {"type": "reference", "id": 471, "name": "DestinationOptions", "dereferenced": {"id": 471, "name": "DestinationOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 472, "name": "destinationBucket", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 75, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [472]}], "sources": [{"fileName": "src/lib/types.ts", "line": 74, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 203, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 204, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 336, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 205, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 206, "name": "message", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 336, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [206]}]}}}, {"id": 207, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 337, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [204, 207]}]}}, {"type": "reflection", "declaration": {"id": 208, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 209, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 340, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 210, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 341, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [209, 210]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 298, "name": "remove", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 662, "character": 8}], "signatures": [{"id": 299, "name": "remove", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Deletes files within the same bucket"}, "parameters": [{"id": 300, "name": "paths", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n"}, "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 301, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 302, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 666, "character": 8}], "type": {"type": "array", "elementType": {"type": "reference", "id": 437, "name": "FileObject"}}}, {"id": 303, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 667, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [302, 303]}]}}, {"type": "reflection", "declaration": {"id": 304, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 305, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 670, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 306, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 671, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [305, 306]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 321, "name": "toBase64", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 795, "character": 2}], "signatures": [{"id": 322, "name": "toBase64", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 323, "name": "data", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}, {"id": 183, "name": "update", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 296, "character": 8}], "signatures": [{"id": 184, "name": "update", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Replaces an existing file at the specified path with a new one."}, "parameters": [{"id": 185, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 186, "name": "fileBody", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The body of the file to be stored in the bucket.\n"}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "qualifiedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "typescript", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "reference", "qualifiedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew", "package": "typescript", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew"}, {"type": "reference", "qualifiedName": "Blob", "package": "typescript", "name": "Blob"}, {"type": "reference", "qualifiedName": "<PERSON><PERSON><PERSON>", "package": "@types/node", "name": "<PERSON><PERSON><PERSON>"}, {"type": "reference", "qualifiedName": "File", "package": "typescript", "name": "File"}, {"type": "reference", "qualifiedName": "FormData", "package": "typescript", "name": "FormData"}, {"type": "reference", "qualifiedName": "NodeJS.ReadableStream", "package": "@types/node", "name": "ReadableStream"}, {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Uint8Array", "package": "typescript", "name": "Uint8Array"}], "qualifiedName": "ReadableStream", "package": "typescript", "name": "ReadableStream"}, {"type": "reference", "qualifiedName": "URLSearchParams", "package": "typescript", "name": "URLSearchParams"}]}}, {"id": 187, "name": "fileOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 464, "name": "FileOptions", "dereferenced": {"id": 464, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 465, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds."}, "sources": [{"fileName": "src/lib/types.ts", "line": 49, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 466, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`."}, "sources": [{"fileName": "src/lib/types.ts", "line": 53, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 468, "name": "duplex", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The duplex option is a string parameter that enables or disables duplex streaming, allowing for both reading and writing data in the same stream. It can be passed as an option to the fetch() method."}, "sources": [{"fileName": "src/lib/types.ts", "line": 61, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 470, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optionally add extra headers"}, "sources": [{"fileName": "src/lib/types.ts", "line": 71, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 469, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The metadata option is an object that allows you to store additional information about the file. This information can be used to filter and search for files. The metadata object can contain any key-value pairs you want to store."}, "sources": [{"fileName": "src/lib/types.ts", "line": 66, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 467, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false."}, "sources": [{"fileName": "src/lib/types.ts", "line": 57, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [465, 466, 468, 470, 469, 467]}], "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 188, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 189, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 312, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 190, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 193, "name": "fullPath", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 312, "character": 42}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 191, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 312, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 192, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 312, "character": 28}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [193, 191, 192]}]}}}, {"id": 194, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 313, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [189, 194]}]}}, {"type": "reflection", "declaration": {"id": 195, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 196, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 316, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 197, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 317, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [196, 197]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 137, "name": "upload", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 154, "character": 8}], "signatures": [{"id": 138, "name": "upload", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Uploads a file to an existing bucket."}, "parameters": [{"id": 139, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 140, "name": "fileBody", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The body of the file to be stored in the bucket.\n"}, "type": {"type": "reference", "name": "FileBody"}}, {"id": 141, "name": "fileOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 464, "name": "FileOptions", "dereferenced": {"id": 464, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 465, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds."}, "sources": [{"fileName": "src/lib/types.ts", "line": 49, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 466, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`."}, "sources": [{"fileName": "src/lib/types.ts", "line": 53, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 468, "name": "duplex", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The duplex option is a string parameter that enables or disables duplex streaming, allowing for both reading and writing data in the same stream. It can be passed as an option to the fetch() method."}, "sources": [{"fileName": "src/lib/types.ts", "line": 61, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 470, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optionally add extra headers"}, "sources": [{"fileName": "src/lib/types.ts", "line": 71, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 469, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The metadata option is an object that allows you to store additional information about the file. This information can be used to filter and search for files. The metadata object can contain any key-value pairs you want to store."}, "sources": [{"fileName": "src/lib/types.ts", "line": 66, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 467, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false."}, "sources": [{"fileName": "src/lib/types.ts", "line": 57, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [465, 466, 468, 470, 469, 467]}], "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 142, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 143, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 160, "character": 8}], "type": {"type": "reflection", "declaration": {"id": 144, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 147, "name": "fullPath", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 160, "character": 42}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 145, "name": "id", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 160, "character": 16}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 146, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 160, "character": 28}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [147, 145, 146]}]}}}, {"id": 148, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 161, "character": 8}], "type": {"type": "literal", "value": null}}], "groups": [{"title": "Properties", "kind": 1024, "children": [143, 148]}]}}, {"type": "reflection", "declaration": {"id": 149, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 150, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 164, "character": 8}], "type": {"type": "literal", "value": null}}, {"id": 151, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 165, "character": 8}], "type": {"type": "reference", "id": 493, "name": "StorageError"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [150, 151]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 152, "name": "uploadToSignedUrl", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 177, "character": 8}], "signatures": [{"id": 153, "name": "uploadToSignedUrl", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Upload a file with a token generated from `createSignedUploadUrl`."}, "parameters": [{"id": 154, "name": "path", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 155, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The token generated from `createSignedUploadUrl`"}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 156, "name": "fileBody", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The body of the file to be stored in the bucket.\n"}, "type": {"type": "reference", "name": "FileBody"}}, {"id": 157, "name": "fileOptions", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 464, "name": "FileOptions", "dereferenced": {"id": 464, "name": "FileOptions", "kind": 256, "kindString": "Interface", "flags": {}, "children": [{"id": 465, "name": "cacheControl", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The number of seconds the asset is cached in the browser and in the Supabase CDN. This is set in the `Cache-Control: max-age=<seconds>` header. Defaults to 3600 seconds."}, "sources": [{"fileName": "src/lib/types.ts", "line": 49, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 466, "name": "contentType", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "the `Content-Type` header value. Should be specified if using a `fileBody` that is neither `Blob` nor `File` nor `FormData`, otherwise will default to `text/plain;charset=UTF-8`."}, "sources": [{"fileName": "src/lib/types.ts", "line": 53, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 468, "name": "duplex", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The duplex option is a string parameter that enables or disables duplex streaming, allowing for both reading and writing data in the same stream. It can be passed as an option to the fetch() method."}, "sources": [{"fileName": "src/lib/types.ts", "line": 61, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 470, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optionally add extra headers"}, "sources": [{"fileName": "src/lib/types.ts", "line": 71, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 469, "name": "metadata", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The metadata option is an object that allows you to store additional information about the file. This information can be used to filter and search for files. The metadata object can contain any key-value pairs you want to store."}, "sources": [{"fileName": "src/lib/types.ts", "line": 66, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 467, "name": "upsert", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "When upsert is set to true, the file is overwritten if it exists. When set to false, an error is thrown if the object already exists. Defaults to false."}, "sources": [{"fileName": "src/lib/types.ts", "line": 57, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [465, 466, 468, 470, 469, 467]}], "sources": [{"fileName": "src/lib/types.ts", "line": 45, "character": 17}]}}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "reflection", "declaration": {"id": 158, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 159, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 220, "character": 10}], "type": {"type": "reflection", "declaration": {"id": 160, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 162, "name": "fullPath", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 220, "character": 35}], "type": {"type": "intrinsic", "name": "any"}, "defaultValue": "data.Key"}, {"id": 161, "name": "path", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 220, "character": 18}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "cleanPath"}], "groups": [{"title": "Properties", "kind": 1024, "children": [162, 161]}]}}, "defaultValue": "..."}, {"id": 163, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 221, "character": 10}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}], "groups": [{"title": "Properties", "kind": 1024, "children": [159, 163]}]}}, {"type": "reflection", "declaration": {"id": 164, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 165, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 225, "character": 17}], "type": {"type": "literal", "value": null}, "defaultValue": "null"}, {"id": 166, "name": "error", "kind": 1024, "kindString": "Property", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [165, 166]}]}}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [97]}, {"title": "Methods", "kind": 2048, "children": [211, 167, 224, 240, 257, 278, 287, 269, 307, 198, 298, 321, 183, 137, 152]}], "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 42, "character": 21}]}], "groups": [{"title": "Classes", "kind": 128, "children": [96]}], "sources": [{"fileName": "src/packages/StorageFileApi.ts", "line": 1, "character": 0}]}], "groups": [{"title": "<PERSON><PERSON><PERSON>", "kind": 2, "children": [1, 2, 95]}]}