{"id": 0, "name": "@supabase/functions-js", "kind": 1, "kindString": "Project", "flags": {}, "originalName": "", "children": [{"id": 67, "name": "FunctionRegion", "kind": 8, "kindString": "Enumeration", "flags": {}, "children": [{"id": 68, "name": "Any", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 45, "character": 2}], "type": {"type": "literal", "value": "any"}, "defaultValue": "\"any\""}, {"id": 69, "name": "ApNortheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 46, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-1"}, "defaultValue": "\"ap-northeast-1\""}, {"id": 70, "name": "ApNortheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 47, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-2"}, "defaultValue": "\"ap-northeast-2\""}, {"id": 71, "name": "ApSouth1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 48, "character": 2}], "type": {"type": "literal", "value": "ap-south-1"}, "defaultValue": "\"ap-south-1\""}, {"id": 72, "name": "ApSoutheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 49, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-1"}, "defaultValue": "\"ap-southeast-1\""}, {"id": 73, "name": "ApSoutheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 50, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-2"}, "defaultValue": "\"ap-southeast-2\""}, {"id": 74, "name": "CaCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 51, "character": 2}], "type": {"type": "literal", "value": "ca-central-1"}, "defaultValue": "\"ca-central-1\""}, {"id": 75, "name": "EuCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 52, "character": 2}], "type": {"type": "literal", "value": "eu-central-1"}, "defaultValue": "\"eu-central-1\""}, {"id": 76, "name": "EuWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 53, "character": 2}], "type": {"type": "literal", "value": "eu-west-1"}, "defaultValue": "\"eu-west-1\""}, {"id": 77, "name": "EuWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 54, "character": 2}], "type": {"type": "literal", "value": "eu-west-2"}, "defaultValue": "\"eu-west-2\""}, {"id": 78, "name": "EuWest3", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 55, "character": 2}], "type": {"type": "literal", "value": "eu-west-3"}, "defaultValue": "\"eu-west-3\""}, {"id": 79, "name": "SaEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 56, "character": 2}], "type": {"type": "literal", "value": "sa-east-1"}, "defaultValue": "\"sa-east-1\""}, {"id": 80, "name": "UsEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 57, "character": 2}], "type": {"type": "literal", "value": "us-east-1"}, "defaultValue": "\"us-east-1\""}, {"id": 81, "name": "UsWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 58, "character": 2}], "type": {"type": "literal", "value": "us-west-1"}, "defaultValue": "\"us-west-1\""}, {"id": 82, "name": "UsWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 59, "character": 2}], "type": {"type": "literal", "value": "us-west-2"}, "defaultValue": "\"us-west-2\""}], "groups": [{"title": "Enumeration Members", "kind": 16, "children": [68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82]}], "sources": [{"fileName": "src/types.ts", "line": 44, "character": 12}]}, {"id": 1, "name": "FunctionsClient", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 2, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 18, "character": 2}], "signatures": [{"id": 3, "name": "new FunctionsClient", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 4, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 5, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 6, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 8, "name": "customFetch", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 26, "character": 6}], "type": {"type": "reflection", "declaration": {"id": 9, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 10, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 11, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 12, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}, {"id": 13, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 14, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "qualifiedName": "Request", "package": "typescript", "name": "Request"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 15, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 7, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 25, "character": 6}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 16, "name": "region", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 27, "character": 6}], "type": {"type": "reference", "id": 67, "name": "FunctionRegion", "dereferenced": {"id": 67, "name": "FunctionRegion", "kind": 8, "kindString": "Enumeration", "flags": {}, "children": [{"id": 68, "name": "Any", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 45, "character": 2}], "type": {"type": "literal", "value": "any"}, "defaultValue": "\"any\""}, {"id": 69, "name": "ApNortheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 46, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-1"}, "defaultValue": "\"ap-northeast-1\""}, {"id": 70, "name": "ApNortheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 47, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-2"}, "defaultValue": "\"ap-northeast-2\""}, {"id": 71, "name": "ApSouth1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 48, "character": 2}], "type": {"type": "literal", "value": "ap-south-1"}, "defaultValue": "\"ap-south-1\""}, {"id": 72, "name": "ApSoutheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 49, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-1"}, "defaultValue": "\"ap-southeast-1\""}, {"id": 73, "name": "ApSoutheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 50, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-2"}, "defaultValue": "\"ap-southeast-2\""}, {"id": 74, "name": "CaCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 51, "character": 2}], "type": {"type": "literal", "value": "ca-central-1"}, "defaultValue": "\"ca-central-1\""}, {"id": 75, "name": "EuCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 52, "character": 2}], "type": {"type": "literal", "value": "eu-central-1"}, "defaultValue": "\"eu-central-1\""}, {"id": 76, "name": "EuWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 53, "character": 2}], "type": {"type": "literal", "value": "eu-west-1"}, "defaultValue": "\"eu-west-1\""}, {"id": 77, "name": "EuWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 54, "character": 2}], "type": {"type": "literal", "value": "eu-west-2"}, "defaultValue": "\"eu-west-2\""}, {"id": 78, "name": "EuWest3", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 55, "character": 2}], "type": {"type": "literal", "value": "eu-west-3"}, "defaultValue": "\"eu-west-3\""}, {"id": 79, "name": "SaEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 56, "character": 2}], "type": {"type": "literal", "value": "sa-east-1"}, "defaultValue": "\"sa-east-1\""}, {"id": 80, "name": "UsEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 57, "character": 2}], "type": {"type": "literal", "value": "us-east-1"}, "defaultValue": "\"us-east-1\""}, {"id": 81, "name": "UsWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 58, "character": 2}], "type": {"type": "literal", "value": "us-west-1"}, "defaultValue": "\"us-west-1\""}, {"id": 82, "name": "UsWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 59, "character": 2}], "type": {"type": "literal", "value": "us-west-2"}, "defaultValue": "\"us-west-2\""}], "groups": [{"title": "Enumeration Members", "kind": 16, "children": [68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82]}], "sources": [{"fileName": "src/types.ts", "line": 44, "character": 12}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [8, 7, 16]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "id": 1, "name": "FunctionsClient"}}]}, {"id": 20, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 16, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 21, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 22, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 23, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 24, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}, {"id": 25, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 26, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "qualifiedName": "Request", "package": "typescript", "name": "Request"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 27, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 18, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 14, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 19, "name": "region", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 15, "character": 12}], "type": {"type": "reference", "id": 67, "name": "FunctionRegion", "dereferenced": {"id": 67, "name": "FunctionRegion", "kind": 8, "kindString": "Enumeration", "flags": {}, "children": [{"id": 68, "name": "Any", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 45, "character": 2}], "type": {"type": "literal", "value": "any"}, "defaultValue": "\"any\""}, {"id": 69, "name": "ApNortheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 46, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-1"}, "defaultValue": "\"ap-northeast-1\""}, {"id": 70, "name": "ApNortheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 47, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-2"}, "defaultValue": "\"ap-northeast-2\""}, {"id": 71, "name": "ApSouth1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 48, "character": 2}], "type": {"type": "literal", "value": "ap-south-1"}, "defaultValue": "\"ap-south-1\""}, {"id": 72, "name": "ApSoutheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 49, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-1"}, "defaultValue": "\"ap-southeast-1\""}, {"id": 73, "name": "ApSoutheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 50, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-2"}, "defaultValue": "\"ap-southeast-2\""}, {"id": 74, "name": "CaCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 51, "character": 2}], "type": {"type": "literal", "value": "ca-central-1"}, "defaultValue": "\"ca-central-1\""}, {"id": 75, "name": "EuCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 52, "character": 2}], "type": {"type": "literal", "value": "eu-central-1"}, "defaultValue": "\"eu-central-1\""}, {"id": 76, "name": "EuWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 53, "character": 2}], "type": {"type": "literal", "value": "eu-west-1"}, "defaultValue": "\"eu-west-1\""}, {"id": 77, "name": "EuWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 54, "character": 2}], "type": {"type": "literal", "value": "eu-west-2"}, "defaultValue": "\"eu-west-2\""}, {"id": 78, "name": "EuWest3", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 55, "character": 2}], "type": {"type": "literal", "value": "eu-west-3"}, "defaultValue": "\"eu-west-3\""}, {"id": 79, "name": "SaEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 56, "character": 2}], "type": {"type": "literal", "value": "sa-east-1"}, "defaultValue": "\"sa-east-1\""}, {"id": 80, "name": "UsEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 57, "character": 2}], "type": {"type": "literal", "value": "us-east-1"}, "defaultValue": "\"us-east-1\""}, {"id": 81, "name": "UsWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 58, "character": 2}], "type": {"type": "literal", "value": "us-west-1"}, "defaultValue": "\"us-west-1\""}, {"id": 82, "name": "UsWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 59, "character": 2}], "type": {"type": "literal", "value": "us-west-2"}, "defaultValue": "\"us-west-2\""}], "groups": [{"title": "Enumeration Members", "kind": 16, "children": [68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82]}], "sources": [{"fileName": "src/types.ts", "line": 44, "character": 12}]}}}, {"id": 17, "name": "url", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 13, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 31, "name": "invoke", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 49, "character": 8}], "signatures": [{"id": 32, "name": "invoke", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Invokes a function"}, "typeParameter": [{"id": 33, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}, "default": {"type": "intrinsic", "name": "any"}}], "parameters": [{"id": 34, "name": "functionName", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The name of the Function to invoke."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 35, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "Options for invoking the Function.\n"}, "type": {"type": "reference", "id": 36, "name": "FunctionInvokeOptions", "dereferenced": {"id": 36, "name": "FunctionInvokeOptions", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 62, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 37, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 44, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The body of the request."}, "sources": [{"fileName": "src/types.ts", "line": 78, "character": 2}], "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "File", "package": "typescript", "name": "File"}, {"type": "reference", "qualifiedName": "Blob", "package": "typescript", "name": "Blob"}, {"type": "reference", "qualifiedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "typescript", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "reference", "qualifiedName": "FormData", "package": "typescript", "name": "FormData"}, {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Uint8Array", "package": "typescript", "name": "Uint8Array"}], "qualifiedName": "ReadableStream", "package": "typescript", "name": "ReadableStream"}, {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 38, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Object representing the headers to send with the request."}, "sources": [{"fileName": "src/types.ts", "line": 66, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 39, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 66, "character": 12}], "indexSignature": {"id": 40, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 41, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 42, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The HTTP verb of the request"}, "sources": [{"fileName": "src/types.ts", "line": 70, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": "POST"}, {"type": "literal", "value": "GET"}, {"type": "literal", "value": "PUT"}, {"type": "literal", "value": "PATCH"}, {"type": "literal", "value": "DELETE"}]}}, {"id": 43, "name": "region", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The Region to invoke the function in."}, "sources": [{"fileName": "src/types.ts", "line": 74, "character": 2}], "type": {"type": "reference", "id": 67, "name": "FunctionRegion", "dereferenced": {"id": 67, "name": "FunctionRegion", "kind": 8, "kindString": "Enumeration", "flags": {}, "children": [{"id": 68, "name": "Any", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 45, "character": 2}], "type": {"type": "literal", "value": "any"}, "defaultValue": "\"any\""}, {"id": 69, "name": "ApNortheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 46, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-1"}, "defaultValue": "\"ap-northeast-1\""}, {"id": 70, "name": "ApNortheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 47, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-2"}, "defaultValue": "\"ap-northeast-2\""}, {"id": 71, "name": "ApSouth1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 48, "character": 2}], "type": {"type": "literal", "value": "ap-south-1"}, "defaultValue": "\"ap-south-1\""}, {"id": 72, "name": "ApSoutheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 49, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-1"}, "defaultValue": "\"ap-southeast-1\""}, {"id": 73, "name": "ApSoutheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 50, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-2"}, "defaultValue": "\"ap-southeast-2\""}, {"id": 74, "name": "CaCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 51, "character": 2}], "type": {"type": "literal", "value": "ca-central-1"}, "defaultValue": "\"ca-central-1\""}, {"id": 75, "name": "EuCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 52, "character": 2}], "type": {"type": "literal", "value": "eu-central-1"}, "defaultValue": "\"eu-central-1\""}, {"id": 76, "name": "EuWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 53, "character": 2}], "type": {"type": "literal", "value": "eu-west-1"}, "defaultValue": "\"eu-west-1\""}, {"id": 77, "name": "EuWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 54, "character": 2}], "type": {"type": "literal", "value": "eu-west-2"}, "defaultValue": "\"eu-west-2\""}, {"id": 78, "name": "EuWest3", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 55, "character": 2}], "type": {"type": "literal", "value": "eu-west-3"}, "defaultValue": "\"eu-west-3\""}, {"id": 79, "name": "SaEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 56, "character": 2}], "type": {"type": "literal", "value": "sa-east-1"}, "defaultValue": "\"sa-east-1\""}, {"id": 80, "name": "UsEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 57, "character": 2}], "type": {"type": "literal", "value": "us-east-1"}, "defaultValue": "\"us-east-1\""}, {"id": 81, "name": "UsWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 58, "character": 2}], "type": {"type": "literal", "value": "us-west-1"}, "defaultValue": "\"us-west-1\""}, {"id": 82, "name": "UsWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 59, "character": 2}], "type": {"type": "literal", "value": "us-west-2"}, "defaultValue": "\"us-west-2\""}], "groups": [{"title": "Enumeration Members", "kind": 16, "children": [68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82]}], "sources": [{"fileName": "src/types.ts", "line": 44, "character": 12}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [44, 38, 42, 43]}], "sources": [{"fileName": "src/types.ts", "line": 62, "character": 36}]}}}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 83, "typeArguments": [{"type": "reference", "id": 33, "name": "T"}], "name": "FunctionsResponse"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 28, "name": "setAuth", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/FunctionsClient.ts", "line": 40, "character": 2}], "signatures": [{"id": 29, "name": "setAuth", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Updates the authorization header"}, "parameters": [{"id": 30, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "the new jwt token sent in the authorisation header\n"}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [2]}, {"title": "Properties", "kind": 1024, "children": [20, 18, 19, 17]}, {"title": "Methods", "kind": 2048, "children": [31, 28]}], "sources": [{"fileName": "src/FunctionsClient.ts", "line": 12, "character": 13}]}, {"id": 45, "name": "FunctionsError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 46, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 19, "character": 2}], "signatures": [{"id": 47, "name": "new FunctionsError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 48, "name": "message", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 49, "name": "name", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "'FunctionsError'"}, {"id": 50, "name": "context", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "reference", "id": 45, "name": "FunctionsError"}, "overwrites": {"type": "reference", "name": "Error.constructor"}}], "overwrites": {"type": "reference", "name": "Error.constructor"}}, {"id": 51, "name": "context", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 18, "character": 2}], "type": {"type": "intrinsic", "name": "any"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [46]}, {"title": "Properties", "kind": 1024, "children": [51]}], "sources": [{"fileName": "src/types.ts", "line": 17, "character": 13}], "extendedTypes": [{"type": "reference", "qualifiedName": "Error", "package": "typescript", "name": "Error"}], "extendedBy": [{"type": "reference", "id": 52, "name": "FunctionsFetchError"}, {"type": "reference", "id": 57, "name": "FunctionsHttpError"}, {"type": "reference", "id": 62, "name": "FunctionsRelayError"}]}, {"id": 52, "name": "FunctionsFetchError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 53, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 27, "character": 2}], "signatures": [{"id": 54, "name": "new FunctionsFetchError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 55, "name": "context", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "reference", "id": 52, "name": "FunctionsFetchError"}, "overwrites": {"type": "reference", "id": 47, "name": "FunctionsError.constructor"}}], "overwrites": {"type": "reference", "id": 46, "name": "FunctionsError.constructor"}}, {"id": 56, "name": "context", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 18, "character": 2}], "type": {"type": "intrinsic", "name": "any"}, "inheritedFrom": {"type": "reference", "id": 51, "name": "FunctionsError.context"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [53]}, {"title": "Properties", "kind": 1024, "children": [56]}], "sources": [{"fileName": "src/types.ts", "line": 26, "character": 13}], "extendedTypes": [{"type": "reference", "id": 45, "name": "FunctionsError"}]}, {"id": 57, "name": "FunctionsHttpError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 58, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 39, "character": 2}], "signatures": [{"id": 59, "name": "new FunctionsHttpError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 60, "name": "context", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "reference", "id": 57, "name": "FunctionsHttpError"}, "overwrites": {"type": "reference", "id": 47, "name": "FunctionsError.constructor"}}], "overwrites": {"type": "reference", "id": 46, "name": "FunctionsError.constructor"}}, {"id": 61, "name": "context", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 18, "character": 2}], "type": {"type": "intrinsic", "name": "any"}, "inheritedFrom": {"type": "reference", "id": 51, "name": "FunctionsError.context"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [58]}, {"title": "Properties", "kind": 1024, "children": [61]}], "sources": [{"fileName": "src/types.ts", "line": 38, "character": 13}], "extendedTypes": [{"type": "reference", "id": 45, "name": "FunctionsError"}]}, {"id": 62, "name": "FunctionsRelayError", "kind": 128, "kindString": "Class", "flags": {}, "children": [{"id": 63, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 33, "character": 2}], "signatures": [{"id": 64, "name": "new FunctionsRelayError", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "parameters": [{"id": 65, "name": "context", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "reference", "id": 62, "name": "FunctionsRelayError"}, "overwrites": {"type": "reference", "id": 47, "name": "FunctionsError.constructor"}}], "overwrites": {"type": "reference", "id": 46, "name": "FunctionsError.constructor"}}, {"id": 66, "name": "context", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 18, "character": 2}], "type": {"type": "intrinsic", "name": "any"}, "inheritedFrom": {"type": "reference", "id": 51, "name": "FunctionsError.context"}}], "groups": [{"title": "Constructors", "kind": 512, "children": [63]}, {"title": "Properties", "kind": 1024, "children": [66]}], "sources": [{"fileName": "src/types.ts", "line": 32, "character": 13}], "extendedTypes": [{"type": "reference", "id": 45, "name": "FunctionsError"}]}, {"id": 36, "name": "FunctionInvokeOptions", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 62, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 37, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 44, "name": "body", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The body of the request."}, "sources": [{"fileName": "src/types.ts", "line": 78, "character": 2}], "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "File", "package": "typescript", "name": "File"}, {"type": "reference", "qualifiedName": "Blob", "package": "typescript", "name": "Blob"}, {"type": "reference", "qualifiedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "package": "typescript", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"type": "reference", "qualifiedName": "FormData", "package": "typescript", "name": "FormData"}, {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Uint8Array", "package": "typescript", "name": "Uint8Array"}], "qualifiedName": "ReadableStream", "package": "typescript", "name": "ReadableStream"}, {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "any"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}, {"type": "intrinsic", "name": "string"}]}}, {"id": 38, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Object representing the headers to send with the request."}, "sources": [{"fileName": "src/types.ts", "line": 66, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 39, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 66, "character": 12}], "indexSignature": {"id": 40, "name": "__index", "kind": 8192, "kindString": "Index signature", "flags": {}, "parameters": [{"id": 41, "name": "key", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}}}}, {"id": 42, "name": "method", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The HTTP verb of the request"}, "sources": [{"fileName": "src/types.ts", "line": 70, "character": 2}], "type": {"type": "union", "types": [{"type": "literal", "value": "POST"}, {"type": "literal", "value": "GET"}, {"type": "literal", "value": "PUT"}, {"type": "literal", "value": "PATCH"}, {"type": "literal", "value": "DELETE"}]}}, {"id": 43, "name": "region", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The Region to invoke the function in."}, "sources": [{"fileName": "src/types.ts", "line": 74, "character": 2}], "type": {"type": "reference", "id": 67, "name": "FunctionRegion", "dereferenced": {"id": 67, "name": "FunctionRegion", "kind": 8, "kindString": "Enumeration", "flags": {}, "children": [{"id": 68, "name": "Any", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 45, "character": 2}], "type": {"type": "literal", "value": "any"}, "defaultValue": "\"any\""}, {"id": 69, "name": "ApNortheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 46, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-1"}, "defaultValue": "\"ap-northeast-1\""}, {"id": 70, "name": "ApNortheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 47, "character": 2}], "type": {"type": "literal", "value": "ap-northeast-2"}, "defaultValue": "\"ap-northeast-2\""}, {"id": 71, "name": "ApSouth1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 48, "character": 2}], "type": {"type": "literal", "value": "ap-south-1"}, "defaultValue": "\"ap-south-1\""}, {"id": 72, "name": "ApSoutheast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 49, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-1"}, "defaultValue": "\"ap-southeast-1\""}, {"id": 73, "name": "ApSoutheast2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 50, "character": 2}], "type": {"type": "literal", "value": "ap-southeast-2"}, "defaultValue": "\"ap-southeast-2\""}, {"id": 74, "name": "CaCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 51, "character": 2}], "type": {"type": "literal", "value": "ca-central-1"}, "defaultValue": "\"ca-central-1\""}, {"id": 75, "name": "EuCentral1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 52, "character": 2}], "type": {"type": "literal", "value": "eu-central-1"}, "defaultValue": "\"eu-central-1\""}, {"id": 76, "name": "EuWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 53, "character": 2}], "type": {"type": "literal", "value": "eu-west-1"}, "defaultValue": "\"eu-west-1\""}, {"id": 77, "name": "EuWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 54, "character": 2}], "type": {"type": "literal", "value": "eu-west-2"}, "defaultValue": "\"eu-west-2\""}, {"id": 78, "name": "EuWest3", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 55, "character": 2}], "type": {"type": "literal", "value": "eu-west-3"}, "defaultValue": "\"eu-west-3\""}, {"id": 79, "name": "SaEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 56, "character": 2}], "type": {"type": "literal", "value": "sa-east-1"}, "defaultValue": "\"sa-east-1\""}, {"id": 80, "name": "UsEast1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 57, "character": 2}], "type": {"type": "literal", "value": "us-east-1"}, "defaultValue": "\"us-east-1\""}, {"id": 81, "name": "UsWest1", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 58, "character": 2}], "type": {"type": "literal", "value": "us-west-1"}, "defaultValue": "\"us-west-1\""}, {"id": 82, "name": "UsWest2", "kind": 16, "kindString": "Enumeration Member", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 59, "character": 2}], "type": {"type": "literal", "value": "us-west-2"}, "defaultValue": "\"us-west-2\""}], "groups": [{"title": "Enumeration Members", "kind": 16, "children": [68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82]}], "sources": [{"fileName": "src/types.ts", "line": 44, "character": 12}]}}}], "groups": [{"title": "Properties", "kind": 1024, "children": [44, 38, 42, 43]}], "sources": [{"fileName": "src/types.ts", "line": 62, "character": 36}]}}}, {"id": 83, "name": "FunctionsResponse", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/types.ts", "line": 15, "character": 12}], "typeParameter": [{"id": 84, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "type": {"type": "union", "types": [null, null]}}], "groups": [{"title": "Enumerations", "kind": 8, "children": [67]}, {"title": "Classes", "kind": 128, "children": [1, 45, 52, 57, 62]}, {"title": "Type Aliases", "kind": 4194304, "children": [36, 83]}], "sources": [{"fileName": "src/index.ts", "line": 1, "character": 0}]}