{"id": 0, "name": "@supabase/realtime-js", "variant": "project", "kind": 1, "flags": {}, "children": [{"id": 618, "name": "REALTIME_LISTEN_TYPES", "variant": "declaration", "kind": 8, "flags": {}, "children": [{"id": 619, "name": "BROADCAST", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 98, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L98"}], "type": {"type": "literal", "value": "broadcast"}}, {"id": 621, "name": "POSTGRES_CHANGES", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 100, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L100"}], "type": {"type": "literal", "value": "postgres_changes"}}, {"id": 620, "name": "PRESENCE", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 99, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L99"}], "type": {"type": "literal", "value": "presence"}}, {"id": 622, "name": "SYSTEM", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 101, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L101"}], "type": {"type": "literal", "value": "system"}}], "groups": [{"title": "Enumeration Members", "children": [619, 621, 620, 622]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 97, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L97"}]}, {"id": 623, "name": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "variant": "declaration", "kind": 8, "flags": {}, "children": [{"id": 624, "name": "ALL", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 91, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L91"}], "type": {"type": "literal", "value": "*"}}, {"id": 627, "name": "DELETE", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 94, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L94"}], "type": {"type": "literal", "value": "DELETE"}}, {"id": 625, "name": "INSERT", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 92, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L92"}], "type": {"type": "literal", "value": "INSERT"}}, {"id": 626, "name": "UPDATE", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 93, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L93"}], "type": {"type": "literal", "value": "UPDATE"}}], "groups": [{"title": "Enumeration Members", "children": [624, 627, 625, 626]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 90, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L90"}]}, {"id": 628, "name": "REALTIME_PRESENCE_LISTEN_EVENTS", "variant": "declaration", "kind": 8, "flags": {}, "children": [{"id": 630, "name": "JOIN", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 37, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L37"}], "type": {"type": "literal", "value": "join"}}, {"id": 631, "name": "LEAVE", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 38, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L38"}], "type": {"type": "literal", "value": "leave"}}, {"id": 629, "name": "SYNC", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 36, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L36"}], "type": {"type": "literal", "value": "sync"}}], "groups": [{"title": "Enumeration Members", "children": [630, 631, 629]}], "sources": [{"fileName": "src/RealtimePresence.ts", "line": 35, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L35"}]}, {"id": 632, "name": "REALTIME_SUBSCRIBE_STATES", "variant": "declaration", "kind": 8, "flags": {}, "children": [{"id": 636, "name": "CHANNEL_ERROR", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 108, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L108"}], "type": {"type": "literal", "value": "CHANNEL_ERROR"}}, {"id": 635, "name": "CLOSED", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 107, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L107"}], "type": {"type": "literal", "value": "CLOSED"}}, {"id": 633, "name": "SUBSCRIBED", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 105, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L105"}], "type": {"type": "literal", "value": "SUBSCRIBED"}}, {"id": 634, "name": "TIMED_OUT", "variant": "declaration", "kind": 16, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 106, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L106"}], "type": {"type": "literal", "value": "TIMED_OUT"}}], "groups": [{"title": "Enumeration Members", "children": [636, 635, 633, 634]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 104, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L104"}]}, {"id": 65, "name": "RealtimeChannel", "variant": "declaration", "kind": 128, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A channel is the basic building block of Realtime\nand narrows the scope of data flow to subscribed clients.\nYou can think of a channel as a chatroom where participants are able to see who's online\nand send and receive messages."}]}, "children": [{"id": 76, "name": "constructor", "variant": "declaration", "kind": 512, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 147, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L147"}], "signatures": [{"id": 77, "name": "RealtimeChannel", "variant": "signature", "kind": 16384, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 147, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L147"}], "parameters": [{"id": 78, "name": "topic", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Topic name can be any string."}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 79, "name": "params", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 359, "name": "RealtimeChannelOptions", "package": "@supabase/realtime-js"}, "defaultValue": "..."}, {"id": 80, "name": "socket", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 372, "name": "RealtimeClient", "package": "@supabase/realtime-js", "qualifiedName": "default"}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}]}, {"id": 81, "name": "bindings", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 128, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L128"}], "type": {"type": "reflection", "declaration": {"id": 82, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 128, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L128"}], "indexSignatures": [{"id": 83, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 129, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L129"}], "parameters": [{"id": 84, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "array", "elementType": {"type": "reflection", "declaration": {"id": 85, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 91, "name": "callback", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 132, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L132"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 87, "name": "filter", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 131, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L131"}], "type": {"type": "reflection", "declaration": {"id": 88, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 131, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L131"}], "indexSignatures": [{"id": 89, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 131, "character": 16, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L131"}], "parameters": [{"id": 90, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}, {"id": 92, "name": "id", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 133, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L133"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 86, "name": "type", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 130, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L130"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [91, 87, 92, 86]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 129, "character": 19, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L129"}]}}}}]}}, "defaultValue": "{}"}, {"id": 100, "name": "broadcastEndpointURL", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 143, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L143"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 95, "name": "joinedOnce", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 138, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L138"}], "type": {"type": "intrinsic", "name": "boolean"}, "defaultValue": "false"}, {"id": 96, "name": "joinPush", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 139, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L139"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/push.ts", "qualifiedName": "default"}, "name": "<PERSON><PERSON>", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 104, "name": "params", "variant": "declaration", "kind": 1024, "flags": {"isPublic": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 150, "character": 11, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L150"}], "type": {"type": "reference", "target": 359, "name": "RealtimeChannelOptions", "package": "@supabase/realtime-js"}, "defaultValue": "..."}, {"id": 99, "name": "presence", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 142, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L142"}], "type": {"type": "reference", "target": 1, "name": "RealtimePresence", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 102, "name": "private", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 145, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L145"}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 98, "name": "pushBuffer", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 141, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L141"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/lib/push.ts", "qualifiedName": "default"}, "name": "<PERSON><PERSON>", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, "defaultValue": "[]"}, {"id": 97, "name": "rejoinTimer", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 140, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L140"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/timer.ts", "qualifiedName": "default"}, "name": "Timer", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 105, "name": "socket", "variant": "declaration", "kind": 1024, "flags": {"isPublic": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 151, "character": 11, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L151"}], "type": {"type": "reference", "target": 372, "name": "RealtimeClient", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 94, "name": "state", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 137, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L137"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/constants.ts", "qualifiedName": "CHANNEL_STATES"}, "name": "CHANNEL_STATES", "package": "@supabase/realtime-js"}, "defaultValue": "CHANNEL_STATES.closed"}, {"id": 101, "name": "subTopic", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 144, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L144"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 93, "name": "timeout", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 136, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L136"}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 103, "name": "topic", "variant": "declaration", "kind": 1024, "flags": {"isPublic": true}, "comment": {"summary": [{"kind": "text", "text": "Topic name can be any string."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 149, "character": 11, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L149"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 137, "name": "on", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 350, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L350"}, {"fileName": "src/RealtimeChannel.ts", "line": 355, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L355"}, {"fileName": "src/RealtimeChannel.ts", "line": 360, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L360"}, {"fileName": "src/RealtimeChannel.ts", "line": 365, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L365"}, {"fileName": "src/RealtimeChannel.ts", "line": 370, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L370"}, {"fileName": "src/RealtimeChannel.ts", "line": 375, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L375"}, {"fileName": "src/RealtimeChannel.ts", "line": 380, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L380"}, {"fileName": "src/RealtimeChannel.ts", "line": 391, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L391"}, {"fileName": "src/RealtimeChannel.ts", "line": 400, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L400"}, {"fileName": "src/RealtimeChannel.ts", "line": 409, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L409"}, {"fileName": "src/RealtimeChannel.ts", "line": 414, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L414"}], "signatures": [{"id": 138, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 350, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L350"}], "parameters": [{"id": 139, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "presence"}}, {"id": 140, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 141, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 142, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 352, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L352"}], "type": {"type": "literal", "value": "sync"}}], "groups": [{"title": "Properties", "children": [142]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 352, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L352"}]}}}, {"id": 143, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 144, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 353, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L353"}], "signatures": [{"id": 145, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 353, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L353"}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 146, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 355, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L355"}], "typeParameters": [{"id": 147, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 148, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 355, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L355"}], "indexSignatures": [{"id": 149, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 355, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L355"}], "parameters": [{"id": 150, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 151, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "presence"}}, {"id": 152, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 153, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 154, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 357, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L357"}], "type": {"type": "literal", "value": "join"}}], "groups": [{"title": "Properties", "children": [154]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 357, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L357"}]}}}, {"id": 155, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 156, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 358, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L358"}], "signatures": [{"id": 157, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 358, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L358"}], "parameters": [{"id": 158, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 588, "typeArguments": [{"type": "reference", "target": 147, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePresenceJoinPayload", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 159, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 360, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L360"}], "typeParameters": [{"id": 160, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 161, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 360, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L360"}], "indexSignatures": [{"id": 162, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 360, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L360"}], "parameters": [{"id": 163, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 164, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "presence"}}, {"id": 165, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 166, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 167, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 362, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L362"}], "type": {"type": "literal", "value": "leave"}}], "groups": [{"title": "Properties", "children": [167]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 362, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L362"}]}}}, {"id": 168, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 169, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 363, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L363"}], "signatures": [{"id": 170, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 363, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L363"}], "parameters": [{"id": 171, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 598, "typeArguments": [{"type": "reference", "target": 160, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePresenceLeavePayload", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 172, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 365, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L365"}], "typeParameters": [{"id": 173, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 174, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 365, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L365"}], "indexSignatures": [{"id": 175, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 365, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L365"}], "parameters": [{"id": 176, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 177, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "postgres_changes"}}, {"id": 178, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 547, "typeArguments": [{"type": "literal", "value": "*"}], "name": "RealtimePostgresChangesFilter", "package": "@supabase/realtime-js"}}, {"id": 179, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 180, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 368, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L368"}], "signatures": [{"id": 181, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 368, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L368"}], "parameters": [{"id": 182, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 554, "typeArguments": [{"type": "reference", "target": 173, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresChangesPayload", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 183, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 370, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L370"}], "typeParameters": [{"id": 184, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 185, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 370, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L370"}], "indexSignatures": [{"id": 186, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 370, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L370"}], "parameters": [{"id": 187, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 188, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "postgres_changes"}}, {"id": 189, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 547, "typeArguments": [{"type": "literal", "value": "INSERT"}], "name": "RealtimePostgresChangesFilter", "package": "@supabase/realtime-js"}}, {"id": 190, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 191, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 373, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L373"}], "signatures": [{"id": 192, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 373, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L373"}], "parameters": [{"id": 193, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 559, "typeArguments": [{"type": "reference", "target": 184, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresInsertPayload", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 194, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 375, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L375"}], "typeParameters": [{"id": 195, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 196, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 375, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L375"}], "indexSignatures": [{"id": 197, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 375, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L375"}], "parameters": [{"id": 198, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 199, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "postgres_changes"}}, {"id": 200, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 547, "typeArguments": [{"type": "literal", "value": "UPDATE"}], "name": "RealtimePostgresChangesFilter", "package": "@supabase/realtime-js"}}, {"id": 201, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 202, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 378, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L378"}], "signatures": [{"id": 203, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 378, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L378"}], "parameters": [{"id": 204, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 569, "typeArguments": [{"type": "reference", "target": 195, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresUpdatePayload", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 205, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 380, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L380"}], "typeParameters": [{"id": 206, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 207, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 380, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L380"}], "indexSignatures": [{"id": 208, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 380, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L380"}], "parameters": [{"id": 209, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 210, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "postgres_changes"}}, {"id": 211, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 547, "typeArguments": [{"type": "literal", "value": "DELETE"}], "name": "RealtimePostgresChangesFilter", "package": "@supabase/realtime-js"}}, {"id": 212, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 213, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 383, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L383"}], "signatures": [{"id": 214, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 383, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L383"}], "parameters": [{"id": 215, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 578, "typeArguments": [{"type": "reference", "target": 206, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresDeletePayload", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 216, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The following is placed here to display on supabase.com/docs/reference/javascript/subscribe."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 391, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L391"}], "parameters": [{"id": 217, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "One of \"broadcast\", \"presence\", or \"postgres_changes\"."}]}, "type": {"type": "literal", "value": "broadcast"}}, {"id": 218, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Custom object specific to the Realtime feature detailing which payloads to receive."}]}, "type": {"type": "reflection", "declaration": {"id": 219, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 220, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 393, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L393"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [220]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 393, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L393"}]}}}, {"id": 221, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Function to be invoked when event handler is triggered."}]}, "type": {"type": "reflection", "declaration": {"id": 222, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 394, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L394"}], "signatures": [{"id": 223, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 394, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L394"}], "parameters": [{"id": 224, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 225, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 227, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 396, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L396"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 226, "name": "type", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 395, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L395"}], "type": {"type": "literal", "value": "broadcast"}}], "groups": [{"title": "Properties", "children": [227, 226]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 394, "character": 24, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L394"}], "indexSignatures": [{"id": 228, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 397, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L397"}], "parameters": [{"id": 229, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 230, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 400, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L400"}], "typeParameters": [{"id": 231, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 232, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 400, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L400"}], "indexSignatures": [{"id": 233, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 400, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L400"}], "parameters": [{"id": 234, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 235, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "broadcast"}}, {"id": 236, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 237, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 238, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 402, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L402"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [238]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 402, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L402"}]}}}, {"id": 239, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 240, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 403, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L403"}], "signatures": [{"id": 241, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 403, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L403"}], "parameters": [{"id": 242, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 243, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 245, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 405, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L405"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 246, "name": "payload", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 406, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L406"}], "type": {"type": "reference", "target": 231, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}}, {"id": 244, "name": "type", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 404, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L404"}], "type": {"type": "literal", "value": "broadcast"}}], "groups": [{"title": "Properties", "children": [245, 246, 244]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 403, "character": 24, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L403"}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 247, "name": "on", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Creates an event handler that listens to changes."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 409, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L409"}], "typeParameters": [{"id": 248, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 249, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 409, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L409"}], "indexSignatures": [{"id": 250, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 409, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L409"}], "parameters": [{"id": 251, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "parameters": [{"id": 252, "name": "type", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "literal", "value": "system"}}, {"id": 253, "name": "filter", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 254, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}}}}, {"id": 255, "name": "callback", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 256, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 412, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L412"}], "signatures": [{"id": 257, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 412, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L412"}], "parameters": [{"id": 258, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}]}, {"id": 114, "name": "presenceState", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 315, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L315"}], "signatures": [{"id": 115, "name": "presenceState", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 315, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L315"}], "typeParameters": [{"id": 116, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 117, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 316, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L316"}], "indexSignatures": [{"id": 118, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 316, "character": 16, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L316"}], "parameters": [{"id": 119, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}, "default": {"type": "reflection", "declaration": {"id": 120, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}}}}], "type": {"type": "reference", "target": 608, "typeArguments": [{"type": "reference", "target": 116, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePresenceState", "package": "@supabase/realtime-js"}}]}, {"id": 259, "name": "send", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 430, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L430"}], "signatures": [{"id": 260, "name": "send", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Sends a message into the channel."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 430, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L430"}], "parameters": [{"id": 261, "name": "args", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Arguments to send to channel"}]}, "type": {"type": "reflection", "declaration": {"id": 262, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 264, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The name of the event being sent"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 433, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L433"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 265, "name": "payload", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Payload to be sent"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 434, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L434"}], "type": {"type": "intrinsic", "name": "any"}}, {"id": 263, "name": "type", "variant": "declaration", "kind": 1024, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The type of event to send"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 432, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L432"}], "type": {"type": "union", "types": [{"type": "literal", "value": "presence"}, {"type": "literal", "value": "broadcast"}, {"type": "literal", "value": "postgres_changes"}]}}], "groups": [{"title": "Properties", "children": [264, 265, 263]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 431, "character": 10, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L431"}], "indexSignatures": [{"id": 266, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 435, "character": 6, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L435"}], "parameters": [{"id": 267, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}, {"id": 268, "name": "opts", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Options to be used during the send process"}]}, "type": {"type": "reflection", "declaration": {"id": 269, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 437, "character": 10, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L437"}], "indexSignatures": [{"id": 270, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 437, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L437"}], "parameters": [{"id": 271, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "reference", "target": 371, "name": "RealtimeChannelSendResponse", "package": "@supabase/realtime-js"}], "name": "Promise", "package": "typescript"}}]}, {"id": 106, "name": "subscribe", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 213, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L213"}], "signatures": [{"id": 107, "name": "subscribe", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Subscribe registers your client with the server"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 213, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L213"}], "parameters": [{"id": 108, "name": "callback", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 109, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 214, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L214"}], "signatures": [{"id": 110, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 214, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L214"}], "parameters": [{"id": 111, "name": "status", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 632, "name": "REALTIME_SUBSCRIBE_STATES", "package": "@supabase/realtime-js"}}, {"id": 112, "name": "err", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Error"}, "name": "Error", "package": "typescript"}}], "type": {"type": "intrinsic", "name": "void"}}]}}}, {"id": 113, "name": "timeout", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "..."}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}]}, {"id": 121, "name": "track", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 321, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L321"}], "signatures": [{"id": 122, "name": "track", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 321, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L321"}], "parameters": [{"id": 123, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 124, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 322, "character": 13, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L322"}], "indexSignatures": [{"id": 125, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 322, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L322"}], "parameters": [{"id": 126, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}, {"id": 127, "name": "opts", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 128, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 323, "character": 10, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L323"}], "indexSignatures": [{"id": 129, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 323, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L323"}], "parameters": [{"id": 130, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "reference", "target": 371, "name": "RealtimeChannelSendResponse", "package": "@supabase/realtime-js"}], "name": "Promise", "package": "typescript"}}]}, {"id": 278, "name": "unsubscribe", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 507, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L507"}], "signatures": [{"id": 279, "name": "unsubscribe", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Leaves the channel.\n\nUnsubscribes from server events, and instructs channel to terminate on server.\nTriggers onClose() hooks.\n\nTo receive leave acknowledgements, use the a "}, {"kind": "code", "text": "`receive`"}, {"kind": "text", "text": " hook to bind to the server ack, ie:\nchannel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 507, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L507"}], "parameters": [{"id": 280, "name": "timeout", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "..."}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": "error"}, {"type": "literal", "value": "ok"}, {"type": "literal", "value": "timed out"}]}], "name": "Promise", "package": "typescript"}}]}, {"id": 131, "name": "untrack", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 335, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L335"}], "signatures": [{"id": 132, "name": "untrack", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 335, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L335"}], "parameters": [{"id": 133, "name": "opts", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 134, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 336, "character": 10, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L336"}], "indexSignatures": [{"id": 135, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 336, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L336"}], "parameters": [{"id": 136, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "reference", "target": 371, "name": "RealtimeChannelSendResponse", "package": "@supabase/realtime-js"}], "name": "Promise", "package": "typescript"}}]}, {"id": 272, "name": "updateJoinPayload", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 494, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L494"}], "signatures": [{"id": 273, "name": "updateJoinPayload", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 494, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L494"}], "parameters": [{"id": 274, "name": "payload", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 275, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 494, "character": 29, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L494"}], "indexSignatures": [{"id": 276, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 494, "character": 31, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L494"}], "parameters": [{"id": 277, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "intrinsic", "name": "void"}}]}], "groups": [{"title": "Constructors", "children": [76]}, {"title": "Properties", "children": [81, 100, 95, 96, 104, 99, 102, 98, 97, 105, 94, 101, 93, 103]}, {"title": "Methods", "children": [137, 114, 259, 106, 121, 278, 131, 272]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 127, "character": 21, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L127"}]}, {"id": 372, "name": "RealtimeClient", "variant": "declaration", "kind": 128, "flags": {}, "children": [{"id": 373, "name": "constructor", "variant": "declaration", "kind": 512, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 144, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L144"}], "signatures": [{"id": 374, "name": "RealtimeClient", "variant": "signature", "kind": 16384, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Initializes the Socket."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 144, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L144"}], "parameters": [{"id": 375, "name": "endPoint", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)"}]}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 376, "name": "options", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "type": {"type": "reference", "target": 515, "name": "RealtimeClientOptions", "package": "@supabase/realtime-js", "highlightedProperties": {"transport": [{"kind": "text", "text": "The Websocket Transport, for example WebSocket. This can be a custom implementation"}], "timeout": [{"kind": "text", "text": "The default timeout in milliseconds to trigger push timeouts."}], "params": [{"kind": "text", "text": "The optional params to pass when connecting."}], "headers": [{"kind": "text", "text": "The optional headers to pass when connecting."}], "heartbeatIntervalMs": [{"kind": "text", "text": "The millisec interval to send a heartbeat message."}], "logger": [{"kind": "text", "text": "The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log("}, {"kind": "code", "text": "`${kind}: ${msg}`"}, {"kind": "text", "text": ", data) }"}], "logLevel": [{"kind": "text", "text": "Sets the log level for Realtime"}], "encode": [{"kind": "text", "text": "The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))"}], "decode": [{"kind": "text", "text": "The function to decode incoming messages. Defaults to Serializer's decode."}], "reconnectAfterMs": [{"kind": "text", "text": "he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off."}], "worker": [{"kind": "text", "text": "Use Web Worker to set a side flow. Defaults to false."}], "workerUrl": [{"kind": "text", "text": "The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive."}]}}}], "type": {"type": "reference", "target": 372, "name": "RealtimeClient", "package": "@supabase/realtime-js", "qualifiedName": "default"}}]}, {"id": 419, "name": "accessToken", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 121, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L121"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reflection", "declaration": {"id": 420, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 121, "character": 16, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L121"}], "signatures": [{"id": 421, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 121, "character": 16, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L121"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}], "name": "Promise", "package": "typescript"}}]}}]}, "defaultValue": "null"}, {"id": 377, "name": "accessTokenValue", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 87, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L87"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}, "defaultValue": "null"}, {"id": 378, "name": "<PERSON><PERSON><PERSON><PERSON>", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 88, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L88"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}, "defaultValue": "null"}, {"id": 379, "name": "channels", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 89, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L89"}], "type": {"type": "array", "elementType": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, "defaultValue": "[]"}, {"id": 402, "name": "conn", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 106, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L106"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "target": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "WebSocketLike"}, "name": "WebSocketLike", "package": "@supabase/realtime-js"}]}, "defaultValue": "null"}, {"id": 400, "name": "decode", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 104, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L104"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 399, "name": "encode", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 103, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L103"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 380, "name": "endPoint", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 90, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L90"}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "''"}, {"id": 411, "name": "fetch", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 120, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L120"}], "type": {"type": "reflection", "declaration": {"id": 412, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "node_modules/typescript/lib/lib.dom.d.ts", "line": 28708, "character": 17}, {"fileName": "node_modules/@types/node/globals.d.ts", "line": 374, "character": 13}], "signatures": [{"id": 413, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "[MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/fetch)"}]}, "sources": [{"fileName": "node_modules/typescript/lib/lib.dom.d.ts", "line": 28708, "character": 17}], "parameters": [{"id": 414, "name": "input", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "RequestInfo"}, "name": "RequestInfo", "package": "typescript"}, {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "URL"}, "name": "URL", "package": "typescript"}]}}, {"id": 415, "name": "init", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "RequestInit"}, "name": "RequestInit", "package": "typescript"}}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "Response"}, "name": "Response", "package": "typescript"}], "name": "Promise", "package": "typescript"}}, {"id": 416, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "[MDN Reference](https://developer.mozilla.org/docs/Web/API/Window/fetch)"}]}, "sources": [{"fileName": "node_modules/@types/node/globals.d.ts", "line": 374, "character": 13}], "parameters": [{"id": 417, "name": "input", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "Request"}, "name": "Request", "package": "typescript"}, {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "URL"}, "name": "URL", "package": "typescript"}]}}, {"id": 418, "name": "init", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "RequestInit"}, "name": "RequestInit", "package": "typescript"}}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "Response"}, "name": "Response", "package": "typescript"}], "name": "Promise", "package": "typescript"}}]}}}, {"id": 382, "name": "headers", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 92, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L92"}], "type": {"type": "reflection", "declaration": {"id": 383, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 92, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L92"}], "indexSignatures": [{"id": 384, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 92, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L92"}], "parameters": [{"id": 385, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}, "defaultValue": "DEFAULT_HEADERS"}, {"id": 392, "name": "heartbeatIntervalMs", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 96, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L96"}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "25000"}, {"id": 393, "name": "heartbeatTimer", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 97, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L97"}], "type": {"type": "union", "types": [{"type": "intrinsic", "name": "undefined"}, {"type": "reference", "target": {"sourceFileName": "node_modules/@types/node/timers.d.ts", "qualifiedName": "__global.NodeJS.Timeout"}, "name": "Timeout", "package": "@types/node", "qualifiedName": "__global.NodeJS.Timeout"}]}, "defaultValue": "undefined"}, {"id": 381, "name": "httpEndpoint", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 91, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L91"}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "''"}, {"id": 397, "name": "logger", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 101, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L101"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}, "defaultValue": "noop"}, {"id": 398, "name": "logLevel", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 102, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L102"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/constants.ts", "qualifiedName": "LOG_LEVEL"}, "name": "LOG_LEVEL", "package": "@supabase/realtime-js"}}, {"id": 386, "name": "params", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 93, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L93"}], "type": {"type": "reflection", "declaration": {"id": 387, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 93, "character": 11, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L93"}], "indexSignatures": [{"id": 388, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 93, "character": 13, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L93"}], "parameters": [{"id": 389, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}, "defaultValue": "{}"}, {"id": 394, "name": "pendingHeartbeatRef", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 98, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L98"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}, "defaultValue": "null"}, {"id": 401, "name": "reconnectAfterMs", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 105, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L105"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 396, "name": "reconnectTimer", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 100, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L100"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/timer.ts", "qualifiedName": "default"}, "name": "Timer", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 395, "name": "ref", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 99, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L99"}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "0"}, {"id": 403, "name": "send<PERSON><PERSON><PERSON>", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 107, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L107"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, "defaultValue": "[]"}, {"id": 404, "name": "serializer", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 108, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L108"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/serializer.ts", "qualifiedName": "default"}, "name": "Serializer", "package": "@supabase/realtime-js", "qualifiedName": "default"}, "defaultValue": "..."}, {"id": 405, "name": "stateChangeCallbacks", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 109, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L109"}], "type": {"type": "reflection", "declaration": {"id": 406, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 408, "name": "close", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 111, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L111"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}}, {"id": 409, "name": "error", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 112, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L112"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}}, {"id": 410, "name": "message", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 113, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L113"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}}, {"id": 407, "name": "open", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 110, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L110"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}}], "groups": [{"title": "Properties", "children": [408, 409, 410, 407]}], "sources": [{"fileName": "src/RealtimeClient.ts", "line": 109, "character": 24, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L109"}]}}, "defaultValue": "..."}, {"id": 390, "name": "timeout", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 94, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L94"}], "type": {"type": "intrinsic", "name": "number"}, "defaultValue": "DEFAULT_TIMEOUT"}, {"id": 391, "name": "transport", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 95, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L95"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "reference", "target": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "WebSocketLikeConstructor"}, "name": "WebSocketLikeConstructor", "package": "@supabase/realtime-js"}]}}, {"id": 422, "name": "worker", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 122, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L122"}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 424, "name": "workerRef", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 124, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L124"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "Worker"}, "name": "Worker", "package": "typescript"}}, {"id": 423, "name": "workerUrl", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 123, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L123"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 449, "name": "channel", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 331, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L331"}], "signatures": [{"id": 450, "name": "channel", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 331, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L331"}], "parameters": [{"id": 451, "name": "topic", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 452, "name": "params", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 359, "name": "RealtimeChannelOptions", "package": "@supabase/realtime-js"}, "defaultValue": "..."}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}]}, {"id": 425, "name": "connect", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 202, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L202"}], "signatures": [{"id": 426, "name": "connect", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Connects the socket, unless already connected."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 202, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L202"}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 445, "name": "connectionState", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 311, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L311"}], "signatures": [{"id": 446, "name": "connectionState", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Returns the current state of the socket."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 311, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L311"}], "type": {"type": "reference", "target": {"sourceFileName": "src/lib/constants.ts", "qualifiedName": "CONNECTION_STATE"}, "name": "CONNECTION_STATE", "package": "@supabase/realtime-js"}}]}, {"id": 429, "name": "disconnect", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 252, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L252"}], "signatures": [{"id": 430, "name": "disconnect", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Disconnects the socket."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 252, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L252"}], "parameters": [{"id": 431, "name": "code", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "A numeric status code to send on disconnect."}]}, "type": {"type": "intrinsic", "name": "number"}}, {"id": 432, "name": "reason", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "A custom reason for the disconnect."}]}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 427, "name": "endpointURL", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 239, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L239"}], "signatures": [{"id": 428, "name": "endpointURL", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Returns the URL of the websocket."}], "blockTags": [{"tag": "@returns", "content": [{"kind": "text", "text": "string The URL of the websocket."}]}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 239, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L239"}], "type": {"type": "intrinsic", "name": "string"}}]}, {"id": 461, "name": "flushSendBuffer", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 421, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L421"}], "signatures": [{"id": 462, "name": "flushSendBuffer", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Flushes send buffer"}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 421, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L421"}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 433, "name": "getChannels", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 270, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L270"}], "signatures": [{"id": 434, "name": "getChannels", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Returns all created channels"}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 270, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L270"}], "type": {"type": "array", "elementType": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}}]}, {"id": 447, "name": "isConnected", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 327, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L327"}], "signatures": [{"id": 448, "name": "isConnected", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Returns "}, {"kind": "code", "text": "`true`"}, {"kind": "text", "text": " is the connection is open."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 327, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L327"}], "type": {"type": "intrinsic", "name": "boolean"}}]}, {"id": 440, "name": "log", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 304, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L304"}], "signatures": [{"id": 441, "name": "log", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Logs the message.\n\nFor customized logging, "}, {"kind": "code", "text": "`this.logger`"}, {"kind": "text", "text": " can be overridden."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 304, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L304"}], "parameters": [{"id": 442, "name": "kind", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 443, "name": "msg", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 444, "name": "data", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "any"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 453, "name": "push", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 345, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L345"}], "signatures": [{"id": 454, "name": "push", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Push out a message if the socket is connected.\n\nIf the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 345, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L345"}], "parameters": [{"id": 455, "name": "data", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "reference", "target": 540, "name": "RealtimeMessage", "package": "@supabase/realtime-js"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 438, "name": "removeAllChannels", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 291, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L291"}], "signatures": [{"id": 439, "name": "removeAllChannels", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Unsubscribes and removes all channels"}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 291, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L291"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "array", "elementType": {"type": "reference", "target": 617, "name": "RealtimeRemoveChannelResponse", "package": "@supabase/realtime-js"}}], "name": "Promise", "package": "typescript"}}]}, {"id": 435, "name": "removeChannel", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 278, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L278"}], "signatures": [{"id": 436, "name": "removeChannel", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Unsubscribes and removes a single channel"}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 278, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L278"}], "parameters": [{"id": 437, "name": "channel", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A RealtimeChannel instance"}]}, "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "reference", "target": 617, "name": "RealtimeRemoveChannelResponse", "package": "@supabase/realtime-js"}], "name": "Promise", "package": "typescript"}}]}, {"id": 459, "name": "sendHeartbeat", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 395, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L395"}], "signatures": [{"id": 460, "name": "sendHeartbeat", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Sends a heartbeat message if the socket is connected."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 395, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L395"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "package": "typescript"}}]}, {"id": 456, "name": "setAuth", "variant": "declaration", "kind": 2048, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 369, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L369"}], "signatures": [{"id": 457, "name": "setAuth", "variant": "signature", "kind": 4096, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n\nIf param is null it will use the "}, {"kind": "code", "text": "`accessToken`"}, {"kind": "text", "text": " callback function or the token set on the client.\n\nOn callback used, it will set the value of the token internal to the client."}]}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 369, "character": 8, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L369"}], "parameters": [{"id": 458, "name": "token", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "A JWT string to override the token set on the client."}]}, "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}, "defaultValue": "null"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "intrinsic", "name": "void"}], "name": "Promise", "package": "typescript"}}]}], "groups": [{"title": "Constructors", "children": [373]}, {"title": "Properties", "children": [419, 377, 378, 379, 402, 400, 399, 380, 411, 382, 392, 393, 381, 397, 398, 386, 394, 401, 396, 395, 403, 404, 405, 390, 391, 422, 424, 423]}, {"title": "Methods", "children": [449, 425, 445, 429, 427, 461, 433, 447, 440, 453, 438, 435, 459, 456]}], "sources": [{"fileName": "src/RealtimeClient.ts", "line": 86, "character": 21, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L86"}]}, {"id": 1, "name": "RealtimePresence", "variant": "declaration", "kind": 128, "flags": {}, "children": [{"id": 36, "name": "constructor", "variant": "declaration", "kind": 512, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 84, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L84"}], "signatures": [{"id": 37, "name": "RealtimePresence", "variant": "signature", "kind": 16384, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "Initializes the Presence."}]}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 84, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L84"}], "parameters": [{"id": 38, "name": "channel", "variant": "param", "kind": 32768, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The RealtimeChannel"}]}, "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 39, "name": "opts", "variant": "param", "kind": 32768, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "The options,\n       for example "}, {"kind": "code", "text": "`{events: {state: 'state', diff: 'diff'}}`"}]}, "type": {"type": "reference", "target": {"sourceFileName": "node_modules/@types/phoenix/index.d.ts", "qualifiedName": "PresenceOpts"}, "name": "PresenceOpts", "package": "@types/phoenix"}}], "type": {"type": "reference", "target": 1, "name": "RealtimePresence", "package": "@supabase/realtime-js", "qualifiedName": "default"}}]}, {"id": 44, "name": "caller", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 67, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L67"}], "type": {"type": "reflection", "declaration": {"id": 45, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 46, "name": "onJoin", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 68, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L68"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/@types/phoenix/index.d.ts", "qualifiedName": "PresenceOnJoinCallback"}, "name": "PresenceOnJoinCallback", "package": "@types/phoenix"}}, {"id": 47, "name": "onLeave", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 69, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L69"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/@types/phoenix/index.d.ts", "qualifiedName": "PresenceOnLeaveCallback"}, "name": "PresenceOnLeaveCallback", "package": "@types/phoenix"}}, {"id": 48, "name": "onSync", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 70, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L70"}], "type": {"type": "reflection", "declaration": {"id": 49, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 70, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L70"}], "signatures": [{"id": 50, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 70, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L70"}], "type": {"type": "intrinsic", "name": "void"}}]}}}], "groups": [{"title": "Properties", "children": [46, 47, 48]}], "sources": [{"fileName": "src/RealtimePresence.ts", "line": 67, "character": 10, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L67"}]}}, "defaultValue": "..."}, {"id": 51, "name": "channel", "variant": "declaration", "kind": 1024, "flags": {"isPublic": true}, "comment": {"summary": [{"kind": "text", "text": "The RealtimeChannel"}]}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 84, "character": 21, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L84"}], "type": {"type": "reference", "target": 65, "name": "RealtimeChannel", "package": "@supabase/realtime-js", "qualifiedName": "default"}}, {"id": 43, "name": "joinRef", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 66, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L66"}], "type": {"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}, "defaultValue": "null"}, {"id": 42, "name": "pendingDiffs", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 65, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L65"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "RawPresenceDiff"}, "name": "RawPresenceDiff", "package": "@supabase/realtime-js"}}, "defaultValue": "[]"}, {"id": 40, "name": "state", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 64, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L64"}], "type": {"type": "reference", "target": 608, "typeArguments": [{"type": "reflection", "declaration": {"id": 41, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}}}], "name": "RealtimePresenceState", "package": "@supabase/realtime-js"}, "defaultValue": "{}"}], "groups": [{"title": "Constructors", "children": [36]}, {"title": "Properties", "children": [44, 51, 43, 42, 40]}], "sources": [{"fileName": "src/RealtimePresence.ts", "line": 63, "character": 21, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L63"}]}, {"id": 359, "name": "RealtimeChannelOptions", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 16, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L16"}], "type": {"type": "reflection", "declaration": {"id": 360, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 361, "name": "config", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 17, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L17"}], "type": {"type": "reflection", "declaration": {"id": 362, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 363, "name": "broadcast", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "self option enables client to receive message it broadcast\nack option instructs server to acknowledge that broadcast message was received"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 22, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L22"}], "type": {"type": "reflection", "declaration": {"id": 364, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 366, "name": "ack", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 22, "character": 34, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L22"}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 365, "name": "self", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 22, "character": 18, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L22"}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [366, 365]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 22, "character": 16, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L22"}]}}}, {"id": 367, "name": "presence", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "key option is used to track presence payload across clients"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 26, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L26"}], "type": {"type": "reflection", "declaration": {"id": 368, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 369, "name": "key", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 26, "character": 17, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L26"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [369]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 26, "character": 15, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L26"}]}}}, {"id": 370, "name": "private", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "defines if the channel is private or not and if RLS policies will be used to check data"}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 30, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L30"}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "children": [363, 367, 370]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 17, "character": 10, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L17"}]}}}], "groups": [{"title": "Properties", "children": [361]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 16, "character": 37, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L16"}]}}}, {"id": 371, "name": "RealtimeChannelSendResponse", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 88, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L88"}], "type": {"type": "union", "types": [{"type": "literal", "value": "ok"}, {"type": "literal", "value": "timed out"}, {"type": "literal", "value": "error"}]}}, {"id": 515, "name": "RealtimeClientOptions", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 60, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L60"}], "type": {"type": "reflection", "declaration": {"id": 516, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 537, "name": "accessToken", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 76, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L76"}], "type": {"type": "reflection", "declaration": {"id": 538, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 76, "character": 16, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L76"}], "signatures": [{"id": 539, "name": "__type", "variant": "signature", "kind": 4096, "flags": {}, "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Promise"}, "typeArguments": [{"type": "union", "types": [{"type": "intrinsic", "name": "string"}, {"type": "literal", "value": null}]}], "name": "Promise", "package": "typescript"}}]}}}, {"id": 522, "name": "decode", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 66, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L66"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 521, "name": "encode", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 65, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L65"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 534, "name": "fetch", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 73, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L73"}], "type": {"type": "reference", "target": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "<PERSON>tch"}, "name": "<PERSON>tch", "package": "@supabase/realtime-js"}}, {"id": 524, "name": "headers", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 68, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L68"}], "type": {"type": "reflection", "declaration": {"id": 525, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 68, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L68"}], "indexSignatures": [{"id": 526, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 68, "character": 14, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L68"}], "parameters": [{"id": 527, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}}}, {"id": 519, "name": "heartbeatIntervalMs", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 63, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L63"}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 532, "name": "log_level", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 71, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L71"}], "type": {"type": "reference", "target": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "LogLevel"}, "name": "LogLevel", "package": "@supabase/realtime-js"}}, {"id": 520, "name": "logger", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 64, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L64"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 533, "name": "logLevel", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 72, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L72"}], "type": {"type": "reference", "target": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "LogLevel"}, "name": "LogLevel", "package": "@supabase/realtime-js"}}, {"id": 528, "name": "params", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 69, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L69"}], "type": {"type": "reflection", "declaration": {"id": 529, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 69, "character": 11, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L69"}], "indexSignatures": [{"id": 530, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 69, "character": 13, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L69"}], "parameters": [{"id": 531, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}, {"id": 523, "name": "reconnectAfterMs", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 67, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L67"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Function"}, "name": "Function", "package": "typescript"}}, {"id": 518, "name": "timeout", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 62, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L62"}], "type": {"type": "intrinsic", "name": "number"}}, {"id": 517, "name": "transport", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 61, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L61"}], "type": {"type": "reference", "target": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "WebSocketLikeConstructor"}, "name": "WebSocketLikeConstructor", "package": "@supabase/realtime-js"}}, {"id": 535, "name": "worker", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 74, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L74"}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 536, "name": "workerUrl", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 75, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L75"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [537, 522, 521, 534, 524, 519, 532, 520, 533, 528, 523, 518, 517, 535, 536]}], "sources": [{"fileName": "src/RealtimeClient.ts", "line": 60, "character": 36, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L60"}]}}}, {"id": 540, "name": "RealtimeMessage", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 32, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L32"}], "type": {"type": "reflection", "declaration": {"id": 541, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 543, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 34, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L34"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 546, "name": "join_ref", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 37, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L37"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 544, "name": "payload", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 35, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L35"}], "type": {"type": "intrinsic", "name": "any"}}, {"id": 545, "name": "ref", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 36, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L36"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 542, "name": "topic", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 33, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L33"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [543, 546, 544, 545, 542]}], "sources": [{"fileName": "src/RealtimeClient.ts", "line": 32, "character": 30, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L32"}]}}}, {"id": 547, "name": "RealtimePostgresChangesFilter", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 67, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L67"}], "typeParameters": [{"id": 553, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "templateLiteral", "head": "", "tail": [[{"type": "reference", "target": 623, "name": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "package": "@supabase/realtime-js"}, ""]]}}], "type": {"type": "reflection", "declaration": {"id": 548, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 549, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The type of database change to listen to."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 73, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L73"}], "type": {"type": "reference", "target": 553, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}}, {"id": 552, "name": "filter", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "Receive database changes when filter is matched."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 85, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L85"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 550, "name": "schema", "variant": "declaration", "kind": 1024, "flags": {}, "comment": {"summary": [{"kind": "text", "text": "The database schema to listen to."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 77, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L77"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 551, "name": "table", "variant": "declaration", "kind": 1024, "flags": {"isOptional": true}, "comment": {"summary": [{"kind": "text", "text": "The database table to listen to."}]}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 81, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L81"}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "children": [549, 552, 550, 551]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 69, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L69"}]}}}, {"id": 554, "name": "RealtimePostgresChangesPayload", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 62, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L62"}], "typeParameters": [{"id": 555, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 556, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 62, "character": 53, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L62"}], "indexSignatures": [{"id": 557, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 62, "character": 55, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L62"}], "parameters": [{"id": 558, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "union", "types": [{"type": "reference", "target": 559, "typeArguments": [{"type": "reference", "target": 555, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresInsertPayload", "package": "@supabase/realtime-js"}, {"type": "reference", "target": 569, "typeArguments": [{"type": "reference", "target": 555, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresUpdatePayload", "package": "@supabase/realtime-js"}, {"type": "reference", "target": 578, "typeArguments": [{"type": "reference", "target": 555, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "RealtimePostgresDeletePayload", "package": "@supabase/realtime-js"}]}}, {"id": 578, "name": "RealtimePostgresDeletePayload", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 55, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L55"}], "typeParameters": [{"id": 584, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 585, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 55, "character": 52, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L55"}], "indexSignatures": [{"id": 586, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 55, "character": 54, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L55"}], "parameters": [{"id": 587, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "intersection", "types": [{"type": "reference", "target": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresChangesPayloadBase"}, "name": "RealtimePostgresChangesPayloadBase", "package": "@supabase/realtime-js"}, {"type": "reflection", "declaration": {"id": 579, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 580, "name": "eventType", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 57, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L57"}], "type": {"type": "templateLiteral", "head": "", "tail": [[{"type": "reference", "target": 627, "name": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE", "package": "@supabase/realtime-js"}, ""]]}}, {"id": 581, "name": "new", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 58, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L58"}], "type": {"type": "reflection", "declaration": {"id": 582, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 58, "character": 9, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L58"}]}}}, {"id": 583, "name": "old", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 59, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L59"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Partial"}, "typeArguments": [{"type": "reference", "target": 584, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Partial", "package": "typescript"}}], "groups": [{"title": "Properties", "children": [580, 581, 583]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 56, "character": 39, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L56"}]}}]}}, {"id": 559, "name": "RealtimePostgresInsertPayload", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 41, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L41"}], "typeParameters": [{"id": 565, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 566, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 41, "character": 52, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L41"}], "indexSignatures": [{"id": 567, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 41, "character": 54, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L41"}], "parameters": [{"id": 568, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "intersection", "types": [{"type": "reference", "target": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresChangesPayloadBase"}, "name": "RealtimePostgresChangesPayloadBase", "package": "@supabase/realtime-js"}, {"type": "reflection", "declaration": {"id": 560, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 561, "name": "eventType", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 43, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L43"}], "type": {"type": "templateLiteral", "head": "", "tail": [[{"type": "reference", "target": 625, "name": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT", "package": "@supabase/realtime-js"}, ""]]}}, {"id": 562, "name": "new", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 44, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L44"}], "type": {"type": "reference", "target": 565, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}}, {"id": 563, "name": "old", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 45, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L45"}], "type": {"type": "reflection", "declaration": {"id": 564, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 45, "character": 9, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L45"}]}}}], "groups": [{"title": "Properties", "children": [561, 562, 563]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 42, "character": 39, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L42"}]}}]}}, {"id": 569, "name": "RealtimePostgresUpdatePayload", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 48, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L48"}], "typeParameters": [{"id": 574, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 575, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 48, "character": 52, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L48"}], "indexSignatures": [{"id": 576, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 48, "character": 54, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L48"}], "parameters": [{"id": 577, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "intersection", "types": [{"type": "reference", "target": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresChangesPayloadBase"}, "name": "RealtimePostgresChangesPayloadBase", "package": "@supabase/realtime-js"}, {"type": "reflection", "declaration": {"id": 570, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 571, "name": "eventType", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 50, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L50"}], "type": {"type": "templateLiteral", "head": "", "tail": [[{"type": "reference", "target": 626, "name": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE", "package": "@supabase/realtime-js"}, ""]]}}, {"id": 572, "name": "new", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 51, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L51"}], "type": {"type": "reference", "target": 574, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}}, {"id": 573, "name": "old", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 52, "character": 4, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L52"}], "type": {"type": "reference", "target": {"sourceFileName": "node_modules/typescript/lib/lib.es5.d.ts", "qualifiedName": "Partial"}, "typeArguments": [{"type": "reference", "target": 574, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Partial", "package": "typescript"}}], "groups": [{"title": "Properties", "children": [571, 572, 573]}], "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 49, "character": 39, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L49"}]}}]}}, {"id": 588, "name": "RealtimePresenceJoinPayload", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 21, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L21"}], "typeParameters": [{"id": 594, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 595, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 21, "character": 50, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L21"}], "indexSignatures": [{"id": 596, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 21, "character": 52, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L21"}], "parameters": [{"id": 597, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 589, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 592, "name": "currentPresences", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 24, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L24"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "Presence"}, "typeArguments": [{"type": "reference", "target": 594, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Presence", "package": "@supabase/realtime-js"}}}, {"id": 590, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 22, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L22"}], "type": {"type": "templateLiteral", "head": "", "tail": [[{"type": "reference", "target": 630, "name": "REALTIME_PRESENCE_LISTEN_EVENTS.JOIN", "package": "@supabase/realtime-js"}, ""]]}}, {"id": 591, "name": "key", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 23, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L23"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 593, "name": "newPresences", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 25, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L25"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "Presence"}, "typeArguments": [{"type": "reference", "target": 594, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Presence", "package": "@supabase/realtime-js"}}}], "groups": [{"title": "Properties", "children": [592, 590, 591, 593]}], "sources": [{"fileName": "src/RealtimePresence.ts", "line": 21, "character": 76, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L21"}]}}}, {"id": 598, "name": "RealtimePresenceLeavePayload", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 28, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L28"}], "typeParameters": [{"id": 604, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 605, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 28, "character": 51, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L28"}], "indexSignatures": [{"id": 606, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 28, "character": 53, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L28"}], "parameters": [{"id": 607, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 599, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "children": [{"id": 602, "name": "currentPresences", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 31, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L31"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "Presence"}, "typeArguments": [{"type": "reference", "target": 604, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Presence", "package": "@supabase/realtime-js"}}}, {"id": 600, "name": "event", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 29, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L29"}], "type": {"type": "templateLiteral", "head": "", "tail": [[{"type": "reference", "target": 631, "name": "REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE", "package": "@supabase/realtime-js"}, ""]]}}, {"id": 601, "name": "key", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 30, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L30"}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 603, "name": "leftPresences", "variant": "declaration", "kind": 1024, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 32, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L32"}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "Presence"}, "typeArguments": [{"type": "reference", "target": 604, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Presence", "package": "@supabase/realtime-js"}}}], "groups": [{"title": "Properties", "children": [602, 600, 601, 603]}], "sources": [{"fileName": "src/RealtimePresence.ts", "line": 28, "character": 77, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L28"}]}}}, {"id": 608, "name": "RealtimePresenceState", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 17, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L17"}], "typeParameters": [{"id": 612, "name": "T", "variant": "typeParam", "kind": 131072, "flags": {}, "type": {"type": "reflection", "declaration": {"id": 613, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 17, "character": 44, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L17"}], "indexSignatures": [{"id": 614, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 17, "character": 46, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L17"}], "parameters": [{"id": 615, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "any"}}]}}, "default": {"type": "reflection", "declaration": {"id": 616, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 17, "character": 69, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L17"}]}}}], "type": {"type": "reflection", "declaration": {"id": 609, "name": "__type", "variant": "declaration", "kind": 65536, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 17, "character": 75, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L17"}], "indexSignatures": [{"id": 610, "name": "__index", "variant": "signature", "kind": 8192, "flags": {}, "sources": [{"fileName": "src/RealtimePresence.ts", "line": 18, "character": 2, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimePresence.ts#L18"}], "parameters": [{"id": 611, "name": "key", "variant": "param", "kind": 32768, "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "array", "elementType": {"type": "reference", "target": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "Presence"}, "typeArguments": [{"type": "reference", "target": 612, "name": "T", "package": "@supabase/realtime-js", "refersToTypeParameter": true}], "name": "Presence", "package": "@supabase/realtime-js"}}}]}}}, {"id": 617, "name": "RealtimeRemoveChannelResponse", "variant": "declaration", "kind": 2097152, "flags": {}, "sources": [{"fileName": "src/RealtimeClient.ts", "line": 40, "character": 12, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeClient.ts#L40"}], "type": {"type": "union", "types": [{"type": "literal", "value": "ok"}, {"type": "literal", "value": "timed out"}, {"type": "literal", "value": "error"}]}}, {"id": 637, "name": "REALTIME_CHANNEL_STATES", "variant": "declaration", "kind": 32, "flags": {"isConst": true}, "sources": [{"fileName": "src/RealtimeChannel.ts", "line": 111, "character": 13, "url": "https://github.com/supabase/realtime-js/blob/527f3fb92e2d3a37544bdbecac3646db0f78dd6f/src/RealtimeChannel.ts#L111"}], "type": {"type": "query", "queryType": {"type": "reference", "target": {"sourceFileName": "src/lib/constants.ts", "qualifiedName": "CHANNEL_STATES"}, "name": "CHANNEL_STATES", "package": "@supabase/realtime-js", "preferValues": true}}, "defaultValue": "CHANNEL_STATES"}], "groups": [{"title": "Enumerations", "children": [618, 623, 628, 632]}, {"title": "Classes", "children": [65, 372, 1]}, {"title": "Type Aliases", "children": [359, 371, 515, 540, 547, 554, 578, 559, 569, 588, 598, 608, 617]}, {"title": "Variables", "children": [637]}], "packageName": "@supabase/realtime-js", "readme": [{"kind": "text", "text": "<br />\n<p align=\"center\">\n  <a href=\"https://supabase.io\">\n        <picture>\n      <source media=\"(prefers-color-scheme: dark)\" srcset=\"https://raw.githubusercontent.com/supabase/supabase/master/packages/common/assets/images/supabase-logo-wordmark--dark.svg\">\n      <source media=\"(prefers-color-scheme: light)\" srcset=\"https://raw.githubusercontent.com/supabase/supabase/master/packages/common/assets/images/supabase-logo-wordmark--light.svg\">\n      <img alt=\"Supabase Logo\" width=\"300\" src=\"https://raw.githubusercontent.com/supabase/supabase/master/packages/common/assets/images/logo-preview.jpg\">\n    </picture>\n  </a>\n\n  <h1 align=\"center\">Supabase Realtime Client</h1>\n\n  <h3 align=\"center\">Send ephemeral messages with <b>Broadcast</b>, track and synchronize state with <b>Presence</b>, and listen to database changes with <b>Postgres Change Data Capture (CDC)</b>.</h3>\n\n  <p align=\"center\">\n    <a href=\"https://supabase.com/docs/guides/realtime\">Guides</a>\n    ·\n    <a href=\"https://supabase.com/docs/reference/javascript\">Reference Docs</a>\n    ·\n    <a href=\"https://multiplayer.dev\">Multiplayer Demo</a>\n  </p>\n</p>\n\n# Overview\n\nThis client enables you to use the following Supabase Realtime's features:\n\n- **Broadcast**: send ephemeral messages from client to clients with minimal latency. Use cases include sharing cursor positions between users.\n- **Presence**: track and synchronize shared state across clients with the help of CRDTs. Use cases include tracking which users are currently viewing a specific webpage.\n- **Postgres Change Data Capture (CDC)**: listen for changes in your PostgreSQL database and send them to clients.\n\n# Usage\n\n## Installing the Package\n\n"}, {"kind": "code", "text": "```bash\nnpm install @supabase/realtime-js\n```"}, {"kind": "text", "text": "\n\n## Creating a Channel\n\n"}, {"kind": "code", "text": "```js\nimport { RealtimeClient } from '@supabase/realtime-js'\n\nconst client = new RealtimeClient(REALTIME_URL, {\n  params: {\n    apikey: API_KEY\n  },\n})\n\nconst channel = client.channel('test-channel', {})\n\nchannel.subscribe((status, err) => {\n  if (status === 'SUBSCRIBED') {\n    console.log('Connected!')\n  }\n\n  if (status === 'CHANNEL_ERROR') {\n    console.log(`There was an error subscribing to channel: ${err.message}`)\n  }\n\n  if (status === 'TIMED_OUT') {\n    console.log('Realtime server did not respond in time.')\n  }\n\n  if (status === 'CLOSED') {\n    console.log('Realtime channel was unexpectedly closed.')\n  }\n})\n```"}, {"kind": "text", "text": "\n\n### Notes:\n\n- "}, {"kind": "code", "text": "`REALTIME_URL`"}, {"kind": "text", "text": " is "}, {"kind": "code", "text": "`'ws://localhost:4000/socket'`"}, {"kind": "text", "text": " when developing locally and "}, {"kind": "code", "text": "`'wss://<project_ref>.supabase.co/realtime/v1'`"}, {"kind": "text", "text": " when connecting to your Supabase project.\n- "}, {"kind": "code", "text": "`API_KEY`"}, {"kind": "text", "text": " is a JWT whose claims must contain "}, {"kind": "code", "text": "`exp`"}, {"kind": "text", "text": " and "}, {"kind": "code", "text": "`role`"}, {"kind": "text", "text": " (existing database role).\n- Channel name can be any "}, {"kind": "code", "text": "`string`"}, {"kind": "text", "text": ".\n\n## Broadcast\n\nYour client can send and receive messages based on the "}, {"kind": "code", "text": "`event`"}, {"kind": "text", "text": ".\n\n"}, {"kind": "code", "text": "```js\n// Setup...\n\nconst channel = client.channel('broadcast-test', { broadcast: { ack: false, self: false } })\n\nchannel.on('broadcast', { event: 'some-event' }, (payload) =>\n  console.log(payload)\n)\n\nchannel.subscribe(async (status) => {\n  if (status === 'SUBSCRIBED') {\n    // Send message to other clients listening to 'broadcast-test' channel\n    await channel.send({\n      type: 'broadcast',\n      event: 'some-event',\n      payload: { hello: 'world' },\n    })\n  }\n})\n```"}, {"kind": "text", "text": "\n\n### Notes:\n\n- Setting "}, {"kind": "code", "text": "`ack`"}, {"kind": "text", "text": " to "}, {"kind": "code", "text": "`true`"}, {"kind": "text", "text": " means that the "}, {"kind": "code", "text": "`channel.send`"}, {"kind": "text", "text": " promise will resolve once server replies with acknowledgement that it received the broadcast message request.\n- Setting "}, {"kind": "code", "text": "`self`"}, {"kind": "text", "text": " to "}, {"kind": "code", "text": "`true`"}, {"kind": "text", "text": " means that the client will receive the broadcast message it sent out.\n- Setting "}, {"kind": "code", "text": "`private`"}, {"kind": "text", "text": " to "}, {"kind": "code", "text": "`true`"}, {"kind": "text", "text": " means that the client will use RLS to determine if the user can connect or not to a given channel.\n\n## Presence\n\nYour client can track and sync state that's stored in the channel.\n\n"}, {"kind": "code", "text": "```js\n// Setup...\n\nconst channel = client.channel(\n  'presence-test',\n  {\n    config: {\n      presence: {\n        key: ''\n      }\n    }\n  }\n)\n\nchannel.on('presence', { event: 'sync' }, () => {\n  console.log('Online users: ', channel.presenceState())\n})\n\nchannel.on('presence', { event: 'join' }, ({ newPresences }) => {\n  console.log('New users have joined: ', newPresences)\n})\n\nchannel.on('presence', { event: 'leave' }, ({ leftPresences }) => {\n  console.log('Users have left: ', leftPresences)\n})\n\nchannel.subscribe(async (status) => {\n  if (status === 'SUBSCRIBED') {\n    const status = await channel.track({ 'user_id': 1 })\n    console.log(status)\n  }\n})\n```"}, {"kind": "text", "text": "\n\n## Postgres CDC\n\nReceive database changes on the client.\n\n"}, {"kind": "code", "text": "```js\n// Setup...\n\nconst channel = client.channel('db-changes')\n\nchannel.on('postgres_changes', { event: '*', schema: 'public' }, (payload) => {\n  console.log('All changes in public schema: ', payload)\n})\n\nchannel.on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'messages' }, (payload) => {\n  console.log('All inserts in messages table: ', payload)\n})\n\nchannel.on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'users', filter: 'username=eq.Realtime' }, (payload) => {\n  console.log('All updates on users table when username is Realtime: ', payload)\n})\n\nchannel.subscribe(async (status) => {\n  if (status === 'SUBSCRIBED') {\n    console.log('Ready to receive database changes!')\n  }\n})\n```"}, {"kind": "text", "text": "\n\n## Get All Channels\n\nYou can see all the channels that your client has instantiatied.\n\n"}, {"kind": "code", "text": "```js\n// Setup...\n\nclient.getChannels()\n```"}, {"kind": "text", "text": "\n\n## Cleanup\n\nIt is highly recommended that you clean up your channels after you're done with them.\n\n- Remove a single channel\n\n"}, {"kind": "code", "text": "```js\n// Setup...\n\nconst channel = client.channel('some-channel-to-remove')\n\nchannel.subscribe()\n\nclient.removeChannel(channel)\n```"}, {"kind": "text", "text": "\n\n- Remove all channels\n\n"}, {"kind": "code", "text": "```js\n// Setup...\n\nconst channel1 = client.channel('a-channel-to-remove')\nconst channel2 = client.channel('another-channel-to-remove')\n\nchannel1.subscribe()\nchannel2.subscribe()\n\nclient.removeAllChannels()\n```"}, {"kind": "text", "text": "\n\n## Credits\n\nThis repo draws heavily from [phoenix-js](https://github.com/phoenixframework/phoenix/tree/master/assets/js/phoenix).\n\n## License\n\nMIT."}], "symbolIdMap": {"0": {"sourceFileName": "src/index.ts", "qualifiedName": ""}, "1": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default"}, "36": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default.__constructor"}, "37": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default"}, "38": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "channel"}, "39": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "opts"}, "40": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default.state"}, "41": {"sourceFileName": "", "qualifiedName": "__type"}, "42": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default.pendingDiffs"}, "43": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default.joinRef"}, "44": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default.caller"}, "45": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "46": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.on<PERSON>oin"}, "47": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.onLeave"}, "48": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.onSync"}, "49": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "50": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "51": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "default.channel"}, "65": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default"}, "76": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.__constructor"}, "77": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default"}, "78": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "topic"}, "79": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "params"}, "80": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "socket"}, "81": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.bindings"}, "82": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "83": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "85": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "86": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.type"}, "87": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.filter"}, "88": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "89": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "91": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.callback"}, "92": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.id"}, "93": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.timeout"}, "94": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.state"}, "95": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.joinedOnce"}, "96": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.joinPush"}, "97": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.rejoinTimer"}, "98": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.pushBuffer"}, "99": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.presence"}, "100": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.broadcastEndpointURL"}, "101": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.subTopic"}, "102": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.private"}, "103": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.topic"}, "104": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.params"}, "105": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.socket"}, "106": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.subscribe"}, "107": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.subscribe"}, "108": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "109": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "110": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "111": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "status"}, "112": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "err"}, "113": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "timeout"}, "114": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.presenceState"}, "115": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.presenceState"}, "116": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "117": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "118": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "120": {"sourceFileName": "", "qualifiedName": "__type"}, "121": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.track"}, "122": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.track"}, "123": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "124": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "125": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "127": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "opts"}, "128": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "129": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "131": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.untrack"}, "132": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.untrack"}, "133": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "opts"}, "134": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "135": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "137": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "138": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "139": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "140": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "141": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "142": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "143": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "144": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "145": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "146": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "147": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "148": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "149": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "151": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "152": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "153": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "154": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "155": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "156": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "157": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "158": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "159": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "160": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "161": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "162": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "164": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "165": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "166": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "167": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "168": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "169": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "170": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "171": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "172": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "173": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "174": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "175": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "177": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "178": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "179": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "180": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "181": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "182": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "183": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "184": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "185": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "186": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "188": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "189": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "190": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "191": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "192": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "193": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "194": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "195": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "196": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "197": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "199": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "200": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "201": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "202": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "203": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "204": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "205": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "206": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "207": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "208": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "210": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "211": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "212": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "213": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "214": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "215": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "216": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "217": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "218": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "219": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "220": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "221": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "222": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "223": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "224": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "225": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "226": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.type"}, "227": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "228": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "230": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "231": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "232": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "233": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "235": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "236": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "237": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "238": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "239": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "240": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "241": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "242": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "243": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "244": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.type"}, "245": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "246": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.payload"}, "247": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.on"}, "248": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "249": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "250": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "252": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "type"}, "253": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "filter"}, "254": {"sourceFileName": "", "qualifiedName": "__type"}, "255": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "callback"}, "256": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "257": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "258": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "259": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.send"}, "260": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.send"}, "261": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "args"}, "262": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "263": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.type"}, "264": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "265": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.payload"}, "266": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "268": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "opts"}, "269": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "270": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "272": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.updateJoinPayload"}, "273": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.updateJoinPayload"}, "274": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "payload"}, "275": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "276": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "278": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.unsubscribe"}, "279": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "default.unsubscribe"}, "280": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "timeout"}, "359": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimeChannelOptions"}, "360": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "361": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.config"}, "362": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "363": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.broadcast"}, "364": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "365": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.self"}, "366": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.ack"}, "367": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.presence"}, "368": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "369": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.key"}, "370": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.private"}, "371": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimeChannelSendResponse"}, "372": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default"}, "373": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.__constructor"}, "374": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default"}, "375": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "endPoint"}, "376": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "options"}, "377": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.accessTokenValue"}, "378": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.apiKey"}, "379": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.channels"}, "380": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.endPoint"}, "381": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.httpEndpoint"}, "382": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.headers"}, "383": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "384": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.__index"}, "386": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.params"}, "387": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "388": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.__index"}, "390": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.timeout"}, "391": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.transport"}, "392": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.heartbeatIntervalMs"}, "393": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.heartbeatTimer"}, "394": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.pendingHeartbeatRef"}, "395": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.ref"}, "396": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.reconnectTimer"}, "397": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.logger"}, "398": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.logLevel"}, "399": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.encode"}, "400": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.decode"}, "401": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.reconnectAfterMs"}, "402": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.conn"}, "403": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.sendBuffer"}, "404": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.serializer"}, "405": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.stateChangeCallbacks"}, "406": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "407": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.open"}, "408": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.close"}, "409": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.error"}, "410": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.message"}, "411": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.fetch"}, "412": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "fetch"}, "413": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "fetch"}, "414": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "input"}, "415": {"sourceFileName": "node_modules/typescript/lib/lib.dom.d.ts", "qualifiedName": "init"}, "416": {"sourceFileName": "node_modules/@types/node/globals.d.ts", "qualifiedName": "fetch"}, "417": {"sourceFileName": "node_modules/@types/node/globals.d.ts", "qualifiedName": "input"}, "418": {"sourceFileName": "node_modules/@types/node/globals.d.ts", "qualifiedName": "init"}, "419": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.accessToken"}, "420": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "421": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "422": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.worker"}, "423": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.workerUrl"}, "424": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.workerRef"}, "425": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.connect"}, "426": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.connect"}, "427": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.endpointURL"}, "428": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.endpointURL"}, "429": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.disconnect"}, "430": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.disconnect"}, "431": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "code"}, "432": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "reason"}, "433": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.getChannels"}, "434": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.getChannels"}, "435": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.removeChannel"}, "436": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.removeChannel"}, "437": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "channel"}, "438": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.removeAllChannels"}, "439": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.removeAllChannels"}, "440": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.log"}, "441": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.log"}, "442": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "kind"}, "443": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "msg"}, "444": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "data"}, "445": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.connectionState"}, "446": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.connectionState"}, "447": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.isConnected"}, "448": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.isConnected"}, "449": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.channel"}, "450": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.channel"}, "451": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "topic"}, "452": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "params"}, "453": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.push"}, "454": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.push"}, "455": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "data"}, "456": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.setAuth"}, "457": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.setAuth"}, "458": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "token"}, "459": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.sendHeartbeat"}, "460": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.sendHeartbeat"}, "461": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.flushSendBuffer"}, "462": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "default.flushSendBuffer"}, "515": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "RealtimeClientOptions"}, "516": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "517": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.transport"}, "518": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.timeout"}, "519": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.heartbeatIntervalMs"}, "520": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.logger"}, "521": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.encode"}, "522": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.decode"}, "523": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.reconnectAfterMs"}, "524": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.headers"}, "525": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "526": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.__index"}, "528": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.params"}, "529": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "530": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.__index"}, "532": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.log_level"}, "533": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.logLevel"}, "534": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.fetch"}, "535": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.worker"}, "536": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.workerUrl"}, "537": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.accessToken"}, "538": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "539": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "540": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "RealtimeMessage"}, "541": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type"}, "542": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.topic"}, "543": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.event"}, "544": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.payload"}, "545": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.ref"}, "546": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "__type.join_ref"}, "547": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresChangesFilter"}, "548": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "549": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.event"}, "550": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.schema"}, "551": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.table"}, "552": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.filter"}, "553": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "554": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresChangesPayload"}, "555": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "556": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "557": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "559": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresInsertPayload"}, "560": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "561": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.eventType"}, "562": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.new"}, "563": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.old"}, "564": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "565": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "566": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "567": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "569": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresUpdatePayload"}, "570": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "571": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.eventType"}, "572": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.new"}, "573": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.old"}, "574": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "575": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "576": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "578": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "RealtimePostgresDeletePayload"}, "579": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "580": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.eventType"}, "581": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.new"}, "582": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "583": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.old"}, "584": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "T"}, "585": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type"}, "586": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "__type.__index"}, "588": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "RealtimePresenceJoinPayload"}, "589": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "590": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.event"}, "591": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.key"}, "592": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.currentPresences"}, "593": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.newPresences"}, "594": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "T"}, "595": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "596": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.__index"}, "598": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "RealtimePresenceLeavePayload"}, "599": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "600": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.event"}, "601": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.key"}, "602": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.currentPresences"}, "603": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.leftPresences"}, "604": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "T"}, "605": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "606": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.__index"}, "608": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "RealtimePresenceState"}, "609": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "610": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.__index"}, "612": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "T"}, "613": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "614": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type.__index"}, "616": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "__type"}, "617": {"sourceFileName": "src/RealtimeClient.ts", "qualifiedName": "RealtimeRemoveChannelResponse"}, "618": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_LISTEN_TYPES"}, "619": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_LISTEN_TYPES.BROADCAST"}, "620": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_LISTEN_TYPES.PRESENCE"}, "621": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_LISTEN_TYPES.POSTGRES_CHANGES"}, "622": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_LISTEN_TYPES.SYSTEM"}, "623": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT"}, "624": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.ALL"}, "625": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.INSERT"}, "626": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.UPDATE"}, "627": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT.DELETE"}, "628": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "REALTIME_PRESENCE_LISTEN_EVENTS"}, "629": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "REALTIME_PRESENCE_LISTEN_EVENTS.SYNC"}, "630": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "REALTIME_PRESENCE_LISTEN_EVENTS.JOIN"}, "631": {"sourceFileName": "src/RealtimePresence.ts", "qualifiedName": "REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE"}, "632": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_SUBSCRIBE_STATES"}, "633": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_SUBSCRIBE_STATES.SUBSCRIBED"}, "634": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_SUBSCRIBE_STATES.TIMED_OUT"}, "635": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_SUBSCRIBE_STATES.CLOSED"}, "636": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR"}, "637": {"sourceFileName": "src/RealtimeChannel.ts", "qualifiedName": "REALTIME_CHANNEL_STATES"}}, "files": {"entries": {"1": "src/index.ts"}, "reflections": {"1": 0}}}