{"id": 0, "name": "@supabase/supabase-js", "kind": 1, "flags": {}, "originalName": "", "children": [{"id": 1, "name": "index", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 227, "name": "QueryData", "kind": 16777216, "kindString": "Reference", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 22, "character": 50}], "target": 123}, {"id": 228, "name": "QueryError", "kind": 16777216, "kindString": "Reference", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 22, "character": 61}], "target": 127}, {"id": 226, "name": "QueryResult", "kind": 16777216, "kindString": "Reference", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 22, "character": 37}], "target": 121}, {"id": 225, "name": "SupabaseClientOptions", "kind": 16777216, "kindString": "Reference", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 22, "character": 14}], "target": 70}, {"id": 130, "name": "SupabaseClient", "kind": 128, "kindString": "Class", "flags": {}, "comment": {"shortText": "Supabase Client.", "text": "An isomorphic Javascript client for interacting with Postgres.\n"}, "children": [{"id": 131, "name": "constructor", "kind": 512, "kindString": "<PERSON><PERSON><PERSON><PERSON>", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 70, "character": 2}], "signatures": [{"id": 132, "name": "new SupabaseClient", "kind": 16384, "kindString": "Constructor signature", "flags": {}, "comment": {"shortText": "Create a new client for use in the browser."}, "typeParameter": [{"id": 133, "name": "Database", "kind": 131072, "kindString": "Type parameter", "flags": {}, "default": {"type": "intrinsic", "name": "any"}}, {"id": 134, "name": "<PERSON><PERSON>aName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}, "default": {"type": "conditional", "checkType": {"type": "literal", "value": "public"}, "extendsType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 133, "name": "Database"}}, "trueType": {"type": "literal", "value": "public"}, "falseType": {"type": "intersection", "types": [{"type": "intrinsic", "name": "string"}, {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 133, "name": "Database"}}]}}}, {"id": 135, "name": "<PERSON><PERSON><PERSON>", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "id": 116, "name": "GenericSchema"}, "default": {"type": "conditional", "checkType": {"type": "indexedAccess", "indexType": {"type": "reference", "id": 134, "name": "<PERSON><PERSON>aName"}, "objectType": {"type": "reference", "id": 133, "name": "Database"}}, "extendsType": {"type": "reference", "id": 116, "name": "GenericSchema"}, "trueType": {"type": "indexedAccess", "indexType": {"type": "intrinsic", "name": "any"}, "objectType": {"type": "intrinsic", "name": "any"}}, "falseType": {"type": "intrinsic", "name": "any"}}}], "parameters": [{"id": 136, "name": "supabaseUrl", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique Supabase URL which is supplied when you create a new project in your project dashboard."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 137, "name": "supabase<PERSON>ey", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The unique Supabase Key which is supplied when you create a new project in your project dashboard."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 138, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 70, "typeArguments": [{"type": "reference", "id": 134, "name": "<PERSON><PERSON>aName"}], "name": "SupabaseClientOptions"}}], "type": {"type": "reference", "id": 130, "typeArguments": [{"type": "reference", "id": 133, "name": "Database"}, {"type": "reference", "id": 134, "name": "<PERSON><PERSON>aName"}, {"type": "reference", "id": 135, "name": "<PERSON><PERSON><PERSON>"}], "name": "default"}}]}, {"id": 153, "name": "accessToken", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isOptional": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 54, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 154, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 155, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 139, "name": "auth", "kind": 1024, "kindString": "Property", "flags": {}, "comment": {"shortText": "Supabase Auth allows you to create and manage user sessions for access to data that is secured by access policies."}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 43, "character": 2}], "type": {"type": "reference", "name": "SupabaseAuthClient"}}, {"id": 142, "name": "authUrl", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 47, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 152, "name": "changedAccessToken", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isOptional": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 53, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 147, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true, "isOptional": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 52, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 148, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 149, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 150, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 151, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 144, "name": "functionsUrl", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 49, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 156, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 56, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 140, "name": "realtime", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 44, "character": 2}], "type": {"type": "reference", "qualifiedName": "RealtimeClient", "package": "@supabase/realtime-js", "name": "default"}}, {"id": 141, "name": "realtimeUrl", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 46, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 145, "name": "rest", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 50, "character": 12}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 133, "name": "Database"}, {"type": "reference", "id": 134, "name": "<PERSON><PERSON>aName"}, {"type": "reference", "id": 135, "name": "<PERSON><PERSON><PERSON>"}], "qualifiedName": "PostgrestClient", "package": "@supabase/postgrest-js", "name": "default"}}, {"id": 146, "name": "storageKey", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 51, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 143, "name": "storageUrl", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 48, "character": 12}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 158, "name": "supabase<PERSON>ey", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 157, "name": "supabaseUrl", "kind": 1024, "kindString": "Property", "flags": {"isProtected": true}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 159, "name": "functions", "kind": 262144, "kindString": "Accessor", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 139, "character": 6}], "getSignature": [{"id": 160, "name": "functions", "kind": 524288, "kindString": "Get signature", "flags": {}, "comment": {"shortText": "Supabase Functions allows you to deploy and invoke edge functions."}, "type": {"type": "reference", "qualifiedName": "FunctionsClient", "package": "@supabase/functions-js", "name": "FunctionsClient"}}]}, {"id": 161, "name": "storage", "kind": 262144, "kindString": "Accessor", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 149, "character": 6}], "getSignature": [{"id": 162, "name": "storage", "kind": 524288, "kindString": "Get signature", "flags": {}, "comment": {"shortText": "Supabase Storage allows you to manage user-generated content, such as photos or videos."}, "type": {"type": "reference", "qualifiedName": "StorageClient", "package": "@supabase/storage-js", "name": "StorageClient"}}]}, {"id": 200, "name": "_getAccessToken", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 269, "character": 16}], "signatures": [{"id": 201, "name": "_getAccessToken", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 217, "name": "_handleTokenChanged", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 329, "character": 10}], "signatures": [{"id": 218, "name": "_handleTokenChanged", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 219, "name": "event", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "AuthChangeEvent", "package": "@supabase/auth-js", "name": "AuthChangeEvent"}}, {"id": 220, "name": "source", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "literal", "value": "CLIENT"}, {"type": "literal", "value": "STORAGE"}]}}, {"id": 221, "name": "token", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "void"}}]}, {"id": 211, "name": "_initRealtimeClient", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 315, "character": 10}], "signatures": [{"id": 212, "name": "_initRealtimeClient", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 213, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "qualifiedName": "RealtimeClientOptions", "package": "@supabase/realtime-js", "name": "RealtimeClientOptions"}}], "type": {"type": "reference", "qualifiedName": "RealtimeClient", "package": "@supabase/realtime-js", "name": "default"}}]}, {"id": 202, "name": "_initSupabaseAuthClient", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 279, "character": 10}], "signatures": [{"id": 203, "name": "_initSupabaseAuthClient", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 204, "name": "__namedParameters", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 68, "name": "SupabaseAuthClientOptions"}}, {"id": 205, "name": "headers", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 206, "name": "fetch", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 207, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 208, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 209, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 210, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}], "type": {"type": "reference", "name": "SupabaseAuthClient"}}]}, {"id": 214, "name": "_listenForAuthEvents", "kind": 2048, "kindString": "Method", "flags": {"isPrivate": true}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 322, "character": 10}], "signatures": [{"id": 215, "name": "_listenForAuthEvents", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 216, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}}]}, {"id": 189, "name": "channel", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 241, "character": 2}], "signatures": [{"id": 190, "name": "channel", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a Realtime channel with Broadcast, Presence, and Postgres Changes."}, "parameters": [{"id": 191, "name": "name", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The name of the Realtime channel."}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 192, "name": "opts", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The options to pass to the Realtime channel.\n\n"}, "type": {"type": "reference", "qualifiedName": "RealtimeChannelOptions", "package": "@supabase/realtime-js", "name": "RealtimeChannelOptions"}, "defaultValue": "..."}], "type": {"type": "reference", "qualifiedName": "RealtimeChannel", "package": "@supabase/realtime-js", "name": "default"}}]}, {"id": 163, "name": "from", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 154, "character": 2}], "signatures": [{"id": 164, "name": "from", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Perform a query on a table or a view."}, "typeParameter": [{"id": 165, "name": "TableName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 166, "name": "Table", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "id": 100, "name": "GenericTable"}}], "parameters": [{"id": 167, "name": "relation", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The table or view name to query\n"}, "type": {"type": "reference", "id": 165, "name": "TableName"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 135, "name": "<PERSON><PERSON><PERSON>"}, {"type": "reference", "id": 166, "name": "Table"}, {"type": "reference", "id": 165, "name": "TableName"}, {"type": "conditional", "checkType": {"type": "reference", "id": 166, "name": "Table"}, "extendsType": {"type": "reflection", "declaration": {"id": 168, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}, "trueType": {"type": "reference", "qualifiedName": "R", "package": "@supabase/postgrest-js", "name": "R"}, "falseType": {"type": "intrinsic", "name": "unknown"}}], "qualifiedName": "PostgrestQueryBuilder", "package": "@supabase/postgrest-js", "name": "default"}}, {"id": 169, "name": "from", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Perform a query on a table or a view."}, "typeParameter": [{"id": 170, "name": "ViewName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 171, "name": "View", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "id": 111, "name": "Generic<PERSON>iew"}}], "parameters": [{"id": 172, "name": "relation", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The table or view name to query\n"}, "type": {"type": "reference", "id": 170, "name": "ViewName"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 135, "name": "<PERSON><PERSON><PERSON>"}, {"type": "reference", "id": 171, "name": "View"}, {"type": "reference", "id": 170, "name": "ViewName"}, {"type": "conditional", "checkType": {"type": "reference", "id": 171, "name": "View"}, "extendsType": {"type": "reflection", "declaration": {"id": 173, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}}}, "trueType": {"type": "reference", "qualifiedName": "R", "package": "@supabase/postgrest-js", "name": "R"}, "falseType": {"type": "intrinsic", "name": "unknown"}}], "qualifiedName": "PostgrestQueryBuilder", "package": "@supabase/postgrest-js", "name": "default"}}]}, {"id": 193, "name": "getChannels", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 248, "character": 2}], "signatures": [{"id": 194, "name": "getChannels", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Returns all Realtime channels."}, "type": {"type": "array", "elementType": {"type": "reference", "qualifiedName": "RealtimeChannel", "package": "@supabase/realtime-js", "name": "default"}}}]}, {"id": 198, "name": "removeAllChannels", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 265, "character": 2}], "signatures": [{"id": 199, "name": "removeAllChannels", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Unsubscribes and removes all Realtime channels from Realtime client."}, "type": {"type": "reference", "typeArguments": [{"type": "array", "elementType": {"type": "union", "types": [{"type": "literal", "value": "error"}, {"type": "literal", "value": "ok"}, {"type": "literal", "value": "timed out"}]}}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 195, "name": "removeChannel", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 258, "character": 2}], "signatures": [{"id": 196, "name": "removeChannel", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Unsubscribes and removes Realtime channel from Realtime client."}, "parameters": [{"id": 197, "name": "channel", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The name of the Realtime channel.\n\n"}, "type": {"type": "reference", "qualifiedName": "RealtimeChannel", "package": "@supabase/realtime-js", "name": "default"}}], "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": "error"}, {"type": "literal", "value": "ok"}, {"type": "literal", "value": "timed out"}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}, {"id": 178, "name": "rpc", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 212, "character": 2}], "signatures": [{"id": 179, "name": "rpc", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Perform a function call."}, "typeParameter": [{"id": 180, "name": "FnName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 181, "name": "Fn", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "id": 112, "name": "GenericFunction"}}], "parameters": [{"id": 182, "name": "fn", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The function name to call"}, "type": {"type": "reference", "id": 180, "name": "FnName"}}, {"id": 183, "name": "args", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The arguments to pass to the function call"}, "type": {"type": "indexedAccess", "indexType": {"type": "literal", "value": "<PERSON><PERSON><PERSON>"}, "objectType": {"type": "reference", "id": 181, "name": "Fn"}}, "defaultValue": "{}"}, {"id": 184, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "Named parameters"}, "type": {"type": "reflection", "declaration": {"id": 185, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 188, "name": "count", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Count algorithm to use to count rows returned by the\nfunction. Only applicable for [set-returning\nfunctions](https://www.postgresql.org/docs/current/functions-srf.html).\n\n`\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\nhood.\n\n`\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\nstatistics under the hood.\n\n`\"estimated\"`: Uses exact count for low numbers and planned count for high\nnumbers.\n"}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 218, "character": 6}], "type": {"type": "union", "types": [{"type": "literal", "value": "exact"}, {"type": "literal", "value": "planned"}, {"type": "literal", "value": "estimated"}]}}, {"id": 187, "name": "get", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "When set to `true`, the function will be called with\nread-only access mode."}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 217, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 186, "name": "head", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "When set to `true`, `data` will not be returned.\nUseful if you only need the count."}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 216, "character": 6}], "type": {"type": "intrinsic", "name": "boolean"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [188, 187, 186]}]}}, "defaultValue": "{}"}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 135, "name": "<PERSON><PERSON><PERSON>"}, {"type": "conditional", "checkType": {"type": "indexedAccess", "indexType": {"type": "literal", "value": "Returns"}, "objectType": {"type": "reference", "id": 181, "name": "Fn"}}, "extendsType": {"type": "array", "elementType": {"type": "intrinsic", "name": "any"}}, "trueType": {"type": "conditional", "checkType": {"type": "indexedAccess", "indexType": {"type": "intrinsic", "name": "number"}, "objectType": {"type": "indexedAccess", "indexType": {"type": "intrinsic", "name": "any"}, "objectType": {"type": "intrinsic", "name": "any"}}}, "extendsType": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}, "trueType": {"type": "indexedAccess", "indexType": {"type": "intrinsic", "name": "any"}, "objectType": {"type": "intrinsic", "name": "any"}}, "falseType": {"type": "intrinsic", "name": "never"}}, "falseType": {"type": "intrinsic", "name": "never"}}, {"type": "indexedAccess", "indexType": {"type": "literal", "value": "Returns"}, "objectType": {"type": "reference", "id": 181, "name": "Fn"}}, {"type": "reference", "id": 180, "name": "FnName"}, {"type": "literal", "value": null}], "qualifiedName": "PostgrestFilterBuilder", "package": "@supabase/postgrest-js", "name": "default"}}]}, {"id": 174, "name": "schema", "kind": 2048, "kindString": "Method", "flags": {}, "sources": [{"fileName": "src/SupabaseClient.ts", "line": 178, "character": 2}], "signatures": [{"id": 175, "name": "schema", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Select a schema to query or perform an function (rpc) call.", "text": "The schema needs to be on the list of exposed schemas inside Supabase.\n"}, "typeParameter": [{"id": 176, "name": "DynamicSchema", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "parameters": [{"id": 177, "name": "schema", "kind": 32768, "kindString": "Parameter", "flags": {}, "comment": {"shortText": "The schema to query\n"}, "type": {"type": "reference", "id": 176, "name": "DynamicSchema"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 133, "name": "Database"}, {"type": "reference", "id": 176, "name": "DynamicSchema"}, {"type": "conditional", "checkType": {"type": "indexedAccess", "indexType": {"type": "reference", "id": 176, "name": "DynamicSchema"}, "objectType": {"type": "reference", "id": 133, "name": "Database"}}, "extendsType": {"type": "reference", "id": 116, "name": "GenericSchema"}, "trueType": {"type": "indexedAccess", "indexType": {"type": "intrinsic", "name": "any"}, "objectType": {"type": "intrinsic", "name": "any"}}, "falseType": {"type": "intrinsic", "name": "any"}}], "qualifiedName": "PostgrestClient", "package": "@supabase/postgrest-js", "name": "default"}}]}], "groups": [{"title": "Constructors", "kind": 512, "children": [131]}, {"title": "Properties", "kind": 1024, "children": [153, 139, 142, 152, 147, 144, 156, 140, 141, 145, 146, 143, 158, 157]}, {"title": "Accessors", "kind": 262144, "children": [159, 161]}, {"title": "Methods", "kind": 2048, "children": [200, 217, 211, 202, 214, 189, 163, 193, 198, 195, 178, 174]}], "sources": [{"fileName": "src/SupabaseClient.ts", "line": 31, "character": 21}], "typeParameter": [{"id": 222, "name": "Database", "kind": 131072, "kindString": "Type parameter", "flags": {}, "default": {"type": "intrinsic", "name": "any"}}, {"id": 223, "name": "<PERSON><PERSON>aName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intersection", "types": [{"type": "intrinsic", "name": "string"}, {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 133, "name": "Database"}}]}, "default": {"type": "conditional", "checkType": {"type": "literal", "value": "public"}, "extendsType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 133, "name": "Database"}}, "trueType": {"type": "literal", "value": "public"}, "falseType": {"type": "intersection", "types": [{"type": "intrinsic", "name": "string"}, {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 133, "name": "Database"}}]}}}, {"id": 224, "name": "<PERSON><PERSON><PERSON>", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "id": 116, "name": "GenericSchema"}, "default": {"type": "conditional", "checkType": {"type": "indexedAccess", "indexType": {"type": "reference", "id": 134, "name": "<PERSON><PERSON>aName"}, "objectType": {"type": "reference", "id": 133, "name": "Database"}}, "extendsType": {"type": "reference", "id": 116, "name": "GenericSchema"}, "trueType": {"type": "indexedAccess", "indexType": {"type": "reference", "id": 134, "name": "<PERSON><PERSON>aName"}, "objectType": {"type": "reference", "id": 133, "name": "Database"}}, "falseType": {"type": "intrinsic", "name": "any"}}}]}, {"id": 2, "name": "createClient", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/index.ts", "line": 27, "character": 13}], "signatures": [{"id": 3, "name": "createClient", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Creates a new Supabase Client."}, "typeParameter": [{"id": 4, "name": "Database", "kind": 131072, "kindString": "Type parameter", "flags": {}, "default": {"type": "intrinsic", "name": "any"}}, {"id": 5, "name": "<PERSON><PERSON>aName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}, "default": {"type": "conditional", "checkType": {"type": "literal", "value": "public"}, "extendsType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 4, "name": "Database"}}, "trueType": {"type": "literal", "value": "public"}, "falseType": {"type": "intersection", "types": [{"type": "intrinsic", "name": "string"}, {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 4, "name": "Database"}}]}}}, {"id": 6, "name": "<PERSON><PERSON><PERSON>", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "reference", "id": 116, "name": "GenericSchema"}, "default": {"type": "conditional", "checkType": {"type": "indexedAccess", "indexType": {"type": "reference", "id": 5, "name": "<PERSON><PERSON>aName"}, "objectType": {"type": "reference", "id": 4, "name": "Database"}}, "extendsType": {"type": "reference", "id": 116, "name": "GenericSchema"}, "trueType": {"type": "indexedAccess", "indexType": {"type": "intrinsic", "name": "any"}, "objectType": {"type": "intrinsic", "name": "any"}}, "falseType": {"type": "intrinsic", "name": "any"}}}], "parameters": [{"id": 7, "name": "supabaseUrl", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 8, "name": "supabase<PERSON>ey", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 9, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "id": 70, "typeArguments": [{"type": "reference", "id": 5, "name": "<PERSON><PERSON>aName"}], "name": "SupabaseClientOptions"}}], "type": {"type": "reference", "id": 130, "typeArguments": [{"type": "reference", "id": 4, "name": "Database"}, {"type": "reference", "id": 5, "name": "<PERSON><PERSON>aName"}, {"type": "reference", "id": 6, "name": "<PERSON><PERSON><PERSON>"}], "name": "default"}}]}], "groups": [{"title": "References", "kind": 16777216, "children": [227, 228, 226, 225]}, {"title": "Classes", "kind": 128, "children": [130]}, {"title": "Functions", "kind": 64, "children": [2]}], "sources": [{"fileName": "src/index.ts", "line": 1, "character": 0}]}, {"id": 10, "name": "lib/constants", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 22, "name": "DEFAULT_AUTH_OPTIONS", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "sources": [{"fileName": "src/lib/constants.ts", "line": 28, "character": 13}], "type": {"type": "reference", "id": 68, "name": "SupabaseAuthClientOptions"}, "defaultValue": "..."}, {"id": 19, "name": "DEFAULT_DB_OPTIONS", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "sources": [{"fileName": "src/lib/constants.ts", "line": 24, "character": 13}], "type": {"type": "reflection", "declaration": {"id": 20, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 21, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/constants.ts", "line": 25, "character": 2}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "'public'"}], "groups": [{"title": "Properties", "kind": 1024, "children": [21]}]}}, "defaultValue": "..."}, {"id": 14, "name": "DEFAULT_GLOBAL_OPTIONS", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "sources": [{"fileName": "src/lib/constants.ts", "line": 20, "character": 13}], "type": {"type": "reflection", "declaration": {"id": 15, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 16, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/constants.ts", "line": 21, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 17, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 18, "name": "X-Client-Info", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/constants.ts", "line": 18, "character": 33}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "..."}], "groups": [{"title": "Properties", "kind": 1024, "children": [18]}]}}, "defaultValue": "DEFAULT_HEADERS"}], "groups": [{"title": "Properties", "kind": 1024, "children": [16]}]}}, "defaultValue": "..."}, {"id": 11, "name": "DEFAULT_HEADERS", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "sources": [{"fileName": "src/lib/constants.ts", "line": 18, "character": 13}], "type": {"type": "reflection", "declaration": {"id": 12, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 13, "name": "X-Client-Info", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/constants.ts", "line": 18, "character": 33}], "type": {"type": "intrinsic", "name": "string"}, "defaultValue": "..."}], "groups": [{"title": "Properties", "kind": 1024, "children": [13]}]}}, "defaultValue": "..."}, {"id": 23, "name": "DEFAULT_REALTIME_OPTIONS", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "sources": [{"fileName": "src/lib/constants.ts", "line": 35, "character": 13}], "type": {"type": "reference", "qualifiedName": "RealtimeClientOptions", "package": "@supabase/realtime-js", "name": "RealtimeClientOptions"}, "defaultValue": "{}"}], "groups": [{"title": "Variables", "kind": 32, "children": [22, 19, 14, 11, 23]}], "sources": [{"fileName": "src/lib/constants.ts", "line": 2, "character": 0}]}, {"id": 24, "name": "lib/fetch", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 38, "name": "fetchWithAuth", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/fetch.ts", "line": 26, "character": 13}], "signatures": [{"id": 39, "name": "fetchWithAuth", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 40, "name": "supabase<PERSON>ey", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}, {"id": 41, "name": "getAccessToken", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reflection", "declaration": {"id": 42, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 43, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}, {"id": 44, "name": "customFetch", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 45, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 46, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 47, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 48, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 49, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 50, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 51, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 52, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}]}, {"id": 25, "name": "resolveFetch", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/fetch.ts", "line": 6, "character": 13}], "signatures": [{"id": 26, "name": "resolveFetch", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 27, "name": "customFetch", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reflection", "declaration": {"id": 28, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 29, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 30, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 31, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}], "type": {"type": "reflection", "declaration": {"id": 32, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "signatures": [{"id": 33, "name": "__type", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 34, "name": "input", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "union", "types": [{"type": "reference", "qualifiedName": "RequestInfo", "package": "typescript", "name": "RequestInfo"}, {"type": "reference", "qualifiedName": "URL", "package": "typescript", "name": "URL"}]}}, {"id": 35, "name": "init", "kind": 32768, "kindString": "Parameter", "flags": {"isOptional": true}, "type": {"type": "reference", "qualifiedName": "RequestInit", "package": "typescript", "name": "RequestInit"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "qualifiedName": "Response", "package": "typescript", "name": "Response"}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}}}]}, {"id": 36, "name": "resolveHeadersConstructor", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/fetch.ts", "line": 18, "character": 13}], "signatures": [{"id": 37, "name": "resolveHeadersConstructor", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "any"}}]}], "groups": [{"title": "Functions", "kind": 64, "children": [38, 25, 36]}], "sources": [{"fileName": "src/lib/fetch.ts", "line": 2, "character": 0}]}, {"id": 53, "name": "lib/helpers", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 59, "name": "applySettingDefaults", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/helpers.ts", "line": 18, "character": 16}], "signatures": [{"id": 60, "name": "applySettingDefaults", "kind": 4096, "kindString": "Call signature", "flags": {}, "typeParameter": [{"id": 61, "name": "Database", "kind": 131072, "kindString": "Type parameter", "flags": {}, "default": {"type": "intrinsic", "name": "any"}}, {"id": 62, "name": "<PERSON><PERSON>aName", "kind": 131072, "kindString": "Type parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}, "default": {"type": "conditional", "checkType": {"type": "literal", "value": "public"}, "extendsType": {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 61, "name": "Database"}}, "trueType": {"type": "literal", "value": "public"}, "falseType": {"type": "intersection", "types": [{"type": "intrinsic", "name": "string"}, {"type": "typeOperator", "operator": "keyof", "target": {"type": "reference", "id": 61, "name": "Database"}}]}}}], "parameters": [{"id": 63, "name": "options", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 70, "typeArguments": [{"type": "reference", "id": 62, "name": "<PERSON><PERSON>aName"}], "name": "SupabaseClientOptions"}}, {"id": 64, "name": "defaults", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "reference", "id": 70, "typeArguments": [{"type": "intrinsic", "name": "any"}], "name": "SupabaseClientOptions"}}], "type": {"type": "reference", "typeArguments": [{"type": "reference", "id": 70, "typeArguments": [{"type": "reference", "id": 62, "name": "<PERSON><PERSON>aName"}], "name": "SupabaseClientOptions"}], "qualifiedName": "Required", "package": "typescript", "name": "Required"}}]}, {"id": 65, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/helpers.ts", "line": 16, "character": 13}], "signatures": [{"id": 66, "name": "<PERSON><PERSON><PERSON><PERSON>", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "boolean"}}]}, {"id": 56, "name": "stripTrailingSlash", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/helpers.ts", "line": 12, "character": 16}], "signatures": [{"id": 57, "name": "stripTrailingSlash", "kind": 4096, "kindString": "Call signature", "flags": {}, "parameters": [{"id": 58, "name": "url", "kind": 32768, "kindString": "Parameter", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}], "type": {"type": "intrinsic", "name": "string"}}]}, {"id": 54, "name": "uuid", "kind": 64, "kindString": "Function", "flags": {}, "sources": [{"fileName": "src/lib/helpers.ts", "line": 4, "character": 16}], "signatures": [{"id": 55, "name": "uuid", "kind": 4096, "kindString": "Call signature", "flags": {}, "type": {"type": "intrinsic", "name": "string"}}]}], "groups": [{"title": "Functions", "kind": 64, "children": [59, 65, 56, 54]}], "sources": [{"fileName": "src/lib/helpers.ts", "line": 2, "character": 0}]}, {"id": 67, "name": "lib/types", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 68, "name": "SupabaseAuthClientOptions", "kind": 256, "kindString": "Interface", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 7, "character": 17}], "extendedTypes": [{"type": "reference", "name": "AuthClientOptions"}]}, {"id": 69, "name": "<PERSON>tch", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 9, "character": 12}], "type": {"type": "query", "queryType": {"type": "reference", "id": 28, "qualifiedName": "fetch", "package": "typescript", "name": "fetch"}}}, {"id": 112, "name": "GenericFunction", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 113, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 114, "name": "<PERSON><PERSON><PERSON>", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 108, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 115, "name": "Returns", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 109, "character": 2}], "type": {"type": "intrinsic", "name": "unknown"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [114, 115]}], "sources": [{"fileName": "src/lib/types.ts", "line": 107, "character": 30}]}}}, {"id": 107, "name": "GenericNonUpdatableView", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 100, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 108, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 110, "name": "Relationships", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 102, "character": 2}], "type": {"type": "array", "elementType": {"type": "reference", "id": 93, "name": "GenericRelationship"}}}, {"id": 109, "name": "Row", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 101, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [110, 109]}], "sources": [{"fileName": "src/lib/types.ts", "line": 100, "character": 38}]}}}, {"id": 93, "name": "GenericRelationship", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 83, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 94, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 96, "name": "columns", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 85, "character": 2}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 95, "name": "foreignKeyName", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 84, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}, {"id": 97, "name": "isOneToOne", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 86, "character": 2}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 99, "name": "referencedColumns", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 88, "character": 2}], "type": {"type": "array", "elementType": {"type": "intrinsic", "name": "string"}}}, {"id": 98, "name": "referencedRelation", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 87, "character": 2}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [96, 95, 97, 99, 98]}], "sources": [{"fileName": "src/lib/types.ts", "line": 83, "character": 34}]}}}, {"id": 116, "name": "GenericSchema", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 117, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 120, "name": "Functions", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 115, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "id": 112, "name": "GenericFunction"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 118, "name": "Tables", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 113, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "id": 100, "name": "GenericTable"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 119, "name": "Views", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 114, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "reference", "id": 111, "name": "Generic<PERSON>iew"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [120, 118, 119]}], "sources": [{"fileName": "src/lib/types.ts", "line": 112, "character": 28}]}}}, {"id": 100, "name": "GenericTable", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 91, "character": 12}], "type": {"type": "reflection", "declaration": {"id": 101, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 103, "name": "Insert", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 93, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 105, "name": "Relationships", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 95, "character": 2}], "type": {"type": "array", "elementType": {"type": "reference", "id": 93, "name": "GenericRelationship"}}}, {"id": 102, "name": "Row", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 92, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}, {"id": 104, "name": "Update", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 94, "character": 2}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "unknown"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [103, 105, 102, 104]}], "sources": [{"fileName": "src/lib/types.ts", "line": 91, "character": 27}]}}}, {"id": 106, "name": "GenericUpdatableView", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 98, "character": 12}], "type": {"type": "reference", "id": 100, "name": "GenericTable"}}, {"id": 111, "name": "Generic<PERSON>iew", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 105, "character": 12}], "type": {"type": "union", "types": [{"type": "reference", "id": 106, "name": "GenericUpdatableView"}, {"type": "reference", "id": 107, "name": "GenericNonUpdatableView"}]}}, {"id": 123, "name": "QueryData", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 122, "character": 12}], "typeParameter": [{"id": 126, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "type": {"type": "conditional", "checkType": {"type": "reference", "id": 126, "name": "T"}, "extendsType": {"type": "reference", "typeArguments": [{"type": "reflection", "declaration": {"id": 124, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 125, "name": "data", "kind": 1024, "kindString": "Property", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 122, "character": 51}], "type": {"type": "inferred", "name": "U"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [125]}], "sources": [{"fileName": "src/lib/types.ts", "line": 122, "character": 49}]}}], "qualifiedName": "PromiseLike", "package": "typescript", "name": "PromiseLike"}, "trueType": {"type": "reference", "typeArguments": [{"type": "reference", "name": "U"}, {"type": "literal", "value": null}], "qualifiedName": "Exclude", "package": "typescript", "name": "Exclude"}, "falseType": {"type": "intrinsic", "name": "never"}}}, {"id": 127, "name": "QueryError", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 123, "character": 12}], "type": {"type": "reference", "qualifiedName": "PostgrestError", "package": "@supabase/postgrest-js", "name": "PostgrestError"}}, {"id": 121, "name": "QueryResult", "kind": 4194304, "kindString": "Type alias", "flags": {}, "comment": {"shortText": "Helper types for query results."}, "sources": [{"fileName": "src/lib/types.ts", "line": 121, "character": 12}], "typeParameter": [{"id": 122, "name": "T", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "type": {"type": "conditional", "checkType": {"type": "reference", "id": 122, "name": "T"}, "extendsType": {"type": "reference", "typeArguments": [{"type": "inferred", "name": "U"}], "qualifiedName": "PromiseLike", "package": "typescript", "name": "PromiseLike"}, "trueType": {"type": "reference", "name": "U"}, "falseType": {"type": "intrinsic", "name": "never"}}}, {"id": 70, "name": "SupabaseClientOptions", "kind": 4194304, "kindString": "Type alias", "flags": {}, "sources": [{"fileName": "src/lib/types.ts", "line": 11, "character": 12}], "typeParameter": [{"id": 92, "name": "<PERSON><PERSON>aName", "kind": 131072, "kindString": "Type parameter", "flags": {}}], "type": {"type": "reflection", "declaration": {"id": 71, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 75, "name": "auth", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 19, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 76, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 77, "name": "autoRefreshToken", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Automatically refreshes the token for logged-in users. Defaults to true."}, "sources": [{"fileName": "src/lib/types.ts", "line": 23, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 83, "name": "debug", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "If debug messages for authentication client are emitted. Can be used to inspect the behavior of the library."}, "sources": [{"fileName": "src/lib/types.ts", "line": 47, "character": 4}], "type": {"type": "indexedAccess", "indexType": {"type": "literal", "value": "debug"}, "objectType": {"type": "reference", "id": 68, "name": "SupabaseAuthClientOptions"}}}, {"id": 80, "name": "detectSessionInUrl", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Detect a session from the URL. Used for OAuth login callbacks. Defaults to true."}, "sources": [{"fileName": "src/lib/types.ts", "line": 35, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 82, "name": "flowType", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "OAuth flow to use - defaults to implicit flow. PKCE is recommended for mobile and server-side applications."}, "sources": [{"fileName": "src/lib/types.ts", "line": 43, "character": 4}], "type": {"type": "indexedAccess", "indexType": {"type": "literal", "value": "flowType"}, "objectType": {"type": "reference", "id": 68, "name": "SupabaseAuthClientOptions"}}}, {"id": 84, "name": "lock", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Provide your own locking mechanism based on the environment. By default no locking is done at this time.", "tags": [{"tag": "experimental", "text": "\n"}]}, "sources": [{"fileName": "src/lib/types.ts", "line": 53, "character": 4}], "type": {"type": "indexedAccess", "indexType": {"type": "literal", "value": "lock"}, "objectType": {"type": "reference", "id": 68, "name": "SupabaseAuthClientOptions"}}}, {"id": 79, "name": "persistSession", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Whether to persist a logged-in session to storage. Defaults to true."}, "sources": [{"fileName": "src/lib/types.ts", "line": 31, "character": 4}], "type": {"type": "intrinsic", "name": "boolean"}}, {"id": 81, "name": "storage", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A storage provider. Used to store the logged-in session."}, "sources": [{"fileName": "src/lib/types.ts", "line": 39, "character": 4}], "type": {"type": "indexedAccess", "indexType": {"type": "literal", "value": "storage"}, "objectType": {"type": "reference", "id": 68, "name": "SupabaseAuthClientOptions"}}}, {"id": 78, "name": "storageKey", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optional key name used for storing tokens in local storage."}, "sources": [{"fileName": "src/lib/types.ts", "line": 27, "character": 4}], "type": {"type": "intrinsic", "name": "string"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [77, 83, 80, 82, 84, 79, 81, 78]}], "sources": [{"fileName": "src/lib/types.ts", "line": 19, "character": 9}]}}}, {"id": 72, "name": "db", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "The Postgres schema which your tables belong to. Must be on the list of exposed schemas in Supabase. Defaults to `public`."}, "sources": [{"fileName": "src/lib/types.ts", "line": 15, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 73, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 74, "name": "schema", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 16, "character": 4}], "type": {"type": "reference", "id": 92, "name": "<PERSON><PERSON>aName"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [74]}], "sources": [{"fileName": "src/lib/types.ts", "line": 15, "character": 7}]}}}, {"id": 86, "name": "global", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 59, "character": 2}], "type": {"type": "reflection", "declaration": {"id": 87, "name": "__type", "kind": 65536, "kindString": "Type literal", "flags": {}, "children": [{"id": 88, "name": "fetch", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "A custom `fetch` implementation."}, "sources": [{"fileName": "src/lib/types.ts", "line": 63, "character": 4}], "type": {"type": "reference", "id": 69, "name": "<PERSON>tch"}}, {"id": 89, "name": "headers", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Optional headers for initializing the client."}, "sources": [{"fileName": "src/lib/types.ts", "line": 67, "character": 4}], "type": {"type": "reference", "typeArguments": [{"type": "intrinsic", "name": "string"}, {"type": "intrinsic", "name": "string"}], "qualifiedName": "Record", "package": "typescript", "name": "Record"}}], "groups": [{"title": "Properties", "kind": 1024, "children": [88, 89]}], "sources": [{"fileName": "src/lib/types.ts", "line": 59, "character": 11}]}}}, {"id": 85, "name": "realtime", "kind": 1024, "kindString": "Property", "flags": {"isOptional": true}, "comment": {"shortText": "Options passed to the realtime-js instance"}, "sources": [{"fileName": "src/lib/types.ts", "line": 58, "character": 2}], "type": {"type": "reference", "qualifiedName": "RealtimeClientOptions", "package": "@supabase/realtime-js", "name": "RealtimeClientOptions"}}, {"id": 90, "name": "accessToken", "kind": 2048, "kindString": "Method", "flags": {"isOptional": true}, "sources": [{"fileName": "src/lib/types.ts", "line": 80, "character": 2}], "signatures": [{"id": 91, "name": "accessToken", "kind": 4096, "kindString": "Call signature", "flags": {}, "comment": {"shortText": "Optional function for using a third-party authentication system with\nSupabase. The function should return an access token or ID token (JWT) by\nobtaining it from the third-party auth client library. Note that this\nfunction may be called concurrently and many times. Use memoization and\nlocking techniques if this is not supported by the client libraries.", "text": "When set, the `auth` namespace of the Supabase client cannot be used.\nCreate another client if you wish to use Supabase Auth and third-party\nauthentications concurrently in the same application.\n"}, "type": {"type": "reference", "typeArguments": [{"type": "union", "types": [{"type": "literal", "value": null}, {"type": "intrinsic", "name": "string"}]}], "qualifiedName": "Promise", "package": "typescript", "name": "Promise"}}]}], "groups": [{"title": "Properties", "kind": 1024, "children": [75, 72, 86, 85]}, {"title": "Methods", "kind": 2048, "children": [90]}], "sources": [{"fileName": "src/lib/types.ts", "line": 11, "character": 48}]}}}], "groups": [{"title": "Interfaces", "kind": 256, "children": [68]}, {"title": "Type Aliases", "kind": 4194304, "children": [69, 112, 107, 93, 116, 100, 106, 111, 123, 127, 121, 70]}], "sources": [{"fileName": "src/lib/types.ts", "line": 1, "character": 0}]}, {"id": 128, "name": "lib/version", "kind": 2, "kindString": "<PERSON><PERSON><PERSON>", "flags": {}, "children": [{"id": 129, "name": "version", "kind": 32, "kindString": "Variable", "flags": {"isConst": true}, "sources": [{"fileName": "src/lib/version.ts", "line": 1, "character": 13}], "type": {"type": "literal", "value": "0.0.0-automated"}, "defaultValue": "'0.0.0-automated'"}], "groups": [{"title": "Variables", "kind": 32, "children": [129]}], "sources": [{"fileName": "src/lib/version.ts", "line": 1, "character": 0}]}], "groups": [{"title": "<PERSON><PERSON><PERSON>", "kind": 2, "children": [1, 10, 24, 53, 67, 128]}]}