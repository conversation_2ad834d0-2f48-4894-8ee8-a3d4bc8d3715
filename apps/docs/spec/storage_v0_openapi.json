{"openapi": "3.0.3", "info": {"title": "Supabase Storage API", "description": "API documentation for Supabase Storage", "version": "0.0.0"}, "components": {"schemas": {"def-0": {"type": "object", "properties": {"authorization": {"type": "string", "examples": ["Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs"]}}, "required": ["authorization"], "title": "authSchema"}, "def-1": {"type": "object", "properties": {"statusCode": {"type": "string"}, "error": {"type": "string"}, "message": {"type": "string"}}, "required": ["statusCode", "error", "message"], "title": "errorSchema"}}}, "paths": {"/metrics": {"get": {"responses": {"200": {"description": "Default Response"}}}}, "/upload/resumable/": {"post": {"summary": "Handle POST request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "options": {"summary": "Handle OPTIONS request for TUS Resumable uploads", "tags": ["resumable"], "description": "Handle OPTIONS request for TUS Resumable uploads", "responses": {"200": {"description": "Default Response"}}}}, "/upload/resumable/{wildcard}": {"post": {"summary": "Handle POST request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "put": {"summary": "Handle PUT request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "patch": {"summary": "Handle PATCH request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "delete": {"summary": "Handle DELETE request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "options": {"summary": "Handle OPTIONS request for TUS Resumable uploads", "tags": ["resumable"], "description": "Handle OPTIONS request for TUS Resumable uploads", "responses": {"200": {"description": "Default Response"}}}}, "/upload/resumable/sign/": {"post": {"summary": "Handle POST request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "options": {"summary": "Handle OPTIONS request for TUS Resumable uploads", "tags": ["resumable"], "description": "Handle OPTIONS request for TUS Resumable uploads", "responses": {"200": {"description": "Default Response"}}}}, "/upload/resumable/sign/{wildcard}": {"post": {"summary": "Handle POST request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "put": {"summary": "Handle PUT request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "patch": {"summary": "Handle PATCH request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "delete": {"summary": "Handle DELETE request for TUS Resumable uploads", "tags": ["resumable"], "responses": {"200": {"description": "Default Response"}}}, "options": {"summary": "Handle OPTIONS request for TUS Resumable uploads", "tags": ["resumable"], "description": "Handle OPTIONS request for TUS Resumable uploads", "responses": {"200": {"description": "Default Response"}}}}, "/bucket/": {"post": {"summary": "Create a bucket", "tags": ["bucket"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "examples": ["avatars"]}, "id": {"type": "string", "examples": ["avatars"]}, "public": {"type": "boolean", "examples": [false]}, "file_size_limit": {"anyOf": [{"type": "integer", "examples": [1000], "nullable": true, "minimum": 0}, {"type": "string", "examples": ["100MB"], "nullable": true}]}, "allowed_mime_types": {"type": "array", "nullable": true, "examples": [["image/png", "image/jpg"]], "items": {"type": "string"}}}, "required": ["name"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"name": {"type": "string", "examples": ["avatars"]}}, "required": ["name"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "get": {"summary": "Gets all buckets", "tags": ["bucket"], "parameters": [{"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "owner": {"type": "string"}, "public": {"type": "boolean"}, "file_size_limit": {"type": ["null", "integer"]}, "allowed_mime_types": {"type": ["null", "array"], "items": {"type": "string"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name"], "additionalProperties": false, "examples": [{"id": "bucket2", "name": "bucket2", "public": false, "file_size_limit": 1000000, "allowed_mime_types": ["image/png", "image/jpeg"], "owner": "4d56e902-f0a0-4662-8448-a4d9e643c142", "created_at": "2021-02-17T04:43:32.770206+00:00", "updated_at": "2021-02-17T04:43:32.770206+00:00"}], "title": "bucketSchema"}}, "example": [{"id": "avatars", "name": "avatars", "owner": "4d56e902-f0a0-4662-8448-a4d9e643c142", "public": false, "file_size_limit": 1000000, "allowed_mime_types": ["image/png", "image/jpeg"], "created_at": "2021-02-17T04:43:32.770206+00:00", "updated_at": "2021-02-17T04:43:32.770206+00:00"}]}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/bucket/{bucketId}/empty": {"post": {"summary": "Empty a bucket", "tags": ["bucket"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketId", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"message": {"type": "string", "examples": ["Successfully emptied"]}}}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/bucket/{bucketId}": {"get": {"summary": "Get details of a bucket", "tags": ["bucket"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketId", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "owner": {"type": "string"}, "public": {"type": "boolean"}, "file_size_limit": {"type": ["null", "integer"]}, "allowed_mime_types": {"type": ["null", "array"], "items": {"type": "string"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name"], "additionalProperties": false}, "example": {"id": "bucket2", "name": "bucket2", "public": false, "file_size_limit": 1000000, "allowed_mime_types": ["image/png", "image/jpeg"], "owner": "4d56e902-f0a0-4662-8448-a4d9e643c142", "created_at": "2021-02-17T04:43:32.770206+00:00", "updated_at": "2021-02-17T04:43:32.770206+00:00"}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "put": {"summary": "Update properties of a bucket", "tags": ["bucket"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"public": {"type": "boolean", "examples": [false]}, "file_size_limit": {"anyOf": [{"type": "integer", "examples": [1000], "nullable": true, "minimum": 0}, {"type": "string", "examples": ["100MB"], "nullable": true}]}, "allowed_mime_types": {"type": "array", "nullable": true, "items": {"type": "string", "examples": [["image/png", "image/jpg"]]}}}}}}}, "parameters": [{"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"message": {"type": "string", "examples": ["Successfully updated"]}}, "required": ["message"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "delete": {"summary": "Delete a bucket", "tags": ["bucket"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketId", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"message": {"type": "string", "examples": ["Successfully deleted"]}}}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/{bucketName}/{wildcard}": {"delete": {"summary": "Delete an object", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"message": {"type": "string", "examples": ["Successfully deleted"]}}}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "get": {"summary": "Get object", "tags": ["object"], "description": "Serve objects", "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Default Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1"}}}}}}, "put": {"summary": "Update the object at an existing key", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"Id": {"type": "string"}, "Key": {"type": "string", "examples": ["avatars/folder/cat.png"]}}, "required": ["Key"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "post": {"summary": "Upload a new object", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"Id": {"type": "string"}, "Key": {"type": "string", "examples": ["avatars/folder/cat.png"]}}, "required": ["Key"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/{bucketName}": {"delete": {"summary": "Delete multiple objects", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"prefixes": {"type": "array", "items": {"type": "string"}, "minItems": 1, "examples": [["folder/cat.png", "folder/morecats.png"]]}}, "required": ["prefixes"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "bucket_id": {"type": "string"}, "owner": {"type": "string"}, "owner_id": {"type": "string"}, "version": {"type": "string"}, "id": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "updated_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "created_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "last_accessed_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}]}, "user_metadata": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}]}, "buckets": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "owner": {"type": "string"}, "public": {"type": "boolean"}, "file_size_limit": {"type": ["null", "integer"]}, "allowed_mime_types": {"type": ["null", "array"], "items": {"type": "string"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name"], "additionalProperties": false, "examples": [{"id": "bucket2", "name": "bucket2", "public": false, "file_size_limit": 1000000, "allowed_mime_types": ["image/png", "image/jpeg"], "owner": "4d56e902-f0a0-4662-8448-a4d9e643c142", "created_at": "2021-02-17T04:43:32.770206+00:00", "updated_at": "2021-02-17T04:43:32.770206+00:00"}]}}, "required": ["name"], "additionalProperties": false, "examples": [{"name": "folder/cat.png", "bucket_id": "avatars", "owner": "317eadce-631a-4429-a0bb-f19a7a517b4a", "id": "eaa8bdb5-2e00-4767-b5a9-d2502efe2196", "updated_at": "2021-04-06T16:30:35.394674+00:00", "created_at": "2021-04-06T16:30:35.394674+00:00", "last_accessed_at": "2021-04-06T16:30:35.394674+00:00", "metadata": {"size": 1234}}], "title": "objectSchema"}}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/authenticated/{bucketName}/{wildcard}": {"get": {"summary": "Retrieve an object", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/object/upload/sign/{bucketName}/{wildcard}": {"post": {"summary": "Generate a presigned url to upload an object", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"url": {"type": "string", "examples": ["/object/sign/upload/avatars/folder/cat.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJhdmF0YXJzL2ZvbGRlci9jYXQucG5nIiwiaWF0IjoxNjE3NzI2MjczLCJleHAiOjE2MTc3MjcyNzN9.s7Gt8ME80iREVxPhH01ZNv8oUn4XtaWsmiQ5csiUHn4"]}, "token": {"type": "string"}}, "required": ["url"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "put": {"summary": "Uploads an object via a presigned URL", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJidWNrZXQyL3B1YmxpYy9zYWRjYXQtdXBsb2FkMjMucG5nIiwiaWF0IjoxNjE3NzI2MjczLCJleHAiOjE2MTc3MjcyNzN9.uBQcXzuvXxfw-9WgzWMBfE_nR3VOgpvfZe032sfLSSk", "in": "query", "name": "token", "required": true}, {"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"Key": {"type": "string", "examples": ["avatars/folder/cat.png"]}}, "required": ["Key"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/object/sign/{bucketName}/{wildcard}": {"post": {"summary": "Generate a presigned url to retrieve an object", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"expiresIn": {"type": "integer", "minimum": 1, "examples": [60000]}, "transform": {"type": "object", "properties": {"height": {"type": "integer", "examples": [100], "minimum": 0}, "width": {"type": "integer", "examples": [100], "minimum": 0}, "resize": {"type": "string", "enum": ["cover", "contain", "fill"]}, "format": {"type": "string", "enum": ["origin", "avif"]}, "quality": {"type": "integer", "minimum": 20, "maximum": 100}}}}, "required": ["expiresIn"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"signedURL": {"type": "string", "examples": ["/object/sign/avatars/folder/cat.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJhdmF0YXJzL2ZvbGRlci9jYXQucG5nIiwiaWF0IjoxNjE3NzI2MjczLCJleHAiOjE2MTc3MjcyNzN9.s7Gt8ME80iREVxPhH01ZNv8oUn4XtaWsmiQ5csiUHn4"]}}, "required": ["signedURL"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}, "get": {"summary": "Retrieve an object via a presigned URL", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "examples": {"filename.jpg": {"value": "filename.jpg"}, "example2": {"value": null}}, "in": "query", "name": "download", "required": false}, {"schema": {"type": "string"}, "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJidWNrZXQyL3B1YmxpYy9zYWRjYXQtdXBsb2FkMjMucG5nIiwiaWF0IjoxNjE3NzI2MjczLCJleHAiOjE2MTc3MjcyNzN9.uBQcXzuvXxfw-9WgzWMBfE_nR3VOgpvfZe032sfLSSk", "in": "query", "name": "token", "required": true}, {"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/object/sign/{bucketName}": {"post": {"summary": "Generate presigned urls to retrieve objects", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"expiresIn": {"type": "integer", "minimum": 1, "examples": [60000]}, "paths": {"type": "array", "items": {"type": "string"}, "minItems": 1, "examples": [["folder/cat.png", "folder/morecats.png"]]}}, "required": ["expiresIn", "paths"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "array", "items": {"type": "object", "properties": {"error": {"error": ["string", "null"], "examples": ["Either the object does not exist or you do not have access to it"]}, "path": {"type": "string", "examples": ["folder/cat.png"]}, "signedURL": {"type": ["null", "string"], "examples": ["/object/sign/avatars/folder/cat.png?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJhdmF0YXJzL2ZvbGRlci9jYXQucG5nIiwiaWF0IjoxNjE3NzI2MjczLCJleHAiOjE2MTc3MjcyNzN9.s7Gt8ME80iREVxPhH01ZNv8oUn4XtaWsmiQ5csiUHn4"]}}, "required": ["error", "path", "signedURL"]}}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/move": {"post": {"summary": "Moves an object", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"bucketId": {"type": "string", "examples": ["avatars"]}, "sourceKey": {"type": "string", "examples": ["folder/cat.png"]}, "destinationBucket": {"type": "string", "examples": ["users"]}, "destinationKey": {"type": "string", "examples": ["folder/newcat.png"]}}, "required": ["bucketId", "sourceKey", "destinationKey"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"message": {"type": "string", "examples": ["Successfully moved"]}}, "required": ["message"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/list-v2/{bucketName}": {"post": {"summary": "Search for objects under a prefix", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"prefix": {"type": "string", "examples": ["folder/subfolder"]}, "limit": {"type": "integer", "minimum": 1, "examples": [10]}, "cursor": {"type": "string"}, "with_delimiter": {"type": "boolean"}}}}}}, "parameters": [{"schema": {"type": "string"}, "in": "path", "name": "bucketName", "required": true}], "responses": {"200": {"description": "Default Response"}}}}, "/object/list/{bucketName}": {"post": {"summary": "Search for objects under a prefix", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"prefix": {"type": "string", "examples": ["folder/subfolder"]}, "limit": {"type": "integer", "minimum": 1, "examples": [10]}, "offset": {"type": "integer", "minimum": 0, "examples": [0]}, "sortBy": {"type": "object", "properties": {"column": {"type": "string", "enum": ["name", "updated_at", "created_at", "last_accessed_at"]}, "order": {"type": "string", "enum": ["asc", "desc"]}}, "required": ["column"]}, "search": {"type": "string"}}, "required": ["prefix"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "bucket_id": {"type": "string"}, "owner": {"type": "string"}, "owner_id": {"type": "string"}, "version": {"type": "string"}, "id": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "updated_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "created_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "last_accessed_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}]}, "user_metadata": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}]}, "buckets": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "owner": {"type": "string"}, "public": {"type": "boolean"}, "file_size_limit": {"type": ["null", "integer"]}, "allowed_mime_types": {"type": ["null", "array"], "items": {"type": "string"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name"], "additionalProperties": false, "examples": [{"id": "bucket2", "name": "bucket2", "public": false, "file_size_limit": 1000000, "allowed_mime_types": ["image/png", "image/jpeg"], "owner": "4d56e902-f0a0-4662-8448-a4d9e643c142", "created_at": "2021-02-17T04:43:32.770206+00:00", "updated_at": "2021-02-17T04:43:32.770206+00:00"}]}}, "required": ["name"], "additionalProperties": false, "examples": [{"name": "folder/cat.png", "bucket_id": "avatars", "owner": "317eadce-631a-4429-a0bb-f19a7a517b4a", "id": "eaa8bdb5-2e00-4767-b5a9-d2502efe2196", "updated_at": "2021-04-06T16:30:35.394674+00:00", "created_at": "2021-04-06T16:30:35.394674+00:00", "last_accessed_at": "2021-04-06T16:30:35.394674+00:00", "metadata": {"size": 1234}}]}}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/info/authenticated/{bucketName}/{wildcard}": {"get": {"summary": "Retrieve object info", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}, {"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/object/info/{bucketName}/{wildcard}": {"get": {"summary": "Retrieve object info", "tags": ["object"], "description": "Object Info", "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Default Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1"}}}}}}}, "/object/copy": {"post": {"summary": "Copies an object", "tags": ["object"], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"bucketId": {"type": "string", "examples": ["avatars"]}, "sourceKey": {"type": "string", "examples": ["folder/source.png"]}, "destinationBucket": {"type": "string", "examples": ["users"]}, "destinationKey": {"type": "string", "examples": ["folder/destination.png"]}, "metadata": {"type": "object", "properties": {"cacheControl": {"type": "string"}, "mimetype": {"type": "string"}}}, "copyMetadata": {"type": "boolean", "examples": [true]}}, "required": ["sourceKey", "bucketId", "destinationKey"]}}}, "required": true}, "parameters": [{"schema": {"type": "string"}, "example": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs", "in": "header", "name": "authorization", "required": true}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"description": "Successful response", "type": "object", "properties": {"Id": {"type": "string"}, "Key": {"type": "string", "examples": ["folder/destination.png"]}, "name": {"type": "string"}, "bucket_id": {"type": "string"}, "owner": {"type": "string"}, "owner_id": {"type": "string"}, "version": {"type": "string"}, "id": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "updated_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "created_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "last_accessed_at": {"anyOf": [{"type": "string"}, {"type": "null"}]}, "metadata": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}]}, "user_metadata": {"anyOf": [{"type": "object", "additionalProperties": true}, {"type": "null"}]}, "buckets": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "owner": {"type": "string"}, "public": {"type": "boolean"}, "file_size_limit": {"type": ["null", "integer"]}, "allowed_mime_types": {"type": ["null", "array"], "items": {"type": "string"}}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "name"], "additionalProperties": false, "examples": [{"id": "bucket2", "name": "bucket2", "public": false, "file_size_limit": 1000000, "allowed_mime_types": ["image/png", "image/jpeg"], "owner": "4d56e902-f0a0-4662-8448-a4d9e643c142", "created_at": "2021-02-17T04:43:32.770206+00:00", "updated_at": "2021-02-17T04:43:32.770206+00:00"}]}}, "required": ["Key"]}}}}, "4XX": {"description": "Error response", "content": {"application/json": {"schema": {"description": "Error response", "$ref": "#/components/schemas/def-1"}}}}}}}, "/object/public/{bucketName}/{wildcard}": {"get": {"summary": "Retrieve an object from a public bucket", "tags": ["object"], "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/object/info/public/{bucketName}/{wildcard}": {"get": {"summary": "Get object info", "tags": ["object"], "description": "returns object info", "parameters": [{"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Default Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1"}}}}}}}, "/render/image/authenticated/{bucketName}/{wildcard}": {"get": {"summary": "Render an authenticated image with the given transformations", "tags": ["transformation"], "parameters": [{"schema": {"type": "integer", "minimum": 0}, "example": 100, "in": "query", "name": "height", "required": false}, {"schema": {"type": "integer", "minimum": 0}, "example": 100, "in": "query", "name": "width", "required": false}, {"schema": {"type": "string", "enum": ["cover", "contain", "fill"]}, "in": "query", "name": "resize", "required": false}, {"schema": {"type": "string", "enum": ["origin", "avif"]}, "in": "query", "name": "format", "required": false}, {"schema": {"type": "integer", "minimum": 20, "maximum": 100}, "in": "query", "name": "quality", "required": false}, {"schema": {"type": "string"}, "example": "filename.png", "in": "query", "name": "download", "required": false}, {"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/render/image/sign/{bucketName}/{wildcard}": {"get": {"summary": "Render an authenticated image with the given transformations", "tags": ["transformation"], "parameters": [{"schema": {"type": "string"}, "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJidWNrZXQyL3B1YmxpYy9zYWRjYXQtdXBsb2FkMjMucG5nIiwiaWF0IjoxNjE3NzI2MjczLCJleHAiOjE2MTc3MjcyNzN9.uBQcXzuvXxfw-9WgzWMBfE_nR3VOgpvfZe032sfLSSk", "in": "query", "name": "token", "required": true}, {"schema": {"type": "string"}, "example": "filename.png", "in": "query", "name": "download", "required": false}, {"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/render/image/public/{bucketName}/{wildcard}": {"get": {"summary": "Render a public image with the given transformations", "tags": ["transformation"], "parameters": [{"schema": {"type": "integer", "minimum": 0}, "example": 100, "in": "query", "name": "height", "required": false}, {"schema": {"type": "integer", "minimum": 0}, "example": 100, "in": "query", "name": "width", "required": false}, {"schema": {"type": "string", "enum": ["cover", "contain", "fill"]}, "in": "query", "name": "resize", "required": false}, {"schema": {"type": "string", "enum": ["origin", "avif"]}, "in": "query", "name": "format", "required": false}, {"schema": {"type": "integer", "minimum": 20, "maximum": 100}, "in": "query", "name": "quality", "required": false}, {"schema": {"type": "string"}, "example": "filename.png", "in": "query", "name": "download", "required": false}, {"schema": {"type": "string"}, "example": "avatars", "in": "path", "name": "bucketName", "required": true}, {"schema": {"type": "string"}, "example": "folder/cat.png", "in": "path", "name": "*", "required": true}], "responses": {"4XX": {"description": "Error response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/def-1", "description": "Error response"}}}}}}}, "/s3/{Bucket}/{wildcard}": {"put": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "post": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "delete": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "get": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}}, "/s3/{Bucket}": {"delete": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "put": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "post": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "get": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}}, "/s3/{Bucket}/": {"delete": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "put": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "post": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}, "get": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}}, "/s3/": {"get": {"tags": ["s3"], "responses": {"200": {"description": "Default Response"}}}}, "/health/": {"get": {"summary": "healthcheck", "responses": {"200": {"description": "Default Response"}}}}}, "tags": [{"name": "object", "description": "Object end-points"}, {"name": "bucket", "description": "Bucket end-points"}, {"name": "s3", "description": "S3 end-points"}, {"name": "transformation", "description": "Image transformation"}, {"name": "resumable", "description": "Resumable Upload end-points"}]}