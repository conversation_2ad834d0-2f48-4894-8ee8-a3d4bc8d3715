configspec: '001'

# This section outlines the general information for the tool.
info:
  id: 'analytics' # {string} A unique ID for this tool.
  version: 'next' # {string} The current version number of the tool.
  title: 'Analytics' # {string} A readable name.
  source: 'https://github.com/supabase/realtime' # {string} Where developers can find the source code.
  bugs: 'https://github.com/supabase/realtime/issues' # {string} Where developers can file bugs.
  spec: 'https://github.com/supabase/supabase/blob/master/spec/realtime_v0_config.yml' # {string} Where developers can find this spec (to link directly in the docs).
  description: |
    You can use environment variables to configure your Analytics Server.
  tags:
    - id: general
      title: General Settings
      description: General server settings.

# This section is an array of public functions which a user might need to execute.
parameters:
  - id: 'LOGFLARE_SINGLE_TENANT' # {string} A unique identifier for this param.
    title: 'LOGFLARE_SINGLE_TENANT' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'true'
    type: 'boolean'
    description: |
      This is will seed a singular user into the database, and will disable browser authentication. All browser usage will default to this user. Inviting team users and other team-related functionality is currently not supported for self-hosted. Logflare self-hosted is currently intended for single-user experience only.
  - id: 'LOGFLARE_API_KEY' # {string} A unique identifier for this param.
    title: 'LOGFLARE_API_KEY' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: ''
    type: 'string'
    description: |
      Allows you to pass in an API key that will used for authentication. This is intended for programmatic usage where an external program sets the API key. It is advised to use the UI to configure the access tokens instead. If this value is not provided, the default API key will be automatically generated.
  - id: 'LOGFLARE_SUPABASE_MODE' # {string} A unique identifier for this param.
    title: 'LOGFLARE_SUPABASE_MODE' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'false'
    type: 'boolean'
    description: |
      This is a special mode for Logflare which will seed additional resources for usage with Supabase self-hosted.
  - id: 'PHX_HTTP_PORT' # {string} A unique identifier for this param.
    title: 'PHX_HTTP_PORT' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: '4000'
    type: 'string'
    description: |
      Port which serves HTTP requests
  - id: 'DB_SCHEMA' # {string} A unique identifier for this param.
    title: 'DB_SCHEMA' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: ''
    type: 'string'
    description: |
      This ENV variable sets the search path to a custom database schema. This allows you to customize the schema used on the database.
  - id: 'LOGFLARE_LOG_LEVEL' # {string} A unique identifier for this param.
    title: 'LOGFLARE_LOG_LEVEL' # {string} Any name.
    tags: ['general'] # {string[]} These tags are useful for grouping parameters
    links: [] # {string[]} These tags are useful for grouping parameters
    required: true
    default: 'info'
    type: 'string'
    description: |
      Allows the setting of the log level at runtime. For production settings, we advise `warn`.
