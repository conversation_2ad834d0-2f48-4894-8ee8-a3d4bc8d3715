[{"id": "introduction", "title": "Introduction", "slug": "introduction", "summary": "Introduction", "type": "markdown"}, {"id": "usage", "type": "category", "title": "Usage", "items": [{"id": "generates-an-email-action-link", "slug": "generates-an-email-action-link", "title": "Generates an email action link", "type": "self-hosted-operation"}, {"id": "get-a-user", "slug": "get-a-user", "title": "Get a user", "type": "self-hosted-operation"}, {"id": "update-a-user", "slug": "update-a-user", "title": "Update a user", "type": "self-hosted-operation"}, {"id": "deletes-a-user", "slug": "deletes-a-user", "title": "Deletes a user", "type": "self-hosted-operation"}, {"id": "list-all-users", "slug": "list-all-users", "title": "List all users", "type": "self-hosted-operation"}, {"id": "returns-the-created-user", "slug": "returns-the-created-user", "title": "Returns the created user", "type": "self-hosted-operation"}, {"id": "redirects-the-user-to-the-3rd-party-oauth-provider-to-start-the-oauth1-0-or-oauth2-0-authentication-process", "slug": "redirects-the-user-to-the-3rd-party-oauth-provider-to-start-the-oauth1-0-or-oauth2-0-authentication-process", "title": "Redirects the user to the 3rd party oauth provider to start the oauth1 0 or oauth2 0 authentication process", "type": "self-hosted-operation"}, {"id": "receives-the-redirect-from-an-external-provider-during-the-oauth-authentication-process-starts-the-process-of-creating-an-access-and-refresh-token", "slug": "receives-the-redirect-from-an-external-provider-during-the-oauth-authentication-process-starts-the-process-of-creating-an-access-and-refresh-token", "title": "Receives the redirect from an external provider during the oauth authentication process starts the process of creating an access and refresh token", "type": "self-hosted-operation"}, {"id": "the-healthcheck-endpoint-for-gotrue-returns-the-current-gotrue-version", "slug": "the-healthcheck-endpoint-for-gotrue-returns-the-current-gotrue-version", "title": "The healthcheck endpoint for gotrue returns the current gotrue version", "type": "self-hosted-operation"}, {"id": "sends-an-invite-link-to-the-user", "slug": "sends-an-invite-link-to-the-user", "title": "Sends an invite link to the user", "type": "self-hosted-operation"}, {"id": "logs-out-the-user", "slug": "logs-out-the-user", "title": "Logs out the user", "type": "self-hosted-operation"}, {"id": "passwordless-sign-in-method-for-email-or-phone", "slug": "passwordless-sign-in-method-for-email-or-phone", "title": "Passwordless sign in method for email or phone", "type": "self-hosted-operation"}, {"id": "sends-a-password-recovery-email-link-to-the-users-email", "slug": "sends-a-password-recovery-email-link-to-the-users-email", "title": "Sends a password recovery email link to the users email", "type": "self-hosted-operation"}, {"id": "returns-the-configuration-settings-for-the-gotrue-server", "slug": "returns-the-configuration-settings-for-the-gotrue-server", "title": "Returns the configuration settings for the gotrue server", "type": "self-hosted-operation"}, {"id": "password-based-signup-with-either-email-or-phone", "slug": "password-based-signup-with-either-email-or-phone", "title": "Password based signup with either email or phone", "type": "self-hosted-operation"}, {"id": "signs-in-a-user-with-a-password", "slug": "signs-in-a-user-with-a-password", "title": "Signs in a user with a password", "type": "self-hosted-operation"}, {"id": "refreshes-a-users-refresh-token", "slug": "refreshes-a-users-refresh-token", "title": "Refreshes a users refresh token", "type": "self-hosted-operation"}, {"id": "get-information-for-the-logged-in-user", "slug": "get-information-for-the-logged-in-user", "title": "Get information for the logged in user", "type": "self-hosted-operation"}, {"id": "returns-the-updated-user", "slug": "returns-the-updated-user", "title": "Returns the updated user", "type": "self-hosted-operation"}, {"id": "verifies-a-sign-up", "slug": "verifies-a-sign-up", "title": "Verifies a sign up", "type": "self-hosted-operation"}]}]