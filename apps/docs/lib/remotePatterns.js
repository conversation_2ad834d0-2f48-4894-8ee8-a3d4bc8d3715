module.exports = [
  {
    protocol: 'https',
    hostname: 'avatars.githubusercontent.com',
    port: '',
    pathname: '/u/*',
  },
  {
    protocol: 'https',
    hostname: 'github.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'supabase.github.io',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'user-images.githubusercontent.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'raw.githubusercontent.com',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'weweb-changelog.ghost.io',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'img.youtube.com',
    port: '',
    pathname: '/vi/**',
  },
  {
    protocol: 'https',
    hostname: 'archbee-image-uploads.s3.amazonaws.com',
    port: '',
    pathname: '*',
  },
  {
    protocol: 'https',
    hostname: 'obuldanrptloktxcffvn.supabase.co',
    port: '',
    pathname: '**',
  },
  {
    protocol: 'https',
    hostname: 'xguihxuzqibwxjnimxev.supabase.co',
    port: '',
    pathname: '**',
  },
]
