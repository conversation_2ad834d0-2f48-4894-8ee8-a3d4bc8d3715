{"compilerOptions": {"incremental": true, "noImplicitAny": false, "baseUrl": ".", "paths": {"~/*": ["./*"], "@ui/*": ["./../../packages/ui/src/*"]}, "target": "ES2021", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "module": "esnext", "plugins": [{"name": "next"}], "strictNullChecks": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "pages/guides/append-test.js", ".next/types/**/*.ts", "./../../packages/ui/src/**/*.d.ts"], "exclude": ["node_modules", "examples"]}