import { type SerializeOptions } from 'next-mdx-remote/dist/types'
import { notFound } from 'next/navigation'
import { isAbsolute, relative } from 'path'
import rehypeSlug from 'rehype-slug'

import { genGuideMeta } from '~/features/docs/GuidesMdx.utils'
import { GuideTemplate, newEditLink } from '~/features/docs/GuidesMdx.template'
import { fetchRevalidatePerDay_TEMP_TESTING } from '~/features/helpers.fetch'
import { UrlTransformFunction, linkTransform } from '~/lib/mdx/plugins/rehypeLinkTransform'
import remarkMkDocsAdmonition from '~/lib/mdx/plugins/remarkAdmonition'
import { removeTitle } from '~/lib/mdx/plugins/remarkRemoveTitle'
import remarkPyMdownTabs from '~/lib/mdx/plugins/remarkTabs'

export const dynamicParams = false

// We fetch these docs at build time from an external repo
const org = 'supabase'
const repo = 'pg_graphql'
const branch = 'master'
const docsDir = 'docs'
const externalSite = 'https://supabase.github.io/pg_graphql'

// Each external docs page is mapped to a local page
const pageMap = [
  {
    meta: {
      id: 'graphql-overview',
      title: 'GraphQL',
      subtitle: 'Autogenerated GraphQL APIs with Postgres.',
    },
    remoteFile: 'supabase.md',
  },
  {
    slug: 'api',
    meta: {
      id: 'graphql-api',
      title: 'GraphQL API',
      subtitle: 'Understanding the core concepts of the GraphQL API.',
    },
    remoteFile: 'api.md',
  },
  {
    slug: 'views',
    meta: {
      id: 'graphql-views',
      title: 'Views',
      subtitle: 'Using Postgres Views with GraphQL.',
    },
    remoteFile: 'views.md',
  },
  {
    slug: 'functions',
    meta: {
      id: 'graphql-functions',
      title: 'Functions',
      subtitle: 'Using Postgres Functions with GraphQL.',
    },
    remoteFile: 'functions.md',
  },
  {
    slug: 'computed-fields',
    meta: {
      id: 'graphql-computed-fields',
      title: 'Computed Fields',
      subtitle: 'Using Postgres Computed Fields with GraphQL.',
    },
    remoteFile: 'computed-fields.md',
  },
  {
    slug: 'configuration',
    meta: {
      id: 'graphql-configuration',
      title: 'Configuration & Customization',
      subtitle: 'Extra configuration options can be set on SQL entities using comment directives.',
    },
    remoteFile: 'configuration.md',
  },
  {
    slug: 'security',
    meta: {
      id: 'graphql-security',
      title: 'Security',
      subtitle: 'Securing your GraphQL API.',
    },
    remoteFile: 'security.md',
  },
  {
    slug: 'with-apollo',
    meta: {
      id: 'graphql-with-apollo',
      title: 'With Apollo',
      subtitle: 'Using pg_grapqhl with Apollo.',
    },
    remoteFile: 'usage_with_apollo.md',
  },
  {
    slug: 'with-relay',
    meta: {
      id: 'graphql-with-relay',
      title: 'With Relay',
      subtitle: 'Using pg_grapqhl with Relay.',
    },
    remoteFile: 'usage_with_relay.md',
  },
]

interface Params {
  slug?: string[]
}

const PGGraphQLDocs = async ({ params }: { params: Params }) => {
  const { meta, ...data } = await getContent(params)

  const options = {
    mdxOptions: {
      remarkPlugins: [remarkMkDocsAdmonition, remarkPyMdownTabs, [removeTitle, meta.title]],
      rehypePlugins: [[linkTransform, urlTransform], rehypeSlug],
    },
  } as SerializeOptions

  return <GuideTemplate mdxOptions={options} meta={meta} {...data} />
}

/**
 * Fetch markdown from external repo and transform links
 */
const getContent = async ({ slug }: Params) => {
  const page = pageMap.find((page) => page.slug === slug?.at(0))

  if (!page) {
    notFound()
  }

  const { remoteFile, meta } = page

  const editLink = newEditLink(`${org}/${repo}/blob/${branch}/${docsDir}/${remoteFile}`)

  const response = await fetchRevalidatePerDay_TEMP_TESTING(
    `https://raw.githubusercontent.com/${org}/${repo}/${branch}/${docsDir}/${remoteFile}`
  )

  const content = await response.text()

  return {
    pathname: `/guides/graphql${slug?.length ? `/${slug.join('/')}` : ''}` satisfies `/${string}`,
    meta,
    content,
    editLink,
  }
}

const urlTransform: UrlTransformFunction = (url) => {
  try {
    const externalSiteUrl = new URL(externalSite)

    const placeholderHostname = 'placeholder'
    const { hostname, pathname, hash } = new URL(url, `http://${placeholderHostname}`)

    // Don't modify a url with a FQDN or a url that's only a hash
    if (hostname !== placeholderHostname || pathname === '/') {
      return url
    }

    const getRelativePath = () => {
      if (pathname.endsWith('.md')) {
        return pathname.replace(/\.md$/, '')
      }
      if (isAbsolute(url)) {
        return relative(externalSiteUrl.pathname, pathname)
      }
      return pathname
    }

    const relativePath = getRelativePath().replace(/^\//, '')

    const page = pageMap.find(({ remoteFile }) => `${relativePath}.md` === remoteFile)

    // If we have a mapping for this page, use the mapped path
    if (page) {
      return 'graphql/' + page.slug + hash
    }

    // If we don't have this page in our docs, link to original docs
    return `${externalSite}/${relativePath}${hash}`
  } catch (err) {
    console.error('Error transforming markdown URL', err)
    return url
  }
}

const generateStaticParams = async () => pageMap.map(({ slug }) => ({ slug: slug ? [slug] : [] }))
const generateMetadata = genGuideMeta(getContent)

export default PGGraphQLDocs
export { generateStaticParams, generateMetadata }
