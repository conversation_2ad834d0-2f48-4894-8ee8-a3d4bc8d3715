---
id: 'ai-google-colab'
title: 'Google Colab'
description: 'Use Google Colab to manage your Supabase Vector store.'
subtitle: 'Use Google Colab to manage your Supabase Vector store.'
sidebar_label: 'Google Colab'
---

<a
  className="w-64"
  href="https://colab.research.google.com/github/supabase/supabase/blob/master/examples/ai/vector_hello_world.ipynb"
>
  <img src="/docs/img/ai/colab-badge.svg" />
</a>

Google Colab is a hosted Jupyter Notebook service. It provides free access to computing resources, including GPUs and TPUs, and is well-suited to machine learning, data science, and education. We can use Colab to manage collections using [Supabase Vecs](/docs/guides/ai/vecs-python-client).

In this tutorial we'll connect to a database running on the Supabase [platform](https://supabase.com/dashboard/). If you don't already have a database, you can create one here: [database.new](https://database.new).

## Create a new notebook

Start by visiting [colab.research.google.com](https://colab.research.google.com/). There you can create a new notebook.

![Google Colab new notebook](/docs/img/ai/google-colab/colab-new.png)

## Install Vecs

We'll use the Supabase Vector client, [Vecs](/docs/guides/ai/vecs-python-client), to manage our collections.

At the top of the notebook add the notebook paste the following code and hit the "execute" button (`ctrl+enter`):

```py
pip install vecs
```

![Install vecs](/docs/img/ai/google-colab/install-vecs.png)

## Connect to your database

Find the Postgres pooler connection string for your Supabase project in the [database settings](https://supabase.com/dashboard/project/_/settings/database) of the dashboard. Copy the "URI" format, which should look something like `postgres://postgres.xxxx:<EMAIL>:6543/postgres`

Create a new code block below the install block (`ctrl+m b`) and add the following code using the Postgres URI you copied above:

```py
import vecs

DB_CONNECTION = "postgres://postgres.xxxx:<EMAIL>:6543/postgres"

# create vector store client
vx = vecs.create_client(DB_CONNECTION)
```

Execute the code block (`ctrl+enter`). If no errors were returned then your connection was successful.

## Create a collection

Now we're going to create a new collection and insert some documents.

Create a new code block below the install block (`ctrl+m b`). Add the following code to the code block and execute it (`ctrl+enter`):

```py
collection = vx.get_or_create_collection(name="colab_collection", dimension=3)

collection.upsert(
    vectors=[
        (
         "vec0",           # the vector's identifier
         [0.1, 0.2, 0.3],  # the vector. list or np.array
         {"year": 1973}    # associated  metadata
        ),
        (
         "vec1",
         [0.7, 0.8, 0.9],
         {"year": 2012}
        )
    ]
)
```

This will create a table inside your database within the `vecs` schema, called `colab_collection`. You can view the inserted items in the [Table Editor](https://supabase.com/dashboard/project/_/editor/), by selecting the `vecs` schema from the schema dropdown.

![Colab documents](/docs/img/ai/google-colab/colab-documents.png)

## Query your documents

Now we can search for documents based on their similarity. Create a new code block and execute the following code:

```py
collection.query(
    query_vector=[0.4,0.5,0.6],  # required
    limit=5,                     # number of records to return
    filters={},                  # metadata filters
    measure="cosine_distance",   # distance measure to use
    include_value=False,         # should distance measure values be returned?
    include_metadata=False,      # should record metadata be returned?
)
```

You will see that this returns two documents in an array `['vec1', 'vec0']`:

![Colab results](/docs/img/ai/google-colab/colab-results.png)

It also returns a warning:

```
Query does not have a covering index for cosine_distance.
```

You can lean more about creating indexes in the [Vecs documentation](https://supabase.github.io/vecs/api/#create-an-index).

## Resources

- Vecs API: [supabase.github.io/vecs/api](https://supabase.github.io/vecs/api)
