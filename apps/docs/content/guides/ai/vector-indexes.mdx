---
id: 'ai-vector-indexes'
title: 'Vector indexes'
description: 'Understanding vector indexes'
sidebar_label: 'Vector indexes'
---

Once your vector table starts to grow, you will likely want to add an index to speed up queries. Without indexes, you'll be performing a sequential scan which can be a resource-intensive operation when you have many records.

## Choosing an index

Today `pgvector` supports two types of indexes:

- [HNSW](/docs/guides/ai/vector-indexes/hnsw-indexes)
- [IVFFlat](/docs/guides/ai/vector-indexes/ivf-indexes)

In general we recommend using [HNSW](/docs/guides/ai/vector-indexes/hnsw-indexes) because of its [performance](https://supabase.com/blog/increase-performance-pgvector-hnsw#hnsw-performance-1536-dimensions) and [robustness against changing data](/docs/guides/ai/vector-indexes/hnsw-indexes#when-should-you-create-hnsw-indexes).

## Distance operators

Indexes can be used to improve performance of nearest neighbor search using various distance measures. `pgvector` includes 3 distance operators:

| Operator | Description            | [**Operator class**](https://www.postgresql.org/docs/current/sql-createopclass.html) |
| -------- | ---------------------- | ------------------------------------------------------------------------------------ |
| `<->`    | Euclidean distance     | `vector_l2_ops`                                                                      |
| `<#>`    | negative inner product | `vector_ip_ops`                                                                      |
| `<=>`    | cosine distance        | `vector_cosine_ops`                                                                  |

Currently vectors with up to 2,000 dimensions can be indexed.

## Resources

Read more about indexing on `pgvector`'s [GitHub page](https://github.com/pgvector/pgvector#indexing).
