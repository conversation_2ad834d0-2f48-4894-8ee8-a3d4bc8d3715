---
title: 'Social Login'
description: 'Logging in with social accounts'
---

Social Login (OAuth) is an open standard for authentication that allows users to log in to one website or application using their credentials from another website or application. OAuth allows users to grant third-party applications access to their online accounts without sharing their passwords.
OAuth is commonly used for things like logging in to a social media account from a third-party app. It is a secure and convenient way to authenticate users and share information between applications.

## Benefits

There are several reasons why you might want to add social login to your applications:

- **Improved user experience**: Users can register and log in to your application using their existing social media accounts, which can be faster and more convenient than creating a new account from scratch. This makes it easier for users to access your application, improving their overall experience.

- **Better user engagement**: You can access additional data and insights about your users, such as their interests, demographics, and social connections. This can help you tailor your content and marketing efforts to better engage with your users and provide a more personalized experience.

- **Increased security**: Social login can improve the security of your application by leveraging the security measures and authentication protocols of the social media platforms that your users are logging in with. This can help protect against unauthorized access and account takeovers.

## Set up a social provider with Supabase Auth

Supabase supports a suite of social providers. Follow these guides to configure a social provider for your platform.

<div className="grid grid-cols-12 xs:gap-x-10 gap-y-10 not-prose py-8">
  <NavData data="socialLoginItems">
    {(data) =>
      data.map((item) => (
        <Link
          href={`${item.url}`}
          key={item.name}
          passHref
          className="col-span-12 xs:col-span-6 lg:col-span-4 xl:col-span-3"
        >
          <IconPanel
            title={item.name}
            span="col-span-6"
            icon={item.icon}
            isDarkMode={item.isDarkMode}
            hasLightIcon={item.hasLightIcon}
          >
            {item.description}
          </IconPanel>
        </Link>
      ))
    }
  </NavData>
</div>

## Provider tokens

You can use the provider token and provider refresh token returned to make API calls to the OAuth provider. For example, you can use the Google provider token to access Google APIs on behalf of your user.

Supabase Auth does not manage refreshing the provider token for the user. Your application will need to use the provider refresh token to obtain a new provider token. If no provider refresh token is returned, then it could mean one of the following:

- The OAuth provider does not return a refresh token
- Additional scopes need to be specified in order for the OAuth provider to return a refresh token.

Provider tokens are intentionally not stored in your project's database. This is because provider tokens give access to potentially sensitive user data in third-party systems. Different applications have different needs, and one application's OAuth scopes may be significantly more permissive than another. If you want to use the provider token outside of the browser that completed the OAuth flow, it is recommended to send it to a trusted and secure server you control.
