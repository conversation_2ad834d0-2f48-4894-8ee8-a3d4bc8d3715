---
id: 'flutter-auth-ui'
title: 'Flutter Auth UI'
description: 'Prebuilt, customizable Flutter widgets for authenticating users.'
sitemapPriority: 0.5
---

Flutter Auth UI is a Flutter package containing pre-built widgets for authenticating users.
It is unstyled and can match your brand and aesthetic.

![Flutter Auth UI](https://raw.githubusercontent.com/supabase-community/flutter-auth-ui/main/screenshots/supabase_auth_ui.png)

## Add Flutter Auth UI

Add the latest version of the package [supabase-auth-ui](https://pub.dev/packages/supabase_auth_ui) to pubspec.yaml:

```bash
flutter pub add supabase_auth_ui
```

### Initialize the Flutter Auth package

```dart
import 'package:flutter/material.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';

void main() async {
  await Supabase.initialize(
    url: dotenv.get('SUPABASE_URL'),
    anonKey: dotenv.get('SUPABASE_ANON_KEY'),
  );

  runApp(const MyApp());
}
```

### Email Auth

Use a `SupaEmailAuth` widget to create an email and password signin and signup form. It also contains a button to toggle to display a forgot password form.

You can pass `metadataFields` to add additional fields to the form to pass as metadata to Supabase.

```dart
SupaEmailAuth(
  redirectTo: kIsWeb ? null : 'io.mydomain.myapp://callback',
  onSignInComplete: (response) {},
  onSignUpComplete: (response) {},
  metadataFields: [
    MetaDataField(
    prefixIcon: const Icon(Icons.person),
    label: 'Username',
    key: 'username',
    validator: (val) {
            if (val == null || val.isEmpty) {
            return 'Please enter something';
            }
            return null;
          },
        ),
    ],
)
```

### Magic link Auth

Use `SupaMagicAuth` widget to create a magic link signIn form.

```dart
SupaMagicAuth(
  redirectUrl: kIsWeb ? null : 'io.mydomain.myapp://callback',
  onSuccess: (Session response) {},
  onError: (error) {},
)
```

### Reset password

Use `SupaResetPassword` to create a password reset form.

```dart
SupaResetPassword(
  accessToken: supabase.auth.currentSession?.accessToken,
  onSuccess: (UserResponse response) {},
  onError: (error) {},
)
```

### Phone Auth

Use `SupaPhoneAuth` to create a phone authentication form.

```dart
SupaPhoneAuth(
  authAction: SupaAuthAction.signUp,
  onSuccess: (AuthResponse response) {},
),
```

### Social Auth

The package supports login with [official social providers](../../auth#providers).

Use `SupaSocialsAuth` to create list of social login buttons.

```dart
SupaSocialsAuth(
  socialProviders: [
    OAuthProvider.apple,
    OAuthProvider.google,
  ],
  colored: true,
  redirectUrl: kIsWeb
    ? null
    : 'io.mydomain.myapp://callback',
  onSuccess: (Session response) {},
  onError: (error) {},
)
```

### Theming

This package uses plain Flutter components allowing you to control the appearance of the components using your own theme.
