---
title: 'Server-Side Rendering'
subtitle: 'How <PERSON> works with Supabase Auth.'
---

SSR frameworks move rendering and data fetches to the server, to reduce client bundle size and execution time.

Supabase Auth is fully compatible with SSR. You need to make a few changes to the configuration of your Supabase client, to store the user session in cookies instead of local storage. After setting up your Supabase client, follow the instructions for any flow in the How-To guides.

<Admonition type="tip">

Make sure to use the PKCE flow instructions where those differ from the implicit flow instructions. If no difference is mentioned, don't worry about this.

</Admonition>

## `@supabase/ssr`

We have developed an [`@supabase/ssr`](https://www.npmjs.com/package/@supabase/ssr) package to make setting up the Supabase client as simple as possible. This package is currently in beta. Adop<PERSON> is recommended but be aware that the API is still unstable and may have breaking changes in the future.

<Admonition type="tip">

If you're currently using the [Auth Helpers package](https://github.com/supabase/auth-helpers), the [docs are still available](/docs/guides/auth/auth-helpers), however we recommend migrating to the new `@supabase/ssr` package as this will be the recommended path moving forward.

</Admonition>

## Framework quickstarts

<div className="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-6 mb-6 not-prose">
  {[
    {
      title: 'Next.js',
      href: '/guides/auth/server-side/nextjs',
      description:
        'Automatically configure Supabase in Next.js to use cookies, making your user and their session available on the client and server.',
      icon: '/docs/img/icons/nextjs-icon',
    },
    {
      title: 'SvelteKit',
      href: '/guides/auth/server-side/sveltekit',
      description:
        'Automatically configure Supabase in SvelteKit to use cookies, making your user and their session available on the client and server.',
      icon: '/docs/img/icons/svelte-icon',
    },
  ].map((item) => {
    return (
      <Link href={`${item.href}`} key={item.title} passHref>
        <GlassPanel title={item.title} background={false} icon={item.icon}>
          {item.description}
        </GlassPanel>
      </Link>
    )
  })}
</div>
