---
title: A DESCRIPTIVE TITLE
subtitle: A DESCRIPTIVE SUBTITLE
---

{/* supa-mdx-lint-disable */}

{/* Use this template to document Auth Flows. These should be how-to guides, walking the reader through the process of (1) enabling the feature and (2) triggering the flow from their code. */}

A brief description of what this flow does. Don't get into details: if the concepts require a lot of explanation, make a separate page under Concepts.

## Enabling WHATEVER_FLOW

If the feature is default on, say so. If the feature needs to be turned on in the Dashboard, say how. If the feature can be turned on in self-hosted, say how.

If configuration is required, say how. If configuration is extensive, you can link to a separate explanation under Configuration.

## Using the flow (1)

Step-by-step instructions on how to use the flow. This assumes configuration is already done, and just contains the application code needed by the developer.

This first section should contain the most basic example of the flow (e.g., a client-side sign in with password).

If multiple steps are required, use an ordered list. Always include code examples.

## Using the flow (2)

Step-by-step instructions on how to use the flow. This assumes configuration is already done, and just contains the application code needed by the developer.

This can contain another related flow (e.g., change password, or PKCE version of flow 1).

If multiple steps are required, use an ordered list. Always include code examples.

## Resources

{/* Optional section containing a list of links to learn more. */}
