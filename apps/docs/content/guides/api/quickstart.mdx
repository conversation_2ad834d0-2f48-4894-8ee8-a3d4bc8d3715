---
title: 'Build an API route in less than 2 minutes.'
subtitle: 'Create your first API route by creating a table called `todos` to store tasks.'
breadcrumb: 'API Quickstart'
hideToc: true
---

Let's create our first REST route which we can query using `cURL` or the browser.

We'll create a database table called `todos` for storing tasks. This creates a corresponding API route `/rest/v1/todos` which can accept `GET`, `POST`, `PATCH`, & `DELETE` requests.

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>
    <StepHikeCompact.Details title="Set up a Supabase project with a 'todos' table">

    [Create a new project](https://supabase.com/dashboard) in the Supabase Dashboard.

    After your project is ready, create a table in your Supabase database. You can do this with either the Table interface or the [SQL Editor](https://supabase.com/dashboard/project/_/sql).

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      <Tabs
        scrollable
        size="small"
        type="underlined"
        defaultActiveId="sql"
        queryGroup="database-method"
      >
      <TabPanel id="sql" label="SQL">

      ```sql
      -- Create a table called "todos"
      -- with a column to store tasks.
      create table todos (
        id serial primary key,
        task text
      );
      ```

      </TabPanel>
      <TabPanel id="dashboard" label="Dashboard">

      <video width="99%" muted playsInline controls={true}>
        <source
          src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/api/api-create-table-sm.mp4"
          type="video/mp4"
        />
      </video>

      </TabPanel>
      </Tabs>

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Allow public access">

    Let's turn on Row Level Security for this table and allow public access.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```sql
      -- Turn on security
      alter table "todos"
      enable row level security;

      -- Allow anonymous access
      create policy "Allow public access"
        on todos
        for select
        to anon
        using (true);
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>
    <StepHikeCompact.Details title="Insert some dummy data">

    Now we can add some data to our table which we can access through our API.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```sql
      insert into todos (task)
      values
        ('Create tables'),
        ('Enable security'),
        ('Add data'),
        ('Fetch data from the API');
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={4}>
    <StepHikeCompact.Details title="Fetch the data">

    Find your API URL and Keys in your Dashboard [API Settings](https://supabase.com/dashboard/project/_/settings/api). You can now query your "todos" table by appending `/rest/v1/todos` to the API URL.

    Copy this block of code, substitute `<PROJECT_REF>` and `<ANON_KEY>`, then run it from a terminal.

    </StepHikeCompact.Details>
    <StepHikeCompact.Code>

      ```bash Terminal
      curl 'https://<PROJECT_REF>.supabase.co/rest/v1/todos' \
      -H "apikey: <ANON_KEY>" \
      -H "Authorization: Bearer <ANON_KEY>"
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

</StepHikeCompact>

## Bonus

There are several options for accessing your data:

### Browser

You can query the route in your browser, by appending the `anon` key as a query parameter:

`https://<PROJECT_REF>.supabase.co/rest/v1/todos?apikey=<ANON_KEY>`

### Client libraries

We provide a number of [Client Libraries](https://github.com/supabase/supabase#client-libraries).

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

```js
const { data, error } = await supabase.from('todos').select()
```

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
final data = await supabase.from('todos').select('*');
```

</TabPanel>
<TabPanel id="python" label="Python">

```python
response = supabase.table('todos').select("*").execute()
```

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
let response = try await supabase.from("todos").select()
```

</TabPanel>
</Tabs>
