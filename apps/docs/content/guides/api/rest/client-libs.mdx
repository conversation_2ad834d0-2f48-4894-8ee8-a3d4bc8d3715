---
id: 'client-libs'
title: 'Client Libraries'
description: 'Supabase provides several client libraries for the REST and Realtime APIs.'
video: 'https://www.youtube.com/v/7CqlTU9aOR4'
---

Supabase provides client libraries for the REST and Realtime APIs. Some libraries are officially supported, and some are contributed by the community.

## Official libraries

| `Language`            | `Source Code`                                                                                        | `Documentation`                                                     |
| --------------------- | ---------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------- |
| Javascript/Typescript | [supabase-js](https://github.com/supabase/supabase-js)                                               | [Docs](https://supabase.com/docs/reference/javascript/introduction) |
| Dart/Flutter          | [supabase-flutter](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter) | [Docs](https://supabase.com/docs/reference/dart/introduction)       |
| Swift                 | [supabase-swift](https://github.com/supabase/supabase-swift)                                         | [Docs](https://supabase.com/docs/reference/swift/introduction)      |
| Python                | [supabase-py](https://github.com/supabase/supabase-py)                                               | [Docs](https://supabase.com/docs/reference/python/initializing)     |

## Community libraries

| `Language`              | `Source Code`                                                                    | `Documentation`                                                 |
| ----------------------- | -------------------------------------------------------------------------------- | --------------------------------------------------------------- |
| C#                      | [supabase-csharp](https://github.com/supabase-community/supabase-csharp)         | [Docs](https://supabase.com/docs/reference/csharp/introduction) |
| Go                      | [supabase-go](https://github.com/supabase-community/supabase-go)                 |                                                                 |
| Kotlin                  | [supabase-kt](https://github.com/supabase-community/supabase-kt)                 | [Docs](https://supabase.com/docs/reference/kotlin/introduction) |
| Ruby                    | [supabase-rb](https://github.com/supabase-community/supabase-rb)                 |                                                                 |
| Godot Engine (GDScript) | [supabase-gdscript](https://github.com/supabase-community/godot-engine.supabase) |                                                                 |
