---
id: 'auto-docs'
title: 'Auto-generated documentation'
description: 'Supabase provides documentation that updates automatically.'
---

Supabase generates documentation in the [Dashboard](https://supabase.com/dashboard) which updates as you make database changes.

1. Go to the [API](https://supabase.com/dashboard/project/_/api) page in the Dashboard.
2. Select any table under **Tables and Views** in the sidebar.
3. Switch between the JavaScript and the cURL docs using the tabs.

<video width="99%" muted playsInline controls={true}>
  <source
    src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/api/api-docs.mp4"
    type="video/mp4"
  />
</video>
