---
title: AI Prompts
subtitle: Prompts for working with Supabase using AI-powered IDE tools
---

We've curated a selection of prompts to help you work with Supabase using your favorite AI-powered IDE tools, such as Cursor or GitHub Copilot.

## How to use

Copy the prompt to a file in your repo.

Use the "include file" feature from your AI tool to include the prompt when chatting with your AI assistant. For example, in Cursor, add them as [project rules](https://docs.cursor.com/context/rules-for-ai#project-rules-recommended), with GitHub Copilot, use `#<filename>`, and in Zed, use `/file`.

## Prompts

<AiPromptsIndex />
