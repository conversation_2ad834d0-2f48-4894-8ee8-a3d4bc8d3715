---
title: 'Use Supabase with SvelteKit'
subtitle: 'Learn how to create a Supabase project, add some sample data to your database, and query the data from a SvelteKit app.'
breadcrumb: 'Framework Quickstarts'
hideToc: true
---

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>

    <$Partial path="quickstart_db_setup.mdx" />

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Create a SvelteKit app">

    Create a SvelteKit app using the `npm create` command.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      npx sv create my-app
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>
    <StepHikeCompact.Details title="Install the Supabase client library">

    The fastest way to get started is to use the `supabase-js` client library which provides a convenient interface for working with Supa<PERSON> from a SvelteKit app.

    Navigate to the SvelteKit app and install `supabase-js`.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      cd my-app && npm install @supabase/supabase-js
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={4}>
    <StepHikeCompact.Details title="Create the Supabase client">

    Create a `src/lib` directory in your SvelteKit app, create a file called `supabaseClient.js` and add the following code to initialize the Supabase client with your project URL and public API (anon) key:

    <ProjectConfigVariables variable="url" />
    <ProjectConfigVariables variable="anonKey" />

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```js name=src/lib/supabaseClient.js
        import { createClient } from '@supabase/supabase-js'

        export const supabase = createClient('https://<project>.supabase.co', '<your-anon-key>')
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={5}>
    <StepHikeCompact.Details title="Query data from the app">

    Use `load` method to fetch the data server-side and display the query results as a simple list.

    Create `+page.server.js` file in the `src/routes` directory with the following code.

    </StepHikeCompact.Details>
    <StepHikeCompact.Code>


      ```js name=src/routes/+page.server.js
        import { supabase } from "$lib/supabaseClient";

        export async function load() {
          const { data } = await supabase.from("instruments").select();
          return {
            instruments: data ?? [],
          };
        }
      ```

    </StepHikeCompact.Code>

    <StepHikeCompact.Details title="">

    Replace the existing content in your `+page.svelte` file in the `src/routes` directory with the following code.

    </StepHikeCompact.Details>
    <StepHikeCompact.Code>


      ```svelte name=src/routes/+page.svelte
        <script>
          let { data } = $props();
        </script>

        <ul>
          {#each data.instruments as instrument}
            <li>{instrument.name}</li>
          {/each}
        </ul>
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={6}>
    <StepHikeCompact.Details title="Start the app">

    Start the app and go to http://localhost:5173 in a browser and you should see the list of instruments.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      npm run dev
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>
</StepHikeCompact>

## Next steps

- Set up [Auth](/docs/guides/auth) for your app
- [Insert more data](/docs/guides/database/import-data) into your database
- Upload and serve static files using [Storage](/docs/guides/storage)
