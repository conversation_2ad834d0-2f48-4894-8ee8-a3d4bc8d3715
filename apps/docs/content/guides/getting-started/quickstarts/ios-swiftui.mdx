---
title: 'Use Supabase with iOS and SwiftUI'
subtitle: 'Learn how to create a Supabase project, add some sample data to your database, and query the data from an iOS app.'
breadcrumb: 'Framework Quickstarts'
hideToc: true
---

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>

    <$Partial path="quickstart_db_setup.mdx" />

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Create an iOS SwiftUI app with Xcode">

    Open Xcode > New Project > iOS > App. You can skip this step if you already have a working app.

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>

    <StepHikeCompact.Details title="Install the Supabase client library">

      Install Supabase package dependency using Xcode by following Apple's [tutorial](https://developer.apple.com/documentation/xcode/adding-package-dependencies-to-your-app).

      Make sure to add `Supabase` product package as dependency to the application.

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={4}>

    <StepHikeCompact.Details title="Initialize the Supabase client">

      Create a new `Supabase.swift` file add a new Supabase instance using your project URL and public API (anon) key:

      <ProjectConfigVariables variable="url" />
      <ProjectConfigVariables variable="anonKey" />

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```swift name=Supabase.swift
      import Supabase

      let supabase = SupabaseClient(
        supabaseURL: URL(string: "YOUR_SUPABASE_URL")!,
        supabaseKey: "YOUR_SUPABASE_ANON_KEY"
      )
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={5}>

    <StepHikeCompact.Details title="Create a data model for instruments">

    Create a decodable struct to deserialize the data from the database.

    Add the following code to a new file named `Instrument.swift`.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```swift name=Supabase.swift
      struct Instrument: Decodable, Identifiable {
        let id: Int
        let name: String
      }
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={6}>

    <StepHikeCompact.Details title="Query data from the app">

      Use a `task` to fetch the data from the database and display it using a `List`.

      Replace the default `ContentView` with the following code.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```swift name=ContentView.swift
      struct ContentView: View {

        @State var instruments: [Instrument] = []

        var body: some View {
          List(instruments) { instrument in
            Text(instrument.name)
          }
          .overlay {
            if instruments.isEmpty {
              ProgressView()
            }
          }
          .task {
            do {
              instruments = try await supabase.from("instruments").select().execute().value
            } catch {
              dump(error)
            }
          }
        }
      }
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={7}>
    <StepHikeCompact.Details title="Start the app">

    Run the app on a simulator or a physical device by hitting `Cmd + R` on Xcode.

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

</StepHikeCompact>
