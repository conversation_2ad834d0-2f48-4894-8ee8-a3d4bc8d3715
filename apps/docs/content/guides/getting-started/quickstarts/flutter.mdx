---
title: 'Use Supabase with Flutter'
subtitle: 'Learn how to create a Supabase project, add some sample data to your database, and query the data from a Flutter app.'
breadcrumb: 'Framework Quickstarts'
hideToc: true
---

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>

    <$Partial path="quickstart_db_setup.mdx" />

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Create a Flutter app">

    Create a Flutter app using the `flutter create` command. You can skip this step if you already have a working app.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      flutter create my_app
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>

    <StepHikeCompact.Details title="Install the Supabase client library">

      The fastest way to get started is to use the [`supabase_flutter`](https://pub.dev/packages/supabase_flutter) client library which provides a convenient interface for working with Supa<PERSON> from a Flutter app.

      Open the `pubspec.yaml` file inside your Flutter app and add `supabase_flutter` as a dependency.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```yaml name=pubspec.yaml
      supabase_flutter: ^2.0.0
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={4}>

    <StepHikeCompact.Details title="Initialize the Supabase client">

      Open `lib/main.dart` and edit the main function to initialize Supabase using your project URL and public API (anon) key:

      <ProjectConfigVariables variable="url" />
      <ProjectConfigVariables variable="anonKey" />

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```dart name=lib/main.dart
      import 'package:supabase_flutter/supabase_flutter.dart';

      Future<void> main() async {
        WidgetsFlutterBinding.ensureInitialized();

        await Supabase.initialize(
          url: 'YOUR_SUPABASE_URL',
          anonKey: 'YOUR_SUPABASE_ANON_KEY',
        );
        runApp(MyApp());
      }
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={5}>

    <StepHikeCompact.Details title="Query data from the app">

      Use a `FutureBuilder` to fetch the data when the home page loads and display the query result in a `ListView`.

      Replace the default `MyApp` and `MyHomePage` classes with the following code.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```dart name=lib/main.dart
      class MyApp extends StatelessWidget {
        const MyApp({super.key});

        @override
        Widget build(BuildContext context) {
          return const MaterialApp(
            title: 'Instruments',
            home: HomePage(),
          );
        }
      }

      class HomePage extends StatefulWidget {
        const HomePage({super.key});

        @override
        State<HomePage> createState() => _HomePageState();
      }

      class _HomePageState extends State<HomePage> {
        final _future = Supabase.instance.client
            .from('instruments')
            .select();

        @override
        Widget build(BuildContext context) {
          return Scaffold(
            body: FutureBuilder(
              future: _future,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }
                final instruments = snapshot.data!;
                return ListView.builder(
                  itemCount: instruments.length,
                  itemBuilder: ((context, index) {
                    final instrument = instruments[index];
                    return ListTile(
                      title: Text(instrument['name']),
                    );
                  }),
                );
              },
            ),
          );
        }
      }
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={6}>
    <StepHikeCompact.Details title="Start the app">

    Run your app on a platform of your choosing! By default an app should launch in your web browser.

    Note that `supabase_flutter` is compatible with web, iOS, Android, macOS, and Windows apps.
    Running the app on macOS requires additional configuration to [set the entitlements](https://docs.flutter.dev/development/platform-integration/macos/building#setting-up-entitlements).

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash name=Terminal
      flutter run
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

</StepHikeCompact>

## Setup deep links

Many sign in methods require deep links to redirect the user back to your app after authentication. Read more about setting deep links up for all platforms (including web) in the [Flutter Mobile Guide](/docs/guides/getting-started/tutorials/with-flutter#setup-deep-links).

## Going to production

### Android

In production, your Android app needs explicit permission to use the internet connection on the user's device which is required to communicate with Supabase APIs.
To do this, add the following line to the `android/app/src/main/AndroidManifest.xml` file.

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <!-- Required to fetch data from the internet. -->
  <uses-permission android:name="android.permission.INTERNET" />
  <!-- ... -->
</manifest>
```
