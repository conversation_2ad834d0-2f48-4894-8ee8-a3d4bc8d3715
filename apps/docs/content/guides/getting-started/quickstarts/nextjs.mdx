---
title: 'Use Supabase with Next.js'
subtitle: 'Learn how to create a Supabase project, add some sample data, and query from a Next.js app.'
breadcrumb: 'Framework Quickstarts'
hideToc: true
---

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>

    <$Partial path="quickstart_db_setup.mdx" />

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Create a Next.js app">

    Use the `create-next-app` command and the `with-supabase` template, to create a Next.js app pre-configured with:
    - [Cookie-based Auth](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)
    - [TypeScript](https://www.typescriptlang.org/)
    - [Tailwind CSS](https://tailwindcss.com/)

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash
      npx create-next-app -e with-supabase
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>
    <StepHikeCompact.Details title="Declare Supabase Environment Variables">

    Rename `.env.example` to `.env.local` and populate with your Supabase connection variables:

    <ProjectConfigVariables variable="url" />
    <ProjectConfigVariables variable="anonKey" />

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      <$CodeTabs>

        ```text name=.env.local
        NEXT_PUBLIC_SUPABASE_URL=<SUBSTITUTE_SUPABASE_URL>
        NEXT_PUBLIC_SUPABASE_ANON_KEY=<SUBSTITUTE_SUPABASE_ANON_KEY>
        ```

      </$CodeTabs>

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={4}>
    <StepHikeCompact.Details title="Query Supabase data from Next.js">

    Create a new file at `app/instruments/page.tsx` and populate with the following.

    This will select all the rows from the `instruments` table in Supabase and render them on the page.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

    <$CodeTabs>

      ```ts name=app/instruments/page.tsx
      import { createClient } from '@/utils/supabase/server';

      export default async function Instruments() {
        const supabase = await createClient();
        const { data: instruments } = await supabase.from("instruments").select();

        return <pre>{JSON.stringify(instruments, null, 2)}</pre>
      }
      ```

      ```ts name=utils/supabase/server.ts
      import { createServerClient } from '@supabase/ssr'
      import { cookies } from 'next/headers'

      export async function createClient() {
        const cookieStore = await cookies()

        return createServerClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          {
            cookies: {
              getAll() {
                return cookieStore.getAll()
              },
              setAll(cookiesToSet) {
                try {
                  cookiesToSet.forEach(({ name, value, options }) =>
                    cookieStore.set(name, value, options)
                  )
                } catch {
                  // The `setAll` method was called from a Server Component.
                  // This can be ignored if you have middleware refreshing
                  // user sessions.
                }
              },
            },
          }
        )
      }
      ```

    </$CodeTabs>

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={5}>
    <StepHikeCompact.Details title="Start the app">

    Run the development server, go to http://localhost:3000/instruments in a browser and you should see the list of instruments.

    </StepHikeCompact.Details>

    <StepHikeCompact.Code>

      ```bash Terminal
      npm run dev
      ```

    </StepHikeCompact.Code>

  </StepHikeCompact.Step>

</StepHikeCompact>

## Next steps

- Set up [Auth](/docs/guides/auth) for your app
- [Insert more data](/docs/guides/database/import-data) into your database
- Upload and serve static files using [Storage](/docs/guides/storage)
