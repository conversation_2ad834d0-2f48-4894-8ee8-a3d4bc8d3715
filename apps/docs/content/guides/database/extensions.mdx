---
id: 'extensions'
title: 'Postgres Extensions Overview'
description: 'Using Postgres extensions.'
---

Extensions are exactly as they sound - they "extend" the database with functionality which isn't part of the Postgres core.
Supabase has pre-installed some of the most useful open source extensions.

### Enable and disable extensions

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="database-method"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Database](https://supabase.com/dashboard/project/_/database/tables) page in the Dashboard.
2. Click **Extensions** in the sidebar.
3. Enable or disable an extension.

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
 -- Example: enable the "pgtap" extension and ensure it is installed
create extension pgtap
with
  schema extensions;

-- Example: disable the "pgtap" extension
drop
  extension pgtap;
```

Even though the SQL code is `create extension`, this is the equivalent of enabling the extension.
To disable an extension call `drop extension`.

</TabPanel>
</Tabs>

<Admonition type="note">

Most extensions are installed under the `extensions` schema, which is accessible to public by default. To avoid namespace pollution, we do not recommend creating other entities in the `extensions` schema.

If you need to restrict user access to tables managed by extensions, we recommend creating a separate schema for installing that specific extension.

Some extensions can only be created under a specific schema, for example, `postgis_tiger_geocoder` extension creates a schema named `tiger`. Before enabling such extensions, make sure you have not created a conflicting schema with the same name.

In addition to the pre-configured extensions, you can also install your own SQL extensions directly in the database using Supabase's SQL editor. The SQL code for the extensions, including plpgsql extensions, can be added through the SQL editor.

</Admonition>

### Upgrade extensions

If a new version of an extension becomes available on Supabase, you need to initiate a software upgrade in the [Infrastructure Settings](https://supabase.com/dashboard/project/_/settings/infrastructure) to access it. Software upgrades can also be initiated by restarting your server in the [General Settings](https://supabase.com/dashboard/project/_/settings/general).

### Full list of extensions

Supabase is pre-configured with over 50 extensions. You can also install your own SQL extensions directly in the database through our SQL editor.

<Extensions />
