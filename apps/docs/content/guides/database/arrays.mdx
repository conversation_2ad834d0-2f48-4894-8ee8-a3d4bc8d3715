---
id: 'arrays'
title: 'Working With Arrays'
description: 'How to use arrays in PostgreSQL and the Supabase API.'
---

Postgres supports flexible [array types](https://www.postgresql.org/docs/12/arrays.html). These arrays are also supported in the Supabase Dashboard and in the JavaScript API.

## Create a table with an array column

Create a test table with a text array (an array of strings):

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="database-method"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Table editor](https://supabase.com/dashboard/project/_/editor) page in the Dashboard.
1. Click **New Table** and create a table with the name `arraytest`.
1. Click **Save**.
1. Click **New Column** and create a column with the name `textarray`, type `text`, and select **Define as array**.
1. Click **Save**.

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
create table arraytest (
  id integer not null,
  textarray text array
);
```

</TabPanel>
</Tabs>

## Insert a record with an array value

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="database-method"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Table editor](https://supabase.com/dashboard/project/_/editor) page in the Dashboard.
1. Select the `arraytest` table.
1. Click **Insert row** and add `["Harry", "Larry", "Moe"]`.
1. Click **Save.**

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
INSERT INTO arraytest (id, textarray) VALUES (1, ARRAY['Harry', 'Larry', 'Moe']);
```

</TabPanel>
<TabPanel id="js" label="JavaScript">

Insert a record from the JavaScript client:

```js
const { data, error } = await supabase
  .from('arraytest')
  .insert([{ id: 2, textarray: ['one', 'two', 'three', 'four'] }])
```

</TabPanel>
<TabPanel id="swift" label="Swift">

Insert a record from the Swift client:

```swift
struct ArrayTest: Encodable {
  let id: Int
  let textarray: [String]
}

try await supabase
  .from("arraytest")
  .insert(
    [
      ArrayTest(
        id: 2,
        textarray: ["one", "two", "three", "four"]
      )
    ]
  )
  .execute()
```

</TabPanel>
<TabPanel id="python" label="Python">

Insert a record from the Python client:

```python
supabase.from_('arraytest').insert(
  [
    {
      id: 2,
      textarray: ["one", "two", "three", "four"]
    }
  ]
)
.execute()
```

</TabPanel>
</Tabs>

## View the results

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="database-method"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Table editor](https://supabase.com/dashboard/project/_/editor) page in the Dashboard.
1. Select the `arraytest` table.

You should see:

```
| id  | textarray               |
| --- | ----------------------- |
| 1   | ["Harry","Larry","Moe"] |
```

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
select * from arraytest;
```

You should see:

```
| id  | textarray               |
| --- | ----------------------- |
| 1   | ["Harry","Larry","Moe"] |
```

</TabPanel>
</Tabs>

## Query array data

Postgres uses 1-based indexing (e.g., `textarray[1]` is the first item in the array).

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="sql"
  queryGroup="language"
>
<TabPanel id="sql" label="SQL">

To select the first item from the array and get the total length of the array:

```js
SELECT textarray[1], array_length(textarray, 1) FROM arraytest;
```

returns:

```
| textarray | array_length |
| --------- | ------------ |
| Harry     | 3            |
```

</TabPanel>
<TabPanel id="js" label="JavaScript">

This returns the entire array field:

```js
const { data, error } = await supabase.from('arraytest').select('textarray')
console.log(JSON.stringify(data, null, 2))
```

returns:

```json
[
  {
    "textarray": ["Harry", "Larry", "Moe"]
  }
]
```

</TabPanel>
<TabPanel id="swift" label="Swift">

This returns the entire array field:

```swift
struct Response: Decodable {
  let textarray: [String]
}

let response: [Response] = try await supabase.from("arraytest").select("textarray").execute().value
dump(response)
```

returns:

```
[
  Response(
    textarray: ["Harry", "Larry", "Moe"],
  )
]
```

</TabPanel>
</Tabs>

## Resources

- [Supabase JS Client](https://github.com/supabase/supabase-js)
- [Supabase - Get started for free](https://supabase.com)
- [Postgres Arrays](https://www.postgresql.org/docs/15/arrays.html)
