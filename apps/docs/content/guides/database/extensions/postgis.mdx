---
id: 'postgis'
title: 'PostGIS: Geo queries'
description: 'Working with geo-spatial data in Postgres'
tocVideo: 'agFsGDJxjwA'
---

[PostGIS](https://postgis.net/) is a Postgres extension that allows you to interact with Geo data within Postgres. You can sort your data by geographic location, get data within certain geographic boundaries, and do much more with it.

## Overview

While you may be able to store simple lat/long geographic coordinates as a set of decimals, it does not scale very well when you try to query through a large data set. PostGIS comes with special data types that are efficient, and indexable for high scalability.

The additional data types that PostGIS provides include [Point](https://postgis.net/docs/using_postgis_dbmanagement.html#Point), [Polygon](https://postgis.net/docs/using_postgis_dbmanagement.html#Polygon), [LineString](https://postgis.net/docs/using_postgis_dbmanagement.html#LineString), and many more to represent different types of geographical data. In this guide, we will mainly focus on how to interact with `Point` type, which represents a single set of latitude and longitude. If you are interested in digging deeper, you can learn more about different data types on the [data management section of PostGIS docs](https://postgis.net/docs/using_postgis_dbmanagement.html).

## Enable the extension

You can get started with PostGIS by enabling the PostGIS extension in your Supabase dashboard.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="database-method"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Database](https://supabase.com/dashboard/project/_/database/tables) page in the Dashboard.
2. Click on **Extensions** in the sidebar.
3. Search for `postgis` and enable the extension.
4. In the confirmation prompt select "Create a new schema" and name it `gis` for example.

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
-- Create a dedicated separate schema
create schema if not exists "gis";

-- Example: enable the "postgis" extension
create extension postgis with schema "gis";

-- Example: disable the "postgis" extension
drop extension if exists postgis;
```

</TabPanel>
</Tabs>

## Examples

Now that we are ready to get started with PostGIS, let’s create a table and see how we can utilize PostGIS for some typical use cases. Let’s imagine we are creating a simple restaurant-searching app.

Let’s create our table. Each row represents a restaurant with its location stored in `location` column as a `Point` type.

```sql
create table if not exists public.restaurants (
	id int generated by default as identity primary key,
	name text not null,
	location gis.geography(POINT) not null
);
```

We can then set a [spatial index](https://postgis.net/docs/using_postgis_dbmanagement.html#build-indexes) on the `location` column of this table.

```sql
create index restaurants_geo_index
  on public.restaurants
  using GIST (location);
```

### Inserting data

You can insert geographical data through SQL or through our API.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="data"
  queryGroup="language"
>
<TabPanel id="data" label="Data">

<h4>Restaurants</h4>

{/* supa-mdx-lint-disable Rule003Spelling */}

| id  | name        | location                         |
| --- | ----------- | -------------------------------- |
| 1   | Supa Burger | lat: 40.807416, long: -73.946823 |
| 2   | Supa Pizza  | lat: 40.807475, long: -73.94581  |
| 3   | Supa Taco   | lat: 40.80629, long: -73.945826  |

{/* supa-mdx-lint-enable Rule003Spelling */}

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
insert into public.restaurants
  (name, location)
values
  ('Supa Burger', gis.st_point(-73.946823, 40.807416)),
  ('Supa Pizza', gis.st_point(-73.94581, 40.807475)),
  ('Supa Taco', gis.st_point(-73.945826, 40.80629));
```

</TabPanel>
<TabPanel id="js" label="JavaScript">

```js
const { error } = await supabase.from('restaurants').insert([
  {
    name: 'Supa Burger',
    location: 'POINT(-73.946823 40.807416)',
  },
  {
    name: 'Supa Pizza',
    location: 'POINT(-73.94581 40.807475)',
  },
  {
    name: 'Supa Taco',
    location: 'POINT(-73.945826 40.80629)',
  },
])
```

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
await supabase.from('restaurants').insert([
  {
    'name': 'Supa Burger',
    'location': 'POINT(-73.946823 40.807416)',
  },
  {
    'name': 'Supa Pizza',
    'location': 'POINT(-73.94581 40.807475)',
  },
  {
    'name': 'Supa Taco',
    'location': 'POINT(-73.945826 40.80629)',
  },
]);
```

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
struct Restaurant: Codable {
    let name: String
    let location: String // You could also use a custom type with a custom `Encodable` conformance for convenience.
}

try await supabase.from("restaurants")
  .insert(
    [
      Restaurant(name: "Supa Burger", location: "POINT(-73.946823 40.807416)"),
      Restaurant(name: "Supa Pizza", location: "POINT(-73.94581 40.807475)"),
      Restaurant(name: "Supa Taco", location: "POINT(-73.945826 40.80629)"),
    ]
  )
  .execute()
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

```kotlin
@Serializable
data class Restaurant(
    val name: String,
    val location: String //you could also use a custom type with a custom serializer for more type safety
)
```

```kotlin
val data = supabase.from("restaurants").insert(listOf(
    Restaurant("Supa Burger", "POINT(-73.946823 40.807416)"),
    Restaurant("Supa Pizza", "POINT(-73.94581 40.807475)"),
    Restaurant("Supa Taco", "POINT(-73.945826 40.80629)"),
))
```

</TabPanel>
</Tabs>

Notice the order in which you pass the latitude and longitude. Longitude comes first, and is because longitude represents the x-axis of the location. Another thing to watch for is when inserting data from the client library, there is no comma between the two values, just a single space.

At this point, if you go into your Supabase dashboard and look at the data, you will notice that the value of the `location` column looks something like this.

```
0101000020E6100000A4DFBE0E9C91614044FAEDEBC0494240
```

We can query the `restaurants` table directly, but it will return the `location` column in the format you see above.
We will create [database functions](https://supabase.com/docs/guides/database/functions) so that we can use the [st_y()](https://postgis.net/docs/ST_Y.html) and [st_x()](https://postgis.net/docs/ST_X.html) function to convert it back to lat and long floating values.

### Order by distance

Sorting datasets from closest to farthest, sometimes called nearest-neighbor sort, is a very common use case in Geo-queries. PostGIS can handle it with the use of the [`<->`](https://postgis.net/docs/geometry_distance_knn.html) operator. `<->` operator returns the two-dimensional distance between two geometries and will utilize the spatial index when used within `order by` clause. You can create the following database function to sort the restaurants from closest to farthest by passing the current locations as parameters.

```sql
create or replace function nearby_restaurants(lat float, long float)
returns table (id public.restaurants.id%TYPE, name public.restaurants.name%TYPE, lat float, long float, dist_meters float)
set search_path = ''
language sql
as $$
  select id, name, gis.st_y(location::gis.geometry) as lat, gis.st_x(location::gis.geometry) as long, gis.st_distance(location, gis.st_point(long, lat)::gis.geography) as dist_meters
  from public.restaurants
  order by location operator(gis.<->) gis.st_point(long, lat)::gis.geography;
$$;
```

Before being able to call this function from our client we need to grant access to our `gis` schema:

```sql
grant usage on schema gis to anon, authenticated;
```

Now you can call this function from your client using `rpc()` like this:

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

```js
const { data, error } = await supabase.rpc('nearby_restaurants', {
  lat: 40.807313,
  long: -73.946713,
})
```

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
final data = await supabase.rpc('nearby_restaurants',params: {
  'lat': 40.807313,
  'long': -73.946713,
});
```

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
struct Response: Codable {
  let id: Int
  let name: String
  let lat: Double
  let long: Double
  let distance: Double

  enum CodingKeys: String, CodingKey {
    case id, name, lat, long
    case distance = "dist_meters"
  }
}

let response: Response = try await supabase.rpc(
  "nearby_restaurants",
  params: [
    "lat": 40.807313,
    "long": -73.946713
  ]
)
.execute()
.value
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

```kotlin
val data = supabase.postgrest.rpc(
    function = "nearby_restaurants",
    parameters = buildJsonObject { //You can put here any serializable object including your own classes
        put("lat", 40.807313)
        put("lon", -73.946713)
    }
)
```

</TabPanel>
<TabPanel id="result" label="Result">

```json
[
  {
    "id": 1,
    "name": "Supa Burger",
    "lat": 40.807416,
    "long": -73.946823,
    "dist_meters": 14.73033739
  },
  {
    "id": 2,
    "name": "Supa Pizza",
    "lat": 40.807475,
    "long": -73.94581,
    "dist_meters": 78.28980007
  },
  {
    "id": 3,
    "name": "Supa Taco",
    "lat": 40.80629,
    "long": -73.945826,
    "dist_meters": 136.04329002
  }
]
```

</TabPanel>
</Tabs>

### Finding all data points within a bounding box

![Searching within a bounding box of a map](/docs/img/guides/database/extensions/postgis/map.png)

When you are working on a map-based application where the user scrolls through your map, you might want to load the data that lies within the bounding box of the map every time your users scroll. PostGIS can return the rows that are within the bounding box just by supplying the bottom left and the top right coordinates. Let’s look at what the function would look like:

```sql
create or replace function restaurants_in_view(min_lat float, min_long float, max_lat float, max_long float)
returns table (id public.restaurants.id%TYPE, name public.restaurants.name%TYPE, lat float, long float)
set search_path to ''
language sql
as $$
	select id, name, gis.st_y(location::gis.geometry) as lat, gis.st_x(location::gis.geometry) as long
	from public.restaurants
	where location operator(gis.&&) gis.ST_SetSRID(gis.ST_MakeBox2D(gis.ST_Point(min_long, min_lat), gis.ST_Point(max_long, max_lat)), 4326)
$$;
```

The [`&&`](https://postgis.net/docs/geometry_overlaps.html) operator used in the `where` statement here returns a boolean of whether the bounding box of the two geometries intersect or not. We are basically creating a bounding box from the two points and finding those points that fall under the bounding box. We are also utilizing a few different PostGIS functions:

- [ST_MakeBox2D](https://postgis.net/docs/ST_MakeBox2D.html): Creates a 2-dimensional box from two points.
- [ST_SetSRID](https://postgis.net/docs/ST_SetSRID.html): Sets the [SRID](https://postgis.net/docs/manual-dev/using_postgis_dbmanagement.html#spatial_ref_sys), which is an identifier of what coordinate system to use for the geometry. 4326 is the standard longitude and latitude coordinate system.

You can call this function from your client using `rpc()` like this:

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="js"
  queryGroup="language"
>
<TabPanel id="js" label="JavaScript">

```js
const { data, error } = await supabase.rpc('restaurants_in_view', {
  min_lat: 40.807,
  min_long: -73.946,
  max_lat: 40.808,
  max_long: -73.945,
})
```

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
final data = await supabase.rpc('restaurants_in_view', params: {
  'min_lat': 40.807,
  'min_long': -73.946,
  'max_lat': 40.808,
  'max_long': -73.945,
});
```

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
struct Response: Codable {
  let id: Int
  let name: String
  let lat: Double
  let long: Double
}

let response: Response = try await supabase.rpc(
  "restaurants_in_view",
  params: [
    "min_lat": 40.807,
    "min_long": -73.946,
    "max_long": -73.945,
    "max_lat": 40.808,
  ]
)
.execute()
.value
```

</TabPanel>
<TabPanel id="kotlin" label="Kotlin">

```kotlin
val data = supabase.postgrest.rpc(
    function = "restaurants_in_view",
    parameters = buildJsonObject { //You can put here any serializable object including your own classes
        put("min_lat", 40.807)
        put("min_lon", -73.946)
        put("max_lat", 40.808)
        put("max_lon", -73.945)
    }
)
```

</TabPanel>
<TabPanel id="result" label="Result">

```json
[
  {
    "id": 2,
    "name": "Supa Pizza",
    "lat": 40.807475,
    "long": -73.94581
  }
]
```

</TabPanel>
</Tabs>

## Troubleshooting

<Admonition type="caution">

The [official PostGIS documentation](https://postgis.net/documentation/tips/tip-move-postgis-schema/) for relocating the schema will cause issues for Supabase projects. These issues might not be apparent immediately but will eventually surface. To relocate your schema, use the following steps instead.

</Admonition>

As of PostGIS 2.3 or newer, the PostGIS extension is no longer relocatable from one schema to another. If you need to move it from one schema to another for any reason (e.g. from the public schema to the extensions schema for security reasons), you would normally run a ALTER EXTENSION to relocate the schema. However, you will now to do the following steps:

1. Backup your Database to prevent data loss - You can do this through the [CLI](https://supabase.com/docs/reference/cli/supabase-db-dump) or Postgres backup tools such as [pg_dumpall](https://www.postgresql.org/docs/current/backup-dump.html#BACKUP-DUMP-ALL)

2. Drop all dependencies you created and the PostGIS extension - `DROP EXTENSION postgis CASCADE;`

3. Enable PostGIS extension in the new schema - `CREATE EXTENSION postgis SCHEMA extensions;`

4. Restore dropped data via the Backup if necessary from step 1 with your tool of choice.

## Resources

- [Official PostGIS documentation](https://postgis.net/documentation/)
