---
title: 'Replicate to another Postgres database using Logical Replication'
description: 'Example to setup logical replication using publish-subscribe to a Postgres database outside of Supabase'
footerHelpType: 'postgres'
---

For this example, you will need:

- A Supabase project
- A Postgres database (running v10 or newer)

You will be running commands on both of these databases to publish changes from the Supabase database to the external database.

1. Create a `publication` on the **Supabase database**:

```sql
CREATE PUBLICATION example_pub;
```

2. Also on the **Supabase database**, create a `replication slot`:

```sql
select pg_create_logical_replication_slot('example_slot', 'pgoutput');
```

3. Now we will connect to our **external database** and subscribe to our `publication` Note: ):

<Admonition type="note">

This will need a direct connection to your database and you can find the connection info in the [Dashboard](https://supabase.com/dashboard/project/_/settings/database).

You will also need to ensure that ipv6 is supported by your replication destination.

If you would prefer not to use the `postgres` user, then you can run `CREATE ROLE <user> WITH REPLICATION;` using the `postgres` user.

</Admonition>

```sql
CREATE SUBSCRIPTION example_sub
CONNECTION 'host=db.oaguxblfdassqxvvwtfe.supabase.co user=postgres password=YOUR_PASS dbname=postgres'
PUBLICATION example_pub
WITH (copy_data = true, create_slot=false, slot_name=example_slot);
```

<Admonition type="note">

`create_slot` is set to `false` because `slot_name` is provided and the slot was already created in Step 2.
To copy data from before the slot was created, set `copy_data` to `true`.

</Admonition>

4. Add all the tables that you want replicated to the publication.

```sql
ALTER PUBLICATION example_pub ADD TABLE example_table;
```

5. Check the replication status using `pg_stat_replication`

```sql
select * from pg_stat_replication;
```
