---
title: 'Select first row for each group in PostgreSQL'
description: 'PostgreSQL snippet for grabbing the first row in each distinct group by group'
footerHelpType: 'postgres'
---

Given a table `seasons`:

| id  |   team    | points |
| --- | :-------: | -----: |
| 1   | Liverpool |     82 |
| 2   | Liverpool |     84 |
| 3   | Brighton  |     34 |
| 4   | Brighton  |     28 |
| 5   | Liverpool |     79 |

We want to find the rows containing the maximum number of points _per team_.

The expected output we want is:

| id  |   team    | points |
| --- | :-------: | -----: |
| 3   | Brighton  |     34 |
| 2   | Liverpool |     84 |

From the [SQL Editor](https://supabase.com/dashboard/project/_/sql), you can run a query like:

```sql
select distinct
  on (team) id,
  team,
  points
from
  seasons
order BY
  id,
  points desc,
  team;
```

The important bits here are:

- The `desc` keyword to order the `points` from highest to lowest.
- The `distinct` keyword that tells Postgres to only return a single row per team.

This query can also be executed via `psql` or any other query editor if you prefer to [connect directly to the database](/docs/guides/database/connecting-to-postgres#direct-connections).
