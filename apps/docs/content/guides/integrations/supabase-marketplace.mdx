---
id: 'integrations'
title: 'Supabase Marketplace'
description: 'Integrations and Partners'
---

The Supabase Marketplace brings together all the tools you need to extend your Supabase project. This includes:

- [Experts](https://supabase.com/partners/experts) - partners to help you build and support your Supabase project.
- [Integrations](https://supabase.com/partners/integrations) - extend your projects with external Auth, Caching, Hosting, and Low-code tools.

## Build an integration

Supabase provides several integration points:

- The [Postgres connection](/docs/guides/database/connecting-to-postgres). Anything that works with Postgres also works with Supabase projects.
- The [Project REST API](/docs/guides/api#rest-api-overview) & client libraries.
- The [Project GraphQL API](/docs/guides/api#graphql-api-overview).
- The [Platform API](/docs/reference/api).

## List your integration

[Apply to the Partners program](https://supabase.com/partners/integrations#become-a-partner) to list your integration in the Partners marketplace and in the Supabase docs.

Integrations are assessed on the following criteria:

- **Business viability**
  While we welcome everyone to built an integration, we only list companies that are deemed to be long-term viable. This includes an official business registration and bank account, meaningful revenue, or Venture Capital backing. We require this criteria to ensure the health of the marketplace.
- **Compliance**
  Integrations should not infringe on the Supabase brand/trademark. In short, you cannot use "Supabase" in the name. As the listing appears on the Supabase domain, we don't want to mislead developers into thinking that an integration is an official product.
- **Service Level Agreements**
  All listings are required to have their own Terms and Conditions, Privacy Policy, and Acceptable Use Policy, and the company must have resources to meet their SLAs.
- **Maintainability**
  All integrations are required to be maintained and functional with Supabase, and the company may be assessed on your ability to remain functional over a long time horizon.
