---
id: 'storage-s3-compatibility'
title: 'S3 Compatibility'
description: 'Compatibility spec'
subtitle: 'Learn about the compatibility of Supabase Storage with S3.'
sidebar_label: 'S3'
---

Supabase Storage is compatible with the S3 protocol. You can use any S3 client to interact with your Storage objects.

Storage supports [standard](/docs/guides/storage/uploads/standard-uploads), [resumable](/docs/guides/storage/uploads/resumable-uploads) and [S3 uploads](/docs/guides/storage/uploads/s3-uploads) and all these protocols are interoperable. You can upload a file with the S3 protocol and list it with the REST API or upload with Resumable uploads and list with S3.

Storage supports presigning a URL using query parameters. Specifically, Supabase Storage expects requests to be made using [AWS Signature Version 4](https://docs.aws.amazon.com/AmazonS3/latest/API/sigv4-query-string-auth.html). To enable this feature, enable the S3 connection via S3 protocol in the Settings page for Supabase Storage.

<Admonition type="note">

The S3 protocol is currently in Public Alpha. If you encounter any issues or have feature requests, [contact us](/dashboard/support/new).

</Admonition>

## Implemented endpoints

The most commonly used endpoints are implemented, and more will be added. Implemented S3 endpoints are marked with ✅ in the following tables.

### Bucket operations

{/* supa-mdx-lint-disable Rule003Spelling */}

| API Name                                                                                                                       | Feature                                                                                                                                                                                                                                                                                           |
| ------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| ✅ [ListBuckets](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListBuckets.html)                                         |                                                                                                                                                                                                                                                                                                   |
| ✅ [HeadBucket](https://docs.aws.amazon.com/AmazonS3/latest/API/API_HeadBucket.html)                                           | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ✅ [CreateBucket](https://docs.aws.amazon.com/AmazonS3/latest/API/API_CreateBucket.html)                                       | ❌ ACL:<br/> ❌ x-amz-acl<br/> ❌ x-amz-grant-full-control<br/> ❌ x-amz-grant-read<br/> ❌ x-amz-grant-read-acp<br/> ❌ x-amz-grant-write<br/> ❌ x-amz-grant-write-acp<br/>❌ Object Locking:<br/> ❌ x-amz-bucket-object-lock-enabled<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner |
| ✅ [DeleteBucket](https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucket.html)                                       | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ✅ [GetBucketLocation](https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketLocation.html)                             | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ❌ [DeleteBucketCors](https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketCors.html)                               | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ❌ [GetBucketEncryption](https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketEncryption.html)                         | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ❌ [GetBucketLifecycleConfiguration](https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketLifecycleConfiguration.html) | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ❌ [GetBucketCors](https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketCors.html)                                     | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                              |
| ❌ [PutBucketCors](https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketCors.html)                                     | ❌ Checksums:<br/> ❌ x-amz-sdk-checksum-algorithm<br/> ❌ x-amz-checksum-algorithm<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                      |
| ❌ [PutBucketLifecycleConfiguration](https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutBucketLifecycleConfiguration.html) | ❌ Checksums:<br/> ❌ x-amz-sdk-checksum-algorithm<br/> ❌ x-amz-checksum-algorithm<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                      |

{/* supa-mdx-lint-enable Rule003Spelling */}

### Object operations

{/* supa-mdx-lint-disable Rule003Spelling */}

| API Name                                                                                                       | Feature                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| -------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| ✅ [HeadObject](https://docs.aws.amazon.com/AmazonS3/latest/API/API_HeadObject.html)                           | ✅ Conditional Operations:<br/> ✅ If-Match<br/> ✅ If-Modified-Since<br/> ✅ If-None-Match<br/> ✅ If-Unmodified-Since<br/>✅ Range:<br/> ✅ Range (has no effect in HeadObject)<br/> ✅ partNumber<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| ✅ [ListObjects](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListObjects.html)                         | Query Parameters:<br/> ✅ delimiter<br/> ✅ encoding-type<br/> ✅ marker<br/> ✅ max-keys<br/> ✅ prefix<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| ✅ [ListObjectsV2](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListObjectsV2.html)                     | Query Parameters:<br/> ✅ list-type<br/> ✅ continuation-token<br/> ✅ delimiter<br/> ✅ encoding-type<br/> ✅ fetch-owner<br/> ✅ max-keys<br/> ✅ prefix<br/> ✅ start-after<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| ✅ [GetObject](https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetObject.html)                             | ✅ Conditional Operations:<br/> ✅ If-Match<br/> ✅ If-Modified-Since<br/> ✅ If-None-Match<br/> ✅ If-Unmodified-Since<br/>✅ Range:<br/> ✅ Range<br/> ✅ PartNumber<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| ✅ [PutObject](https://docs.aws.amazon.com/AmazonS3/latest/API/API_PutObject.html)                             | System Metadata:<br/> ✅ Content-Type<br/> ✅ Cache-Control<br/> ✅ Content-Disposition<br/> ✅ Content-Encoding<br/> ✅ Content-Language<br/> ✅ Expires<br/> ❌ Content-MD5<br/>❌ Object Lifecycle<br/>❌ Website:<br/> ❌ x-amz-website-redirect-location<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/> ❌ x-amz-server-side-encryption-aws-kms-key-id<br/> ❌ x-amz-server-side-encryption-context<br/> ❌ x-amz-server-side-encryption-bucket-key-enabled<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Tagging:<br/> ❌ x-amz-tagging<br/>❌ Object Locking:<br/> ❌ x-amz-object-lock-mode<br/> ❌ x-amz-object-lock-retain-until-date<br/> ❌ x-amz-object-lock-legal-hold<br/>❌ ACL:<br/> ❌ x-amz-acl<br/> ❌ x-amz-grant-full-control<br/> ❌ x-amz-grant-read<br/> ❌ x-amz-grant-read-acp<br/> ❌ x-amz-grant-write-acp<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| ✅ [DeleteObject](https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteObject.html)                       | ❌ Multi-factor authentication:<br/> ❌ x-amz-mfa<br/>❌ Object Locking:<br/> ❌ x-amz-bypass-governance-retention<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| ✅ [DeleteObjects](https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteObjects.html)                     | ❌ Multi-factor authentication:<br/> ❌ x-amz-mfa<br/>❌ Object Locking:<br/> ❌ x-amz-bypass-governance-retention<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| ✅ [ListMultipartUploads](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListMultipartUploads.html)       | ✅ Query Parameters:<br/> ✅ delimiter<br/> ✅ encoding-type<br/> ✅ key-marker<br/> ✅️ max-uploads<br/> ✅ prefix<br/> ✅ upload-id-marker                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| ✅ [CreateMultipartUpload](https://docs.aws.amazon.com/AmazonS3/latest/API/API_CreateMultipartUpload.html)     | ✅ System Metadata:<br/> ✅ Content-Type<br/> ✅ Cache-Control<br/> ✅ Content-Disposition<br/> ✅ Content-Encoding<br/> ✅ Content-Language<br/> ✅ Expires<br/> ❌ Content-MD5<br/>❌ Website:<br/> ❌ x-amz-website-redirect-location<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/> ❌ x-amz-server-side-encryption-aws-kms-key-id<br/> ❌ x-amz-server-side-encryption-context<br/> ❌ x-amz-server-side-encryption-bucket-key-enabled<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Tagging:<br/> ❌ x-amz-tagging<br/>❌ Object Locking:<br/> ❌ x-amz-object-lock-mode<br/> ❌ x-amz-object-lock-retain-until-date<br/> ❌ x-amz-object-lock-legal-hold<br/>❌ ACL:<br/> ❌ x-amz-acl<br/> ❌ x-amz-grant-full-control<br/> ❌ x-amz-grant-read<br/> ❌ x-amz-grant-read-acp<br/> ❌ x-amz-grant-write-acp<br/>❌ Storage class:<br/> ❌ x-amz-storage-class<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| ✅ [CompleteMultipartUpload](https://docs.aws.amazon.com/AmazonS3/latest/API/API_CompleteMultipartUpload.html) | ❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ✅ [AbortMultipartUpload](https://docs.aws.amazon.com/AmazonS3/latest/API/API_AbortMultipartUpload.html)       | ❌ Request Payer:<br/> ❌ x-amz-request-payer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| ✅ [CopyObject](https://docs.aws.amazon.com/AmazonS3/latest/API/API_CopyObject.html)                           | ✅ Operation Metadata:<br/> ⚠️ x-amz-metadata-directive<br/>✅ System Metadata:<br/> ✅ Content-Type<br/> ✅ Cache-Control<br/> ✅ Content-Disposition<br/> ✅ Content-Encoding<br/> ✅ Content-Language<br/> ✅ Expires<br/>✅ Conditional Operations:<br/> ✅ x-amz-copy-source<br/> ✅ x-amz-copy-source-if-match<br/> ✅ x-amz-copy-source-if-modified-since<br/> ✅ x-amz-copy-source-if-none-match<br/> ✅ x-amz-copy-source-if-unmodified-since<br/>❌ ACL:<br/> ❌ x-amz-acl<br/> ❌ x-amz-grant-full-control<br/> ❌ x-amz-grant-read<br/> ❌ x-amz-grant-read-acp<br/> ❌ x-amz-grant-write-acp<br/>❌ Website:<br/> ❌ x-amz-website-redirect-location<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/> ❌ x-amz-server-side-encryption-aws-kms-key-id<br/> ❌ x-amz-server-side-encryption-context<br/> ❌ x-amz-server-side-encryption-bucket-key-enabled<br/> ❌ x-amz-copy-source-server-side-encryption-customer-algorithm<br/> ❌ x-amz-copy-source-server-side-encryption-customer-key<br/> ❌ x-amz-copy-source-server-side-encryption-customer-key-MD5<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Tagging:<br/> ❌ x-amz-tagging<br/> ❌ x-amz-tagging-directive<br/>❌ Object Locking:<br/> ❌ x-amz-object-lock-mode<br/> ❌ x-amz-object-lock-retain-until-date<br/> ❌ x-amz-object-lock-legal-hold<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner<br/> ❌ x-amz-source-expected-bucket-owner<br/>❌ Checksums:<br/> ❌ x-amz-checksum-algorithm |
| ✅ [UploadPart](https://docs.aws.amazon.com/AmazonS3/latest/API/API_UploadPart.html)                           | ✅ System Metadata:<br/>❌ Content-MD5<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| ✅ [UploadPartCopy](https://docs.aws.amazon.com/AmazonS3/latest/API/API_UploadPartCopy.html)                   | ❌ Conditional Operations:<br/> ❌ x-amz-copy-source<br/> ❌ x-amz-copy-source-if-match<br/> ❌ x-amz-copy-source-if-modified-since<br/> ❌ x-amz-copy-source-if-none-match<br/> ❌ x-amz-copy-source-if-unmodified-since<br/>✅ Range:<br/> ✅ x-amz-copy-source-range<br/>❌ SSE-C:<br/> ❌ x-amz-server-side-encryption-customer-algorithm<br/> ❌ x-amz-server-side-encryption-customer-key<br/> ❌ x-amz-server-side-encryption-customer-key-MD5<br/> ❌ x-amz-copy-source-server-side-encryption-customer-algorithm<br/> ❌ x-amz-copy-source-server-side-encryption-customer-key<br/> ❌ x-amz-copy-source-server-side-encryption-customer-key-MD5<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner<br/> ❌ x-amz-source-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| ✅ [ListParts](https://docs.aws.amazon.com/AmazonS3/latest/API/API_ListParts.html)                             | Query Parameters:<br/> ✅ max-parts<br/> ✅ part-number-marker<br/>❌ Request Payer:<br/> ❌ x-amz-request-payer<br/>❌ Bucket Owner:<br/> ❌ x-amz-expected-bucket-owner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |

{/* supa-mdx-lint-enable Rule003Spelling */}
