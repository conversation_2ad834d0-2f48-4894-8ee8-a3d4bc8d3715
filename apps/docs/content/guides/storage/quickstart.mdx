---
id: 'storage-quickstart'
title: 'Storage Quickstart'
description: 'Learn how to use Supabase to store and serve files.'
subtitle: 'Learn how to use Supabase to store and serve files.'
sidebar_label: 'Quickstart'
tocVideo: 'J9mTPY8rIXE'
---

This guide shows the basic functionality of Supabase Storage. Find a full [example application on GitHub](https://github.com/supabase/supabase/tree/master/examples/user-management/nextjs-user-management).

## Concepts

Supabase Storage consists of Files, Folders, and Buckets.

### Files

Files can be any sort of media file. This includes images, GIFs, and videos. It is best practice to store files outside of your database because of their sizes. For security, HTML files are returned as plain text.

### Folders

Folders are a way to organize your files (just like on your computer). There is no right or wrong way to organize your files. You can store them in whichever folder structure suits your project.

### Buckets

Buckets are distinct containers for files and folders. You can think of them like "super folders". Generally you would create distinct buckets for different Security and Access Rules. For example, you might keep all video files in a "video" bucket, and profile pictures in an "avatar" bucket.

<Admonition type="note">

File, Folder, and Bucket names **must follow** [AWS object key naming guidelines](https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-keys.html) and avoid use of any other characters.

</Admonition>

## Create a bucket

You can create a bucket using the Supabase Dashboard. Since the storage is interoperable with your Postgres database, you can also use SQL or our client libraries. Here we create a bucket called "avatars":

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="language"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Storage](https://supabase.com/dashboard/project/_/storage/buckets) page in the Dashboard.
2. Click **New Bucket** and enter a name for the bucket.
3. Click **Create Bucket**.

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
-- Use Postgres to create a bucket.

insert into storage.buckets
  (id, name)
values
  ('avatars', 'avatars');
```

</TabPanel>
<TabPanel id="javascript" label="JavaScript">

```js
// Use the JS library to create a bucket.

const { data, error } = await supabase.storage.createBucket('avatars')
```

[Reference.](/docs/reference/javascript/storage-createbucket)

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
void main() async {
  final supabase = SupabaseClient('supabaseUrl', 'supabaseKey');

  final storageResponse = await supabase
      .storage
      .createBucket('avatars');
}
```

[Reference.](https://pub.dev/documentation/storage_client/latest/storage_client/SupabaseStorageClient/createBucket.html)

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
try await supabase.storage.createBucket("avatars")
```

[Reference.](/docs/reference/swift/storage-createbucket)

</TabPanel>
<TabPanel id="python" label="Python">

```python
response = supabase.storage.create_bucket('avatars')
```

[Reference.](/docs/reference/python/storage-createbucket)

</TabPanel>
</Tabs>

## Upload a file

You can upload a file from the Dashboard, or within a browser using our JS libraries.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="language"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Storage](https://supabase.com/dashboard/project/_/storage/buckets) page in the Dashboard.
2. Select the bucket you want to upload the file to.
3. Click **Upload File**.
4. Select the file you want to upload.

</TabPanel>
<TabPanel id="javascript" label="JavaScript">

```js
const avatarFile = event.target.files[0]
const { data, error } = await supabase.storage
  .from('avatars')
  .upload('public/avatar1.png', avatarFile)
```

[Reference.](/docs/reference/javascript/storage-from-upload)

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
void main() async {
  final supabase = SupabaseClient('supabaseUrl', 'supabaseKey');

  // Create file `example.txt` and upload it in `public` bucket
  final file = File('example.txt');
  file.writeAsStringSync('File content');
  final storageResponse = await supabase
      .storage
      .from('public')
      .upload('example.txt', file);
}
```

[Reference.](https://pub.dev/documentation/storage_client/latest/storage_client/SupabaseStorageClient/createBucket.html)

</TabPanel>
</Tabs>

## Download a file

You can download a file from the Dashboard, or within a browser using our JS libraries.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="language"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Storage](https://supabase.com/dashboard/project/_/storage/buckets) page in the Dashboard.
2. Select the bucket that contains the file.
3. Select the file that you want to download.
4. Click **Download**.

</TabPanel>
<TabPanel id="javascript" label="JavaScript">

```js
// Use the JS library to download a file.

const { data, error } = await supabase.storage.from('avatars').download('public/avatar1.png')
```

[Reference.](/docs/reference/javascript/storage-from-download)

</TabPanel>
<TabPanel id="dart" label="Dart">

```dart
void main() async {
  final supabase = SupabaseClient('supabaseUrl', 'supabaseKey');

  final storageResponse = await supabase
      .storage
      .from('public')
      .download('example.txt');
}
```

[Reference.](/docs/reference/dart/storage-from-download)

</TabPanel>
<TabPanel id="swift" label="Swift">

```swift
let response = try await supabase.storage.from("avatars").download(path: "public/avatar1.png")
```

[Reference.](/docs/reference/python/storage-from-download)

</TabPanel>
<TabPanel id="python" label="Python">

```python
response = supabase.storage.from_('avatars').download('public/avatar1.png')
```

[Reference.](/docs/reference/python/storage-from-download)

</TabPanel>
</Tabs>

## Add security rules

To restrict access to your files you can use either the Dashboard or SQL.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="dashboard"
  queryGroup="database-method"
>
<TabPanel id="dashboard" label="Dashboard">

1. Go to the [Storage](https://supabase.com/dashboard/project/_/storage/buckets) page in the Dashboard.
2. Click **Policies** in the sidebar.
3. Click **Add Policies** in the `OBJECTS` table to add policies for Files. You can also create policies for Buckets.
4. Choose whether you want the policy to apply to downloads (SELECT), uploads (INSERT), updates (UPDATE), or deletes (DELETE).
5. Give your policy a unique name.
6. Write the policy using SQL.

</TabPanel>
<TabPanel id="sql" label="SQL">

```sql
-- Use SQL to create a policy.

create policy "Public Access"
  on storage.objects for select
  using ( bucket_id = 'public' );
```

</TabPanel>
</Tabs>

---

{/* Finish with a video. This also appears in the Sidebar via the "tocVideo" metadata */}

<div className="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/J9mTPY8rIXE"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>
