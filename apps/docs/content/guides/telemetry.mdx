---
title: Telemetry
---

Telemetry helps you understand what’s happening inside your app by collecting logs, metrics, and traces.

- **Logs** capture individual events, such as errors or warnings, providing details about what happened at a specific moment.
- **Metrics** track numerical data over time, like request latency or database query performance, helping you spot trends.
- **Traces** show the flow of a request through different services, helping you debug slow or failing operations.

Supabase is working towards full support for the [OpenTelemetry](https://opentelemetry.io/) standard, making it easier to integrate with observability tools.

This section provides guidance on telemetry in Supabase, including how to work with Supabase Logs.
