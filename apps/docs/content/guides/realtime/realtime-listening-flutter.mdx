---
id: 'realtime-listening-flutter'
title: 'Listening to Postgres Changes with Flutter'
description: 'Listening to real-time changes on the database with Flutter'
sidebar_label: 'Videos'
---

The Postgres Changes extension listens for database changes and sends them to clients which enables you to receive database changes in real-time.

<div className="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/gboTC2lcgzw?si=WBfCrZyqi9zDWS5n"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>
