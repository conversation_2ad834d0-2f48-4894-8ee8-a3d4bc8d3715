---
id: 'realtime-with-nextjs'
title: 'Using Realtime with Next.js'
description: 'Client & Server Components in Next.js with Realtime Updates'
sidebar_label: 'Videos'
---

In this guide, we explore the best ways to receive real-time Postgres changes with your Next.js application.
We'll show both client and server side updates, and explore which option is best.

<div className="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/YR-xP6PPXXA"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>
