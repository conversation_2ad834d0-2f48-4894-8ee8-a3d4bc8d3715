---
id: 'functions-local-quickstart'
title: 'Developing Edge Functions locally'
description: 'Get started with Edge Functions on your local machine.'
subtitle: 'Get started with Edge Functions on your local machine.'
tocVideo: '5OWH9c4u68M'
---

Let's create a basic Edge Function on your local machine and then invoke it using the Supabase CLI.

## Initialize a project

Create a new Supabase project in a folder on your local machine:

```bash
supabase init
```

<Admonition type="tip" label="CLI not installed?">

Check out the [CLI Docs](/docs/guides/cli) to learn how to install the Supabase CLI on your local machine.

</Admonition>

<Admonition type="tip">

If you're using VS code you can have the CLI automatically create helpful Deno settings when running `supabase init`. Select `y` when prompted "Generate VS Code settings for Deno? [y/N]"!

</Admonition>

<Admonition type="tip">

If you're using an IntelliJ IDEA editor such as WebStorm, you can use the `--with-intellij-settings` flag with `supabase init` to create an auto generated Deno config.

</Admonition>

## Create an Edge Function

Let's create a new Edge Function called `hello-world` inside your project:

```bash
supabase functions new hello-world
```

This creates a function stub in your `supabase` folder:

```bash
└── supabase
    ├── functions
    │   └── hello-world
    │   │   └── index.ts ## Your function code
    └── config.toml
```

## How to write the code

The generated function uses native [Deno.serve](https://docs.deno.com/runtime/manual/runtime/http_server_apis) to handle requests. It gives you access to `Request` and `Response` objects.

Here's the generated Hello World Edge Function, that accepts a name in the `Request` and responds with a greeting:

```tsx
Deno.serve(async (req) => {
  const { name } = await req.json()
  const data = {
    message: `Hello ${name}!`,
  }

  return new Response(JSON.stringify(data), { headers: { 'Content-Type': 'application/json' } })
})
```

## Running Edge Functions locally

You can run your Edge Function locally using [`supabase functions serve`](/docs/reference/cli/usage#supabase-functions-serve):

```bash
supabase start # start the supabase stack
supabase functions serve # start the Functions watcher
```

The `functions serve` command has hot-reloading capabilities. It will watch for any changes to your files and restart the Deno server.

## Invoking Edge Functions locally

While serving your local Edge Function, you can invoke it using curl or one of the client libraries.
To call the function from a browser you need to handle CORS requests. See [CORS](/docs/guides/functions/cors).

<$CodeTabs>

```bash name=cURL
curl --request POST 'http://localhost:54321/functions/v1/hello-world' \
  --header 'Authorization: Bearer SUPABASE_ANON_KEY' \
  --header 'Content-Type: application/json' \
  --data '{ "name":"Functions" }'
```

```js name=JavaScript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY)

const { data, error } = await supabase.functions.invoke('hello-world', {
  body: { name: 'Functions' },
})
```

</$CodeTabs>

<Admonition type="tip" label="Where is my SUPABASE_ANON_KEY?">

Run `supabase status` to see your local credentials.

</Admonition>

You should see the response `{ "message":"Hello Functions!" }`.

If you execute the function with a different payload, the response will change.

Modify the `--data '{"name":"Functions"}'` line to `--data '{"name":"World"}'` and try invoking the command again.

## Next steps

Check out the [Deploy to Production](/docs/guides/functions/deploy) guide to make your Edge Function available to the world.

See the [development tips](/docs/guides/functions/development-tips) for best practices.
