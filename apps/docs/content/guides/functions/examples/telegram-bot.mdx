---
id: 'examples-telegram-bot'
title: 'Building a Telegram Bot'
description: 'Building a Telegram Bot with Edge Functions.'
video: 'https://www.youtube.com/v/AWfE3a9J_uo'
---

<div class="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/AWfE3a9J_uo"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

Handle Telegram Bot Webhooks with the [grammY framework](https://grammy.dev/). grammY is an open source Telegram Bot Framework which makes it easy to handle and respond to incoming messages. [View on GitHub](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/telegram-bot).
