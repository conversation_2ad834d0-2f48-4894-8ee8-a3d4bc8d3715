---
title: 'Image Manipulation'
description: 'How to optimize and transform images using Edge Functions.'
---

Supabase Storage has [out-of-the-box support](https://supabase.com/docs/guides/storage/serving/image-transformations?queryGroups=language&language=js) for the most common image transformations and optimizations you need.
If you need to do anything custom beyond what Supabase Storage provides, you can use Edge Functions to write custom image manipulation scripts.

In this example, we will use [`magick-wasm`](https://github.com/dlemstra/magick-wasm) to perform image manipulations. `magick-wasm` is the WebAssembly port of the popular ImageMagick library and supports processing over 100 file formats.

<Admonition type="caution">

Edge Functions currently doesn't support image processing libraries such as `Sharp`, which depend on native libraries. Only WASM-based libraries are supported.

</Admonition>

### Prerequisites

Make sure you have the latest version of the [Supabase CLI](https://supabase.com/docs/guides/cli#installation) installed.

### Create the Edge Function

Create a new function locally:

```bash
supabase functions new image-blur

```

### Write the function

In this example, we are implementing a function allowing users to upload an image and get a blurred thumbnail.

Here's the implementation in `index.ts` file:

<$CodeSample
path="edge-functions/supabase/functions/image-manipulation/index.ts"
lines={[[1, -1]]}
/>

### Test it locally

You can test the function locally by running:

```bash
supabase start
supabase functions serve --no-verify-jwt

```

Then, make a request using `curl` or your favorite API testing tool.

```bash
curl --location '<http://localhost:54321/functions/v1/image-blur>' \\
--form 'file=@"/path/to/image.png"'
--output '/path/to/output.png'

```

If you open the `output.png` file you will find a transformed version of your original image.

### Deploy to your hosted project

Now, let's deploy the function to your Supabase project.

```bash
supabase link
supabase functions deploy image-blur

```

<Admonition type="caution">

Hosted Edge Functions have [limits](https://supabase.com/docs/guides/functions/limits) on memory and CPU usage.

If you try to perform complex image processing or handle large images (> 5MB) your function may return a resource limit exceeded error.

</Admonition>
