---
title: 'Taking Screenshots with <PERSON><PERSON><PERSON><PERSON>'
description: 'Take screenshots in Edge Functions with <PERSON><PERSON>peteer and Browserless.io.'
---

<div class="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/Q1nfnQggR4c"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

[Puppeteer](https://pptr.dev/) is a handy tool to programmatically take screenshots and generate PDFs. However, trying to do so in Edge Functions can be challenging due to the size restrictions. Luckily there is a [serverless browser offering available](https://www.browserless.io/) that we can connect to via WebSockets.

Find the code on [GitHub](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/puppeteer).
