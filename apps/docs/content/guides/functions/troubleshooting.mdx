---
id: 'functions-debugging'
title: 'Troubleshooting Common Issues'
description: 'How to solve common problems and issues related to Edge Functions.'
subtitle: 'How to solve common problems and issues related to Edge Functions.'
---

If you encounter any problems or issues with your Edge Functions, here are some tips and steps to help you resolve them.

### Unable to deploy Edge Function

- Make sure you're on the latest version of the [Supabase CLI](/docs/guides/cli#updates).
- If the output from the commands above does not help you to resolve the issue, open a support ticket via the Supabase Dashboard (by clicking the "Help" button at the top right) and include all output from the commands mentioned above.

### Unable to call Edge Function

If you’re unable to call your Edge Function or are experiencing any CORS issues:

- Make sure you followed the [CORS guide](/docs/guides/functions/cors). This guide explains how to enable and configure CORS for your Edge Functions, and how to avoid common pitfalls and errors.
- Check your function logs. Navigate to the [Functions section](https://supabase.com/dashboard/project/_/functions) in your dashboard, select your function from the list, and click `Logs`. Check for any errors or warnings that may indicate the cause of the problem.

There are two debugging tools available: Invocations and Logs. Invocations shows the Request and Response for each execution, while Logs shows any platform events, including deployments and errors.

### Edge Function takes too long to respond

If your Edge Function takes too long to respond or times out:

- Navigate to the [Functions section](https://supabase.com/dashboard/project/_/functions) in your dashboard, select your function from the list, and click `Logs`.
- In the logs, look for the `booted` event and check if they have consistent boot times.
  - If the boot times are similar, it’s likely an issue with your function’s code, such as a large dependency, a slow API call, or a complex computation. You can try to optimize your code, reduce the size of your dependencies, or use caching techniques to improve the performance of your function.
  - If only some of the `booted` events are slow, find the affected `region` in the metadata and submit a support request via the "Help" button at the top.

{/* supa-mdx-lint-disable-next-line Rule001HeadingCase */}

### Receiving 546 Error Response

The 546 error response might occur because:

- **Memory or CPU Limits**: The function might have exhausted its memory or encountered CPU limits enforced during execution.
- **Event Loop Completion**: If you observe "Event loop completed" in your error logs, it's likely your function is not implemented correctly. You should check your function code for any syntax errors, infinite loops, or unresolved promises that might cause this error. Or you can try running the function locally (using Supabase CLI **`functions serve`**) to see if you can debug the error. The local console should give a full stack trace on the error with line numbers of the source code. You can also refer to [Edge Functions examples](https://github.com/supabase/supabase/tree/master/examples/edge-functions) for guidance.

### Issues serving Edge Functions locally with the Supabase CLI

- Make sure you're on the latest version of the [Supabase CLI](/docs/guides/cli#updates).
- Run the serve command with the `-debug` flag.
- Support engineers can then try to run the provided sample code locally and see if they can reproduce the issue.
- Search the [Edge Runtime](https://github.com/supabase/edge-runtime) and [CLI](https://github.com/supabase/cli) repos for the error message, to see if it has been reported before.
- If the output from the commands above does not help you to resolve the issue, open a support ticket via the Supabase Dashboard (by clicking the "Help" button at the top right) and include all output and details about your commands.

## Advanced techniques

### Monitoring Edge Function resource usage

To determine how much memory and CPU your Edge Function consumes, follow these steps:

- Navigate to the Supabase Dashboard.
- Go to **Edge Functions**.
- Select the specific function by clicking on its name.
- View the resource usage **Metrics** on the charts provided.

<Admonition type="note">
  Edge Functions have limited resources (CPU, memory, and execution time) compared to traditional
  servers. Make sure your functions are optimized for performance and don't exceed the allocated
  resources.
</Admonition>

### Understanding CPU soft and hard limits

An isolate is like a worker that can handle multiple requests for a function. It works until a time limit of 400 seconds is reached. Now, there are two types of limits for the CPU.

1. **Soft Limit**: When the isolate hits the soft limit, it retires. This means it won't take on any new requests, but it will finish processing the ones it's already working on. It keeps going until it either hits the hard limit for CPU time or reaches the 400-second time limit, whichever comes first.
2. **Hard Limit**: If there are new requests after the soft limit is reached, a new isolate is created to handle them. The original isolate continues until it hits the hard limit or the time limit. This ensures that existing requests are completed, and new ones will be managed by a newly created isolate.

### Checking function boot time

Check the logs for the function. In the logs, look for a "Booted" event and note the reported boot time. If available, click on the event to access more details, including the regions from where the function was served. Investigate if the boot time is excessively high (longer than 1 second) and note any patterns or regions where it occurs. You can refer to this guide for troubleshooting [regional invocations](/docs/guides/functions/regional-invocation).

### Finding bundle size

To find the bundle size of a function, run the following command locally:

`deno info /path/to/function/index.ts`

Look for the "size" field in the output which represents the approximate bundle size of the function. You can find the accurate bundle size when you deploy your function via Supabase CLI. If the function is part of a larger application, consider examining the bundle size of the specific function independently.

The source code of a function is subject to 10MB site limit.

### Analyze dependencies

When analyzing dependencies for your Supabase Edge Functions, it's essential to review both Deno and NPM dependencies to ensure optimal performance and resource utilization.
By selectively importing only the required submodules, you can effectively reduce the size of your function's dependencies and optimize its performance.
Before finalizing your imports, ensure to review both Deno and NPM dependencies, checking for any unnecessary or redundant dependencies that can be removed. Additionally, check for outdated dependencies and update to the latest versions if possible.

#### Deno dependencies

Run `deno info`, providing the path to your input map if you use one.
Review the dependencies listed in the output. Pay attention to any significantly large dependencies, as they can contribute to increased bundle size and potential boot time issues.
Examine if there are any unnecessary or redundant dependencies that can be removed. Check for outdated dependencies and update to the latest versions if possible.

```bash
deno info --import-map=/path/to/import_map.json /path/to/function/index.ts
```

#### NPM dependencies

Additionally, if you utilize NPM modules in your Edge Functions, it's crucial to be mindful of their size and impact on the overall bundle size. While importing NPM modules, consider using the notation `import { submodule } from 'npm:package/submodule'` to selectively import specific submodules rather than importing the entire package. This approach can help minimize unnecessary overhead and streamline the execution of your function.

For example, if you only need the `Sheets` submodule from the `googleapis` package, you can import it like this:

```typescript
import { Sheets } from 'npm:@googleapis/sheets'
```
