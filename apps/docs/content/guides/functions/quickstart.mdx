---
id: 'functions-quickstart'
title: 'Developing Edge Functions with Supabase'
description: 'Get started with Edge Functions on the Supabase dashboard.'
subtitle: 'Get started with Edge Functions on the Supabase dashboard.'
---

In this guide we'll cover how to create a basic Edge Function on the Supabase dashboard, and access it using the Supabase CLI.

## Deploy from Dashboard

Go to your project > Edge Functions > Deploy a new function > Via Editor

<Image
  alt="Deploy functions from the dashboard"
  src={{
    light: '/docs/img/guides/functions/dashboard/deploy-function--light.png',
    dark: '/docs/img/guides/functions/dashboard/deploy-function--light.png',
  }}
  zoomable
/>

This will scaffold a new function for you. You can choose from Templates some of the pre-defined functions for common use cases.

<Image
  alt="Scaffold functions through the dashboard editor"
  src={{
    light: '/docs/img/guides/functions/dashboard/scaffold-function--light.png',
    dark: '/docs/img/guides/functions/dashboard/scaffold-function--light.png',
  }}
  zoomable
/>

Modify the function as needed, name it, and click `Deploy function`.

Your function is now active. Navigate to the function's details page, and click on the test button.

You can test your function by providing the expected HTTP method, headers, query parameters, and request body. You can also change the authorization token passed (e.g., anon key or a user key).

<div style={{ width: '60%', position: 'relative', aspectRatio: 'auto' }}>
  <Image
    alt="Provide a request body to test your function"
    src={{
      light: '/docs/img/guides/functions/dashboard/test-request-body--light.png',
      dark: '/docs/img/guides/functions/dashboard/test-request-body--light.png',
    }}
    zoomable
  />
</div>

## Access deployed functions via Supabase CLI

<Admonition type="tip" label="CLI not installed?">

Check out the [CLI Docs](/docs/guides/cli) to learn how to install the Supabase CLI on your local machine.

</Admonition>

Now that your function is deployed, you can access it from your local development environment.
Here's how:

1. **Link your project** to your local environment.

   You can find your project reference ID in the URL of your Supabase dashboard or in the project settings.

   ```bash
   supabase link --project-ref your-project-ref
   ```

2. **List all Functions** in the linked Supabase project.

   ```bash
   supabase functions list
   ```

3. **Access the specific function** you want to work on.

   ```bash
   supabase functions download function-name
   ```

4. **Make local edits** to the function code as needed.

5. **Run your function locally** before redeploying.

   ```bash
   supabase functions serve function-name
   ```

6. **Redeploy** when you're ready with your changes.

   ```bash
   supabase functions deploy function-name
   ```

{/* supa-mdx-lint-disable-next-line Rule001HeadingCase */}

## Deploy via Assistant

You can also leverage the Supabase Assistant to help you write and deploy edge functions.

Go to your project > Edge Functions > Click on the Assistant icon to Create with Supabase Assistant

<Image
  alt="Open Supabase Assistant"
  src={{
    light: '/docs/img/guides/functions/dashboard/create-with-assistant--light.png',
    dark: '/docs/img/guides/functions/dashboard/create-with-assistant--light.png',
  }}
  zoomable
/>

This brings up an assistant window with a pre-filled prompt for generating edge functions.
Write up your Edge Function requirement, and let Supabase Assistant do the rest.

<div style={{ width: '75%', position: 'relative', aspectRatio: 'auto' }}>
  <Image
    alt="Generate a function with the assistant"
    src={{
      light: '/docs/img/guides/functions/dashboard/assistant-function-gen--light.png',
      dark: '/docs/img/guides/functions/dashboard/assistant-function-gen--light.png',
    }}
    zoomable
  />
</div>

Click Deploy and the Assistant will automatically deploy your function.

This function requires an OpenAI API key. You can add the key in your Edge Functions secrets page, or ask Assistant for help.

1. Navigate to your Edge Functions > Secrets page.
2. Look for the option to add environment variables.
3. Add a new environment variable with the key `OPENAI_API_KEY` and set its value to your actual OpenAI API key.

Once you've set this environment variable, your edge functions will be able to access the OPENAI_API_KEY securely without hardcoding it into the function code. This is a best practice for keeping sensitive information safe.

With your variable set, you can test by sending a request via the dashboard. Navigate to the function's details page, and click on the test button. Then provide a Request Body your function expects.

<div style={{ width: '60%', position: 'relative', aspectRatio: 'auto' }}>
  <Image
    alt="Provide a request body to test your function"
    src={{
      light: '/docs/img/guides/functions/dashboard/test-request-body--light.png',
      dark: '/docs/img/guides/functions/dashboard/test-request-body--light.png',
    }}
    zoomable
  />
</div>

## Editing functions from the Dashboard

<Admonition type="caution" label="Be careful: there is currently no version control for edits">

The Dashboard's Edge Function editor currently does not support versioning or rollbacks. We recommend using it only for quick testing and prototypes. When you’re ready to go to production, store Edge Functions code in a source code repository (e.g., git) and deploy it using one of the [CI integrations](https://supabase.com/docs/guides/functions/cicd-workflow).

</Admonition>

1. From the functions page, click on the function you want to edit. From the function page, click on the Code tab.

2. This opens up a code editor in the dashboard where you can see your deployed function's code.

3. Modify the code as needed, then click Deploy updates. This will overwrite the existing deployment with the newly edited function code.

## Next steps

Check out the [Local development](/docs/guides/functions/local-quickstart) guide for more details on working with Edge Functions.

Read on for some [common development tips](/docs/guides/functions/development-tips).
