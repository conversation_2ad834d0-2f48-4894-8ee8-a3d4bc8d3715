---
id: 'functions-local-development'
title: 'Local development'
description: 'Setup local development environment for Edge Functions.'
subtitle: 'Setup local development environment for Edge Functions.'
---

We recommend installing the Deno CLI and related tools for local development.

## Deno support

You can follow the [Deno guide](https://deno.com/manual@v1.32.5/getting_started/setup_your_environment) for setting up your development environment with your favorite editor/IDE.

## Deno with Visual Studio Code

When using VSCode, you should install both the Deno CLI and the the Deno language server [via this link](vscode:extension/denoland.vscode-deno) or by browsing the extensions in VSCode and choosing to install the _Deno_ extension.

<Admonition type="tip">

The Supabase CLI can automatically create helpful Deno settings when running `supabase init`. Select `y` when prompted "Generate VS Code settings for Deno? [y/N]"!

</Admonition>

## Deno support in subfolders

You can enable the Deno language server for specific sub-paths in a workspace, while using VSCode's built-in JavaScript/TypeScript language server for all other files.

For example if you have a project like this:

```
project
├── app
└── supabase
  └── functions
```

To enable the Deno language server only for the `supabase/functions` folder, add `./supabase/functions` to the list of _Deno: Enable Paths_ in the configuration. In your `.vscode/settings.json` file add:

```json
{
  "deno.enablePaths": ["./supabase/functions"],
  "deno.importMap": "./supabase/functions/import_map.json"
}
```

## Multi-root workspaces in VSCode

We recommend using `deno.enablePaths` mentioned above as it's easier to manage, however if you like [multi-root workspaces](https://code.visualstudio.com/docs/editor/workspaces#_multiroot-workspaces) you can use these as an alternative.

<div className="video-container">
  <iframe
    src="https://www.youtube-nocookie.com/embed/lFhU3L8VoSQ"
    frameBorder="1"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
    allowFullScreen
  ></iframe>
</div>

For example, see this `edge-functions.code-workspace` configuration for a CRA (create react app) client with Supabase Edge Functions. You can find the complete example on [GitHub](https://github.com/supabase/supabase/tree/master/examples/edge-functions).

```json
{
  "folders": [
    {
      "name": "project-root",
      "path": "./"
    },
    {
      "name": "client",
      "path": "app"
    },
    {
      "name": "supabase-functions",
      "path": "supabase/functions"
    }
  ],
  "settings": {
    "files.exclude": {
      "node_modules/": true,
      "app/": true,
      "supabase/functions/": true
    },
    "deno.importMap": "./supabase/functions/import_map.json"
  }
}
```
