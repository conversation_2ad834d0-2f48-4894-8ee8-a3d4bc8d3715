---
id: 'functions-import-maps'
title: 'Managing dependencies'
description: 'Managing packages and dependencies.'
subtitle: 'Managing packages and dependencies.'
tocVideo: 'ILr3cneZuFk'
---

## Importing dependencies

Supabase Edge Functions support several ways to import dependencies:

- JavaScript modules from npm (https://docs.deno.com/examples/npm/)
- Built-in [Node APIs](https://docs.deno.com/runtime/manual/node/compatibility)
- Modules published to [JSR](https://jsr.io/) or [deno.land/x](https://deno.land/x)

### NPM modules

You can import npm modules using the `npm:` specifier:

```ts
import { createClient } from 'npm:@supabase/supabase-js@2'
```

### Node.js built-ins

For Node.js built-in APIs, use the `node:` specifier:

```ts
import process from 'node:process'
```

Learn more about npm specifiers and Node built-in APIs in [Deno's documentation](https://docs.deno.com/runtime/manual/node/npm_specifiers).

### JSR

You can import JS modules published to [JSR](https://jsr.io/) (e.g.: Deno's standard library), using the `jsr:` specifier:

```ts
import path from 'jsr:@std/path@1.0.8'
```

## Managing dependencies

Developing with Edge Functions is similar to developing with Node.js, but with a few key differences.

In the Deno ecosystem, each function should be treated as an independent project with its own set of dependencies and configurations. This "isolation by design" approach:

- Ensures each function has explicit control over its dependencies
- Prevents unintended side effects between functions
- Makes deployments more predictable and maintainable
- Allows for different versions of the same dependency across functions

For these reasons, we recommend maintaining separate configuration files (`deno.json`, `.npmrc`, or `import_map.json`) within each function's directory, even if it means duplicating some configurations.

There are two ways to manage your dependencies in Supabase Edge Functions:

### Using deno.json (recommended)

<Admonition type="note">

This feature requires Supabase CLI version 1.215.0 or higher.

</Admonition>

Each function should have its own `deno.json` file to manage dependencies and configure Deno-specific settings. This ensures proper isolation between functions and is the recommended approach for deployment. For a complete list of supported options, see the [official Deno configuration documentation](https://docs.deno.com/runtime/manual/getting_started/configuration_file).

```json supabase/functions/my-function/deno.json
{
  "imports": {
    "lodash": "https://cdn.skypack.dev/lodash"
  }
}
```

The recommended file structure for deployment:

```bash
└── supabase
    ├── functions
    │   ├── function-one
    │   │   ├── index.ts
    │   │   ├─- deno.json    # Function-specific Deno configuration
    │   │   └── .npmrc       # Function-specific npm configuration (if needed)
    │   └── function-two
    │       ├── index.ts
    │       ├─- deno.json    # Function-specific Deno configuration
    │       └── .npmrc       # Function-specific npm configuration (if needed)
    └── config.toml
```

<Admonition type="caution">
  While it's possible to use a global `deno.json` in the `/supabase/functions` directory for local
  development, this approach is not recommended for deployment. Each function should maintain its
  own configuration to ensure proper isolation and dependency management.
</Admonition>

### Using import maps (legacy)

Import Maps are a legacy way to manage dependencies, similar to a `package.json` file. While still supported, we recommend using `deno.json`. If both exist, `deno.json` takes precedence.

Each function should have its own `import_map.json` file for proper isolation:

```json supabase/functions/my-function/import_map.json
{
  "imports": {
    "lodash": "https://cdn.skypack.dev/lodash"
  }
}
```

The recommended file structure:

```bash
└── supabase
    ├── functions
    │   ├── function-one
    │   │   ├── index.ts
    │   │   └── import_map.json    # Function-specific import map
    │   └── function-two
    │       ├── index.ts
    │       └── import_map.json    # Function-specific import map
    └── config.toml
```

<Admonition type="caution">
  While it's possible to use a global `import_map.json` in the `/supabase/functions` directory for
  local development, this approach is not recommended for deployment. Each function should maintain
  its own import map to ensure proper isolation.
</Admonition>

If using import maps with VSCode, update your `.vscode/settings.json` to point to your function-specific import map:

```json settings.json
{
  "deno.enable": true,
  "deno.unstable": [
    "bare-node-builtins",
    "byonm"
    // ... other flags ...
  ],
  "deno.importMap": "./supabase/functions/my-function/import_map.json"
}
```

You can override the default import map location using the `--import-map <string>` flag with `serve` and `deploy` commands, or by setting the `import_map` property in your `config.toml` file:

```toml supabase/config.toml
[functions.my-function]
import_map = "./supabase/functions/my-function/import_map.json"
```

### Importing from private registries

<Admonition type="info">This feature requires Supabase CLI version 1.207.9 or higher.</Admonition>

To use private npm packages, create a `.npmrc` file within your function directory. This ensures proper isolation and dependency management for each function.

```bash
└── supabase
    └── functions
        └── my-function
            ├── index.ts
            ├── deno.json
            └── .npmrc       # Function-specific npm configuration
```

Add your registry details in the `.npmrc` file. Follow [this guide](https://docs.npmjs.com/cli/v10/configuring-npm/npmrc) to learn more about the syntax of npmrc files.

```plaintext
@myorg:registry=https://npm.registryhost.com
//npm.registryhost.com/:_authToken=VALID_AUTH_TOKEN
```

<Admonition type="caution">
  While it's possible to use a global `.npmrc` in the `/supabase/functions` directory for local
  development, we recommend using function-specific `.npmrc` files for deployment to maintain proper
  isolation.
</Admonition>

After configuring your `.npmrc`, you can import the private package in your function code:

```ts
import MyPackage from 'npm:@myorg/private-package@v1.0.1'

// use MyPackage
```

### Using a custom NPM registry

<Admonition type="info">This feature requires Supabase CLI version 2.2.8 or higher.</Admonition>

Some organizations require a custom NPM registry for security and compliance purposes. In such instances, you can specify the custom NPM registry to use via `NPM_CONFIG_REGISTRY` environment variable.

You can define it in the project's `.env` file or directly specify it when running the deploy command:

```bash
NPM_CONFIG_REGISTRY=https://custom-registry/ supabase functions deploy my-function
```

## Importing types

If your [environment is set up properly](/docs/guides/functions/local-development) and the module you're importing is exporting types, the import will have types and autocompletion support.

Some npm packages may not ship out of the box types and you may need to import them from a separate package. You can specify their types with a `@deno-types` directive:

```ts
// @deno-types="npm:@types/express@^4.17"
import express from 'npm:express@^4.17'
```

To include types for built-in Node APIs, add the following line to the top of your imports:

```ts
/// <reference types="npm:@types/node" />
```
