---
id: 'function-regional-invocation'
title: 'Regional Invocations'
description: 'How to execute an Edge Functions in a particular region.'
subtitle: 'How to execute an Edge Function in a particular region.'
---

Edge Functions are executed in the region closest to the user making the request. This helps to reduce network latency and provide faster responses to the user.

However, if your Function performs lots of database or storage operations, invoking the Function in the same region as your database may provide better performance. Some situations where this might be helpful include:

- Bulk adding and editing records in your database
- Uploading files

Supabase provides an option to specify the region when invoking the Function.

## Using the `x-region` header

Use the `x-region` HTTP header when calling an Edge Function to determine where the Function should be executed:

<$CodeTabs>

```bash name=cURL
# https://supabase.com/docs/guides/functions/deploy#invoking-remote-functions
curl --request POST 'https://<project_ref>.supabase.co/functions/v1/hello-world' \
  --header 'Authorization: Bearer ANON_KEY' \
  --header 'Content-Type: application/json' \
  --header 'x-region: eu-west-3' \
  --data '{ "name":"Functions" }'
```

```js name=JavaScript
// https://supabase.com/docs/reference/javascript/installing
import { createClient } from '@supabase/supabase-js'

// Create a single supabase client for interacting with your database
const supabase = createClient('https://xyzcompany.supabase.co', 'public-anon-key')

// https://supabase.com/docs/reference/javascript/functions-invoke
const { data, error } = await supabase.functions.invoke('hello-world', {
  body: { name: 'Functions' },
  headers: { 'x-region': 'eu-west-3' },
})
```

</$CodeTabs>

You can verify the execution region by looking at the `x-sb-edge-region` HTTP header in the response. You can also find it as metadata in [Edge Function Logs](/docs/guides/functions/logging).

## Available regions

These are the currently supported region values you can provide for `x-region` header.

- `ap-northeast-1`
- `ap-northeast-2`
- `ap-south-1`
- `ap-southeast-1`
- `ap-southeast-2`
- `ca-central-1`
- `eu-central-1`
- `eu-west-1`
- `eu-west-2`
- `eu-west-3`
- `sa-east-1`
- `us-east-1`
- `us-west-1`
- `us-west-2`

## Using the client library

You can also specify the region when invoking a Function using the Supabase client library:

```js
import { createClient, FunctionRegion } from '@supabase/supabase-js'
const supabase = createClient('SUPABASE_URL', 'SUPABASE_ANON_KEY')

const { data: ret, error } = await supabase.functions.invoke('my-function-name', {
  headers: { 'Content-Type': 'application/json' },
  method: 'GET',
  body: {},
  region: FunctionRegion.UsEast1,
})
```

## Handling regional outages

If you explicitly specify the region via `x-region` header, requests **will NOT** be automatically re-routed to another region and you should consider temporarily changing regions during the outage.
