---
id: 'cicd-workflow'
title: 'Deploying with CI / CD pipelines'
description: 'Use GitHub Actions, Bitbucket, and GitLab CI to deploy your Edge Functions.'
subtitle: 'Use GitHub Actions, Bitbucket, and GitLab CI to deploy your Edge Functions.'
tocVideo: '6OMVWiiycLs'
---

You can use popular CI / CD tools like GitHub Actions, Bitbucket, and GitLab CI to automate Edge Function deployments.

## GitHub Actions

You can use the official [`setup-cli` GitHub Action](https://github.com/marketplace/actions/supabase-cli-action) to run Supabase CLI commands in your GitHub Actions.

The following GitHub Action deploys all Edge Functions any time code is merged into the `main` branch:

```yaml
name: Deploy Function

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      PROJECT_ID: your-project-id

    steps:
      - uses: actions/checkout@v4

      - uses: supabase/setup-cli@v1
        with:
          version: latest

      - run: supabase functions deploy --project-ref $PROJECT_ID
```

## GitLab CI

Here is the sample pipeline configuration to deploy via GitLab CI.

```yaml
image: node:20

# List of stages for jobs, and their order of execution
stages:
  - setup
  - deploy

# This job runs in the setup stage, which runs first.
setup-npm:
  stage: setup
  script:
    - npm i supabase
  cache:
    paths:
      - node_modules/
  artifacts:
    paths:
      - node_modules/

# This job runs in the deploy stage, which only starts when the job in the build stage completes successfully.
deploy-function:
  stage: deploy
  script:
    - npx supabase init
    - npx supabase functions deploy --debug
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
```

## Bitbucket Pipelines

Here is the sample pipeline configuration to deploy via Bitbucket.

```yaml
image: node:20

pipelines:
  default:
    - step:
        name: Setup
        caches:
          - node
        script:
          - npm i supabase
    - parallel:
        - step:
            name: Functions Deploy
            script:
              - npx supabase init
              - npx supabase functions deploy --debug
            services:
              - docker
```

## Declarative configuration

Individual function configuration like [JWT verification](/docs/guides/cli/config#functions.function_name.verify_jwt) and [import map location](/docs/guides/cli/config#functions.function_name.import_map) can be set via the `config.toml` file.

```toml
[functions.hello-world]
verify_jwt = false
```

## Resources

- See the [example on GitHub](https://github.com/supabase/supabase/blob/master/examples/edge-functions/.github/workflows/deploy.yaml).
