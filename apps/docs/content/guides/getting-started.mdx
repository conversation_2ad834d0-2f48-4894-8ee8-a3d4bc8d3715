---
id: 'getting-started'
title: 'Getting Started'
description: 'Resources for getting started with Supabase.'
hideToc: true
---

<div className="flex flex-col gap-12 my-12">

<div>

  <div className="grid grid-cols-12 gap-6 not-prose">
    {[
      {
        title: 'Features',
        hasLightIcon: true,
        href: '/guides/getting-started/features',
        description: 'A non-exhaustive list of features that Supabase provides for every project.'
      },
      {
        title: 'Architecture',
        hasLightIcon: true,
        href: '/guides/getting-started/architecture',
        description: "An overview of Supabase's architecture and product principles.",
      },
      {
        title: 'Local Development',
        hasLightIcon: true,
        href: '/guides/cli/getting-started',
        description: 'Use the Supabase CLI to develop locally and collaborate between teams.',
      }
    ].map((resource) => {
      return (
        <Link
          href={`${resource.href}`}
          key={resource.title}
          className={'col-span-12 md:col-span-4'}
          passHref
        >
          <GlassPanel {...resource} background={false} showIconBg={true}>
            {resource.description}
          </GlassPanel>
        </Link>
      )

})}

  </div>

</div>

</div>

### Use cases

<div className="grid lg:grid-cols-12 gap-6 not-prose">
  {[
    {
      title: 'AI, Vectors, and embeddings',
      href: '/guides/ai#examples',
      description: `Build AI-enabled applications using our Vector toolkit.`,
      icon: '/docs/img/icons/openai_logo',
      hasLightIcon: true,
    },
    {
      title: 'Subscription Payments (SaaS)',
      href: 'https://github.com/vercel/nextjs-subscription-payments#nextjs-subscription-payments-starter',
      description: `Clone, deploy, and fully customize a SaaS subscription application with Next.js.`,
      icon: '/docs/img/icons/nextjs-icon',
    },
    {
      title: 'Partner Gallery',
      href: 'https://github.com/supabase-community/partner-gallery-example#supabase-partner-gallery-example',
      description: `Postgres full-text search, image storage, and more.`,
      icon: '/docs/img/icons/nextjs-icon',
    },
  ].map((item) => {
    return (
      <Link href={`${item.href}`} key={item.title} passHref className={'col-span-4'}>
        <GlassPanel
          title={item.title}
          span="col-span-6"
          background={false}
          icon={item.icon}
          hasLightIcon={item.hasLightIcon}
        >
          {item.description}
        </GlassPanel>
      </Link>
    )
  })}
</div>

### Framework quickstarts

<div className="grid lg:grid-cols-12 gap-6 not-prose">
  {[
    {
      title: 'React',
      href: '/guides/getting-started/quickstarts/reactjs',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a React app.',
      icon: '/docs/img/icons/react-icon',
    },
    {
      title: 'Next.js',
      href: '/guides/getting-started/quickstarts/nextjs',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a Next.js app.',
      icon: '/docs/img/icons/nextjs-icon',
      hasLightIcon: true,
    },
    {
      title: 'Nuxt',
      href: '/guides/getting-started/quickstarts/nuxtjs',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a Nuxt app.',
      icon: '/docs/img/icons/nuxt-icon',
    },
    {
      title: 'Hono',
      href: '/guides/getting-started/quickstarts/hono',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, secure it with auth, and query the data from a Hono app.',
      icon: '/docs/img/icons/hono-icon',
    },
    {
      title: 'RedwoodJS',
      href: '/guides/getting-started/quickstarts/redwoodjs',
      description:
        'Learn how to create a Supabase project, add some sample data to your database using Prisma migration and seeds, and query the data from a RedwoodJS app.',
      icon: '/docs/img/icons/redwood-icon',
    },
    {
      title: 'Flutter',
      href: '/guides/getting-started/quickstarts/flutter',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a Flutter app.',
      icon: '/docs/img/icons/flutter-icon',
    },
    {
      title: 'iOS SwiftUI',
      href: '/guides/getting-started/quickstarts/ios-swiftui',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from an iOS app.',
      icon: '/docs/img/icons/swift-icon',
    },
    {
      title: 'Android Kotlin',
      href: '/guides/getting-started/quickstarts/kotlin',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from an Android Kotlin app.',
      icon: '/docs/img/icons/kotlin-icon',
    },
    {
      title: 'SvelteKit',
      href: '/guides/getting-started/quickstarts/sveltekit',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a SvelteKit app.',
      icon: '/docs/img/icons/svelte-icon',
    },
    {
      title: 'SolidJS',
      href: '/guides/getting-started/quickstarts/solidjs',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a SolidJS app.',
      icon: '/docs/img/icons/solidjs-icon',
    },
    {
      title: 'Vue',
      href: '/guides/getting-started/quickstarts/vue',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a Vue app.',
      icon: '/docs/img/icons/vuejs-icon',
    },
    {
      title: 'refine',
      href: '/guides/getting-started/quickstarts/refine',
      description:
        'Learn how to create a Supabase project, add some sample data to your database, and query the data from a refine app.',
      icon: '/docs/img/icons/refine-icon',
    },
  ].map((item) => {
    return (
      <Link href={`${item.href}`} key={item.title} passHref className={'col-span-4'}>
        <GlassPanel
          title={item.title}
          span="col-span-6"
          background={false}
          icon={item.icon}
          hasLightIcon={item.hasLightIcon}
        >
          {item.description}
        </GlassPanel>
      </Link>
    )
  })}
</div>

### Web app demos

<div className="grid lg:grid-cols-12 gap-6 not-prose">
  {
  [
    {
      title: 'Next.js',
      href: '/guides/getting-started/tutorials/with-nextjs',
      description:
        'Learn how to build a user management app with Next.js and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/nextjs-icon',
      hasLightIcon: true
    },
    {
      title: 'React',
      href: '/guides/getting-started/tutorials/with-react',
      description:
        'Learn how to build a user management app with React and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/react-icon'
    },
    {
      title: 'Vue 3',
      href: '/guides/getting-started/tutorials/with-vue-3',
      description:
        'Learn how to build a user management app with Vue 3 and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/vuejs-icon'
    },
    {
      title: 'Nuxt 3',
      href: '/guides/getting-started/tutorials/with-nuxt-3',
      description:
        'Learn how to build a user management app with Nuxt 3 and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/nuxt-icon'
    },
    {
      title: 'Angular',
      href: '/guides/getting-started/tutorials/with-angular',
      description:
        'Learn how to build a user management app with Angular and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/angular-icon'
    },
    {
      title: 'RedwoodJS',
      href: '/guides/getting-started/tutorials/with-redwoodjs',
      description:
        'Learn how to build a user management app with RedwoodJS and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/redwood-icon'
    },
    {
      title: 'Svelte',
      href: '/guides/getting-started/tutorials/with-svelte',
      description:
        'Learn how to build a user management app with Svelte and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/svelte-icon'
    },
    {
      title: 'SvelteKit',
      href: '/guides/getting-started/tutorials/with-sveltekit',
      description:
        'Learn how to build a user management app with SvelteKit and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/svelte-icon'
    },
    {
      title: 'refine',
      href: '/guides/getting-started/tutorials/with-refine',
      description:
        'Learn how to build a user management app with refine and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/refine-icon'
    }
  ]
.map((item) => {
    return (
      <Link href={`${item.href}`} key={item.title} passHref className={'col-span-4'}>
        <GlassPanel
          title={item.title}
          span="col-span-6"
          background={false}
          icon={item.icon}
          hasLightIcon={item.hasLightIcon}
        >
          {item.description}
        </GlassPanel>
      </Link>
    )

})}

</div>

### Mobile tutorials

<div className="grid lg:grid-cols-12 gap-6 not-prose">
  {[
    {
      title: 'Flutter',
      href: '/guides/getting-started/tutorials/with-flutter',
      description:
        'Learn how to build a user management app with Flutter and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/flutter-icon'
    },
    {
      title: 'Expo React Native',
      href: '/guides/getting-started/tutorials/with-expo-react-native',
      description:
        'Learn how to build a user management app with Expo React Native and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/expo-icon',
      hasLightIcon: true
    },
    {
      title: 'Android Kotlin',
      href: '/guides/getting-started/tutorials/with-kotlin',
      description:
        'Learn how to build a product management app with Android and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/kotlin-icon'
    },
    {
      title: 'iOS Swift',
      href: '/guides/getting-started/tutorials/with-swift',
      description:
        'Learn how to build a user management app with iOS and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/swift-icon'
    },
    {
      title: 'Ionic React',
      href: '/guides/getting-started/tutorials/with-ionic-react',
      description:
        'Learn how to build a user management app with Ionic React and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/ionic-icon'
    },
    {
      title: 'Ionic Vue',
      href: '/guides/getting-started/tutorials/with-ionic-vue',
      description:
        'Learn how to build a user management app with Ionic Vue and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/ionic-icon'
    },
    {
      title: 'Ionic Angular',
      href: '/guides/getting-started/tutorials/with-ionic-angular',
      description:
        'Learn how to build a user management app with Ionic Angular and Supabase Database, Auth, and Storage functionality.',
      icon: '/docs/img/icons/ionic-icon'
    }
  ].map((item) => {
    return (
      <Link href={`${item.href}`} key={item.title} passHref className={'col-span-4'}>
        <GlassPanel
          title={item.title}
          span="col-span-6"
          background={false}
          icon={item.icon}
          hasLightIcon={item.hasLightIcon}
        >
          {item.description}
        </GlassPanel>
      </Link>
    )

})}

</div>
