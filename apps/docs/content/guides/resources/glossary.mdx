---
id: 'glossary'
title: 'Glossary'
description: 'Definitions for terminology and acronyms used in the Supabase documentation.'
---

Definitions for terminology and acronyms used in the Supabase documentation.

## Access token

An access token is a short-lived (usually no more than 1 hour) token that authorizes a client to access resources on a server. It comes in the form of a [JSON Web Token (JWT)](#json-web-token-jwt).

## Authentication

Authentication (often abbreviated `authn.`) is the process of verifying the identity of a user. Verification of the identity of a user can happen in multiple ways:

1. Asking users for something they know. For example: password, passphrase.
2. Checking that users have access to something they own. For example: an email address, a phone number, a hardware key, recovery codes.
3. Confirming that users have some biological features. For example: a fingerprint, a certain facial structure, an iris print.

## Authenticator app

An authenticator app generates time-based one-time passwords (TOTPs). These passwords are generated based off a long and difficult to guess secret string. The secret is initially passed to the application by scanning a QR code.

## Authorization

Authorization (often abbreviated `authz.`) is the process of verifying if a certain identity is allowed to access resources. Authorization often occurs by verifying an access token.

## Identity provider

An identity provider is software or service that allows third-party applications to identify users without the exchange of passwords. Social login and enterprise single-sign on won't be possible without identity providers.

Social login platforms typically use the OAuth protocol, while enterprise single-sign on is based on the OIDC or SAML protocols.

## JSON Web Token (JWT)

A [JSON Web Token](https://jwt.io/introduction) is a type of data structure, represented as a string, that usually contains identity and authorization information about a user. It encodes information about its lifetime and is signed with cryptographic key making it tamper resistant.

Access tokens are JWTs and by inspecting the information they contain you can allow or deny access to resources. Row level security policies are based on the information present in JWTs.

## JWT signing secret

JWTs issued by Supabase are signed using the HMAC-SHA256 algorithm. The secret key used in the signing is called the JWT signing secret. You should not share this secret with someone or some thing you don't trust, nor should you post it publicly. Anyone with access to the secret can create arbitrary JWTs.

## Multi-factor authentication (MFA or 2FA)

Multi-factor authentication is the process of authenticating a user's identity by using a combination of factors: something users know, something users have or something they are.

## Nonce

Nonce means number used once. In reality though, it is a unique and difficult to guess string used to either initialize a protocol or algorithm securely, or detect abuse in various forms of replay attacks.

## OAuth

OAuth is a protocol allowing third-party applications to request and receive authorization from their users. It is typically used to implement social login, and serves as a base for enterprise single-sign on in the OIDC protocol. Applications can request different levels of access, including basic user identification information such as name, email address, and user ID.

## OIDC

OIDC stands for OpenID Connect and is a protocol that enables single-sign on for enterprises. OIDC is based on modern web technologies such as OAuth and JSON Web Tokens. It is commonly used instead of the older SAML protocol.

## One-time password (OTP)

A one-time password is a short, randomly generated and difficult to guess password or code that is sent to a device (like a phone number) or generated by a device or application.

## Password hashing function

Password hashing functions are specially-designed algorithms that allow web servers to verify a password without storing it as-is. Unlike other difficult to guess strings generated from secure random number generators, passwords are picked by users and often are easy to guess by attackers. These algorithms slow down and make it very costly for attackers to guess passwords.

There are three generally accepted password hashing functions: Argon2, bcrypt and scrypt.

## Password strength

Password strength is a measurement of how difficult a password is to guess. Simple measurement includes calculating the number of possibilities given the types of characters used in the password. For example a password of only letters has fewer variations than ones with letters and digits. Better measurements include strategies such as looking for similarity to words, phrases or already known passwords.

## PKCE

Proof Key for Code Exchange is an extension to the OAuth protocol that enables secure exchange of refresh and access tokens between an application (web app, single-page app or mobile app) and the authorization server. It is used in places where the exchange of the refresh and access token may be intercepted by third parties such as other applications running in the operating system. This is a common problem on mobile devices where the operating system may hand out URLs to other applications. This can sometimes be also exploited in single-page apps too.

## Provider refresh token

A provider refresh token is a refresh token issued by a third-party identity provider which can be used to refresh the provider token returned.

## Provider tokens

A provider token is a long-lived token issued by a third-party identity provider. These are issued by social login services (e.g., Google, Twitter, Apple, Microsoft) and uniquely identify a user on those platforms.

## Refresh token

A refresh token is a long-lived (in most cases with an indefinite lifetime) token that is meant to be stored and exchanged for a new refresh and access tokens only once. Once a refresh token is exchanged it becomes invalid, and can't be exchanged again. In practice, though, a refresh token can be exchanged multiple times but in a short time window.

## Refresh token flow

The refresh token flow is a mechanism that issues a new refresh and access token on the basis of a valid refresh token. It is used to extend authorization access for an application. An application that is being constantly used will invoke the refresh token flow just before the access token expires.

## Replay attack

A replay attack is when sensitive information is stolen or intercepted by attackers who then attempt to use it again (thus replay) in an effort to compromise a system. Commonly replay attacks can be mitigated with the proper use of nonces.

## Row level security policies (RLS)

Row level security policies are special objects within the Postgres database that limit the available operations or data returned to clients. RLS policies use information contained in a JWT to identify users and the actions and data they are allowed to perform or view.

## SAML

SAML stands for Security Assertion Markup Language and is a protocol that enables single-sign on for enterprises. SAML was invented in the early 2000s and is based on XML technology. It is the de facto standard for enabling single-sign on for enterprises, although the more recent OIDC (OpenID Connect) protocol is gaining popularity.

## Session

A session or authentication session is the concept that binds a verified user identity to a web browser. A session usually is long-lived, and can be terminated by the user logging out. An access and refresh token pair represent a session in the browser, and they are stored in local storage or as cookies.

## Single-sign on (SSO)

Single-sign on allows enterprises to centrally manage accounts and access to applications. They use identity provider software or services to organize employee information in directories and connect those accounts with applications via OIDC or SAML protocols.

## Time-based one-time password (TOTP)

A time-based one-time password is a one-time password generated at regular time intervals from a secret, usually from an application in a mobile device (e.g., Google Authenticator, 1Password).
