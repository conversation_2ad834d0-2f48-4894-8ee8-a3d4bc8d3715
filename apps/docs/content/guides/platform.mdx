---
id: 'platform'
title: 'Supabase Platform'
description: 'Getting started with the Supabase Platform.'
sidebar_label: 'Overview'
---

Supabase is a hosted platform which makes it very simple to get started without needing to manage any infrastructure.

Visit [supabase.com/dashboard](https://supabase.com/dashboard) and sign in to start creating projects.

## Projects

Each project on Supabase comes with:

- A dedicated [Postgres database](/docs/guides/database)
- [Auto-generated APIs](/docs/guides/database/api)
- [Auth and user management](/docs/guides/auth)
- [Edge Functions](/docs/guides/functions)
- [Realtime API](/docs/guides/realtime)
- [Storage](/docs/guides/storage)

## Organizations

Organizations are a way to group your projects. Each organization can be configured with different team members and billing settings.
Refer to [access control](/docs/guides/platform/access-control) for more information on how to manage team members within an organization.

## Platform status

If Supabase experiences outages, we keep you as informed as possible, as early as possible. We provide the following feedback channels:

- Status page: [status.supabase.com](https://status.supabase.com/)
- RSS Feed: [status.supabase.com/history.rss](https://status.supabase.com/history.rss)
- Atom Feed: [status.supabase.com/history.atom](https://status.supabase.com/history.atom)
- Slack Alerts: You can receive updates via the RSS feed, using Slack's [built-in RSS functionality](https://slack.com/help/articles/*********-Add-RSS-feeds-to-Slack) <br />`/feed subscribe https://status.supabase.com/history.atom`

Make sure to review our [SLA](/docs/company/sla) for details on our commitment to Platform Stability.
