---
id: 'ai'
title: 'AI & Vectors'
description: 'The best vector database is the database you already have.'
subtitle: 'The best vector database is the database you already have.'
hideToc: true
---

Supabase provides an open source toolkit for developing AI applications using Postgres and pgvector. Use the Supabase client libraries to store, index, and query your vector embeddings at scale.

The toolkit includes:

- A [vector store](/docs/guides/ai/vector-columns) and embeddings support using Postgres and pgvector.
- A [Python client](/docs/guides/ai/vecs-python-client) for managing unstructured embeddings.
- An [embedding generation](/docs/guides/ai/quickstarts/generate-text-embeddings) process using open source models directly in Edge Functions.
- [Database migrations](/docs/guides/ai/examples/headless-vector-search#prepare-your-database) for managing structured embeddings.
- Integrations with all popular AI providers, such as [OpenAI](/docs/guides/ai/examples/openai), [Hugging Face](/docs/guides/ai/hugging-face), [<PERSON><PERSON><PERSON><PERSON>](/docs/guides/ai/langchain), and more.

## Search

You can use Supabase to build different types of search features for your app, including:

- [Semantic search](/docs/guides/ai/semantic-search): search by meaning rather than exact keywords
- [Keyword search](/docs/guides/ai/keyword-search): search by words or phrases
- [Hybrid search](/docs/guides/ai/hybrid-search): combine semantic search with keyword search

## Examples

Check out all of the AI [templates and examples](https://github.com/supabase/supabase/tree/master/examples/ai) in our GitHub repository.

{/* <!-- vale off --> */}

<div className="grid md:grid-cols-12 gap-4 not-prose">
  {examples.map((x) => (
    <div className="col-span-4" key={x.href}>
      <Link href={x.href} passHref>
        <GlassPanel icon={'/docs/img/icons/github-icon'} hasLightIcon={true} title={x.name}>
          {x.description}
        </GlassPanel>
      </Link>
    </div>
  ))}
</div>

export const examples = [
  {
    name: 'Headless Vector Search',
    description: 'A toolkit to perform vector similarity search on your knowledge base embeddings.',
    href: '/guides/ai/examples/headless-vector-search',
  },
  {
    name: 'Image Search with OpenAI CLIP',
    description: 'Implement image search with the OpenAI CLIP Model and Supabase Vector.',
    href: '/guides/ai/examples/image-search-openai-clip',
  },
  {
    name: 'Hugging Face inference',
    description: 'Generate image captions using Hugging Face.',
    href: '/guides/ai/examples/huggingface-image-captioning',
  },
  {
    name: 'OpenAI completions',
    description: 'Generate GPT text completions using OpenAI in Edge Functions.',
    href: '/guides/ai/examples/openai',
  },
  {
    name: 'Building ChatGPT Plugins',
    description: 'Use Supabase as a Retrieval Store for your ChatGPT plugin.',
    href: '/guides/ai/examples/building-chatgpt-plugins',
  },
  {
    name: 'Vector search with Next.js and OpenAI',
    description:
      'Learn how to build a ChatGPT-style doc search powered by Next.js, OpenAI, and Supabase.',
    href: '/guides/ai/examples/nextjs-vector-search',
  },
]

{/* <!-- vale on --> */}

## Integrations

{/* <!-- vale off --> */}

<div className="grid md:grid-cols-12 gap-4 not-prose">
  {integrations.map((x) => (
    <div className="col-span-4" key={x.href}>
      <Link href={x.href} passHref>
        <GlassPanel title={x.name}>{x.description}</GlassPanel>
      </Link>
    </div>
  ))}
</div>

export const integrations = [
  {
    name: 'OpenAI',
    description:
      'OpenAI is an AI research and deployment company. Supabase provides a simple way to use OpenAI in your applications.',
    href: '/guides/ai/examples/building-chatgpt-plugins',
  },
  {
    name: 'Amazon Bedrock',
    description:
      'A fully managed service that offers a choice of high-performing foundation models from leading AI companies.',
    href: '/guides/ai/integrations/amazon-bedrock',
  },
  {
    name: 'Hugging Face',
    description:
      "Hugging Face is an open-source provider of NLP technologies. Supabase provides a simple way to use Hugging Face's models in your applications.",
    href: '/guides/ai/hugging-face',
  },
  {
    name: 'LangChain',
    description:
      'LangChain is a language-agnostic, open-source, and self-hosted API for text translation, summarization, and sentiment analysis.',
    href: '/guides/ai/langchain',
  },
  {
    name: 'LlamaIndex',
    description: 'LlamaIndex is a data framework for your LLM applications.',
    href: '/guides/ai/integrations/llamaindex',
  },
]

{/* <!-- vale on --> */}

## Case studies

{/* <!-- vale off --> */}

<div className="grid md:grid-cols-12 gap-4 not-prose">
  {[
    {
      name: 'Berri AI Boosts Productivity by Migrating from AWS RDS to Supabase with pgvector',
      description:
        'Learn how Berri AI overcame challenges with self-hosting their vector database on AWS RDS and successfully migrated to Supabase.',
      href: 'https://supabase.com/customers/berriai',
    },
    {
      name: 'Mendable switches from Pinecone to Supabase for PostgreSQL vector embeddings',
      description:
        'How Mendable boosts efficiency and accuracy of chat powered search for documentation using Supabase with pgvector',
      href: 'https://supabase.com/customers/mendableai',
    },
    {
      name: 'Markprompt: GDPR-Compliant AI Chatbots for Docs and Websites',
      description:
        "AI-powered chatbot platform, Markprompt, empowers developers to deliver efficient and GDPR-compliant prompt experiences on top of their content, by leveraging Supabase's secure and privacy-focused database and authentication solutions",
      href: 'https://supabase.com/customers/markprompt',
    },
  ].map((x) => (
    <div className="col-span-4" key={x.href}>
      <Link href={x.href} passHref>
        <GlassPanel title={x.name}>{x.description}</GlassPanel>
      </Link>
    </div>
  ))}
</div>

{/* <!-- vale on --> */}
