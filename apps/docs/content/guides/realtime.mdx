---
id: 'realtime'
title: 'Realtime'
description: 'Send and receive messages to connected clients.'
subtitle: 'Send and receive messages to connected clients.'
hideToc: true
---

Supabase provides a globally distributed [Realtime](https://github.com/supabase/realtime) service with the following features:

- [Broadcast](/docs/guides/realtime/broadcast): Send low-latency messages using the client libraries, REST, or your Database.
- [Presence](/docs/guides/realtime/presence): Track and synchronize shared state between users.
- [Postgres Changes](/docs/guides/realtime/postgres-changes): Listen to Database changes and send them to authorized users.

## Examples

<div className="grid md:grid-cols-12 gap-4 not-prose">
  {[
    {
      name: 'Multiplayer.dev',
      description: 'Mouse movements and chat messages.',
      href: 'https://multiplayer.dev',
    },
  ].map((x) => (
    <div className="col-span-12" key={x.href}>
      <Link href={x.href} passHref>
        <GlassPanel title={x.name}>{x.description}</GlassPanel>
      </Link>
    </div>
  ))}
</div>

## Resources

Find the source code and documentation in the Supabase GitHub repository.

<div className="grid md:grid-cols-12 gap-4 not-prose">
  {[
    {
      name: 'Supabase Realtime',
      description: 'View the source code.',
      href: 'https://github.com/supabase/realtime',
    },
    {
      name: 'Realtime: Multiplayer Edition',
      description: 'Read more about Supabase Realtime.',
      href: 'https://supabase.com/blog/supabase-realtime-multiplayer-general-availability',
    },
  ].map((x) => (
    <div className="col-span-6" key={x.href}>
      <Link href={x.href} passHref>
        <GlassPanel title={x.name}>{x.description}</GlassPanel>
      </Link>
    </div>
  ))}
</div>
