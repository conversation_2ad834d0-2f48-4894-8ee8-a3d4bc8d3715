---
title: 'Migrate from MySQL to Supabase'
description: 'Migrate your MySQL database to Supabase Postgres database.'
subtitle: 'Migrate your MySQL database to Supabase Postgres database.'
---

This guide aims to exhibit the process of transferring your MySQL database to Supabase's Postgres database. Supabase is a robust and open-source platform offering a wide range of backend features, including a Postgres database, authentication, instant APIs, edge functions, real-time subscriptions, and storage. Migrating your MySQL database to Supabase's Postgres enables you to leverage PostgreSQL's capabilities and access all the features you need for your project.

## Retrieve your MySQL database credentials

Before you begin the migration, you need to collect essential information about your MySQL database. Follow these steps:

1. Log in to your MySQL database provider.

1. Locate and note the following database details:
   - Hostname or IP address
   - Database name
   - Username
   - Password

## Retrieve your Supabase host [#retrieve-supabase-host]

1. If you're new to Supabase, [create a project](https://supabase.com/dashboard).
   Make a note of your password, you will need this later. If you forget it, you can [reset it here](https://supabase.com/dashboard/project/_/settings/database).

1. Go to the [Database settings](https://supabase.com/dashboard/project/_/settings/database) for your project in the Supabase Dashboard.
1. Under **Connection Info**, note your Host (`$SUPABASE_HOST`).

![Finding Supabase host address](/docs/img/guides/resources/migrating-to-supabase/mysql/database-settings-host.png)

## Migrate the database

The fastest way to migrate your database is with the Supabase migration tool on
[Google Colab](https://colab.research.google.com/github/mansueli/Supa-Migrate/blob/main/Amazon_RDS_to_Supabase.ipynb).

Alternatively, you can use [pgloader](https://github.com/dimitri/pgloader), a flexible and powerful data migration tool that supports a wide range of source database engines, including MySQL and MS SQL, and migrates the data to a Postgres database. For databases using the Postgres engine, we recommend using the [`pg_dump`](https://www.postgresql.org/docs/current/app-pgdump.html) and [psql](https://www.postgresql.org/docs/current/app-psql.html) command line tools, which are included in a full Postgres installation.

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="colab"
  queryGroup="migrate-method"
>
<TabPanel id="colab" label="Migrate using Colab">

1. Select the Database Engine from the Source database in the dropdown
1. Set the environment variables (`HOST`, `USER`, `SOURCE_DB`,`PASSWORD`, `SUPABASE_URL`, and `SUPABASE_PASSWORD`) in the Colab notebook.
1. Run the first two steps in [the notebook](https://colab.research.google.com/github/mansueli/Supa-Migrate/blob/main/Amazon_RDS_to_Supabase.ipynb) in order. The first sets engine and installs the necessary files.
1. Run the third step to start the migration. This will take a few minutes.

</TabPanel>
<TabPanel id="MySQL" label="Migrate from MySQL with pgloader">

1. Install pgloader.
2. Create a configuration file (e.g., config.load).

   For your destination, use your Supabase connection string with `Use connection pooling` enabled, and the mode set to `Session`. You can get the string from your [`Database Settings`](https://supabase.com/dashboard/project/_/settings/general).

   ```sql
   load database
     from mysql://user:password@host/source_db
     into postgres://postgres.xxxx:<EMAIL>:5432/postgres
   alter schema 'public' owner to 'postgres';
   set wal_buffers = '64MB', max_wal_senders = 0, statement_timeout = 0, work_mem to '2GB';
   ```

3. Run the migration with pgloader

   ```bash
   pgloader config.load
   ```

</TabPanel>
</Tabs>

<$Partial path="migration_warnings.mdx" />

## Enterprise

[Contact us](https://forms.supabase.com/enterprise) if you need more help migrating your project.
