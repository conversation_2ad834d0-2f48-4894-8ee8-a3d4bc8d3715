---
id: 'manage-usage-branching'
title: 'Manage Branching usage'
---

## What you are charged for

Each [Preview branch](/docs/guides/deployment/branching) is a separate environment with all Supabase services (Database, Auth, Storage, etc.). You're charged for usage within that environment—such as [Compute](/docs/guides/platform/manage-your-usage/compute), [Disk Size](/docs/guides/platform/manage-your-usage/disk-size), [Egress](/docs/guides/platform/manage-your-usage/egress), and [Storage](/docs/guides/platform/manage-your-usage/storage-size)—just like the project you branched from.

Usage by Preview branches counts toward your subscription plan's quota.

## How charges are calculated

Refer to individual [usage items](/docs/guides/platform/manage-your-usage) for details on how charges are calculated. Branching charges are the sum of all these items.

### Usage on your invoice

Compute incurred by Preview branches is shown as "Branching Compute Hours" on your invoice. Other usage items are not shown separately for branches and are rolled up into the project.

## Pricing

<$Partial path="billing/pricing/pricing_branching.mdx" />

## Billing examples

The project has a Preview branch "XYZ", that runs for 30 hours, incurring Compute and Egress costs. Disk Size usage remains within the 8 GB included in the subscription plan, so no additional charges apply.

| Line Item                      | Costs     |
| ------------------------------ | --------- |
| Pro Plan                       | $25       |
|                                |           |
| Compute Hours Small Project 1  | $15       |
| Egress Project 1               | $7        |
| Disk Size Project 1            | $3        |
|                                |           |
| Compute Hours Micro Branch XYZ | $0.4      |
| Egress Branch XYZ              | $1        |
| Disk Size Branch XYZ           | $0        |
|                                |           |
| **Subtotal**                   | **$51.4** |
| Compute Credits                | -$10      |
| **Total**                      | **$41.4** |

## View usage

You can view Branching usage on the [organization's usage page](https://supabase.com/dashboard/org/_/usage). The page shows the usage of all projects by default. To view the usage for a specific project, select it from the dropdown. You can also select a different time period.

<Image
  alt="Usage page navigation bar"
  src={{
    light: '/docs/img/guides/platform/usage-navbar--light.png',
    dark: '/docs/img/guides/platform/usage-navbar--dark.png',
  }}
  zoomable
/>

In the Usage Summary section, you can see how many hours your Preview branches existed during the selected time period. Hover over "Branches Compute Hours" for a detailed breakdown.

<Image
  alt="Usage summary Branches Compute Hours"
  src={{
    light: '/docs/img/guides/platform/usage-summary-branch-hours--light.png',
    dark: '/docs/img/guides/platform/usage-summary-branch-hours--dark.png',
  }}
  zoomable
/>

## Optimize usage

- Merge Preview branches as soon as they are ready
- Delete Preview branches that are no longer in use
- Check whether your [persistent branches](/docs/guides/deployment/branching#persistent-branches) need to be defined as persistent, or if they can be ephemeral instead. Persistent branches will remain active even after the underlying PR is closed.

## FAQ

### Do Compute Credits apply to Branching Compute?

No, Compute Credits do not apply to Branching Compute.
