---
title: Migrating within Supabase
subtitle: Learn how to migrate from one Supabase project to another
---

If you are on a Paid Plan and have physical backups enabled, you should instead use the [Restore
to another project feature](/docs/guides/platform/backups#restore-to-a-new-project)

## Database migration guides

If you need to migrate from one Supabase project to another, choose the appropriate guide below:

### Backup file from the dashboard (\*.backup)

Follow the [Restore dashboard backup guide](/docs/guides/platform/migrating-within-supabase/dashboard-restore)

### SQL backup files (\*.sql)

Follow the [Backup and Restore using the CLI guide](/docs/guides/platform/migrating-within-supabase/backup-restore)

## Transfer project to a different organization

Project migration is primarily for changing regions or upgrading to new major versions of the platform in some scenarios. If you need to move your project to a different organization without touching the infrastructure, see [project transfers](/docs/guides/platform/project-transfer).
