---
title: 'Enable SSO for Your Organization'
description: 'General information about enabling single sign-on (SSO) for your organization'
---

<Admonition type="tip">

Looking for docs on how to add Single Sign-On support in your Supabase project? Head on over to [Single Sign-On with SAML 2.0 for Projects](/docs/guides/auth/enterprise-sso/auth-sso-saml).

</Admonition>

Supabase offers single sign-on (SSO) as a login option to provide additional account security for your team. This allows company administrators to enforce the use of an identity provider when logging into Supabase. SSO improves the onboarding and offboarding experience of the company as the employee only needs a single set of credentials to access third-party applications or tools which can also be revoked by an administrator.

<Admonition type="note">

Supabase currently provides SAML SSO for [Team and Enterprise Plan customers](https://supabase.com/pricing). If you are an existing Team or Enterprise Plan customer, continue with the setup below. Once completed, [contact us](https://supabase.com/dashboard/support/new?category=Login_issues&subject=Enquiry%20about%20setting%20up%20SSO&message=I%20would%20like%20to%20set%20up%20SAML%20SSO%20for%20my%20team%20and%20have%20followed%20https://supabase.com/docs/guides/platform/sso%20and%20configured%20my%20provider%20%0A%0APlease%20attach%20the%20IDP%20metadata%20in%20the%20attachments%20below) to enable SSO for your team.

</Admonition>

## Setup and limitations

Supabase supports practically all identity providers that support the SAML 2.0 SSO protocol. We've prepared these guides for commonly used identity providers to help you get started. If you use a different provider, our support stands ready to support you.

- [Google Workspaces (formerly G Suite)](/docs/guides/platform/sso/gsuite)
- [Azure Active Directory](/docs/guides/platform/sso/azure)
- [Okta](/docs/guides/platform/sso/okta)

Accounts signing in with SSO have certain limitations.
The following sections outline the limitations when SSO is enabled or disabled for your team.

### Enable SSO for your team [#enable-sso]

- Organization invites are restricted to company members belonging to the same identity provider.
- Every user has an organization created by default. They can create as many projects as they want.
- An SSO user will not be able to update or reset their password since the company administrator manages their access via the identity provider.
- If an SSO user with the following email of `<EMAIL>` attempts to sign in with a GitHub account that uses the same email, a separate Supabase account is created and will not be linked to the SSO user's account.
- An SSO user will not be able to see all organizations/projects created under the same identity provider. They will need to be invited to the Supabase organization first. Refer to [access control](/docs/guides/platform/access-control) for more information.

### Disable SSO for your team [#disable-sso]

- You can prevent a user's account from further access to Supabase by removing or disabling their account in your identity provider.
- You should also remove or downgrade their permissions from any organizations inside Supabase.
