---
title: 'Set Up SSO with Azure AD'
description: 'Configure single sign-on with Azure AD (Microsoft Entra).'
---

<Admonition type="note">

This feature is only available on the [Team and Enterprise Plans](https://supabase.com/pricing). If you are an existing Team or Enterprise Plan customer, continue with the setup below. Once completed, [contact us](https://supabase.com/dashboard/support/new?category=Login_issues&subject=Enquiry%20about%20setting%20up%20SSO&message=I%20would%20like%20to%20set%20up%20SAML%20SSO%20for%20my%20team%20and%20have%20followed%20https://supabase.com/docs/guides/platform/sso%20and%20configured%20my%20provider%20%0A%0APlease%20attach%20the%20IDP%20metadata%20in%20the%20attachments%20below) to enable SSO for your team.

</Admonition>

<Admonition type="tip">

Looking for docs on how to add Single Sign-On support in your Supabase project? Head on over to [Single Sign-On with SAML 2.0 for Projects](/docs/guides/auth/enterprise-sso/auth-sso-saml).

</Admonition>

Supabase supports single sign-on (SSO) using Microsoft Azure AD.

## Step 1: Add and register an Enterprise application [#add-and-register-enterprise-application]

Open up the [Azure Active Directory](https://portal.azure.com/#view/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/~/Overview) dashboard for your Azure account.

Click the _Add_ button then _Enterprise application_.

![Azure AD console: Default Directory Overview](/docs/img/sso-azure-step-01.png)

## Step 2: Choose to create your own application [#create-application]

You'll be using the custom enterprise application setup for Supabase.

![Azure AD console: Browse Azure AD Gallery, select: Create your own application](/docs/img/sso-azure-step-02.png)

## Step 3: Fill in application details [#add-application-details]

In the modal titled _Create your own application_, enter a display name for Supabase. This is the name your Azure AD users see when signing in to Supabase from Azure. `Supabase` works in most cases.

Make sure to choose the third option: _Integrate any other application you
don't find in the gallery (Non-gallery)_.

![Azure AD console: Create your own application modal](/docs/img/sso-azure-step-03.png)

## Step 4: Set up single sign-on [#set-up-single-sign-on]

Before you get to assigning users and groups, which would allow accounts in Azure AD to access Supabase, you need to configure the SAML details that allows Supabase to accept sign in requests from Azure AD.

![Azure AD console: Supabase custom enterprise application, selected Set up single sign-on](/docs/img/sso-azure-step-04.png)

## Step 5: Select SAML single sign-on method [#saml-sso]

Supabase only supports the SAML 2.0 protocol for Single Sign-On, which is an industry standard.

![Azure AD console: Supabase application, Single sign-on configuration screen, selected SAML](/docs/img/sso-azure-step-05.png)

## Step 6: Upload SAML-based sign-on metadata file [#upload-saml-metadata]

First you need to download Supabase's SAML metadata file. Click the button below to initiate a download of the file.

    <a href="https://alt.supabase.io/auth/v1/sso/saml/metadata?download=true">
      <Button size="large" icon={<IconArrowDown />}>
        Download Supabase SAML Metadata File
      </Button>
    </a>

Alternatively, visit this page to initiate a download: `https://alt.supabase.io/auth/v1/sso/saml/metadata?download=true`

Click on the _Upload metadata file_ option in the toolbar and select the file you just downloaded.

![Azure AD console: Supabase application, SAML-based Sign-on screen, selected Upload metadata file button](/docs/img/sso-azure-step-06-1.png)

All of the correct information should automatically populate the _Basic SAML Configuration_ screen as shown.

![Azure AD console: Supabase application, SAML-based Sign-on screen, Basic SAML Configuration shown](/docs/img/sso-azure-step-06-2.png)

**Make sure you input these additional settings.**

| Setting     | Value                                        |
| ----------- | -------------------------------------------- |
| Sign on URL | `https://supabase.com/dashboard/sign-in-sso` |
| Relay State | `https://supabase.com/dashboard`             |

Finally, click the _Save_ button to save the configuration.

## Step 7: Obtain metadata URL and send to Supabase [#send-metadata-url]

Supabase needs to finalize enabling single sign-on with your Azure AD application. To do this, copy and send the link under **App Federation Metadata URL** in \*section 3 **SAML Certificates\*** to your support contact and await further instructions. If you're not clear who to send this link to or need further assistance, reach out to [Supabase Support](https://supabase.help).

**Do not test the login until you have heard back from the support contact.**

![Azure AD console: Supabase application, SAML Certificates card shown, App Federation Metadata Url highlighted](/docs/img/sso-azure-step-07.png)

## Step 8: Wait for confirmation [#confirmation]

Wait for confirmation or further instructions from your support contact at Supabase before proceeding to the next step. It usually takes us 1 business day to configure SSO for you.

## Step 9: Test single sign-on [#testing]

_Testing sign-on before your Azure AD has been registered with Supabase will not work. Make sure you've received confirmation from your support contact at Supabase as laid out in the [confirmation](#confirmation) step._

Once you’ve received confirmation from your support contact at Supabase that SSO setup has been completed for your enterprise, you can ask some of your users to sign in via their Azure AD account.

You ask them to enter their email address on the [Sign in with SSO](https://supabase.com/dashboard/sign-in-sso) page.

If sign in is not working correctly, reach out to your support contact at Supabase for further guidance.
