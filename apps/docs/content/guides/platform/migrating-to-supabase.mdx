---
title: Migrating to Supabase
---

Learn how to migrate to Supabase from another database service.

## Migration guides

<NavData data="migrationPages">
  {(migrationPages) => (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-6 mb-6 not-prose">
      {migrationPages.map((page) => (
        <Link href={`${page.url}`} key={page.url} passHref>
          <GlassPanel
            icon={page.icon}
            title={page.name}
            hasLightIcon={page.hasLightIcon}
            background={false}
            className="[&>div]:p-4"
          />
        </Link>
      ))}
    </div>
  )}
</NavData>
