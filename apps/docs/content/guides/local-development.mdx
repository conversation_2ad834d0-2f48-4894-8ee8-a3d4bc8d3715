---
title: Local Development & CLI
subtitle: Learn how to develop locally and use the Supabase CLI
---

Develo<PERSON> locally while running the Supabase stack on your machine.

## Quickstart

1.  Install the Supabase CLI:

    <Tabs scrollable size="small" type="underlined" defaultActiveId="npm" queryGroup="package-manager"><TabPanel id="npm" label="npm">

    ```sh
    npm install supabase --save-dev
    ```

    </TabPanel><TabPanel id="yarn" label="yarn">

    ```sh
    yarn add supabase --dev
    ```

    </TabPanel><TabPanel id="pnpm" label="pnpm">

    ```sh
    pnpm add supabase --save-dev
    ```

    </TabPanel><TabPanel id="brew" label="brew">

    ```sh
    brew install supabase/tap/supabase
    ```

    </TabPanel></Tabs>

2.  In your repo, initialize the Supabase project:

    <Tabs scrollable size="small" type="underlined" defaultActiveId="npm" queryGroup="package-manager"><TabPanel id="npm" label="npm">

    ```sh
    npx supabase init
    ```

    </TabPanel><TabPanel id="yarn" label="yarn">

    ```sh
    yarn supabase init
    ```

    </TabPanel><TabPanel id="pnpm" label="pnpm">

    ```sh
    pnpm supabase init
    ```

    </TabPanel><TabPanel id="brew" label="brew">

    ```sh
    supabase init
    ```

    </TabPanel>

    </Tabs>

3.  Start the Supabase stack:

    <Tabs scrollable size="small" type="underlined" defaultActiveId="npm" queryGroup="package-manager"><TabPanel id="npm" label="npm">

    ```sh
    npx supabase start
    ```

    </TabPanel><TabPanel id="yarn" label="yarn">

    ```sh
    yarn supabase start
    ```

    </TabPanel><TabPanel id="pnpm" label="pnpm">

    ```sh
    pnpm supabase start
    ```

    </TabPanel><TabPanel id="brew" label="brew">

    ```sh
    supabase start
    ```

    </TabPanel>

    </Tabs>

4.  View your local Supabase instance at [http://localhost:54323](http://localhost:54323).

## Local development

Local development with Supabase allows you to work on your projects in a self-contained environment on your local machine. Working locally has several advantages:

1. Faster development: You can make changes and see results instantly without waiting for remote deployments.
2. Offline work: You can continue development even without an internet connection.
3. Cost-effective: Local development is free and doesn't consume your project's quota.
4. Enhanced privacy: Sensitive data remains on your local machine during development.
5. Easy testing: You can experiment with different configurations and features without affecting your production environment.

To get started with local development, you'll need to install the [Supabase CLI](#cli) and Docker. The Supabase CLI allows you to start and manage your local Supabase stack, while Docker is used to run the necessary services.

Once set up, you can initialize a new Supabase project, start the local stack, and begin developing your application using local Supabase services. This includes access to a local Postgres database, Auth, Storage, and other Supabase features.

## CLI

The Supabase CLI is a powerful tool that enables developers to manage their Supabase projects directly from the terminal. It provides a suite of commands for various tasks, including:

- Setting up and managing local development environments
- Generating TypeScript types for your database schema
- Handling database migrations
- Managing environment variables and secrets
- Deploying your project to the Supabase platform

With the CLI, you can streamline your development workflow, automate repetitive tasks, and maintain consistency across different environments. It's an essential tool for both local development and CI/CD pipelines.

See the [CLI Getting Started guide](/docs/guides/local-development/cli/getting-started) for more information.
