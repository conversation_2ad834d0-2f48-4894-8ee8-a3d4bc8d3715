| Endpoint                                         | Path                                                           | Limited By               | Rate Limit                                                                                                                                                                                                                                                     |
| ------------------------------------------------ | -------------------------------------------------------------- | ------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| All endpoints that send emails                   | `/auth/v1/signup` `/auth/v1/recover` `/auth/v1/user`[^1]       | Sum of combined requests | Defaults to 4 emails per hour as of 14th July 2023. As of 21 Oct 2023, this has been updated to <SharedData data="config">auth.rate_limits.email.inbuilt_smtp_per_hour</SharedData> emails per hour. You can only change this with your own custom SMTP setup. |
| All endpoints that send One-Time-Passwords (OTP) | `/auth/v1/otp`                                                 | Sum of combined requests | Defaults to <SharedData data="config">auth.rate_limits.otp.requests_per_hour</SharedData> OTPs per hour. Is customizable.                                                                                                                                      |
| Send OTPs or magic links                         | `/auth/v1/otp`                                                 | Last request             | Defaults to <SharedData data="config">auth.rate_limits.otp.period</SharedData> window before a new request is allowed. Is customizable.                                                                                                                        |
| Signup confirmation request                      | `/auth/v1/signup`                                              | Last request             | Defaults to <SharedData data="config">auth.rate_limits.signup_confirmation.period</SharedData> window before a new request is allowed. Is customizable.                                                                                                        |
| Password Reset Request                           | `/auth/v1/recover`                                             | Last request             | Defaults to <SharedData data="config">auth.rate_limits.password_reset.period</SharedData> window before a new request is allowed. Is customizable.                                                                                                             |
| Verification requests                            | `/auth/v1/verify`                                              | IP Address               | <SharedData data="config">auth.rate_limits.verification.requests_per_hour</SharedData> requests per hour (with bursts up to <SharedData data="config">auth.rate_limits.verification.requests_burst</SharedData> requests)                                      |
| Token refresh requests                           | `/auth/v1/token`                                               | IP Address               | <SharedData data="config">auth.rate_limits.token_refresh.requests_per_hour</SharedData> requests per hour (with bursts up to <SharedData data="config">auth.rate_limits.token_refresh.requests_burst</SharedData> requests)                                    |
| Create or Verify an MFA challenge                | `/auth/v1/factors/:id/challenge` `/auth/v1/factors/:id/verify` | IP Address               | <SharedData data="config">auth.rate_limits.mfa.requests_per_hour</SharedData> requests per hour (with bursts up to <SharedData data="config">auth.rate_limits.verification.mfa</SharedData> requests)                                                          |
| Anonymous sign-ins                               | `/auth/v1/signup`[^2]                                          | IP Address               | <SharedData data="config">auth.rate_limits.anonymous_signin.requests_per_hour</SharedData> requests per hour (with bursts up to <SharedData data="config">auth.rate_limits.anonymous_signin.requests_burst</SharedData> requests)                              |

[^1]: The rate limit is only applied on `/auth/v1/user` if this endpoint is called to update the user's email address.
[^2]: The rate limit is only applied on `/auth/v1/signup` if this endpoint is called without passing in an email or phone number in the request body.
