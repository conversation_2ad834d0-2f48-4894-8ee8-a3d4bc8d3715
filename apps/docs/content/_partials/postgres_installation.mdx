<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="windows"
>

<TabPanel id="windows" label="Windows">

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>

    <StepHikeCompact.Details title="Install Postgres" fullWidth>

    Download and run the installation file for the latest version from the [Postgres installer download page](https://www.postgresql.org/download/windows/).

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Add Postgres to your system PATH" fullWidth>

    Add the Postgres binary to your system PATH.

    In Control Panel, under the Advanced tab of System Properties, click Environment Variables. Edit the Path variable by adding the path the SQL binary you just installed.

    The path will look something like this, though it may differ slightly depending on your installed version:

    ```
    C:\Program Files\PostgreSQL\17\bin
    ```

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>

    <StepHikeCompact.Details title="Verify that psql is working" fullWidth>

    Open your terminal and run the following command:

    ```sh
    psql --version
    ```

    <Admonition type="tip">

    If you get an error that psql is not available or cannot be found, check that you have correctly added the binary to your system PATH. Also try restarting your terminal.

    </Admonition>

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

</StepHikeCompact>

</TabPanel>

<TabPanel id="mac" label="MacOS">

<StepHikeCompact>

  <StepHikeCompact.Step step={1}>

    <StepHikeCompact.Details title="Install Homebrew" fullWidth>

    Install [Homebrew](https://brew.sh/).

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={2}>

    <StepHikeCompact.Details title="Install Postgres" fullWidth>

    Install Postgres via Homebrew by running the following command in your terminal:

    ```sh
    brew install postgresql@17
    ```

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

  <StepHikeCompact.Step step={3}>

    <StepHikeCompact.Details title="Verify that psql is working" fullWidth>

    Restart your terminal and run the following command:

    ```sh
    psql --version
    ```

    If you get an error that psql is not available or cannot be found then the PATH variable is likely either not correctly set or you need to restart your terminal.

    You can add the Postgres installation path to your PATH variable by running the following command:

    ```sh
    brew info postgresql@17
    ```

    The above command will give an output like this:

    ```sh
    If you need to have postgresql@17 first in your PATH, run:

    echo 'export PATH="/opt/homebrew/opt/postgresql@17/bin:$PATH"' >> ~/.zshrc
    ```

    Run the command mentioned and restart the terminal.

    </StepHikeCompact.Details>

  </StepHikeCompact.Step>

</StepHikeCompact>

</TabPanel>

</Tabs>
