---
title = "Why am I being redirected to the wrong url when using auth redirectTo option?"
github_url = "https://github.com/orgs/supabase/discussions/12946"
date_created = "2023-03-10T17:15:10+00:00"
topics = [ "auth" ]
database_id = "d4b45e90-9c52-4781-8cc2-d936e8f4edeb"
---

Why are redirects going to localhost instead of the production site URL?
In order for the provided `redirectTo` option to work you must set the exact URL in the shown Redirect URL's setting.

![image](/docs/img/troubleshooting/224379580-fa77bd31-bb58-47e6-90ce-64e140f32579.png)

For more information on formats for redirect URL settings see the documentation here: https://supabase.com/docs/guides/auth/overview#redirect-urls-and-wildcards
