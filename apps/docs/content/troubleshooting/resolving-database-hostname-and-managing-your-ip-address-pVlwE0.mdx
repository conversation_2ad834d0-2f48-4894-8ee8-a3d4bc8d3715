---
title = "Resolving database hostname and managing your IP address"
github_url = "https://github.com/orgs/supabase/discussions/22400"
date_created = "2024-04-02T09:01:54+00:00"
topics = [ "database" ]
keywords = [ "hostname", "ip", "ipv4", "ipv6" ]
database_id = "c89147f8-a66a-4c04-a1a7-45442fd7f2ee"
---

### Finding your database hostname

Your database's hostname is crucial for establishing a direct connection. It resolves to the underlying IP address of your database. To find your hostname, navigate to your [Database Settings](https://supabase.com/dashboard/project/_/settings/database). It's important to note that the pooler (connection pooler) has a different IP than your database. Therefore, to reveal the database host and direct connection string, you must hide the pooler connection string.

Example Hostname: `db.zcjtzmeifsoteyjytnbc.supabase.co`

![Screenshot 2024-04-02 at 11 38 28 AM](/docs/img/troubleshooting/565c96e0-cbb9-4e7a-b0aa-5b423afc0ada.png)

### Managing your IP address

To determine your current IP address, you can use an [IP address lookup](https://whatismyipaddress.com/hostname-ip) website or the terminal command:

- Type `nslookup hostname` and press Enter.
- This command queries the domain name servers to find the IP address of the given hostname.

Example IPv6 Address: `2a05:d014:1c06:5f0c:d7a9:8616:bee2:30df`

### IPv6 address

Upon project creation, a static IPv6 address is assigned. However, it's essential to understand that this IPv6 address can change due to specific actions:

- When a project is paused or resumed.
- During database version upgrades.

### IPv4 address

Opting for the static [IPv4 add-on](https://supabase.com/docs/guides/platform/ipv4-address) provides a more stable connection address. The IPv4 address remains constant unless:

- The project is paused or resumed.
- Unlike the IPv6 address, upgrading your database does not affect the IPv4 address.
