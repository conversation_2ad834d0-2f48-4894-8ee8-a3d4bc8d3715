---
title = "High RAM usage"
topics = [ "platform" ]
keywords = [ "memory", "RAM", "performance" ]
database_id = "a589d411-ee50-455a-892e-526914ddb40d"
---

<Admonition type="note">

High memory usage doesn't necessarily mean that your instance is at risk. Memory that is used for caching and buffers improves data access speed. But if you notice less performance alongside high memory usage, your memory usage might be unhealthy.

</Admonition>

## Base memory usage

You may observe elevated memory usage even when your database has little to no load. Supabase requires a wide range of services other than Postgres to operate, which can result in an elevated base memory usage. Especially on the smallest compute instance that comes with 1 GB of RAM, it is not unusual for your project to have a base memory usage of ~50%.

## Issues with high memory usage

Every Supabase project runs in its own dedicated virtual machine. Your instance will have a different set of hardware provisioned depending on your [compute add-on](https://supabase.com/docs/guides/platform/compute-add-ons). Depending on your workload, your compute hardware may not be suitable and can result in high RAM usage.

A good proxy for unhealthy memory usage is swap usage. If you run out of RAM, your system will offload memory to your disk's much slower swap partition. If your swap is above 70%, chances are high that your compute hardware is not suitable for your workload. Head over to your project's [Database Health](https://supabase.com/dashboard/project/_/reports/database) to see your swap usage.

High RAM usage could come with a range of issues:

- degraded performance overall when your instance has to use swap memory
- the operating system may start killing processes as your system runs out of memory
- in rare cases, your instance may become unresponsive

## Monitor your RAM

To check your RAM usage on the Supabase Platform, head over to [Database Health in the Reports section](https://supabase.com/dashboard/project/_/reports/database).

It is also possible to monitor your resources and set up alerts using Prometheus/Grafana. With Grafana you will be able to see how much of your RAM is used for caching and you can track other metrics such as your Swap usage. Read the [Metrics Guide](https://supabase.com/docs/guides/platform/metrics) to learn more.

## Common reasons for high RAM usage

Everything you do with your Supabase project requires memory in some form. Hence, there can be many reasons for high RAM usage. Here are some common ones:

- **Query performance:** Queries that take a long time to complete (>1 second) could be using your RAM inefficiently. Check our guide on [examining query performance](https://supabase.com/docs/guides/platform/performance#examining-query-performance).
- **Too many connections:** Every connection to your database consumes memory. You can check the number of active connections under [Database Roles](https://supabase.com/dashboard/project/_/database/roles) after you select your project. Read our guide on [too many open connections](https://supabase.com/docs/guides/platform/troubleshooting#too-many-open-connections).
- **Extensions:** Some extensions such as `timescaledb` or `pg_cron` can use a lot of memory. It can also add up when you have too many extensions running. You can manage your database extensions in the dashboard under [Extensions](https://supabase.com/dashboard/project/_/database/extensions).

## How to fix your memory issues

1. **Upgrade your compute:** You can get a Compute Add-on for your project. See your [upgrade options](https://supabase.com/dashboard/project/_/settings/compute-and-disk) by selecting your project.
2. **Optimize performance:** Get more out of your instance's resources by optimizing your usage. Have a look at our [performance tuning guide](https://supabase.com/docs/guides/platform/performance#examining-query-performance) and our [production readiness guide](https://supabase.com/docs/guides/platform/going-into-prod#performance).
