---
title = "Fetch requests to API endpoints aren't showing the session"
github_url = "https://github.com/orgs/supabase/discussions/15859"
date_created = "2023-07-18T12:22:14+00:00"
topics = [ "auth" ]
keywords = [ "session", "cookie" ]
database_id = "6f19b113-09cb-45ce-8141-0d6dc10ea464"
---

You must pass along the cookie header with the fetch request in order for your API endpoint to get access to the cookie from this request.

```ts
const res = await fetch('http://localhost:3000/contact', {
  headers: {
    cookie: headers().get('cookie') as string,
  },
})
```
