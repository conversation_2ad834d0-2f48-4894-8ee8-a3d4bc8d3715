---
title = "error: no pg_hba.conf entry for host \"xx.xxx.xxx.xxx\", user \"postgres\", database \"postgres\", SSL off"
github_url = "https://github.com/orgs/supabase/discussions/21145"
date_created = "2024-02-09T11:48:08+00:00"
topics = [ "database", "platform" ]
keywords = [ "ssl", "authentication", "connection" ]
database_id = "0739256c-1b25-46ec-963b-2f8e4dccedc7"

[[errors]]
message = "no pg_hba.conf entry for host \"xx.xxx.xxx.xxx\", user \"postgres\", database \"postgres\", SSL off"
---

This error indicates a failed authentication attempt to the database and the connection couldn't be established.

In Supabase, this is generally seen when [SSL enforcement](https://supabase.com/docs/guides/platform/ssl-enforcement) is enabled on your Supabase Project. The authentication failed because the incoming connection didn't use SSL encryption when connecting to the database.

You can ignore this message if the attempt is from an unknown user. If you want this connection attempt to be successful, you will either need to connect with SSL or disable SSL enforcement on your Supabase project.
