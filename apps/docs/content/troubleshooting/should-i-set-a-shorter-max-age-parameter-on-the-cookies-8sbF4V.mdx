---
title = "Should I set a shorter Max-Age parameter on the cookies?"
github_url = "https://github.com/orgs/supabase/discussions/18870"
date_created = "2023-11-10T11:53:00+00:00"
topics = [ "auth" ]
keywords = [ "cookies", "max-age", "browser", "session" ]
database_id = "e51bedf0-cdc4-4880-865b-e39c06dfe497"
---

The `Max-Age` or `Expires` cookie parameters only control whether the browser sends the value to the server. Since a refresh token represents the long-lived authentication session of the user on that browser, setting a short `Max-Age` or `Expires` parameter on the cookies only results in a degraded user experience.

The only way to ensure that a user has logged out or their session has ended is to get the user's details with `getUser()`.
