---
title = "NEW variable is null in a trigger function."
github_url = "https://github.com/orgs/supabase/discussions/15934"
date_created = "2023-07-20T19:05:52+00:00"
topics = [ "database", "functions" ]
keywords = [ "trigger", "function", "statement", "row", "NEW", "OLD", "SQL" ]
database_id = "f5cabb97-aae3-4fb5-b597-4e7857821753"
---

When you create a function and call it from a trigger the NEW variable is only passed in if the trigger is on ROW operations

The UI currently defaults to STATEMENT which will not provide NEW or OLD variables to the function.

![image](/docs/img/troubleshooting/49e11154-296b-42b9-8036-8a7288e49b8a.png)

Also in SQL (reference: https://www.postgresql.org/docs/current/sql-createtrigger.html)

![image](/docs/img/troubleshooting/c7ac0f69-6fb9-4ff2-8aab-5cda6bada3f6.png)
