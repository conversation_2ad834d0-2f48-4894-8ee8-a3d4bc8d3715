---
title = "No toast messages on the Dashboard"
github_url = "https://github.com/orgs/supabase/discussions/27702"
date_created = "2024-07-01T19:45:53+00:00"
topics = [ "studio" ]
keywords = [ "notifications", "toasts" ]
database_id = "a1bb1131-dd11-4a76-8a70-98a666c08286"
---

This is a known issue that is on the roadmap to resolve. However, this discussion serves as a temporary workaround for affected users until then.

## What is the issue?

When executing operations in the Supabase dashboard, there are situations where you will not see a toast message in the top right corner to indicate the status of the operation. This leaves users confused about the operation's status and why it errored. They would then have to look in the browser console logs for further details.

Here is an example of a toast error message:

![Screenshot 2024-07-01 at 2 05 36 PM](/docs/img/troubleshooting/8ba54441-2057-4dc0-8add-a8eaab9f2d93.png)

Here is an example of a toast success message:

![Screenshot 2024-07-01 at 2 06 35 PM](/docs/img/troubleshooting/c6e228b8-fa64-47a9-bdf6-f1f6405b9cf9.png)

## What causes it?

If you are not seeing these error messages, there are a couple of known scenarios that might be blocking them:

1. **Brave Browser**: By default, Brave has strict ad-blocking settings that can block these messages.

2. **Chrome Browser**: Specifically, the ARM64 build (tested on v125.0.6422.142) with the new v3 manifest version of uBlock Origin Lite ([https://ublockorigin.com/](https://ublockorigin.com/)). If the setting is changed from basic to optimal, it can block these messages.

There are likely other causes that are not known right now as well.

## How to fix this issue?

**Adjust Ad-blocking Settings**: Turn down the ad-blocking settings or turn it off for the Supabase dashboard.

**Use a different Browser**: If the above is not feasible, using a different browser is an alternative.
