---
title = "Why can't I upload/list/etc my public bucket?"
github_url = "https://github.com/orgs/supabase/discussions/19180"
date_created = "2023-11-23T16:52:55+00:00"
topics = [ "storage" ]
database_id = "8abd4a38-041b-4620-95af-53673f698dea"
---

A public bucket in the storage API only means there is a public URL available you can use to download the file.
All other bucket or file operations require you to meet storage policies on that bucket.
Note also you should use the normal path for all other operations and not the public URL path with 'public' in it.
