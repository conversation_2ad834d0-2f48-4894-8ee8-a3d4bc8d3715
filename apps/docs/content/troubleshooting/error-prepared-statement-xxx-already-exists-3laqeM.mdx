---
title = "Error: prepared statement \"XXX\" already exists"
github_url = "https://github.com/orgs/supabase/discussions/17751"
date_created = "2023-09-27T09:53:08+00:00"
topics = [ "database" ]
keywords = [ "pgbouncer", "prepared", "connection" ]
database_id = "8688dad6-3dce-4912-9b90-4f128daf79d7"

[[errors]]
message = "prepared statement \"XXX\" already exists"
---

This error occurs when you are trying to connect to the database using PgBouncer. PgBouncer does not support prepared statements. If you have prepared statements in use, you will need to use direct connections - port 5432.

There is a special parameter in the query string for Prisma to work with PgBouncer
https://www.prisma.io/docs/guides/performance-and-optimization/connection-management/configure-pg-bouncer
