---
title = "Database Error: remaining connection slots are reserved for non-replication superuser connections"
github_url = "https://github.com/orgs/supabase/discussions/13995"
date_created = "2023-04-27T10:39:24+00:00"
topics = [ "database" ]
keywords = [ "connections", "connection slots", "reserved", "superuser" ]
database_id = "b3a73631-0b8d-460d-8033-28df88d26d75"

[[errors]]
message = "remaining connection slots are reserved for non-replication superuser connections"
---

This error usually occurs when the database reaches the maximum number of connections allowed based on the compute add-on.

To overcome this, the connections need to be optimized as mentioned here: https://supabase.com/docs/guides/platform/performance#optimizing-the-number-of-connections

Additionally, you can try using the connection pool to help solve this issue:
https://supabase.com/docs/guides/database/connecting-to-postgres#connection-pooler

If you're already using connection pooling and still hitting the maximum connections, then it is suggested to upgrade your compute add-on that allows more connections: https://supabase.com/docs/guides/platform/compute-add-ons
