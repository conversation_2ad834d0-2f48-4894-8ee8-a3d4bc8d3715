---
title = "Change email associated with Supabase account"
github_url = "https://github.com/orgs/supabase/discussions/21147"
date_created = "2024-02-09T11:58:06+00:00"
topics = [ "platform", "auth" ]
keywords = [ "email", "account", "authentication" ]
database_id = "6de3c3a9-8614-48d7-827e-cf41d6a1ce23"
---

Currently, there isn't a direct way to change the email address of an existing Supabase account.

If you are using Email Authentication to login into your Supabase account:

- Invite a new email address to your Supabase Organization(s) as an Organization owner.
- After signing up for a new account with your new email address and confirming you have access to the Organization(s) with your new account, you can leave the Organization(s) from your previous account.

If you are using GitHub Authentication to login into your Supabase account:

- Log out of Supabase.
- Change Primary Email in GitHub
- Log out of GitHub.
- Log back into GitHub (with the new, desired email set as primary)
- Log back into Supabase.
