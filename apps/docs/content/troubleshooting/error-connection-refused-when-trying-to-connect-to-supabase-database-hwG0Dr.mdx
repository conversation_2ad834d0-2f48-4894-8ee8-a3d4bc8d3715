---
title = "Error: \"Connection refused\" when trying to connect to Supabase database"
github_url = "https://github.com/orgs/supabase/discussions/14929"
date_created = "2023-06-09T10:38:06+00:00"
topics = [ "database", "cli" ]
keywords = [ "connection", "fail2ban", "econnrefused" ]
database_id = "9d8b179c-ff4d-4072-b9e6-2d16e63b2aaa"

[api]
cli = [ "supabase-network-bans-get", "supabase-network-bans-remove" ]

[[errors]]
message = "connect ECONNREFUSED *******:5432"

[[errors]]
message = "psql: error: connection to server at \"db.xxxxxxxxxxxxxxxxxxxx.supabase.co\" (*******), port 5432 failed: Connection refused Is the server running on that host and accepting TCP/IP connections?"
---

If you're not able to connect to the Supabase database and see the error `connect ECONNREFUSED *******:5432` or `psql: error: connection to server at "db.xxxxxxxxxxxxxxxxxxxx.supabase.co" (*******), port 5432 failed: Connection refused
Is the server running on that host and accepting TCP/IP connections?`, this could be because there are banned IPs on your project caused by Fail2ban as it kicks in when attempting 2 wrong passwords in a row.

These bans will clear after 30mins but you can unban the IPs using the Supabase CLI https://supabase.com/docs/guides/cli following the commands below.

How to list the banned IPs:

```
% supabase network-bans get --project-ref <project_reference_id> --experimental
```

How to unban the IPs:

```
% supabase network-bans remove --db-unban-ip <ip_address> --project-ref <project_reference_id> --experimental
```
