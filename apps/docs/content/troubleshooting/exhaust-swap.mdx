---
title = "High swap usage"
topics = [ "database", "platform" ]
keywords = [ "swap", "performance" ]
database_id = "2db43a09-792b-484b-b97d-5d96a405b525"
---

Learn what high Swap usage means, what can cause it, and how to solve it.

## What is swap for?

<Admonition type="tip">

High Swap is usually not a problem unless other resources (such as RAM) are constrained.

</Admonition>

Every Supabase project runs on its own dedicated virtual machine. The machine's underlying specs and hardware depend on your [compute add-on](https://supabase.com/docs/guides/platform/compute-add-ons). If your hardware isn't suitable for your workload, you might experience high Swap usage.

Swap is a portion of your instance's disk that is reserved for the operating system to use when the available RAM has been utilized. As it uses the disk, Swap is slower to access and is generally used as a last resort.

Swap can be used even if your instance has plenty of RAM. If this is the case, do not worry. Your instance might try to "preemptively swap" by swapping background processes to make space for your traffic in RAM.

### When is high swap concerning?

High Swap is concerning if your instance is using all of the available RAM (i.e. consistently using more than 75%).

High Swap usage can affect your database performance. For example, you might see:

- **Slower query responses.**
- **Degraded performance due to swapping regularly between RAM and disk.**
- **Higher Disk I/O due to swapping regularly.**

## Monitor your swap

You can check your Swap usage directly on the Supabase Platform. Navigate to the [**Database** page](https://supabase.com/dashboard/project/_/reports/database) of the **Reports** section.

You can also monitor your resources and set up alerts using Prometheus/Grafana. See the [metrics guide](https://supabase.com/docs/guides/platform/metrics) for more information.

An [example repository](https://github.com/supabase/supabase-grafana) to ingest metrics and visualize them with Grafana is provided in the linked guide, where we maintain a [list of the exported metrics](https://github.com/supabase/supabase-grafana/blob/main/docs/metrics.md).

Some useful metrics to monitor are (this is not an exhaustive list):

- `node_memory_SwapFree_bytes` - The total amount of Swap available in bytes.
- `node_disk_io_time_seconds_total` and `node_disk_io_now` - The amount of time spent on disk I/O. An increase might be an indirect sign of excessive swapping, but not always.
- `node_memory_MemTotal_bytes` and `node_memory_MemFree_bytes` - The total RAM and available RAM.
- `node_vmstat_pswpin` and `node_vmstat_pswpout` - The number of pages that have been swapped in or out (monitoring this for spikes means that your instance is swapping).

## Common reasons for high swap usage

Everything you do with your Supabase project requires compute. Hence, there can be many reasons for high Swap usage. Here are some common ones:

- **Query performance:** You might have high read traffic or queries that process a large amount of data on disk (this can happen even if you are returning a small amount of data).
- **Missing indexes:** Your database might have to scan through a large amount of data to find the information it needs. Creating indexes helps your database find data faster. See the [indexes guide](https://supabase.com/docs/guides/database/postgres/indexes) to learn more.
- **Unsuitable compute:** The compute size of your Supabase project might not be suitable for your application as you might have more traffic or run resource-intensive operations.
- **Workload style:** The usage pattern of your Supabase project might be more read heavy, or involve large amounts of data.
- **Extensions:** You might be using extensions that perform intensive operations on large datasets. This increases resource usage.

## Solving high swap usage

If you find that your RAM and Swap usage are high, you have three options:

1. **Optimize performance:** Get more out of your instance's resources by optimizing your usage. See the [performance tuning guide](https://supabase.com/docs/guides/platform/performance#examining-query-performance) and our [production readiness guide](https://supabase.com/docs/guides/platform/going-into-prod#performance).
2. **Upgrade your compute:** You can get a Compute Add-on for your project. Follow [this link](https://supabase.com/dashboard/project/_/settings/compute-and-disk) and select your project to see your upgrade options.
3. **Read Replicas:** You can spread the load on your Supabase project by creating a Read Replica. See [the read replicas guide](https://supabase.com/docs/guides/platform/read-replicas) for more information.
