---
title = 'Realtime "Concurrent Peak Connections" quota'
github_url = "https://github.com/orgs/supabase/discussions/19391"
date_created = "2023-12-03T17:05:20+00:00"
topics = ["realtime", "self-hosting"]
keywords = ["quota", "connections"]
---

The "Concurrent Peak Connections" quota refers to the maximum number of simultaneous connections to Supabase Realtime.

For example, if you have a chat application that uses Supabase Realtime and you have 100 users who are all connected and subscribed to the chat channel, then you would have 100 concurrent peak connections. Each client can connect to multiple Realtime channels (see: https://supabase.com/docs/guides/realtime/concepts#channels).

This quota applies to all Supabase projects, including self-hosted projects, but you can increase it depending on your use case. For hosted Supabase projects, select the plan that fits your Realtime usage and reach out if you need custom quotas. For those self-hosting Supabase, you can set those limits yourself by setting the `max_concurrent_users` field on the tenant record (see: https://supabase.com/docs/guides/self-hosting/realtime/config).

You can learn more about Realtime quotas here: https://supabase.com/docs/guides/realtime/quotas#quotas-by-plan
