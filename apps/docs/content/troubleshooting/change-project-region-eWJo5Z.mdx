---
title = "Change Project Region"
github_url = "https://github.com/orgs/supabase/discussions/16341"
date_created = "2023-08-08T14:44:20+00:00"
topics = [ "platform", "database", "auth" ]
keywords = [ "region", "migration" ]
database_id = "fd711d33-9703-402f-84b2-8e74034e7690"
---

The process to change the region of a Supabase Project is to create a new project in the desired region and migrate your existing project the [migrations guide](/docs/guides/platform/migrating-within-supabase).

If you are using third-party auth(Facebook, Google, etc.), you need to manually copy over your client id/secret pairs in the dashboard. Also, you'd need to change your API URL/Anon or Service Keys, which is usually done via ENV vars on your web host.
