---
title = "Webhook debugging guide"
github_url = "https://github.com/orgs/supabase/discussions/21023"
date_created = "2024-02-05T17:56:07+00:00"
topics = [ "database" ]
keywords = [ "webhook", "timeout", "pg_net", "postgres", "trigger" ]
database_id = "a5524182-9dc4-4470-86fe-45ace109a53a"
---

> NOTE: version 0.10.0 of pg*net is out. You should consider updating your database in the[ Infrastructure Settings](https://supabase.com/dashboard/project/*/settings/infrastructure) if you are on a prior version. If you do not know your version, you can check the [Extensions Dashboard](https://supabase.com/dashboard/project/_/database/extensions).

**Debugging Steps**

**1. Test if webhooks are active:**
Webhooks are run in a Postgres background worker. The first debugging step is to see if the worker is active. Run the following SQL code:

```sql
select pid from pg_stat_activity where backend_type ilike '%pg_net%';
```

If it does not return an integer, then the worker has failed and needs to be restarted. If you are running PG_NET 0.8 or later, you can restart the background worker by executing the following function:

```sql
select net.worker_restart();
```

Otherwise, it is necessary to fast reboot your instance in the [Dashboard's Settings](https://supabase.com/dashboard/project/_/settings/general).

**2. Remove any triggers on net tables:**

Using `pg_net` in triggers on most tables is fine; however, do not add triggers to the `net._http_response` or `net.http_request_queue` tables.

The `net` tables are special and if triggers on them fail or call a pg_net function (`http_get`, `http_post`, `http_delete`), it can lead to an infinite loop. This warning is irrelevant to most projects, but it's worth specifying just in case.

**3. Check for timeout errors**

> **NOTE**: The timeout issue has been patched in pg*net v0.11. It is available in Postgres 15.6.1.135 and above. You can upgrade your version of Postgres in the [Infrastructure Settings.](https://supabase.com/dashboard/project/*/settings/infrastructure)

Go to your [Table Editor](https://supabase.com/dashboard/project/_/editor/) and navigate to the net schema. It will contain two tables:

- `_http_response`
- `http_request_queue`

The `_http_response` table saves all the response messages from the past 6 hours. If every column contains NULL values, except for the `id`, `error_msg`, and `created` columns, then you are experiencing [a timeout bug](https://github.com/supabase/pg_net/issues/86).

By default, webhooks will execute the next 200 available queued requests. If the requests are too intense, it may result in a mass timeout. In the [Webhook Dashboard](https://supabase.com/dashboard/project/_/database/hooks), you should increase your webhook's timeout to minimize this issue.

**4. Inspect endpoints**
The below code makes a request to the Postman Echo API through PG_NET

```sql
select
net.http_post(
url := 'https://postman-echo.com/post',
body := '{"key1": "value", "key2": 5}'::jsonb
) as request_id;
```

Postman will then respond with the same payload. This is just a test to confirm that requests are being properly formatted and going through.

You can then view the request in the `net._http_response table` in the [Table Editor](https://supabase.com/dashboard/project/_/editor) or with the following SQL:

```sql
select
*
from net._http_response
where id = <request_id>
```

You should also inspect all the failed responses from the past 6 hours to see if their are any insightful messages:

```sql
select
  *
from net._http_response
where "status_code" >= 400 or "error_msg" is not null
order by created desc;
```

Status codes from a server are described in [Mozilla's web docs](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status)

**5. Create logs**

If none of the above suggestions uncovered the cause of the error, for debugging purposes, it may be useful to write a custom webhook that can create logs in the [Dashboard's Postgres Logs](https://supabase.com/dashboard/project/_/logs/postgres-logs). The process is outlined in the [PG_NET](https://supabase.com/docs/guides/database/extensions/pg_net#debugging-requests) documentation.

**Conclusion:**

If your issue still persists, document it as an issue in the [PG_NET GitHub Repo](https://github.com/supabase/pg_net/issues). You are also welcome to contact Supabase Support from your project's [Dashboard](https://supabase.com/dashboard/project/_/) for more guidance.
