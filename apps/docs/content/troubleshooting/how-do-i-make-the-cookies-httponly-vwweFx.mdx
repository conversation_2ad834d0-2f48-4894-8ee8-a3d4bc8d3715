---
title = "How do I make the cookies HttpOnly?"
github_url = "https://github.com/orgs/supabase/discussions/18869"
date_created = "2023-11-10T11:49:06+00:00"
topics = [ "auth" ]
keywords = [ "cookies", "HttpOnly" ]
database_id = "cdaa76a4-3e5b-4c8f-82f0-b713c4cf19ab"
---

This is not necessary. Both the access token and refresh token are designed to be passed around to different components in your application. The browser-based side of your application needs access to the refresh token to properly maintain a browser session anyway.
