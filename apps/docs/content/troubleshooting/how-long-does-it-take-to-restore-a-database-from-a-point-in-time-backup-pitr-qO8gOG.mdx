---
title = "How long does it take to restore a database from a Point-in-Time backup (PITR)?"
github_url = "https://github.com/orgs/supabase/discussions/19527"
date_created = "2023-12-08T09:40:51+00:00"
keywords = [ "PITR", "restore", "WAL" ]
topics = [ "database" ]
database_id = "cc852935-510f-4beb-9f70-b4582505a863"
---

The time required for a PIT restoration isn't fixed. It depends on several factors:

**Time Since Last Full Backup:**

Full backups occur weekly. The time elapsed since the last full backup can affect restoration time.

**Write-Ahead Logging (WAL) Activity:**

The volume of WAL activity since the last full backup is a critical factor. More activity can lead to longer restoration times.

**Database Size:**

While important, the size of the database isn't the sole determinant of restoration time.
