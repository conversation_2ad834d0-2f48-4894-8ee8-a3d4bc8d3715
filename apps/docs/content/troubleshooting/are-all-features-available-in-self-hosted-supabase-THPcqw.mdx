---
title = "Are all features available in self-hosted Supabase?"
github_url = "https://github.com/orgs/supabase/discussions/27299"
date_created = "2024-06-16T09:33:39+00:00"
topics = [ "self-hosting" ]
database_id = "03854567-8838-4f12-8a6c-095fc1671d9f"
---

### Overview

The self-hosted version is pretty similar to the hosted one. It might not always have the latest features right away, but it includes everything you need to get your application up and running.

### Feature availability

To know what features are available in the self-hosted version, refer to the comprehensive list here:
[Supabase Features](https://supabase.com/docs/guides/getting-started/features#generally-available)

### Self-hosting documentation

For detailed steps and guidance on how to set up and manage your self-hosted Supabase instance, follow the documentation provided:
[Self-Hosting Guide](https://supabase.com/docs/guides/self-hosting)
