---
title = "Failed to <PERSON>tch in dashboard and other areas -- browser extension"
github_url = "https://github.com/orgs/supabase/discussions/17296"
date_created = "2023-09-09T13:22:26+00:00"
topics = [ "studio" ]
keywords = [ "cors" ]
database_id = "23c4e1b5-c7e7-497f-acf8-006133e243a4"

[[errors]]
message = "Failed to Fetch"
---

Although Failed to <PERSON><PERSON> can occur for a variety of reasons a common one in the dashboard or when using update code is related to a browser extension.

![image](/docs/img/troubleshooting/7fe991ac-40ea-4628-a5e3-181d706225ea.png)

https://chrome.google.com/webstore/detail/allow-cors-access-control/lhobafahddgcelffkeicbaginigeejlf

This extension sets PATCH to be blocked by default for some reason...

<img
  width="704"
  alt="168501713-5f020510-4555-4cc3-84de-582efc1c62de"
  src="https://github.com/supabase/supabase/assets/54564956/c61b9292-2954-4c68-8129-995941f36210"
/>
