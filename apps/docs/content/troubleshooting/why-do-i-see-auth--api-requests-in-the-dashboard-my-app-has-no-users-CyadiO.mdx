---
title = "Why do I see Auth & API requests in the dashboard? My app has no users"
github_url = "https://github.com/orgs/supabase/discussions/21579"
date_created = "2024-02-26T21:39:41+00:00"
topics = [ "auth", "platform", "database", "realtime", "functions" ]
keywords = [ "dashboard", "health", "endpoints" ]
database_id = "f85935d8-e871-4463-8288-f118f4e24afd"
---

The dashboard makes requests to the health endpoints of the Supabase services (Database, Auth, Data API, Realtime, Edge Functions to ensure everything is working). These requests appear in the charts about your project:

<img
  width="1135"
  alt="image"
  src="https://github.com/supabase/supabase/assets/5036432/08b1c142-289c-46f3-ac21-38faad50ef8a"
/>

You can see these requests to the health endpoints from the log explorer here:

https://supabase.com/dashboard/project/_/logs/edge-logs
