---
title = "Importing Stripe or other modules from esm.sh on Deno Edge Functions throws an error"
github_url = "https://github.com/orgs/supabase/discussions/14303"
date_created = "2023-05-12T01:30:31+00:00"
topics = [ "functions" ]
keywords = [ "import", "module", "esm" ]
database_id = "225cb141-f442-47cf-834d-1d82d703748a"
---

Try adding `?target=deno` to the import path of the module. For Stripe, the updated import would be:

```
import Stripe from "https://esm.sh/stripe@11.2.0?target=deno";
```
