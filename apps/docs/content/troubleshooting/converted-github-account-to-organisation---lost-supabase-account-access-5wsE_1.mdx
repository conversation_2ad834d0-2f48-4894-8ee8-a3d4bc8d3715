---
title = "Converted Github account to organization - Lost Supabase account access"
github_url = "https://github.com/orgs/supabase/discussions/21101"
date_created = "2024-02-08T01:39:25+00:00"
topics = [ "auth" ]
keywords = [ "github", "organization", "access" ]
database_id = "94c6e33b-cc05-49b3-b010-7480f8dde843"
---

[Converting](https://docs.github.com/en/account-and-profile/setting-up-and-managing-your-personal-account-on-github/managing-your-personal-account/converting-a-user-into-an-organization) your GitHub account to a [GitHub organization](https://docs.github.com/en/get-started/learning-about-github/types-of-github-accounts#organization-accounts) will cause you to lose access to all other accounts using GitHub sign-ins.

However, Supabase can help you transfer ownership of an existing organization on your account by inviting a new owner to your organization. This new user will need to have a different email from the previous one, and you will need <NAME_EMAIL> with that new email as well to confirm that you own the email address.
