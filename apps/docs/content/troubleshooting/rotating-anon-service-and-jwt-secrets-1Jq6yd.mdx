---
title = "Rotating Anon, Service, and JWT Secrets"
github_url = "https://github.com/orgs/supabase/discussions/20031"
date_created = "2023-12-27T14:54:11+00:00"
topics = [ "auth", "platform" ]
keywords = [ "jwt", "secrets", "key", "security" ]
database_id = "caa72a34-696c-47c6-8976-e50cf7fb396e"
---

Have you ever accidentally committed a service key to a public repo? Or maybe rotating keys is just something you regularly do for security compliance.
Whatever the reason, here's how to rotate the keys for your Supabase project.

1. Go to the [API Settings page](https://supabase.com/dashboard/project/_/settings/api) in the Supabase Dashboard
2. Find the JWT Secrets section

<img
  width="1072"
  alt="Screenshot 2023-12-27 at 08 39 41"
  src="https://github.com/supabase/supabase/assets/1923424/bdcab8a2-7007-496c-a845-d331ee883a0a"
/>

3. Click the `Generate new secret` button and choose either a random secret, or custom if you'd like to supply one of your own.
4. NOTE: Once regenerated, all current API secrets will be immediately invalidated, and all connections using them will be severed. You will need to deploy the new secrets for connections to begin working again.
5. Confirm the changes in the warning that pops up by clicking `Generate New Secret` again.

<img
  width="517"
  alt="Screenshot 2023-12-27 at 08 39 59"
  src="https://github.com/supabase/supabase/assets/1923424/1f5cf876-ff65-41f0-b92e-37826773041c"
/>

6. After confirming, the secret will be generated, and Supabase will start rolling that out across our services. Postgres will restart, the API gateways will be updated, etc. Once the process is complete, you will be able to see your new JWT secret as well as the new anon and service keys.
