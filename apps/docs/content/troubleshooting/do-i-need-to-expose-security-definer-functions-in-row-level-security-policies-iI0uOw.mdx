---
title = 'Do I need to expose "security definer" Functions in Row Level Security Policies?'
github_url = "https://github.com/orgs/supabase/discussions/16784"
date_created = "2023-08-24T13:45:01+00:00"
topics = ["database"]
keywords = ["security", "function", "schema", "policy", "definer"]
---

PostgREST supports 2 config parameters:

- Exposed Schemas
- Extra Search Path

![image](/docs/img/troubleshooting/d756aeb0-515f-425d-b737-75a935935b73.png)

You do not need to add your "security definer" Functions to either of these if you are using them in your Policies.

PostgREST doesn’t need to know about this function on extra search path or exposed schemas, as long as you explicitly use the schema inside RLS (e.g.: `security.rls_func`).
