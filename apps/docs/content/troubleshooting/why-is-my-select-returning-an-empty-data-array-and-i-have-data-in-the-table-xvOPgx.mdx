---
title = "Why is my select returning an empty data array and I have data in the table?"
github_url = "https://github.com/orgs/supabase/discussions/12895"
date_created = "2023-03-08T16:38:02+00:00"
topics = [ "database", "auth" ]
keywords = [ "rls", "policy", "filter", "jwt" ]
database_id = "6fa68831-6db8-4836-a1b4-e9e51296d529"
---

Usually this means you have RLS (row level security) enabled and no policy, or do not meet the policy. It can also mean you have a filter and have no rows matching that.

If you have RLS enabled you can test quickly by disable <PERSON><PERSON> on the table. If your query works then you have no policies or do not meet them.

If you have policies that depend on having a signed in user (TO set to `authenticated` or using `auth.uid()`) then you can check by setting TO as `anon` and setting your policy to just `TRUE`. If that works then you don't have a signed in user (with a JWT in the authorization header) when you make the call.

For more information on RLS see https://supabase.com/docs/guides/auth/row-level-security

Adding:
To test if you have a user session at time of your call you can add this function with the SQL editor and an RPC call in your code at same place as your current database call.

```sql
create function test_authorization_header() returns json
    language SQL
    as
$$
    select auth.jwt();
$$;
```

```javascript
const { data: testData, error: testError } = await supabase.rpc('test_authorization_header')
console.log(`The user role is ${testData.role} and the user UUID is ${testData.sub}. `, testError)
```

If you have anon or service_role as the role then you do not have a user session at the time of your call.

To delete the test SQL function:

```sql
drop function test_authorization_header;
```
