---
title = "How to download logical backups in Supabase with physical backups enabled?"
topics = [ "database" ]
keywords = [ "backups" ]
github_url = "https://github.com/orgs/supabase/discussions/33045"
database_id = "91ae4c49-bf22-4b8c-b96b-cd11f1c38158"
---

If you're unable to download backups due to physical backups being enabled, you can use the Supabase CLI command `pgdump` as an alternative.
For step-by-step instructions, check out our guide: [Backup & Restore](https://supabase.com/docs/guides/platform/migrating-within-supabase/backup-restore).

You can also learn more about how backups work in Supabase here: [Database Backups](https://supabase.com/docs/guides/platform/backups).
