---
title = "How do I check GoTrue/API version of a Supabase project?"
github_url = "https://github.com/orgs/supabase/discussions/14217"
date_created = "2023-05-09T04:21:49+00:00"
topics = [ "auth", "platform" ]
database_id = "74972531-f2fc-4d68-a745-21f9c75761e6"
---

Make a `GET` request to the health check endpoint to retrieve this information. Below is an example using `curl`:

```
curl -X GET 'https://project-ref.supabase.co/auth/v1/health' -H 'apikey: ANON_KEY'

{
    "version": "v2.60.7",
    "name": "GoTrue",
    "description": "GoTrue is a user registration and authentication API"
}
```
