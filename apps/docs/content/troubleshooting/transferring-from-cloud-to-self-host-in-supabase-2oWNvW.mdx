---
title = "Transferring from cloud to self-host in Supabase"
github_url = "https://github.com/orgs/supabase/discussions/22712"
date_created = "2024-04-14T15:19:10+00:00"
topics = [ "database", "self-hosting" ]
keywords = [ "migrate", "pg_dump", "psql", "self-host" ]
database_id = "c6b6ae3c-1b5b-4ba8-a40f-5f2ca1f007e2"
---

To migrate from cloud to self-hosting, you can use the [pg_dump](https://www.postgresql.org/docs/9.6/app-pgdump.html) command to export your database to an SQL file, which then you can run on any database to load the same data in.

You can then try to import your SQL files using psql from the terminal:

`psql -h 127.0.0.1 -p 5432 -d postgres -U postgres -f <dump-file-name>.sql`

You can also find some useful information about self-hosting here: https://supabase.com/docs/guides/self-hosting.
