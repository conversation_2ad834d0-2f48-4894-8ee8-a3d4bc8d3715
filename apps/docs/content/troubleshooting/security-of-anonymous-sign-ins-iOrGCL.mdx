---
title = "Security of Anonymous Sign-ins"
github_url = "https://github.com/orgs/supabase/discussions/22856"
date_created = "2024-04-18T07:45:51+00:00"
topics = [ "auth", "platform" ]
keywords = [ "anonymous", "security", "sign-in", "authentication", "identity", "policies" ]
database_id = "46bab0a2-9780-4e4f-99f5-fcc9fa51c496"
---

We want to clarify and provide reassurance on this topic.

### Security overview:

Enabling anonymous sign-ins on your project does not reduce its security. Here's why:

- Same as Regular Users: Anonymous users function just like regular users within your project. They have unique user IDs and their own records in the authentication tables.
- Security Policies: All role-based security policies (RLS) applicable to regular users also apply to anonymous users.
- Identity Verification Measures: Even though anonymous users do not initially provide an email or phone number, the security of your project remains robust. But to prevent misuse, we recommend implementing additional security measure such as [CAPTCHA](https://supabase.com/docs/guides/auth/auth-captcha): to ensure that interactions are genuinely human.

### Practical use cases:

- Demo Mode: You can enable users to try out your product in a demo mode without full account creation.
- Feature Restrictions: You can limit certain actions (like posting public content) to users who sign up with more identifiable information (e.g., Google or Apple sign-ins), while still allowing anonymous users to explore your app.

Remember, the underlying security of your project will remain the same. If your project was secure before enabling anonymous sign-ins, it will continue to be secure afterwards. It's important to review and adjust your RLS policies regularly to align with your security and business needs.

If you have further questions or need assistance in setting up, don't hesitate to contact the [support team](https://supabase.com/dashboard/support/new).
