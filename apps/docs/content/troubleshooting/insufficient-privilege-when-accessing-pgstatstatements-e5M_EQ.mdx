---
title = "\"insufficient privilege\" when accessing pg_stat_statements"
github_url = "https://github.com/orgs/supabase/discussions/20126"
date_created = "2024-01-03T10:59:49+00:00"
topics = [ "database" ]
keywords = [ "privilege", "permissions", "postgres" ]
database_id = "1da315be-38ac-487a-b178-f865a1a73c09"

[[errors]]
message = "insufficient privilege"
---

If you see the error "insufficient privilege" when accessing [pg_stat_statements](https://supabase.com/docs/guides/platform/performance#postgres-cumulative-statistics-system) or when accessing [Query Performance Report](https://supabase.com/dashboard/project/_/reports/query-performance), it means that the Postgres role does not have required permissions.

In this case, you can run the below command to allow the Postgres role to read all statistics from the system:

```
grant pg_read_all_stats to postgres;
```
