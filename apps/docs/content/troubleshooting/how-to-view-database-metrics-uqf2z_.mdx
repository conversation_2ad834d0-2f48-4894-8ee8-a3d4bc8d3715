---
title = "How to View Database Metrics"
github_url = "https://github.com/orgs/supabase/discussions/20938"
date_created = "2024-02-01T17:48:11+00:00"
topics = [ "database", "platform" ]
keywords = [ "metrics", "grafana", "monitoring" ]
database_id = "da2d95e5-abc5-47c8-8389-1554d12abf91"
---

To monitor real-time metrics of your database, like CPU, EBS, active database connections, and memory usage, you can deploy a Grafana Dashboard. Check our [GitHub repo](https://github.com/supabase/supabase-grafana) for setup instructions for local or free [Fly.io](http://fly.io/) deployments. Refer to our concise [documentation](https://supabase.com/docs/guides/platform/metrics) to learn more about the metrics endpoint.

While the [Dashboard's Reports Page](https://supabase.com/dashboard/project/_/reports) displays some metric data, it provides hourly averages, not real-time by the second data. However, it offers query metrics, which the Grafana Dashboard does not include.
