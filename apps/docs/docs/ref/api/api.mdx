---
slug: /
sidebar_position: 1
id: management-api
title: Management API
sidebar_label: Management API
---

The Management API allows you to manage your projects programmatically.

## Authentication

All API requests require a Supabase Personal token to be included in the Authorization header: `Authorization: Bearer <supabase_personal_token>`.
To generate or manage your API token, visit your [account](https://supabase.com/dashboard/account/tokens) page.
Your API tokens carry the same privileges as your user account, so be sure to keep it secret.

```bash
$ curl https://api.supabase.com/v1/projects \
-H "Authorization: Bearer sbp_bdd0••••••••••••••••••••••••••••••••4f23"
```

All API requests must be authenticated and made over HTTPS.

## Rate limits

The rate limit for Management API is 60 requests per one minute per user, and applies cumulatively across all requests made with your personal access tokens.

If you exceed this limit, all Management API calls for the next minute will be blocked, resulting in a HTTP 429 response.

The Management API is subject to our fair-use policy.
All resources created via the API are subject to the pricing detailed on our [Pricing](https://supabase.com/pricing) pages.

## Additional links

- [OpenAPI Docs](https://api.supabase.com/api/v1)
- [OpenAPI Spec](https://api.supabase.com/api/v1-json)
- Reporting bugs and issues: [github.com/supabase/supabase](https://github.com/supabase/supabase)
