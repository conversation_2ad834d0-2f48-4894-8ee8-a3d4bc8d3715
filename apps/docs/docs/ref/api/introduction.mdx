---
id: introduction
title: introduction
hideTitle: true
---

<div className="flex items-start gap-6 not-prose" id="introduction">
  <div className="flex flex-col gap-2">
    <h1 className="text-3xl text-foreground m-0">Management API</h1>
  </div>
</div>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

      Manage your Supabase organizations and projects programmatically.

      ## Authentication

      All API requests require an access token to be included in the Authorization header: `Authorization Bearer <access_token>`.

      There are two ways to generate an access token:

      1. **Personal access token (PAT):**
      PATs are long-lived tokens that you manually generate to access the Management API. They are useful for automating workflows or developing against the Management API. PATs carry the same privileges as your user account, so be sure to keep it secret.

          To generate or manage your personal access tokens, visit your [account](/dashboard/account/tokens) page.

      2. **OAuth2:**
      OAuth2 allows your application to generate tokens on behalf of a Supabase user, providing secure and limited access to their account without requiring their credentials. Use this if you're building a third-party app that needs to create or manage Supabase projects on behalf of your users. Tokens generated via OAuth2 are short-lived and tied to specific scopes to ensure your app can only perform actions that are explicitly approved by the user.

          See [Build a Supabase Integration](/docs/guides/integrations/build-a-supabase-integration) to set up OAuth2 for your application.

      ```bash
        curl https://api.supabase.com/v1/projects \
        -H "Authorization: Bearer sbp_bdd0••••••••••••••••••••••••••••••••4f23"
      ```

      All API requests must be authenticated and made over HTTPS.

      ## Rate limits

      The rate limit for Management API is 60 requests per one minute per user, and applies cumulatively across all requests made with your personal access tokens.

      If you exceed this limit, all Management API calls for the next minute will be blocked, resulting in a HTTP 429 response.

      The Management API is subject to our fair-use policy.
      All resources created via the API are subject to the pricing detailed on our [Pricing](https://supabase.com/pricing) pages.

    </RefSubLayout.Details>

    <RefSubLayout.Examples>

      Additional links

      - [OpenAPI Docs](https://api.supabase.com/api/v1)
      - [OpenAPI Spec](https://api.supabase.com/api/v1-json)
      - [Report bugs and issues](https://github.com/supabase/supabase)

      </RefSubLayout.Examples>

  </RefSubLayout.EducationRow>
