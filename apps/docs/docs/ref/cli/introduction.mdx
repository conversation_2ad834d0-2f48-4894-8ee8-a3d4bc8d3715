---
id: introduction
title: Introduction
hideTitle: true
---

<div className="flex items-start gap-6 not-prose" id="introduction">
  <div className="flex flex-col gap-2">
    <h1 className="text-3xl text-foreground m-0">Supabase CLI</h1>
  </div>
</div>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    The Supabase CLI provides tools to develop your project locally and deploy to the Supabase Platform.
    The CLI is still under development, but it contains all the functionality for working with your Supabase projects and the Supabase Platform.

    - Run Supabase locally: [`supabase init`](/docs/reference/cli/usage#supabase-init) and [`supabase start`](/docs/reference/cli/usage#supabase-start)
    - Manage database migrations: [`supabase migration`](/docs/reference/cli/usage#supabase-migration)
    - CI/CD for releasing to production: [`supabase db push`](/docs/reference/cli/usage#supabase-db-push)
    - Manage your Supabase projects: [`supabase projects`](/docs/reference/cli/usage#supabase-projects)
    - Generate types directly from your database schema: [`supabase gen types`](/docs/reference/cli/usage#supabase-gen)
      - A [community-supported GitHub Action](https://github.com/lyqht/generate-supabase-db-types-github-action) to generate TypeScript types
    - Shell autocomplete: [`supabase completion`](/docs/reference/cli/usage#supabase-completion)
      - A [community-supported Fig autocomplete spec](https://fig.io/manual/supabase) for macOS terminal

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ### Additional links

    - [Install the Supabase CLI](/docs/guides/cli)
    - [Source code](https://github.com/supabase/cli)
    - [Known bugs and issues](https://github.com/supabase/cli/issues)
    - [Supabase CLI v1 and Management API Beta](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)
    - [Video: Announcing CLI V1 and Management API Beta](https://www.youtube.com/watch?v=OpPOaJI_Z28)

  </RefSubLayout.Examples>

</RefSubLayout.EducationRow>
