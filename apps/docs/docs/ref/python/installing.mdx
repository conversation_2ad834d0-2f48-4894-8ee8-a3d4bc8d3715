---
id: installing
title: 'Installing'
slug: installing
custom_edit_url: https://github.com/supabase/supabase/edit/master/apps/docs/spec/supabase_py_v2.yml
---

### Install with PyPi

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    You can install supabase-py via the terminal. (for Python > 3.8)

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    <Tabs
      size="small"
      type="underlined"
      defaultActiveId="pip"
      queryGroup="platform"
    >
      <TabPanel id="pip" label="PIP">

        ```sh Terminal
        pip install supabase
        ```

      </TabPanel>
      <TabPanel id="conda" label="Conda">

        ```sh Terminal
        conda install -c conda-forge supabase
        ```

      </TabPanel>
    </Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>
