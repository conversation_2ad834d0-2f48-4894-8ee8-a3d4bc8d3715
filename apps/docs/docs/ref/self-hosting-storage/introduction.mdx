---
id: introduction
title: Introduction
hideTitle: true
---

<div className="flex items-start gap-6 not-prose" id="introduction">
  <div className="flex flex-col gap-2">
    <h1 className="text-3xl text-foreground m-0">Self-Hosting Storage</h1>
  </div>
</div>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    An S3 compatible object storage service that integrates with Postgres.

    - Uses Postgres as it's datastore for storing metadata
    - Authorization rules are written as Postgres Row Level Security policies
    - Integrates with S3 as the storage backend (with more in the pipeline!)
    - Extremely lightweight and performant

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ### Client libraries

    - [JavaScript](https://github.com/supabase/storage-js)
    - [Dart](https://github.com/supabase/storage-dart)

    ### Additional links

    - [Source code](https://github.com/supabase/storage-api)
    - [Known bugs and issues](https://github.com/supabase/storage-js/issues)
    - [Storage guides](/docs/guides/storage)
    - [OpenAPI docs](https://supabase.github.io/storage/)
    - [Why we built a new object storage service](https://supabase.com/blog/supabase-storage)

  </RefSubLayout.Examples>

</RefSubLayout.EducationRow>
