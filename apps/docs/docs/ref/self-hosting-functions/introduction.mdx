---
id: introduction
title: Introduction
hideTitle: true
---

<div className="flex items-start gap-6 not-prose" id="introduction">
  <img src="/docs/img/icons/menu/reference-analytics.svg" className="w-8 h-8 rounded" />
  <div className="flex flex-col gap-2">
    <h1 className="text-3xl text-foreground m-0">Self-Hosting Functions</h1>
  </div>
</div>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

A web server based on [Deno](https://deno.land) runtime, capable of running JavaScript, TypeScript, and WASM services.

You can use it to:

- Locally test and self-host Supabase's Edge Functions (or any Deno Edge Function)
- As a programmable HTTP Proxy: You can intercept / route HTTP requests

<Admonition type="note" label="Beta Version">

Self hosted Edge functions are in beta. There will be breaking changes to APIs / Configuration Options.

</Admonition>

## How to run locally

```bash
./run.sh start --main-service /path/to/supabase/functions -p 9000
```

using Docker:

```bash
docker build -t edge-runtime .
docker run -it --rm -p 9000:9000 -v /path/to/supabase/functions:/usr/services supabase/edge-runtime start --main-service /usr/services
```

## How to update to a newer Deno version

- Select the Deno version to upgrade and visit its tag on GitHub (eg: https://github.com/denoland/deno/blob/v1.30.3/Cargo.toml)
- Open the `Cargo.toml` at the root of this repo and modify all `deno_*` modules to match to the selected tag of Deno.

## Self hosting Edge Functions on Fly.io

We have put together a demo on how to self-host edge functions on [Fly.io](http://Fly.io) (you can also use other providers like Digital Ocean or AWS).

To try it yourself,

1. Sign up for an [Fly.io](http://Fly.io) account and install [flyctl](https://fly.io/docs/hands-on/install-flyctl/)
2. Clone the demo repository to your machine - https://github.com/supabase/self-hosted-edge-functions-demo
3. Copy your Edge Function into the `./functions` directory in the demo repo.
4. Update the Dockerfile to pull the latest edge-runtime image (check [releases](https://github.com/supabase/edge-runtime/pkgs/container/edge-runtime))
5. [Optional] Edit `./functions/main/index.ts`, adding any other request preprocessing logic (for example, you can enable JWT validation, handle CORS requests)
6. Run `fly launch` to create a new app to serve your Edge Functions
7. Access your Edge Function by visiting:
   `https://{your-app-name}.fly.dev/{your-function-name}`

You can view the logs for the Edge Runtime, by visiting Fly.io’s Dashboard > Your App > Metrics. Also, you can serve edge-runtime from multiple regions by running `fly regions add [REGION]`.

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ### Client libraries
    - [JavaScript](https://supabase.com/docs/reference/javascript/functions-invoke)
    - [Dart](https://supabase.com/docs/reference/dart/functions-invoke)

    ### Additional Links

    - [Source code](https://github.com/supabase/edge-runtime/)

  </RefSubLayout.Examples>

</RefSubLayout.EducationRow>
