---
id: installing
title: 'Installing'
slug: installing
custom_edit_url: https://github.com/supabase/supabase/edit/master/web/spec/supabase.yml
---

### Install using Swift Package Manager

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    You can install Supabase package using Swift Package Manager.

    The package exposes multiple libraries, you can choose between adding all of them using Supabase, or some of:

    - `Auth`
    - `Realtime`
    - `Postgrest`
    - `Functions`
    - `Storage`

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    <Tabs
      size="small"
      type="underlined"
      defaultActiveId="swift"
      queryGroup="framework"
    >
      <TabPanel id="swift" label="Package.swift">

        ```swift
        let package = Package(
            ...
            dependencies: [
                ...
                .package(
                    url: "https://github.com/supabase/supabase-swift.git",
                    from: "2.0.0"
                ),
            ],
            targets: [
                .target(
                    name: "YourTargetName",
                    dependencies: [
                        .product(
                            name: "Supabase", // Auth, Realtime, Postgrest, Functions, or Storage
                            package: "supabase-swift"
                        ),
                    ]
                )
            ]
        )
        ```

      </TabPanel>
    </Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>
