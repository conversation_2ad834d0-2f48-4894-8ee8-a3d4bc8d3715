---
id: introduction
title: Introduction
hideTitle: true
---

<div className="flex items-start gap-6 not-prose" id="introduction">
  <div className="flex flex-col gap-2">
    <h1 className="text-3xl text-foreground m-0">Self-Hosting Analytics</h1>
  </div>
</div>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

The Supabase Analytics server is a Logflare self-hostable instance that manages the ingestion and query pipelines for searching and aggregating structured analytics events.

When self-hosting the Analytics server, the full logging experience matching that of the Supabase Platform is available in the Studio instance, allowing for an integrated and enhanced development experience.
However, it's important to note that certain [differences](#differences) may arise due to the platform's infrastructure.

<Admonition type="note" label="Logflare Technical Docs">

All Logflare technical documentation is available at https://docs.logflare.app.

</Admonition>

## Backends Supported

The Analytics server supports either **Postgres** or **BigQuery** as the backend. The `supabase-cli` experience uses the Postgres backend out-of-the-box. However, the Supabase Platform uses the BigQuery backend for storing all platform logs.

When using the BigQuery backend, a BigQuery dataset is created in the provided Google Cloud project, and tables are created for each source. Log events are streamed into each table, and all queries generated by Studio or by the Logs Explorer are executed against the BigQuery API. This backend requires internet access to work, and cannot be run fully locally.

When using the Postgres backend, tables are created for each source within the provided schema (for supabase-cli, this would be `_analytics`). Log events received by Logflare are inserted directly into the respective tables. All BigQuery-dialect SQL queries from Studio will be handled by a translation layer within the Analytics server. This translation layer translates the query to PostgreSQL dialect, and then executes it against the Postgres database.

The Postgres backend is not yet optimized for a high volume of inserts, or for heavy query usage. Today the translation layer only handles a limited subset of the BigQuery dialect. As such, the [Log Explorer](https://supabase.com/docs/guides/platform/logs#logs-explorer) may produce errors for more advanced queries when using the Postgres Backend.

## Getting Started

The Postgres backend is recommended when familiarizing and experimenting with self-hosting Supabase. For production, we recommend using the BigQuery backend. See [production recommendations](#production-recommendations) for more information.

To set up logging in self-hosted Supabase, see the [docker-compose example](https://github.com/supabase/supabase/tree/master/docker).
Two compose services are required: Logflare, and Vector. Logflare is the HTTP Analytics server, while Vector is the logging pipeline to route all compose services' syslog to the Logflare sever.

Regardless of the backend chosen, the following environment variables **must** be set for the `supabase/logflare` docker image:

- `LOGFLARE_SINGLE_TENANT=true`: The feature flag for enabling single tenant mode for Logflare. Must be set to `true`
- `LOGFLARE_SUPABASE_MODE=true`: The feature flag for seeding Supabase-related data. Must be set to `true`

For all other configuration environment variables, please refer to the [Logflare self-hosting documentation](https://docs.logflare.app/self-hosting/#configuration).

## Postgres Backend Setup

The [example docker-compose](https://github.com/supabase/supabase/tree/master/docker) uses the Postgres backend out of the box.

```bash
# clone the supabase/supabase repo, and run the following
cd docker
docker compose -f docker-compose.yml up
```

### Configuration and Requirements

- `supabase/logflare:1.4.0` or above
- Relevant environment variables:
  - `POSTGRES_BACKEND_URL` : Required. The connection string to the Postgres database.
  - `POSTGRES_BACKEND_SCHEMA` : Optional. Allows customization of the schema used to scope all backend operations within the database.

## BigQuery Backend Setup

The BigQuery backend is a more robust and scalable backend option that is battle-tested and production ready. Use this backend if you intend to have heavy logging usage and require advanced querying features such as the Logs Explorer.

### Configuration and Requirements

The requirements are as follows after creating the project:

- Google Cloud project with billing enabled
- Project ID
- Project number
- A service account key.

<Admonition>

You must enable billing on your Google Cloud project, as a valid billing account is required for streaming inserts.

</Admonition>

#### Setting up BigQuery Service Account

The service account used must have sufficient permissions to insert into your Google Cloud BigQuery. Ensure that the service account has either:

- BigQuery Admin role; or
- The following permissions:
  - `bigquery.datasets.create`
  - `bigquery.datasets.get`
  - `bigquery.datasets.getIamPolicy`
  - `bigquery.datasets.update`
  - `bigquery.jobs.create`
  - `bigquery.routines.create`
  - `bigquery.routines.update`
  - `bigquery.tables.create`
  - `bigquery.tables.delete`
  - `bigquery.tables.get`
  - `bigquery.tables.getData`
  - `bigquery.tables.update`
  - `bigquery.tables.updateData`

You can create the service account via the web console or `gcloud` CLI, as per the [Google Cloud documentation](https://cloud.google.com/iam/docs/keys-create-delete). In the web console, you can create the key by navigating to IAM > Service Accounts > Actions (dropdown) > Manage Keys

We recommend setting the BigQuery Admin role, as it simplifies permissions setup.

#### Download the Service Account Keys

After the service account is created, you will need to create a key for the service account. This key will sign the JWTs for API requests that the Analytics server makes with BigQuery. This can be done through the IAM section in Google Cloud console.

#### Docker Image Configuration

Using the example [self-hosting stack based on docker-compose](https://github.com/supabase/supabase/tree/master/docker), you include the logging related services using the following command

1. Update the `.env.example` file with the necessary environment variables.

- `GOOGLE_PROJECT_ID`
- `GOOGLE_PROJECT_NUMBER`

2. Place your Service Account key in your present working directory with the filename `gcloud.json`.
3. On `docker-compose.yml`, uncomment the block section below the commentary `# Uncomment to use Big Query backend for analytics`
4. On `docker-compose.yml`, comment the block section below the commentary `# Comment variables to use Big Query backend for analytics`

Thereafter, you can start the example stack using the following command:

```bash
# assuming you clone the supabase/supabase repo.
cd docker
docker compose -f docker-compose.yml
```

#### BigQuery Datset Storage Location

Currently, all BigQuery datasets stored and managed by Analytics, whether via CLI or self-hosted, will default to the US region.

## Vector Usage

In the Docker Compose example, Vector is used for the logging pipieline, where log events are forwarded to the Analytics API for ingestion.

Please refer to the [Vector configuration file](https://github.com/supabase/supabase/blob/master/docker/volumes/logs/vector.yml) when customizing your own setup.

You **must** ensure that the payloads matches the expected event schema structure. Without the correct structure, it would cause the Studio Logs UI features to break.

## Differences from Platform

API logs rely on Kong instead of the Supabase Cloud API Gateway. Logs from Kong are not enriched with platform-only data.

Within the self-hosted setup, all logs are routed to Logflare via Vector. As Kong routes API requests to PostgREST, self-hosted or local deployments will result in Kong request logs instead.
This would result in differences in the log event metadata between self-hosted API requests and Supabase Platform requests.

## Production Recommendations

To self-host in a production setting, we recommend performing the following for a better experience.

### Ensure that Logflare is behind a firewall and restrict all network access to it besides safe requests.

Self-hosted Logflare has UI authentication disabled and is intended for exposure to the internet. We recommend restricting access to the dashboard, accessible at the `/dashboard` path.
If dashboard access is required for managing sources, we recommend having an authentication layer, such as a VPN.

### Use a different Postgres Database to store Logflare data.

Logflare requires a Postgres database to function. However, if there is an issue with you self-hosted Postgres service, you would not be able to debug it as it would also bring Logflare down together.

The self-hosted example is only used as a minimal example on running the entire stack, however it is not recommended to use the same database server for both production and observability.

### Use Big Query as the Logflare Backend

The current Postgres Ingestion backend isn't optimized for production usage. We recommend using Big Query for more heavy use cases.

**We recommend using the BigQuery backend for production environments as it offers better scaling and querying/debugging experiences.**

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ### Client libraries

    - [JavaScript - Pino Transport](https://github.com/Logflare/pino-logflare)
    - [Elixir](https://github.com/Logflare/logflare_api_client)
    - [Elixir - Logger Backend](https://github.com/Logflare/logflare_logger_backend)
    - [Erlang](https://github.com/Logflare/logflare_erl)
    - [Erlang - Lager Backend](https://github.com/Logflare/logflare_lager_backend)
    - [Cloudflare Worker](https://gist.github.com/chasers/c7a220e91820a1084b27fcfdb18ad6bd)

    ### Integrations

    - [Fly - Logs](https://github.com/Logflare/fly-log-shipper)
    - [Vercel Integration - Logs](https://vercel.com/integrations/logflare)
    - [Cloudflare App - Logs](https://www.cloudflare.com/apps/logflare/install)

    ### Additional links

    - [Source code](https://github.com/logflare/logflare)
    - [OpenAPI docs](https://logflare.app/api/openapi)
    - [Supabase Acquires Logflare](https://supabase.com/blog/supabase-acquires-logflare)
    - [Logflare self-hosting docs](https://docs.logflare.app/self-hosting)

  </RefSubLayout.Examples>

</RefSubLayout.EducationRow>
