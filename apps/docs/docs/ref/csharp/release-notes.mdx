---
id: release-notes
title: Release Notes
---

## 1.0.0 - 2024-04-21

- Assembly Name has been changed to `Supabase.dll`
- Update dependency: `postgrest-csharp@5.0.0`
  - [MAJOR] Moves namespaces from `Postgrest` to `Supabase.Postgrest`
  - Re: [#135](https://github.com/supabase-community/supabase-csharp/issues/135) Update nuget package
    name `postgrest-csharp` to `Supabase.Postgrest`
- Update dependency: `gotrue-csharp@5.0.0`
  - Re: [#135](supabase-community/supabase-csharp#135) Update nuget package name `gotrue-csharp` to `Supabase.Gotrue`
  - Re: [#89](https://github.com/supabase-community/gotrue-csharp/issues/89), Only add `access_token` to request body
    when it is explicitly declared.
  - [MINOR] Re: [#89](https://github.com/supabase-community/gotrue-csharp/issues/89) Update signature
    for `SignInWithIdToken` which adds an optional `accessToken` parameter, update doc comments, and
    call `DestroySession` in method
  - Re: [#88](https://github.com/supabase-community/gotrue-csharp/issues/88), Add `IsAnonymous` property to `User`
  - Re: [#90](https://github.com/supabase-community/gotrue-csharp/issues/90) Implement `LinkIdentity`
    and `UnlinkIdentity`
- Update dependency: `realtime-csharp@7.0.0`
  - Merges [#45](https://github.com/supabase-community/realtime-csharp/pull/45) - Updating
    the `Websocket.Client@5.1.1`
  - Re: [#135](https://github.com/supabase-community/supabase-csharp/issues/135) Update nuget package
    name `realtime-csharp` to `Supabase.Realtime`
- Update dependency: `storage-csharp@2.0.0`
  - Re: [#135](https://github.com/supabase-community/supabase-csharp/issues/135) Update nuget package
    name `storage-csharp` to `Supabase.Storage`
- Update dependency: `functions-csharp@2.0.0`
  - Re: [#135](https://github.com/supabase-community/supabase-csharp/issues/135) Update nuget package
    name `functions-csharp` to `Supabase.Functions`
- Update dependency: `core-csharp@1.0.0`
  - Re: [#135](https://github.com/supabase-community/supabase-csharp/issues/135) Update nuget package
    name `supabase-core`
    to `Supabase.Core`
- Adds comments to the remaining undocumented code.
