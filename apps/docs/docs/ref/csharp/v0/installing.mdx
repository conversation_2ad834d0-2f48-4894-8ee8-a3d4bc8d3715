---
id: installing
title: 'Installing & Initialization'
slug: installing
custom_edit_url: https://github.com/supabase/supabase/edit/master/web/spec/supabase.yml
---

### Install from NuGet

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    You can install Supabase package from [nuget.org](https://www.nuget.org/packages/supabase-csharp/)

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    <Tabs
      size="small"
      type="underlined"
      defaultActiveId="csharp"
      queryGroup="language"
    >
      <TabPanel id="csharp" label="Terminal">

        ```sh Terminal
        dotnet add package supabase-csharp
        ```

      </TabPanel>
    </Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>
