This reference documents every object and method available in Supabase's C# library, [supabase-csharp](https://www.nuget.org/packages/supabase-csharp). You can use `Supabase` to interact with your Postgres database, listen to database changes, invoke Deno Edge Functions, build login and user management functionality, and manage large files.

<Admonition type="note">

The C# client library is created and maintained by the Supabase community, and is not an official library. Please be tolerant of areas where the library is still being developed, and — as with all the libraries — feel free to contribute wherever you find issues.

Huge thanks to official maintainer, [<PERSON>](https://github.com/acupofjose). As well as [<PERSON>](https://github.com/wiverson), [<PERSON>](https://github.com/veleek), and [<PERSON><PERSON><PERSON>](https://github.com/rhuanbarros) for their help.

</Admonition>
