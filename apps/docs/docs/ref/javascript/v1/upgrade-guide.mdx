---
id: upgrade-guide
title: Upgrade to supabase-js v2
description: 'Learn how to upgrade to supabase-js v2.'
---

supabase-js v2 focuses on "quality-of-life" improvements for developers and addresses some of the largest pain points in v1. v2 includes type support, a rebuilt Auth library with async methods, improved errors, and more.

No new features will be added to supabase-js v1 , but we'll continuing merging security fixes to v1, with maintenance patches for the next 3 months.

## Upgrade the client library

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    Install the latest version

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

```sh
npm install @supabase/supabase-js@2
```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

_Optionally_ if you are using custom configuration with `createClient` then follow below:

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">

```ts src/supabaseClient.ts
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  schema: 'custom',
  persistSession: false,
})
```

</TabPanel>
<TabPanel id="2.0x" label="After">

```ts src/supabaseClient.ts
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  db: {
    schema: 'custom',
  },
  auth: {
    persistSession: true,
  },
})
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

Read more about the [constructor options](/docs/reference/javascript/release-notes#explicit-constructor-options).

### Auth methods

The signIn() method has been deprecated in favor of more explicit method signatures to help with type hinting. Previously it was difficult for developers to know what they were missing (e.g., a lot of developers didn't realize they could use passwordless magic links).

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Sign in with email and password

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

 <Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { user, error } = await supabase
  .auth
  .signIn({ email, password })
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const {
  data: { user },
  error,
} = await supabase
  .auth
  .signInWithPassword({ email, password })
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Sign in with magic link

  </RefSubLayout.Details>
  <RefSubLayout.Examples>
 <Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { error } = await supabase
  .auth
  .signIn({ email })
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { error } = await supabase
  .auth
  .signInWithOtp({ email })
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Sign in with a third-party provider

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { error } = await supabase
  .auth
  .signIn({ provider })
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { error } = await supabase
  .auth
  .signInWithOAuth({ provider })
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Sign in with phone

  </RefSubLayout.Details>
  <RefSubLayout.Examples>
 <Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { error } = await supabase
  .auth
  .signIn({ phone, password })
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { error } = await supabase
  .auth
  .signInWithPassword({ phone, password })
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Sign in with phone using OTP

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { error } = await supabase
  .auth
  .api
  .sendMobileOTP(phone)
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { data, error } = await supabase
  .auth
  .signInWithOtp({ phone })

// After receiving a SMS with a OTP.
const { data, error } = await supabase
.auth
.verifyOtp({ phone, token })

````

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Reset password for email

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { data, error } = await supabase
  .auth
  .api
  .resetPasswordForEmail(email)
````

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { data, error } = await supabase
  .auth
  .resetPasswordForEmail(email)
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Get the user's current session

<$Partial path="get_session_warning.mdx" />

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">

```ts
const session = supabase.auth.session()
```

</TabPanel>
<TabPanel id="2.0x" label="After">

```ts
const {
  data: { session },
} = await supabase.auth.getSession()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Get the logged-in user

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">

```ts
const user = supabase.auth.user()
```

</TabPanel>
<TabPanel id="2.0x" label="After">

```ts
const {
  data: { user },
} = await supabase.auth.getUser()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Update user data for a logged-in user

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { user, error } = await supabase
  .auth
  .update({ attributes })
```

</TabPanel>
<TabPanel id="2.0x" label="After">

```ts
const {
  data: { user },
  error,
} = await supabase.auth.updateUser({ attributes })
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Use a custom `access_token` JWT with Supabase

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">

```ts
const { user, error } = supabase.auth.setAuth(access_token)
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const supabase = createClient(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    global: {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    },
  }
)
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

### Cookie methods

The cookie-related methods like `setAuthCookie` and `getUserByCookie` have been removed.

For Next.js you can use the [Auth Helpers](https://supabase.com/docs/guides/auth/auth-helpers/nextjs) to help you manage cookies.
If you can't use the Auth Helpers, you can use [server-side rendering](https://supabase.com/docs/guides/auth/server-side-rendering).

Some the [PR](https://github.com/supabase/gotrue-js/pull/340) for additional background information.

### Data methods

`.insert()` / `.upsert()` / `.update()` / `.delete()` don't return rows by default: [PR](https://github.com/supabase/postgrest-js/pull/276).

Previously, these methods return inserted/updated/deleted rows by default (which caused [some confusion](https://github.com/supabase/supabase/discussions/1548)), and you can opt to not return it by specifying `returning: 'minimal'`. Now the default behavior is to not return rows. To return inserted/updated/deleted rows, add a `.select()` call at the end.

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Insert and return data

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { data, error } = await supabase
  .from('my_table')
  .insert({ new_data })
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { data, error } = await supabase
  .from('my_table')
  .insert({ new_data })
  .select()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Update and return data

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { data, error } = await supabase
  .from('my_table')
  .update({ new_data })
  .eq('id', id)
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { data, error } = await supabase
  .from('my_table')
  .update({ new_data })
  .eq('id', id)
  .select()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Upsert and return data

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { data, error } = await supabase
  .from('my_table')
  .upsert({ new_data })
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { data, error } = await supabase
  .from('my_table')
  .upsert({ new_data })
  .select()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Delete and return data

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">
```ts
const { data, error } = await supabase
  .from('my_table')
  .delete()
  .eq('id', id)
```

</TabPanel>
<TabPanel id="2.0x" label="After">
```ts
const { data, error } = await supabase
  .from('my_table')
  .delete()
  .eq('id', id)
  .select()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

### Realtime methods

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Subscribe

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">

```ts
const userListener = supabase
  .from('users')
  .on('*', (payload) => handleAllEventsPayload(payload.new))
  .subscribe()
```

</TabPanel>
<TabPanel id="2.0x" label="After">

```ts
const userListener = supabase
  .channel('public:user')
  .on('postgres_changes', { event: '*', schema: 'public', table: 'user' }, (payload) =>
    handleAllEventsPayload()
  )
  .subscribe()
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

#### Unsubscribe

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

<Tabs
  scrollable
  size="small"
  type="underlined"
  defaultActiveId="1.0x"
  queryGroup="version"
>
<TabPanel id="1.0x" label="Before">

```ts
userListener.unsubscribe()
```

</TabPanel>
<TabPanel id="2.0x" label="After">

```ts
supabase.removeChannel(userListener)
```

</TabPanel>
</Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>
