---
id: typescript-support
title: TypeScript support
---

`supabase-js` has TypeScript support for type inference, autocompletion, type-safe queries, and more.

With TypeScript, `supabase-js` detects things like `not null` constraints and [generated columns](https://www.postgresql.org/docs/current/ddl-generated-columns.html). Nullable columns are typed as `T | null` when you select the column. Generated columns will show a type error when you insert to it.

`supabase-js` also detects relationships between tables. A referenced table with one-to-many relationship is typed as `T[]`. Likewise, a referenced table with many-to-one relationship is typed as `T | null`.

## Generating TypeScript Types

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    You can use the Supabase CLI to [generate the types](/docs/reference/cli/supabase-gen-types). You can also generate the types [from the dashboard](https://supabase.com/dashboard/project/_/api?page=tables-intro).

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

    ```bash Terminal
    supabase gen types typescript --project-id abcdefghijklmnopqrst > database.types.ts
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

     These types are generated from your database schema. Given a table `public.movies`, the generated types will look like:

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

    ```sql
    create table public.movies (
      id bigint generated always as identity primary key,
      name text not null,
      data jsonb null
    );
    ```

    ```ts ./database.types.ts
    export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

    export interface Database {
      public: {
        Tables: {
          movies: {
            Row: {               // the data expected from .select()
              id: number
              name: string
              data: Json | null
            }
            Insert: {            // the data to be passed to .insert()
              id?: never         // generated columns must not be supplied
              name: string       // `not null` columns with no default must be supplied
              data?: Json | null // nullable columns can be omitted
            }
            Update: {            // the data to be passed to .update()
              id?: never
              name?: string      // `not null` columns are optional on .update()
              data?: Json | null
            }
          }
        }
      }
    }
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

## Using TypeScript type definitions

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    You can supply the type definitions to `supabase-js` like so:

  </RefSubLayout.Details>
  <RefSubLayout.Examples>
    ```ts ./index.tsx
    import { createClient } from '@supabase/supabase-js'
    import { Database } from './database.types'

    const supabase = createClient<Database>(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_ANON_KEY
    )
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

## Helper types for Tables and Joins

You can use the following helper types to make the generated TypeScript types easier to use.

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    Sometimes the generated types are not what you expect. For example, a view's column may show up as nullable when you expect it to be `not null`. Using [type-fest](https://github.com/sindresorhus/type-fest), you can override the types like so:

  </RefSubLayout.Details>
  <RefSubLayout.Examples>
    ```ts ./database-generated.types.ts
    export type Json = // ...

    export interface Database {
      // ...
    }
    ```

    ```ts ./database.types.ts
    import { MergeDeep } from 'type-fest'
    import { Database as DatabaseGenerated } from './database-generated.types'
    export { Json } from './database-generated.types'

    // Override the type for a specific column in a view:
    export type Database = MergeDeep<
      DatabaseGenerated,
      {
        public: {
          Views: {
            movies_view: {
              Row: {
                // id is a primary key in public.movies, so it must be `not null`
                id: number
              }
            }
          }
        }
      }
    >
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
<RefSubLayout.Details>

You can also override the type of an individual successful response if needed:

</RefSubLayout.Details>
<RefSubLayout.Examples>

```ts
// Partial type override allows you to only override some of the properties in your results
const { data } = await supabase.from('countries').select().overrideTypes<Array<{ id: string }>>()
// For a full replacement of the original return type use the `{ merge: false }` property as second argument
const { data } = await supabase
  .from('countries')
  .select()
  .overrideTypes<Array<{ id: string }>, { merge: false }>()
// Use it with `maybeSingle` or `single`
const { data } = await supabase.from('countries').select().single().overrideTypes<{ id: string }>()
```

</RefSubLayout.Examples>
</RefSubLayout.EducationRow>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    The generated types provide shorthands for accessing tables and enums.

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

    ```ts ./index.ts
    import { Database, Tables, Enums } from "./database.types.ts";

    // Before 😕
    let movie: Database['public']['Tables']['movies']['Row'] = // ...

    // After 😍
    let movie: Tables<'movies'>
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

### Response types for complex queries

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    `supabase-js` always returns a `data` object (for success), and an `error` object (for unsuccessful requests).

    These helper types provide the result types from any query, including nested types for database joins.

    Given the following schema with a relation between cities and countries, we can get the nested `CountriesWithCities` type:

    ```sql
    create table countries (
      "id" serial primary key,
      "name" text
    );

    create table cities (
      "id" serial primary key,
      "name" text,
      "country_id" int references "countries"
    );
    ```

  </RefSubLayout.Details>
  <RefSubLayout.Examples>

    ```ts
    import { QueryResult, QueryData, QueryError } from '@supabase/supabase-js'

    const countriesWithCitiesQuery = supabase
      .from("countries")
      .select(`
        id,
        name,
        cities (
          id,
          name
        )
      `);
    type CountriesWithCities = QueryData<typeof countriesWithCitiesQuery>;

    const { data, error } = await countriesWithCitiesQuery;
    if (error) throw error;
    const countriesWithCities: CountriesWithCities = data;
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>
