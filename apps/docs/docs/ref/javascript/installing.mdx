---
id: installing
title: 'Installing'
slug: installing
custom_edit_url: https://github.com/supabase/supabase/edit/master/web/spec/supabase.yml
---

### Install as package

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    You can install @supabase/supabase-js via the terminal.

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    <Tabs
      size="small"
      type="underlined"
      defaultActiveId="npm"
      queryGroup="platform"
    >
      <TabPanel id="npm" label="npm">

        ```sh Terminal
        npm install @supabase/supabase-js
        ```

      </TabPanel>
      <TabPanel id="yarn" label="Yarn">

        ```sh Terminal
        yarn add @supabase/supabase-js
        ```

      </TabPanel>
      <TabPanel id="pnpm" label="pnpm">

        ```sh Terminal
        pnpm add @supabase/supabase-js
        ```

      </TabPanel>
    </Tabs>

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

### Install via CDN

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>
    
    You can install @supabase/supabase-js via CDN links.

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ```js
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    //or
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>

### Use at runtime in Deno

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>
    
    You can use supabase-js in the Deno runtime via [JSR](https://jsr.io/@supabase/supabase-js):

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ```ts
      import { createClient } from 'jsr:@supabase/supabase-js@2'
    ```

  </RefSubLayout.Examples>
</RefSubLayout.EducationRow>
