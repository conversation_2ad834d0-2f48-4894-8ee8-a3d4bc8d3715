---
id: introduction
title: Introduction
hideTitle: true
---

<div className="flex items-start gap-6 not-prose" id="introduction">
  <div className="flex flex-col gap-2">
    <h1 className="text-3xl text-foreground m-0">Self-Hosting Auth</h1>
  </div>
</div>

<RefSubLayout.EducationRow>
  <RefSubLayout.Details>

    The Supabase Auth Server (GoTrue) is a JSON Web Token (JWT)-based API for managing users and issuing access tokens.

    GoTrue is an open-source API written in Golang, that acts as a self-standing API service for handling user registration and authentication for JAM projects. It's based on OAuth2 and JWT and handles user signup, authentication, and custom user data.

  </RefSubLayout.Details>

  <RefSubLayout.Examples>

    ### Client libraries

    - [JavaScript](https://github.com/supabase/gotrue-js)
    - [Dart](https://github.com/supabase/gotrue-dart)

    ### Additional links

    - [Source code](https://github.com/supabase/gotrue)
    - [Known bugs and issues](https://github.com/supabase/gotrue/issues)
    - [Auth guides](/docs/guides/auth)

  </RefSubLayout.Examples>

</RefSubLayout.EducationRow>
