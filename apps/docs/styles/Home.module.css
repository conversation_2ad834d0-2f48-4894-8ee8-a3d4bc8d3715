.container {
  padding: 0;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content {
  min-height: 400px;
}

.footer {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 2rem 0;
  justify-content: flex-start;
  align-items: center;
}

.footer a {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-grow: 1;
}

.footer > div > div {
  width: 256px;
}
.footer h4 {
  margin-bottom: 1em;
}
.footer a {
  margin-top: 1em;
}

.title a {
  color: #0070f3;
  text-decoration: none;
}

.title a:hover,
.title a:focus,
.title a:active {
  text-decoration: underline;
}

.title {
  margin: 0;
  line-height: 1.15;
  font-size: 4rem;
}

.title,
.description {
  text-align: center;
}

.description {
  margin: 4rem 0;
  line-height: 1.5;
  font-size: 1.5rem;
}

/* .code {
  background: #fafafa;
  border-radius: 5px;
  padding: 0.75rem;
  font-size: 1.1rem;
  font-family: Menlo, Monaco, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>,
    Bitstream Vera Sans Mono, Courier New, monospace;
} */

.grid {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 800px;
}

.card {
  margin: 1rem;
  padding: 1.5rem;
  text-align: left;
  color: inherit;
  text-decoration: none;
  border: 1px solid #eaeaea;
  border-radius: 10px;
  transition:
    color 0.15s ease,
    border-color 0.15s ease;
  max-width: 300px;
}

.card:hover,
.card:focus,
.card:active {
  color: #0070f3;
  border-color: #0070f3;
}

.card h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.card p {
  margin: 0;
  font-size: 1.25rem;
  line-height: 1.5;
}

.logo {
  height: 1em;
  margin-left: 0.5rem;
}

@media (max-width: 600px) {
  .grid {
    width: 100%;
    flex-direction: column;
  }
}
