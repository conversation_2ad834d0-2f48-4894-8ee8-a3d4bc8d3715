const authProviders = [
  {
    name: 'Apple',
    logo: '/docs/img/icons/apple-icon',
    href: '/guides/auth/social-login/auth-apple',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Azure (Microsoft)',
    logo: '/docs/img/icons/microsoft-icon',
    href: '/guides/auth/social-login/auth-azure',
    official: false,
    supporter: 'TBD',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Bitbucket',
    logo: '/docs/img/icons/bitbucket-icon',
    href: '/guides/auth/social-login/auth-bitbucket',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Discord',
    logo: '/docs/img/icons/discord-icon',
    href: '/guides/auth/social-login/auth-discord',
    official: true,
    supporter: 'Supa<PERSON>',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Facebook',
    logo: '/docs/img/icons/facebook-icon',
    href: '/guides/auth/social-login/auth-facebook',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Figma',
    logo: '/docs/img/icons/figma-icon',
    href: '/guides/auth/social-login/auth-figma',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'GitHub',
    logo: '/docs/img/icons/github-icon',
    href: '/guides/auth/social-login/auth-github',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
    hasLightIcon: true,
  },
  {
    name: 'GitLab',
    logo: '/docs/img/icons/gitlab-icon',
    href: '/guides/auth/social-login/auth-gitlab',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Google',
    logo: '/docs/img/icons/google-icon',
    href: '/guides/auth/social-login/auth-google',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Kakao',
    logo: '/docs/img/icons/kakao-icon',
    href: '/guides/auth/social-login/auth-kakao',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Keycloak',
    logo: '/docs/img/icons/keycloak-icon',
    href: '/guides/auth/social-login/auth-keycloak',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'LinkedIn',
    logo: '/docs/img/icons/linkedin-icon',
    href: '/guides/auth/social-login/auth-linkedin',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'MessageBird',
    logo: '/docs/img/icons/messagebird-icon',
    href: '/guides/auth/phone-login?showSmsProvider=MessageBird',
    official: false,
    supporter: 'MessageBird',
    platform: true,
    selfHosted: true,
    authType: 'phone',
  },
  {
    name: 'Notion',
    logo: '/docs/img/icons/notion-icon',
    href: '/guides/auth/social-login/auth-notion',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Slack',
    logo: '/docs/img/icons/slack-icon',
    href: '/guides/auth/social-login/auth-slack',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Spotify',
    logo: '/docs/img/icons/spotify-icon',
    href: '/guides/auth/social-login/auth-spotify',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Twitter',
    logo: '/docs/img/icons/twitter-icon',
    href: '/guides/auth/social-login/auth-twitter',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
    hasLightIcon: true,
  },
  {
    name: 'Twitch',
    logo: '/docs/img/icons/twitch-icon',
    href: '/guides/auth/social-login/auth-twitch',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'WorkOS',
    logo: '/docs/img/icons/workos-icon',
    href: '/guides/auth/social-login/auth-workos',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Zoom',
    logo: '/docs/img/icons/zoom-icon',
    href: '/guides/auth/social-login/auth-zoom',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'social',
  },
  {
    name: 'Twilio',
    logo: '/docs/img/icons/twilio-icon',
    href: '/guides/auth/phone-login?showSmsProvider=Twilio',
    official: true,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'phone',
  },
  {
    name: 'Vonage',
    logo: '/docs/img/icons/vonage-icon',
    href: '/guides/auth/phone-login?showSmsProvider=Vonage',
    official: false,
    supporter: 'Supabase',
    platform: true,
    selfHosted: true,
    authType: 'phone',
    hasLightIcon: true,
  },
]

export default authProviders
