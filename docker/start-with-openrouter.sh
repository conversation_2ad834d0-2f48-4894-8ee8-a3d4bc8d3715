#!/bin/bash

# Cargar variables desde el archivo .env
if [ -f ".env" ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Asegúrate de que la variable OPENROUTER_API_KEY esté configurada
if [ -z "$OPENROUTER_API_KEY" ]; then
  echo "Por favor, configura tu clave API de Openrouter en el archivo .env"
  exit 1
fi

# Inicia el proxy de OpenAI a Openrouter
docker compose -f docker-compose.openrouter.yml up -d

# Espera a que el proxy esté listo
echo "Esperando a que el proxy de OpenAI esté listo..."
sleep 5

# Inicia Supabase
docker compose up -d

echo "Supabase está iniciando con soporte para Openrouter"
echo "Puedes acceder al dashboard en http://localhost:3000"
