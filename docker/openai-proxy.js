// Simple proxy server to redirect OpenAI API requests to Openrouter
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const app = express();

// Get your Openrouter API key from environment variable
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

if (!OPENROUTER_API_KEY) {
  console.error('OPENROUTER_API_KEY environment variable is required');
  process.exit(1);
}

// Proxy middleware configuration
const apiProxy = createProxyMiddleware({
  target: 'https://openrouter.ai/api/v1',
  changeOrigin: true,
  pathRewrite: {
    '^/v1': '', // Remove /v1 from the path
  },
  onProxyReq: (proxyReq, req, res) => {
    // Replace the OpenAI API key with the Openrouter API key
    proxyReq.setHeader('Authorization', `Bearer ${OPENROUTER_API_KEY}`);
    
    // Add Openrouter specific headers
    proxyReq.setHeader('HTTP-Referer', 'http://localhost:3000');
    proxyReq.setHeader('X-Title', 'Supabase SQL Assistant');
  },
});

// Proxy all requests to OpenAI API
app.use('/v1', apiProxy);

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`OpenAI to Openrouter proxy server running on port ${PORT}`);
});
